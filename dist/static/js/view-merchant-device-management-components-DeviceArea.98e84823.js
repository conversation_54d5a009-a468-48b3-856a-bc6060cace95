(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-device-management-components-DeviceArea"],{"8da5":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("div",[t("div",{staticClass:"table-wrapper"},[t("div",{staticClass:"table-header",staticStyle:{display:"flex","justify-content":"space-between"}},[t("div",{staticClass:"table-title"},[e._v("编辑信息")]),t("div",{staticStyle:{"padding-right":"20px"}},[t("el-button",{staticClass:"ps-origin-btn",attrs:{size:"small",type:"primary"},on:{click:e.checkForm}},[e._v("保存")])],1)]),t("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"deviceForm",staticStyle:{padding:"0 25px"},attrs:{model:e.deviceInfo,inline:"",rules:e.deviceFormRule,"label-width":"100px"}},[t("el-form-item",{attrs:{label:"设备名",prop:"deviceName"}},[t("el-input",{staticClass:"ps-input w-180",model:{value:e.deviceInfo.deviceName,callback:function(t){e.$set(e.deviceInfo,"deviceName",t)},expression:"deviceInfo.deviceName"}})],1),t("el-form-item",{attrs:{label:"设备型号"}},[t("el-input",{staticClass:"ps-input w-180",attrs:{disabled:""},model:{value:e.deviceInfo.deviceModel,callback:function(t){e.$set(e.deviceInfo,"deviceModel",t)},expression:"deviceInfo.deviceModel"}})],1),t("el-form-item",{attrs:{label:"所属组织"}},[t("el-input",{staticClass:"ps-input w-180",attrs:{disabled:""},model:{value:e.deviceInfo.organizationName,callback:function(t){e.$set(e.deviceInfo,"organizationName",t)},expression:"deviceInfo.organizationName"}})],1),t("el-form-item",{attrs:{label:"取餐柜终端id"}},[t("el-input",{staticClass:"ps-input w-180",attrs:{disabled:""},model:{value:e.deviceInfo.appId,callback:function(t){e.$set(e.deviceInfo,"appId",t)},expression:"deviceInfo.appId"}})],1)],1)],1),t("div",{staticClass:"table-wrapper"},[t("div",{staticClass:"table-header",staticStyle:{display:"flex","align-items":"center","justify-content":"space-between"}},[t("div",{staticClass:"table-title"},[e._v("配送区域： "),t("el-select",{staticClass:"ps-select w-300",attrs:{multiple:!0,"collapse-tags":""},on:{change:e.addrAreaChange},model:{value:e.deviceInfo.addrArea,callback:function(t){e.$set(e.deviceInfo,"addrArea",t)},expression:"deviceInfo.addrArea"}},e._l(e.addressArea,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),t("div",{staticClass:"align-r"},[t("button-icon",{attrs:{color:"plain",type:"del"},on:{click:function(t){return e.mulOperation("mulDel")}}},[e._v("批量删除")])],1)]),t("div",{staticClass:"table-content"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:""},on:{"selection-change":e.handleSelectionChange}},[t("el-table-column",{attrs:{type:"selection",width:"50",align:"center","class-name":"ps-checkbox"}}),t("el-table-column",{attrs:{prop:"name",label:"配送区域",align:"center"}}),t("el-table-column",{attrs:{width:"180",label:"操作",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-button",{staticClass:"ps-warn",attrs:{type:"text",size:"small"},on:{click:function(t){return e.mulOperation("del",r.row.id)}}},[e._v(" 删除 ")])]}}])})],1)],1)])])},a=[];function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function o(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */o=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},c="function"==typeof Symbol?Symbol:{},s=c.iterator||"@@iterator",l=c.asyncIterator||"@@asyncIterator",u=c.toStringTag||"@@toStringTag";function d(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(e){d=function(e,t,r){return e[t]=r}}function f(e,t,r,n){var i=t&&t.prototype instanceof b?t:b,o=Object.create(i.prototype),c=new O(n||[]);return a(o,"_invoke",{value:C(e,r,c)}),o}function p(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var v="suspendedStart",h="suspendedYield",m="executing",g="completed",y={};function b(){}function w(){}function x(){}var I={};d(I,s,(function(){return this}));var L=Object.getPrototypeOf,_=L&&L(L($([])));_&&_!==r&&n.call(_,s)&&(I=_);var k=x.prototype=b.prototype=Object.create(I);function A(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function N(e,t){function r(a,o,c,s){var l=p(e[a],e,o);if("throw"!==l.type){var u=l.arg,d=u.value;return d&&"object"==i(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,c,s)}),(function(e){r("throw",e,c,s)})):t.resolve(d).then((function(e){u.value=e,c(u)}),(function(e){return r("throw",e,c,s)}))}s(l.arg)}var o;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){r(e,n,t,a)}))}return o=o?o.then(a,a):a()}})}function C(t,r,n){var a=v;return function(i,o){if(a===m)throw Error("Generator is already running");if(a===g){if("throw"===i)throw o;return{value:e,done:!0}}for(n.method=i,n.arg=o;;){var c=n.delegate;if(c){var s=S(c,n);if(s){if(s===y)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===v)throw a=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=m;var l=p(t,r,n);if("normal"===l.type){if(a=n.done?g:h,l.arg===y)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(a=g,n.method="throw",n.arg=l.arg)}}}function S(t,r){var n=r.method,a=t.iterator[n];if(a===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,S(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var i=p(a,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,y;var o=i.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,y):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function D(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(D,this),this.reset(!0)}function $(t){if(t||""===t){var r=t[s];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,o=function r(){for(;++a<t.length;)if(n.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(i(t)+" is not iterable")}return w.prototype=x,a(k,"constructor",{value:x,configurable:!0}),a(x,"constructor",{value:w,configurable:!0}),w.displayName=d(x,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,x):(e.__proto__=x,d(e,u,"GeneratorFunction")),e.prototype=Object.create(k),e},t.awrap=function(e){return{__await:e}},A(N.prototype),d(N.prototype,l,(function(){return this})),t.AsyncIterator=N,t.async=function(e,r,n,a,i){void 0===i&&(i=Promise);var o=new N(f(e,r,n,a),i);return t.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},A(k),d(k,u,"Generator"),d(k,s,(function(){return this})),d(k,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=$,O.prototype={constructor:O,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(E),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(n,a){return c.type="throw",c.arg=t,r.next=n,a&&(r.method="next",r.arg=e),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],c=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var s=n.call(o,"catchLoc"),l=n.call(o,"finallyLoc");if(s&&l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),E(r),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;E(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:$(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),y}},t}function c(e,t,r,n,a,i,o){try{var c=e[i](o),s=c.value}catch(e){return void r(e)}c.done?t(s):Promise.resolve(s).then(n,a)}function s(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){c(i,n,a,o,s,"next",e)}function s(e){c(i,n,a,o,s,"throw",e)}o(void 0)}))}}var l={name:"DeviceArea",props:{deviceNo:[String,Number]},data:function(){return{isLoading:!1,deviceInfo:{deviceName:"",deviceModel:"",organizationName:"",groupIds:[],appId:"",addrArea:[]},deviceFormRule:{deviceName:[{required:!0,message:"请输入设备名",trigger:"blur"}]},deviceOrg:"",tableData:[],addressArea:[],selectListId:[]}},created:function(){this.initLoad()},methods:{initLoad:function(){var e=this;return s(o().mark((function t(){return o().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getSettingDetail();case 2:e.getAddressAreaList();case 3:case"end":return t.stop()}}),t)})))()},checkForm:function(){var e=this;this.$refs.deviceForm.validate((function(t){if(!t)return e.$message.error("数据填写有误，请检查"),!1;var r={device_no:e.$route.query.device_no,device_name:e.deviceInfo.deviceName};e.saveSetting(r)}))},saveSetting:function(e){var t=this;return s(o().mark((function r(){var n;return o().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,t.$apis.apiBackgroundDeviceDeviceModifyPost(e);case 2:n=r.sent,0===n.code?(t.$message.success("保存成功"),t.getSettingDetail()):t.$message.error(n.msg);case 4:case"end":return r.stop()}}),r)})))()},getSettingDetail:function(){var e=this;return s(o().mark((function t(){var r;return o().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundDeviceDeviceDeviceInfoPost({device_no:e.deviceNo});case 2:r=t.sent,0===r.code?r.data?(e.deviceInfo.deviceName=r.data.device_name,e.deviceInfo.deviceModel=r.data.device_model,e.deviceInfo.organizationName=r.data.organization_name,e.deviceInfo.appId=r.data.cupboard_json.appId,e.deviceInfo.addrArea=[],r.data.addr_area.map((function(t){e.deviceInfo.addrArea.push(t.id)})),e.tableData=r.data.addr_area,e.deviceOrg=r.data.organization):e.resetForm():e.$message.error(r.msg);case 4:case"end":return t.stop()}}),t)})))()},getAddressAreaList:function(){var e=this;return s(o().mark((function t(){var r;return o().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiAddressAddersAreaAllPost({id:e.deviceOrg});case 2:r=t.sent,0===r.code?e.addressArea=r.data:e.$message.error(r.msg);case 4:case"end":return t.stop()}}),t)})))()},addrAreaChange:function(){var e=this;return s(o().mark((function t(){var r;return o().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundDeviceDeviceCupboardModifyPost({device_no:e.deviceNo,addr_area:e.deviceInfo.addrArea});case 2:r=t.sent,0===r.code?(e.$message.success("保存成功"),e.getSettingDetail()):e.$message.error(r.msg);case 4:case"end":return t.stop()}}),t)})))()},handleSelectionChange:function(e){var t=this;this.selectListId=[];var r=Object.freeze(e);r.map((function(e){t.selectListId.push(e.id)}))},mulOperation:function(e,t){var r=this;if(!t&&!this.selectListId.length)return this.$message.error("请先选择数据！");var n="提示",a="";switch(e){case"mulDel":a="确定批量删除所选配送区域吗？";break;case"del":a="确定删除该配送区域吗？";break}this.$confirm("".concat(a),"".concat(n),{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var n=s(o().mark((function n(a,i,c){var s;return o().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if("confirm"!==a){n.next=15;break}i.confirmButtonLoading=!0,s={device_no:r.deviceNo,can_refund:0,addr_area:r.deviceInfo.addrArea},n.t0=e,n.next="mulDel"===n.t0?6:"del"===n.t0?8:10;break;case 6:return s.addr_area=r.setDelId(r.selectListId),n.abrupt("break",10);case 8:return s.addr_area=r.setDelId([t]),n.abrupt("break",10);case 10:r.addrAreaChange(s),c(),i.confirmButtonLoading=!1,n.next=16;break;case 15:i.confirmButtonLoading||c();case 16:case"end":return n.stop()}}),n)})));function a(e,t,r){return n.apply(this,arguments)}return a}()}).then((function(e){})).catch((function(e){}))},setDelId:function(e){var t=this;e.map((function(e){var r=t.deviceInfo.addrArea.findIndex((function(t){return t===e}));r>=0&&t.deviceInfo.addrArea.splice(r,1)}))}}},u=l,d=r("2877"),f=Object(d["a"])(u,n,a,!1,null,null,null);t["default"]=f.exports}}]);