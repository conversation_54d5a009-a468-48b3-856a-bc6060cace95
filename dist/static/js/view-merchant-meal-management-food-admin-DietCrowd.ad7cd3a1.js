(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-food-admin-DietCrowd","view-merchant-meal-management-components-ColumnItem","view-merchant-meal-management-components-dietCrowdNutritionData"],{"38b4":function(e,t,r){"use strict";r("d151")},"81cc":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"diet-crowd container-wrapper"},[t("refresh-tool",{on:{refreshPage:e.refreshHandle}}),t("div",{staticClass:"table-wrapper"},[e._m(0),t("div",[t("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.formLoading,expression:"formLoading"}],ref:"formData",staticClass:"dialog-form",attrs:{rules:e.formDataRuls,model:e.formData}},[t("el-form-item",{attrs:{label:"名称：",prop:"name","label-width":"100px"}},[t("el-input",{staticClass:"ps-input",staticStyle:{width:"190px"},attrs:{placeholder:"请输入名称"},model:{value:e.formData.name,callback:function(t){e.$set(e.formData,"name",t)},expression:"formData.name"}})],1),t("el-form-item",{attrs:{label:"人群：",prop:"group","label-width":"100px"}},[t("el-input",{staticClass:"ps-input",staticStyle:{width:"190px"},attrs:{placeholder:"请输入人群"},model:{value:e.formData.group,callback:function(t){e.$set(e.formData,"group",t)},expression:"formData.group"}})],1),t("el-form-item",{attrs:{label:"性别","label-width":"100px"}},[t("el-radio-group",{model:{value:e.formData.gender,callback:function(t){e.$set(e.formData,"gender",t)},expression:"formData.gender"}},[t("el-radio",{staticClass:"ps-radio",attrs:{label:"MAN"}},[e._v("男")]),t("el-radio",{staticClass:"ps-radio",attrs:{label:"WOMEN"}},[e._v("女")]),t("el-radio",{staticClass:"ps-radio",attrs:{label:"OTHER"}},[e._v("其他")])],1)],1),"WOMEN"==e.formData.gender?t("el-form-item",{attrs:{label:"特殊人群：",prop:"","label-width":"100px"}},[t("el-select",{staticClass:"ps-select",attrs:{placeholder:"请下拉选择人群","popper-class":"ps-popper-select","collapse-tags":"",clearable:""},model:{value:e.formData.special_group,callback:function(t){e.$set(e.formData,"special_group",t)},expression:"formData.special_group"}},e._l(e.specialGroupList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1):e._e(),t("div",{staticClass:"age-box"},[t("el-form-item",{attrs:{label:"最小年龄","label-width":"100px",prop:"min_age"}},[t("el-input",{staticClass:"input",model:{value:e.formData.min_age,callback:function(t){e.$set(e.formData,"min_age",t)},expression:"formData.min_age"}})],1),t("el-form-item",{attrs:{label:"最大年龄","label-width":"100px",prop:"max_age"}},[t("el-input",{staticClass:"input",model:{value:e.formData.max_age,callback:function(t){e.$set(e.formData,"max_age",t)},expression:"formData.max_age"}})],1)],1),t("el-form-item",{attrs:{label:"来源","label-width":"100px"}},[t("el-input",{staticStyle:{width:"300px"},attrs:{type:"textarea",autosize:{minRows:2,maxRows:10},placeholder:"请输入内容"},model:{value:e.formData.source,callback:function(t){e.$set(e.formData,"source",t)},expression:"formData.source"}})],1)],1)],1)]),t("div",{staticClass:"table-wrapper"},[e._m(1),t("div",{staticClass:"table-content"},[t("el-table",{ref:"tableDataNutrition",staticStyle:{width:"100%"},attrs:{data:e.tableDataNutrition,border:""}},[t("el-table-column",{attrs:{prop:"",label:"中国居民常量和微量元素的参考摄入量(RNIS)或推荐摄入量(AI)、可耐最高摄入量(UL)",align:"center"}},[e._l(e.tableDataNutritionData,(function(e){return t("column-item",{key:e.label,attrs:{col:e}})}))],2)],1)],1)]),t("div",{staticClass:"table-wrapper"},[t("div",{staticClass:"align-r diet-btn"},[t("button-icon",{attrs:{color:"plain",type:""},on:{click:e.cancel}},[e._v("取消")]),t("button-icon",{attrs:{color:"origin",type:"",loading:e.dietCrowdLoading},on:{click:e.preservationDiet}},[e._v(" 保存 ")])],1)])],1)},n=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[e._v("膳食营养表单")])])},function(){var e=this,t=e._self._c;return t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[e._v("膳食营养设置")])])}],o=r("ed08"),i=r("981e"),l=r("9665");function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return t};var e,t={},r=Object.prototype,a=r.hasOwnProperty,n=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",l=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function p(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{p({},"")}catch(e){p=function(e,t,r){return e[t]=r}}function d(e,t,r,a){var o=t&&t.prototype instanceof v?t:v,i=Object.create(o.prototype),l=new j(a||[]);return n(i,"_invoke",{value:U(e,r,l)}),i}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var h="suspendedStart",m="suspendedYield",y="executing",b="completed",g={};function v(){}function k(){}function w(){}var L={};p(L,i,(function(){return this}));var D=Object.getPrototypeOf,_=D&&D(D(E([])));_&&_!==r&&a.call(_,i)&&(L=_);var A=w.prototype=v.prototype=Object.create(L);function x(e){["next","throw","return"].forEach((function(t){p(e,t,(function(e){return this._invoke(t,e)}))}))}function I(e,t){function r(n,o,i,l){var s=f(e[n],e,o);if("throw"!==s.type){var u=s.arg,p=u.value;return p&&"object"==c(p)&&a.call(p,"__await")?t.resolve(p.__await).then((function(e){r("next",e,i,l)}),(function(e){r("throw",e,i,l)})):t.resolve(p).then((function(e){u.value=e,i(u)}),(function(e){return r("throw",e,i,l)}))}l(s.arg)}var o;n(this,"_invoke",{value:function(e,a){function n(){return new t((function(t,n){r(e,a,t,n)}))}return o=o?o.then(n,n):n()}})}function U(t,r,a){var n=h;return function(o,i){if(n===y)throw Error("Generator is already running");if(n===b){if("throw"===o)throw i;return{value:e,done:!0}}for(a.method=o,a.arg=i;;){var l=a.delegate;if(l){var c=C(l,a);if(c){if(c===g)continue;return c}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(n===h)throw n=b,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);n=y;var s=f(t,r,a);if("normal"===s.type){if(n=a.done?b:m,s.arg===g)continue;return{value:s.arg,done:a.done}}"throw"===s.type&&(n=b,a.method="throw",a.arg=s.arg)}}}function C(t,r){var a=r.method,n=t.iterator[a];if(n===e)return r.delegate=null,"throw"===a&&t.iterator.return&&(r.method="return",r.arg=e,C(t,r),"throw"===r.method)||"return"!==a&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+a+"' method")),g;var o=f(n,t.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,g;var i=o.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,g):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function N(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function O(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function j(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(N,this),this.reset(!0)}function E(t){if(t||""===t){var r=t[i];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function r(){for(;++n<t.length;)if(a.call(t,n))return r.value=t[n],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(c(t)+" is not iterable")}return k.prototype=w,n(A,"constructor",{value:w,configurable:!0}),n(w,"constructor",{value:k,configurable:!0}),k.displayName=p(w,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===k||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,p(e,u,"GeneratorFunction")),e.prototype=Object.create(A),e},t.awrap=function(e){return{__await:e}},x(I.prototype),p(I.prototype,l,(function(){return this})),t.AsyncIterator=I,t.async=function(e,r,a,n,o){void 0===o&&(o=Promise);var i=new I(d(e,r,a,n),o);return t.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},x(A),p(A,u,"Generator"),p(A,i,(function(){return this})),p(A,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var a in t)r.push(a);return r.reverse(),function e(){for(;r.length;){var a=r.pop();if(a in t)return e.value=a,e.done=!1,e}return e.done=!0,e}},t.values=E,j.prototype={constructor:j,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(O),!t)for(var r in this)"t"===r.charAt(0)&&a.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(a,n){return l.type="throw",l.arg=t,r.next=a,n&&(r.method="next",r.arg=e),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],l=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=a.call(i,"catchLoc"),s=a.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),O(r),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var a=r.completion;if("throw"===a.type){var n=a.arg;O(r)}return n}}throw Error("illegal catch attempt")},delegateYield:function(t,r,a){return this.delegate={iterator:E(t),resultName:r,nextLoc:a},"next"===this.method&&(this.arg=e),g}},t}function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach((function(t){d(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function d(e,t,r){return(t=f(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function f(e){var t=h(e,"string");return"symbol"==c(t)?t:t+""}function h(e,t){if("object"!=c(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=c(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function m(e,t){return k(e)||v(e,t)||b(e,t)||y()}function y(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function b(e,t){if(e){if("string"==typeof e)return g(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?g(e,t):void 0}}function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,a=Array(t);r<t;r++)a[r]=e[r];return a}function v(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var a,n,o,i,l=[],c=!0,s=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(a=o.call(r)).done)&&(l.push(a.value),l.length!==t);c=!0);}catch(e){s=!0,n=e}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(s)throw n}}return l}}function k(e){if(Array.isArray(e))return e}function w(e,t,r,a,n,o,i){try{var l=e[o](i),c=l.value}catch(e){return void r(e)}l.done?t(c):Promise.resolve(c).then(a,n)}function L(e){return function(){var t=this,r=arguments;return new Promise((function(a,n){var o=e.apply(t,r);function i(e){w(o,a,n,i,l,"next",e)}function l(e){w(o,a,n,i,l,"throw",e)}i(void 0)}))}}var D={name:"DietCrowd",components:{columnItem:i["default"]},data:function(){return{dietCrowdLoading:!1,tableDataNutrition:[],tableDataNutritionData:l["dietCrowdData"],formLoading:!1,formDataRuls:{name:[{required:!0,message:"请输入名字",trigger:"blur"}],group:[{required:!0,message:"请输入人群",trigger:"blur"}],min_age:[{required:!0,message:"请输入最小年龄",trigger:"blur"}],max_age:[{required:!0,message:"请输入最大年龄",trigger:"blur"}]},formData:{name:"",group:"",gender:"MAN",special_group:"",min_age:"",max_age:"",source:""},specialGroupList:[{name:"孕妇(早期)",id:1},{name:"孕妇(中期)",id:2},{name:"孕妇(晚期)",id:3},{name:"哺乳期",id:4}],type:"",id:""}},created:function(){this.$route.query.type&&(this.type=this.$route.query.type,"edit"===this.$route.query.type&&(this.id=this.$route.query.id,this.getFoodDietGroupList(this.$route.query.id))),this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.initTableDataNutrition()},searchHandle:Object(o["d"])((function(){this.currentPage=1}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.currentPage=1,this.initLoad()},initTableDataNutrition:function(){var e={};this.calleArr(l["dietCrowdData"],e,1),this.tableDataNutrition.push(e)},setFoodDietGroupAdd:function(){var e=this;return L(s().mark((function t(){var r,a,n,i;return s().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.dietCrowdLoading=!0,t.next=3,Object(o["Z"])(e.$apis.apiBackgroundFoodDietGroupAddPost(p({name:e.formData.name,group:e.formData.group,gender:e.formData.gender,special_group:e.formData.special_group,min_age:e.formData.min_age,max_age:e.formData.max_age,source:e.formData.source},e.initNutrition())));case 3:if(r=t.sent,a=m(r,2),n=a[0],i=a[1],e.dietCrowdLoading=!1,!n){t.next=11;break}return e.$message.error(n.message),t.abrupt("return");case 11:0===i.code?(e.$message.success(i.msg),e.$closeCurrentTab(e.$route.path)):e.$message.error(i.msg);case 12:case"end":return t.stop()}}),t)})))()},setFoodDietGroupModify:function(){var e=this;return L(s().mark((function t(){var r,a,n,i;return s().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.dietCrowdLoading=!0,t.next=3,Object(o["Z"])(e.$apis.apiBackgroundFoodDietGroupModifyPost(p({id:e.id,name:e.formData.name,group:e.formData.group,gender:e.formData.gender,special_group:e.formData.special_group,min_age:e.formData.min_age,max_age:e.formData.max_age,source:e.formData.source},e.initNutrition())));case 3:if(r=t.sent,a=m(r,2),n=a[0],i=a[1],e.dietCrowdLoading=!1,!n){t.next=11;break}return e.$message.error(n.message),t.abrupt("return");case 11:0===i.code?(e.$message.success(i.msg),e.$closeCurrentTab(e.$route.path)):e.$message.error(i.msg);case 12:case"end":return t.stop()}}),t)})))()},getFoodDietGroupList:function(e){var t=this;return L(s().mark((function r(){var a,n,i,l;return s().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,Object(o["Z"])(t.$apis.apiBackgroundFoodDietGroupListPost({id:e,page:1,page_size:99}));case 2:if(a=r.sent,n=m(a,2),i=n[0],l=n[1],!i){r.next=9;break}return t.$message.error(i.message),r.abrupt("return");case 9:0===l.code?(t.formData={name:l.data.results[0].name,group:l.data.results[0].group,gender:l.data.results[0].gender,special_group:l.data.results[0].special_group?l.data.results[0].special_group:"",min_age:l.data.results[0].min_age,max_age:l.data.results[0].max_age,source:l.data.results[0].source},t.editRecurrence(l.data.results[0])):t.$message.error(l.msg);case 10:case"end":return r.stop()}}),r)})))()},handleSizeChange:function(e){this.pageSize=e},handleCurrentChange:function(e){this.currentPage=e},calleArr:function(e,t,r,a){for(var n=0;n<e.length;n++){var o=e[n];if(1===r&&(a=o.key),o.children){var i=r+1;this.calleArr(o.children,t,i,a)}else o.prop=a+"_"+o.key,t[o.prop]=""}},editRecurrence:function(e){var t=JSON.parse(e.element),r=JSON.parse(e.vitamin);for(var a in this.tableDataNutrition[0].energy_mj=e.energy_mj,this.tableDataNutrition[0].energy_kcal=e.energy_kcal,this.tableDataNutrition[0].protein_40=e.protein,this.tableDataNutrition[0].axunge_20=e.axunge,t)this.tableDataNutrition[0][a+"_AI"]=t[a].AI,this.tableDataNutrition[0][a+"_UL"]=t[a].UL;for(var n in r)this.tableDataNutrition[0][n+"_AI"]=r[n].AI,this.tableDataNutrition[0][n+"_UL"]=r[n].UL},initNutrition:function(){var e=["axunge","protein","energy"],t=["Ca","P","K","Na","Mg","Fe","I","Zn","Se","Cu","F","Cr","Mn","Mo"],r=["VA","VD","VE","VB1","VB2","VB6","VB12","VC","VB5","VM","VB3","Choline","Nicotinamide","VH"],a={energy_mj:"",energy_kcal:"",axunge:"",protein:"",element:{},vitamin:{}};for(var n in this.tableDataNutrition[0]){var o=n.split("_"),i=this.tableDataNutrition[0][n]?this.tableDataNutrition[0][n]:0;t.includes(o[0])&&(a.element[o[0]]||(a.element[o[0]]={}),"AI"===o[1]&&(a.element[o[0]][o[1]]=i),"UL"===o[1]&&(a.element[o[0]][o[1]]=i)),r.includes(o[0])&&(a.vitamin[o[0]]||(a.vitamin[o[0]]={}),"AI"===o[1]&&(a.vitamin[o[0]][o[1]]=i),"UL"===o[1]&&(a.vitamin[o[0]][o[1]]=i)),e.includes(o[0])&&("mj"===o[1]&&(a.energy_mj=i),"kcal"===o[1]&&(a.energy_kcal=i),"axunge"===o[0]&&(a.axunge=i),"protein"===o[0]&&(a.protein=i))}return a.element=JSON.stringify(a.element),a.vitamin=JSON.stringify(a.vitamin),a},handleSelectionChange:function(e){},cancel:function(){this.$closeCurrentTab(this.$route.path)},preservationDiet:function(){var e=this;this.$refs.formData.validate((function(t){if(!t)return!1;"add"===e.type?e.setFoodDietGroupAdd():"edit"===e.type&&e.setFoodDietGroupModify()}))}}},_=D,A=(r("38b4"),r("2877")),x=Object(A["a"])(_,a,n,!1,null,"21558d16",null);t["default"]=x.exports},9665:function(e,t,r){"use strict";r.r(t),r.d(t,"dietCrowdData",(function(){return a}));var a=[{key:"energy",label:"能量(ERR=mj/kg+kcal/kg)",children:[{key:"mj",label:"兆焦耳(mj/kg)",prop:""},{key:"kcal",label:"千卡(kcal/kg)",prop:""}]},{key:"axunge",label:"蛋白质(Protein)",children:[{key:20,label:"克/天(g/d)",prop:""}]},{key:"protein",label:"脂肪(Fat)",children:[{key:40,label:"占能量百分比（EER%）",prop:""}]},{key:"Ca",label:"钙",children:[{key:5,label:"毫克/天(mg/d)",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"P",label:"磷",children:[{key:60,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"K",label:"钾",children:[{key:70,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"Na",label:"钠",children:[{key:80,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"Mg",label:"镁",children:[{key:90,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"Fe",label:"铁",children:[{key:100,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"I",label:"碘",children:[{key:110,label:"微克/天（μg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"Zn",label:"锌",children:[{key:120,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"Se",label:"硒",children:[{key:130,label:"微克/天（μg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"Cu",label:"铜",children:[{key:140,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"F",label:"氟",children:[{key:150,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"Cr",label:"铬",children:[{key:160,label:"微克/天（μg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"Mn",label:"锰",children:[{key:170,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"Mo",label:"钼",children:[{key:180,label:"微克/天（μg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"VA",label:"维生素A",children:[{key:190,label:"微克（μgRAE/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"VD",label:"维生素D",children:[{key:200,label:"微克/天（μg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"VE",label:"维生素E",children:[{key:210,label:"毫克（mga-TE/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"VK",label:"维生素K",children:[{key:220,label:"微克/天（μg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"VB1",label:"维生素B1",children:[{key:230,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"VB2",label:"维生素B2",children:[{key:240,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"VB6",label:"维生素B6",children:[{key:250,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"VB12",label:"维生素B12",children:[{key:260,label:"微克/天（μg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"VC",label:"维生素C",children:[{key:270,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"VB5",label:"泛酸",children:[{key:280,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"VM",label:"叶酸",children:[{key:290,label:"微克（μgDFE/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"VB3",label:"烟酸",children:[{key:300,label:"毫克/天（mg NE/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"Choline",label:"胆碱",children:[{key:310,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"Nicotinamide",label:"烟酰胺",children:[{key:320,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"VH",label:"生物素",children:[{key:330,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]}]},"981e":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e._self._c;return t("el-table-column",{attrs:{prop:e.col.prop,label:e.col.label,align:e.alignType}},[e._l(e.col.children,(function(r,a){return[r.children?t("column-item",{key:a,attrs:{col:r,showDialogNutritionDisabled:e.showDialogNutritionDisabled}}):t("el-table-column",{key:a,attrs:{label:r.label,prop:r.prop,align:e.alignType},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-input",{staticClass:"input",attrs:{disabled:e.showDialogNutritionDisabled},model:{value:a.row[r.prop],callback:function(t){e.$set(a.row,r.prop,t)},expression:"scope.row[item.prop]"}})]}}],null,!0)})]}))],2)},n=[],o={name:"ColumnItem",props:{col:{type:Object},alignType:{type:String,default:"center"},showDialogNutritionDisabled:{type:Boolean,default:!1}}},i=o,l=r("2877"),c=Object(l["a"])(i,a,n,!1,null,null,null);t["default"]=c.exports},d151:function(e,t,r){}}]);