(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-reservation-management-DepartmentReportCollect","view-merchant-meal-management-reservation-management-constants","view-merchant-report-financial-statements-BusinessList"],{"059e":function(t,e,r){"use strict";r("6e9e")},"6e9e":function(t,e,r){},cc06:function(t,e,r){"use strict";function n(t,e){var r={},n=e.useKeyList&&Object.keys(e.useKeyList).length;return n&&Object.keys(e.useKeyList).forEach((function(n,o){e.useKeyList[n].forEach((function(e,o){r[e]={row:[],mergeNum:0,key:n},r=a(r,t,e,n)}))})),e.mergeKeyList&&e.mergeKeyList.forEach((function(e,n){r[e]={row:[],mergeNum:0},r=a(r,t,e)})),r}function a(t,e,r,n){return e.forEach((function(a,i){if(0===i)t[r].row.push(1),t[r].mergeNum=i;else{var c=n?a[n]===e[i-1][n]:!n,s=a[r]===e[i-1][r]&&c;if(s){var u=o(t[r].row);t[r].row[u]+=1,t[r].row.push(0),t[r].mergeNum=i}else t[r].row.push(1),t[r].mergeNum=i}})),t}function o(t){var e=t.length-1;while(e>0){if(t[e])break;e--}return e}function i(t,e,r,n){var a=t[e].row[r],o=a>0?1:0;return[a,o]}r.d(e,"a",(function(){return n})),r.d(e,"b",(function(){return i}))},d0c5:function(t,e,r){"use strict";r.r(e),r.d(e,"recentSevenDay",(function(){return o})),r.d(e,"dietaryStatus",(function(){return i})),r.d(e,"RESERVATION_REPORT",(function(){return c})),r.d(e,"DEPARTMENT_REPORT_COLLECT",(function(){return l})),r.d(e,"USERREChARGEREFUNDSUMMARY",(function(){return f}));var n=r("5a0c"),a=r("e9c7"),o=[n().subtract(7,"day").format("YYYY-MM-DD"),n().format("YYYY-MM-DD")],i=[{label:"普食",value:"PS"},{label:"流食",value:"LS"},{label:"停送",value:"TS"}],c={select_time:{type:"daterange",label:"就餐日期",value:o,format:"yyyy-MM-dd",clearable:!1,pickerOptions:a["c"]},person_name:{type:"input",value:"",label:"姓名",placeholder:"请输入姓名"},phone:{type:"input",value:"",label:"手机号",placeholder:"请输入手机号"},person_no:{type:"input",value:"",label:"人员编号",placeholder:"请输入人员编号"},card_user_group_ids:{type:"groupSelect",label:"科室",value:[],placeholder:"请选择科室",dataList:[],multiple:!0,collapseTags:!0,clearable:!0},dietary_status:{type:"select",label:"医嘱",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""}].concat(i)}},s=n().format("YYYY-MM-DD"),u=n().subtract(1,"day").format("YYYY-MM-DD"),l={select_time:{type:"daterange",label:"就餐日期",value:[s,s],format:"yyyy-MM-dd",clearable:!1,pickerOptions:a["c"]},card_user_group_ids:{type:"groupSelect",label:"科室",value:[],placeholder:"请选择科室",dataList:[],multiple:!0,collapseTags:!0,clearable:!0},dietary_status:{type:"select",label:"医嘱",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"普食",value:"PS"},{label:"流食",value:"LS"}]}},f={select_time:{type:"daterange",label:"支付日期",value:[u,u],format:"yyyy-MM-dd",clearable:!1,pickerOptions:a["b"]}}},e9c7:function(t,e,r){"use strict";r.d(e,"a",(function(){return o})),r.d(e,"c",(function(){return i})),r.d(e,"b",(function(){return c}));var n=r("5a0c"),a=n().subtract(1,"day").format("YYYY/MM/DD"),o=[n().subtract(7,"day").format("YYYY-MM-DD"),n(a).format("YYYY-MM-DD")],i={disabledDate:function(t){return t.getTime()>Date.now()},shortcuts:[{text:"最近一周",onClick:function(t){var e=new Date,r=new Date;r.setTime(r.getTime()-6048e5),t.$emit("pick",[r,e])}},{text:"最近一个月",onClick:function(t){var e=new Date,r=new Date;r.setTime(r.getTime()-2592e6),t.$emit("pick",[r,e])}},{text:"最近三个月",onClick:function(t){var e=new Date,r=new Date;r.setTime(r.getTime()-7776e6),t.$emit("pick",[r,e])}}]},c={disabledDate:function(t){return t.getTime()>new Date(a+" 23:59:59")},shortcuts:[{text:"最近一周",onClick:function(t){var e=new Date(a),r=new Date(a);r.setTime(r.getTime()-5184e5),t.$emit("pick",[r,e])}},{text:"最近一个月",onClick:function(t){var e=new Date(a),r=new Date(a);r.setTime(r.getTime()-2592e6),t.$emit("pick",[r,e])}},{text:"最近三个月",onClick:function(t){var e=new Date(a),r=new Date(a);r.setTime(r.getTime()-7776e6),t.$emit("pick",[r,e])}}]}},ed91:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"DepartmentReportCollect container-wrapper"},[e("refresh-tool",{on:{refreshPage:t.refreshHandle}}),e("search-form",{ref:"searchRef",attrs:{"form-setting":t.searchFormSetting,"label-width":"100px"},on:{search:t.searchHandle}}),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("数据列表")]),e("div",{staticClass:"align-r"},[e("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_order.ordering_food.ordering_food_summary_export"],expression:"['background_order.ordering_food.ordering_food_summary_export']"}],attrs:{color:"origin",type:"export"},on:{click:t.gotoExport}},[t._v(" 导出EXCEL ")])],1)]),e("div",{staticClass:"table-content"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:t.tableData,border:"","header-row-class-name":"ps-table-header-row","span-method":t.arraySpanMethod,"row-class-name":t.tableRowClassName},on:{"cell-mouse-enter":t.handleCellMouseEnter,"cell-mouse-leave":t.handleCellMouseLeave}},[e("el-table-column",{attrs:{label:"序号",prop:"index",align:"center",width:"80px",index:t.indexMethod}}),e("el-table-column",{attrs:{prop:"group_name",label:"科室",align:"center"}}),e("el-table-column",{attrs:{prop:"dietary_status_alias",label:"饮食",align:"center"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("span",{class:[t.dietaryClass[r.row.dietary_status]]},[t._v(t._s(r.row.dietary_status_alias))])]}}])}),e("el-table-column",{attrs:{prop:"total_count",label:"数量",align:"center"}}),e("el-table-column",{attrs:{prop:"reservation_date",label:"就餐日期",align:"center"}})],1)],1),e("table-statistics",{attrs:{statistics:t.collect}}),e("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[e("el-pagination",{staticClass:"ps-text",attrs:{"current-page":t.currentPage,"page-size":t.pageSize,layout:"total, prev, pager, next, jumper",total:t.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1)],1)},a=[],o=r("f63a"),i=r("ed08"),c=r("d0c5"),s=r("cc06");function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function l(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */l=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function p(t,e,r,n){var o=e&&e.prototype instanceof b?e:b,i=Object.create(o.prototype),c=new T(n||[]);return a(i,"_invoke",{value:k(t,r,c)}),i}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=p;var h="suspendedStart",y="suspendedYield",m="executing",g="completed",v={};function b(){}function w(){}function _(){}var L={};f(L,i,(function(){return this}));var x=Object.getPrototypeOf,O=x&&x(x(M([])));O&&O!==r&&n.call(O,i)&&(L=O);var E=_.prototype=b.prototype=Object.create(L);function S(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function D(t,e){function r(a,o,i,c){var s=d(t[a],t,o);if("throw"!==s.type){var l=s.arg,f=l.value;return f&&"object"==u(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,i,c)}),(function(t){r("throw",t,i,c)})):e.resolve(f).then((function(t){l.value=t,i(l)}),(function(t){return r("throw",t,i,c)}))}c(s.arg)}var o;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return o=o?o.then(a,a):a()}})}function k(e,r,n){var a=h;return function(o,i){if(a===m)throw Error("Generator is already running");if(a===g){if("throw"===o)throw i;return{value:t,done:!0}}for(n.method=o,n.arg=i;;){var c=n.delegate;if(c){var s=C(c,n);if(s){if(s===v)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===h)throw a=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=m;var u=d(e,r,n);if("normal"===u.type){if(a=n.done?g:y,u.arg===v)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(a=g,n.method="throw",n.arg=u.arg)}}}function C(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,C(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var o=d(a,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,v;var i=o.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function P(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(P,this),this.reset(!0)}function M(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,o=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(u(e)+" is not iterable")}return w.prototype=_,a(E,"constructor",{value:_,configurable:!0}),a(_,"constructor",{value:w,configurable:!0}),w.displayName=f(_,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,f(t,s,"GeneratorFunction")),t.prototype=Object.create(E),t},e.awrap=function(t){return{__await:t}},S(D.prototype),f(D.prototype,c,(function(){return this})),e.AsyncIterator=D,e.async=function(t,r,n,a,o){void 0===o&&(o=Promise);var i=new D(p(t,r,n,a),o);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},S(E),f(E,s,"Generator"),f(E,i,(function(){return this})),f(E,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=M,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return c.type="throw",c.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],c=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var s=n.call(i,"catchLoc"),u=n.call(i,"finallyLoc");if(s&&u){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),j(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;j(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:M(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function f(t){return y(t)||h(t)||d(t)||p()}function p(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(t,e){if(t){if("string"==typeof t)return m(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?m(t,e):void 0}}function h(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function y(t){if(Array.isArray(t))return m(t)}function m(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function g(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function v(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?g(Object(r),!0).forEach((function(e){b(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function b(t,e,r){return(e=w(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function w(t){var e=_(t,"string");return"symbol"==u(e)?e:e+""}function _(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function L(t,e,r,n,a,o,i){try{var c=t[o](i),s=c.value}catch(t){return void r(t)}c.done?e(s):Promise.resolve(s).then(n,a)}function x(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var o=t.apply(e,r);function i(t){L(o,n,a,i,c,"next",t)}function c(t){L(o,n,a,i,c,"throw",t)}i(void 0)}))}}var O={name:"DepartmentReportCollect",mixins:[o["a"]],data:function(){return{tableData:[],isLoading:!1,searchFormSetting:c["DEPARTMENT_REPORT_COLLECT"],pageSize:10,totalCount:0,currentPage:1,collect:[{key:"ALL",value:0,label:"合计总数:"},{key:"PS",value:0,label:"普食:"},{key:"LS",value:0,label:"流食:"}],dietaryStatus:c["dietaryStatus"],selectList:[],formRules:{dietaryType:[{required:!0,message:"请选择"}],remark:[{required:!0,message:"请先填写备注"}]},mergeOpts:{useKeyList:{collect_key:["index","group_name","dietary_status_alias"],total_key:["total_count","reservation_date"]}},dietaryClass:{PS:"ps-span-text",LS:"ls-span-text"},currentIndex:""}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getDataList()},searchHandle:Object(i["d"])((function(){this.currentPage=1,this.getDataList()}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.currentPage=1,this.initLoad()},formatQueryParams:function(t){var e={};for(var r in t)""!==t[r].value&&null!==t[r].value&&0!==t[r].value.length&&("select_time"!==r?e[r]=t[r].value:t[r].value.length>0&&(e.start_date=t[r].value[0],e.end_date=t[r].value[1]));return e},getDataList:function(){var t=this;return x(l().mark((function e(){var r,n,a,o,c;return l().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.$apis.apiBackgroundOrderOrderingFoodOrderingFoodSummaryPost(v(v({},t.formatQueryParams(t.searchFormSetting)),{},{page:t.currentPage,page_size:t.pageSize}));case 3:r=e.sent,t.isLoading=!1,0===r.code?(n={},t.totalCount=r.data.count,r.data.results&&r.data.results.length&&r.data.results.forEach((function(t,e){if(t.data&&t.data.length){var r={};t.data.forEach((function(e){r[e.reservation_date]||(r[e.reservation_date]=[]),r[e.reservation_date].push(v({group_name:t.group_name,group_id:t.group_id},e))}));var a=function(e){var a={};r[e].forEach((function(t){a[t.dietary_status]?a[t.dietary_status]=a[t.dietary_status]+t.count:a[t.dietary_status]=t.count})),r[e].sort((function(t,e){var r=t.dietary_status;return r.localeCompare(e.dietary_status)}));var o=t.group_id;r[e].forEach((function(t){n[e]||(n[e]={}),n[e][o]||(n[e][o]=[]),n[e]&&n[e][o]&&n[e][o].push(v(v({},t),{},{total_key:"".concat(e,"-").concat(o,"-").concat(t.dietary_status),total_count:a[t.dietary_status],collect_key:"".concat(e,"-").concat(o)}))}))};for(var o in r)a(o)}})),a=[],o=Object.keys(n),o.sort((function(t,e){return new Date(Object(i["Q"])(e.reservation_date)).getTime()-new Date(Object(i["Q"])(t.reservation_date)).getTime()})),c=0,o.forEach((function(t){for(var e in n[t]){c++;var r=n[t][e],o=r.map((function(t){return v(v({},t),{},{index:c})}));a.push.apply(a,f(o))}})),t.tableData=a,t.rowMergeArrs=Object(s["a"])(t.tableData,t.mergeOpts),r.data.total&&r.data.total.forEach((function(e){t.collect.forEach((function(t){t.key===e.dietary_status&&(t.value=e.count)}))}))):t.$message.error(r.msg);case 6:case"end":return e.stop()}}),e)})))()},arraySpanMethod:function(t){t.row;var e=t.column,r=t.rowIndex,n=t.columnIndex,a=Object.keys(this.mergeOpts.useKeyList),o=this.mergeOpts.useKeyList&&a.length;if(o)for(var i in this.mergeOpts.useKeyList)if(this.mergeOpts.useKeyList[i].includes(e.property))return Object(s["b"])(this.rowMergeArrs,e.property,r,n);if(this.mergeOpts.mergeKeyList&&this.mergeOpts.mergeKeyList.length&&this.mergeOpts.mergeKeyList.includes(e.property))return Object(s["b"])(this.rowMergeArrs,e.property,r,n)},addRowClassName:function(t){var e=t.row;t.rowIndex;if(!(e.index%2))return"hover-row--striped"},tableRowClassName:function(t){var e=t.row;return e.index===this.currentIndex?"row--striped":""},handleCellMouseEnter:function(t,e,r,n){this.currentIndex=t.index},handleCellMouseLeave:function(){this.currentIndex=""},indexMethod:function(t){return(this.currentPage-1)*this.pageSize+(t+1)},handleSizeChange:function(t){this.pageSize=t,this.getDataList()},handleCurrentChange:function(t){this.currentPage=t,this.getDataList()},gotoExport:function(){var t={type:"DepartmentReportCollect",url:"apiBackgroundOrderOrderingFoodOrderingFoodSummaryExportPost",params:v(v({},this.formatQueryParams(this.searchFormSetting)),{},{page:this.currentPage,page_size:this.pageSize})};this.exportHandle(t)}}},E=O,S=(r("059e"),r("2877")),D=Object(S["a"])(E,n,a,!1,null,null,null);e["default"]=D.exports}}]);