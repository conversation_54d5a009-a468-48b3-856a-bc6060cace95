(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-health-habit-cultivate-HabitCultivate"],{4787:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"HabitCultivate"},[e("refresh-tool",{on:{refreshPage:t.refreshHandle}}),e("search-form",{ref:"searchRef",attrs:{"form-setting":t.searchFormSetting},on:{search:t.searchHandle}}),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("数据列表")]),e("div",{staticClass:"align-r"},[e("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(e){return t.gotoHabitCultivate("add")}}},[t._v(" 新增 ")])],1)]),e("div",{staticClass:"table-content"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableData",staticClass:"ps-table-tree",staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:"","header-row-class-name":"ps-table-header-row","tree-props":{children:"children_list",hasChildren:"has_children"}}},[e("el-table-column",{attrs:{prop:"icon",label:"图标",align:"center",width:"170px"},scopedSlots:t._u([{key:"default",fn:function(r){return[r.row.image?e("el-image",{staticStyle:{width:"50px",height:"50px"},attrs:{src:r.row.image}}):t._e()]}}])}),e("el-table-column",{attrs:{prop:"name",label:"习惯名称",align:"center"}}),e("el-table-column",{attrs:{prop:"users",label:"使用用户数",align:"center"}}),e("el-table-column",{attrs:{prop:"clocks",label:"打卡总数",align:"center"}}),e("el-table-column",{attrs:{prop:"create_time",label:"创建时间",align:"center"}}),e("el-table-column",{attrs:{prop:"update_time",label:"修改时间",align:"center"}}),e("el-table-column",{attrs:{prop:"operator_name",label:"操作人",align:"center"}}),e("el-table-column",{attrs:{prop:"",label:"使用状态",align:"center"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("el-switch",{attrs:{"active-color":"#f59933"},on:{change:function(e){return t.modifyStatus(e,r.row.id)}},model:{value:r.row.is_open,callback:function(e){t.$set(r.row,"is_open",e)},expression:"scope.row.is_open"}})]}}])}),e("el-table-column",{attrs:{fixed:"right",label:"操作",align:"center"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.gotoHabitCultivate("modify",r.row)}}},[t._v(" 编辑 ")]),e("span",{staticStyle:{margin:"0 10px",color:"#e2e8f0"}},[t._v("|")]),e("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHandler("single",r.row.id)}}},[t._v(" 删除 ")])]}}])})],1)],1),e("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[e("el-pagination",{staticClass:"ps-text",attrs:{"page-size":t.pageSize,layout:"total, prev, pager, next",total:t.totalCount,background:"","popper-class":"ps-popper-select"},on:{"current-change":t.handleCurrentChange}})],1)])],1)},a=[],o=r("ed08");function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},l=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function h(t,e,r,n){var o=e&&e.prototype instanceof b?e:b,i=Object.create(o.prototype),c=new $(n||[]);return a(i,"_invoke",{value:k(t,r,c)}),i}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var d="suspendedStart",g="suspendedYield",m="executing",y="completed",v={};function b(){}function w(){}function x(){}var L={};f(L,l,(function(){return this}));var _=Object.getPrototypeOf,S=_&&_(_(z([])));S&&S!==r&&n.call(S,l)&&(L=S);var O=x.prototype=b.prototype=Object.create(L);function P(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function j(t,e){function r(a,o,c,l){var s=p(t[a],t,o);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==i(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,c,l)}),(function(t){r("throw",t,c,l)})):e.resolve(f).then((function(t){u.value=t,c(u)}),(function(t){return r("throw",t,c,l)}))}l(s.arg)}var o;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return o=o?o.then(a,a):a()}})}function k(e,r,n){var a=d;return function(o,i){if(a===m)throw Error("Generator is already running");if(a===y){if("throw"===o)throw i;return{value:t,done:!0}}for(n.method=o,n.arg=i;;){var c=n.delegate;if(c){var l=C(c,n);if(l){if(l===v)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===d)throw a=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=m;var s=p(e,r,n);if("normal"===s.type){if(a=n.done?y:g,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(a=y,n.method="throw",n.arg=s.arg)}}}function C(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,C(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var o=p(a,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,v;var i=o.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function H(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function $(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function z(e){if(e||""===e){var r=e[l];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,o=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(i(e)+" is not iterable")}return w.prototype=x,a(O,"constructor",{value:x,configurable:!0}),a(x,"constructor",{value:w,configurable:!0}),w.displayName=f(x,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,x):(t.__proto__=x,f(t,u,"GeneratorFunction")),t.prototype=Object.create(O),t},e.awrap=function(t){return{__await:t}},P(j.prototype),f(j.prototype,s,(function(){return this})),e.AsyncIterator=j,e.async=function(t,r,n,a,o){void 0===o&&(o=Promise);var i=new j(h(t,r,n,a),o);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},P(O),f(O,u,"Generator"),f(O,l,(function(){return this})),f(O,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=z,$.prototype={constructor:$,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(H),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return c.type="throw",c.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],c=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),s=n.call(i,"finallyLoc");if(l&&s){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),H(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;H(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:z(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function l(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function s(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?l(Object(r),!0).forEach((function(e){u(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function u(t,e,r){return(e=f(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function f(t){var e=h(t,"string");return"symbol"==i(e)?e:e+""}function h(t,e){if("object"!=i(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function p(t,e){return v(t)||y(t,e)||g(t,e)||d()}function d(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function g(t,e){if(t){if("string"==typeof t)return m(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?m(t,e):void 0}}function m(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function y(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,o,i,c=[],l=!0,s=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);l=!0);}catch(t){s=!0,a=t}finally{try{if(!l&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(s)throw a}}return c}}function v(t){if(Array.isArray(t))return t}function b(t,e,r,n,a,o,i){try{var c=t[o](i),l=c.value}catch(t){return void r(t)}c.done?e(l):Promise.resolve(l).then(n,a)}function w(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var o=t.apply(e,r);function i(t){b(o,n,a,i,c,"next",t)}function c(t){b(o,n,a,i,c,"throw",t)}i(void 0)}))}}var x={name:"HealthAssessment",components:{},props:{},data:function(){return{isLoading:!1,pageSize:10,currentPage:1,totalCount:0,totalPageSize:0,tableData:[],searchFormSetting:{date_type:{type:"select",label:"",value:"create_time",maxWidth:"130px",placeholder:"请选择",dataList:[{label:"创建时间",value:"create_time"},{label:"修改时间",value:"update_time"}]},select_time:{type:"datetimerange",label:"",format:"yyyy-MM-dd HH:mm:ss",value:[]},name:{type:"input",label:"习惯名称",value:"",placeholder:"请输入习惯名称"},status:{type:"select",label:"状态",value:"",maxWidth:"130px",placeholder:"请选择",dataList:[{label:"全部",value:"all"},{label:"启用",value:"enable"},{label:"禁用",value:"disable"}]}},dialogLoading:!1}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getHabitList()},searchHandle:Object(o["d"])((function(){this.currentPage=1,this.initLoad()}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.currentPage=1,this.initLoad()},getHabitList:function(){var t=this;return w(c().mark((function e(){var r,n,a,i;return c().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(o["Z"])(t.$apis.apiBackgroundHealthyAdminHabitListPost(s(s({},t.formatQueryParams(t.searchFormSetting)),{},{page:t.currentPage,page_size:t.pageSize})));case 3:if(r=e.sent,n=p(r,2),a=n[0],i=n[1],t.isLoading=!1,!a){e.next=11;break}return t.$message.error(a.message),e.abrupt("return");case 11:0===i.code?(t.totalCount=i.data.count,t.totalPageSize=t.$computedTotalPageSize(t.totalCount,t.pageSize),t.tableData=i.data.results.map((function(t){return t.is_open="enable"===t.status,t}))):t.$message.error(i.msg);case 12:case"end":return e.stop()}}),e)})))()},deleteHandler:function(t,e){var r=this,n=[];"single"===t&&(n=[e]),this.$confirm("确定删除？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-warn",center:!0,beforeClose:function(){var a=w(c().mark((function a(i,l,s){var u,f,h,d;return c().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if("confirm"!==i){a.next=20;break}if(!r.dialogLoading){a.next=3;break}return a.abrupt("return",r.$message.error("请勿重复提交！"));case 3:return r.dialogLoading=!0,l.confirmButtonLoading=!0,a.next=7,Object(o["Z"])(r.$apis.apiBackgroundHealthyAdminHabitDeletePost({ids:[e]}));case 7:if(u=a.sent,f=p(u,2),h=f[0],d=f[1],r.dialogLoading=!1,!h){a.next=15;break}return r.$message.error(h.message),a.abrupt("return");case 15:0===d.code?(r.$message.success(d.msg),r.currentPage>1&&(1===r.tableData.length&&"one"===t||r.currentPage===r.totalPageSize&&n.length===r.tableData.length)&&r.currentPage--,r.getHabitList()):r.$message.error(d.msg),s(),l.confirmButtonLoading=!1,a.next=21;break;case 20:l.confirmButtonLoading||s();case 21:case"end":return a.stop()}}),a)})));function i(t,e,r){return a.apply(this,arguments)}return i}()}).then((function(t){})).catch((function(t){}))},modifyStatus:function(t,e){var r=this;return w(c().mark((function n(){var a;return c().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return r.isLoading=!0,n.next=3,r.$apis.apiBackgroundHealthyAdminHabitModifyPost({id:e,status:t?"enable":"disable"});case 3:a=n.sent,r.isLoading=!1,0===a.code?(r.$message.success("操作成功！"),r.getHabitList()):r.$message.error(a.msg);case 6:case"end":return n.stop()}}),n)})))()},gotoHabitCultivate:function(t,e){this.$router.push({name:"SuperAddModifyHabit",params:{type:t},query:{type:t,data:"modify"===t?this.$encodeQuery(e):""}})},handleSizeChange:function(t){this.pageSize=t,this.getHabitList()},handleCurrentChange:function(t){this.currentPage=t,this.getHabitList()},formatQueryParams:function(t){var e={};for(var r in t)""!==t[r].value&&null!==t[r].value&&0!==t[r].value.length&&("select_time"!==r?e[r]=t[r].value:t[r].value.length>0&&(e.start_date=t[r].value[0],e.end_date=t[r].value[1]));return e}}},L=x,_=(r("b789"),r("2877")),S=Object(_["a"])(L,n,a,!1,null,"b95c7d6a",null);e["default"]=S.exports},"6f2e":function(t,e,r){},b789:function(t,e,r){"use strict";r("6f2e")}}]);