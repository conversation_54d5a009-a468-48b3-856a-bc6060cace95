(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-merchant-admin-components-BankMerchant-merchantBankInfo","view-merchant-consumption-rules-service-admin-AddChargeServiceRule","view-merchant-meal-management-components-mealFoodList-FoodDiscountDialog","view-super-merchant-admin-components-BankMerchant-customInput","view-super-merchant-admin-components-BankMerchant-merchantBaseInfo","view-super-merchant-admin-constants-bankMerchantConstants"],{9792:function(e,a,t){"use strict";t("b7be")},b7be:function(e,a,t){},c3cc:function(e,a,t){"use strict";t.r(a);var l=function(){var e=this,a=e._self._c;return a("div",{staticClass:"ps-flex"},[a("el-input",{class:"textarea"!=e.inputType?"w-180":"w-350 h-100",attrs:{placeholder:e.inputPlaceHolder,autocomplete:"off",type:e.inputType,disabled:e.inputDisabled,clearable:"",rows:e.inputRows,maxlength:e.inputMaxLength,autosize:{minRows:e.inputRows,maxRows:e.inputRows}},on:{input:e.handlerInputChange},model:{value:e.inputContent,callback:function(a){e.inputContent=a},expression:"inputContent"}}),a("div",{staticClass:"m-l-10 m-t-10"},[e._v(e._s(e.currentLength)+"/"+e._s(e.inputMaxLength))])],1)},n=[],i={name:"customInput",props:{value:{type:String,default:""},type:{type:String,default:"text"},disabled:{type:Boolean,default:!1},maxLength:{type:Number,default:50},placeholder:{type:String,default:"请输入"},natureType:{type:String,default:""},rows:{type:Number,default:2}},data:function(){return{inputContent:this.value,inputType:this.type,inputDisabled:this.disabled,inputRows:this.rows,currentLength:this.value?this.value.length:0,inputMaxLength:this.maxLength,inputPlaceHolder:this.placeholder}},watch:{value:function(e){this.inputContent=e},disabled:function(e){this.inputDisabled=e}},methods:{handlerInputChange:function(e){var a=e?e.length:0;a>this.maxLength&&(this.$message.error("超出最大字符"+this.maxLength+"限制"),e=e.slice(0,this.maxLength),this.inputContent=e,a=e.length),this.$set(this,"currentLength",a),this.$emit("inputChange",e,this.natureType)}}},r=i,s=t("2877"),u=Object(s["a"])(r,l,n,!1,null,"7537d78b",null);a["default"]=u.exports},ca59:function(e,a,t){"use strict";t.r(a);var l=function(){var e=this,a=e._self._c;return a("div",{staticClass:"container-wrapper"},[a("div",{staticClass:"table-wrapper"},[e._m(0),a("div",{staticClass:"m-l-40 m-r-40"},[a("el-form",{ref:"bankInfo",attrs:{model:e.params,"label-width":"180px","label-position":"left",rules:e.contactInfoRules}},[a("el-form-item",{staticClass:"item-style",attrs:{label:"银行账号：",prop:"account"}},[a("custom-input",{attrs:{value:e.params.account,maxLength:30,natureType:"account",disabled:e.isDisabledEdit},on:{inputChange:e.inputChangeBankInfo}})],1),a("div",{staticClass:"ps-flex flex-wrap"},[a("el-form-item",{staticClass:"item-style",attrs:{label:"银行账户户名：",prop:"account_name"}},[a("custom-input",{attrs:{value:e.params.account_name,maxLength:50,natureType:"account_name",disabled:e.isDisabledEdit},on:{inputChange:e.inputChangeBankInfo}})],1),a("el-form-item",{staticClass:"item-style",attrs:{label:"开户银行名称：",prop:"bank_name"}},[a("custom-input",{attrs:{value:e.params.bank_name,maxLength:15,natureType:"bank_name",disabled:e.isDisabledEdit},on:{inputChange:e.inputChangeBankInfo}})],1),a("el-form-item",{staticClass:"item-style",attrs:{label:"银行预留手机号：",prop:"mobile_phone"}},[a("custom-input",{attrs:{value:e.params.mobile_phone,maxLength:20,natureType:"mobile_phone",disabled:e.isDisabledEdit,type:"number"},on:{inputChange:e.inputChangeBankInfo}})],1)],1),a("div",{staticClass:"ps-flex flex-wrap"},[a("el-form-item",{staticClass:"item-style",attrs:{label:"账户类型：",prop:"account_type"}},[a("el-select",{staticClass:"ps-select w-180",attrs:{placeholder:"请选择","popper-class":"ps-popper-select",disabled:e.isDisabledEdit},on:{change:function(a){return e.changeSelect(a,"account_type")}},model:{value:e.params.account_type,callback:function(a){e.$set(e.params,"account_type",a)},expression:"params.account_type"}},e._l(e.dicAccountType,(function(e){return a("el-option",{key:e.value,attrs:{label:e.name,value:e.value}})})),1)],1),a("el-form-item",{staticClass:"item-style",attrs:{label:"申请服务：",prop:"apply_service"}},[a("custom-input",{attrs:{value:e.params.apply_service,maxLength:6,natureType:"apply_service",disabled:e.isDisabledEdit},on:{inputChange:e.inputChangeBankInfo}})],1),a("div",{staticClass:"text-gray-12 m-b-20 w-400"},[e._v("申请服务由六位数0或1组成的代码，如010101，每一位代表是否开通下列服务：第1位-PC（PC网站） 第2位-WAP（手机网站） 第3位-APP（APP支付） 第4位-JSAPI(公众号支付) 第5位-APPLET(小程序支付) 第6位-MICROPAY/F2F（付款码支付/当面付）")])],1)],1)],1)])])},n=[function(){var e=this,a=e._self._c;return a("div",{staticClass:"table-header"},[a("div",{staticClass:"table-title"},[e._v("银行账户信息")])])}],i=t("c3cc"),r=t("ddcc"),s=t("ed08"),u=t("d0dd"),c={name:"merchantBankInfo",props:{subParams:{type:Object,default:function(){return{}}}},data:function(){return{params:this.subParams,isDisabledEdit:this.subParams.isDisabledEdit,dicAccountType:Object(s["f"])(r["DIC_ACCOUNT_TYPE"]),contactInfoRules:{account:[{required:!0,message:"请输入银行账号",trigger:"blur"}],account_name:[{required:!0,message:"请输入银行账户户名",trigger:"blur"}],bank_name:[{required:!0,message:"请输入开户银行名称",trigger:"blur"}],mobile_phone:[{required:!0,message:"请输入银行预留手机号",trigger:"blur"},{validator:u["g"],trigger:"blur"}],account_type:[{required:!0,message:"请选择账户类型",trigger:"change"}],apply_service:[{required:!0,message:"请输入申请服务",trigger:"blur"}]}}},components:{customInput:i["default"]},watch:{subParams:{handler:function(e){this.params=Object(s["f"])(e),this.isDisabledEdit=this.params.isDisabledEdit},deep:!0}},methods:{changeSelect:function(e,a){this.$set(this.params,a,e),this.$emit("changeSelect",e,a)},inputChangeBankInfo:function(e,a){this.$set(this.params,a,e),this.$emit("inputChange",e,a)},checkParams:function(){var e=this;return new Promise((function(a){e.$refs.bankInfo.validate((function(e){a(!!e)}))}))}}},o=c,_=(t("9792"),t("2877")),m=Object(_["a"])(o,l,n,!1,null,"322e15aa",null);a["default"]=m.exports},d0dd:function(e,a,t){"use strict";t.d(a,"a",(function(){return l})),t.d(a,"b",(function(){return n})),t.d(a,"g",(function(){return i})),t.d(a,"c",(function(){return r})),t.d(a,"f",(function(){return s})),t.d(a,"d",(function(){return u})),t.d(a,"e",(function(){return c}));var l=function(e,a,t){if(a){var l=/^-?(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;l.test(a)?t():t(new Error("金额格式有误"))}else t(new Error("请输入金额"))},n=function(e,a,t){if(a){var l=/^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;l.test(a)?t():t(new Error("金额格式有误"))}else t()},i=function(e,a,t){if(!a)return t(new Error("手机号不能为空"));var l=/^1[3456789]\d{9}$/;l.test(a)?t():t(new Error("请输入正确手机号"))},r=function(e,a,t){if(!a)return t(new Error("金额有误"));var l=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;l.test(a)?t():t(new Error("金额格式有误"))},s=function(e,a,t){if(""===a)return t(new Error("不能为空"));var l=/^\d+$/;l.test(a)?t():t(new Error("请输入正确数字"))},u=function(e,a,t){if(""!==a){var l=/^(\+|-)?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;l.test(a)?t():t(new Error("金额格式有误"))}else t(new Error("请输入金额"))},c=function(e,a,t){var l=/^[\u4E00-\u9FA5\w-]+$/;l.test(a)?t():t(new Error("格式不正确，不能包含特殊字符"))}},ddcc:function(e,a,t){"use strict";t.r(a),t.d(a,"TABLE_HEAD_DATA",(function(){return l})),t.d(a,"SEARCH_FORM_SET_DATA",(function(){return n})),t.d(a,"DIC_MERCHANT_STATUS",(function(){return i})),t.d(a,"DIC_MERCHANT_TYPE",(function(){return r})),t.d(a,"DIC_MERCHANT_ID_TYPE",(function(){return s})),t.d(a,"DIC_PERSON_MERCHANT_CATEGORY",(function(){return u})),t.d(a,"DIC_MERCHANT_CONTACT_ID",(function(){return c})),t.d(a,"DIC_CERTIFICATE_TYPE",(function(){return o})),t.d(a,"DIC_ACCOUNT_TYPE",(function(){return _})),t.d(a,"DIC_IS_NOT",(function(){return m})),t.d(a,"UPLOAD_DIALOG_DATA_LIST",(function(){return p})),t.d(a,"PRINT_BANK_TABBLE_SETTING",(function(){return d})),t.d(a,"DEFAULT_CHANNEL_TABLE_SETTING",(function(){return b}));var l=[{label:"二级商户编号",key:"sub_mch_id",width:"140",fixed:"left"},{label:"一级商户",key:"php_path",width:"120"},{label:"二级商户名称",key:"sub_mch_name",width:"120"},{label:"二级商户类型",key:"sub_mch_type_alias",width:"120"},{label:"二级商户证件类型",key:"company_cert_type_alias",width:"140"},{label:"二级商户证件有效期结束时间",key:"end_certificate_validity",width:"140"},{label:"法定代表人",key:"contact_name",width:"120"},{label:"法定代表人证件类型",key:"certificate_type_alias",width:"150"},{label:"法定代表人证件编号",key:"certificate_no",width:"150"},{label:"法定代表人证件有效结束时间",key:"fr_cert_end_date",width:"140"},{label:"银行账号",key:"account",width:"120"},{label:"银行账户户名",key:"account_name",width:"120"},{label:"开户银行名称",key:"bank_name",width:"120"},{label:"银行预留手机号",key:"mobile_phone",width:"120"},{label:"账户类型",key:"account_type_alias",width:"120"},{label:"二级商户状态",key:"is_passed_alias",width:"120"},{label:"历史二级商户号",key:"history_sub_mch_ids",type:"slot",slotName:"detail",width:"120"},{label:"签署组织",key:"organization_name",width:"120",type:"slot",slotName:"organizationName"},{label:"签署账号",key:"get_agreement_info",width:"120",type:"slot",slotName:"accountName"},{label:"是否签署协议",key:"is_sign",width:"120",type:"slot",slotName:"isSign"},{label:"操作人",key:"operator",width:"120"},{label:"操作时间",key:"update_time",width:"120"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"200"}],n={sub_mch_id:{type:"input",value:"",label:"二级商户编号",placeholder:"请输入二级商户编号"},sub_mch_name:{type:"input",value:"",label:"二级商户名称",placeholder:"请输入二级商户名称"},is_passed:{type:"select",label:"商户状态",value:[],placeholder:"请选择商户状态",listNameKey:"name",listValueKey:"value",dataList:[]},php_path:{type:"input",value:"",label:"一级商户",placeholder:"请输入一级商户"}},i=[{name:"全部",value:"",label:""},{name:"已验证,未审核(不可交易)",value:"0",label:"VERIFIED_NOT_REVIEWED_0"},{name:"已验证,审核通过",value:"1",label:"VERIFIED_REVIEWED"},{name:"已验证,未审核(暂可交易)",value:"2",label:"VERIFIED_NOT_REVIEWED_2"},{name:"未验证,未审核",value:"3",label:"NOT_VERIFIED_NOT_REVIEWED"},{name:"已解约",value:"4",label:"TERMINATED"},{name:"已关闭",value:"5",label:"CLOSED"},{name:"审核拒绝",value:"8",label:"AUDIT_REJECTED"},{name:"驳回",value:"9",label:"TURN_DOWN"}],r=[{name:"个人商户",value:"1",label:"INDIVIDUAL"},{name:"企业",value:"2",label:"ENTERPRISE"},{name:"个体工商户",value:"3",label:"INDIVIDUAL_BUSINESS"},{name:"政府、金融机构及事业单位",value:"4",label:"BUSINESS_UNIT"}],s=[{name:"个体工商户营业执照",value:"610049",label:"INDIVIDUAL_LICENSE"},{name:"企业营业执照",value:"610047",label:"ENTERPRISE_LICENSE"},{name:"组织机构代码",value:"610001",label:"ORGANIZATION_CODE"},{name:"统一社会信用代码",value:"611009",label:"CREDIT_CODE"},{name:"事业单位法人证书",value:"610170",label:"BUSINESS_UNIT_CERTIFICATE"},{name:"社会团体登记证书",value:"610023",label:"SOCIAL_GROUPS_CERTIFICATE"},{name:"民办非企业登记证书",value:"610025",label:"PRIVATE_CERTIFICATE"},{name:"农民专业合作社营业执照",value:"610079",label:"FARMER_LICENSE"},{name:"主管部门颁居民委员会批文",value:"610033",label:"COMMITTEE_APPROVAL"},{name:"政府主管部门批文",value:"610037",label:"GOVERNMENT_APPROVAL"},{name:"财政部门证明",value:"610039",label:"FINANCIAL_PROVE"},{name:"其他机构证件标识",value:"619999",label:"OTHER"}],u=[{name:"有固定经营场所的实体商户",value:"0",label:"FIXED"},{name:"无固定经营场所的实体商户",value:"1",label:"NOT_FIXED"},{name:"网络商户",value:"2",label:"NET"}],c=[{name:"商户信息核实联系人",value:"01",label:"VERIFY_CONTACT"},{name:"商户巡检联系人",value:"02",label:"INSPECTION_CONTACT"},{name:"客户投诉处理联系人",value:"03",label:"COMPLAINT_HANDLING_CONTACT"}],o=[{name:"身份证",value:"110001",label:"ID_CARD"},{name:"临时居民身份证",value:"110003",label:"TEMPORARY_ID_CARD"},{name:"中国人民解放军军人身份证件",value:"110007",label:"MILITARY_ID"},{name:"中国人民武装警察身份证件",value:"110009",label:"POLICE_ID"},{name:"港澳居民来往内地通行证",value:"110019",label:"HONG_KONG_AND_MACAU_PASS"},{name:"台湾居民来往大陆通行证",value:"110021",label:"TAIWAN_PASS"},{name:"中华人民共和国护照",value:"110023",label:"CHINESE_PASSPORT"},{name:"外国护照",value:"110025",label:"FOREIGN_PASSPORT"},{name:"其他证件",value:"119999",label:"OTHER"}],_=[{name:"借记卡",value:"401",label:"DEBIT_CARD"},{name:"企业户",value:"601",label:"ENTERPRISE_HOUSEHOLD"},{name:"二类户",value:"701",label:"CLASS_II_HOUSEHOLDS"},{name:"三类户",value:"702",label:"CLASS_III_HOUSEHOLDS"}],m=[{name:"是",value:!0},{name:"否",value:!1}],p=[{name:"法人身份证人像面照片",required:!0,fileName:"",fileKey:"id_card_face_url",fileUrl:""},{name:"法人身份证国徽面照片",required:!0,fileName:"",fileKey:"id_card_national_emblem_url",fileUrl:""},{name:"法人护照、通行证照片",required:!0,fileName:"",fileKey:"passport_url",fileUrl:""},{name:"个体工商户/企业营业执照照片",required:!0,fileName:"",fileKey:"license_url",fileUrl:""},{name:"辅助证明材料",required:!0,fileName:"",fileKey:"auxiliary_proof_url",fileUrl:""},{name:"政府机关/事业单位/社会组织登记证书照片",required:!0,fileName:"",fileKey:"certificate_url",fileUrl:""},{name:"法定代表人授权函",required:!1,fileName:"",fileKey:"authorization_letter_url",fileUrl:""},{name:"定位证明材料",required:!1,fileName:"",fileKey:"gps_prove_url",fileUrl:""},{name:"固定经营场所证明材料",required:!1,fileName:"",fileKey:"fixed_place_prove_url",fileUrl:""},{name:"合法合规用途证明材料：",required:!1,fileName:"",fileKey:"use_prove_url",fileUrl:""}],d=[{key:"ori_sub_mer_no",label:"*原二级商户号"},{key:"sub_merchant_short_name",label:"*二级商户名称"},{key:"sub_mch_type",label:"*二级商户类型"},{key:"sub_mch_name",label:"*二级商户经营名称"},{key:"service_phone",label:"*二级商户客服电话"},{key:"industry",label:"*二级商户所属行业"},{key:"business_range",label:"二级商户经营范围"},{key:"address",label:"*二级商户实际经营地址"},{key:"company_cert_type",label:"二级商户证件类型"},{key:"company_cert_no",label:"二级商户证件编号"},{key:"end_certificate_validity",label:"二级商户证件有效期"},{key:"sub_mer_class",label:"个人商户类别"},{key:"account",label:"*银行账号"},{key:"account_name",label:"*银行账户户名"},{key:"bank_name",label:"*开户银行名称"},{key:"mobile_phone",label:"*银行预留手机号"},{key:"account_type",label:"*账户类型"},{key:"apply_service",label:"*申请服务"},{key:"sub_mer_contact_name",label:"*联系人姓名"},{key:"mer_mobile_phone",label:"*联系人手机号码"},{key:"sub_mer_contact_cert",label:"*联系人证件号码"},{key:"sub_mer_contact_mail",label:"联系人邮箱"},{key:"sub_mer_contact_type",label:"*商户联系人业务标识"},{key:"contact_name",label:"*法定代表人姓名"},{key:"certificate_type",label:"*法定代表人证件类型"},{key:"certificate_no",label:"*法定代表人证件编号"},{key:"certificate_beg_date",label:"*法定代表人证件有效期开始时间"},{key:"fr_cert_end_date",label:"*法定代表人证件有效期结束时间"},{key:"fr_residence",label:"*法定代表人证件居住地址"},{key:"fr_is_controller",label:"法定代表人是否为受益所有人"},{key:"fr_is_agent",label:"法定代表人是否为实际办理业务人员"},{key:"controller_name",label:"受益所有人姓名"},{key:"controller_cert_type",label:"受益所有人证件类型"},{key:"controller_cert_no",label:"受益所有人证件号码"},{key:"controller_cert_beg_date",label:"受益所有人证件有效期开始时间"},{key:"controller_cert_end_date",label:"受益所有人证件有效期结束时间"},{key:"controller_residence",label:"受益所有人证件居住地址"},{key:"agent_name",label:"授权办理业务人员姓名"},{key:"agent_cert_type",label:"授权办理业务人员证件类型"},{key:"agent_cert_no",label:"授权办理业务人员证件号码"},{key:"agent_cert_beg_date",label:"授权办理业务人员证件有效期开始时间"},{key:"agent_cert_end_date",label:"授权办理业务人员证件有效期结束时间"},{key:"agent_residence",label:"授权办理业务人员证件居住地址"}],b=[{label:"所属项目",key:"name",align:"left"},{label:"所属地址",key:"district_alias",type:"slot",slotName:"districtAlias"},{label:"监管渠道",key:"supervision_channel_name"},{label:"操作",key:"operation",type:"slot",slotName:"operation",width:"200"}]}}]);