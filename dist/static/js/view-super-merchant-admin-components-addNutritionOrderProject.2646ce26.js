(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-merchant-admin-components-addNutritionOrderProject","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-meal-report-MealPackageRule"],{a86c:function(e,a,t){"use strict";t("ff47")},b2f2:function(e,a,t){"use strict";t.r(a);var n=function(){var e=this,a=e._self._c;return a("el-drawer",e._g(e._b({staticClass:"drawer-wrapper",attrs:{title:"选择项目",visible:e.showDrawer,direction:e.direction,wrapperClosable:e.wrapperClosable},on:{"update:visible":function(a){e.showDrawer=a}}},"el-drawer",e.$attrs,!1),e.$listeners),[a("div",{staticClass:"p-20"},[a("el-form",{ref:"formRef",attrs:{model:e.formData,"label-width":"80px",rules:e.formRules}},[a("el-form-item",{attrs:{label:"项目",prop:"companyId"}},[a("company-select",{staticClass:"search-item-w ps-select",staticStyle:{width:"300px"},attrs:{clearable:!0,filterable:!0,options:{label:"name",value:"company"},params:e.companyParams,placeholder:"请选择项目"},on:{getselect:e.getCompanySelect},model:{value:e.formData.companyId,callback:function(a){e.$set(e.formData,"companyId",a)},expression:"formData.companyId"}})],1),a("el-form-item",{attrs:{label:"日期",prop:"date"}},[a("el-date-picker",{staticStyle:{width:"300px"},attrs:{type:"date",placeholder:"请选择日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:e.formData.date,callback:function(a){e.$set(e.formData,"date",a)},expression:"formData.date"}})],1),a("el-form-item",{attrs:{label:"餐段",prop:"mealType"}},[a("el-select",{staticClass:"ps-input",staticStyle:{width:"300px"},attrs:{clearable:"",filterable:"",placeholder:"请选择餐段"},on:{change:e.changeMeal},model:{value:e.formData.mealType,callback:function(a){e.$set(e.formData,"mealType",a)},expression:"formData.mealType"}},e._l(e.mealTypeList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{staticClass:"m-t-60",attrs:{label:"",prop:""}},[a("el-button",{staticClass:"ps-cancel min-btn-w",on:{click:e.clickCancelHandle}},[e._v("取消")]),a("el-button",{staticClass:"ps-origin-btn min-btn-w",on:{click:e.clickConfirmHandle}},[e._v("确定")])],1)],1)],1)])},i=[],l=t("6e71"),r=t("c9d9"),m=t("ed08"),o={name:"",components:{CompanySelect:l["a"]},props:{show:{required:!0,type:Boolean},wrapperClosable:{type:Boolean,default:!0},direction:{type:String,default:"rtl"},infoData:{type:Object,default:function(){return{}}}},data:function(){return{formData:{companyName:"",companyId:"",date:Object(m["M"])(new Date,"{y}-{m}-{d}"),mealType:"",mealName:""},formRules:{companyId:[{required:!0,message:"请选择项目",trigger:"change"}],date:[{required:!0,message:"请选择日期",trigger:"change"}],mealType:[{required:!0,message:"请选择餐段",trigger:"change"}]},mealTypeList:r["a"],companyParams:{}}},computed:{showDrawer:{get:function(){return this.show&&(this.formData=Object.assign(this.formData,this.infoData)),this.show},set:function(e){this.$emit("update:show",e)}}},watch:{},created:function(){location.origin.indexOf("debug")<0?this.companyParams={company__in:[26,58]}:this.companyParams={}},mounted:function(){},methods:{getCompanySelect:function(e){this.formData.companyName=e.item.name},changeMeal:function(e){var a=this.mealTypeList.find((function(a){return a.value===e}));this.formData.mealName=a.label},clickCancelHandle:function(){this.showDrawer=!1},clickConfirmHandle:function(){var e=this;this.$refs.formRef.validate((function(a){a&&(e.$emit("confirm",e.formData),e.resetForm(),e.showDrawer=!1)}))},resetForm:function(){this.formData={companyName:"",companyId:"",date:"",mealType:""},this.$refs.formRef.clearValidate()}}},d=o,u=(t("a86c"),t("2877")),c=Object(u["a"])(d,n,i,!1,null,null,null);a["default"]=c.exports},c9d9:function(e,a,t){"use strict";t.d(a,"a",(function(){return l})),t.d(a,"d",(function(){return r})),t.d(a,"b",(function(){return m})),t.d(a,"c",(function(){return o})),t.d(a,"e",(function(){return d})),t.d(a,"f",(function(){return u})),t.d(a,"g",(function(){return c}));var n=t("5a0c"),i=t("da92"),l=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],r=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],m={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},o=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],d=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],u=(n().subtract(7,"day").format("YYYY-MM-DD"),n().format("YYYY-MM-DD"),function(e){return e?"number"===typeof e?i["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:i["a"].divide(e,100).toFixed(2):"0.00"}),c=function(e){return i["a"].times(e,100)}},ff47:function(e,a,t){}}]);