(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-merchant-admin-components-channelInfo","view-super-merchant-admin-components-BankMerchant-merchantBaseInfo","view-super-merchant-admin-components-PermissionConfiguration","view-super-merchant-admin-constants-bankMerchantConstants"],{1122:function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"ps-el-drawer"},[t("el-drawer",{attrs:{visible:e.visible,"show-close":!1,size:"75%"},scopedSlots:e._u([{key:"title",fn:function(){return[t("div",{staticClass:"ps-flex-align-c flex-align-c"},[t("div",{staticClass:"m-r-20"},[e._v("资金监管平台")]),t("div",{staticClass:"ps-el-popover"},[t("el-popover",{attrs:{placement:"bottom",title:"部分特殊权限控制说明：",width:"710",trigger:"hover"}},[t("div",{staticClass:"popover"},[e._v(" 留着看看有啥用 ")]),t("i",{staticClass:"el-icon-question",attrs:{slot:"reference"},slot:"reference"})])],1)])]},proxy:!0}])},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],staticClass:"p-20"},[t("el-tabs",{staticClass:"version-configuration-content-box",attrs:{type:"card","tab-position":"left"}},e._l(e.merchantFeatureList,(function(n,r){return t("el-tab-pane",{key:r,attrs:{label:n.verbose_name}},[t("div",{staticClass:"p-20"},[t("div",{staticClass:"m-b-10 w-150"},[t("el-checkbox",{staticClass:"ps-flex-align-c flex-align-c",attrs:{indeterminate:n.isIndeterminate,disabled:n.canNotSelect},model:{value:n.isSelect,callback:function(t){e.$set(n,"isSelect",t)},expression:"item.isSelect"}},[t("span",{staticClass:"font-size-18 f-w-500"},[e._v("全选当前页")])])],1),e._l(n.children,(function(n,r){return t("div",{key:r,staticClass:"m-b-20"},[t("div",{staticClass:"w-150"},[t("el-checkbox",{staticClass:"ps-flex-align-c flex-align-c m-b-10",attrs:{indeterminate:n.isIndeterminate,disabled:n.canNotSelect},model:{value:n.isSelect,callback:function(t){e.$set(n,"isSelect",t)},expression:"item1.isSelect"}},[t("span",{staticClass:"font-size-18 f-w-700"},[e._v(e._s(n.verbose_name))])])],1),t("div",{staticStyle:{"border-top":"1px solid #e5e7ea"}},e._l(n.children,(function(n,r){return t("div",{key:r,staticClass:"box-item flex-start",style:r%2===0?{backgroundColor:"#ffffff"}:{backgroundColor:"#f8f9fa"}},[t("div",{class:[n.children.length?"":"box-item-left","p-20"]},[t("el-checkbox",{attrs:{indeterminate:n.isIndeterminate},model:{value:n.isSelect,callback:function(t){e.$set(n,"isSelect",t)},expression:"item2.isSelect"}},[t("span",{staticClass:"ellipsis w-100"},[e._v(e._s(n.verbose_name))])])],1),t("div",{class:[n.children.length?"box-item-right1":"","p-20","w-100-p"]},e._l(n.children,(function(n,r){return t("div",{key:r},[t("el-checkbox",{attrs:{disabled:n.canNotSelect},model:{value:n.isSelect,callback:function(t){e.$set(n,"isSelect",t)},expression:"item3.isSelect"}},[t("span",{staticClass:"ellipsis w-150"},[e._v(e._s(n.verbose_name))])])],1)})),0),t("div",{class:[n.children.length?"box-item-right2":"","w-100-p"]},e._l(n.children,(function(r,a){return t("div",{key:a,staticClass:"three-level flex-start",style:a<n.children.length-1?{borderBottom:"1px solid #e5e7ea"}:{}},[t("el-checkbox",{staticClass:"p-20",attrs:{disabled:r.canNotSelect},model:{value:r.isSelect,callback:function(t){e.$set(r,"isSelect",t)},expression:"item3.isSelect"}},[t("span",{staticClass:"ellipsis w-150"},[e._v(e._s(r.verbose_name))])]),r.children.length?t("div",{staticClass:"three-level-right p-20 w-100-p"},e._l(r.children,(function(n,r){return t("div",{key:r},[t("el-checkbox",{attrs:{disabled:n.canNotSelect},model:{value:n.isSelect,callback:function(t){e.$set(n,"isSelect",t)},expression:"item4.isSelect"}},[t("span",{staticClass:"ellipsis w-150"},[e._v(e._s(n.verbose_name))])])],1)})),0):e._e()],1)})),0)])})),0)])}))],2)])})),1),t("div",{staticClass:"version-configuration-content-footer"},[t("div",{staticClass:"button-area m-r-40"},[t("el-button",{staticClass:"w-100",attrs:{size:"small"},on:{click:e.cancel}},[e._v("取消")]),t("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:e.save}},[e._v("保存")])],1),t("div",{staticClass:"checkbox-area m-r-40"},[t("el-checkbox",{on:{change:function(t){return e.isSelectAll("selectAll",!0)}},model:{value:e.selectAll,callback:function(t){e.selectAll=t},expression:"selectAll"}},[t("span",{staticClass:"font-size-16"},[e._v("全选")])]),t("el-checkbox",{on:{change:function(t){return e.isSelectAll("selectNone",!1)}},model:{value:e.selectNone,callback:function(t){e.selectNone=t},expression:"selectNone"}},[t("span",{staticClass:"font-size-16"},[e._v("全不选")])])],1),t("div",[e._v("定制数量："+e._s(1)+"/"+e._s(1))])])],1)])],1)},a=[];n("ed08");function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function o(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */o=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,a=Object.defineProperty||function(e,t,n){e[t]=n.value},l="function"==typeof Symbol?Symbol:{},s=l.iterator||"@@iterator",c=l.asyncIterator||"@@asyncIterator",u=l.toStringTag||"@@toStringTag";function d(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(e){d=function(e,t,n){return e[t]=n}}function h(e,t,n,r){var i=t&&t.prototype instanceof g?t:g,o=Object.create(i.prototype),l=new D(r||[]);return a(o,"_invoke",{value:L(e,n,l)}),o}function f(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=h;var p="suspendedStart",m="suspendedYield",b="executing",v="completed",_={};function g(){}function y(){}function w(){}var C={};d(C,s,(function(){return this}));var k=Object.getPrototypeOf,O=k&&k(k(I([])));O&&O!==n&&r.call(O,s)&&(C=O);var E=w.prototype=g.prototype=Object.create(C);function S(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function T(e,t){function n(a,o,l,s){var c=f(e[a],e,o);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==i(d)&&r.call(d,"__await")?t.resolve(d.__await).then((function(e){n("next",e,l,s)}),(function(e){n("throw",e,l,s)})):t.resolve(d).then((function(e){u.value=e,l(u)}),(function(e){return n("throw",e,l,s)}))}s(c.arg)}var o;a(this,"_invoke",{value:function(e,r){function a(){return new t((function(t,a){n(e,r,t,a)}))}return o=o?o.then(a,a):a()}})}function L(t,n,r){var a=p;return function(i,o){if(a===b)throw Error("Generator is already running");if(a===v){if("throw"===i)throw o;return{value:e,done:!0}}for(r.method=i,r.arg=o;;){var l=r.delegate;if(l){var s=x(l,r);if(s){if(s===_)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===p)throw a=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=b;var c=f(t,n,r);if("normal"===c.type){if(a=r.done?v:m,c.arg===_)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(a=v,r.method="throw",r.arg=c.arg)}}}function x(t,n){var r=n.method,a=t.iterator[r];if(a===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,x(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),_;var i=f(a,t.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,_;var o=i.arg;return o?o.done?(n[t.resultName]=o.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,_):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,_)}function N(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function A(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function D(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(N,this),this.reset(!0)}function I(t){if(t||""===t){var n=t[s];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,o=function n(){for(;++a<t.length;)if(r.call(t,a))return n.value=t[a],n.done=!1,n;return n.value=e,n.done=!0,n};return o.next=o}}throw new TypeError(i(t)+" is not iterable")}return y.prototype=w,a(E,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:y,configurable:!0}),y.displayName=d(w,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,d(e,u,"GeneratorFunction")),e.prototype=Object.create(E),e},t.awrap=function(e){return{__await:e}},S(T.prototype),d(T.prototype,c,(function(){return this})),t.AsyncIterator=T,t.async=function(e,n,r,a,i){void 0===i&&(i=Promise);var o=new T(h(e,n,r,a),i);return t.isGeneratorFunction(n)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},S(E),d(E,u,"Generator"),d(E,s,(function(){return this})),d(E,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=I,D.prototype={constructor:D,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(A),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function a(r,a){return l.type="throw",l.arg=t,n.next=r,a&&(n.method="next",n.arg=e),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],l=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var s=r.call(o,"catchLoc"),c=r.call(o,"finallyLoc");if(s&&c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,_):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),_},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),A(n),_}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var a=r.arg;A(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:I(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),_}},t}function l(e,t,n,r,a,i,o){try{var l=e[i](o),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,a)}function s(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function o(e){l(i,r,a,o,s,"next",e)}function s(e){l(i,r,a,o,s,"throw",e)}o(void 0)}))}}var c={props:{isShow:Boolean},data:function(){return{isLoading:!1,merchantFeatureList:[],selectNone:!1,selectAll:!1,haveBeenSelectKey:[],versionList:[],selectedVersionData:{}}},computed:{visible:{get:function(){return this.isShow},set:function(e){this.$emit("update:isShow",e)}}},watch:{selectAll:function(e,t){e&&this.selectNone&&(this.selectNone=!1)},selectNone:function(e,t){e&&this.selectAll&&(this.selectAll=!1)},visible:function(e){}},created:function(){},methods:{getPermissionList:function(){return s(o().mark((function e(){return o().wrap((function(e){while(1)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e)})))()},save:function(){this.selectAll=!1,this.selectNone=!1,this.visible=!1},cancel:function(){this.selectAll=!1,this.selectNone=!1,this.visible=!1}}},u=c,d=(n("7f4b"),n("2877")),h=Object(d["a"])(u,r,a,!1,null,"b866637c",null);t["default"]=h.exports},"3ef7":function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"channel-info-content"},[t("el-form",{attrs:{"label-position":"right","label-width":"120px",model:e.formLabelAlign}},[t("el-form-item",{attrs:{label:"所属上级："}},[t("div",[e._v(e._s(e.formLabelAlign.father||"--"))])]),t("el-form-item",{attrs:{label:"渠道名称："}},[t("div",{staticClass:"ps-flex-align-c"},[t("div",{staticClass:"m-r-20"},[e._v(e._s(e.formLabelAlign.name||"--"))]),t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.showEditChannelDrawer(e.formLabelAlign.name)}}},[e._v("修改")])],1)]),t("el-form-item",{attrs:{label:"跳转链接："}},[t("el-button",{attrs:{type:"text",disabled:!e.formLabelAlign.token},on:{click:function(t){return e.gotoPage(e.formLabelAlign.token)}}},[e._v("登录")])],1),t("el-form-item",{attrs:{label:"绑定组织："}},[t("div",{staticClass:"flex-b-c"},[t("el-button",{staticClass:"m-r-20",attrs:{type:"text"},on:{click:e.gotoBind}},[e._v("去绑定")]),t("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"",placement:"top-end"}},[t("div",{staticClass:"text-center",attrs:{slot:"content"},slot:"content"},[e._v(" 进行绑定操作时，如上级/下级已被绑定，请先"),t("br"),e._v("解绑后再进行操作，解绑前请自行记录当前绑"),t("br"),e._v("定组织的数据权限。绑定后可根据数据需求进"),t("br"),e._v("行组织树的权限控制。 ")]),t("i",{staticClass:"el-icon-question font-size-24"})])],1),t("div",{staticClass:"table-style"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],attrs:{data:e.formLabelAlign.org,stripe:"","header-row-class-name":"ps-table-header-row"}},e._l(e.tableSetting,(function(n,r){return t("table-column",{key:r,attrs:{col:n},scopedSlots:e._u([{key:"updateTime",fn:function(t){var n=t.row;return[e._v(" "+e._s(e.computedTime(n.update_time))+" ")]}},{key:"operation",fn:function(n){var r=n.row;return[t("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.setPermission(r)}}},[e._v("数据权限")]),t("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.removeHandle(r)}}},[e._v("移除")])]}}],null,!0)})})),1)],1)])],1),t("div",{staticClass:"ps-el-drawer"},[t("el-drawer",{attrs:{title:"修改渠道名称",visible:e.editChannelDrawerShow,"show-close":!1,size:"40%"}},[t("div",{staticClass:"p-20"},[t("el-form",{ref:"editChannelFormRef",attrs:{model:e.editChannelForm,"label-width":"80px","label-position":"left"}},[t("el-form-item",{attrs:{label:"渠道名称",prop:"name",rules:[{required:!0,message:"请输入渠道名称",trigger:["change","blur"]}]}},[t("el-input",{staticClass:"w-300",attrs:{placeholder:"请输入渠道名称，不超过30个字",maxlength:"30"},model:{value:e.editChannelForm.name,callback:function(t){e.$set(e.editChannelForm,"name",t)},expression:"editChannelForm.name"}})],1)],1),t("div",{staticClass:"ps-el-drawer-footer"},[t("el-button",{staticClass:"w-100",attrs:{size:"small"},on:{click:function(t){return e.cancelHandle("edit")}}},[e._v("取消")]),t("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:function(t){return e.saveHandle("edit")}}},[e._v("保存")])],1)],1)]),t("el-drawer",{attrs:{title:"绑定组织",visible:e.bindOrgDrawerShow,"show-close":!1,size:"40%"}},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.checkChannelLoading,expression:"checkChannelLoading"}],staticClass:"p-20"},[t("div",{staticClass:"red m-b-10 font-size-14"},[e._v("绑定需要保存后生效。本次新增绑定组织："+e._s(e.addCount)+"个")]),t("el-form",{ref:"bindOrgFormRef",attrs:{model:e.bindOrgForm,"label-width":"80px","label-position":"left"}},[t("el-form-item",{attrs:{label:"项目名称",prop:"name",rules:{required:!e.bindOrgForm.address,message:"请输入项目名称",trigger:["change","blur"]}}},[t("el-select",{staticClass:"w-300",attrs:{filterable:"",clearable:"",placeholder:"输入项目点名称进行查询"},model:{value:e.bindOrgForm.name,callback:function(t){e.$set(e.bindOrgForm,"name",t)},expression:"bindOrgForm.name"}},e._l(e.orgList,(function(e,n){return t("el-option",{key:n,attrs:{label:e.name,value:e.id}})})),1)],1),t("el-form-item",{attrs:{label:"项目地址",prop:"address",rules:{required:!e.bindOrgForm.name,message:"请输入项目地址",trigger:["change","blur"]}}},[t("el-cascader",{ref:"cascaderRef",staticClass:"w-300 m-r-20",attrs:{placeholder:"请选择项目点地址进行查询",options:e.addrOptions,filterable:""},model:{value:e.bindOrgForm.address,callback:function(t){e.$set(e.bindOrgForm,"address",t)},expression:"bindOrgForm.address"}}),t("el-button",{staticClass:"ps-origin-btn",on:{click:e.getBingOrgList}},[e._v("查询")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.bindLoading,expression:"bindLoading"}],attrs:{data:e.bindOrgTableData,stripe:"","header-row-class-name":"ps-table-header-row","row-key":"id","default-expand-all":"","tree-props":{children:"children_list",hasChildren:"hasChildren"}}},e._l(e.bindOrgTableSetting,(function(n,r){return t("table-column",{key:n.key+r,attrs:{col:n},scopedSlots:e._u([{key:"districtAlias",fn:function(n){var r=n.row;return[t("span",[e._v(e._s(e.computedAddress(r.district_alias)))])]}},{key:"operation",fn:function(n){var r=n.row;return[r.supervision_channel_bind?t("div",[t("el-popconfirm",{attrs:{title:"解绑后项目数据将不再上传，确定要解绑？"},on:{confirm:function(t){return e.bindHandle(r,"unBind")}}},[t("el-button",{staticClass:"ps-red",attrs:{slot:"reference",type:"text",size:"small",disabled:0!==r.supervision_channel_id&&e.organizationData.id!==r.supervision_channel_id},slot:"reference"},[e._v("解绑")])],1)],1):t("div",[t("el-button",{staticClass:"ps-origin-text",attrs:{type:"text",size:"small",disabled:!r.isCanBind},on:{click:function(t){return e.bindHandle(r,"unBind")}}},[e._v("绑定")])],1)]}}],null,!0)})})),1),t("div",{staticClass:"ps-el-drawer-footer"},[t("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:function(t){return e.cancelHandle("bind")}}},[e._v("关闭")])],1)],1)]),t("el-drawer",{attrs:{title:"数据权限",visible:e.dataPermissionDrawerShow,"show-close":!1,size:"40%"}},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.dataPermissionLoading,expression:"dataPermissionLoading"}],staticClass:"p-20"},[t("span",[e._v("注：默认计算绑定的组织及下级组织的数据，被选择的组织为禁用状态，被禁用的组织将不统计该项数据。仅适用绑定组织为只计算绑定的组织数据其下级组织全为禁用状态。")]),t("el-form",{ref:"dataPermissionDataRef",attrs:{model:e.dataPermissionData,"label-position":"right","label-width":"100px"}},e._l(e.dataPermissionData.list,(function(n,r){return t("el-form-item",{key:r,attrs:{label:n.label}},[t("el-switch",{attrs:{"active-text":"全部禁用"},on:{change:function(t){return e.allIsOpenClose(n)}},model:{value:n.isAllClose,callback:function(t){e.$set(n,"isAllClose",t)},expression:"item.isAllClose"}}),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],attrs:{"show-header":!1,data:n.orgData,stripe:"","header-row-class-name":"ps-table-header-row","default-expand-all":"","row-key":"id","tree-props":{children:"children_list",hasChildren:"hasChildren"}}},e._l(e.permissionSetting,(function(r,a){return t("table-column",{key:a,attrs:{col:r},scopedSlots:e._u([{key:"operation",fn:function(r){var a=r.row;return[t("el-radio-group",{on:{input:function(t){return e.changeOrgTreeStatus(e.orgTree,a,n.dataType)}},model:{value:a.isOpen,callback:function(t){e.$set(a,"isOpen",t)},expression:"row.isOpen"}},[t("el-radio",{attrs:{label:"1"}},[e._v("启用")]),t("el-radio",{attrs:{label:"0"}},[e._v("禁用")])],1),t("el-checkbox",{staticClass:"m-l-10",on:{change:function(t){return e.selectThisIsOpen(a,n.orgData)}},model:{value:a.selectThisRow,callback:function(t){e.$set(a,"selectThisRow",t)},expression:"row.selectThisRow"}},[e._v("仅当前")])]}}],null,!0)})})),1)],1)})),1),t("div",{staticClass:"ps-el-drawer-footer"},[t("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:function(t){return e.cancelHandle("permission")}}},[e._v("关闭")]),t("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:function(t){return e.saveHandle("permission")}}},[e._v("保存")])],1)],1)])],1),t("PermissionConfiguration",{attrs:{isShow:e.permissionConfigurationShow}})],1)},a=[],i=n("ed08"),o=n("ddcc"),l=n("ef6c"),s=n("1122"),c=n("5a0c"),u=n.n(c);function d(e){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},d(e)}function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function f(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach((function(t){p(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function p(e,t,n){return(t=m(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function m(e){var t=b(e,"string");return"symbol"==d(t)?t:t+""}function b(e,t){if("object"!=d(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=d(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function v(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */v=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,a=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function u(e,t,n,r){var i=t&&t.prototype instanceof g?t:g,o=Object.create(i.prototype),l=new D(r||[]);return a(o,"_invoke",{value:L(e,n,l)}),o}function h(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var f="suspendedStart",p="suspendedYield",m="executing",b="completed",_={};function g(){}function y(){}function w(){}var C={};c(C,o,(function(){return this}));var k=Object.getPrototypeOf,O=k&&k(k(I([])));O&&O!==n&&r.call(O,o)&&(C=O);var E=w.prototype=g.prototype=Object.create(C);function S(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function T(e,t){function n(a,i,o,l){var s=h(e[a],e,i);if("throw"!==s.type){var c=s.arg,u=c.value;return u&&"object"==d(u)&&r.call(u,"__await")?t.resolve(u.__await).then((function(e){n("next",e,o,l)}),(function(e){n("throw",e,o,l)})):t.resolve(u).then((function(e){c.value=e,o(c)}),(function(e){return n("throw",e,o,l)}))}l(s.arg)}var i;a(this,"_invoke",{value:function(e,r){function a(){return new t((function(t,a){n(e,r,t,a)}))}return i=i?i.then(a,a):a()}})}function L(t,n,r){var a=f;return function(i,o){if(a===m)throw Error("Generator is already running");if(a===b){if("throw"===i)throw o;return{value:e,done:!0}}for(r.method=i,r.arg=o;;){var l=r.delegate;if(l){var s=x(l,r);if(s){if(s===_)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===f)throw a=b,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=m;var c=h(t,n,r);if("normal"===c.type){if(a=r.done?b:p,c.arg===_)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(a=b,r.method="throw",r.arg=c.arg)}}}function x(t,n){var r=n.method,a=t.iterator[r];if(a===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,x(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),_;var i=h(a,t.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,_;var o=i.arg;return o?o.done?(n[t.resultName]=o.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,_):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,_)}function N(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function A(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function D(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(N,this),this.reset(!0)}function I(t){if(t||""===t){var n=t[o];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,i=function n(){for(;++a<t.length;)if(r.call(t,a))return n.value=t[a],n.done=!1,n;return n.value=e,n.done=!0,n};return i.next=i}}throw new TypeError(d(t)+" is not iterable")}return y.prototype=w,a(E,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:y,configurable:!0}),y.displayName=c(w,s,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,c(e,s,"GeneratorFunction")),e.prototype=Object.create(E),e},t.awrap=function(e){return{__await:e}},S(T.prototype),c(T.prototype,l,(function(){return this})),t.AsyncIterator=T,t.async=function(e,n,r,a,i){void 0===i&&(i=Promise);var o=new T(u(e,n,r,a),i);return t.isGeneratorFunction(n)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},S(E),c(E,s,"Generator"),c(E,o,(function(){return this})),c(E,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=I,D.prototype={constructor:D,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(A),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function a(r,a){return l.type="throw",l.arg=t,n.next=r,a&&(n.method="next",n.arg=e),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],l=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var s=r.call(o,"catchLoc"),c=r.call(o,"finallyLoc");if(s&&c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,_):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),_},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),A(n),_}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var a=r.arg;A(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:I(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),_}},t}function _(e,t,n,r,a,i,o){try{var l=e[i](o),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,a)}function g(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function o(e){_(i,r,a,o,l,"next",e)}function l(e){_(i,r,a,o,l,"throw",e)}o(void 0)}))}}var y={components:{PermissionConfiguration:s["default"]},props:{organizationData:{type:Object,default:function(){return{}}},tabType:{type:String,default:""}},data:function(){return{isLoading:!1,bindLoading:!1,checkChannelLoading:!1,formLabelAlign:{father:"",name:"",token:"",org:[]},tableSetting:[{label:"绑定组织",key:"org_name"},{label:"所属项目",key:"company_name"},{label:"修改时间",key:"update_time",type:"slot",slotName:"updateTime"},{label:"操作人",key:"operator_name"},{label:"操作",key:"operation",type:"slot",slotName:"operation"}],editChannelDrawerShow:!1,editChannelForm:{name:""},bindOrgDrawerShow:!1,bindOrgForm:{name:"",address:""},orgList:[],addrOptions:l["regionData"],bindOrgTableData:[],bindOrgTableSetting:o["DEFAULT_CHANNEL_TABLE_SETTING"],permissionSetting:[{label:"组织名称",key:"name",align:"left"},{label:"操作",key:"operation",type:"slot",slotName:"operation"}],dataPermissionDrawerShow:!1,dataPermissionLoading:!1,dataPermissionData:{list:[{isAllClose:!1,dataType:"revenue_data",label:"收入数据",orgData:[]},{isAllClose:!1,dataType:"expenditure_data",label:"支出数据",orgData:[]},{isAllClose:!1,dataType:"recorded_data",label:"入账数据",orgData:[]},{isAllClose:!1,dataType:"supplier_information",label:"供应商信息",orgData:[]},{isAllClose:!1,dataType:"purchasing_data",label:"采购数据",orgData:[]},{isAllClose:!1,dataType:"in_out_data",label:"出入库数据",orgData:[]},{isAllClose:!1,dataType:"recipe_data",label:"食谱数据",orgData:[]},{isAllClose:!1,dataType:"sample_data",label:"留样数据",orgData:[]},{isAllClose:!1,dataType:"monitoring_data",label:"监控数据",orgData:[]}]},orgTree:[],addCount:0,selectRowData:{},permissionConfigurationShow:!1}},computed:{computedTime:function(){return function(e){return u()(e).format("YYYY-MM-DD HH:mm:ss")}},computedAddress:function(){return function(e){var t=e.replace(/^\[|\]$/g,"");return t}}},watch:{organizationData:{handler:function(e,t){e&&(this.formLabelAlign.father=e.parent_alias||"--",this.formLabelAlign.name=e.name||"--",this.formLabelAlign.token=e.get_login_token||"",this.formLabelAlign.org=Object(i["f"])(e.binded_org_info||[]))},immediate:!0,deep:!0}},created:function(){this.getOrganizationList()},methods:{cancelHandle:function(e){switch(e){case"edit":this.$refs.editChannelFormRef.resetFields(),this.editChannelDrawerShow=!1;break;case"bind":this.bindOrgDrawerShow=!1,this.$emit("refresh",this.organizationData);break;case"permission":this.$refs.dataPermissionDataRef.resetFields(),this.dataPermissionDrawerShow=!1;break}},showEditChannelDrawer:function(e){this.editChannelForm.name=e,this.editChannelDrawerShow=!0},saveHandle:function(e){switch(e){case"edit":this.changeName();break;case"permission":this.savePermission();break}},gotoBind:function(){this.addCount=0,this.bindOrgDrawerShow=!0},bindHandle:function(e,t){var n=this;return g(v().mark((function r(){var a;return v().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return a={supervision_channel_id:n.organizationData.id,org_id:e.id,bind_type:!e.supervision_channel_bind},r.next=3,n.$apis.apiBackgroundFundSupervisionSupervisionChannelOrgBingConfigPost(a).then((function(r){0===r.code?(e.supervision_channel_bind||n.addCount++,n.$message.success(e.supervision_channel_bind?"解绑成功":"绑定成功"),"unBind"===t?(n.bindOrgTableData=[],n.getBingOrgList()):"remove"===t&&(n.bindOrgTableData=[],n.$emit("refresh",n.organizationData))):n.$message.error(r.msg)}));case 3:case"end":return r.stop()}}),r)})))()},changeName:function(){var e=this,t={id:this.organizationData.id,name:this.editChannelForm.name};this.$apis.apiBackgroundFundSupervisionSupervisionChannelModifyPost(t).then((function(t){0===t.code?e.$message.success("修改成功"):e.$message.error(t.msg),e.editChannelDrawerShow=!1,e.$emit("refresh",e.organizationData)}))},savePermission:function(){var e=this,t=[];this.setConfigArr(this.orgTree,t),this.$apis.apiBackgroundFundSupervisionSupervisionChannelOrgPermissionConfigPost({supervision_channel_id:this.organizationData.id,config:t}).then((function(t){0===t.code?e.$message.success("保存成功"):e.$message.error(t.msg),e.dataPermissionDrawerShow=!1,e.$emit("refresh",e.organizationData)}))},allIsOpenClose:function(e){var t=this;e.isAllClose&&this.$apis.apiBackgroundFundSupervisionSupervisionChannelBatchDisableOrgPermissionPost({org_id:this.selectRowData.org_id,data_type:e.dataType}).then((function(e){0===e.code?t.setPermission(t.selectRowData):t.$message.error(e.msg)}))},changeOrgTreeStatus:function(e,t,n){var r=this;e.forEach((function(e){e.id===t.id?e[n]="1"===t.isOpen:e.has_children&&e.children_list.length&&r.changeOrgTreeStatus(e.children_list,t,n)}))},setConfigArr:function(e,t){var n=this;e.length&&e.forEach((function(e){var r={organization_id:e.id,revenue_data:e.revenue_data,expenditure_data:e.expenditure_data,recorded_data:e.recorded_data,purchasing_data:e.purchasing_data,supplier_information:e.supplier_information,in_out_data:e.in_out_data,recipe_data:e.recipe_data,sample_data:e.sample_data,monitoring_data:e.monitoring_data};t.push(r),e.has_children&&e.children_list.length&&n.setConfigArr(e.children_list,t)}))},getOrganizationList:function(){var e=this;this.checkChannelLoading=!0,this.$apis.apiBackgroundAdminOrganizationListPost({page:1,page_size:9999,parent__is_null:"1",status__in:["enable"]}).then((function(t){e.checkChannelLoading=!1,0===t.code?e.orgList=t.data.results.map((function(e){var t={id:e.id,name:e.name};return t})):e.$message.error(t.msg)}))},gotoPage:function(){window.open("https://baidu.com","_blank")},getBingOrgList:function(){var e=this;this.$refs.bindOrgFormRef.validate((function(t){t?(e.bindLoading=!0,e.$apis.apiBackgroundFundSupervisionSupervisionChannelTreeListPost({org_id:e.bindOrgForm.name||void 0,district:e.bindOrgForm.address?JSON.stringify(e.bindOrgForm.address):void 0}).then((function(t){if(0===t.code){var n=[];if(t.data.length){var r=t.data.map((function(e){return e[0]}));n=Object(i["f"])(r)}else n=[];e.addStatus(n),e.setIsCanBind(n);var a=new Map;e.treeToMap(n,a),a.forEach((function(t){t.supervision_channel_bind&&t.parent&&e.findAndSet(t,a)})),e.bindOrgTableData=Object(i["f"])(n),e.bindLoading=!1}else e.$message.error(t.msg)}))):e.$message.error("查询条件不能为空")}))},findAndSet:function(e,t){var n=t.get(e.parent);n.isCanBind=!1,t.set(e.parent,f({},n)),n.parent&&this.findAndSet(n,t)},treeToMap:function(e,t){var n=this;e.forEach((function(e){t.set(e.id,e),e.children_list&&e.children_list.length>0&&n.treeToMap(e.children_list,t)}))},setIsCanBind:function(e){var t=this;e.forEach((function(e){e.supervision_channel_bind?(e.isCanBind=!1,e.children_list&&e.children_list.length&&t.setFalseStatus(e.children_list)):(e.isCanBind=!0,e.children_list&&e.children_list.length&&t.setIsCanBind(e.children_list))}))},setFalseStatus:function(e){var t=this;e.forEach((function(e){e.isCanBind=!1,e.supervision_channel_bind=!1,e.children_list&&e.children_list.length&&t.setFalseStatus(e.children_list)}))},addStatus:function(e){var t=this;e.length&&e.forEach((function(e){Object.assign(e,{isCanBind:!0}),e.has_children&&e.children_list.length&&t.addStatus(e.children_list)}))},load:function(e,t,n){var r=this,a={supervision_channel_id:this.organizationData.id,parent__in:e.tree_id};this.$apis.apiBackgroundFundSupervisionSupervisionChannelBingOrgListPost(a).then((function(e){0===e.code?n(e.data.results||[]):r.$message.error(e.msg)}))},setPermission:function(e){var t=this;this.selectRowData=Object(i["f"])(e),this.dataPermissionDrawerShow=!0,this.$nextTick((function(){t.dataPermissionLoading=!0})),this.$apis.apiBackgroundFundSupervisionSupervisionChannelGetOrgPermissionPost({org_id:e.org_id}).then((function(e){t.dataPermissionLoading=!1,0===e.code?(t.addIsOpen(e.data),t.orgTree=Object(i["f"])(e.data),t.dataPermissionData.list.forEach((function(e){e.orgData=Object(i["f"])(t.orgTree),t.changeIsOpenStatus(e.orgData,e.dataType)}))):t.$message.error(e.msg)}))},addIsOpen:function(e){var t=this;e.length&&e.forEach((function(e){Object.assign(e,{isOpen:"0",selectThisRow:!1}),e.has_children&&e.children_list.length&&t.addIsOpen(e.children_list)}))},changeIsOpenStatus:function(e,t){var n=this;e.length&&e.forEach((function(e){e[t]?e.isOpen="1":e.isOpen="0",e.has_children&&e.children_list.length&&n.changeIsOpenStatus(e.children_list,t)}))},selectThisIsOpen:function(e,t){var n=this;t.length&&t.forEach((function(t){t.id===e.id?t.isOpen="1":(t.isOpen="0",t.selectThisRow=!1),t.has_children&&t.children_list.length&&n.selectThisIsOpen(e,t.children_list)}))},removeHandle:function(e){var t=this;this.$confirm("您正在移除组织：".concat(e.company_name,"。移除后组织数据将不再计入渠道，请谨慎操作。确定要移除该组织？"),"提示",{distinguishCancelAndClose:!0,confirmButtonText:"确定",cancelButtonText:"取消"}).then((function(){var n={id:e.org_id,supervision_channel_bind:!0};t.bindHandle(n,"remove")})).catch((function(e){t.$message("已取消")}))},showPermissionConfiguration:function(){this.permissionConfigurationShow=!0}}},w=y,C=(n("7227"),n("2877")),k=Object(C["a"])(w,r,a,!1,null,"ab01560c",null);t["default"]=k.exports},7227:function(e,t,n){"use strict";n("c0fa")},"7f4b":function(e,t,n){"use strict";n("ab45")},ab45:function(e,t,n){},c0fa:function(e,t,n){},ddcc:function(e,t,n){"use strict";n.r(t),n.d(t,"TABLE_HEAD_DATA",(function(){return r})),n.d(t,"SEARCH_FORM_SET_DATA",(function(){return a})),n.d(t,"DIC_MERCHANT_STATUS",(function(){return i})),n.d(t,"DIC_MERCHANT_TYPE",(function(){return o})),n.d(t,"DIC_MERCHANT_ID_TYPE",(function(){return l})),n.d(t,"DIC_PERSON_MERCHANT_CATEGORY",(function(){return s})),n.d(t,"DIC_MERCHANT_CONTACT_ID",(function(){return c})),n.d(t,"DIC_CERTIFICATE_TYPE",(function(){return u})),n.d(t,"DIC_ACCOUNT_TYPE",(function(){return d})),n.d(t,"DIC_IS_NOT",(function(){return h})),n.d(t,"UPLOAD_DIALOG_DATA_LIST",(function(){return f})),n.d(t,"PRINT_BANK_TABBLE_SETTING",(function(){return p})),n.d(t,"DEFAULT_CHANNEL_TABLE_SETTING",(function(){return m}));var r=[{label:"二级商户编号",key:"sub_mch_id",width:"140",fixed:"left"},{label:"一级商户",key:"php_path",width:"120"},{label:"二级商户名称",key:"sub_mch_name",width:"120"},{label:"二级商户类型",key:"sub_mch_type_alias",width:"120"},{label:"二级商户证件类型",key:"company_cert_type_alias",width:"140"},{label:"二级商户证件有效期结束时间",key:"end_certificate_validity",width:"140"},{label:"法定代表人",key:"contact_name",width:"120"},{label:"法定代表人证件类型",key:"certificate_type_alias",width:"150"},{label:"法定代表人证件编号",key:"certificate_no",width:"150"},{label:"法定代表人证件有效结束时间",key:"fr_cert_end_date",width:"140"},{label:"银行账号",key:"account",width:"120"},{label:"银行账户户名",key:"account_name",width:"120"},{label:"开户银行名称",key:"bank_name",width:"120"},{label:"银行预留手机号",key:"mobile_phone",width:"120"},{label:"账户类型",key:"account_type_alias",width:"120"},{label:"二级商户状态",key:"is_passed_alias",width:"120"},{label:"历史二级商户号",key:"history_sub_mch_ids",type:"slot",slotName:"detail",width:"120"},{label:"签署组织",key:"organization_name",width:"120",type:"slot",slotName:"organizationName"},{label:"签署账号",key:"get_agreement_info",width:"120",type:"slot",slotName:"accountName"},{label:"是否签署协议",key:"is_sign",width:"120",type:"slot",slotName:"isSign"},{label:"操作人",key:"operator",width:"120"},{label:"操作时间",key:"update_time",width:"120"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"200"}],a={sub_mch_id:{type:"input",value:"",label:"二级商户编号",placeholder:"请输入二级商户编号"},sub_mch_name:{type:"input",value:"",label:"二级商户名称",placeholder:"请输入二级商户名称"},is_passed:{type:"select",label:"商户状态",value:[],placeholder:"请选择商户状态",listNameKey:"name",listValueKey:"value",dataList:[]},php_path:{type:"input",value:"",label:"一级商户",placeholder:"请输入一级商户"}},i=[{name:"全部",value:"",label:""},{name:"已验证,未审核(不可交易)",value:"0",label:"VERIFIED_NOT_REVIEWED_0"},{name:"已验证,审核通过",value:"1",label:"VERIFIED_REVIEWED"},{name:"已验证,未审核(暂可交易)",value:"2",label:"VERIFIED_NOT_REVIEWED_2"},{name:"未验证,未审核",value:"3",label:"NOT_VERIFIED_NOT_REVIEWED"},{name:"已解约",value:"4",label:"TERMINATED"},{name:"已关闭",value:"5",label:"CLOSED"},{name:"审核拒绝",value:"8",label:"AUDIT_REJECTED"},{name:"驳回",value:"9",label:"TURN_DOWN"}],o=[{name:"个人商户",value:"1",label:"INDIVIDUAL"},{name:"企业",value:"2",label:"ENTERPRISE"},{name:"个体工商户",value:"3",label:"INDIVIDUAL_BUSINESS"},{name:"政府、金融机构及事业单位",value:"4",label:"BUSINESS_UNIT"}],l=[{name:"个体工商户营业执照",value:"610049",label:"INDIVIDUAL_LICENSE"},{name:"企业营业执照",value:"610047",label:"ENTERPRISE_LICENSE"},{name:"组织机构代码",value:"610001",label:"ORGANIZATION_CODE"},{name:"统一社会信用代码",value:"611009",label:"CREDIT_CODE"},{name:"事业单位法人证书",value:"610170",label:"BUSINESS_UNIT_CERTIFICATE"},{name:"社会团体登记证书",value:"610023",label:"SOCIAL_GROUPS_CERTIFICATE"},{name:"民办非企业登记证书",value:"610025",label:"PRIVATE_CERTIFICATE"},{name:"农民专业合作社营业执照",value:"610079",label:"FARMER_LICENSE"},{name:"主管部门颁居民委员会批文",value:"610033",label:"COMMITTEE_APPROVAL"},{name:"政府主管部门批文",value:"610037",label:"GOVERNMENT_APPROVAL"},{name:"财政部门证明",value:"610039",label:"FINANCIAL_PROVE"},{name:"其他机构证件标识",value:"619999",label:"OTHER"}],s=[{name:"有固定经营场所的实体商户",value:"0",label:"FIXED"},{name:"无固定经营场所的实体商户",value:"1",label:"NOT_FIXED"},{name:"网络商户",value:"2",label:"NET"}],c=[{name:"商户信息核实联系人",value:"01",label:"VERIFY_CONTACT"},{name:"商户巡检联系人",value:"02",label:"INSPECTION_CONTACT"},{name:"客户投诉处理联系人",value:"03",label:"COMPLAINT_HANDLING_CONTACT"}],u=[{name:"身份证",value:"110001",label:"ID_CARD"},{name:"临时居民身份证",value:"110003",label:"TEMPORARY_ID_CARD"},{name:"中国人民解放军军人身份证件",value:"110007",label:"MILITARY_ID"},{name:"中国人民武装警察身份证件",value:"110009",label:"POLICE_ID"},{name:"港澳居民来往内地通行证",value:"110019",label:"HONG_KONG_AND_MACAU_PASS"},{name:"台湾居民来往大陆通行证",value:"110021",label:"TAIWAN_PASS"},{name:"中华人民共和国护照",value:"110023",label:"CHINESE_PASSPORT"},{name:"外国护照",value:"110025",label:"FOREIGN_PASSPORT"},{name:"其他证件",value:"119999",label:"OTHER"}],d=[{name:"借记卡",value:"401",label:"DEBIT_CARD"},{name:"企业户",value:"601",label:"ENTERPRISE_HOUSEHOLD"},{name:"二类户",value:"701",label:"CLASS_II_HOUSEHOLDS"},{name:"三类户",value:"702",label:"CLASS_III_HOUSEHOLDS"}],h=[{name:"是",value:!0},{name:"否",value:!1}],f=[{name:"法人身份证人像面照片",required:!0,fileName:"",fileKey:"id_card_face_url",fileUrl:""},{name:"法人身份证国徽面照片",required:!0,fileName:"",fileKey:"id_card_national_emblem_url",fileUrl:""},{name:"法人护照、通行证照片",required:!0,fileName:"",fileKey:"passport_url",fileUrl:""},{name:"个体工商户/企业营业执照照片",required:!0,fileName:"",fileKey:"license_url",fileUrl:""},{name:"辅助证明材料",required:!0,fileName:"",fileKey:"auxiliary_proof_url",fileUrl:""},{name:"政府机关/事业单位/社会组织登记证书照片",required:!0,fileName:"",fileKey:"certificate_url",fileUrl:""},{name:"法定代表人授权函",required:!1,fileName:"",fileKey:"authorization_letter_url",fileUrl:""},{name:"定位证明材料",required:!1,fileName:"",fileKey:"gps_prove_url",fileUrl:""},{name:"固定经营场所证明材料",required:!1,fileName:"",fileKey:"fixed_place_prove_url",fileUrl:""},{name:"合法合规用途证明材料：",required:!1,fileName:"",fileKey:"use_prove_url",fileUrl:""}],p=[{key:"ori_sub_mer_no",label:"*原二级商户号"},{key:"sub_merchant_short_name",label:"*二级商户名称"},{key:"sub_mch_type",label:"*二级商户类型"},{key:"sub_mch_name",label:"*二级商户经营名称"},{key:"service_phone",label:"*二级商户客服电话"},{key:"industry",label:"*二级商户所属行业"},{key:"business_range",label:"二级商户经营范围"},{key:"address",label:"*二级商户实际经营地址"},{key:"company_cert_type",label:"二级商户证件类型"},{key:"company_cert_no",label:"二级商户证件编号"},{key:"end_certificate_validity",label:"二级商户证件有效期"},{key:"sub_mer_class",label:"个人商户类别"},{key:"account",label:"*银行账号"},{key:"account_name",label:"*银行账户户名"},{key:"bank_name",label:"*开户银行名称"},{key:"mobile_phone",label:"*银行预留手机号"},{key:"account_type",label:"*账户类型"},{key:"apply_service",label:"*申请服务"},{key:"sub_mer_contact_name",label:"*联系人姓名"},{key:"mer_mobile_phone",label:"*联系人手机号码"},{key:"sub_mer_contact_cert",label:"*联系人证件号码"},{key:"sub_mer_contact_mail",label:"联系人邮箱"},{key:"sub_mer_contact_type",label:"*商户联系人业务标识"},{key:"contact_name",label:"*法定代表人姓名"},{key:"certificate_type",label:"*法定代表人证件类型"},{key:"certificate_no",label:"*法定代表人证件编号"},{key:"certificate_beg_date",label:"*法定代表人证件有效期开始时间"},{key:"fr_cert_end_date",label:"*法定代表人证件有效期结束时间"},{key:"fr_residence",label:"*法定代表人证件居住地址"},{key:"fr_is_controller",label:"法定代表人是否为受益所有人"},{key:"fr_is_agent",label:"法定代表人是否为实际办理业务人员"},{key:"controller_name",label:"受益所有人姓名"},{key:"controller_cert_type",label:"受益所有人证件类型"},{key:"controller_cert_no",label:"受益所有人证件号码"},{key:"controller_cert_beg_date",label:"受益所有人证件有效期开始时间"},{key:"controller_cert_end_date",label:"受益所有人证件有效期结束时间"},{key:"controller_residence",label:"受益所有人证件居住地址"},{key:"agent_name",label:"授权办理业务人员姓名"},{key:"agent_cert_type",label:"授权办理业务人员证件类型"},{key:"agent_cert_no",label:"授权办理业务人员证件号码"},{key:"agent_cert_beg_date",label:"授权办理业务人员证件有效期开始时间"},{key:"agent_cert_end_date",label:"授权办理业务人员证件有效期结束时间"},{key:"agent_residence",label:"授权办理业务人员证件居住地址"}],m=[{label:"所属项目",key:"name",align:"left"},{label:"所属地址",key:"district_alias",type:"slot",slotName:"districtAlias"},{label:"监管渠道",key:"supervision_channel_name"},{label:"操作",key:"operation",type:"slot",slotName:"operation",width:"200"}]}}]);