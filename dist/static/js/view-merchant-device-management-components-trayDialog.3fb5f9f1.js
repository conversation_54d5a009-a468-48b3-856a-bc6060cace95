(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-device-management-components-trayDialog"],{"7bf0":function(t,r,e){"use strict";e("d432")},d432:function(t,r,e){},e35e:function(t,r,e){"use strict";e.r(r);var n=function(){var t=this,r=t._self._c;return r("dialog-message",{attrs:{show:t.visible,title:t.title,showFooter:t.showFooter,loading:t.isLoading,customClass:"ps-dialog",width:t.width,top:"200px"},on:{"update:show":function(r){t.visible=r},"update:loading":function(r){t.isLoading=r},close:t.handleClose}},[r("el-form",{ref:"trayForm",staticClass:"dialog-form",attrs:{model:t.trayForm,"status-icon":"",rules:t.trayFormRules,"label-width":"120px"},nativeOn:{submit:function(t){t.preventDefault()}}},["edit"===t.type||"add"===t.type?r("div",[r("el-form-item",{attrs:{label:"所属消费点：",prop:"consume"}},[r("consume-select",{staticClass:"ps-input w-250",attrs:{placeholder:"请选择",multiple:!0,"collapse-tags":!0},model:{value:t.trayForm.consume,callback:function(r){t.$set(t.trayForm,"consume",r)},expression:"trayForm.consume"}})],1),r("el-form-item",{attrs:{label:"编号：",prop:"trayNo"}},[r("el-input",{staticClass:"ps-input w-250",attrs:{disabled:"edit"===t.type,placeholder:"请输入编号"},model:{value:t.trayForm.trayNo,callback:function(r){t.$set(t.trayForm,"trayNo",r)},expression:"trayForm.trayNo"}})],1),r("el-form-item",{attrs:{label:"RFID：",prop:"rfid"}},[r("el-input",{staticClass:"ps-input w-250",attrs:{placeholder:"请输入RFID编号(不填则自动生成)"},model:{value:t.trayForm.rfid,callback:function(r){t.$set(t.trayForm,"rfid",r)},expression:"trayForm.rfid"}})],1),r("el-form-item",{attrs:{label:"备注："}},[r("el-input",{staticClass:"ps-input w-250",attrs:{type:"textarea",rows:3,placeholder:"请输入备注"},model:{value:t.trayForm.remark,callback:function(r){t.$set(t.trayForm,"remark",r)},expression:"trayForm.remark"}})],1)],1):t._e(),"qrcode"===t.type?r("div",{staticClass:"code-box"},[r("qrcode",{attrs:{value:t.trayForm.rfid,options:{width:250},margin:10,alt:""}})],1):t._e(),"barcode"===t.type?r("div",[r("barcode",{staticClass:"code-box",attrs:{value:t.trayForm.rfid,displayValue:!1,width:1.2,height:100,alt:""}})],1):t._e()]),r("template",{slot:"tool"},[t.showFooter?r("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[r("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:t.isLoading},on:{click:t.clickCancleHandle}},[t._v(" 取消 ")]),r("el-button",{staticClass:"ps-btn",attrs:{disabled:t.isLoading,type:"primary"},on:{click:t.clickConfirmHandle}},[t._v(" 确定 ")])],1):t._e()])],2)},o=[],i=e("7c9c"),a=e("b2e5"),s=e.n(a),c=e("f579"),l=e.n(c);function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function f(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */f=function(){return r};var t,r={},e=Object.prototype,n=e.hasOwnProperty,o=Object.defineProperty||function(t,r,e){t[r]=e.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function l(t,r,e){return Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}),t[r]}try{l({},"")}catch(t){l=function(t,r,e){return t[r]=e}}function h(t,r,e,n){var i=r&&r.prototype instanceof b?r:b,a=Object.create(i.prototype),s=new j(n||[]);return o(a,"_invoke",{value:I(t,e,s)}),a}function d(t,r,e){try{return{type:"normal",arg:t.call(r,e)}}catch(t){return{type:"throw",arg:t}}}r.wrap=h;var p="suspendedStart",y="suspendedYield",m="executing",v="completed",g={};function b(){}function w(){}function F(){}var x={};l(x,a,(function(){return this}));var L=Object.getPrototypeOf,k=L&&L(L($([])));k&&k!==e&&n.call(k,a)&&(x=k);var _=F.prototype=b.prototype=Object.create(x);function E(t){["next","throw","return"].forEach((function(r){l(t,r,(function(t){return this._invoke(r,t)}))}))}function C(t,r){function e(o,i,a,s){var c=d(t[o],t,i);if("throw"!==c.type){var l=c.arg,f=l.value;return f&&"object"==u(f)&&n.call(f,"__await")?r.resolve(f.__await).then((function(t){e("next",t,a,s)}),(function(t){e("throw",t,a,s)})):r.resolve(f).then((function(t){l.value=t,a(l)}),(function(t){return e("throw",t,a,s)}))}s(c.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new r((function(r,o){e(t,n,r,o)}))}return i=i?i.then(o,o):o()}})}function I(r,e,n){var o=p;return function(i,a){if(o===m)throw Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var s=n.delegate;if(s){var c=N(s,n);if(c){if(c===g)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=m;var l=d(r,e,n);if("normal"===l.type){if(o=n.done?v:y,l.arg===g)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(o=v,n.method="throw",n.arg=l.arg)}}}function N(r,e){var n=e.method,o=r.iterator[n];if(o===t)return e.delegate=null,"throw"===n&&r.iterator.return&&(e.method="return",e.arg=t,N(r,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var i=d(o,r.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,g;var a=i.arg;return a?a.done?(e[r.resultName]=a.value,e.next=r.nextLoc,"return"!==e.method&&(e.method="next",e.arg=t),e.delegate=null,g):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,g)}function O(t){var r={tryLoc:t[0]};1 in t&&(r.catchLoc=t[1]),2 in t&&(r.finallyLoc=t[2],r.afterLoc=t[3]),this.tryEntries.push(r)}function S(t){var r=t.completion||{};r.type="normal",delete r.arg,t.completion=r}function j(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(O,this),this.reset(!0)}function $(r){if(r||""===r){var e=r[a];if(e)return e.call(r);if("function"==typeof r.next)return r;if(!isNaN(r.length)){var o=-1,i=function e(){for(;++o<r.length;)if(n.call(r,o))return e.value=r[o],e.done=!1,e;return e.value=t,e.done=!0,e};return i.next=i}}throw new TypeError(u(r)+" is not iterable")}return w.prototype=F,o(_,"constructor",{value:F,configurable:!0}),o(F,"constructor",{value:w,configurable:!0}),w.displayName=l(F,c,"GeneratorFunction"),r.isGeneratorFunction=function(t){var r="function"==typeof t&&t.constructor;return!!r&&(r===w||"GeneratorFunction"===(r.displayName||r.name))},r.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,F):(t.__proto__=F,l(t,c,"GeneratorFunction")),t.prototype=Object.create(_),t},r.awrap=function(t){return{__await:t}},E(C.prototype),l(C.prototype,s,(function(){return this})),r.AsyncIterator=C,r.async=function(t,e,n,o,i){void 0===i&&(i=Promise);var a=new C(h(t,e,n,o),i);return r.isGeneratorFunction(e)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},E(_),l(_,c,"Generator"),l(_,a,(function(){return this})),l(_,"toString",(function(){return"[object Generator]"})),r.keys=function(t){var r=Object(t),e=[];for(var n in r)e.push(n);return e.reverse(),function t(){for(;e.length;){var n=e.pop();if(n in r)return t.value=n,t.done=!1,t}return t.done=!0,t}},r.values=$,j.prototype={constructor:j,reset:function(r){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(S),!r)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(r){if(this.done)throw r;var e=this;function o(n,o){return s.type="throw",s.arg=r,e.next=n,o&&(e.method="next",e.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),l=n.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,r){for(var e=this.tryEntries.length-1;e>=0;--e){var o=this.tryEntries[e];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=r&&r<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=r,i?(this.method="next",this.next=i.finallyLoc,g):this.complete(a)},complete:function(t,r){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&r&&(this.next=r),g},finish:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.finallyLoc===t)return this.complete(e.completion,e.afterLoc),S(e),g}},catch:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.tryLoc===t){var n=e.completion;if("throw"===n.type){var o=n.arg;S(e)}return o}}throw Error("illegal catch attempt")},delegateYield:function(r,e,n){return this.delegate={iterator:$(r),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=t),g}},r}function h(t,r,e,n,o,i,a){try{var s=t[i](a),c=s.value}catch(t){return void e(t)}s.done?r(c):Promise.resolve(c).then(n,o)}function d(t){return function(){var r=this,e=arguments;return new Promise((function(n,o){var i=t.apply(r,e);function a(t){h(i,n,o,a,s,"next",t)}function s(t){h(i,n,o,a,s,"throw",t)}a(void 0)}))}}var p={name:"trayDialog",components:{ConsumeSelect:i["a"],qrcode:s.a,barcode:l.a},props:{loading:Boolean,type:{type:String,default:"add"},title:{type:String,default:"新增托盘"},width:{type:String,default:"500px"},showFooter:{type:Boolean,default:!0},isshow:Boolean,trayInfo:{type:Object,default:function(){return{}}},confirm:Function},data:function(){var t=function(t,r,e){var n=/^[0-9a-zA-Z]*$/g;r&&!n.test(r)?e(new Error("请输入英文或数字")):e()};return{isLoading:!1,trayForm:{trayNo:"",rfid:"",remark:"",consume:[]},trayFormRules:{rfid:[{validator:t,trigger:"blur"}],trayNo:[{required:!0,message:"请输入编号",trigger:"blur"}],consume:[{required:!0,message:"请选择消费点",trigger:"change"}]}}},computed:{visible:{get:function(){return this.isshow},set:function(t){this.$emit("update:isshow",t)}}},watch:{visible:function(){this.visible&&"edit"===this.type?(this.trayForm.consume=this.trayInfo.org_id,this.trayForm.rfid=this.trayInfo.RFID,this.trayForm.trayNo=this.trayInfo.tray_no,this.trayForm.remark=this.trayInfo.remark):"add"===this.type?(this.trayForm.consume=[],this.trayForm.rfid="",this.trayForm.trayNo="",this.trayForm.remark=""):"qrcode"===this.type?this.trayForm.rfid=this.trayInfo.qr_code:"barcode"===this.type&&(this.trayForm.rfid=this.trayInfo.RFID)}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){},clickConfirmHandle:function(){var t,r=this,e={org_ids:this.trayForm.consume,tray_no:this.trayForm.trayNo,rfid:this.trayForm.rfid};this.trayForm.remark&&(e.remark=this.trayForm.remark),this.$refs.trayForm.validate((function(n){n&&(t="add"===r.type?r.$apis.apiBackgroundDeviceTrayTrayAddPost(e):r.$apis.apiBackgroundDeviceTrayTrayModifyPost(e),r.addEditTray(t))}))},addEditTray:function(t){var r=this;return d(f().mark((function e(){var n;return f().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!r.isLoading){e.next=2;break}return e.abrupt("return");case 2:return r.isLoading=!0,e.next=5,t;case 5:n=e.sent,r.isLoading=!1,0===n.code?(r.$message.success("成功"),r.$emit("confirm","search")):r.$message.error(n.msg);case 8:case"end":return e.stop()}}),e)})))()},clickCancleHandle:function(){this.visible=!1},handleClose:function(t){this.isLoading=!1,this.visible=!1,this.$refs.trayForm.resetFields()},normalizer:function(t){return{id:t.id,label:t.name,children:t.children_list}},findIdData:function(t,r){var e=this;t.forEach((function(t){t.id===r?(e.departmentIds.push(t.id),e.findIdData(e.departmentList,t.parent)):t.children_list&&e.findIdData(t.children_list,r)}))}}},y=p,m=(e("7bf0"),e("2877")),v=Object(m["a"])(y,n,o,!1,null,"2333a8bb",null);r["default"]=v.exports}}]);