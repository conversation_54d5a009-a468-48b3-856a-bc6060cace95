(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-user-center-components-userDialog"],{6052:function(e,t,r){"use strict";r("ab3e")},"6c35":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("dialog-message",{attrs:{show:e.visible,title:e.title,loading:e.isLoading,"user-info":e.userInfo,customClass:"ps-dialog",width:"750px"},on:{"update:show":function(t){e.visible=t},"update:loading":function(t){e.isLoading=t},close:e.handleClose}},[t("el-form",{ref:"userForm",staticClass:"dialog-form",attrs:{model:e.userForm,"status-icon":"",inline:"",rules:e.userFormRules,"label-width":"120px"},nativeOn:{submit:function(e){e.preventDefault()}}},[t("el-form-item",{attrs:{label:"姓名：",prop:"name"}},[t("el-input",{staticClass:"ps-input w-180",attrs:{placeholder:"请输入"},model:{value:e.userForm.name,callback:function(t){e.$set(e.userForm,"name",t)},expression:"userForm.name"}})],1),t("el-form-item",{attrs:{label:"人员编号：",prop:"personNo"}},[t("el-input",{staticClass:"ps-input w-180",attrs:{placeholder:"请输入",disabled:"add"!=e.type},model:{value:e.userForm.personNo,callback:function(t){e.$set(e.userForm,"personNo",t)},expression:"userForm.personNo"}})],1),t("el-form-item",{attrs:{label:"性别：",prop:"gender"}},[t("el-select",{staticClass:"ps-select w-180",attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select"},model:{value:e.userForm.gender,callback:function(t){e.$set(e.userForm,"gender",t)},expression:"userForm.gender"}},e._l(e.genderList,(function(e){return t("el-option",{key:e.value,attrs:{label:e.gender,value:e.value}})})),1)],1),t("el-form-item",{attrs:{label:"手机号码：",prop:"phone"}},[t("el-input",{staticClass:"ps-input w-180",attrs:{placeholder:"请输入8-11位数"},model:{value:e.userForm.phone,callback:function(t){e.$set(e.userForm,"phone",t)},expression:"userForm.phone"}})],1),t("el-form-item",{attrs:{label:"卡号：",prop:"cardNumber"}},[t("el-input",{staticClass:"ps-input w-180",attrs:{disabled:e.disabledCardNumber,placeholder:"请输入"},model:{value:e.userForm.cardNumber,callback:function(t){e.$set(e.userForm,"cardNumber",t)},expression:"userForm.cardNumber"}})],1),t("el-form-item",{attrs:{label:"分组：",prop:"group"}},[t("user-group-select",{staticClass:"search-item-w ps-input w-180",attrs:{placeholder:"请下拉选择"},model:{value:e.userForm.group,callback:function(t){e.$set(e.userForm,"group",t)},expression:"userForm.group"}})],1),t("el-form-item",{attrs:{label:"部门：",prop:"department"}},[e.showDepartmentSelect()?t("tree-select",{staticClass:"w-180",attrs:{multiple:!1,options:e.departmentList,limit:1,limitText:function(e){return"+"+e},"default-expand-level":1,normalizer:e.departmentNode,placeholder:"请选择"},model:{value:e.userForm.department,callback:function(t){e.$set(e.userForm,"department",t)},expression:"userForm.department"}}):t("el-input",{staticClass:"ps-input w-180",attrs:{disabled:!0},model:{value:e.userForm.department,callback:function(t){e.$set(e.userForm,"department",t)},expression:"userForm.department"}})],1),t("el-form-item",{attrs:{label:"有效期",prop:"add"==e.type?"validityDate":""}},[t("el-date-picker",{staticStyle:{width:"490px"},attrs:{type:"datetimerange",align:"left","unlink-panels":"","range-separator":"至","start-placeholder":"生效时间","end-placeholder":"失效时间","picker-options":e.pickerOptions,"value-format":"add"==e.type?"yyyy-MM-ddTHH:mm:ssZ":"yyyy-MM-ddTHH:mm:ss",format:"yyyy-MM-dd HH:mm:ss",clearable:""},model:{value:e.userForm.validityDate,callback:function(t){e.$set(e.userForm,"validityDate",t)},expression:"userForm.validityDate"}})],1)],1),t("template",{slot:"tool"},[t("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:e.clickCancleHandle}},[e._v(" 取消 ")]),t("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickConfirmHandle}},[e._v(" 确定 ")])],1)])],2)},i=[],o=r("390a");function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,i=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},u=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function d(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(e){d=function(e,t,r){return e[t]=r}}function p(e,t,r,n){var o=t&&t.prototype instanceof b?t:b,a=Object.create(o.prototype),s=new I(n||[]);return i(a,"_invoke",{value:N(e,r,s)}),a}function m(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=p;var f="suspendedStart",h="suspendedYield",g="executing",v="completed",y={};function b(){}function w(){}function _(){}var x={};d(x,u,(function(){return this}));var F=Object.getPrototypeOf,L=F&&F(F(T([])));L&&L!==r&&n.call(L,u)&&(x=L);var k=_.prototype=b.prototype=Object.create(x);function C(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function $(e,t){function r(i,o,s,u){var c=m(e[i],e,o);if("throw"!==c.type){var l=c.arg,d=l.value;return d&&"object"==a(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,s,u)}),(function(e){r("throw",e,s,u)})):t.resolve(d).then((function(e){l.value=e,s(l)}),(function(e){return r("throw",e,s,u)}))}u(c.arg)}var o;i(this,"_invoke",{value:function(e,n){function i(){return new t((function(t,i){r(e,n,t,i)}))}return o=o?o.then(i,i):i()}})}function N(t,r,n){var i=f;return function(o,a){if(i===g)throw Error("Generator is already running");if(i===v){if("throw"===o)throw a;return{value:e,done:!0}}for(n.method=o,n.arg=a;;){var s=n.delegate;if(s){var u=D(s,n);if(u){if(u===y)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===f)throw i=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=g;var c=m(t,r,n);if("normal"===c.type){if(i=n.done?v:h,c.arg===y)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(i=v,n.method="throw",n.arg=c.arg)}}}function D(t,r){var n=r.method,i=t.iterator[n];if(i===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,D(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var o=m(i,t.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,y;var a=o.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,y):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function S(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function I(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function T(t){if(t||""===t){var r=t[u];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,o=function r(){for(;++i<t.length;)if(n.call(t,i))return r.value=t[i],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(a(t)+" is not iterable")}return w.prototype=_,i(k,"constructor",{value:_,configurable:!0}),i(_,"constructor",{value:w,configurable:!0}),w.displayName=d(_,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,d(e,l,"GeneratorFunction")),e.prototype=Object.create(k),e},t.awrap=function(e){return{__await:e}},C($.prototype),d($.prototype,c,(function(){return this})),t.AsyncIterator=$,t.async=function(e,r,n,i,o){void 0===o&&(o=Promise);var a=new $(p(e,r,n,i),o);return t.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},C(k),d(k,l,"Generator"),d(k,u,(function(){return this})),d(k,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=T,I.prototype={constructor:I,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(S),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function i(n,i){return s.type="throw",s.arg=t,r.next=n,i&&(r.method="next",r.arg=e),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var u=n.call(a,"catchLoc"),c=n.call(a,"finallyLoc");if(u&&c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,y):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),S(r),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;S(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:T(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),y}},t}function u(e,t,r,n,i,o,a){try{var s=e[o](a),u=s.value}catch(e){return void r(e)}s.done?t(u):Promise.resolve(u).then(n,i)}function c(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var o=e.apply(t,r);function a(e){u(o,n,i,a,s,"next",e)}function s(e){u(o,n,i,a,s,"throw",e)}a(void 0)}))}}var l={name:"userDialog",components:{UserGroupSelect:o["a"]},props:{loading:Boolean,type:{type:String,default:"add"},title:{type:String,default:"新增用户"},isshow:Boolean,userInfo:{type:Object,default:function(){return{}}},groupList:{type:Array,default:function(){return[]}},confirm:Function},data:function(){var e=this,t=function(t,r,n){"add"===e.type&&null!=r&&r.length&&new Date(r[0]).getTime()<(new Date).getTime()?n(new Error("生效时间不能早于当前时间")):"edit"===e.type&&null!=r&&r.length&&new Date(e.userForm.create_time).getTime()>new Date(r[0]).getTime()?n(new Error("生效时间不能早于创建时间")):n()},r=function(e,t,r){t&&!/^[a-zA-Z0-9_]+$/i.test(t)?r(new Error("请输入正确的卡号")):r()},n=function(e,t,r){if(t){var n=/^\d{8,11}$/;n.test(t)?r():r(new Error("请输入正确手机号"))}else r()};return{isLoading:!1,userForm:{id:"",name:"",gender:"",group:"",department:null,cardNumber:"",cardPwd:"",phone:"",personNo:"",validityDate:[],create_time:""},userFormRules:{name:[{required:!0,message:"请输入姓名",trigger:"blur"}],personNo:[{required:!0,message:"请输入人员编号",trigger:"blur"}],cardNumber:[{validator:r,trigger:"blur"}],phone:[{validator:n,trigger:"blur"}],gender:[{required:!0,message:"请选择性别",trigger:"blur"}],validityDate:[{validator:t,trigger:"change"}]},props:{value:"id",label:"group_name",children:"children_list",checkStrictly:!0},genderList:[{gender:"男",value:"MAN"},{gender:"女",value:"WOMEN"},{gender:"其他",value:"OTHER"}],pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(e){var t=new Date,r=new Date;r.setTime(r.getTime()-6048e5),e.$emit("pick",[r,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,r=new Date;r.setTime(r.getTime()-2592e6),e.$emit("pick",[r,t])}},{text:"最近三个月",onClick:function(e){var t=new Date,r=new Date;r.setTime(r.getTime()-7776e6),e.$emit("pick",[r,t])}}]},departmentIds:[],departmentList:[],isCurrent:!1}},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}},disabledCardNumber:function(){var e=!1;return this.userInfo&&(e="LOSS"===this.userInfo.card_status||"UNUSED"===this.userInfo.card_status||"QUIT"===this.userInfo.card_status),e}},watch:{visible:function(){if(this.visible&&"edit"===this.type){this.userForm.id=this.userInfo.id,this.userForm.name=this.userInfo.name,this.userForm.gender=this.userInfo.gender;var e=this.userInfo.card_user_group_objs.filter((function(e){return e.organization===Number(sessionStorage.getItem("organization"))}));this.userForm.group=e.length?e[0].id:"",this.userForm.department=this.userInfo.card_department_group,this.userForm.cardNumber=this.userInfo.card_no,this.userForm.phone=this.userInfo.phone,this.userForm.personNo=this.userInfo.person_no,this.userForm.validityDate=this.userInfo.effective_time&&this.userInfo.expiration_time?[this.userInfo.effective_time,this.userInfo.expiration_time]:[],this.userForm.create_time=this.userInfo.create_time}else this.userForm={name:"",gender:"",group:[],department:null,cardNumber:"",cardPwd:"",phone:"",personNo:"",validityDate:[]}}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getDepartmentList()},clickConfirmHandle:function(){var e=this,t={person_name:this.userForm.name,person_no:this.userForm.personNo,gender:this.userForm.gender};this.userForm.phone&&(t.phone=this.userForm.phone),this.userForm.cardNumber&&(t.card_no=this.userForm.cardNumber),this.userForm.group&&(t.card_user_group_ids=[Number(this.userForm.group)]),this.userForm.department&&(t.card_department_group_id=Number(this.userForm.department)),this.userForm.validityDate&&(t.effective_time=this.userForm.validityDate[0],t.expiration_time=this.userForm.validityDate[1]),this.$refs.userForm.validate((function(r){r&&("add"===e.type?e.addCardUser(t):(t.card_user_id=e.userForm.id,e.editCardUser(t)))}))},addCardUser:function(e){var t=this;return c(s().mark((function r(){var n;return s().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!t.isLoading){r.next=2;break}return r.abrupt("return");case 2:return t.isLoading=!0,r.next=5,t.$apis.apiCardServiceCardUserAddPost(e);case 5:if(n=r.sent,t.isLoading=!1,100016!==n.code){r.next=11;break}return e.is_sync=!0,t.$confirm("该用户已存在，是否同步用户信息?","提示",{confirmButtonText:"同步",cancelButtonText:"不同步",closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var r=c(s().mark((function r(n,i,o){return s().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if("confirm"!==n){r.next=9;break}return i.confirmButtonLoading=!0,t.visible=!1,r.next=5,t.addCardUser(e);case 5:i.confirmButtonLoading=!1,o(),r.next=10;break;case 9:i.confirmButtonLoading||(o(),t.visible=!1,t.$emit("confirm","search"));case 10:case"end":return r.stop()}}),r)})));function n(e,t,n){return r.apply(this,arguments)}return n}()}).then((function(e){})).catch((function(e){})),r.abrupt("return");case 11:if(100020!==n.code){r.next=15;break}return e.is_sync=!0,t.$confirm("存在相同用户（已退户），是否取消退户？","提示",{confirmButtonText:"取消退户",cancelButtonText:"否",closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var e=c(s().mark((function e(r,i,o){return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:"confirm"===r?(i.confirmButtonLoading=!0,t.visible=!1,t.cancelQuit(n.data.card_user_id),i.confirmButtonLoading=!1,o()):i.confirmButtonLoading||(o(),t.visible=!1,t.$emit("confirm","search"));case 1:case"end":return e.stop()}}),e)})));function r(t,r,n){return e.apply(this,arguments)}return r}()}).then((function(e){})).catch((function(e){})),r.abrupt("return");case 15:if(100017!==n.code){r.next=18;break}return t.visible=!1,r.abrupt("return",t.$emit("showServiceDialog",n.msg));case 18:if(100018!==n.code){r.next=22;break}return t.isLoading=!1,t.visible=!1,r.abrupt("return",t.$emit("showServiceDialog",n.msg));case 22:0===n.code?(t.$message.success(n.msg),t.$emit("confirm","search")):(t.isLoading=!1,t.$message.error(n.msg));case 23:case"end":return r.stop()}}),r)})))()},editCardUser:function(e){var t=this;return c(s().mark((function r(){var n;return s().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!t.isLoading){r.next=2;break}return r.abrupt("return");case 2:return t.isLoading=!0,r.next=5,t.$apis.apiCardServiceCardUserModifyPost(e);case 5:n=r.sent,t.isLoading=!1,0===n.code?(t.$message.success(n.msg),t.$emit("confirm","search")):t.$message.error(n.msg);case 8:case"end":return r.stop()}}),r)})))()},cancelQuit:function(e){var t=this;return c(s().mark((function r(){var n;return s().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!t.isLoading){r.next=2;break}return r.abrupt("return");case 2:return t.isLoading=!0,r.next=5,t.$apis.apiCardServiceCardOperateCancelPersonQuitPost({card_user_ids:[e]});case 5:n=r.sent,t.isLoading=!1,0===n.code?(t.$message.success(n.msg),t.$emit("confirm","search")):t.$message.error(n.msg);case 8:case"end":return r.stop()}}),r)})))()},clickCancleHandle:function(){this.visible=!1},handleClose:function(e){this.$refs.userForm.resetFields(),this.isLoading=!1,this.visible=!1},normalizer:function(e){return{id:e.id,label:e.group_name,children:e.children_list}},getDepartmentList:function(){var e=this;return c(s().mark((function t(){var r;return s().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,e.$apis.apiCardServiceCardDepartmentGroupTreeListPost();case 3:r=t.sent,e.isLoading=!1,0===r.code?e.departmentList=e.deleteEmptyGroup(r.data):e.$message.error(r.msg);case 6:case"end":return t.stop()}}),t)})))()},deleteEmptyGroup:function(e){var t=this;function r(e){e.map((function(e){e.children_list&&e.children_list.length>0?r(e.children_list):t.$delete(e,"children_list")}))}return r(e),e},departmentNode:function(e){return{id:e.id,label:e.group_name,children:e.children_list}},isCurrentDepartment:function(e,t){var r=this;e.forEach((function(e){e.id===r.userInfo.card_department_group?r.isCurrent=!0:e.children_list&&r.isCurrentDepartment(e.children_list,t)}))},showDepartmentSelect:function(){return!this.userInfo.card_department_group||(this.isCurrentDepartment(this.departmentList,this.isCurrent),!!this.isCurrent||(this.userForm.department=this.userInfo.card_department_group_alias,!1))},getValue:function(e){this.userForm.department=e}}},d=l,p=(r("6052"),r("2877")),m=Object(p["a"])(d,n,i,!1,null,"31c4696c",null);t["default"]=m.exports},ab3e:function(e,t,r){}}]);