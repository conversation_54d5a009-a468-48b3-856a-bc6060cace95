(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-supervision-and-management-food-safety-management-compontents-AddWorkforceDialog"],{"3ebe":function(t,e,n){},"52a4":function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t._self._c;return e("el-drawer",{staticClass:"ps-el-drawer",attrs:{visible:t.visible,title:t.title,loading:t.isLoading,size:"1278px",wrapperClosable:!1},on:{"update:visible":function(e){t.visible=e},"update:loading":function(e){t.isLoading=e},close:t.handleClose}},[e("div",{staticClass:"ps-flex"},[e("div",{staticClass:"left"},[e("div",{staticClass:"dialog-content m-t-20 m-l-20"},[e("div",[t._v(" 待选择（"),"confirm"!=t.type?e("span",[t._v(t._s(t.memberOpts.tableData.length))]):t._e(),t._v("）")]),e("div",{staticClass:"table-wrap m-t-10"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"userListRef",attrs:{data:t.memberOpts.tableData,"tooltip-effect":"dark","header-row-class-name":"table-header-row"},on:{"selection-change":t.handleSelectionChange}},[e("el-table-column",{attrs:{"class-name":"ps-checkbox",type:"selection",width:"37",selectable:t.selectableHandle}}),e("el-table-column",{attrs:{prop:"name",label:"姓名",align:"center"}}),e("el-table-column",{attrs:{prop:"job_title",label:"所属岗位",align:"center"}}),e("el-table-column",{attrs:{prop:"phone",label:"联系号码",align:"center"}})],1)],1)])]),e("div",{staticClass:"right"},[e("div",{staticClass:"dialog-content m-t-20 m-l-20"},[e("div",[t._v(" 已选择 （"+t._s(t.selectListId.length)+"）")]),e("div",{staticClass:"table-wrap m-t-10"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"useralreadyListRef",attrs:{data:t.cloneConfirmList,"tooltip-effect":"dark","header-row-class-name":"table-header-row"}},[e("el-table-column",{attrs:{prop:"name",label:"姓名",align:"center"}}),e("el-table-column",{attrs:{prop:"job_title",label:"所属岗位",align:"center"}}),e("el-table-column",{attrs:{prop:"phone",label:"联系号码",align:"center"}}),e("el-table-column",{attrs:{prop:"",label:"操作",align:"center"},scopedSlots:t._u([{key:"default",fn:function(n){return[e("el-button",{staticClass:"delete-btn",attrs:{type:"text"},on:{click:function(e){return t.handlerDelete(n.row,n.$index)}}},[t._v("删除")])]}}])})],1)],1)])])]),e("div",{staticClass:"ps-el-drawer-footer m-l-20"},[e("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:t.isBtnLoading},on:{click:t.clickCancleHandle}},[t._v(" 取消 ")]),e("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.isBtnLoading,expression:"isBtnLoading"}],staticClass:"ps-btn",attrs:{type:"primary",disabled:t.isBtnLoading||!t.selectListId||t.selectListId.length<=0},on:{click:t.clickConfirmHandle}},[t._v(" 确定 ")])],1)])},i=[],o=n("ed08");function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(t,e,n){t[e]=n.value},o="function"==typeof Symbol?Symbol:{},c=o.iterator||"@@iterator",l=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function f(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,n){return t[e]=n}}function h(t,e,n,r){var o=e&&e.prototype instanceof y?e:y,a=Object.create(o.prototype),s=new D(r||[]);return i(a,"_invoke",{value:S(t,n,s)}),a}function d(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var p="suspendedStart",m="suspendedYield",g="executing",b="completed",v={};function y(){}function L(){}function w(){}var O={};f(O,c,(function(){return this}));var x=Object.getPrototypeOf,_=x&&x(x($([])));_&&_!==n&&r.call(_,c)&&(O=_);var C=w.prototype=y.prototype=Object.create(O);function j(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function n(i,o,s,c){var l=d(t[i],t,o);if("throw"!==l.type){var u=l.arg,f=u.value;return f&&"object"==a(f)&&r.call(f,"__await")?e.resolve(f.__await).then((function(t){n("next",t,s,c)}),(function(t){n("throw",t,s,c)})):e.resolve(f).then((function(t){u.value=t,s(u)}),(function(t){return n("throw",t,s,c)}))}c(l.arg)}var o;i(this,"_invoke",{value:function(t,r){function i(){return new e((function(e,i){n(t,r,e,i)}))}return o=o?o.then(i,i):i()}})}function S(e,n,r){var i=p;return function(o,a){if(i===g)throw Error("Generator is already running");if(i===b){if("throw"===o)throw a;return{value:t,done:!0}}for(r.method=o,r.arg=a;;){var s=r.delegate;if(s){var c=E(s,r);if(c){if(c===v)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===p)throw i=b,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=g;var l=d(e,n,r);if("normal"===l.type){if(i=r.done?b:m,l.arg===v)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(i=b,r.method="throw",r.arg=l.arg)}}}function E(e,n){var r=n.method,i=e.iterator[r];if(i===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,E(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var o=d(i,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,v;var a=o.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,v):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function I(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function P(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function D(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(I,this),this.reset(!0)}function $(e){if(e||""===e){var n=e[c];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function n(){for(;++i<e.length;)if(r.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return o.next=o}}throw new TypeError(a(e)+" is not iterable")}return L.prototype=w,i(C,"constructor",{value:w,configurable:!0}),i(w,"constructor",{value:L,configurable:!0}),L.displayName=f(w,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===L||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,f(t,u,"GeneratorFunction")),t.prototype=Object.create(C),t},e.awrap=function(t){return{__await:t}},j(k.prototype),f(k.prototype,l,(function(){return this})),e.AsyncIterator=k,e.async=function(t,n,r,i,o){void 0===o&&(o=Promise);var a=new k(h(t,n,r,i),o);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},j(C),f(C,u,"Generator"),f(C,c,(function(){return this})),f(C,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=$,D.prototype={constructor:D,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(P),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function i(r,i){return s.type="throw",s.arg=e,n.next=r,i&&(n.method="next",n.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),l=r.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),P(n),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;P(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:$(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),v}},e}function c(t,e){return d(t)||h(t,e)||u(t,e)||l()}function l(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(t,e){if(t){if("string"==typeof t)return f(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?f(t,e):void 0}}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function h(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o,a,s=[],c=!0,l=!1;try{if(o=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(s.push(r.value),s.length!==e);c=!0);}catch(t){l=!0,i=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw i}}return s}}function d(t){if(Array.isArray(t))return t}function p(t,e,n,r,i,o,a){try{var s=t[o](a),c=s.value}catch(t){return void n(t)}s.done?e(c):Promise.resolve(c).then(r,i)}function m(t){return function(){var e=this,n=arguments;return new Promise((function(r,i){var o=t.apply(e,n);function a(t){p(o,r,i,a,s,"next",t)}function s(t){p(o,r,i,a,s,"throw",t)}a(void 0)}))}}var g={name:"AddWorkforceDialog",props:{loading:Boolean,isshow:Boolean,title:{type:String,default:"选择值班人员"},limitId:{type:String,default:""},dialogType:{type:String,default:"default"},useDate:{type:String,default:""},confirm:Function},data:function(){return{isLoading:!1,groupOpts:{value:"id",label:"group_name",children:"children_list",checkStrictly:!0},memberOpts:{tableData:[],person_name:"",selectGroup:[],departmentList:[]},selectListId:[],personList:[],pageSize:999999,totalCount:0,currentPage:1,isBtnLoading:!1,type:this.dialogType,cloneTableList:[],cloneConfirmList:[]}},computed:{visible:{get:function(){return this.isshow},set:function(t){this.$emit("update:isshow",t)}}},watch:{visible:function(){this.visible&&this.getCardUserList()}},mounted:function(){},methods:{getCardUserList:function(){var t=this;return m(s().mark((function e(){var n,r,i,a,l,u,f,h;return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,n={page_size:t.pageSize,page:t.currentPage},t.memberOpts.person_name&&(n.person_name=t.memberOpts.person_name),e.next=5,Object(o["Z"])(t.$apis.apiBackgroundFundSupervisionJobPersonJobPersonListPost(n));case 5:if(r=e.sent,i=c(r,2),a=i[0],l=i[1],!a){e.next=12;break}return t.isLoading=!1,e.abrupt("return",t.$message.error("获取人员信息失败"));case 12:l&&0===l.code?(u=l.data||{},f=u.results||[],h=[],t.selectListId&&(f=f.filter((function(e){return!t.selectListId.includes(e.id)}))),h=Object(o["f"])(f),t.isLoading=!1,t.totalCount=u.count||0,t.memberOpts.tableData=h,t.cloneTableList=Object(o["f"])(h),t.setCheckoutId()):t.$message.error(l.msg);case 13:case"end":return e.stop()}}),e)})))()},setCheckoutId:function(){var t=this;this.memberOpts.tableData=this.memberOpts.tableData.map((function(e){return"default"===t.dialogType&&t.personList.forEach((function(n){e.id===n.id&&t.$refs.userListRef&&t.$nextTick((function(){t.$refs.userListRef.toggleRowSelection(e,!0)}))})),e}))},changePersonNo:Object(o["d"])((function(t){"confirm"===this.type?this.selectListId=this.cloneConfirmList.filter((function(e){return-1!==e.name.indexOf(t)})):this.memberOpts.tableData=this.cloneTableList.filter((function(e){return-1!==e.name.indexOf(t)}))}),500),handleSelectionChange:function(t){var e=this,n=Object(o["f"])(t);n.forEach((function(t){var n=e.cloneConfirmList.find((function(e){return e.id===t.id}));n||(e.cloneConfirmList.push(Object(o["f"])(t)),e.selectListId.push(t.id))})),setTimeout((function(){var t=e.memberOpts.tableData.filter((function(t){return!e.selectListId.includes(t.id)}));e.$set(e.memberOpts,"tableData",Object(o["f"])(t))}),100)},clickCancleHandle:function(){this.handleClose()},clickConfirmHandle:function(){var t=this;return m(s().mark((function e(){var n,r,i,a,l;return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.selectListId.length){e.next=2;break}return e.abrupt("return",t.$message.error("请选择用户"));case 2:return t.isBtnLoading=!0,n={job_person_ids:t.selectListId,start_date:t.useDate||"",end_date:t.useDate||"",operate_type:"modify"},e.next=6,Object(o["Z"])(t.$apis.apiBackgroundFundSupervisionCanteenSafetyManagementModifyPersonSchedulePost(n));case 6:if(r=e.sent,i=c(r,2),a=i[0],l=i[1],t.isBtnLoading=!1,!a){e.next=13;break}return e.abrupt("return",t.$message.error("保存失败"));case 13:l&&0===l.code?(t.$message.success("保存成功"),t.$emit("confirm",t.cloneConfirmList,t.useDate)):t.$message.error(l.msg);case 14:case"end":return e.stop()}}),e)})))()},updatePersonList:function(){var t=this;this.memberOpts.tableData.forEach((function(e){t.selectListId.includes(e.id)&&t.personList.push(e)}))},handleClose:function(t){this.isLoading=!1,this.memberOpts={tableData:[],person_name:"",selectGroup:[]},this.visible=!1,this.type="default",this.cloneConfirmList=[],this.$emit("close",!1)},setPersonList:function(t){this.personList=Object(o["f"])(t),this.cloneConfirmList=Object(o["f"])(t),this.selectListId=t.map((function(t){return t.id}))},onPaginationChange:function(t){this.currentPage=t.current,this.pageSize=t.pageSize,this.getCardUserList()},handlerSearch:function(){this.updatePersonList(),this.currentPage=1,this.getCardUserList()},selectableHandle:function(t){return"default"===this.dialogType||t.is_enable},handlerDelete:function(t,e){var n=t.id,r=Object(o["f"])(t),i=Object(o["f"])(this.memberOpts.tableData);i.push(Object(o["f"])(r)),this.$set(this.memberOpts,"tableData",Object(o["f"])(i)),this.selectListId=this.selectListId.filter((function(t){return t!==n})),this.cloneConfirmList.splice(e,1)}}},b=g,v=(n("8e59"),n("2877")),y=Object(v["a"])(b,r,i,!1,null,"0658abea",null);e["default"]=y.exports},"8e59":function(t,e,n){"use strict";n("3ebe")}}]);