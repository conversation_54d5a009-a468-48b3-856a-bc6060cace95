(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-home-page-home","view-merchant-application-ApplicationCenter","view-merchant-user-center-constants-cardManageConstants"],{"035f":function(e,t,l){"use strict";l.r(t),l.d(t,"TABLE_HEAD_DATA_CARD_BINDING",(function(){return a})),l.d(t,"SEARCH_FORM_SET_DATA_CARD_BINDING",(function(){return o})),l.d(t,"TABLE_HEAD_DATA_TRAFFIC_RECORDS",(function(){return r})),l.d(t,"SEARCH_FORM_SET_DATA_TRAFFIC_RECORDS",(function(){return n})),l.d(t,"TABLE_HEAD_DATA_TRAFFIC_ORDER_COST",(function(){return i})),l.d(t,"SEARCH_FORM_SET_DATA_TRAFFIC_ORDER_COST",(function(){return p})),l.d(t,"TABLE_HEAD_DATA_TRAFFIC_ORDER_REFUND",(function(){return u})),l.d(t,"SEARCH_FORM_SET_DATA_TRAFFIC_ORDER_REFUND",(function(){return c})),l.d(t,"TABLE_HEAD_DATA_IMPORT_CAR",(function(){return s})),l.d(t,"URL_MANUFACTURER",(function(){return d})),l.d(t,"URL_MANUFACTURER_STAGING",(function(){return b})),l.d(t,"URL_TEMPLATE_MODEL",(function(){return m})),l.d(t,"DIC_OPERATION_TYPE",(function(){return y})),l.d(t,"DIC_IN_OUT_DIRECTION",(function(){return _})),l.d(t,"DIC_PARK_TYPE",(function(){return f}));var a=[{label:"人员编号",key:"person_no"},{label:"姓名",key:"name"},{label:"手机号",key:"phone"},{label:"车牌号",key:"car_no"},{label:"操作类型",key:"operation_type",type:"slot",slotName:"operationType"},{label:"绑定时间",key:"bind_time"},{label:"操作人",key:"creater"},{label:"操作",key:"operation",type:"slot",slotName:"operation",width:"100"}],o={person_no:{type:"input",value:"",label:"人员编号",placeholder:"请输入"},name:{type:"input",value:"",label:"姓名",placeholder:"请输入"},phone:{type:"input",value:"",label:"手机号",placeholder:"请输入"},car_no:{type:"input",value:"",label:"车牌号",placeholder:"请输入"},operation_type:{type:"select",label:"操作类型",value:[],placeholder:"请选择",listNameKey:"name",listValueKey:"value",dataList:[]}},r=[{label:"通行时间",key:"create_time",width:"200"},{label:"出入方向",key:"in_out_direction_alias"},{label:"道口名称",key:"cross_name"},{label:"停车类型",key:"park_type_alias"},{label:"车牌号",key:"car_no",width:"130"},{label:"人员编号",key:"person_no"},{label:"姓名",key:"name"},{label:"手机号",key:"phone",width:"130"},{label:"分组",key:"card_user_groups_alias",type:"slot",slotName:"userGroups"},{label:"部门",key:"department_group_alias",type:"slot",slotName:"departmentGroups"},{label:"一级组织",key:"primary"},{label:"二级组织",key:"secondary"},{label:"通行图片",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"100"}],n={select_date:{type:"datetimerange",label:"通行时间",value:[],clearable:!0},in_out_direction:{type:"select",label:"出入方向",value:[],placeholder:"请选择",listNameKey:"name",listValueKey:"value",dataList:[]},cross_name:{type:"input",value:"",label:"道口名称",placeholder:"请输入"},park_type:{type:"select",label:"停车类型",value:[],placeholder:"请选择",listNameKey:"name",listValueKey:"value",dataList:[]},person_no:{type:"input",value:"",label:"人员编号",placeholder:"请输入"},name:{type:"input",value:"",label:"姓名",placeholder:"请输入"},phone:{type:"input",value:"",label:"手机号",placeholder:"请输入"},car_no:{type:"input",value:"",label:"车牌号",placeholder:"请输入"},payer_group_ids:{type:"groupSelect",label:"分组",value:[],placeholder:"请选择分组",multiple:!0,collapseTags:!0},payer_department_group_ids:{type:"organizationDepartmentSelect",multiple:!0,isLazy:!1,checkStrictly:!0,label:"部门",value:[],placeholder:"请选择部门"},consumption_name:{type:"consumeSelect",label:"消费点",value:[],multiple:!0,placeholder:"请选择消费点"}},i=[{label:"订单号",key:"trade_no",width:"150"},{label:"应付全额",key:"origin_fee",type:"slot",slotName:"originFee"},{label:"实付全额",key:"pay_fee",type:"slot",slotName:"payFee"},{label:"支付渠道",key:"payway_alias"},{label:"支付方式",key:"sub_payway_alias"},{label:"支付时间",key:"pay_time",width:"160"},{label:"停车时长",key:"park_time_str"},{label:"一级组织",key:"primary"},{label:"二级组织",key:"secondary"},{label:"分组",key:"card_user_groups_alias",type:"slot",slotName:"userGroups"},{label:"部门",key:"department_group_alias",type:"slot",slotName:"departmentGroups"},{label:"车牌号",key:"car_no"},{label:"人员编号",key:"person_no"},{label:"姓名",key:"name"},{label:"手机号",key:"phone",width:"130"},{label:"操作",key:"operation",type:"slot",slotName:"operation",width:"100"}],p={select_date:{type:"daterange",label:"支付时间",value:[],clearable:!0},trade_no:{type:"input",value:"",label:"订单号",placeholder:"请输入"},sub_payway:{type:"select",label:"支付方式",value:"",placeholder:"请选择",listNameKey:"name",listValueKey:"value",dataList:[]},person_no:{type:"input",value:"",label:"人员编号",placeholder:"请输入"},name:{type:"input",value:"",label:"姓名",placeholder:"请输入"},phone:{type:"input",value:"",label:"手机号",placeholder:"请输入"},car_no:{type:"input",value:"",label:"车牌号",placeholder:"请输入"},payer_group_ids:{type:"groupSelect",label:"分组",value:[],placeholder:"请选择分组",multiple:!0,collapseTags:!0},payer_department_group_ids:{type:"organizationDepartmentSelect",multiple:!0,isLazy:!1,checkStrictly:!0,label:"部门",value:[],placeholder:"请选择部门"},org_ids:{type:"consumeSelect",label:"消费点",value:[],multiple:!0,placeholder:"请选择消费点"}},u=[{label:"退款订单号",key:"trade_no",labelWidth:"200px"},{label:"原订单金额",key:"origin_fee",type:"slot",slotName:"originFee"},{label:"退款全额",key:"pay_fee",type:"slot",slotName:"payFee"},{label:"退款渠道",key:"refund_payway_alias"},{label:"退款时间",key:"pay_time"},{label:"一级组织",key:"primary"},{label:"二级组织",key:"secondary"},{label:"分组",key:"card_user_groups_alias",type:"slot",slotName:"userGroups"},{label:"部门",key:"department_group_alias",type:"slot",slotName:"departmentGroups"},{label:"人员编号",key:"person_no"},{label:"姓名",key:"name"},{label:"手机号",key:"phone"},{label:"车牌号",key:"car_no"},{label:"原订单号",key:"origin_trade_no"}],c={select_date:{type:"daterange",label:"退款时间",value:[],clearable:!0},trade_no:{type:"input",value:"",label:"退款订单号",placeholder:"请输入"},sub_payway:{type:"select",label:"退款渠道",value:"",placeholder:"请选择",listNameKey:"name",listValueKey:"value",dataList:[]},sub_mch_id:{type:"input",value:"",label:"原订单号",placeholder:"请输入"},person_no:{type:"input",value:"",label:"人员编号",placeholder:"请输入"},name:{type:"input",value:"",label:"姓名",placeholder:"请输入"},phone:{type:"input",value:"",label:"手机号",placeholder:"请输入"},car_no:{type:"input",value:"",label:"车牌号",placeholder:"请输入"},payer_group_ids:{type:"groupSelect",label:"分组",value:[],placeholder:"请选择分组",multiple:!0,collapseTags:!0},payer_department_group_ids:{type:"organizationDepartmentSelect",multiple:!0,isLazy:!1,checkStrictly:!0,label:"部门",value:[],placeholder:"请选择部门"},org_ids:{type:"consumeSelect",label:"消费点",value:[],multiple:!0,placeholder:"请选择消费点"}},s=[{label:"人员编号",key:"person_no"},{label:"姓名",key:"name"},{label:"车牌号",key:"car_no"}],d="http://passage-customer-manager-test.rlinking.com/#/",b="http://po.rlinking.com/#/",m="/api/temporary/template_excel/%E8%BD%A6%E8%BE%86%E7%AE%A1%E7%90%86/%E5%AF%BC%E5%85%A5%E8%BD%A6%E8%BE%86%E7%BB%91%E5%AE%9A.xlsx",y=[{name:"全部",value:""},{name:"后台导入",value:"background_import"},{name:"手动绑定",value:"manual_bind"}],_=[{name:"全部",value:""},{name:"入场",value:"in"},{name:"出场",value:"out"}],f=[{name:"全部",value:""},{name:"会员",value:"member"},{name:"长期",value:"long_time"},{name:"临停",value:"temporary"},{name:"长期过期",value:"long_timeout"},{name:"长期超员",value:"long_time_max"}]},"1b8f":function(e,t,l){"use strict";l.r(t),l.d(t,"INITNUMBER",(function(){return r})),l.d(t,"RECENTSEVENTDAYARR",(function(){return n})),l.d(t,"TIMEARR",(function(){return i})),l.d(t,"EXPAND",(function(){return p})),l.d(t,"NOTICE",(function(){return u})),l.d(t,"NAVTYPE",(function(){return c})),l.d(t,"OPTIONLINE",(function(){return s})),l.d(t,"OPTIONYUAN",(function(){return d})),l.d(t,"OPTIONHUAN",(function(){return b})),l.d(t,"COLUMNS",(function(){return m})),l.d(t,"OPRIONBAR",(function(){return y})),l.d(t,"PICKEROPTIONS",(function(){return _})),l.d(t,"RECENTSEVENTDAY",(function(){return f})),l.d(t,"ULR_DO_BEI_SYSTEM_DEV",(function(){return h})),l.d(t,"ULR_DO_BEI_SYSTEM_STAGING",(function(){return g})),l.d(t,"ULR_DO_BEI_SHOP_DEV",(function(){return v})),l.d(t,"ULR_DO_BEI_SHOP_STAGING",(function(){return A})),l.d(t,"URL_WASHING_SYSTEM_DEV",(function(){return S})),l.d(t,"URL_WASHING_SYSTEM_STAGING",(function(){return x}));var a=l("5a0c"),o=l("035f"),r=function(e){var t="",l=e.toString().split("");if(e>=1e6&&e<=9999999)l.splice(1,0,","),l.splice(5,0,","),t=l.join("");else if(e>=1e5)l.splice(3,0,","),t=l.join("");else if(e>=1e4)l.splice(2,0,","),t=l.join("");else if(e>=1e3)l.splice(1,0,","),t=l.join("");else if(e<1e3&&e>=0)return e;return t},n=function(){for(var e=[a().format("YYYY-MM-DD")],t=1;t<7;t++)e.unshift(a().subtract(t,"day").format("YYYY-MM-DD"));return e},i=[{value:"today",label:"今天"},{value:"week",label:"最近7天"},{value:"month",label:"最近30天"}],p=[{id:9,label:"洗衣管理",disabled:!0,src:l("b977"),type:"ZK_LAUNDRY"},{id:6,label:"车辆管理",disabled:!0,src:l("a744"),url:o["URL_MANUFACTURER"],type:"car_management"},{id:1,label:"考勤管理",disabled:!0,src:l("ded3")},{id:2,label:"水控管理",disabled:!0,src:l("e82b")},{id:3,label:"电控管理",disabled:!0,src:l("b23c")},{id:10,label:"督贝管理",disabled:!0,src:l("2e83"),type:"DoBay"},{id:11,label:"智慧门店",disabled:!0,src:l("d49f"),type:"DoBay"},{id:4,label:"监控管理",disabled:!0,src:l("abea")},{id:5,label:"门禁管理",disabled:!0,src:l("c70d")}],u=[{id:1,label:"大声的呐喊大卡司打开大声的呐喊大卡司打开",time:"2022-03-03 15:08"},{id:2,label:"大声的呐喊大卡司打开",time:"2022-03-01 12:20"},{id:3,label:"大声的呐喊大卡司打开",time:"2022-02-28 10:00"},{id:4,label:"大声的呐喊大卡司打开",time:"2022-02-01 12:15"}],c=[{id:1,fee:89536,label:"营业额"},{id:2,fee:73124,label:"实收金额"},{id:3,fee:3560,label:"消费订单笔数"},{id:4,fee:26707.5,label:"充值金额"},{id:5,fee:3006.58,label:"退款金额"}],s={xAxis:{type:"category",data:n(),axisLine:{show:!1},axisTick:{show:!1},splitLine:{show:!1},axisLabel:{textStyle:{fontSize:14,color:"#666",lineHeight:50}}},yAxis:{type:"value",axisTick:{show:!1},axisLine:{show:!1},splitLine:{show:!0,lineStyle:{type:"dashed",color:"#dfe5ec"}},splitNumber:4,axisLabel:{textStyle:{fontSize:16,color:"#666"},formatter:function(e,t){return e>=1e3&&(e=e/1e3+"k"),e}}},tooltip:{trigger:"axis",axisPointer:{lineStyle:{type:"dashed"}},transitionDuration:0,borderColor:"#FCA155",textStyle:{color:"#000"},backgroundColor:"#fff",extraCssText:"box-shadow: 0 0 10px rgba(0,0,0,0.2);font-weight: 540;",formatter:'<div style="padding:5px;font-size:16px;font-weight: 540;">{b0}<br /><span style=" color: #666;display:inline-block;margin: 5px 20px 0 0;">营业额</span>￥{c0}</div>'},grid:{left:"40",top:"20"},series:[{data:[14820,15932,12437,13934,11900,20213,11010],type:"line",smooth:!0,symbolSize:10,showSymbol:!1,lineStyle:{color:"#FCAD6A",width:4},itemStyle:{borderColor:"#FCAD6A",borderWidth:3}}]},d={tooltip:{trigger:"item",borderColor:"#FCA155",textStyle:{color:"#000",fontWeight:500},backgroundColor:"#fff",extraCssText:"box-shadow: 0 0 10px rgba(0,0,0,0.2);font-weight: 540;",formatter:function(e){var t=e.marker,l=e.percent;return t+e.name+"&nbsp;&nbsp;&nbsp;"+l+"%"}},title:{text:0,subtext:"订单总笔数",left:"center",top:"30%",textStyle:{color:"#000",fontSize:40,align:"center"},subtextStyle:{color:"#999",fontSize:16,align:"center"}},legend:[{bottom:"18%",left:"20",itemWidth:8,itemHeight:8,icon:"circle",data:["早餐"],width:400,textStyle:{fontSize:"14"},formatter:function(e){var t=Number(d.title.text.replace(/,/g,"")),l=[Math.round(.2*t)];return e+"    "+r(l[0])+"笔"},itemGap:120},{bottom:"18%",left:"60%",itemWidth:8,itemHeight:8,icon:"circle",data:["午餐"],width:400,textStyle:{fontSize:"14"},formatter:function(e){var t=Number(d.title.text.replace(/,/g,"")),l=[Math.round(.3*t)];return e+"    "+r(l[0])+"笔"},itemGap:120},{bottom:"10%",left:"20",itemWidth:8,itemHeight:8,icon:"circle",data:["下午茶"],width:400,textStyle:{fontSize:"14"},formatter:function(e){var t=Number(d.title.text.replace(/,/g,"")),l=[Math.round(.1*t)];return e+"    "+r(l[0])+"笔"},itemGap:118},{bottom:"10%",left:"60%",itemWidth:8,itemHeight:8,icon:"circle",data:["晚餐"],width:400,textStyle:{fontSize:"14"},formatter:function(e){var t=Number(d.title.text.replace(/,/g,"")),l=[Math.round(.25*t)];return e+"    "+r(l[0])+"笔"},itemGap:118},{bottom:"2%",left:"20",itemWidth:8,itemHeight:8,icon:"circle",data:["宵夜"],width:400,textStyle:{fontSize:"14"},formatter:function(e){var t=Number(d.title.text.replace(/,/g,"")),l=[Math.round(.09*t)];return e+"    "+r(l[0])+"笔"}},{bottom:"2%",left:"60%",itemWidth:8,itemHeight:8,icon:"circle",data:["凌晨餐"],width:400,textStyle:{fontSize:"14"},formatter:function(e){var t=Number(d.title.text.replace(/,/g,"")),l=[Math.round(.06*t)];return e+"    "+r(l[0])+"笔"}}],series:[{type:"pie",radius:["70%","60%"],avoidLabelOverlap:!1,top:"-25%",itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:5},hoverAnimation:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!1,fontSize:"20",fontWeight:"bold"}},labelLine:{show:!1},data:[{value:1048,name:"早餐"},{value:1235,name:"午餐"},{value:780,name:"下午茶"},{value:684,name:"晚餐"},{value:400,name:"宵夜"},{value:200,name:"凌晨餐"}]}],color:["#07DED0","#FE985F","#9E92F7","#F97C95","#58AFFE","#F8C345"]},b={tooltip:{trigger:"item",borderColor:"#FCA155",textStyle:{color:"#000"},backgroundColor:"#fff",extraCssText:"box-shadow: 0 0 10px rgba(0,0,0,0.2);font-weight: 540;",formatter:function(e){var t=e.marker,l=e.percent;return t+e.name+"&nbsp;&nbsp;&nbsp;"+l+"%"}},legend:[{bottom:"40",left:"5%",icon:"circle",data:["堂食"],textStyle:{fontSize:"14"},formatter:function(e){var t=Number(d.title.text.replace(/,/g,"")),l=[Math.round(.3*t)];return e+"    "+r(l[0])+"笔"}},{bottom:"40",left:"50%",icon:"circle",data:["外卖配送"],textStyle:{fontSize:"14"},formatter:function(e){var t=Number(d.title.text.replace(/,/g,"")),l=[Math.round(.2*t)];return e+"    "+r(l[0])+"笔"}},{bottom:"10",left:"5%",icon:"circle",data:["食堂自提"],textStyle:{fontSize:"14"},formatter:function(e){var t=Number(d.title.text.replace(/,/g,"")),l=[Math.round(.1*t)];return e+"    "+r(l[0])+"笔"}},{bottom:"10",left:"50%",icon:"circle",data:["取餐柜自提"],textStyle:{fontSize:"14"},formatter:function(e){var t=Number(d.title.text.replace(/,/g,"")),l=[Math.round(.4*t)];return e+"    "+r(l[0])+"笔"}}],series:[{type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,top:"-20%",left:"-5%",label:{show:!1},labelLine:{show:!1},data:[{value:1198,name:"堂食"},{value:835,name:"外卖配送"},{value:680,name:"食堂自提"},{value:634,name:"取餐柜自提"}]}],color:["#4D95FA","#07D7D7","#727AFF","#4AD96D"]},m=[{label:"餐段",column:"meal_type"},{label:"营业额",column:"turnover"},{label:"占比",column:"Proportion"}],y={xAxis:{type:"category",data:["琶洲食堂","万胜围食堂","新港东食堂","棠东食堂","保利食堂","天河中心食堂","太古汇食堂","黄村食堂"],axisLine:{show:!1},axisTick:{show:!1},splitLine:{show:!1},axisLabel:{textStyle:{fontSize:16,color:"#666",lineHeight:40}}},yAxis:{type:"value",axisLine:{show:!1},axisTick:{show:!1},splitNumber:4,axisLabel:{textStyle:{fontSize:16,color:"#666"},formatter:function(e,t){return e>=1e3&&(e=e/1e3+"k"),e}},splitLine:{show:!0,lineStyle:{type:"dashed",color:"#dfe5ec"}}},grid:{left:"40",top:"50"},tooltip:{borderColor:"#FCA155",textStyle:{color:"#000"},transitionDuration:0,backgroundColor:"#fff",extraCssText:"box-shadow: 0 0 10px rgba(0,0,0,0.2);font-weight: 540;",formatter:function(e){var t=e.value,l=e.name;return'<div style="padding:5px;font-size:16px;font-weight: 540;">'.concat(l,'<br /><span style=" color: #666;display:inline-block;margin: 5px 20px 0 0;">营业额</span>￥').concat(r(t))}},series:[{barWidth:30,color:"#FE943C",data:[11e3,7500,9e3,5645.5,5900,6100,11800,7e3,8500],type:"bar"}]},_={shortcuts:[{text:"最近一周",onClick:function(e){var t=new Date,l=new Date;l.setTime(l.getTime()-6048e5),e.$emit("pick",[l,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,l=new Date;l.setTime(l.getTime()-2592e6),e.$emit("pick",[l,t])}},{text:"最近三个月",onClick:function(e){var t=new Date,l=new Date;l.setTime(l.getTime()-7776e6),e.$emit("pick",[l,t])}}],disabledDate:function(e){var t=new Date;return t.setTime(t.getTime()-7776e6),e.getTime()>Date.now()-864e4||e.getTime()<t}},f=[a().subtract(7,"day").format("YYYY-MM-DD"),a().format("YYYY-MM-DD")],h="https://dobayadmin.anasit.com/login",g="https://dobayadmin.anasit.com/login",v="https://shopadmin.anasit.com/login",A="https://shopadmin.anasit.com/login",S="https://web-xhf.lxt6.cn:8089/#/login",x="https://web-xhf.lxt6.cn:8089/#/login"}}]);