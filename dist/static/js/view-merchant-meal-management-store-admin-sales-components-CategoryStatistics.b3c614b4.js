(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-store-admin-sales-components-CategoryStatistics","view-merchant-meal-management-store-admin-goodsStockDetails","view-merchant-meal-management-store-admin-components-constants"],{"2fa2":function(e,t,r){},"323e0":function(e,t,r){"use strict";r("2fa2")},"5a97":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"CategoryStatistics container-wrapper"},[t("search-form",{ref:"searchRef",attrs:{"form-setting":e.searchFormSetting,autoSearch:!1},on:{search:e.searchHandle}}),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],staticClass:"table-wrapper"},[t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[e._v("数据列表")]),t("div",{staticClass:"align-r"},[t("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_store.goods.category_sales_summary_export"],expression:"['background_store.goods.category_sales_summary_export']"}],attrs:{color:"plain",type:"export"},on:{click:e.gotoExport}},[e._v("导出EXCEL")])],1)]),t("div",{staticClass:"table-content"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","header-row-class-name":"ps-table-header-row"},on:{"sort-change":e.sortChange}},e._l(e.tableSetting,(function(r){return t("table-column",{key:r.key,attrs:{col:r,sortable:r.sortable},scopedSlots:e._u([{key:"index",fn:function(r){var a=r.row;return["slot"===a.index?t("div",[e._v(e._s(a.sumsName))]):t("div",[e._v(e._s(a.index))])]}},{key:"cost_money",fn:function(r){var a=r.row;return[t("span",[e._v(e._s(e._f("formatDate")(a.cost_money)))])]}},{key:"sale_money",fn:function(r){var a=r.row;return[t("span",[e._v(e._s(e._f("formatDate")(a.sale_money)))])]}},{key:"sale_profit",fn:function(r){var a=r.row;return[t("span",[e._v(e._s(e._f("formatDate")(a.sale_profit)))])]}},{key:"count",fn:function(r){var a=r.row;return[t("div",[e._v(e._s(a.count))])]}}],null,!0)})})),1),t("div",{staticClass:"tips p-t-10"},[e._v("注：默认按照销售数量降序")])],1),t("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[t("el-pagination",{staticClass:"ps-text",attrs:{"current-page":e.currentPage,"page-sizes":[10,20,50,100,500],"page-size":e.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)])],1)},n=[],o=r("ed08"),i=r("a94d"),l=r("f63a");function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function u(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach((function(t){p(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function p(e,t,r){return(t=y(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function y(e){var t=m(e,"string");return"symbol"==s(t)?t:t+""}function m(e,t){if("object"!=s(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=s(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function f(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */f=function(){return t};var e,t={},r=Object.prototype,a=r.hasOwnProperty,n=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",l=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function p(e,t,r,a){var o=t&&t.prototype instanceof b?t:b,i=Object.create(o.prototype),l=new P(a||[]);return n(i,"_invoke",{value:j(e,r,l)}),i}function y(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=p;var m="suspendedStart",d="suspendedYield",h="executing",g="completed",v={};function b(){}function _(){}function w(){}var S={};u(S,i,(function(){return this}));var L=Object.getPrototypeOf,O=L&&L(L(D([])));O&&O!==r&&a.call(O,i)&&(S=O);var x=w.prototype=b.prototype=Object.create(S);function C(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function T(e,t){function r(n,o,i,l){var c=y(e[n],e,o);if("throw"!==c.type){var u=c.arg,p=u.value;return p&&"object"==s(p)&&a.call(p,"__await")?t.resolve(p.__await).then((function(e){r("next",e,i,l)}),(function(e){r("throw",e,i,l)})):t.resolve(p).then((function(e){u.value=e,i(u)}),(function(e){return r("throw",e,i,l)}))}l(c.arg)}var o;n(this,"_invoke",{value:function(e,a){function n(){return new t((function(t,n){r(e,a,t,n)}))}return o=o?o.then(n,n):n()}})}function j(t,r,a){var n=m;return function(o,i){if(n===h)throw Error("Generator is already running");if(n===g){if("throw"===o)throw i;return{value:e,done:!0}}for(a.method=o,a.arg=i;;){var l=a.delegate;if(l){var s=E(l,a);if(s){if(s===v)continue;return s}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(n===m)throw n=g,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);n=h;var c=y(t,r,a);if("normal"===c.type){if(n=a.done?g:d,c.arg===v)continue;return{value:c.arg,done:a.done}}"throw"===c.type&&(n=g,a.method="throw",a.arg=c.arg)}}}function E(t,r){var a=r.method,n=t.iterator[a];if(n===e)return r.delegate=null,"throw"===a&&t.iterator.return&&(r.method="return",r.arg=e,E(t,r),"throw"===r.method)||"return"!==a&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+a+"' method")),v;var o=y(n,t.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,v;var i=o.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,v):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function k(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function N(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function P(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(k,this),this.reset(!0)}function D(t){if(t||""===t){var r=t[i];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function r(){for(;++n<t.length;)if(a.call(t,n))return r.value=t[n],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(s(t)+" is not iterable")}return _.prototype=w,n(x,"constructor",{value:w,configurable:!0}),n(w,"constructor",{value:_,configurable:!0}),_.displayName=u(w,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===_||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,u(e,c,"GeneratorFunction")),e.prototype=Object.create(x),e},t.awrap=function(e){return{__await:e}},C(T.prototype),u(T.prototype,l,(function(){return this})),t.AsyncIterator=T,t.async=function(e,r,a,n,o){void 0===o&&(o=Promise);var i=new T(p(e,r,a,n),o);return t.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},C(x),u(x,c,"Generator"),u(x,i,(function(){return this})),u(x,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var a in t)r.push(a);return r.reverse(),function e(){for(;r.length;){var a=r.pop();if(a in t)return e.value=a,e.done=!1,e}return e.done=!0,e}},t.values=D,P.prototype={constructor:P,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(N),!t)for(var r in this)"t"===r.charAt(0)&&a.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(a,n){return l.type="throw",l.arg=t,r.next=a,n&&(r.method="next",r.arg=e),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],l=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var s=a.call(i,"catchLoc"),c=a.call(i,"finallyLoc");if(s&&c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),N(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var a=r.completion;if("throw"===a.type){var n=a.arg;N(r)}return n}}throw Error("illegal catch attempt")},delegateYield:function(t,r,a){return this.delegate={iterator:D(t),resultName:r,nextLoc:a},"next"===this.method&&(this.arg=e),v}},t}function d(e,t){return _(e)||b(e,t)||g(e,t)||h()}function h(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function g(e,t){if(e){if("string"==typeof e)return v(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?v(e,t):void 0}}function v(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,a=Array(t);r<t;r++)a[r]=e[r];return a}function b(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var a,n,o,i,l=[],s=!0,c=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;s=!1}else for(;!(s=(a=o.call(r)).done)&&(l.push(a.value),l.length!==t);s=!0);}catch(e){c=!0,n=e}finally{try{if(!s&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(c)throw n}}return l}}function _(e){if(Array.isArray(e))return e}function w(e,t,r,a,n,o,i){try{var l=e[o](i),s=l.value}catch(e){return void r(e)}l.done?t(s):Promise.resolve(s).then(a,n)}function S(e){return function(){var t=this,r=arguments;return new Promise((function(a,n){var o=e.apply(t,r);function i(e){w(o,a,n,i,l,"next",e)}function l(e){w(o,a,n,i,l,"throw",e)}i(void 0)}))}}var L={name:"CategoryStock",mixins:[l["a"]],data:function(){return{isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[],tableSetting:[{label:"销售排行",type:"slot",slotName:"index"},{label:"分类",key:"category_name"},{label:"已销售成本总额",key:"cost_money",type:"money",slotName:"cost_money",sortable:"custom"},{label:"已销售总额",key:"sale_money",type:"money",slotName:"sale_money",sortable:"custom"},{label:"已销售利润",key:"sale_profit",type:"money",slotName:"sale_profit",sortable:"custom"},{label:"已销售数量",key:"count",slotName:"count",sortable:"custom"}],searchFormSetting:i["CATEGORY_STATISTICS"],sortData:{}}},created:function(){this.getGoodsCategoryList(),this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getCategorySalesSummary()},sortChange:function(e){var t="";"ascending"===e.order?t="asc":"descending"===e.order&&(t="desc"),e.order?this.sortData={sort_name:e.prop,sort_type:t}:this.sortData={},this.getCategorySalesSummary()},searchHandle:Object(o["d"])((function(e){e&&"search"===e&&(this.currentPage=1,this.getCategorySalesSummary())}),300),getGoodsCategoryList:function(){var e=this;return S(f().mark((function t(){var r,a,n,i;return f().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(o["Z"])(e.$apis.apiBackgroundStoreGoodsCategoryListPost({page:1,page_size:99999}));case 2:if(r=t.sent,a=d(r,2),n=a[0],i=a[1],!n){t.next=9;break}return e.$message.error(n.message),t.abrupt("return");case 9:0===i.code?e.searchFormSetting.goods_category_ids.dataList=i.data.results:e.$message.error(i.msg);case 10:case"end":return t.stop()}}),t)})))()},getCategorySalesSummary:function(){var e=this;return S(f().mark((function t(){var r,a,n,i,l;return f().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(o["Z"])(e.$apis.apiBackgroundStoreGoodsCategorySalesSummaryPost(u(u({},e.formatQueryParams(e.searchFormSetting)),{},{page:e.currentPage,page_size:e.pageSize},e.sortData)));case 3:if(r=t.sent,a=d(r,2),n=a[0],i=a[1],e.isLoading=!1,!n){t.next=11;break}return e.$message.error(n.message),t.abrupt("return");case 11:0===i.code?(e.tableData=[],l=i.data.summary_data,e.totalCount=i.data.count,e.tableData=i.data.results,e.tableData.length&&e.tableData.push({index:"slot",sumsName:"当页合计",cost_money:l.cost_money,sale_money:l.sale_money,sale_profit:l.sale_profit,count:l.count},{index:"slot",sumsName:"全部合计",cost_money:l.total_cost_money,sale_money:l.total_sale_money,sale_profit:l.total_sale_profit,count:l.total_count})):e.$message.error(i.msg);case 12:case"end":return t.stop()}}),t)})))()},formatQueryParams:function(e){var t={};for(var r in e)""!==e[r].value&&null!==e[r].value&&("select_time"!==r?t[r]=e[r].value:e[r].value.length>0&&(t.start_time=e[r].value[0],t.end_time=e[r].value[1]));return t},handleSizeChange:function(e){this.pageSize=e,this.getCategorySalesSummary()},handleCurrentChange:function(e){this.currentPage=e,this.getCategorySalesSummary()},gotoExport:function(){var e={type:"ExportCategoryStatistics",params:u(u({},this.formatQueryParams(this.searchFormSetting)),{},{page:this.currentPage,page_size:this.pageSize})};this.exportHandle(e)}}},O=L,x=(r("323e0"),r("2877")),C=Object(x["a"])(O,a,n,!1,null,"586d2928",null);t["default"]=C.exports},a94d:function(e,t,r){"use strict";r.r(t),r.d(t,"STORE_GOODS_ADMIN_INFO",(function(){return n})),r.d(t,"STORE_STOCK",(function(){return o})),r.d(t,"CATEGORY_STOCK",(function(){return i})),r.d(t,"CATEGORY_STATISTICS",(function(){return l})),r.d(t,"GOODS_STATISTICS",(function(){return s})),r.d(t,"ADD_STOCK_DETAILS",(function(){return c})),r.d(t,"DEDUCT_STOCK_DETAILS",(function(){return u}));var a=r("ed08"),n={goods_category_ids:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"分类",dataList:[]},barcode:{type:"input",label:"条码",value:"",placeholder:"请输入条码"},name:{type:"input",label:"商品名称",value:"",placeholder:"请输入商品名称"},sale_status:{type:"select",label:"上下架",value:"",placeholder:"请选择类型",dataList:[{label:"全部",value:"2"},{label:"上架",value:"1"},{label:"下架",value:"0"}]},goods_unit:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"单位",dataList:[]},other:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"type",label:"其他",dataList:[{name:"一品多码商品",type:"is_multi_barcode"},{name:"多规格商品",type:"is_multi_spec"}]}},o={goods_category_ids:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"分类",dataList:[]},barcode:{type:"input",label:"条码",value:"",placeholder:"请输入条码"},name:{type:"input",label:"商品名称",value:"",placeholder:"请输入商品名称"},goods_unit:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"单位",dataList:[]}},i={goods_category_ids:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"分类",dataList:[]}},l={select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"创建时间",value:Object(a["t"])(-7),clearable:!1},goods_category_ids:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"分类",dataList:[]}},s={select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"创建时间",value:Object(a["t"])(-7),clearable:!1},goods_category_ids:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"分类",dataList:[]},barcode:{type:"input",label:"条码",value:"",placeholder:"请输入条码"},name:{type:"input",label:"商品名称",value:"",placeholder:"请输入商品名称"}},c={select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"入库时间",value:Object(a["t"])(-7),clearable:!1},goods_category_ids:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"分类",dataList:[]},barcode:{type:"input",label:"条码",value:"",placeholder:"请输入条码"},name:{type:"input",label:"商品名称",value:"",placeholder:"请输入商品名称"},operator_name:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"},operate_type:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"type",label:"入库类型",dataList:[{name:"操作入库",type:"operate"},{name:"退款入库",type:"refund"},{name:"失败订单入库",type:"order_fail"}]},trade_no:{type:"input",label:"订单号",value:"",placeholder:"请输入订单号"}},u={select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"出库时间",value:Object(a["t"])(-7),clearable:!1},goods_category_ids:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"分类",dataList:[]},barcode:{type:"input",label:"条码",value:"",placeholder:"请输入条码"},name:{type:"input",label:"商品名称",value:"",placeholder:"请输入商品名称"},operator_name:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"},operate_type:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"type",label:"出库原因",dataList:[{name:"销售出库",type:"sale"},{name:"盘点出库",type:"check"},{name:"保质期出库",type:"allot"},{name:"破损出库",type:"breakage"},{name:"其他",type:"other"}]},trade_no:{type:"input",label:"订单号",value:"",placeholder:"请输入订单号"}}}}]);