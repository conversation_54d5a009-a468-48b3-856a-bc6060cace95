(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-report-report-management-SummaryOfSales","view-merchant-home-page-components-FoodCategory","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-meal-report-MealPackageRule","view-merchant-order-RechargeOrder","view-merchant-order-RefundOrder","view-merchant-report-report-management-FoodSaleRanking","view-merchant-report-report-management-constantsConfig"],{"20e4":function(t,e,r){"use strict";r.r(e),r.d(e,"recentSevenDay",(function(){return i})),r.d(e,"FoodSaleRanking",(function(){return o})),r.d(e,"SummaryOfSales",(function(){return c})),r.d(e,"TABLE_HEAD_DATA_DISHES_TAKEN",(function(){return l})),r.d(e,"SEARCH_FORM_SET_DATA_DISHES_TAKEN",(function(){return s}));var n=r("5a0c"),a=r("c9d9"),i=[n().subtract(7,"day").format("YYYY-MM-DD"),n().format("YYYY-MM-DD")],o={select_time:{type:"daterange",label:"就餐时间",value:i,format:"yyyy-MM-dd",clearable:!1},meal_type_list:{type:"select",label:"餐段",value:[],multiple:!0,placeholder:"请选择",collapseTags:!0,dataList:a["a"]},org_ids:{type:"organizationSelect",value:[],label:"消费点",listNameKey:"name",listValueKey:"id",dataList:[],checkStrictly:!0,multiple:!0,collapseTags:!0,clearable:!0},food_name:{type:"input",value:"",label:"菜品名称",maxlength:20,placeholder:"请输入菜品名称"}},c={select_time:{type:"daterange",label:"就餐时间",value:i,format:"yyyy-MM-dd",clearable:!1},meal_type_list:{type:"select",label:"餐段",value:[],multiple:!0,placeholder:"请选择",collapseTags:!0,dataList:a["a"]},org_ids:{type:"organizationSelect",value:[],label:"消费点",listNameKey:"name",listValueKey:"id",dataList:[],checkStrictly:!0,multiple:!0,collapseTags:!0,clearable:!0}},l=[{label:"所属组织",key:"org_name"},{label:"菜品一级分类",key:"sort"},{label:"菜品二级分类",key:"category"},{label:"菜品名称",key:"name"},{label:"取用量",key:"food_weight",type:"slot",slotName:"foodWeight"},{label:"取菜人数",key:"use_count"},{label:"平均取用量",key:"average_weight",type:"slot",slotName:"averageWeight"}],s={select_time:{type:"daterange",label:"就餐时间",value:[],format:"yyyy-MM-dd",clearable:!1},food_name:{type:"input",value:"",label:"菜品名称",maxlength:20,placeholder:"请输入菜品名称"},org_ids:{type:"organizationSelect",value:[],label:"所属组织",listNameKey:"name",listValueKey:"id",dataList:[],checkStrictly:!0,multiple:!0,collapseTags:!0,clearable:!0},meal_type:{type:"select",label:"餐段",value:[],multiple:!0,placeholder:"请选择",collapseTags:!0,dataList:a["a"]}}},4960:function(t,e,r){},5662:function(t,e,r){"use strict";r("9d48")},"87ac":function(t,e,r){"use strict";var n=r("ed08"),a=r("2f62");function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function o(t,e){return f(t)||u(t,e)||l(t,e)||c()}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function l(t,e){if(t){if("string"==typeof t)return s(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?s(t,e):void 0}}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function u(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,i,o,c=[],l=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);l=!0);}catch(t){s=!0,a=t}finally{try{if(!l&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw a}}return c}}function f(t){if(Array.isArray(t))return t}function h(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */h=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},c=o.iterator||"@@iterator",l=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,o=Object.create(i.prototype),c=new D(n||[]);return a(o,"_invoke",{value:E(t,r,c)}),o}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var d="suspendedStart",y="suspendedYield",g="executing",m="completed",v={};function b(){}function _(){}function w(){}var S={};u(S,c,(function(){return this}));var k=Object.getPrototypeOf,x=k&&k(k(F([])));x&&x!==r&&n.call(x,c)&&(S=x);var L=w.prototype=b.prototype=Object.create(S);function O(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function P(t,e){function r(a,o,c,l){var s=p(t[a],t,o);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==i(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,c,l)}),(function(t){r("throw",t,c,l)})):e.resolve(f).then((function(t){u.value=t,c(u)}),(function(t){return r("throw",t,c,l)}))}l(s.arg)}var o;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return o=o?o.then(a,a):a()}})}function E(e,r,n){var a=d;return function(i,o){if(a===g)throw Error("Generator is already running");if(a===m){if("throw"===i)throw o;return{value:t,done:!0}}for(n.method=i,n.arg=o;;){var c=n.delegate;if(c){var l=j(c,n);if(l){if(l===v)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===d)throw a=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=g;var s=p(e,r,n);if("normal"===s.type){if(a=n.done?m:y,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(a=m,n.method="throw",n.arg=s.arg)}}}function j(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,j(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=p(a,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var o=i.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function T(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function C(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function D(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(T,this),this.reset(!0)}function F(e){if(e||""===e){var r=e[c];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,o=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(i(e)+" is not iterable")}return _.prototype=w,a(L,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:_,configurable:!0}),_.displayName=u(w,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===_||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,u(t,s,"GeneratorFunction")),t.prototype=Object.create(L),t},e.awrap=function(t){return{__await:t}},O(P.prototype),u(P.prototype,l,(function(){return this})),e.AsyncIterator=P,e.async=function(t,r,n,a,i){void 0===i&&(i=Promise);var o=new P(f(t,r,n,a),i);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},O(L),u(L,s,"Generator"),u(L,c,(function(){return this})),u(L,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=F,D.prototype={constructor:D,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(C),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return c.type="throw",c.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],c=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var l=n.call(o,"catchLoc"),s=n.call(o,"finallyLoc");if(l&&s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),C(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;C(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:F(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function p(t,e,r,n,a,i,o){try{var c=t[i](o),l=c.value}catch(t){return void r(t)}c.done?e(l):Promise.resolve(l).then(n,a)}function d(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var i=t.apply(e,r);function o(t){p(i,n,a,o,c,"next",t)}function c(t){p(i,n,a,o,c,"throw",t)}o(void 0)}))}}function y(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function g(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?y(Object(r),!0).forEach((function(e){m(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function m(t,e,r){return(e=v(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function v(t){var e=b(t,"string");return"symbol"==i(e)?e:e+""}function b(t,e){if("object"!=i(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}e["a"]={data:function(){return{currentTableSetting:[],dialogPrintVisible:!1}},computed:g({},Object(a["c"])(["userInfo"])),mounted:function(){},methods:{initPrintSetting:function(){var t=this;return d(h().mark((function e(){var r,a;return h().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=[],e.next=3,t.getPrintSettingInfo();case 3:r=e.sent;try{r=r?Object(n["E"])(r):Object(n["E"])(t.tableSetting)}catch(i){r=Object(n["E"])(t.tableSetting)}r.length<12?(a=Object(n["m"])(t.tableSetting,r),a=t.deleteWidthKey(a),t.currentTableSetting=a):t.currentTableSetting=Object(n["m"])(t.tableSetting,r);case 6:case"end":return e.stop()}}),e)})))()},getPrintSettingInfo:function(){var t=this;return d(h().mark((function e(){var r,a,i,c,l;return h().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=null,e.next=3,Object(n["Z"])(t.$apis.apiBackgroundAdminAccountGetAccountPrintInfoPost({id:t.userInfo.account_id,print_key:t.printType}));case 3:if(a=e.sent,i=o(a,2),c=i[0],l=i[1],!c){e.next=10;break}return t.$message.error(c.message),e.abrupt("return",r);case 10:return 0===l.code?r=l.data:t.$message.error(l.msg),e.abrupt("return",r);case 12:case"end":return e.stop()}}),e)})))()},setPrintSettingInfo:function(t,e){var r=this;return d(h().mark((function a(){var i,c,l,s;return h().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,Object(n["Z"])(r.$apis.apiBackgroundAdminAccountSetAccountPrintInfoPost(g({id:r.userInfo.account_id,print_key:r.printType,print_list:t},e)));case 2:if(i=a.sent,c=o(i,2),l=c[0],s=c[1],!l){a.next=9;break}return r.$message.error(l.message),a.abrupt("return");case 9:0===s.code?r.$message.success("设置成功"):r.$message.error(s.msg);case 10:case"end":return a.stop()}}),a)})))()},openPrintSetting:function(){this.dialogPrintVisible=!0},confirmPrintDialog:function(t,e){var r=this;return d(h().mark((function a(){var i;return h().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(!t){a.next=6;break}return i=Object(n["f"])(t),i.length<12&&(i=r.deleteWidthKey(i)),a.next=5,r.setPrintSettingInfo(i,e);case 5:r.currentTableSetting=i;case 6:case"end":return a.stop()}}),a)})))()},deleteWidthKey:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"children";function r(t){t.map((function(t){t.width&&delete t.width,t.minWidth&&delete t.minWidth,t[e]&&t[e].length>0&&r(t[e])}))}return r(t),t},indexMethod:function(t){return(this.page-1)*this.pageSize+(t+1)},setCollectData:function(t){var e=this;this.collect.forEach((function(r){t.data.collect&&void 0!==t.data.collect[r.key]&&e.$set(r,"value",t.data.collect[r.key])}))},setSummaryData:function(t){var e=t.data.collect;e[this.tableSetting[0].key]="合计",this.tableData.length&&this.tableData.push(e)}},beforeDestroy:function(){}}},"894e":function(t,e,r){"use strict";r("4960")},"9d48":function(t,e,r){},b068:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",[e("div",{staticClass:"summary-of-sales container-wrapper"},[e("refresh-tool",{on:{refreshPage:t.refreshHandle}}),e("search-form",{ref:"searchRef",attrs:{loading:t.isLoading,"form-setting":t.searchFormSetting,autoSearch:!1},on:{search:t.searchHandle,reset:t.resetHandler}},[e("div",{staticClass:"searchref_top",attrs:{slot:"perv"},slot:"perv"},t._l(t.tabsList,(function(r,n){return e("el-button",{key:n,class:{active:r.type===t.currentType},on:{click:function(e){return t.tabHandler(r)}}},[t._v(" "+t._s(r.name)+" ")])})),1),e("div",{staticClass:"ps-flex",attrs:{slot:"append"},slot:"append"},[e("div",{staticClass:"el-form-item__label w-80 m-b-20"},[t._v("菜品分类")]),e("food-category",{ref:"foodCateGory",staticStyle:{width:"200px"},attrs:{onlyFirstList:!0},on:{input:t.changeCategory}})],1)]),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("数据列表")]),e("div",{staticClass:"align-r"},[e("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_order.manage_report.food_sort_payment_ranking_list_export"],expression:"['background_order.manage_report.food_sort_payment_ranking_list_export']"}],attrs:{color:"plain",type:"export"},on:{click:t.gotoExport}},[t._v("导出Excel")]),e("button-icon",{attrs:{color:"plain"},on:{click:t.gotoPrint}},[t._v("打印")]),e("button-icon",{attrs:{color:"plain"},on:{click:t.openPrintSetting}},[t._v("报表设置")])],1)]),e("div",{staticClass:"table-content"},[e("custom-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{border:"","table-data":t.tableData,"table-setting":t.currentTableSetting,stripe:"",index:t.indexMethod,isFirst:t.isFirstSearch,"default-sort":{prop:"sell_count",order:"descending"},"header-row-class-name":"ps-table-header-row"},on:{"sort-change":t.sortChange}})],1),e("table-statistics",{attrs:{statistics:t.collect}}),e("pagination",{attrs:{onPaginationChange:t.onPaginationChange,"current-page":t.currentPage,"page-size":t.pageSize,layout:"total, prev, pager, next, jumper",total:t.total},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"update:pageSize":function(e){t.pageSize=e},"update:page-size":function(e){t.pageSize=e}}})],1)],1),t.dialogPrintVisible?e("print-setting",{attrs:{extraParams:{printType:t.printType},tableSetting:t.tableSetting,defaultCheckedSetting:t.currentTableSetting,show:t.dialogPrintVisible},on:{"update:show":function(e){t.dialogPrintVisible=e},confirm:t.confirmPrintDialog}}):t._e()],1)},a=[],i=r("20e4"),o=r("ed08"),c=r("f63a"),l=r("87ac"),s=r("d4e4");function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function f(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */f=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function h(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,o=Object.create(i.prototype),c=new D(n||[]);return a(o,"_invoke",{value:E(t,r,c)}),o}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var d="suspendedStart",y="suspendedYield",g="executing",m="completed",v={};function b(){}function _(){}function w(){}var S={};s(S,o,(function(){return this}));var k=Object.getPrototypeOf,x=k&&k(k(F([])));x&&x!==r&&n.call(x,o)&&(S=x);var L=w.prototype=b.prototype=Object.create(S);function O(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function P(t,e){function r(a,i,o,c){var l=p(t[a],t,i);if("throw"!==l.type){var s=l.arg,f=s.value;return f&&"object"==u(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,o,c)}),(function(t){r("throw",t,o,c)})):e.resolve(f).then((function(t){s.value=t,o(s)}),(function(t){return r("throw",t,o,c)}))}c(l.arg)}var i;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return i=i?i.then(a,a):a()}})}function E(e,r,n){var a=d;return function(i,o){if(a===g)throw Error("Generator is already running");if(a===m){if("throw"===i)throw o;return{value:t,done:!0}}for(n.method=i,n.arg=o;;){var c=n.delegate;if(c){var l=j(c,n);if(l){if(l===v)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===d)throw a=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=g;var s=p(e,r,n);if("normal"===s.type){if(a=n.done?m:y,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(a=m,n.method="throw",n.arg=s.arg)}}}function j(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,j(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=p(a,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var o=i.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function T(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function C(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function D(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(T,this),this.reset(!0)}function F(e){if(e||""===e){var r=e[o];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,i=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(u(e)+" is not iterable")}return _.prototype=w,a(L,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:_,configurable:!0}),_.displayName=s(w,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===_||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,s(t,l,"GeneratorFunction")),t.prototype=Object.create(L),t},e.awrap=function(t){return{__await:t}},O(P.prototype),s(P.prototype,c,(function(){return this})),e.AsyncIterator=P,e.async=function(t,r,n,a,i){void 0===i&&(i=Promise);var o=new P(h(t,r,n,a),i);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},O(L),s(L,l,"Generator"),s(L,o,(function(){return this})),s(L,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=F,D.prototype={constructor:D,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(C),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return c.type="throw",c.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],c=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var l=n.call(o,"catchLoc"),s=n.call(o,"finallyLoc");if(l&&s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),C(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;C(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:F(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function h(t,e,r,n,a,i,o){try{var c=t[i](o),l=c.value}catch(t){return void r(t)}c.done?e(l):Promise.resolve(l).then(n,a)}function p(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var i=t.apply(e,r);function o(t){h(i,n,a,o,c,"next",t)}function c(t){h(i,n,a,o,c,"throw",t)}o(void 0)}))}}function d(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function y(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d(Object(r),!0).forEach((function(e){g(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function g(t,e,r){return(e=m(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function m(t){var e=v(t,"string");return"symbol"==u(e)?e:e+""}function v(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var b={name:"FoodSaleRanking",mixins:[c["a"],l["a"]],components:{FoodCategory:s["default"]},data:function(){return{isLoading:!1,tableSetting:[{label:"消费点",key:"org_name"},{label:"一级分类",key:"sort_name"},{label:"销售数量",key:"sell_count"},{label:"销售金额",key:"sell_money",type:"money"},{label:"销售占比",key:"count_share",type:"percent"},{label:"金额占比",key:"sell_share",type:"percent"}],tableData:[],defaultProps:{children:"children",label:"label"},currentPage:1,page:1,pageSize:10,total:0,searchFormSetting:Object(o["f"])(i["SummaryOfSales"]),collect:[{key:"total_sell_fee",value:0,label:"总销售金额:￥",type:"money"},{key:"total_count",value:0,label:"总销售量:",type:""},{key:"text",value:"因涉及消费规则与套餐规则，销售数据仅做参考，销售数据没有减去退款。",label:"",block:!0}],currentTableSetting:[],dialogPrintVisible:!1,printType:"SummaryOfSales",sort:{sort_type:"descending",sort_name:"sell_count"},tabsList:[{type:"instore",name:"堂食"},{type:"reservation",name:"预约"}],currentType:"instore",isFirstSearch:!0,chooseCategory:[]}},mounted:function(){this.currentTableSetting=this.tableSetting},methods:{initLoad:function(){this.initPrintSetting(),this.getSummaryOfSalesList(),this.getFoodSortList()},refreshHandle:function(){this.$refs.searchRef.resetForm(),this.currentPage=1,this.tableData=[],this.isFirstSearch=!0,this.$refs.foodCateGory&&(this.chooseCategory=null,this.$refs.foodCateGory.reset()),this.initLoad()},searchHandle:Object(o["d"])((function(t){t&&"search"===t&&(this.currentPage=1,this.isFirstSearch=!1,this.initLoad())}),300),sortChange:function(t){t.order&&(this.sort={sort_type:t.order?t.order:"descending",sort_name:t.prop},this.page=1,this.pageSize=10,this.getSummaryOfSalesList())},tabHandler:function(t){this.currentType=t.type,this.tableData=[],this.searchHandle()},handleExport:function(){var t=this;this.$confirm("确定导出？","提示",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0}).then((function(e){t.$router.push({name:"Excel",query:{type:"PaymentOrderTotal",params:y(y({},t.formatQueryParams(t.searchFormSetting)),{},{page:t.currentPage,page_size:t.pageSize})}})})).catch((function(t){}))},getSummaryOfSalesList:function(){var t=this;return p(f().mark((function e(){var r;return f().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.$apis.apiBackgroundReportCenterManageReportFoodSortPaymentRankingListPost(y(y(y({},t.formatQueryParams(t.searchFormSetting)),t.sort),{},{payment_order_type:t.currentType,page:t.currentPage,page_size:t.pageSize}));case 3:r=e.sent,t.isLoading=!1,0===r.code?(t.total=r.data.count,t.tableData=r.data.result,t.setCollectData(r)):t.$message.error(r.msg);case 6:case"end":return e.stop()}}),e)})))()},onPaginationChange:function(t){this.currentPage=t.current,this.pageSize=t.pageSize,this.getSummaryOfSalesList()},gotoExport:function(){var t={type:"ExportSummaryOfSalesList",params:y(y(y({payment_order_type:this.currentType},this.sort),this.formatQueryParams(this.searchFormSetting)),{},{page:this.currentPage,page_size:this.pageSize})};this.exportHandle(t)},formatQueryParams:function(t){var e={};for(var r in t)""!==t[r].value&&("select_time"!==r?e[r]=t[r].value:t[r].value&&t[r].value.length>0&&(e.start_date=t[r].value[0],e.end_date=t[r].value[1]));return this.chooseCategory&&this.chooseCategory.length>0&&(e.food_sort_list=this.chooseCategory),e},gotoPrint:function(){var t=Object(o["f"])(this.tableSetting);t.forEach((function(t){t.sortable&&(t.sortable=!0)}));var e=Object(o["f"])(this.currentTableSetting);e.forEach((function(t){t.sortable&&(t.sortable=!0)}));var r=this.$router.resolve({name:"Print",query:{print_date_state:!0,print_type:this.printType,print_title:"菜品分类销售汇总",result_key:"result",api:"apiBackgroundReportCenterManageReportFoodSortPaymentRankingListPost",show_print_header_and_footer:!0,table_setting:JSON.stringify(t),current_table_setting:JSON.stringify(e),collect:JSON.stringify(this.collect),push_summary:!1,params:JSON.stringify(y(y(y({},this.formatQueryParams(this.searchFormSetting)),{},{payment_order_type:this.currentType},this.sort),{},{page:1,page_size:this.total?this.total:10}))}}),n=r.href;window.open(n,"_blank")},changeCategory:function(t){this.chooseCategory=t},resetHandler:function(){this.currentPage=1,this.tableData=[],this.$refs.foodCateGory&&(this.chooseCategory=null,this.$refs.foodCateGory.reset())}}},_=b,w=(r("5662"),r("2877")),S=Object(w["a"])(_,n,a,!1,null,"2cc57b63",null);e["default"]=S.exports},c9d9:function(t,e,r){"use strict";r.d(e,"a",(function(){return i})),r.d(e,"d",(function(){return o})),r.d(e,"b",(function(){return c})),r.d(e,"c",(function(){return l})),r.d(e,"e",(function(){return s})),r.d(e,"f",(function(){return u})),r.d(e,"g",(function(){return f}));var n=r("5a0c"),a=r("da92"),i=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],o=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],c={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},l=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],s=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],u=(n().subtract(7,"day").format("YYYY-MM-DD"),n().format("YYYY-MM-DD"),function(t){return t?"number"===typeof t?a["a"].divide(t,100).toFixed(2):"string"!==typeof t||isNaN(Number(t))?t:a["a"].divide(t,100).toFixed(2):"0.00"}),f=function(t){return a["a"].times(t,100)}},d4e4:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",[e("tree-select",t._g(t._b({attrs:{multiple:!0,options:t.treeList,limit:1,limitText:function(t){return"+"+t},"default-expand-level":1,normalizer:t.normalizer,placeholder:"请选择","no-children-text":"暂无更多",noOptionsText:"暂无分类",noResultsText:"暂无更多","search-nested":"","value-consists-of":"LEAF_PRIORITY",appendToBody:!0},model:{value:t.selectData,callback:function(e){t.selectData=e},expression:"selectData"}},"tree-select",t.$attrs,!1),t.$listeners))],1)},a=[],i=r("ed08"),o={name:"",components:{},props:{onlyFirstList:{type:Boolean,default:!1}},data:function(){return{selectData:[],treeList:[],normalizer:function(t){return{id:t.id,label:t.name,children:t.children}},firstLevelList:[],secondLevelList:[]}},computed:{},watch:{},created:function(){this.getCategory()},mounted:function(){},methods:{getCategory:function(){var t=this,e={page:1,page_size:999999},r=this.$apis.apiBackgroundFoodFoodSortListPost(e),n=this.$apis.apiBackgroundFoodFoodCategoryListPost(e);this.onlyFirstList?r.then((function(e){0===e.code&&(t.firstLevelList=e.data.results),t.treeList=t.arrayToTree(t.firstLevelList,[])})):Promise.all([r,n]).then((function(e){e.forEach((function(e,r){if(0===e.code)switch(r){case 0:t.firstLevelList=e.data.results;break;case 1:t.secondLevelList=e.data.results;break}})),t.treeList=t.arrayToTree(t.firstLevelList,t.secondLevelList)})).catch((function(t){}))},arrayToTree:function(t,e){var r=Object(i["f"])(t);return e.forEach((function(t){for(var e=0;e<r.length;e++){var n=r[e];t.sort==n.id?(n.isDisabled=!1,n.children?n.children.push(t):n.children=[t]):n.children||(n.children=[],n.isDisabled=!0)}})),r},reset:function(){this.selectData=[]}}},c=o,l=(r("894e"),r("2877")),s=Object(l["a"])(c,n,a,!1,null,null,null);e["default"]=s.exports}}]);