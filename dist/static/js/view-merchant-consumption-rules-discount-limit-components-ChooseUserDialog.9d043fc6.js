(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-consumption-rules-discount-limit-components-ChooseUserDialog"],{"5d0d":function(t,e,r){"use strict";r("8fd4")},"8fd4":function(t,e,r){},"978b":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("el-drawer",{staticClass:"ps-el-drawer",attrs:{visible:t.visible,title:t.title,loading:t.isLoading,size:"678px",wrapperClosable:!1},on:{"update:visible":function(e){t.visible=e},"update:loading":function(e){t.isLoading=e},close:t.handleClose}},[e("div",{staticClass:"dialog-content m-t-20 m-l-20"},["default"===t.dialogType?e("el-form",{ref:"cardruleForm",staticClass:"demo-ruleForm",attrs:{"label-width":"50px"}},[e("div",{staticStyle:{display:"flex"}},[e("el-form-item",{attrs:{label:"姓名"}},[e("el-input",{staticClass:"ps-input",attrs:{placeholder:"请输入"},on:{input:t.changePersonNo},model:{value:t.memberOpts.person_name,callback:function(e){t.$set(t.memberOpts,"person_name",e)},expression:"memberOpts.person_name"}})],1),e("el-form-item",{attrs:{label:"分组"}},[e("user-group-select",{staticClass:"search-item-w ps-input w-180",attrs:{multiple:!0,"collapse-tags":!0,placeholder:"请下拉选择"},on:{change:t.handlerChangeGroup},model:{value:t.memberOpts.selectGroup,callback:function(e){t.$set(t.memberOpts,"selectGroup",e)},expression:"memberOpts.selectGroup"}})],1),e("div",{staticClass:"m-l-20"},[e("el-button",{staticClass:"ps-origin-btn",on:{click:t.handlerSearch}},[t._v("搜索")])],1)],1)]):t._e(),e("div",{staticClass:"table-wrap"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"userListRef",attrs:{data:t.memberOpts.tableData,"tooltip-effect":"dark","header-row-class-name":"table-header-row"},on:{"selection-change":t.handleSelectionChange}},[e("el-table-column",{attrs:{"class-name":"ps-checkbox",type:"selection",width:"37",selectable:t.selectableHandle}}),e("el-table-column",{attrs:{prop:"name",label:"姓名",align:"center"}}),e("el-table-column",{attrs:{prop:"person_no",label:"人员编号",align:"center"}}),e("el-table-column",{attrs:{prop:"card_user_group_alias",label:"分组",align:"center"}})],1)],1),e("div",{staticClass:"block ps-pagination"})],1),e("div",{staticClass:"ps-el-drawer-footer m-l-20"},[e("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:t.isBtnLoading},on:{click:t.clickCancleHandle}},[t._v(" 取消 ")]),e("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.isBtnLoading,expression:"isBtnLoading"}],staticClass:"ps-btn",attrs:{type:"primary",disabled:t.isBtnLoading||!t.selectListId||t.selectListId.length<=0},on:{click:t.clickConfirmHandle}},[t._v(" 确定 ")])],1)])},i=[],o=r("ed08"),a=r("390a");function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",l=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function p(t,e,r,n){var o=e&&e.prototype instanceof v?e:v,a=Object.create(o.prototype),s=new I(n||[]);return i(a,"_invoke",{value:E(t,r,s)}),a}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=p;var h="suspendedStart",m="suspendedYield",g="executing",y="completed",b={};function v(){}function L(){}function w(){}var _={};f(_,a,(function(){return this}));var x=Object.getPrototypeOf,O=x&&x(x(T([])));O&&O!==r&&n.call(O,a)&&(_=O);var C=w.prototype=v.prototype=Object.create(_);function k(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function r(i,o,a,c){var l=d(t[i],t,o);if("throw"!==l.type){var u=l.arg,f=u.value;return f&&"object"==s(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,a,c)}),(function(t){r("throw",t,a,c)})):e.resolve(f).then((function(t){u.value=t,a(u)}),(function(t){return r("throw",t,a,c)}))}c(l.arg)}var o;i(this,"_invoke",{value:function(t,n){function i(){return new e((function(e,i){r(t,n,e,i)}))}return o=o?o.then(i,i):i()}})}function E(e,r,n){var i=h;return function(o,a){if(i===g)throw Error("Generator is already running");if(i===y){if("throw"===o)throw a;return{value:t,done:!0}}for(n.method=o,n.arg=a;;){var s=n.delegate;if(s){var c=j(s,n);if(c){if(c===b)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===h)throw i=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=g;var l=d(e,r,n);if("normal"===l.type){if(i=n.done?y:m,l.arg===b)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(i=y,n.method="throw",n.arg=l.arg)}}}function j(e,r){var n=r.method,i=e.iterator[n];if(i===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,j(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),b;var o=d(i,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,b;var a=o.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,b):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,b)}function P(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function $(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(P,this),this.reset(!0)}function T(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function r(){for(;++i<e.length;)if(n.call(e,i))return r.value=e[i],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(s(e)+" is not iterable")}return L.prototype=w,i(C,"constructor",{value:w,configurable:!0}),i(w,"constructor",{value:L,configurable:!0}),L.displayName=f(w,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===L||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,f(t,u,"GeneratorFunction")),t.prototype=Object.create(C),t},e.awrap=function(t){return{__await:t}},k(S.prototype),f(S.prototype,l,(function(){return this})),e.AsyncIterator=S,e.async=function(t,r,n,i,o){void 0===o&&(o=Promise);var a=new S(p(t,r,n,i),o);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},k(C),f(C,u,"Generator"),f(C,a,(function(){return this})),f(C,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=T,I.prototype={constructor:I,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach($),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function i(n,i){return s.type="throw",s.arg=e,r.next=n,i&&(r.method="next",r.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),l=n.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,b):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),b},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),$(r),b}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var i=n.arg;$(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:T(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),b}},e}function l(t,e){return p(t)||f(t,e)||y(t,e)||u()}function u(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,s=[],c=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(s.push(n.value),s.length!==e);c=!0);}catch(t){l=!0,i=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return s}}function p(t){if(Array.isArray(t))return t}function d(t,e,r,n,i,o,a){try{var s=t[o](a),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,i)}function h(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var o=t.apply(e,r);function a(t){d(o,n,i,a,s,"next",t)}function s(t){d(o,n,i,a,s,"throw",t)}a(void 0)}))}}function m(t){return v(t)||b(t)||y(t)||g()}function g(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function y(t,e){if(t){if("string"==typeof t)return L(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?L(t,e):void 0}}function b(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function v(t){if(Array.isArray(t))return L(t)}function L(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var w={name:"chooseUser",props:{loading:Boolean,isshow:Boolean,title:{type:String,default:"选择人员"},limitId:{type:String,default:""},dialogType:{type:String,default:"default"},confirm:Function},components:{UserGroupSelect:a["a"]},data:function(){return{isLoading:!1,groupOpts:{value:"id",label:"group_name",children:"children_list",checkStrictly:!0},memberOpts:{tableData:[],person_name:"",selectGroup:[],departmentList:[]},selectListId:[],personList:[],pageSize:999999,totalCount:0,currentPage:1,isBtnLoading:!1}},computed:{visible:{get:function(){return this.isshow},set:function(t){this.$emit("update:isshow",t)}}},watch:{visible:function(){if(this.visible)if(this.personList&&this.personList.length>0){var t=[];this.personList.forEach((function(e){var r=e.card_user_groups||[];t=t.concat(r)})),t=m(new Set(t)),this.memberOpts.selectGroup=t,this.getCardUserList()}else"limit"===this.dialogType&&this.getCardUserList()}},mounted:function(){},methods:{getCardUserList:function(){var t=this;return h(c().mark((function e(){var r,n,i,a,s,u,f;return c().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.isLoading=!0,r={page_size:t.pageSize,page:t.currentPage},t.memberOpts.selectGroup&&t.memberOpts.selectGroup.length>0&&(r.card_user_group_ids=t.memberOpts.selectGroup),"default"!==t.dialogType?r.discount_limit_id=t.limitId:r.is_self_org=!0,t.memberOpts.person_name&&(r.person_name=t.memberOpts.person_name),"default"!==t.dialogType){e.next=11;break}return e.next=8,Object(o["Z"])(t.$apis.apiCardServiceCardUserListPost(r));case 8:e.t0=e.sent,e.next=14;break;case 11:return e.next=13,Object(o["Z"])(t.$apis.apiBackgroundMarketingDiscountLimitCardInfoChosenListPost(r));case 13:e.t0=e.sent;case 14:if(n=e.t0,i=l(n,2),a=i[0],s=i[1],!a){e.next=21;break}return t.isLoading=!1,e.abrupt("return",t.$message.error("获取人员信息失败"));case 21:if(!s||0!==s.code){e.next=36;break}return u=s.data||{},f=[],"default"===t.dialogType&&(f=f.concat(Object(o["f"])(t.personList))),f=f.concat(u.results),f=Object(o["db"])(f,"id"),e.next=29,t.getChoosePersonList(f);case 29:f=e.sent,t.isLoading=!1,t.totalCount=u.count||0,t.memberOpts.tableData=f,t.memberOpts.tableData=t.memberOpts.tableData.map((function(e){return e.card_user_group_alias=e.card_user_group_alias&&Array.isArray(e.card_user_group_alias)?e.card_user_group_alias.join("，"):e.card_user_group_alias,"default"===t.dialogType?t.personList.forEach((function(r){e.id===r.id&&t.$refs.userListRef&&t.$nextTick((function(){t.$refs.userListRef.toggleRowSelection(e,!0)}))})):t.$nextTick((function(){e.is_enable&&t.$refs.userListRef&&t.$refs.userListRef.toggleRowSelection(e,!0)})),e})),e.next=37;break;case 36:t.$message.error(s.msg);case 37:case"end":return e.stop()}}),e)})))()},handlerChangeGroup:function(t){},changePersonNo:Object(o["d"])((function(){}),300),handleSelectionChange:function(t){var e=this;if(this.selectListId=[],"default"===this.dialogType){var r=Object.freeze(t);r.map((function(t){e.selectListId.push(t.id)}))}else{var n=t.map((function(t){return t.id}));this.memberOpts.tableData.forEach((function(t){n.includes(t.id)||e.selectListId.push(t.id)}))}},clickCancleHandle:function(){this.handleClose()},clickConfirmHandle:function(){var t=this;return h(c().mark((function e(){var r,n,i,a,s;return c().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.selectListId.length){e.next=2;break}return e.abrupt("return",t.$message.error("default"===t.dialogType?"请选择用户":"请反选要取消的人员"));case 2:if("default"!==t.dialogType){e.next=7;break}t.updatePersonList(),t.$emit("confirm",t.personList),e.next=19;break;case 7:return t.isBtnLoading=!0,r={ids:t.selectListId},e.next=11,Object(o["Z"])(t.$apis.apiBackgroundMarketingDiscountLimitCardCancelPost(r));case 11:if(n=e.sent,i=l(n,2),a=i[0],s=i[1],t.isBtnLoading=!1,!a){e.next=18;break}return e.abrupt("return",t.$message.error("取消失败"));case 18:s&&0===s.code?(t.$message.success("取消成功"),t.$emit("confirm",s.data)):t.$message.error(s.msg);case 19:case"end":return e.stop()}}),e)})))()},updatePersonList:function(){var t=this;this.memberOpts.tableData.forEach((function(e){t.selectListId.includes(e.id)&&t.personList.push(e)}))},handleClose:function(t){this.isLoading=!1,this.memberOpts={tableData:[],person_name:"",selectGroup:[]},this.visible=!1,this.$emit("close",!1)},setPersonList:function(t){this.personList=Object(o["f"])(t)},onPaginationChange:function(t){this.currentPage=t.current,this.pageSize=t.pageSize,this.getCardUserList()},handlerSearch:function(){this.updatePersonList(),this.currentPage=1,this.getCardUserList()},selectableHandle:function(t){return"default"===this.dialogType||t.is_enable},getChoosePersonList:function(t){var e=this;return new Promise((function(r){e.$apis.apiBackgroundMarketingDiscountLimitChosenCardInfoIdsPost().then((function(e){if(e&&0===e.code){var n=e.data||{},i=n.card_info_ids||[],o=[];i&&i.forEach((function(t){o=o.concat(t||[])})),t=t.filter((function(t){return!o.includes(t.id)})),r(t)}else r(t)})).catch((function(e){r(t)}))}))}}},_=w,x=(r("5d0d"),r("2877")),O=Object(x["a"])(_,n,i,!1,null,"7d664e62",null);e["default"]=O.exports}}]);