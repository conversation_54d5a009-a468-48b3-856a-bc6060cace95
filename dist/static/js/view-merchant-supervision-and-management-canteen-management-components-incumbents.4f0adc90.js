(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-supervision-and-management-canteen-management-components-incumbents"],{"30d7":function(e,t,r){},a4ef:function(e,t,r){"use strict";r("30d7")},acde:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e._self._c;return t("div",[e._m(0),t("div",{staticClass:"table-content disposition-content"},e._l(e.countData,(function(r,a){return t("div",{key:a},[t("span",[e._v(e._s(r.job_title)+"：")]),t("span",[e._v(e._s(r.count)+"人")])])})),0),t("div",{staticClass:"table-header"},[t("div",{staticClass:"header-left"},[t("div",{staticClass:"header-left-title"},[e._v("在职人员（"+e._s(e.totalCount)+"人）")]),t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_fund_supervision.job_person.add_job_person"],expression:"['background_fund_supervision.job_person.add_job_person']"}],staticClass:"ps-text m-r-10",attrs:{type:"text",size:"small"},on:{click:function(t){return e.showDrawer("add")}}},[e._v("添加")])],1)]),t("div",{staticClass:"table-content"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},e._l(e.tableSetting,(function(r,a){return t("table-column",{key:a,attrs:{col:r},scopedSlots:e._u([{key:"phone",fn:function(r){var a=r.row;return[t("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:a.phone,placement:"top"}},[t("div",[e._v(e._s(e.computedPhone(a.phone)))])])]}},{key:"idNumber",fn:function(r){var a=r.row;return[t("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:a.id_number,placement:"top"}},[t("div",[e._v(e._s(e.computedIdNumber(a.id_number)))])])]}},{key:"recruitType",fn:function(t){var r=t.row;return[e._v(" "+e._s(e.computedRecruitType(r.recruit_type))+" ")]}},{key:"faceImage",fn:function(r){var a=r.row;return[t("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small",disabled:!a.face_image},on:{click:function(t){return e.handleClick("faceImg",a)}}},[e._v("查看")])]}},{key:"healthImage",fn:function(r){var a=r.row;return[t("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small",disabled:!a.health_image},on:{click:function(t){return e.handleClick("healthImg",a)}}},[e._v("查看")])]}},{key:"effectiveTime",fn:function(r){var a=r.row;return[t("div",{class:e.computedTime(a.effective_time)?"red":""},[e._v(e._s(a.effective_time))])]}},{key:"operation",fn:function(r){var a=r.row;return[t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_fund_supervision.job_person.modify_job_person"],expression:"['background_fund_supervision.job_person.modify_job_person']"}],staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.showDrawer("edit",a)}}},[e._v("编辑")]),t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_fund_supervision.job_person.delete_job_person"],expression:"['background_fund_supervision.job_person.delete_job_person']"}],staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.deleteHandle(a)}}},[e._v("删除")])]}}],null,!0)})})),1),t("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[t("el-pagination",{staticClass:"ps-text",attrs:{"current-page":e.page,"page-sizes":[10,20,50,100,500],"page-size":e.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),t("div",{staticClass:"ps-el-drawer"},[t("el-drawer",{attrs:{title:"add"===e.selectType?"添加人员":"编辑人员",visible:e.drawerShow,"show-close":!1,size:"40%"}},[t("div",{staticClass:"p-20"},[t("el-form",{ref:"drawerFormRef",attrs:{model:e.drawerForm,"label-width":"100px","label-position":"right"}},[t("el-form-item",{attrs:{label:"姓名",prop:"name",rules:[{required:!0,message:"请输入姓名",trigger:["change","blur"]}]}},[t("el-input",{staticClass:"w-300",attrs:{placeholder:"请输入姓名，不超过20个字",maxlength:"20"},model:{value:e.drawerForm.name,callback:function(t){e.$set(e.drawerForm,"name",t)},expression:"drawerForm.name"}})],1),t("el-form-item",{attrs:{label:"联系电话",prop:"phone",rules:[{required:!0,message:"请输入联系电话",trigger:["change","blur"]},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:["change","blur"]}]}},[t("el-input",{staticClass:"w-300",attrs:{placeholder:"请输入联系电话，不超过11位",maxlength:"11"},model:{value:e.drawerForm.phone,callback:function(t){e.$set(e.drawerForm,"phone",t)},expression:"drawerForm.phone"}})],1),t("el-form-item",{attrs:{label:"身份证号",prop:"IDcard",rules:[{required:!0,message:"请输入身份证号",trigger:["change","blur"]},{pattern:/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,message:"请输入有效的身份证号",trigger:["change","blur"]}]}},[t("el-input",{staticClass:"w-300",attrs:{placeholder:"请输入身份证号",maxlength:"18"},model:{value:e.drawerForm.IDcard,callback:function(t){e.$set(e.drawerForm,"IDcard",t)},expression:"drawerForm.IDcard"}})],1),t("el-form-item",{attrs:{label:"所属岗位",prop:"post",rules:[{required:!0,message:"请输入岗位信息",trigger:["change","blur"]}]}},[t("el-input",{staticClass:"w-300",attrs:{placeholder:"请输入岗位信息，不超过20个字",maxlength:"20"},model:{value:e.drawerForm.post,callback:function(t){e.$set(e.drawerForm,"post",t)},expression:"drawerForm.post"}})],1),t("el-form-item",{attrs:{label:"用工形式",prop:"form",rules:[{required:!0,message:"请选择用工形式",trigger:["change","blur"]}]}},[t("el-radio-group",{model:{value:e.drawerForm.form,callback:function(t){e.$set(e.drawerForm,"form",t)},expression:"drawerForm.form"}},[t("el-radio",{attrs:{label:"zz"}},[e._v("直招")]),t("el-radio",{attrs:{label:"pq"}},[e._v("派遣")])],1)],1),t("el-form-item",{attrs:{label:"系统账号",prop:"systemAccount"}},[t("el-select",{attrs:{filterable:"",placeholder:"请选择",clearable:""},model:{value:e.drawerForm.systemAccount,callback:function(t){e.$set(e.drawerForm,"systemAccount",t)},expression:"drawerForm.systemAccount"}},e._l(e.accountList,(function(e,r){return t("el-option",{key:r,attrs:{label:e.member_name,value:e.id}})})),1)],1),t("el-form-item",{attrs:{label:"人脸照片",prop:"faceUrl",rules:[{required:!0,message:"请上传有效的人脸照片",trigger:["change","blur"]}]}},[t("div",{staticClass:"certification-info-show-tips",staticStyle:{color:"#C0C4CC"}},[e._v(" 图片最大不超过2MB，仅支持jpg,png格式 ")]),t("div",{staticClass:"flex-b-c"},[t("el-upload",{directives:[{name:"loading",rawName:"v-loading",value:e.uploadingForFace,expression:"uploadingForFace"}],ref:"fileUpload",staticClass:"upload-w",attrs:{"element-loading-text":"上传中",action:e.serverUrl,"file-list":e.fileListsForFace,"on-success":e.uploadSuccessForFace,"before-upload":e.beforeFoodImgUpload,limit:1,multiple:!1,"show-file-list":!1,headers:e.headersOpts,accept:".jpeg,.jpg,.png,.bmp"}},[e.drawerForm.faceUrl?t("img",{staticStyle:{width:"100px",height:"100px"},attrs:{src:e.drawerForm.faceUrl}}):t("div",{staticStyle:{width:"100px",height:"100px",border:"1px dashed #C0C4CC","text-align":"center","line-height":"100px"}},[t("i",{staticClass:"el-icon-plus"})])]),t("div",{staticStyle:{"line-height":"16px","font-size":"14px",color:"#C0C4CC"}},[e._v(" 提示:"),t("br"),e._v(" 1、确保拍摄/上传的图片只存在一张人脸信息;"),t("br"),e._v(" 2、确保拍摄/上传的图片清晰，无模糊;"),t("br"),e._v(" 3、确保拍摄的图片，人脸位于图片正中央位置，且占据图片超过60%的区域;"),t("br"),e._v(" 4、确保拍摄时光线充足;"),t("br"),e._v(" 5、确保照片为正90度;"),t("br"),e._v(" 6、为避免影响人脸功能的正常使用，建议满6个月后更新一次人脸照片。"),t("br")])],1)]),t("el-form-item",{attrs:{label:"健康证件",prop:"imgUrl",rules:[{required:!0,message:"请上传有效证件",trigger:["change","blur"]}]}},[t("div",{staticClass:"certification-info-show-tips",staticStyle:{color:"#C0C4CC"}},[e._v(" 图片最大不超过2MB，仅支持jpg,png格式 ")]),t("el-upload",{directives:[{name:"loading",rawName:"v-loading",value:e.uploadingForImg,expression:"uploadingForImg"}],ref:"fileUpload",staticClass:"upload-w",attrs:{"element-loading-text":"上传中",action:e.serverUrl,"file-list":e.fileListsForImg,"on-success":e.uploadSuccessForImg,"before-upload":e.beforeFoodImgUpload,limit:1,multiple:!1,"show-file-list":!1,headers:e.headersOpts,accept:".jpeg,.jpg,.png,.bmp"}},[e.drawerForm.imgUrl?t("img",{staticStyle:{width:"100px",height:"100px"},attrs:{src:e.drawerForm.imgUrl}}):t("div",{staticStyle:{width:"100px",height:"100px",border:"1px dashed #C0C4CC","text-align":"center","line-height":"100px"}},[t("i",{staticClass:"el-icon-plus"})])])],1),t("el-form-item",{attrs:{label:"证件有效期",prop:"time",rules:[{required:!0,message:"请选择证件的过期日期",trigger:["change","blur"]}]}},[t("el-date-picker",{attrs:{"picker-options":e.pickerOptions,type:"date","value-format":"yyyy-MM-dd",placeholder:"选择日期"},model:{value:e.drawerForm.time,callback:function(t){e.$set(e.drawerForm,"time",t)},expression:"drawerForm.time"}})],1)],1),t("div",{staticClass:"ps-el-drawer-footer"},[t("el-button",{staticClass:"w-100",attrs:{size:"small"},on:{click:e.cancelHandle}},[e._v("取消")]),t("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:e.saveHandle}},[e._v("保存")])],1)],1)])],1),e.showImagePreview?t("el-image-viewer",{staticStyle:{"z-index":"3000"},attrs:{"url-list":e.previewList,"hide-on-click-modal":"",teleported:"","on-close":e.closePreview}}):e._e()],1)},s=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"table-header"},[t("div",{staticClass:"header-left"},[t("div",{staticClass:"header-left-title"},[e._v("人员配比")])])])}],i=r("ed08"),o=r("08a9"),n=r("5a0c"),l=r.n(n),c={components:{ElImageViewer:o["a"]},data:function(){return{isLoading:!1,tableData:[],countData:[],accountList:[],tableSetting:[{label:"姓名",key:"name",showTooltip:!0},{label:"联系电话",key:"phone",type:"slot",slotName:"phone"},{label:"身份证号",key:"id_number",type:"slot",slotName:"idNumber"},{label:"所属岗位",key:"job_title",showTooltip:!0},{label:"用工形式",key:"recruit_type",type:"slot",slotName:"recruitType"},{label:"系统账号",key:"sys_account_alias"},{label:"人脸照片",key:"face_image",type:"slot",slotName:"faceImage"},{label:"健康证",key:"health_image",type:"slot",slotName:"healthImage"},{label:"证件有效期",key:"effective_time",type:"slot",slotName:"effectiveTime"},{label:"修改时间",key:"update_time"},{label:"操作人",key:"operator"},{label:"操作",key:"operation",type:"slot",slotName:"operation",width:"200"}],page:1,pageSize:10,totalCount:0,selectType:"",selectId:"",drawerShow:!1,drawerForm:{name:"",phone:"",IDcard:"",post:"",form:"zz",systemAccount:"",faceUrl:"",imgUrl:"",time:""},uploadingForFace:!1,uploadingForImg:!1,serverUrl:"/api/background/file/upload",uploadParams:{prefix:"incumbents",key:"incumbents"+(new Date).getTime()+Math.floor(150*Math.random())},fileListsForFace:[],fileListsForImg:[],headersOpts:{TOKEN:Object(i["B"])()},pickerOptions:{disabledDate:function(e){return e.getTime()<Date.now()}},importDrawerShow:!1,showImagePreview:!1,previewList:[]}},computed:{computedRecruitType:function(){return function(e){var t="";return t="zz"===e?"直招":"派遣",t}},computedTime:function(){return function(e){var t=l()().diff(l()(e),"day");return!(t<=-30)}},computedPhone:function(){return function(e){return e.replace(/^(\d{3})\d{4}(\d{4})$/,"$1****$2")}},computedIdNumber:function(){return function(e){return e.slice(0,6)+"********"+e.slice(-4)}}},created:function(){this.initLoad()},methods:{initLoad:function(){this.getDataList(),this.getCountData(),this.getAccountList()},uploadSuccessForFace:function(e,t,r){this.uploadingForFace=!1,e&&0===e.code?(this.fileListsForFace=[],this.drawerForm.faceUrl=e.data.public_url):(this.drawerForm.faceUrl="",this.$message.error(e.msg))},uploadSuccessForImg:function(e,t,r){this.uploadingForImg=!1,e&&0===e.code?(this.fileListsForImg=[],this.drawerForm.imgUrl=e.data.public_url):(this.drawerForm.imgUrl="",this.$message.error(e.msg))},beforeFoodImgUpload:function(e){var t=[".jpeg",".jpg",".png",".bmp"],r=e.size/1024/1024<=2;return t.includes(Object(i["A"])(e.name))?r?void(this.uploading=!0):(this.$message.error("上传图片大小不能超过 2MB!"),!1):(this.$message.error("上传图片只能是 JPG 格式或者PNG格式!"),!1)},handleSizeChange:function(e){this.pageSize=e,this.initLoad()},handleCurrentChange:function(e){this.page=e,this.initLoad()},getDataList:function(){var e=this;this.isLoading=!0;var t={page:this.page,page_size:this.pageSize};this.$apis.apiBackgroundFundSupervisionJobPersonJobPersonListPost(t).then((function(t){0===t.code?(e.isLoading=!1,e.tableData=Object(i["f"])(t.data.results||[]),e.totalCount=t.data.count):e.$message.error(t.msg)}))},getCountData:function(){var e=this;this.$apis.apiBackgroundFundSupervisionJobPersonCountJobPersonPost({page:1,page_size:9999}).then((function(t){0===t.code?e.countData=Object(i["f"])(t.data):e.$message.error(t.msg)}))},getAccountList:function(){var e=this;this.$apis.apiBackgroundOrganizationAccountListPost({page:1,page_size:9999,status:1}).then((function(t){0===t.code?e.accountList=Object(i["f"])(t.data.results):e.$message.error(t.msg)}))},handleClick:function(e,t){this.previewList="faceImg"===e?[t.face_image]:[t.health_image],document.body.style.overflow="hidden",this.showImagePreview=!0},closePreview:function(){this.previewList=[],this.showImagePreview=!1,document.body.style.overflow="auto"},showDrawer:function(e,t){var r=this;this.selectType=e,"edit"===e?(this.selectId=t.id,this.drawerForm.phone=t.phone,this.drawerForm.IDcard=t.id_number,this.drawerForm.form=t.recruit_type,this.drawerForm.faceUrl=t.face_image,this.drawerForm.systemAccount=t.sys_account_id,this.drawerForm.imgUrl=t.health_image,this.drawerForm.name=t.name,this.drawerForm.post=t.job_title,this.drawerForm.time=t.effective_time):(this.selectId="",this.drawerForm.name="",this.drawerForm.phone="",this.drawerForm.IDcard="",this.drawerForm.post="",this.drawerForm.form="zz",this.drawerForm.systemAccount="",this.drawerForm.faceUrl="",this.drawerForm.imgUrl="",this.drawerForm.time=""),this.drawerShow=!0,setTimeout((function(){r.$refs.drawerFormRef.clearValidate()}),10)},cancelHandle:function(){this.$refs.drawerFormRef.resetFields(),this.drawerShow=!1},saveHandle:function(){var e=this;this.$refs.drawerFormRef.validate((function(t){if(t){var r={id:"add"===e.selectType?void 0:e.selectId,phone:e.drawerForm.phone?e.drawerForm.phone:void 0,face_image:e.drawerForm.faceUrl,health_image:e.drawerForm.imgUrl,sys_account:e.drawerForm.systemAccount?e.drawerForm.systemAccount:void 0,name:e.drawerForm.name,job_title:e.drawerForm.post,effective_time:e.drawerForm.time,id_number:e.drawerForm.IDcard,recruit_type:e.drawerForm.form?e.drawerForm.form:void 0};"add"===e.selectType?e.addJobPerson(r):e.editJobPerson(r)}else e.$message.error("请确认表单内容填写是否正确")}))},addJobPerson:function(e){var t=this;this.$apis.apiBackgroundFundSupervisionJobPersonAddJobPersonPost(e).then((function(e){0===e.code?(t.$message.success("新增成功"),t.$refs.drawerFormRef.resetFields(),t.drawerShow=!1,t.initLoad()):t.$message.error(e.msg)}))},editJobPerson:function(e){var t=this;this.$apis.apiBackgroundFundSupervisionJobPersonModifyJobPersonPost(e).then((function(e){0===e.code?(t.$message.success("编辑成功"),t.$refs.drawerFormRef.resetFields(),t.drawerShow=!1,t.initLoad()):t.$message.error(e.msg)}))},deleteHandle:function(e){var t=this;this.$confirm("确定要删除 ".concat(e.name," 的人员信息？删除后不可恢复，请谨慎操作。"),"提示",{distinguishCancelAndClose:!0,confirmButtonText:"确定",cancelButtonText:"取消"}).then((function(){t.$apis.apiBackgroundFundSupervisionJobPersonDeleteJobPersonPost({id:e.id}).then((function(e){0===e.code?t.$message.success("删除成功"):t.$message.error(e.msg),t.initLoad()}))})).catch((function(e){t.$message("已取消删除")}))}}},d=c,m=(r("a4ef"),r("2877")),u=Object(m["a"])(d,a,s,!1,null,"65f601f2",null);t["default"]=u.exports}}]);