(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-store-admin-stock-components-StoreStock","view-merchant-meal-management-store-admin-goodsStockDetails","view-merchant-meal-management-store-admin-components-constants","view-merchant-meal-management-store-admin-stock-components-DeductStockDialog"],{"0b20":function(e,t,r){"use strict";r("394f")},"195c":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"DeductStockDialog"},[t("el-dialog",{attrs:{title:"出库",visible:e.visible,top:"20vh","custom-class":"ps-dialog","show-close":"","close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"formData",attrs:{model:e.formData,"label-width":"120px",rules:e.rules}},[t("el-form-item",{attrs:{label:"出库数量",prop:"stock_num"}},[t("el-input",{staticClass:"ps-input w-350",attrs:{onkeyup:"value=value.replace(/[^\\d]/g,'')",maxlength:"4"},model:{value:e.formData.stock_num,callback:function(t){e.$set(e.formData,"stock_num",t)},expression:"formData.stock_num"}})],1),t("el-form-item",{attrs:{label:"出库原因",prop:"type"}},[t("el-select",{staticClass:"ps-select w-350",attrs:{"popper-class":"ps-popper-select",clearable:"",filterable:"",placeholder:"请选择分类"},on:{change:e.handleTypeChange},model:{value:e.formData.type,callback:function(t){e.$set(e.formData,"type",t)},expression:"formData.type"}},e._l(e.typeList,(function(e){return t("el-option",{key:e.name,attrs:{label:e.name,value:e.type}})})),1)],1),t("el-form-item",{attrs:{label:"出库备注",prop:"remark"}},[t("el-input",{staticClass:"ps-input w-350",attrs:{type:"textarea",autosize:{minRows:4,maxRows:8},placeholder:"请输入内容",maxlength:"30","show-word-limit":""},model:{value:e.formData.remark,callback:function(t){e.$set(e.formData,"remark",t)},expression:"formData.remark"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-cancel-btn",on:{click:e.canceDialogHandle}},[e._v("取 消")]),t("el-button",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],staticClass:"ps-btn",attrs:{type:"primary"},on:{click:e.clickDetermineDialog}},[e._v(" 确定 ")])],1)],1)],1)},a=[],o=r("ed08");function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function l(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */l=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},s=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function p(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{p({},"")}catch(e){p=function(e,t,r){return e[t]=r}}function f(e,t,r,n){var o=t&&t.prototype instanceof b?t:b,i=Object.create(o.prototype),l=new P(n||[]);return a(i,"_invoke",{value:j(e,r,l)}),i}function h(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var d="suspendedStart",y="suspendedYield",m="executing",g="completed",v={};function b(){}function w(){}function _(){}var k={};p(k,s,(function(){return this}));var S=Object.getPrototypeOf,L=S&&S(S(N([])));L&&L!==r&&n.call(L,s)&&(k=L);var x=_.prototype=b.prototype=Object.create(k);function O(e){["next","throw","return"].forEach((function(t){p(e,t,(function(e){return this._invoke(t,e)}))}))}function E(e,t){function r(a,o,l,s){var c=h(e[a],e,o);if("throw"!==c.type){var u=c.arg,p=u.value;return p&&"object"==i(p)&&n.call(p,"__await")?t.resolve(p.__await).then((function(e){r("next",e,l,s)}),(function(e){r("throw",e,l,s)})):t.resolve(p).then((function(e){u.value=e,l(u)}),(function(e){return r("throw",e,l,s)}))}s(c.arg)}var o;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){r(e,n,t,a)}))}return o=o?o.then(a,a):a()}})}function j(t,r,n){var a=d;return function(o,i){if(a===m)throw Error("Generator is already running");if(a===g){if("throw"===o)throw i;return{value:e,done:!0}}for(n.method=o,n.arg=i;;){var l=n.delegate;if(l){var s=D(l,n);if(s){if(s===v)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===d)throw a=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=m;var c=h(t,r,n);if("normal"===c.type){if(a=n.done?g:y,c.arg===v)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(a=g,n.method="throw",n.arg=c.arg)}}}function D(t,r){var n=r.method,a=t.iterator[n];if(a===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,D(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var o=h(a,t.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,v;var i=o.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,v):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function P(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function N(t){if(t||""===t){var r=t[s];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,o=function r(){for(;++a<t.length;)if(n.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(i(t)+" is not iterable")}return w.prototype=_,a(x,"constructor",{value:_,configurable:!0}),a(_,"constructor",{value:w,configurable:!0}),w.displayName=p(_,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,p(e,u,"GeneratorFunction")),e.prototype=Object.create(x),e},t.awrap=function(e){return{__await:e}},O(E.prototype),p(E.prototype,c,(function(){return this})),t.AsyncIterator=E,t.async=function(e,r,n,a,o){void 0===o&&(o=Promise);var i=new E(f(e,r,n,a),o);return t.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},O(x),p(x,u,"Generator"),p(x,s,(function(){return this})),p(x,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=N,P.prototype={constructor:P,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(C),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(n,a){return l.type="throw",l.arg=t,r.next=n,a&&(r.method="next",r.arg=e),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],l=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var s=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(s&&c){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),C(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;C(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:N(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),v}},t}function s(e,t){return h(e)||f(e,t)||u(e,t)||c()}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(e,t){if(e){if("string"==typeof e)return p(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?p(e,t):void 0}}function p(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function f(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,o,i,l=[],s=!0,c=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=o.call(r)).done)&&(l.push(n.value),l.length!==t);s=!0);}catch(e){c=!0,a=e}finally{try{if(!s&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(c)throw a}}return l}}function h(e){if(Array.isArray(e))return e}function d(e,t,r,n,a,o,i){try{var l=e[o](i),s=l.value}catch(e){return void r(e)}l.done?t(s):Promise.resolve(s).then(n,a)}function y(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var o=e.apply(t,r);function i(e){d(o,n,a,i,l,"next",e)}function l(e){d(o,n,a,i,l,"throw",e)}i(void 0)}))}}var m={props:{isshow:Boolean,type:{type:String,default:""},selectListId:{type:Array,default:function(){return[]}},confirm:Function},data:function(){return{isLoading:!1,rules:{stock_num:[{required:!0,message:"请输入出库数量",trigger:["blur","change"]}],type:[{required:!0,message:"请选择出库原因",trigger:["blur","change"]}],remark:[{required:!1,message:"请输入出库备注",trigger:["blur","change"]}]},formData:{stock_num:"",type:"",remark:""},typeList:[{name:"销售出库",type:"sale"},{name:"盘点出库",type:"check"},{name:"保质期出库",type:"allot"},{name:"破损出库",type:"breakage"},{name:"其他",type:"other"}]}},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},mounted:function(){},methods:{handleTypeChange:function(){this.rules.remark=[{required:"other"===this.formData.type,message:"请输入出库备注",trigger:["blur","change"]}]},canceDialogHandle:function(){this.visible=!1},clickDetermineDialog:function(){var e=this;this.$refs.formData.validate((function(t){t&&e.getGoodsDeductStock()}))},getGoodsDeductStock:function(){var e=this;return y(l().mark((function t(){var r,n,a,i;return l().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(o["Z"])(e.$apis.apiBackgroundStoreGoodsDeductStockPost({ids:e.selectListId,stock_num:Number(e.formData.stock_num),type:e.formData.type,remark:e.formData.remark}));case 3:if(r=t.sent,n=s(r,2),a=n[0],i=n[1],e.isLoading=!1,!a){t.next=11;break}return e.$message.error(a.message),t.abrupt("return");case 11:0===i.code?(e.visible=!1,e.$emit("deductStockSuccess")):e.$message.error(i.msg);case 12:case"end":return t.stop()}}),t)})))()}}},g=m,v=(r("0b20"),r("2877")),b=Object(v["a"])(g,n,a,!1,null,"57f69a14",null);t["default"]=b.exports},"394f":function(e,t,r){},a94d:function(e,t,r){"use strict";r.r(t),r.d(t,"STORE_GOODS_ADMIN_INFO",(function(){return a})),r.d(t,"STORE_STOCK",(function(){return o})),r.d(t,"CATEGORY_STOCK",(function(){return i})),r.d(t,"CATEGORY_STATISTICS",(function(){return l})),r.d(t,"GOODS_STATISTICS",(function(){return s})),r.d(t,"ADD_STOCK_DETAILS",(function(){return c})),r.d(t,"DEDUCT_STOCK_DETAILS",(function(){return u}));var n=r("ed08"),a={goods_category_ids:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"分类",dataList:[]},barcode:{type:"input",label:"条码",value:"",placeholder:"请输入条码"},name:{type:"input",label:"商品名称",value:"",placeholder:"请输入商品名称"},sale_status:{type:"select",label:"上下架",value:"",placeholder:"请选择类型",dataList:[{label:"全部",value:"2"},{label:"上架",value:"1"},{label:"下架",value:"0"}]},goods_unit:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"单位",dataList:[]},other:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"type",label:"其他",dataList:[{name:"一品多码商品",type:"is_multi_barcode"},{name:"多规格商品",type:"is_multi_spec"}]}},o={goods_category_ids:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"分类",dataList:[]},barcode:{type:"input",label:"条码",value:"",placeholder:"请输入条码"},name:{type:"input",label:"商品名称",value:"",placeholder:"请输入商品名称"},goods_unit:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"单位",dataList:[]}},i={goods_category_ids:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"分类",dataList:[]}},l={select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"创建时间",value:Object(n["t"])(-7),clearable:!1},goods_category_ids:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"分类",dataList:[]}},s={select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"创建时间",value:Object(n["t"])(-7),clearable:!1},goods_category_ids:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"分类",dataList:[]},barcode:{type:"input",label:"条码",value:"",placeholder:"请输入条码"},name:{type:"input",label:"商品名称",value:"",placeholder:"请输入商品名称"}},c={select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"入库时间",value:Object(n["t"])(-7),clearable:!1},goods_category_ids:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"分类",dataList:[]},barcode:{type:"input",label:"条码",value:"",placeholder:"请输入条码"},name:{type:"input",label:"商品名称",value:"",placeholder:"请输入商品名称"},operator_name:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"},operate_type:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"type",label:"入库类型",dataList:[{name:"操作入库",type:"operate"},{name:"退款入库",type:"refund"},{name:"失败订单入库",type:"order_fail"}]},trade_no:{type:"input",label:"订单号",value:"",placeholder:"请输入订单号"}},u={select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"出库时间",value:Object(n["t"])(-7),clearable:!1},goods_category_ids:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"分类",dataList:[]},barcode:{type:"input",label:"条码",value:"",placeholder:"请输入条码"},name:{type:"input",label:"商品名称",value:"",placeholder:"请输入商品名称"},operator_name:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"},operate_type:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"type",label:"出库原因",dataList:[{name:"销售出库",type:"sale"},{name:"盘点出库",type:"check"},{name:"保质期出库",type:"allot"},{name:"破损出库",type:"breakage"},{name:"其他",type:"other"}]},trade_no:{type:"input",label:"订单号",value:"",placeholder:"请输入订单号"}}},cf16:function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"GoodsCategory container-wrapper"},[t("search-form",{ref:"searchRef",attrs:{"form-setting":e.searchFormSetting,autoSearch:!1},on:{search:e.searchHandle}}),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],staticClass:"table-wrapper"},[t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[e._v("数据列表")]),t("div",{staticClass:"align-r"},[t("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_store.goods.deduct_stock"],expression:"['background_store.goods.deduct_stock']"}],attrs:{color:"origin"},on:{click:e.clickDeductStockDialog}},[e._v("出库")]),t("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_store.goods_stock.goods_stock_list_export"],expression:"['background_store.goods_stock.goods_stock_list_export']"}],attrs:{color:"plain",type:"export"},on:{click:e.gotoExport}},[e._v("导出Excel")])],1)]),t("div",{staticClass:"table-content"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","empty-text":e.isFirstSearch?"暂无数据，请查询":"","header-row-class-name":"ps-table-header-row","row-key":"id"},on:{"selection-change":e.handleSelectionChange}},[t("el-table-column",{attrs:{type:"selection",width:"80","reserve-selection":!0,align:"center","class-name":"ps-checkbox"}}),e._l(e.tableSetting,(function(e){return t("table-column",{key:e.key,attrs:{col:e}})}))],2)],1),t("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[t("el-pagination",{staticClass:"ps-text",attrs:{"current-page":e.currentPage,"page-sizes":[10,20,50,100,500],"page-size":e.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)]),e.deductStockDialogVisible?t("deduct-stock-dialog",{attrs:{isshow:e.deductStockDialogVisible,selectListId:e.selectListId},on:{"update:isshow":function(t){e.deductStockDialogVisible=t},confirm:e.searchHandle,deductStockSuccess:e.deductStockSuccess}}):e._e()],1)},a=[],o=r("ed08"),i=r("a94d"),l=r("f63a"),s=r("195c");function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach((function(t){f(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function f(e,t,r){return(t=h(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function h(e){var t=d(e,"string");return"symbol"==c(t)?t:t+""}function d(e,t){if("object"!=c(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function y(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */y=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",l=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function p(e,t,r,n){var o=t&&t.prototype instanceof b?t:b,i=Object.create(o.prototype),l=new P(n||[]);return a(i,"_invoke",{value:j(e,r,l)}),i}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=p;var h="suspendedStart",d="suspendedYield",m="executing",g="completed",v={};function b(){}function w(){}function _(){}var k={};u(k,i,(function(){return this}));var S=Object.getPrototypeOf,L=S&&S(S(N([])));L&&L!==r&&n.call(L,i)&&(k=L);var x=_.prototype=b.prototype=Object.create(k);function O(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function E(e,t){function r(a,o,i,l){var s=f(e[a],e,o);if("throw"!==s.type){var u=s.arg,p=u.value;return p&&"object"==c(p)&&n.call(p,"__await")?t.resolve(p.__await).then((function(e){r("next",e,i,l)}),(function(e){r("throw",e,i,l)})):t.resolve(p).then((function(e){u.value=e,i(u)}),(function(e){return r("throw",e,i,l)}))}l(s.arg)}var o;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){r(e,n,t,a)}))}return o=o?o.then(a,a):a()}})}function j(t,r,n){var a=h;return function(o,i){if(a===m)throw Error("Generator is already running");if(a===g){if("throw"===o)throw i;return{value:e,done:!0}}for(n.method=o,n.arg=i;;){var l=n.delegate;if(l){var s=D(l,n);if(s){if(s===v)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===h)throw a=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=m;var c=f(t,r,n);if("normal"===c.type){if(a=n.done?g:d,c.arg===v)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(a=g,n.method="throw",n.arg=c.arg)}}}function D(t,r){var n=r.method,a=t.iterator[n];if(a===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,D(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var o=f(a,t.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,v;var i=o.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,v):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function P(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function N(t){if(t||""===t){var r=t[i];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,o=function r(){for(;++a<t.length;)if(n.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(c(t)+" is not iterable")}return w.prototype=_,a(x,"constructor",{value:_,configurable:!0}),a(_,"constructor",{value:w,configurable:!0}),w.displayName=u(_,s,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,u(e,s,"GeneratorFunction")),e.prototype=Object.create(x),e},t.awrap=function(e){return{__await:e}},O(E.prototype),u(E.prototype,l,(function(){return this})),t.AsyncIterator=E,t.async=function(e,r,n,a,o){void 0===o&&(o=Promise);var i=new E(p(e,r,n,a),o);return t.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},O(x),u(x,s,"Generator"),u(x,i,(function(){return this})),u(x,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=N,P.prototype={constructor:P,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(C),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(n,a){return l.type="throw",l.arg=t,r.next=n,a&&(r.method="next",r.arg=e),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],l=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var s=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(s&&c){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),C(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;C(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:N(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),v}},t}function m(e,t){return _(e)||w(e,t)||v(e,t)||g()}function g(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function v(e,t){if(e){if("string"==typeof e)return b(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?b(e,t):void 0}}function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function w(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,o,i,l=[],s=!0,c=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=o.call(r)).done)&&(l.push(n.value),l.length!==t);s=!0);}catch(e){c=!0,a=e}finally{try{if(!s&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(c)throw a}}return l}}function _(e){if(Array.isArray(e))return e}function k(e,t,r,n,a,o,i){try{var l=e[o](i),s=l.value}catch(e){return void r(e)}l.done?t(s):Promise.resolve(s).then(n,a)}function S(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var o=e.apply(t,r);function i(e){k(o,n,a,i,l,"next",e)}function l(e){k(o,n,a,i,l,"throw",e)}i(void 0)}))}}var L={name:"StoreStock",mixins:[l["a"]],components:{DeductStockDialog:s["default"]},data:function(){return{isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[],tableSetting:[{label:"序号",key:"index",type:"index",width:"80"},{label:"名称",key:"name"},{label:"条码",key:"barcode"},{label:"规格",key:"spec"},{label:"单位",key:"goods_unit_name"},{label:"分类",key:"goods_category_name"},{label:"库存",key:"stock_num"},{label:"销售价",key:"sales_price",type:"money"},{label:"成本价",key:"cost_price",type:"money"},{label:"供应商",key:"supplier_name"}],searchFormSetting:i["STORE_STOCK"],deductStockDialogVisible:!1,selectListId:[],isFirstSearch:!1}},created:function(){this.getApiStoreGoodsUnitList(),this.getGoodsCategoryList(),this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getGoodsStockList()},searchHandle:Object(o["d"])((function(e){e&&"search"===e&&(this.currentPage=1,this.isFirstSearch=!1,this.getGoodsStockList())}),300),getApiStoreGoodsUnitList:function(){var e=this;return S(y().mark((function t(){var r,n,a,i;return y().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(o["Z"])(e.$apis.apiBackgroundStoreGoodsUnitListPost({page:1,page_size:99999}));case 2:if(r=t.sent,n=m(r,2),a=n[0],i=n[1],!a){t.next=9;break}return e.$message.error(a.message),t.abrupt("return");case 9:0===i.code?e.searchFormSetting.goods_unit.dataList=i.data.results:e.$message.error(i.msg);case 10:case"end":return t.stop()}}),t)})))()},getGoodsCategoryList:function(){var e=this;return S(y().mark((function t(){var r,n,a,i;return y().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(o["Z"])(e.$apis.apiBackgroundStoreGoodsCategoryListPost({page:1,page_size:99999}));case 2:if(r=t.sent,n=m(r,2),a=n[0],i=n[1],!a){t.next=9;break}return e.$message.error(a.message),t.abrupt("return");case 9:0===i.code?e.searchFormSetting.goods_category_ids.dataList=i.data.results:e.$message.error(i.msg);case 10:case"end":return t.stop()}}),t)})))()},getGoodsStockList:function(){var e=this;return S(y().mark((function t(){var r,n,a,i;return y().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(o["Z"])(e.$apis.apiBackgroundStoreGoodsStockGoodsStockListPost(p(p({},e.formatQueryParams(e.searchFormSetting)),{},{page:e.currentPage,page_size:e.pageSize})));case 3:if(r=t.sent,n=m(r,2),a=n[0],i=n[1],e.isLoading=!1,!a){t.next=11;break}return e.$message.error(a.message),t.abrupt("return");case 11:0===i.code?(e.totalCount=i.data.count,e.tableData=i.data.results,e.isFirstSearch=!1):e.$message.error(i.msg);case 12:case"end":return t.stop()}}),t)})))()},formatQueryParams:function(e){var t={};for(var r in e)""!==e[r].value&&null!==e[r].value&&("select_time"!==r?t[r]=e[r].value:e[r].value.length>0&&(t.start_time=e[r].value[0],t.end_time=e[r].value[1]));return t},clickDeductStockDialog:function(){if(!this.selectListId.length)return this.$message.error("请选择数据");this.deductStockDialogVisible=!0},handleSelectionChange:function(e){var t=this;this.selectListId=[];var r=Object.freeze(e);r.map((function(e){t.selectListId.push(e.id)}))},deductStockSuccess:function(){this.$refs.tableData.clearSelection(),this.searchHandle()},handleSizeChange:function(e){this.pageSize=e,this.getGoodsStockList()},handleCurrentChange:function(e){this.currentPage=e,this.getGoodsStockList()},gotoExport:function(){var e={type:"ExportStoreStock",params:p(p({},this.formatQueryParams(this.searchFormSetting)),{},{page:this.currentPage,page_size:this.pageSize})};this.exportHandle(e)}}},x=L,O=r("2877"),E=Object(O["a"])(x,n,a,!1,null,"878624e4",null);t["default"]=E.exports}}]);