(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-store-admin-admin-components-GoodsInfo","view-merchant-meal-management-store-admin-goodsStockDetails","view-merchant-meal-management-store-admin-admin-components-GoodsInfo-StockDataSuccessDialog","view-merchant-meal-management-store-admin-components-constants"],{"4f42":function(e,t,a){},"5dd2":function(e,t,a){},9683:function(e,t,a){"use strict";a("5dd2")},a94d:function(e,t,a){"use strict";a.r(t),a.d(t,"STORE_GOODS_ADMIN_INFO",(function(){return r})),a.d(t,"STORE_STOCK",(function(){return i})),a.d(t,"CATEGORY_STOCK",(function(){return n})),a.d(t,"CATEGORY_STATISTICS",(function(){return s})),a.d(t,"GOODS_STATISTICS",(function(){return l})),a.d(t,"ADD_STOCK_DETAILS",(function(){return c})),a.d(t,"DEDUCT_STOCK_DETAILS",(function(){return u}));var o=a("ed08"),r={goods_category_ids:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"分类",dataList:[]},barcode:{type:"input",label:"条码",value:"",placeholder:"请输入条码"},name:{type:"input",label:"商品名称",value:"",placeholder:"请输入商品名称"},sale_status:{type:"select",label:"上下架",value:"",placeholder:"请选择类型",dataList:[{label:"全部",value:"2"},{label:"上架",value:"1"},{label:"下架",value:"0"}]},goods_unit:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"单位",dataList:[]},other:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"type",label:"其他",dataList:[{name:"一品多码商品",type:"is_multi_barcode"},{name:"多规格商品",type:"is_multi_spec"}]}},i={goods_category_ids:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"分类",dataList:[]},barcode:{type:"input",label:"条码",value:"",placeholder:"请输入条码"},name:{type:"input",label:"商品名称",value:"",placeholder:"请输入商品名称"},goods_unit:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"单位",dataList:[]}},n={goods_category_ids:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"分类",dataList:[]}},s={select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"创建时间",value:Object(o["t"])(-7),clearable:!1},goods_category_ids:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"分类",dataList:[]}},l={select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"创建时间",value:Object(o["t"])(-7),clearable:!1},goods_category_ids:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"分类",dataList:[]},barcode:{type:"input",label:"条码",value:"",placeholder:"请输入条码"},name:{type:"input",label:"商品名称",value:"",placeholder:"请输入商品名称"}},c={select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"入库时间",value:Object(o["t"])(-7),clearable:!1},goods_category_ids:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"分类",dataList:[]},barcode:{type:"input",label:"条码",value:"",placeholder:"请输入条码"},name:{type:"input",label:"商品名称",value:"",placeholder:"请输入商品名称"},operator_name:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"},operate_type:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"type",label:"入库类型",dataList:[{name:"操作入库",type:"operate"},{name:"退款入库",type:"refund"},{name:"失败订单入库",type:"order_fail"}]},trade_no:{type:"input",label:"订单号",value:"",placeholder:"请输入订单号"}},u={select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"出库时间",value:Object(o["t"])(-7),clearable:!1},goods_category_ids:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"分类",dataList:[]},barcode:{type:"input",label:"条码",value:"",placeholder:"请输入条码"},name:{type:"input",label:"商品名称",value:"",placeholder:"请输入商品名称"},operator_name:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"},operate_type:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"type",label:"出库原因",dataList:[{name:"销售出库",type:"sale"},{name:"盘点出库",type:"check"},{name:"保质期出库",type:"allot"},{name:"破损出库",type:"breakage"},{name:"其他",type:"other"}]},trade_no:{type:"input",label:"订单号",value:"",placeholder:"请输入订单号"}}},aba2:function(e,t,a){"use strict";a("4f42")},e265:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"StockDataSuccessDialog"},[t("el-dialog",{attrs:{title:"入库成功",visible:e.visible,top:"20vh","custom-class":"ps-dialog","show-close":"","close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t}}},[t("el-table",{staticClass:"ps-table",staticStyle:{width:"100%"},attrs:{border:"",data:e.stockListData.slice((e.currentPage-1)*e.pageSize,e.currentPage*e.pageSize),"header-row-class-name":"ps-table-header-row"}},[t("el-table-column",{attrs:{prop:"index",label:"序号",width:"70",align:"center"}}),t("el-table-column",{attrs:{prop:"name",label:"商品名称",align:"center"}}),t("el-table-column",{attrs:{prop:"spec",label:"规格",align:"center"}}),t("el-table-column",{attrs:{prop:"barcode",label:"条码",align:"center",width:"180"}}),t("el-table-column",{attrs:{prop:"before_stock_num",label:"原库存",align:"center"}}),t("el-table-column",{attrs:{prop:"add_stock_num",label:"当前入库数量",align:"center"}}),t("el-table-column",{attrs:{prop:"stock_num",label:"现库存",align:"center"}})],1),t("div",{staticClass:"pageSizeItem ps-pagination"},[t("el-pagination",{staticClass:"ps-text",attrs:{"current-page":e.currentPage,"page-size":e.pageSize,layout:"prev, pager, next, total, jumper",total:e.stockListData.length,background:"","popper-class":"ps-popper-select"},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange,"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t}}})],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-cancel-btn",on:{click:e.canceDialogHandle}},[e._v("取 消")])],1)],1)],1)},r=[],i={props:{isshow:Boolean,type:{type:String,default:""},stockListData:{type:Array,default:function(){return[]}}},data:function(){return{currentPage:1,pageSize:10}},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},mounted:function(){},methods:{canceDialogHandle:function(){this.visible=!1},handleSizeChange:function(e){this.pageSize=e},handleCurrentChange:function(e){this.currentPage=e}}},n=i,s=(a("aba2"),a("2877")),l=Object(s["a"])(n,o,r,!1,null,"44d69253",null);t["default"]=l.exports},f810:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"GoodsInfo container-wrapper"},[t("search-form",{ref:"searchRef",attrs:{"form-setting":e.searchFormSetting,autoSearch:!1},on:{search:e.searchHandle}}),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],staticClass:"table-wrapper"},[t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[e._v("数据列表")]),t("div",{staticClass:"align-r"},[t("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_store.goods.add_stock"],expression:"['background_store.goods.add_stock']"}],attrs:{color:"origin"},on:{click:e.clickDeviceGoodsWarehousingDialog}},[e._v(" 商品入库 ")]),t("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_store.goods.add"],expression:"['background_store.goods.add']"}],attrs:{color:"origin",type:"add"},on:{click:function(t){return e.addOrEditGoods("add")}}},[e._v("新增")]),t("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_store.goods.batch_add"],expression:"['background_store.goods.batch_add']"}],attrs:{color:"origin",type:"add"},on:{click:function(t){e.batchAddGoodsDialogVisible=!0}}},[e._v(" 批量新增 ")]),t("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_store.goods.batch_import_goods"],expression:"['background_store.goods.batch_import_goods']"}],staticClass:"but-import",attrs:{color:"plain"},on:{click:function(t){e.importGoodsDialog=!0}}},[e._v(" 导入新增 ")]),t("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_store.goods.goods_list_export"],expression:"['background_store.goods.goods_list_export']"}],attrs:{color:"plain",type:"export"},on:{click:e.gotoExport}},[e._v("导出Excel")])],1)]),t("div",{staticClass:"table-content"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticClass:"table-wrapper",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","row-key":"id","empty-text":e.isFirstSearch?"暂无数据，请查询":"","header-row-class-name":"ps-table-header-row","default-expand-all":"","tree-props":{children:"children",hasChildren:"hasChildren"}}},e._l(e.tableSetting,(function(a){return t("table-column",{key:a.key,attrs:{col:a},scopedSlots:e._u([{key:"goods_image",fn:function(e){var a=e.row;return[t("el-image",{staticStyle:{width:"100px",height:"100px"},attrs:{src:a.goods_image?a.goods_image:"https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png",fit:"cover","preview-src-list":[a.goods_image?a.goods_image:"https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png"]}})]}},{key:"sale_status",fn:function(a){var o=a.row;return[t("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2","active-value":1,"inactive-value":0,disabled:!e.allPermissions.includes("background_store.goods.sale_status_modify")},on:{change:function(t){return e.changeSaleStatus(o)}},model:{value:o.sale_status,callback:function(t){e.$set(o,"sale_status",t)},expression:"row.sale_status"}})]}},{key:"operation",fn:function(a){var o=a.row;return["0"===o.barcode_type?t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_store.goods.modify"],expression:"['background_store.goods.modify']"}],attrs:{type:"text",size:"small"},on:{click:function(t){return e.addOrEditGoods("modify",o)}}},[e._v(" 编辑 ")]):e._e(),t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_store.goods.delete"],expression:"['background_store.goods.delete']"}],staticClass:"ps-red",attrs:{type:"text",size:"small",disabled:o.stock_num>0},on:{click:function(t){return e.clickDelete(o)}}},[e._v(" 删除 ")])]}}],null,!0)})})),1)],1),t("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[t("el-pagination",{staticClass:"ps-text",attrs:{"current-page":e.currentPage,"page-sizes":[10,20,50,100,500],"page-size":e.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)]),e.deviceGoodsWarehousingDialogVisible?t("goods-warehousing-dialog",{attrs:{isshow:e.deviceGoodsWarehousingDialogVisible},on:{"update:isshow":function(t){e.deviceGoodsWarehousingDialogVisible=t},determineStock:e.determineStock}}):e._e(),e.stockDataSuccessDialogVisible?t("stock-data-success-dialog",{attrs:{isshow:e.stockDataSuccessDialogVisible,stockListData:e.stockListData},on:{"update:isshow":function(t){e.stockDataSuccessDialogVisible=t},determineStock:e.determineStock}}):e._e(),e.deviceGoodsVisible?t("add-goods-dialog",{attrs:{isshow:e.deviceGoodsVisible,dialogTitle:e.deviceGoodsDialogTitle,dialogModifyId:e.dialogModifyId,type:e.type},on:{"update:isshow":function(t){e.deviceGoodsVisible=t},confirm:e.searchHandle}}):e._e(),e.batchAddGoodsDialogVisible?t("batch-add-goods-dialog",{attrs:{isshow:e.batchAddGoodsDialogVisible,goodsCategoryList:e.goodsCategoryList,supplierList:e.supplierList,unitList:e.unitList},on:{"update:isshow":function(t){e.batchAddGoodsDialogVisible=t},confirm:e.searchHandle}}):e._e(),e.importGoodsDialog?t("el-dialog",{attrs:{title:"导入新增",visible:e.importGoodsDialog,width:"700px",top:"10vh","custom-class":"ps-dialog","show-close":"","close-on-click-modal":!1},on:{"update:visible":function(t){e.importGoodsDialog=t}}},[t("div",[t("el-form",{staticClass:"import-face-form-wrapper",attrs:{"label-width":"140px"}},[t("el-form-item",{attrs:{label:"商品导入模版"}},[t("el-link",{attrs:{type:"primary",href:e.templateUrl}},[e._v(" 点击下载 ")])],1),t("el-form-item",{attrs:{label:"上传商品"}},[t("file-upload",{attrs:{drag:"",limit:1,"before-upload":e.beforeUpload,prefix:"goods_url_zip","on-remove":e.remove},on:{fileLists:e.getSuccessUploadRes}},[t("div",{},[t("i",{staticClass:"el-icon-upload"}),t("div",{staticClass:"el-upload__text"},[e._v(" 将文件拖到此处，或 "),t("em",[e._v("点击上传")])])]),t("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("只能上传zip文件")])])],1)],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-cancel-btn",on:{click:function(t){e.importGoodsDialog=!1}}},[e._v("取 消")]),t("el-button",{staticClass:"ps-btn",attrs:{type:"primary"},on:{click:e.importGoodsDetermine}},[e._v(" 确定 ")])],1)]):e._e()],1)},r=[],i=a("ed08"),n=a("a94d"),s=a("27b6"),l=a("e265"),c=a("0e415"),u=a("2f62"),d=a("cb78"),p=a("f63a");function g(e){return g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},g(e)}function m(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */m=function(){return t};var e,t={},a=Object.prototype,o=a.hasOwnProperty,r=Object.defineProperty||function(e,t,a){e[t]=a.value},i="function"==typeof Symbol?Symbol:{},n=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function c(e,t,a){return Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,a){return e[t]=a}}function u(e,t,a,o){var i=t&&t.prototype instanceof v?t:v,n=Object.create(i.prototype),s=new T(o||[]);return r(n,"_invoke",{value:O(e,a,s)}),n}function d(e,t,a){try{return{type:"normal",arg:e.call(t,a)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var p="suspendedStart",f="suspendedYield",h="executing",y="completed",b={};function v(){}function _(){}function w(){}var k={};c(k,n,(function(){return this}));var S=Object.getPrototypeOf,L=S&&S(S(z([])));L&&L!==a&&o.call(L,n)&&(k=L);var x=w.prototype=v.prototype=Object.create(k);function D(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function G(e,t){function a(r,i,n,s){var l=d(e[r],e,i);if("throw"!==l.type){var c=l.arg,u=c.value;return u&&"object"==g(u)&&o.call(u,"__await")?t.resolve(u.__await).then((function(e){a("next",e,n,s)}),(function(e){a("throw",e,n,s)})):t.resolve(u).then((function(e){c.value=e,n(c)}),(function(e){return a("throw",e,n,s)}))}s(l.arg)}var i;r(this,"_invoke",{value:function(e,o){function r(){return new t((function(t,r){a(e,o,t,r)}))}return i=i?i.then(r,r):r()}})}function O(t,a,o){var r=p;return function(i,n){if(r===h)throw Error("Generator is already running");if(r===y){if("throw"===i)throw n;return{value:e,done:!0}}for(o.method=i,o.arg=n;;){var s=o.delegate;if(s){var l=C(s,o);if(l){if(l===b)continue;return l}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(r===p)throw r=y,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);r=h;var c=d(t,a,o);if("normal"===c.type){if(r=o.done?y:f,c.arg===b)continue;return{value:c.arg,done:o.done}}"throw"===c.type&&(r=y,o.method="throw",o.arg=c.arg)}}}function C(t,a){var o=a.method,r=t.iterator[o];if(r===e)return a.delegate=null,"throw"===o&&t.iterator.return&&(a.method="return",a.arg=e,C(t,a),"throw"===a.method)||"return"!==o&&(a.method="throw",a.arg=new TypeError("The iterator does not provide a '"+o+"' method")),b;var i=d(r,t.iterator,a.arg);if("throw"===i.type)return a.method="throw",a.arg=i.arg,a.delegate=null,b;var n=i.arg;return n?n.done?(a[t.resultName]=n.value,a.next=t.nextLoc,"return"!==a.method&&(a.method="next",a.arg=e),a.delegate=null,b):n:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,b)}function P(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function j(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function T(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(P,this),this.reset(!0)}function z(t){if(t||""===t){var a=t[n];if(a)return a.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,i=function a(){for(;++r<t.length;)if(o.call(t,r))return a.value=t[r],a.done=!1,a;return a.value=e,a.done=!0,a};return i.next=i}}throw new TypeError(g(t)+" is not iterable")}return _.prototype=w,r(x,"constructor",{value:w,configurable:!0}),r(w,"constructor",{value:_,configurable:!0}),_.displayName=c(w,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===_||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,c(e,l,"GeneratorFunction")),e.prototype=Object.create(x),e},t.awrap=function(e){return{__await:e}},D(G.prototype),c(G.prototype,s,(function(){return this})),t.AsyncIterator=G,t.async=function(e,a,o,r,i){void 0===i&&(i=Promise);var n=new G(u(e,a,o,r),i);return t.isGeneratorFunction(a)?n:n.next().then((function(e){return e.done?e.value:n.next()}))},D(x),c(x,l,"Generator"),c(x,n,(function(){return this})),c(x,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),a=[];for(var o in t)a.push(o);return a.reverse(),function e(){for(;a.length;){var o=a.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},t.values=z,T.prototype={constructor:T,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(j),!t)for(var a in this)"t"===a.charAt(0)&&o.call(this,a)&&!isNaN(+a.slice(1))&&(this[a]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var a=this;function r(o,r){return s.type="throw",s.arg=t,a.next=o,r&&(a.method="next",a.arg=e),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var n=this.tryEntries[i],s=n.completion;if("root"===n.tryLoc)return r("end");if(n.tryLoc<=this.prev){var l=o.call(n,"catchLoc"),c=o.call(n,"finallyLoc");if(l&&c){if(this.prev<n.catchLoc)return r(n.catchLoc,!0);if(this.prev<n.finallyLoc)return r(n.finallyLoc)}else if(l){if(this.prev<n.catchLoc)return r(n.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<n.finallyLoc)return r(n.finallyLoc)}}}},abrupt:function(e,t){for(var a=this.tryEntries.length-1;a>=0;--a){var r=this.tryEntries[a];if(r.tryLoc<=this.prev&&o.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var n=i?i.completion:{};return n.type=e,n.arg=t,i?(this.method="next",this.next=i.finallyLoc,b):this.complete(n)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),b},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.finallyLoc===e)return this.complete(a.completion,a.afterLoc),j(a),b}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.tryLoc===e){var o=a.completion;if("throw"===o.type){var r=o.arg;j(a)}return r}}throw Error("illegal catch attempt")},delegateYield:function(t,a,o){return this.delegate={iterator:z(t),resultName:a,nextLoc:o},"next"===this.method&&(this.arg=e),b}},t}function f(e,t){return _(e)||v(e,t)||y(e,t)||h()}function h(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function y(e,t){if(e){if("string"==typeof e)return b(e,t);var a={}.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?b(e,t):void 0}}function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,o=Array(t);a<t;a++)o[a]=e[a];return o}function v(e,t){var a=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=a){var o,r,i,n,s=[],l=!0,c=!1;try{if(i=(a=a.call(e)).next,0===t){if(Object(a)!==a)return;l=!1}else for(;!(l=(o=i.call(a)).done)&&(s.push(o.value),s.length!==t);l=!0);}catch(e){c=!0,r=e}finally{try{if(!l&&null!=a.return&&(n=a.return(),Object(n)!==n))return}finally{if(c)throw r}}return s}}function _(e){if(Array.isArray(e))return e}function w(e,t,a,o,r,i,n){try{var s=e[i](n),l=s.value}catch(e){return void a(e)}s.done?t(l):Promise.resolve(l).then(o,r)}function k(e){return function(){var t=this,a=arguments;return new Promise((function(o,r){var i=e.apply(t,a);function n(e){w(i,o,r,n,s,"next",e)}function s(e){w(i,o,r,n,s,"throw",e)}n(void 0)}))}}function S(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,o)}return a}function L(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?S(Object(a),!0).forEach((function(t){x(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):S(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function x(e,t,a){return(t=D(t))in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function D(e){var t=G(e,"string");return"symbol"==g(t)?t:t+""}function G(e,t){if("object"!=g(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var o=a.call(e,t||"default");if("object"!=g(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var O={name:"goodsInfo",mixins:[p["a"]],components:{GoodsWarehousingDialog:s["default"],StockDataSuccessDialog:l["default"],AddGoodsDialog:d["default"],BatchAddGoodsDialog:c["default"]},computed:L({},Object(u["c"])(["appPermissions"])),data:function(){return{isLoading:!1,totalPageSize:0,pageSize:10,totalCount:0,currentPage:1,tableData:[],tableSetting:[{label:"序号",type:"index",width:"80"},{label:"商品名称",key:"name"},{label:"图片",key:"goods_image",type:"slot",slotName:"goods_image",width:"160"},{label:"条码",key:"barcode"},{label:"规格",key:"spec"},{label:"分类",key:"goods_category_name"},{label:"供应商",key:"supplier_name"},{label:"库存",key:"stock_num"},{label:"单位",key:"goods_unit_name"},{label:"销售价",key:"sales_price",type:"money"},{label:"成本价",key:"cost_price",type:"money"},{label:"创建时间",key:"create_time"},{label:"上下架",key:"sale_status",type:"slot",slotName:"sale_status"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"220"}],searchFormSetting:n["STORE_GOODS_ADMIN_INFO"],deviceGoodsWarehousingDialogVisible:!1,stockDataSuccessDialogVisible:!1,stockListData:[],deviceGoodsVisible:!1,type:"",deviceGoodsDialogTitle:"",dialogModifyId:-1,goodsCategoryList:[],supplierList:[],unitList:[],batchAddGoodsDialogVisible:!1,isFirstSearch:!1,importGoodsDialog:!1,goodsUploadUrl:"",templateUrl:location.origin+"/api/temporary/template_excel/food_stock/导入商品模板.zip"}},created:function(){this.getFoodIngredientSupplierList(),this.getGoodsCategoryList(),this.getApiStoreGoodsUnitList(),this.getGoodsUnitList(),this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getGoodsList()},searchHandle:Object(i["d"])((function(e){e&&"search"===e&&(this.currentPage=1,this.isFirstSearch=!1,this.getGoodsList())}),300),getGoodsUnitList:function(){var e=this;return k(m().mark((function t(){var a,o,r,n;return m().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(i["Z"])(e.$apis.apiBackgroundStoreGoodsUnitListPost({page:1,page_size:99999}));case 3:if(a=t.sent,o=f(a,2),r=o[0],n=o[1],e.isLoading=!1,!r){t.next=11;break}return e.$message.error(r.message),t.abrupt("return");case 11:0===n.code?e.unitList=n.data.results:e.$message.error(n.msg);case 12:case"end":return t.stop()}}),t)})))()},getFoodIngredientSupplierList:function(){var e=this;return k(m().mark((function t(){var a;return m().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,e.$apis.apiBackgroundFoodIngredientSupplierListPost({page:1,page_size:99999});case 3:a=t.sent,e.isLoading=!1,0===a.code?e.supplierList=a.data.results:e.$message.error(a.msg);case 6:case"end":return t.stop()}}),t)})))()},getGoodsCategoryList:function(){var e=this;return k(m().mark((function t(){var a,o,r,n;return m().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(i["Z"])(e.$apis.apiBackgroundStoreGoodsCategoryListPost({page:1,page_size:99999}));case 2:if(a=t.sent,o=f(a,2),r=o[0],n=o[1],!r){t.next=9;break}return e.$message.error(r.message),t.abrupt("return");case 9:0===n.code?(e.searchFormSetting.goods_category_ids.dataList=n.data.results,e.goodsCategoryList=n.data.results):e.$message.error(n.msg);case 10:case"end":return t.stop()}}),t)})))()},getApiStoreGoodsUnitList:function(){var e=this;return k(m().mark((function t(){var a,o,r,n;return m().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(i["Z"])(e.$apis.apiBackgroundStoreGoodsUnitListPost({page:1,page_size:99999}));case 2:if(a=t.sent,o=f(a,2),r=o[0],n=o[1],!r){t.next=9;break}return e.$message.error(r.message),t.abrupt("return");case 9:0===n.code?e.searchFormSetting.goods_unit.dataList=n.data.results:e.$message.error(n.msg);case 10:case"end":return t.stop()}}),t)})))()},setStatusModify:function(e){var t=this;return k(m().mark((function a(){var o,r,n,s;return m().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,Object(i["Z"])(t.$apis.apiBackgroundStoreGoodsSaleStatusModifyPost(e));case 2:if(o=a.sent,r=f(o,2),n=r[0],s=r[1],!n){a.next=9;break}return t.$message.error(n.message),a.abrupt("return");case 9:0===s.code?t.getGoodsList():t.$message.error(s.msg);case 10:case"end":return a.stop()}}),a)})))()},getGoodsList:function(){var e=this;return k(m().mark((function t(){var a,o,r,n;return m().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(i["Z"])(e.$apis.apiBackgroundStoreGoodsListPost(L(L({},e.formatQueryParams(e.searchFormSetting)),{},{page:e.currentPage,page_size:e.pageSize})));case 3:if(a=t.sent,o=f(a,2),r=o[0],n=o[1],e.isLoading=!1,!r){t.next=11;break}return e.$message.error(r.message),t.abrupt("return");case 11:0===n.code?(e.totalCount=n.data.count,e.tableData=n.data.tree_list,e.totalPageSize=e.$computedTotalPageSize(e.totalCount,e.pageSize),e.isFirstSearch=!1):e.$message.error(n.msg);case 12:case"end":return t.stop()}}),t)})))()},hasChildren:function(e){},changeSaleStatus:function(e){var t={id:e.id,sale_status:e.sale_status};this.setStatusModify(t)},clickDelete:function(e){var t=this;this.$confirm("确定删除该条商品信息？","提示",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var a=k(m().mark((function a(o,r,n){var s,l,c,u;return m().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if("confirm"!==o){a.next=18;break}return r.confirmButtonLoading=!0,t.isLoading=!0,a.next=5,Object(i["Z"])(t.$apis.apiBackgroundStoreGoodsDeletePost({id:e.id}));case 5:if(s=a.sent,l=f(s,2),c=l[0],u=l[1],t.isLoading=!1,r.confirmButtonLoading=!1,n(),!c){a.next=15;break}return t.$message.error(c.message),a.abrupt("return");case 15:0===u.code?(n(),t.$message.success(u.msg),t.currentPage>1&&(1===t.tableData.length||t.currentPage===t.totalPageSize)&&t.currentPage--,t.getGoodsList()):t.$message.error(u.msg),a.next=19;break;case 18:r.confirmButtonLoading||n();case 19:case"end":return a.stop()}}),a)})));function o(e,t,o){return a.apply(this,arguments)}return o}()})},formatQueryParams:function(e){var t={};for(var a in e)""!==e[a].value&&null!==e[a].value&&("select_time"!==a?t[a]=e[a].value:e[a].value.length>0&&(t.start_time=e[a].value[0],t.end_time=e[a].value[1]));return t},clickDeviceGoodsWarehousingDialog:function(){this.deviceGoodsWarehousingDialogVisible=!0},addOrEditGoods:function(e,t){this.type=e,this.deviceGoodsDialogTitle="新增商品","modify"===e&&(this.dialogModifyId=t.id,this.deviceGoodsDialogTitle="编辑商品"),this.deviceGoodsVisible=!0},determineStock:function(e){this.stockListData=e.map((function(e,t){return e.index=t+1,e})),this.stockDataSuccessDialogVisible=!0},handleSizeChange:function(e){this.pageSize=e,this.getGoodsList()},handleCurrentChange:function(e){this.currentPage=e,this.getGoodsList()},gotoExport:function(){var e={type:"ExportGoodsInfo",params:L(L({},this.formatQueryParams(this.searchFormSetting)),{},{page:this.currentPage,page_size:this.pageSize})};this.exportHandle(e)},beforeUpload:function(e){var t=/application\/\S*zip\S*/;if(!t.test(e.type))return this.$message.error("请上传后缀名为.zip的压缩包文件"),!1},remove:function(){this.goodsUploadUrl=""},getSuccessUploadRes:function(e){this.goodsUploadUrl=e[0].url},importGoodsDetermine:function(){if(this.goodsUploadUrl){var e={type:"importGoods",message:"确定导入商品？",url:"apiBackgroundStoreGoodsBatchImportGoodsPost",params:{zip_url:this.goodsUploadUrl}};this.exportHandle(e)}else this.$message.error("人脸压缩包还没上传完毕或未上传")}}},C=O,P=(a("9683"),a("2877")),j=Object(P["a"])(C,o,r,!1,null,"1e6ac7d6",null);t["default"]=j.exports}}]);