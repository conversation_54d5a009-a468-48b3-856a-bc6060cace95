(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-inventory-components-related-document-VehicleInformation"],{"2c74":function(e,i,t){"use strict";t.r(i);var s=function(){var e=this,i=e._self._c;return i("div",{staticClass:"vehicleInformation-box"},[e._l(e.vehicleList,(function(t,s){return i("div",{key:s,staticClass:"vehicle-item"},[i("div",{staticClass:"form-item"},[i("span",{staticClass:"form-label"},[e._v("车辆类型：")]),e._v(e._s(t.car_type_alias)+" ")]),i("div",{staticClass:"form-item"},[i("span",{staticClass:"form-label"},[e._v("车牌号：")]),e._v(e._s(t.plate_number)+" ")]),i("div",{staticClass:"form-item"},[i("span",{staticClass:"form-label"},[e._v("车辆图片：")]),i("div",{staticClass:"form-img-box"},e._l(t.car_img,(function(s,n){return i("el-image",{key:s,staticClass:"detault-img m-r-6 pointer",attrs:{src:s,fit:"contain"},on:{click:function(i){return e.clickViewerHandler(t.car_img,n)}}})})),1)]),e.showDivider?i("el-divider"):e._e()],1)})),i("image-viewer",{attrs:{"initial-index":e.imgIndex,"z-index":3e3,"on-close":e.closeViewer,"preview-src-list":e.previewSrcList},model:{value:e.showViewer,callback:function(i){e.showViewer=i},expression:"showViewer"}})],2)},n=[],r={name:"VehicleInformation",props:{vehicleList:{type:Array,default:function(){return[]}},showDivider:{type:Boolean,default:!1}},data:function(){return{imgIndex:0,previewSrcList:[],showViewer:!1}},computed:{},watch:{},created:function(){},mounted:function(){},methods:{clickViewerHandler:function(e,i){this.previewSrcList=e||[],this.imgIndex=i,this.showViewer=!0},closeViewer:function(){this.showViewer=!1}}},c=r,a=(t("5cc2"),t("2877")),o=Object(a["a"])(c,s,n,!1,null,"6526e0fe",null);i["default"]=o.exports},"47b5":function(e,i,t){},"5cc2":function(e,i,t){"use strict";t("47b5")}}]);