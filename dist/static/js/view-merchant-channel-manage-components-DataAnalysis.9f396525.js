(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-channel-manage-components-DataAnalysis"],{1532:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",[e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isBusinessLoading,expression:"isBusinessLoading"}],staticClass:"line-trend"},[e("div",{staticClass:"top flex"},[e("div",{staticClass:"title"},[t._v("数据分析")]),e("div",{staticClass:"m-l-10"},[e("el-select",{attrs:{placeholder:"请选择",multiple:"",clearable:"","collapse-tags":"","multiple-limit":t.limitNum},on:{change:t.orgsChange},model:{value:t.orgsId,callback:function(e){t.orgsId=e},expression:"orgsId"}},t._l(t.orgsOptions,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),e("div",{staticClass:"m-l-10 m-t-10"},[t._v(t._s(t.chooseNum)+"/10")]),e("div",{staticClass:"m-l-10 ps-text-gray"},[t._v("默认展示所有项目汇总数据，可选择其中至多10个进行比较")])]),e("div",{staticClass:"nav-cen flex"},t._l(t.businessSummery,(function(r){return e("div",{key:r.key,staticClass:"item",class:t.selectTrend.key===r.key?"nav-active":"",on:{click:function(e){return t.changetrendHandle(r)}}},[t._v(" "+t._s(r.label)+" ")])})),0),e("div",{directives:[{name:"show",rawName:"v-show",value:!t.isShowNoMore,expression:"!isShowNoMore"}],ref:"line_chart",attrs:{id:"line_chart"}}),e("div",{directives:[{name:"show",rawName:"v-show",value:t.isShowNoMore,expression:"isShowNoMore"}],staticClass:"flex flex-center empty-text"},[t._v(" 暂无更多 ")])])])},i=[],a=r("ed08"),o=r("2232"),s=r("5a0c");function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},o=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",l=a.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function h(t,e,r,n){var a=e&&e.prototype instanceof b?e:b,o=Object.create(a.prototype),s=new A(n||[]);return i(o,"_invoke",{value:T(t,r,s)}),o}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var d="suspendedStart",p="suspendedYield",y="executing",v="completed",g={};function b(){}function w(){}function x(){}var L={};u(L,o,(function(){return this}));var O=Object.getPrototypeOf,_=O&&O(O(D([])));_&&_!==r&&n.call(_,o)&&(L=_);var j=x.prototype=b.prototype=Object.create(L);function C(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function r(i,a,o,s){var c=f(t[i],t,a);if("throw"!==c.type){var l=c.arg,u=l.value;return u&&"object"==m(u)&&n.call(u,"__await")?e.resolve(u.__await).then((function(t){r("next",t,o,s)}),(function(t){r("throw",t,o,s)})):e.resolve(u).then((function(t){l.value=t,o(l)}),(function(t){return r("throw",t,o,s)}))}s(c.arg)}var a;i(this,"_invoke",{value:function(t,n){function i(){return new e((function(e,i){r(t,n,e,i)}))}return a=a?a.then(i,i):i()}})}function T(e,r,n){var i=d;return function(a,o){if(i===y)throw Error("Generator is already running");if(i===v){if("throw"===a)throw o;return{value:t,done:!0}}for(n.method=a,n.arg=o;;){var s=n.delegate;if(s){var c=k(s,n);if(c){if(c===g)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===d)throw i=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=y;var l=f(e,r,n);if("normal"===l.type){if(i=n.done?v:p,l.arg===g)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(i=v,n.method="throw",n.arg=l.arg)}}}function k(e,r){var n=r.method,i=e.iterator[n];if(i===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,k(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var a=f(i,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,g;var o=a.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,g):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function N(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function A(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function D(e){if(e||""===e){var r=e[o];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,a=function r(){for(;++i<e.length;)if(n.call(e,i))return r.value=e[i],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(m(e)+" is not iterable")}return w.prototype=x,i(j,"constructor",{value:x,configurable:!0}),i(x,"constructor",{value:w,configurable:!0}),w.displayName=u(x,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,x):(t.__proto__=x,u(t,l,"GeneratorFunction")),t.prototype=Object.create(j),t},e.awrap=function(t){return{__await:t}},C(S.prototype),u(S.prototype,s,(function(){return this})),e.AsyncIterator=S,e.async=function(t,r,n,i,a){void 0===a&&(a=Promise);var o=new S(h(t,r,n,i),a);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},C(j),u(j,l,"Generator"),u(j,o,(function(){return this})),u(j,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=D,A.prototype={constructor:A,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(N),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function i(n,i){return s.type="throw",s.arg=e,r.next=n,i&&(r.method="next",r.arg=t),!!i}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],s=o.completion;if("root"===o.tryLoc)return i("end");if(o.tryLoc<=this.prev){var c=n.call(o,"catchLoc"),l=n.call(o,"finallyLoc");if(c&&l){if(this.prev<o.catchLoc)return i(o.catchLoc,!0);if(this.prev<o.finallyLoc)return i(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return i(o.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return i(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=t,o.arg=e,a?(this.method="next",this.next=a.finallyLoc,g):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),N(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var i=n.arg;N(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:D(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),g}},e}function l(t,e){return p(t)||d(t,e)||h(t,e)||u()}function u(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function h(t,e){if(t){if("string"==typeof t)return f(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?f(t,e):void 0}}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function d(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,a,o,s=[],c=!0,l=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(s.push(n.value),s.length!==e);c=!0);}catch(t){l=!0,i=t}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return s}}function p(t){if(Array.isArray(t))return t}function y(t,e,r,n,i,a,o){try{var s=t[a](o),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,i)}function v(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var a=t.apply(e,r);function o(t){y(a,n,i,o,s,"next",t)}function s(t){y(a,n,i,o,s,"throw",t)}o(void 0)}))}}function m(t){return m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},m(t)}var g={name:"DataAnalysis",props:{chartData:{type:Object,default:function(){}},channelId:{type:Array,default:function(){return[]}},timeValue:{type:Object,default:function(){return{}}},channelIdFormat:{type:Array,default:function(){return[]}}},data:function(){return{isBusinessLoading:!1,businessSummery:o["TAB_DATA_LIST"],selectTrend:{},lineChart:null,LineChartObj:{},orgsOptions:[],orgsId:"",chooseNum:0,chartXLineList:[],intervalDays:0,isShowNoMore:!1,limitNum:10}},created:function(){this.selectTrend=this.businessSummery[0],this.initDic()},mounted:function(){window.addEventListener("resize",this.resizeChartHandle)},watch:{channelId:function(t){},channelIdFormat:function(t){this.initDic()},timeValue:{deep:!0,handler:function(t){this.setDataTimeList(t)}},chartData:{deep:!0,handler:function(t){t&&"object"===m(t)&&Object.keys(t).length>0?(this.isShowNoMore=!1,this.initTurnoverTrendLine(Object(a["f"])(t))):(this.clearChart(),this.isShowNoMore=!0)}}},methods:{initTurnoverTrendLine:function(t){var e=this,r=Object(a["f"])(o["TREND_SETTING"]);r.xAxis.data=this.chartXLineList,r.tooltip.formatter='<div style="padding:5px;font-size:16px;font-weight: 540;"><span style="margin-right:10px;">{a0}</span>{b0}<br /><span style=" color: #666;display:inline-block;margin: 5px 20px 0 0;">'.concat(this.selectTrend.label,"</span>").concat("交易金额"===this.selectTrend.label?"￥":"","{c0}</div>"),Reflect.has(t,"analysis_data")&&Array.isArray(t.analysis_data)&&t.analysis_data.length>0?(this.isShowNoMore=!1,this.setChartYData(r,t),this.lineChart||(this.lineChart=this.$echarts.init(this.$refs.line_chart)),this.lineChart&&(this.lineChart.clear(),this.$nextTick((function(){e.lineChart.setOption(r),e.resizeChartHandle()})))):(this.clearChart(),this.isShowNoMore=!0)},setChartYData:function(t,e){var r=this,n=e.analysis_data||[],i=[],o=[],s=this.timeValue.type;n.forEach((function(e){var n=Object(a["f"])(t.series[0]);n.name=e.org_name||"",o.push(e.org_name);var c=e[r.selectTrend.key]||[],l=[],u="quarter"!==s?Object(a["f"])(r.chartXLineList):["1","2","3","4"];u.forEach((function(t){var e=t,n=0,i=c.find((function(t){return Reflect.has(t,e)}));i&&(n=i[e],"real_fee_list"===r.selectTrend.key&&(n=Object(a["i"])(n))),l.push(n)})),n.data=Object(a["f"])(l),i.push(n)})),t.legend.data=Object(a["f"])(o),t.series=Object(a["f"])(i)},changetrendHandle:function(t){this.selectTrend=t;var e=Object(a["f"])(this.chartData);this.initTurnoverTrendLine(e)},resizeChartHandle:Object(a["d"])((function(){this.lineChart&&this.lineChart.resize()}),300),getDiffDay:function(t,e){return s(t).diff(s(e),"days")},setDataTimeList:function(t){if(null!=t&&"object"===m(t)&&Reflect.has(t,"type")){var e,r,n=[],i=t.type||"",o=t.value||[];switch(i){case"day":e=o[0]?o[0]:"",r=o[1]?o[1]:"";var c=s(r).diff(s(e),"days")+1;if(this.intervalDays=c,c>0)for(var l=0;l<c;l++){var u=s(e).add(l,"day").format("YYYY-MM-DD");n.push(u)}else n=[e];break;case"month":e=o[0]?o[0].slice(0,7):"",r=o[1]?o[1].slice(0,7):"";var h=s(r).diff(s(e),"months")+1;if(this.intervalDays=h,h>0)for(var f=0;f<h;f++){var d=s(e).add(f,"month").format("YYYY-MM");n.push(d)}else n=[e];break;case"quarter":n=["第一季度","第二季度","第三季度","第四季度"];break;default:break}this.chartXLineList=Object(a["f"])(n)}},initDic:function(){var t=this;return v(c().mark((function e(){var r,n,i,o,s,u;return c().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r={},Array.isArray(t.channelIdFormat)&&t.channelIdFormat.length>0?r.channel_ids=Object(a["f"])(t.channelIdFormat):delete r.channel_ids,e.next=4,Object(a["Z"])(t.$apis.apiBackgroundChannelDataStatisticsGetOrganizationListPost(r));case 4:if(n=e.sent,i=l(n,2),o=i[0],s=i[1],!o){e.next=10;break}return e.abrupt("return");case 10:0===s.code&&(u=s.data||[],Array.isArray(u)&&u.length>0?t.orgsOptions=Object(a["f"])(u):t.orgsOptions=[]);case 11:case"end":return e.stop()}}),e)})))()},orgsChange:function(t){this.chooseNum=t?t.length:0,this.$emit("orgsChooseChange",t)},clearChart:function(){null!=this.lineChart&&""!==this.lineChart&&void 0!==this.lineChart&&(this.lineChart.dispose(),this.lineChart=null)}}},b=g,w=(r("b3c6"),r("2877")),x=Object(w["a"])(b,n,i,!1,null,"20af4070",null);e["default"]=x.exports},b3c6:function(t,e,r){"use strict";r("f7133")},f7133:function(t,e,r){}}]);