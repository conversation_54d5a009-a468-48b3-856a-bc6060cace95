(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-collective-admin-addAndModifyCollective","view-merchant-consumption-rules-service-admin-AddChargeServiceRule","view-merchant-meal-management-components-mealFoodList-AddAndEditMealFoodDialog","view-merchant-meal-management-components-mealFoodList-FoodDiscountDialog","view-merchant-meal-management-components-selectLaber"],{"9fac":function(t,e,r){},a99b:function(t,e,r){"use strict";r("9fac")},c6ce:function(t,e,r){},d0dd:function(t,e,r){"use strict";r.d(e,"a",(function(){return n})),r.d(e,"b",(function(){return a})),r.d(e,"g",(function(){return i})),r.d(e,"c",(function(){return o})),r.d(e,"f",(function(){return s})),r.d(e,"d",(function(){return l})),r.d(e,"e",(function(){return c}));var n=function(t,e,r){if(e){var n=/^-?(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;n.test(e)?r():r(new Error("金额格式有误"))}else r(new Error("请输入金额"))},a=function(t,e,r){if(e){var n=/^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;n.test(e)?r():r(new Error("金额格式有误"))}else r()},i=function(t,e,r){if(!e)return r(new Error("手机号不能为空"));var n=/^1[3456789]\d{9}$/;n.test(e)?r():r(new Error("请输入正确手机号"))},o=function(t,e,r){if(!e)return r(new Error("金额有误"));var n=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;n.test(e)?r():r(new Error("金额格式有误"))},s=function(t,e,r){if(""===e)return r(new Error("不能为空"));var n=/^\d+$/;n.test(e)?r():r(new Error("请输入正确数字"))},l=function(t,e,r){if(""!==e){var n=/^(\+|-)?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;n.test(e)?r():r(new Error("金额格式有误"))}else r(new Error("请输入金额"))},c=function(t,e,r){var n=/^[\u4E00-\u9FA5\w-]+$/;n.test(e)?r():r(new Error("格式不正确，不能包含特殊字符"))}},f6f8:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("dialog-message",{attrs:{show:t.visible,title:t.title,loading:t.isLoading,customClass:"ps-dialog",width:t.width},on:{"update:show":function(e){t.visible=e},"update:loading":function(e){t.isLoading=e},close:t.handleClose}},[e("div",{staticClass:"healthTagDialog"},[e("el-input",{staticClass:"ps-input",attrs:{placeholder:"请输入要查询的标签组/标签名称名称"},on:{input:t.searchHandle},model:{value:t.name,callback:function(e){t.name=e},expression:"name"}}),t._t("append"),e("el-card",{staticClass:"m-t-20",attrs:{shadow:"never"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v(" 已选 "),e("span",[t._v(t._s(t.selectLabelIdList.length))]),t._v(" 个标签 ")])]),t._l(t.tableData,(function(r,n){return e("div",{key:n},[e("el-collapse",{model:{value:t.activeLaberList,callback:function(e){t.activeLaberList=e},expression:"activeLaberList"}},[e("el-collapse-item",{attrs:{name:r.id}},[e("template",{slot:"title"},[e("span",[t._v(" "+t._s(r.name)+" "),e("span",[t._v("（"+t._s(r.label_list.length)+"）")])]),e("span",{staticClass:"tips-r"},[e("span",{staticClass:"open"},[t._v("展开")]),e("span",{staticClass:"close"},[t._v("收起")])])]),e("div",{staticClass:"ps-flex-align-c flex-align-c flex-wrap"},[t.ruleSingleInfo.isAdmin?t._e():e("div",[r.inputVisible?e("el-input",{ref:"saveTagInput"+r.id,refInFor:!0,staticClass:"ps-input w-100 p-r-10 p-t-5",attrs:{size:"mini",autofocus:"",maxlength:"15"},on:{blur:function(e){return t.handleInputConfirm(r)}},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleInputConfirm(r)}},model:{value:r.inputValue,callback:function(e){t.$set(r,"inputValue",e)},expression:"item.inputValue"}}):e("button-icon",{staticClass:"p-t-5",attrs:{color:"origin",type:"add"},on:{click:function(e){return t.showInput(r)}}},[t._v(" 添加标签 ")])],1),e("div",{staticStyle:{flex:"1"}},[e("el-checkbox-group",{attrs:{size:"mini"},model:{value:t.selectLabelIdList,callback:function(e){t.selectLabelIdList=e},expression:"selectLabelIdList"}},t._l(r.label_list,(function(n,a){return e("el-checkbox-button",{key:a,attrs:{label:n.id,disabled:n.disabled},on:{change:function(e){return t.checkboxChangge(n,r)}}},[t._v(" "+t._s(n.name)+" ")])})),1)],1)])],2)],1)],1)}))],2)],2),e("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[e("el-pagination",{staticClass:"ps-text",attrs:{"page-size":t.pageSize,"current-page":t.currentPage,layout:"total, prev, pager, next",total:t.totalCount,background:"","popper-class":"ps-popper-select"},on:{"current-change":t.handleCurrentChange,"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e}}})],1),e("template",{slot:"tool"},[e("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:t.isLoading},on:{click:t.clickCancleHandle}},[t._v(" 取消 ")]),e("el-button",{staticClass:"ps-btn",attrs:{disabled:t.isLoading,type:"primary"},on:{click:t.clickConfirmHandle}},[t._v(" 保存 ")])],1)])],2)},a=[],i=r("ed08");function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},l=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function d(t,e,r,n){var i=e&&e.prototype instanceof y?e:y,o=Object.create(i.prototype),s=new P(n||[]);return a(o,"_invoke",{value:S(t,r,s)}),o}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=d;var h="suspendedStart",m="suspendedYield",g="executing",b="completed",v={};function y(){}function L(){}function w(){}var D={};f(D,l,(function(){return this}));var x=Object.getPrototypeOf,_=x&&x(x($([])));_&&_!==r&&n.call(_,l)&&(D=_);var k=w.prototype=y.prototype=Object.create(D);function C(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function I(t,e){function r(a,i,s,l){var c=p(t[a],t,i);if("throw"!==c.type){var u=c.arg,f=u.value;return f&&"object"==o(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,s,l)}),(function(t){r("throw",t,s,l)})):e.resolve(f).then((function(t){u.value=t,s(u)}),(function(t){return r("throw",t,s,l)}))}l(c.arg)}var i;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return i=i?i.then(a,a):a()}})}function S(e,r,n){var a=h;return function(i,o){if(a===g)throw Error("Generator is already running");if(a===b){if("throw"===i)throw o;return{value:t,done:!0}}for(n.method=i,n.arg=o;;){var s=n.delegate;if(s){var l=O(s,n);if(l){if(l===v)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===h)throw a=b,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=g;var c=p(e,r,n);if("normal"===c.type){if(a=n.done?b:m,c.arg===v)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(a=b,n.method="throw",n.arg=c.arg)}}}function O(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,O(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=p(a,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var o=i.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function P(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function $(e){if(e||""===e){var r=e[l];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,i=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(o(e)+" is not iterable")}return L.prototype=w,a(k,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:L,configurable:!0}),L.displayName=f(w,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===L||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,f(t,u,"GeneratorFunction")),t.prototype=Object.create(k),t},e.awrap=function(t){return{__await:t}},C(I.prototype),f(I.prototype,c,(function(){return this})),e.AsyncIterator=I,e.async=function(t,r,n,a,i){void 0===i&&(i=Promise);var o=new I(d(t,r,n,a),i);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},C(k),f(k,u,"Generator"),f(k,l,(function(){return this})),f(k,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=$,P.prototype={constructor:P,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return s.type="throw",s.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var l=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),j(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;j(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:$(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function l(t,e){return p(t)||d(t,e)||u(t,e)||c()}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(t,e){if(t){if("string"==typeof t)return f(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?f(t,e):void 0}}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function d(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,i,o,s=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(s.push(n.value),s.length!==e);l=!0);}catch(t){c=!0,a=t}finally{try{if(!l&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw a}}return s}}function p(t){if(Array.isArray(t))return t}function h(t,e,r,n,a,i,o){try{var s=t[i](o),l=s.value}catch(t){return void r(t)}s.done?e(l):Promise.resolve(l).then(n,a)}function m(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var i=t.apply(e,r);function o(t){h(i,n,a,o,s,"next",t)}function s(t){h(i,n,a,o,s,"throw",t)}o(void 0)}))}}var g={name:"selectLaber",props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:"选择标签"},width:{type:String,default:"600px"},isshow:Boolean,ruleSingleInfo:{type:Object,default:function(){return{}}}},data:function(){return{isLoading:!1,name:"",activeLaberList:[],selectLabelIdList:[],selectLabelListData:[],tableData:[],labelType:"",pageSize:3,totalCount:0,currentPage:1}},computed:{visible:{get:function(){return this.isshow},set:function(t){this.$emit("update:isshow",t)}}},created:function(){this.ruleSingleInfo.selectLabelIdList&&this.ruleSingleInfo.selectLabelIdList.length&&(this.selectLabelIdList=Object(i["f"])(this.ruleSingleInfo.selectLabelIdList)),this.ruleSingleInfo.selectLabelListData&&this.ruleSingleInfo.selectLabelListData.length&&(this.selectLabelListData=Object(i["f"])(this.ruleSingleInfo.selectLabelListData)),this.labelType=this.ruleSingleInfo.labelType,this.getLabelGroupList()},mounted:function(){},methods:{searchHandle:Object(i["d"])((function(){this.currentPage=1,this.getLabelGroupList()}),300),getLabelGroupList:function(){var t=this;return m(s().mark((function e(){var r,n,a,o,c;return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,r={type:t.labelType,page:t.currentPage,page_size:t.pageSize},t.name&&(r.name=t.name),t.ruleSingleInfo.isAdmin&&(r.is_admin=t.ruleSingleInfo.isAdmin),e.next=6,Object(i["Z"])(t.$apis.apiBackgroundHealthyLabelGroupAllLabelGroupListPost(r));case 6:if(n=e.sent,a=l(n,2),o=a[0],c=a[1],t.isLoading=!1,!o){e.next=14;break}return t.$message.error(o.message),e.abrupt("return");case 14:0===c.code?(t.totalCount=c.data.count,t.totalPageSize=t.$computedTotalPageSize(t.totalCount,t.pageSize),t.tableData=c.data.results.map((function(e){return e.inputVisible=!1,e.inputValue="",e.label_list.forEach((function(r){r.label_group_name=e.name,t.ruleSingleInfo.selectLabelAllIds&&t.ruleSingleInfo.selectLabelAllIds.length&&t.ruleSingleInfo.selectLabelAllIds.includes(r.id)&&!t.selectLabelIdList.includes(r.id)?r.disabled=!0:r.disabled=!1})),t.activeLaberList.push(e.id),e}))):t.$message.error(c.msg);case 15:case"end":return e.stop()}}),e)})))()},handleChange:function(){},checkboxChangge:function(t,e){var r=this,n=this.selectLabelIdList.indexOf(t.id);-1!==n?this.selectLabelListData.push(t):this.selectLabelListData.map((function(e,n){t.id===e.id&&r.selectLabelListData.splice(n,1)}))},handleInputConfirm:function(t){t.inputValue?this.getLabelGroupAddLabel(t):t.inputVisible=!1},showInput:function(t){var e=this;t.inputVisible=!0,this.$nextTick((function(r){e.$refs["saveTagInput"+t.id][0].$refs.input.focus()}))},getLabelGroupAddLabel:function(t){var e=this;return m(s().mark((function r(){var n,a,o,c;return s().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return e.isLoading=!0,r.next=3,Object(i["Z"])(e.$apis.apiBackgroundHealthyLabelGroupAddLabelPost({name:t.inputValue,label_group_id:t.id}));case 3:if(n=r.sent,a=l(n,2),o=a[0],c=a[1],e.isLoading=!1,!o){r.next=11;break}return e.$message.error(o.message),r.abrupt("return");case 11:0===c.code?(t.inputValue="",t.inputVisible=!1,e.getLabelGroupList()):e.$message.error(c.msg);case 12:case"end":return r.stop()}}),r)})))()},clickConfirmHandle:function(){var t={selectLabelIdList:this.selectLabelIdList,selectLabelListData:this.selectLabelListData};this.$emit("selectLaberData",t),this.visible=!1},clickCancleHandle:function(){this.visible=!1},handleClose:function(t){this.visible=!1},handleCurrentChange:function(t){this.currentPage=t,this.getLabelGroupList()}}},b=g,v=(r("fa05"),r("2877")),y=Object(v["a"])(b,n,a,!1,null,null,null);e["default"]=y.exports},f72e:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",[e("dialog-message",{staticClass:"collective-dialog",attrs:{show:t.visible,title:t.title,customClass:"ps-dialog",width:t.width},on:{"update:show":function(e){t.visible=e},close:t.handleClose}},[e("el-form",{ref:"formData",staticClass:"dialog-form add-and-modify-collectivel",staticStyle:{overflow:"auto",height:"600px"},attrs:{loading:t.isLoading,rules:t.formDataRuls,model:t.formData,size:"small"},on:{"update:loading":function(e){t.isLoading=e}}},[e("el-form-item",{staticClass:"block-label",attrs:{label:"集体名称：",prop:"name"}},[e("el-input",{staticClass:"ps-input",attrs:{placeholder:"请输入集体名称",maxlength:"15"},model:{value:t.formData.name,callback:function(e){t.$set(t.formData,"name",e)},expression:"formData.name"}})],1),e("el-form-item",{staticClass:"block-label",attrs:{label:"总人数：",prop:"number"}},[e("el-input",{staticClass:"ps-input",attrs:{disabled:"",maxlength:"15"},model:{value:t.formData.number,callback:function(e){t.$set(t.formData,"number",e)},expression:"formData.number"}})],1),e("div",{staticClass:"ps-flex-bw"},[e("div",{staticStyle:{width:"45%"}},[e("el-form-item",{staticClass:"block-label",attrs:{label:"男性数量：",prop:"man_number"}},[e("el-input",{staticClass:"ps-input",attrs:{placeholder:"请输入男性数量",maxlength:"15"},on:{input:t.changeInput},model:{value:t.formData.man_number,callback:function(e){t.$set(t.formData,"man_number",e)},expression:"formData.man_number"}})],1)],1),e("div",{staticStyle:{width:"45%"}},[e("el-form-item",{staticClass:"block-label",attrs:{label:"女性数量：",prop:"women_number"}},[e("el-input",{staticClass:"ps-input",attrs:{placeholder:"请输入女性数量",maxlength:"15"},on:{input:t.changeInput},model:{value:t.formData.women_number,callback:function(e){t.$set(t.formData,"women_number",e)},expression:"formData.women_number"}})],1)],1)]),e("div",{staticClass:"crowd-box"},[e("div",{staticClass:"crowd-title ps-flex-bw"},[e("span",[t._v(" 所含人群 "),e("i",{staticClass:"el-icon-warning"})]),e("span",{staticStyle:{color:"red"}},[t._v("*占比相加必须等于100%")])]),e("div",{staticClass:"tag-box"},[e("el-checkbox-group",{attrs:{size:"mini"},model:{value:t.crowdDataListTagIds,callback:function(e){t.crowdDataListTagIds=e},expression:"crowdDataListTagIds"}},t._l(t.crowdDataTagList,(function(r,n){return e("el-checkbox-button",{key:n,attrs:{label:r.id},on:{change:function(e){return t.checkboxChangge(r)}}},[t._v(" "+t._s(r.name)+" ")])})),1)],1),t._l(t.crowdDataList,(function(r,n){return e("div",{key:n,staticClass:"proportion-box"},[e("div",[t._v(t._s(r.name)+"人数占比：")]),e("div",{staticClass:"cantent"},[e("div",{staticStyle:{width:"100%","margin-left":"10px"}},[e("el-slider",{staticClass:"cantent",attrs:{disabled:!t.formData.number},on:{change:function(e){return t.changePercentage(r)}},model:{value:r.percentage,callback:function(e){t.$set(r,"percentage",e)},expression:"item.percentage"}})],1)]),e("span",{staticClass:"p-l-20 p-r-10"},[t._v(t._s(r.percentage)+"%")]),e("el-input-number",{attrs:{size:"small",min:0,max:t.formData.number?t.formData.number:0},on:{change:function(e){return t.handleNumberChange(r)}},model:{value:r.percentageNumber,callback:function(e){t.$set(r,"percentageNumber",e)},expression:"item.percentageNumber"}}),e("span",{staticClass:"p-l-10"},[t._v("人")])],1)}))],2),e("div",{staticClass:"p-t-20"},[t.formData.selectLabelIdList.length?t._e():e("div",{staticStyle:{color:"red","font-size":"13px"}},[t._v(" 如集体中包含特殊人群建议添加标签，系统可以更科学有效的为您提供菜谱营养建议与风险预警 ")]),e("el-form-item",{attrs:{label:"标签：",prop:""}},[e("el-button",{staticClass:"ps-origin-btn",attrs:{type:"primary",size:"small"},on:{click:t.labelClick}},[t._v(" 选择标签 ")]),t.formData.selectLabelIdList.length?e("span",{staticClass:"p-l-10",staticStyle:{color:"#fd9445"}},[t._v(" 已经选择"+t._s(t.formData.selectLabelIdList.length)+"个标签 ")]):t._e()],1),t.formData.selectLabelIdList.length?e("el-form-item",{attrs:{label:"已添加标签：",prop:""}},t._l(t.formData.selectLabelListData,(function(r,n){return e("el-tag",{key:n,staticClass:"m-r-5 collapse-data",attrs:{size:"medium",effect:"plain",type:"info",color:"#fff",closable:""},on:{close:function(e){return t.closeTag(r)}}},[t._v(" "+t._s(r.name)+" ")])})),1):t._e()],1),e("el-form-item",{staticClass:"block-label",attrs:{label:"备注：",prop:"remark"}},[e("el-input",{staticClass:"ps-input",attrs:{type:"textarea",placeholder:"请输入备注内容",maxlength:"60","show-word-limit":"",autosize:{minRows:2,maxRows:4}},model:{value:t.formData.remark,callback:function(e){t.$set(t.formData,"remark",e)},expression:"formData.remark"}})],1)],1),e("template",{slot:"tool"},[e("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:t.isLoading},on:{click:t.clickCancleHandle}},[t._v(" 取消 ")]),e("el-button",{staticClass:"ps-btn",attrs:{disabled:t.isLoading,type:"primary"},on:{click:t.clickConfirmHandle}},[t._v(" 保存 ")])],1)])],2),t.selectLaberDialogVisible?e("select-laber",{ref:"selectLaber",attrs:{isshow:t.selectLaberDialogVisible,width:"600px",ruleSingleInfo:t.ruleSingleInfo},on:{"update:isshow":function(e){t.selectLaberDialogVisible=e},selectLaberData:t.selectLaberData}}):t._e()],1)},a=[],i=r("f6f8"),o=r("ed08"),s=r("da92"),l=r("d0dd");function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function u(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function f(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u(Object(r),!0).forEach((function(e){d(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function d(t,e,r){return(e=p(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function p(t){var e=h(t,"string");return"symbol"==c(e)?e:e+""}function h(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function m(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */m=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var i=e&&e.prototype instanceof y?e:y,o=Object.create(i.prototype),s=new P(n||[]);return a(o,"_invoke",{value:S(t,r,s)}),o}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="suspendedYield",g="executing",b="completed",v={};function y(){}function L(){}function w(){}var D={};u(D,o,(function(){return this}));var x=Object.getPrototypeOf,_=x&&x(x($([])));_&&_!==r&&n.call(_,o)&&(D=_);var k=w.prototype=y.prototype=Object.create(D);function C(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function I(t,e){function r(a,i,o,s){var l=d(t[a],t,i);if("throw"!==l.type){var u=l.arg,f=u.value;return f&&"object"==c(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,o,s)}),(function(t){r("throw",t,o,s)})):e.resolve(f).then((function(t){u.value=t,o(u)}),(function(t){return r("throw",t,o,s)}))}s(l.arg)}var i;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return i=i?i.then(a,a):a()}})}function S(e,r,n){var a=p;return function(i,o){if(a===g)throw Error("Generator is already running");if(a===b){if("throw"===i)throw o;return{value:t,done:!0}}for(n.method=i,n.arg=o;;){var s=n.delegate;if(s){var l=O(s,n);if(l){if(l===v)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===p)throw a=b,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=g;var c=d(e,r,n);if("normal"===c.type){if(a=n.done?b:h,c.arg===v)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(a=b,n.method="throw",n.arg=c.arg)}}}function O(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,O(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=d(a,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var o=i.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function P(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function $(e){if(e||""===e){var r=e[o];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,i=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(c(e)+" is not iterable")}return L.prototype=w,a(k,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:L,configurable:!0}),L.displayName=u(w,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===L||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,u(t,l,"GeneratorFunction")),t.prototype=Object.create(k),t},e.awrap=function(t){return{__await:t}},C(I.prototype),u(I.prototype,s,(function(){return this})),e.AsyncIterator=I,e.async=function(t,r,n,a,i){void 0===i&&(i=Promise);var o=new I(f(t,r,n,a),i);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},C(k),u(k,l,"Generator"),u(k,o,(function(){return this})),u(k,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=$,P.prototype={constructor:P,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return s.type="throw",s.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var l=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),j(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;j(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:$(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function g(t,e){return w(t)||L(t,e)||v(t,e)||b()}function b(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function v(t,e){if(t){if("string"==typeof t)return y(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?y(t,e):void 0}}function y(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function L(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,i,o,s=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(s.push(n.value),s.length!==e);l=!0);}catch(t){c=!0,a=t}finally{try{if(!l&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw a}}return s}}function w(t){if(Array.isArray(t))return t}function D(t,e,r,n,a,i,o){try{var s=t[i](o),l=s.value}catch(t){return void r(t)}s.done?e(l):Promise.resolve(l).then(n,a)}function x(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var i=t.apply(e,r);function o(t){D(i,n,a,o,s,"next",t)}function s(t){D(i,n,a,o,s,"throw",t)}o(void 0)}))}}var _={name:"addAndModifyCollective",props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:"选择标签"},width:{type:String,default:"600px"},isshow:Boolean,confirm:Function,formDataDialog:{type:Object,default:function(){return{}}}},components:{selectLaber:i["default"]},data:function(){return{isLoading:!1,formData:{name:"",number:"",man_number:"",women_number:"",selectLabelListData:[],selectLabelIdList:[],remark:""},formDataRuls:{name:[{required:!0,message:"请输入集体名称",trigger:"blur"}],man_number:[{required:!0,message:"请输入整数",validator:l["f"],trigger:"blur"}],women_number:[{required:!0,message:"请输入整数",validator:l["f"],trigger:"blur"}]},crowdDataListTagIds:[],crowdDataTagList:[],crowdDataList:[],selectLaberDialogVisible:!1,ruleSingleInfo:{}}},computed:{visible:{get:function(){return this.isshow},set:function(t){this.$emit("update:isshow",t)}}},created:function(){var t=this;this.getCollectiveCrowdData(),"modify"===this.type&&(this.formData={name:this.formDataDialog.name,number:this.formDataDialog.number,man_number:this.formDataDialog.man_number,women_number:this.formDataDialog.women_number,selectLabelListData:this.formDataDialog.label,selectLabelIdList:[],remark:this.formDataDialog.remark},this.formDataDialog.crowd_list.forEach((function(e){t.crowdDataListTagIds.push(e.crowd_id),t.crowdDataList.push({id:e.crowd_id,name:e.name,percentage:parseInt(e.number/t.formDataDialog.number*100),percentageNumber:e.number})})),this.formDataDialog.label.length&&(this.formData.selectLabelIdList=this.formDataDialog.label.map((function(t){return t.id}))))},mounted:function(){},methods:{changeInput:function(){this.formData.number=Number(this.formData.man_number)+Number(this.formData.women_number),this.crowdDataListTagIds=[],this.crowdDataList=[]},handleClose:function(t){this.visible=!1},clickCancleHandle:function(){this.visible=!1},checkboxChangge:function(t){var e=this,r=this.crowdDataListTagIds.indexOf(t.id);-1!==r?this.crowdDataList.push({id:t.id,name:t.name,percentage:0,percentageNumber:0}):this.crowdDataList.map((function(r,n){t.id===r.id&&e.crowdDataList.splice(n,1)}))},getCollectiveCrowdData:function(){var t=this;return x(m().mark((function e(){var r,n,a,i;return m().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(o["Z"])(t.$apis.apiBackgroundFoodCollectiveCrowdDataPost());case 3:if(r=e.sent,n=g(r,2),a=n[0],i=n[1],t.isLoading=!1,!a){e.next=11;break}return t.$message.error(a.message),e.abrupt("return");case 11:0===i.code?t.crowdDataTagList=i.data.result:t.$message.error(i.msg);case 12:case"end":return e.stop()}}),e)})))()},verificationPercentage:function(){var t=!1,e=this.crowdDataList.reduce((function(t,e){return s["a"].plus(e.percentageNumber,t)}),0);return t=e===this.formData.number,t},changePercentage:function(t){var e=t.percentage/100;t.percentageNumber=parseInt(this.formData.number*e)},handleNumberChange:function(t){t.percentage=parseInt(t.percentageNumber/this.formData.number*100)},labelClick:function(){this.ruleSingleInfo={isAdmin:!0,labelType:"user",selectLabelIdList:this.formData.selectLabelIdList,selectLabelListData:this.formData.selectLabelListData},this.selectLaberDialogVisible=!0},selectLaberData:function(t){this.formData.selectLabelIdList=t.selectLabelIdList,this.formData.selectLabelListData=t.selectLabelListData},closeTag:function(t){var e=this.formData.selectLabelIdList.indexOf(t.id),r=this.formData.selectLabelListData.indexOf(t);this.formData.selectLabelIdList.splice(e,1),this.formData.selectLabelListData.splice(r,1)},getCollectiveAdd:function(t){var e=this;return x(m().mark((function r(){var n,a,i,s;return m().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return e.formFoodLoading=!0,r.next=3,Object(o["Z"])(e.$apis.apiBackgroundFoodCollectiveAddPost(t));case 3:if(n=r.sent,a=g(n,2),i=a[0],s=a[1],e.formFoodLoading=!1,!i){r.next=11;break}return e.$message.error(i.message),r.abrupt("return");case 11:0===s.code?(e.visible=!1,e.$message.success(s.msg),e.$emit("confirm","search")):e.$message.error(s.msg);case 12:case"end":return r.stop()}}),r)})))()},getCollectiveModify:function(t){var e=this;return x(m().mark((function r(){var n,a,i,s;return m().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return e.formFoodLoading=!0,r.next=3,Object(o["Z"])(e.$apis.apiBackgroundFoodCollectiveModifyPost(t));case 3:if(n=r.sent,a=g(n,2),i=a[0],s=a[1],e.formFoodLoading=!1,!i){r.next=11;break}return e.$message.error(i.message),r.abrupt("return");case 11:0===s.code?(e.visible=!1,e.$message.success(s.msg),e.$emit("confirm","search")):e.$message.error(s.msg);case 12:case"end":return r.stop()}}),r)})))()},clickConfirmHandle:function(){var t=this;this.$refs.formData.validate((function(e){if(!e)return!1;if(!t.verificationPercentage())return t.$message.error("所含人数必须等于总人数");var r={name:t.formData.name,number:t.formData.number,man_number:t.formData.man_number,women_number:t.formData.women_number,label_list:t.formData.selectLabelIdList,remark:t.formData.remark};t.crowdDataList.length&&(r.crowd_data=t.crowdDataList.map((function(t){var e={crowd_id:t.id,number:t.percentageNumber};return e}))),"add"===t.type?t.getCollectiveAdd(r):t.getCollectiveModify(f({id:t.formDataDialog.id},r))}))}}},k=_,C=(r("a99b"),r("2877")),I=Object(C["a"])(k,n,a,!1,null,null,null);e["default"]=I.exports},fa05:function(t,e,r){"use strict";r("c6ce")}}]);