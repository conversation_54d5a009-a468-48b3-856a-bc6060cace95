(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-inventory-components-addAptitudeDialog","view-merchant-inventory-constants"],{"32ab":function(e,t,a){"use strict";a("ab62")},4038:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("custom-drawer",e._g(e._b({staticClass:"aptitude-drawer-wrapper",attrs:{title:e.title,show:e.visible,direction:e.direction,wrapperClosable:e.wrapperClosable,size:e.size,"confirm-text":"确定"},on:{"update:show":function(t){e.visible=t},close:e.handlerClose,cancel:e.clickCancleHandle,confirm:e.clickConfirmHandle}},"custom-drawer",e.$attrs,!1),e.$listeners),[t("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"formRef",staticClass:"dialog-form m-t-20",attrs:{model:e.formData,rules:e.formDataRules,"label-width":"160px","status-icon":!1,size:"small"}},[t("el-form-item",{attrs:{label:"资质类型",prop:"aptitude"}},[t("el-select",{staticClass:"ps-select form-item-w",attrs:{"popper-class":"ps-popper-select",placeholder:"请选择",disabled:"detail"===e.type},on:{change:e.setRules},model:{value:e.formData.aptitude,callback:function(t){e.$set(e.formData,"aptitude",t)},expression:"formData.aptitude"}},e._l(e.aptitudeList,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("el-form-item",{attrs:{label:"经营者名称",prop:"name"}},[t("el-input",{staticClass:"form-item-w",attrs:{disabled:"detail"===e.type},model:{value:e.formData.name,callback:function(t){e.$set(e.formData,"name",t)},expression:"formData.name"}})],1),"1"===e.formData.aptitude?t("div",[t("el-form-item",{attrs:{label:"类型",prop:"aptitude_type"}},[t("el-input",{staticClass:"form-item-w",attrs:{disabled:"detail"===e.type},model:{value:e.formData.aptitude_type,callback:function(t){e.$set(e.formData,"aptitude_type",t)},expression:"formData.aptitude_type"}})],1),t("el-form-item",{attrs:{label:"经营范围",prop:"business_scope"}},[t("el-input",{staticClass:"form-item-w",attrs:{type:"textarea",rows:2,disabled:"detail"===e.type},model:{value:e.formData.business_scope,callback:function(t){e.$set(e.formData,"business_scope",t)},expression:"formData.business_scope"}})],1)],1):e._e(),"2"===e.formData.aptitude?t("div",[t("el-form-item",{attrs:{label:"法人代表",prop:"legalRepresentative"}},[t("el-input",{staticClass:"form-item-w",attrs:{disabled:"detail"===e.type},model:{value:e.formData.legalRepresentative,callback:function(t){e.$set(e.formData,"legalRepresentative",t)},expression:"formData.legalRepresentative"}})],1),t("el-form-item",{attrs:{label:"住所",prop:"domicile"}},[t("el-input",{staticClass:"form-item-w",attrs:{disabled:"detail"===e.type},model:{value:e.formData.domicile,callback:function(t){e.$set(e.formData,"domicile",t)},expression:"formData.domicile"}})],1),t("el-form-item",{attrs:{label:"经营场所",prop:"businessPremises"}},[t("el-input",{staticClass:"form-item-w",attrs:{disabled:"detail"===e.type},model:{value:e.formData.businessPremises,callback:function(t){e.$set(e.formData,"businessPremises",t)},expression:"formData.businessPremises"}})],1),t("el-form-item",{attrs:{label:"主体业态",prop:"mainFormOfBusiness"}},[t("el-input",{staticClass:"form-item-w",attrs:{disabled:"detail"===e.type},model:{value:e.formData.mainFormOfBusiness,callback:function(t){e.$set(e.formData,"mainFormOfBusiness",t)},expression:"formData.mainFormOfBusiness"}})],1),t("el-form-item",{attrs:{label:"经营项目",prop:"businessProject"}},[t("el-input",{staticClass:"form-item-w",attrs:{disabled:"detail"===e.type},model:{value:e.formData.businessProject,callback:function(t){e.$set(e.formData,"businessProject",t)},expression:"formData.businessProject"}})],1),t("el-form-item",{attrs:{label:"许可证编号",prop:"licenseNumber"}},[t("el-input",{staticClass:"form-item-w",attrs:{disabled:"detail"===e.type},model:{value:e.formData.licenseNumber,callback:function(t){e.$set(e.formData,"licenseNumber",t)},expression:"formData.licenseNumber"}})],1),t("el-form-item",{attrs:{label:"举报电话",prop:"tipOffTelephone"}},[t("el-input",{staticClass:"form-item-w",attrs:{disabled:"detail"===e.type},model:{value:e.formData.tipOffTelephone,callback:function(t){e.$set(e.formData,"tipOffTelephone",t)},expression:"formData.tipOffTelephone"}})],1),t("el-form-item",{attrs:{label:"签发机关",prop:"issuingAuthority"}},[t("el-input",{staticClass:"form-item-w",attrs:{disabled:"detail"===e.type},model:{value:e.formData.issuingAuthority,callback:function(t){e.$set(e.formData,"issuingAuthority",t)},expression:"formData.issuingAuthority"}})],1)],1):e._e(),t("el-form-item",{attrs:{label:"有效期限",prop:"expiry_date"}},[t("el-date-picker",{staticClass:"form-item-w",attrs:{format:"yyyy-MM-dd","value-format":"yyyy-MM-dd",type:"daterange","start-placeholder":"开始日期","end-placeholder":"结束日期",disabled:"detail"===e.type},model:{value:e.formData.expiry_date,callback:function(t){e.$set(e.formData,"expiry_date",t)},expression:"formData.expiry_date"}})],1),t("el-form-item",{attrs:{label:"资质照片",prop:"imageFileList"}},[t("div",{staticClass:"red m-b-10"},[e._v("仅支持jpg、png、bmp格式，大小不超过2M")]),t("el-upload",{class:{"upload-img-wrapper":!0,"hide-upload":e.formData.imageFileList.length>0},attrs:{drag:"",data:e.uploadParams,action:e.actionUrl,multiple:!1,"file-list":e.formData.imageFileList,"list-type":"picture-card","on-change":e.handelChange,"on-success":e.handleImgSuccess,"before-upload":e.beforeImgUpload,limit:1,headers:e.headersOpts,disabled:"detail"===e.type},scopedSlots:e._u([{key:"file",fn:function(a){var r=a.file;return t("div",{directives:[{name:"loading",rawName:"v-loading",value:"uploading"===r.status,expression:"file.status==='uploading'"}],attrs:{"element-loading-text":"上传中"}},[e.formData.imageFileList[0]&&e.formData.imageFileList[0].public_url?t("el-image",{attrs:{src:e.formData.imageFileList[0].public_url,fit:"fill"}}):e._e(),e.formData.imageFileList[0]&&e.formData.imageFileList[0].public_url?t("span",{staticClass:"el-upload-list__item-actions"},[t("span",{staticClass:"el-upload-list__item-preview",on:{click:function(t){return e.handlePictureCardPreview(e.formData.imageFileList[0].public_url)}}},[t("i",{staticClass:"el-icon-zoom-in"})]),t("span",{staticClass:"el-upload-list__item-delete",on:{click:function(t){return e.handleImgRemove(r)}}},[t("i",{staticClass:"el-icon-delete"})])]):e._e()],1)}}])},[e.formData.imageFileList.length<1?t("i",{staticClass:"el-icon-plus"}):e._e()])],1)],1)],1)},i=[],n=a("8309"),l=a("ed08"),o=a("2f62"),s=a("5a0c"),u=a.n(s);function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function f(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */f=function(){return t};var e,t={},a=Object.prototype,r=a.hasOwnProperty,i=Object.defineProperty||function(e,t,a){e[t]=a.value},n="function"==typeof Symbol?Symbol:{},l=n.iterator||"@@iterator",o=n.asyncIterator||"@@asyncIterator",s=n.toStringTag||"@@toStringTag";function u(e,t,a){return Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,a){return e[t]=a}}function m(e,t,a,r){var n=t&&t.prototype instanceof v?t:v,l=Object.create(n.prototype),o=new N(r||[]);return i(l,"_invoke",{value:P(e,a,o)}),l}function p(e,t,a){try{return{type:"normal",arg:e.call(t,a)}}catch(e){return{type:"throw",arg:e}}}t.wrap=m;var d="suspendedStart",h="suspendedYield",b="executing",y="completed",g={};function v(){}function _(){}function w(){}var D={};u(D,l,(function(){return this}));var O=Object.getPrototypeOf,E=O&&O(O(F([])));E&&E!==a&&r.call(E,l)&&(D=E);var k=w.prototype=v.prototype=Object.create(D);function L(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function T(e,t){function a(i,n,l,o){var s=p(e[i],e,n);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==c(f)&&r.call(f,"__await")?t.resolve(f.__await).then((function(e){a("next",e,l,o)}),(function(e){a("throw",e,l,o)})):t.resolve(f).then((function(e){u.value=e,l(u)}),(function(e){return a("throw",e,l,o)}))}o(s.arg)}var n;i(this,"_invoke",{value:function(e,r){function i(){return new t((function(t,i){a(e,r,t,i)}))}return n=n?n.then(i,i):i()}})}function P(t,a,r){var i=d;return function(n,l){if(i===b)throw Error("Generator is already running");if(i===y){if("throw"===n)throw l;return{value:e,done:!0}}for(r.method=n,r.arg=l;;){var o=r.delegate;if(o){var s=x(o,r);if(s){if(s===g)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===d)throw i=y,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=b;var u=p(t,a,r);if("normal"===u.type){if(i=r.done?y:h,u.arg===g)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(i=y,r.method="throw",r.arg=u.arg)}}}function x(t,a){var r=a.method,i=t.iterator[r];if(i===e)return a.delegate=null,"throw"===r&&t.iterator.return&&(a.method="return",a.arg=e,x(t,a),"throw"===a.method)||"return"!==r&&(a.method="throw",a.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var n=p(i,t.iterator,a.arg);if("throw"===n.type)return a.method="throw",a.arg=n.arg,a.delegate=null,g;var l=n.arg;return l?l.done?(a[t.resultName]=l.value,a.next=t.nextLoc,"return"!==a.method&&(a.method="next",a.arg=e),a.delegate=null,g):l:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,g)}function R(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function j(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function N(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(R,this),this.reset(!0)}function F(t){if(t||""===t){var a=t[l];if(a)return a.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,n=function a(){for(;++i<t.length;)if(r.call(t,i))return a.value=t[i],a.done=!1,a;return a.value=e,a.done=!0,a};return n.next=n}}throw new TypeError(c(t)+" is not iterable")}return _.prototype=w,i(k,"constructor",{value:w,configurable:!0}),i(w,"constructor",{value:_,configurable:!0}),_.displayName=u(w,s,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===_||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,u(e,s,"GeneratorFunction")),e.prototype=Object.create(k),e},t.awrap=function(e){return{__await:e}},L(T.prototype),u(T.prototype,o,(function(){return this})),t.AsyncIterator=T,t.async=function(e,a,r,i,n){void 0===n&&(n=Promise);var l=new T(m(e,a,r,i),n);return t.isGeneratorFunction(a)?l:l.next().then((function(e){return e.done?e.value:l.next()}))},L(k),u(k,s,"Generator"),u(k,l,(function(){return this})),u(k,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),a=[];for(var r in t)a.push(r);return a.reverse(),function e(){for(;a.length;){var r=a.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=F,N.prototype={constructor:N,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(j),!t)for(var a in this)"t"===a.charAt(0)&&r.call(this,a)&&!isNaN(+a.slice(1))&&(this[a]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var a=this;function i(r,i){return o.type="throw",o.arg=t,a.next=r,i&&(a.method="next",a.arg=e),!!i}for(var n=this.tryEntries.length-1;n>=0;--n){var l=this.tryEntries[n],o=l.completion;if("root"===l.tryLoc)return i("end");if(l.tryLoc<=this.prev){var s=r.call(l,"catchLoc"),u=r.call(l,"finallyLoc");if(s&&u){if(this.prev<l.catchLoc)return i(l.catchLoc,!0);if(this.prev<l.finallyLoc)return i(l.finallyLoc)}else if(s){if(this.prev<l.catchLoc)return i(l.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<l.finallyLoc)return i(l.finallyLoc)}}}},abrupt:function(e,t){for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var n=i;break}}n&&("break"===e||"continue"===e)&&n.tryLoc<=t&&t<=n.finallyLoc&&(n=null);var l=n?n.completion:{};return l.type=e,l.arg=t,n?(this.method="next",this.next=n.finallyLoc,g):this.complete(l)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.finallyLoc===e)return this.complete(a.completion,a.afterLoc),j(a),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.tryLoc===e){var r=a.completion;if("throw"===r.type){var i=r.arg;j(a)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,a,r){return this.delegate={iterator:F(t),resultName:a,nextLoc:r},"next"===this.method&&(this.arg=e),g}},t}function m(e,t,a,r,i,n,l){try{var o=e[n](l),s=o.value}catch(e){return void a(e)}o.done?t(s):Promise.resolve(s).then(r,i)}function p(e){return function(){var t=this,a=arguments;return new Promise((function(r,i){var n=e.apply(t,a);function l(e){m(n,r,i,l,o,"next",e)}function o(e){m(n,r,i,l,o,"throw",e)}l(void 0)}))}}function d(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,r)}return a}function h(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?d(Object(a),!0).forEach((function(t){b(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):d(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function b(e,t,a){return(t=y(t))in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function y(e){var t=g(e,"string");return"symbol"==c(t)?t:t+""}function g(e,t){if("object"!=c(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var r=a.call(e,t||"default");if("object"!=c(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var v={name:"addAptitudeDialog",props:{showdialog:Boolean,loading:Boolean,type:{type:String,default:"add"},direction:{type:String,default:"rtl"},wrapperClosable:{type:Boolean,default:!0},title:{type:String,default:"新建资质"},size:{type:[String,Number],default:720},showFooter:{type:Boolean,default:!0},infoData:{type:Object,default:function(){return{}}}},data:function(){return{isLoading:!1,formData:{aptitude:"",aptitude_type:"",business_scope:"",imageFileList:[],expiry_date:[],legalRepresentative:"",domicile:"",businessPremises:"",mainFormOfBusiness:"",businessProject:"",licenseNumber:"",tipOffTelephone:"",issuingAuthority:""},defaultFormDataRules:{name:[{required:!0,message:"请输入分类名称",trigger:"change"}],expiry_date:[{required:!0,message:"请选择有效期限",trigger:"change"}],imageFileList:[{required:!0,message:"请选择资质照片",trigger:"change"}]},formDataRules:{name:[{required:!0,message:"请输入分类名称",trigger:"change"}],expiry_date:[{required:!0,message:"请选择有效期限",trigger:"change"}],imageFileList:[{required:!0,message:"请选择资质照片",trigger:"change"}]},aptitudeList:n["APTITUDE_LIST"],uploadParams:{prefix:"inventoryImage"},actionUrl:"/api/background/file/upload",headersOpts:{TOKEN:Object(l["B"])()}}},computed:h(h({},Object(o["c"])(["userInfo"])),{},{visible:{get:function(){return this.showdialog},set:function(e){this.$emit("update:showdialog",e)}}}),watch:{showdialog:function(e){e&&this.initLoad()}},created:function(){},mounted:function(){},methods:{initLoad:function(){var e=this;return p(f().mark((function t(){var a;return f().wrap((function(t){while(1)switch(t.prev=t.next){case 0:"modify"===e.type||"detail"===e.type?(a=Object(l["f"])(e.infoData),e.infoData.imgUrl&&e.infoData.imgUrl.length>0?a.imageFileList=e.infoData.imgUrl.map((function(e){return{url:e,name:e,status:"success",uid:e,public_url:e}})):a.imageFileList=[],e.formData=a):e.formData={aptitude:"",code:"",aptitude_type:"",business_scope:"",imageFileList:[],expiry_date:[],legalRepresentative:"",domicile:"",businessPremises:"",mainFormOfBusiness:"",businessProject:"",licenseNumber:"",tipOffTelephone:"",issuingAuthority:""};case 1:case"end":return t.stop()}}),t)})))()},handelChange:function(e,t){this.uploadParams.key=this.uploadParams.prefix+"_"+(new Date).getTime()+Math.floor(150*Math.random())+".png"},handleImgSuccess:function(e,t,a){0===e.code?this.formData.imageFileList=a.map((function(a){return a.uid===t.uid&&(a.public_url=e.data.public_url),a})):this.$message.error(e.msg)},handleImgRemove:function(e,t){if("detail"!==this.type){var a=this.formData.imageFileList.findIndex((function(t){return t.url===e.url}));this.formData.imageFileList.splice(a,1)}},beforeImgUpload:function(e){var t=[".jpeg",".jpg",".png",".bmp"],a=e.size/1024/1024>2;return t.includes(Object(l["A"])(e.name))?a?(this.$message.error("上传图片大小不能超过 2MB!"),!1):void 0:(this.$message.error("上传图片只能是PNG/JPG/bmp格式!"),!1)},clickConfirmHandle:function(){var e=this,t=h({id:(new Date).getTime(),name:this.formData.name,fileType:this.formData.aptitude,upLoadTime:u()().format("YYYY-MM-DD HH:mm:ss"),editTime:u()().format("YYYY-MM-DD HH:mm:ss"),operator:this.userInfo.member_name},this.formData);delete t.imageFileList;var a=[];this.formData.imageFileList.forEach((function(e){e.public_url&&a.push(e.public_url)})),t.imgUrl=a,this.$refs.formRef.validate((function(a){if(a){if(e.isLoading)return;e.isLoading=!0,e.$emit("clickConfirm",t,e.type),e.visible=!1}else e.$message.error("添加失败")}))},clickCancleHandle:function(){this.visible=!1},handlerClose:function(e){this.formData={aptitude:"",code:"",aptitude_type:"",business_scope:"",imageFileList:[],expiry_date:[],legalRepresentative:"",domicile:"",businessPremises:"",mainFormOfBusiness:"",businessProject:"",licenseNumber:"",tipOffTelephone:"",issuingAuthority:""},this.$refs.formRef&&this.$refs.formRef.resetFields(),this.isLoading=!1},handlePictureCardPreview:function(e){this.$emit("showImgViewer",e)},setRules:function(){var e=this,t=Object(l["f"])(this.defaultFormDataRules);switch(this.formData.aptitude){case"1":t=h(h({},t),{},{aptitude_type:[{required:!0,message:"请输入",trigger:["change","blur"]}],business_scope:[{required:!0,message:"请输入",trigger:["change","blur"]}]});break;case"2":t=h(h({},t),{},{legalRepresentative:[{required:!0,message:"请输入",trigger:"change"}],domicile:[{required:!0,message:"请输入",trigger:"change"}],businessPremises:[{required:!0,message:"请输入",trigger:"change"}],mainFormOfBusiness:[{required:!0,message:"请输入",trigger:"change"}],businessProject:[{required:!0,message:"请输入",trigger:"change"}],licenseNumber:[{required:!0,message:"请输入",trigger:"change"}],tipOffTelephone:[{required:!0,message:"请输入",trigger:"change"}],issuingAuthority:[{required:!0,message:"请输入",trigger:"change"}]});break}this.formDataRules=Object(l["f"])(t),"add"===this.type&&setTimeout((function(){e.$refs.formRef.clearValidate()}),10)}}},_=v,w=(a("32ab"),a("2877")),D=Object(w["a"])(_,r,i,!1,null,null,null);t["default"]=D.exports},8309:function(e,t,a){"use strict";a.r(t),a.d(t,"INVENTORY_TYPE",(function(){return i})),a.d(t,"ENTRY_TYPE",(function(){return n})),a.d(t,"OUT_TYPE",(function(){return l})),a.d(t,"APPROVAL_DAIBAN_SEARCHFORMSETTINGS",(function(){return o})),a.d(t,"APPROVAL_DAIBAN_TABLESETTINGS",(function(){return s})),a.d(t,"APPROVAL_YIBAN_SEARCHFORMSETTINGS",(function(){return u})),a.d(t,"APPROVAL_YIBAN_TABLESETTINGS",(function(){return c})),a.d(t,"APPROVAL_DETAIL_TABLESETTINGS",(function(){return f})),a.d(t,"CYCLE_TYPE_LIST",(function(){return m})),a.d(t,"QUARTER_LIST",(function(){return p})),a.d(t,"APTITUDE_LIST",(function(){return d})),a.d(t,"DELIVERY_STATUS",(function(){return h}));var r=a("ed08"),i=[{label:"采购入库",value:"PURCHASE_ENTRY"},{label:"借调入库",value:"BORROW_ENTRY"},{label:"导入入库",value:"IMPORT_ENTRY"},{label:"消耗出库",value:"EXPEND_EXIT"},{label:"借调出库",value:"BORROW_EXIT"},{label:"过期出库",value:"OVERDUE_EXIT"},{label:"退货出库",value:"REFUND_EXIT"}],n=[{label:"采购入库",value:"PURCHASE_ENTRY"},{label:"借调入库",value:"BORROW_ENTRY"},{label:"导入入库",value:"IMPORT_ENTRY"},{label:"赠送物资",value:"BESTOW_ENTRY"}],l=[{label:"过期出库",value:"OVERDUE_EXIT"},{label:"损耗出库",value:"EXPEND_EXIT"},{label:"借调出库",value:"BORROW_EXIT"},{label:"退货出库",value:"REFUND_EXIT"}],o={select_time:{type:"daterange",format:"yyyy-MM-dd",label:"申请时间",clearable:!1,value:Object(r["y"])(7)},name:{type:"input",label:"申请人",value:"",placeholder:"请输入"},trade_no:{type:"input",label:"单据编号",value:"",placeholder:"请输入"}},s=[{label:"申请时间",key:"create_time"},{label:"申请人",key:"name"},{label:"单据编号",key:"trade_no"},{label:"单据类型",key:"approve_type_alias"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right"}],u={date_type:{type:"select",label:"",value:"create_time",maxWidth:"100px",placeholder:"请选择",dataList:[{label:"申请时间",value:"create_time"},{label:"审批时间",value:"approve_time"}]},select_time:{type:"daterange",format:"yyyy-MM-dd",label:"",clearable:!1,value:Object(r["y"])(7)},name:{type:"input",label:"申请人",value:"",placeholder:"请输入"},trade_no:{type:"input",label:"单据编号",value:"",placeholder:"请输入"}},c=[{label:"申请时间",key:"create_time"},{label:"申请人",key:"name"},{label:"单据编号",key:"trade_no"},{label:"审批结果",key:"approve_status_alias"},{label:"审批意见",key:"reject_reason"},{label:"审批时间",key:"approve_time"},{label:"审批详情",key:"record_list",type:"slot",slotName:"record"},{label:"审批项进程",key:"deal_status_alias"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right"}],f={purchase_info:[{label:"物资名称",key:"name"},{label:"数量",key:"count"},{label:"单位",key:"unit_name"},{label:"参考单价",key:"ref_unit_price",type:"money"},{label:"合计",key:"total",type:"money"},{label:"供应商",key:"supplier_manage_name"}],entry_info:[{label:"物资名称",key:"materials_name"},{label:"入库数量",key:"expected_entry_count"},{label:"单位",key:"unit_name"},{label:"参考单价",key:"ref_unit_price",type:"money"},{label:"入库价",key:"entry_price",type:"money"},{label:"供应商",key:"supplier_manage_name"}],exit_info:[{label:"物资名称",key:"materials_name"},{label:"出库数量",key:"count"},{label:"单位",key:"unit_name"},{label:"供应商",key:"supplier_manage_name"}],return_info:[{label:"物资名称",key:"materials_name"},{label:"出库数量",key:"refund_count"},{label:"单位",key:"unit_name"},{label:"入库价",key:"ref_unit_price",type:"money"},{label:"退货金额",key:"refund_fee",type:"money"}],subscribe_info:[{label:"物资名称",key:"materials_name"},{label:"申购数量",key:"count"},{label:"单位",key:"unit_name"}]},m=[{label:"按天",value:"DAY"},{label:"按周",value:"WEEK"},{label:"按月",value:"MONTH"}],p=[{label:"第1季度",value:"1"},{label:"第2季度",value:"2"},{label:"第3季度",value:"3"},{label:"第4季度",value:"4"}],d=[{label:"营业执照",value:"1"},{label:"食品经营许可证",value:"2"},{label:"食品生产许可证",value:"3"}],h=[{label:"待配送",value:"wait_delivery"},{label:"配送中",value:"delivering"},{label:"货物送达待确认",value:"arrive"},{label:"货物送达已确认",value:"confirmed"}]},ab62:function(e,t,a){}}]);