(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-inventory-components-related-document-DeliveryOrder","purchase_order_list","view-merchant-inventory-components-related-document-DriverInformation","view-merchant-inventory-components-related-document-PurchaseRelatedDocument","view-merchant-inventory-components-related-document-VehicleInformation"],{1862:function(t,e,r){"use strict";r.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"delivery-order-box"},[e("div",{staticClass:"info-wrap"},[e("div",{staticClass:"title"},[t._v("基本信息")]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("供应商名称：")]),t._v(t._s(t.infoData.supplier_manage_name))]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("单据编号：")]),t._v(t._s(t.detailData.trade_no))]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("配送日期：")]),t._v(t._s(t.detailData.delivery_date))]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("预计送达时间：")]),t._v(t._s(t.detailData.expect_arrival_date))]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("配送状态：")]),t._v(t._s(t.detailData.order_status_alias))]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("创建时间：")]),t._v(t._s(t.detailData.create_time))])]),e("div",{staticClass:"info-wrap"},[e("div",{staticClass:"title"},[t._v("配送信息")]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("配送温度：")]),t._v(t._s(t.detailData.delivery_temperature)+"°c")])]),e("DriverInformation",{attrs:{"driver-list":t.detailData.driver_info}}),e("VehicleInformation",{attrs:{"vehicle-list":t.detailData.vehicle_info}}),e("div",{staticClass:"info-wrap"},[e("div",{staticClass:"title"},[t._v("物资信息")]),"detail"!==t.type?e("div",[t._v("合计金额：￥"+t._s(t.totalPrice))]):t._e(),e("el-table",{ref:"tableData",staticStyle:{width:"100%"},attrs:{data:t.detailData.ingredient_data,stripe:"",size:"mini","max-height":"600","header-row-class-name":"ps-table-header-row"}},t._l(t.tableSettings,(function(t){return e("table-column",{key:t.key,attrs:{col:t}})})),1)],1)],1)},n=[],a=r("ed08"),o=r("2c74"),s=r("d8a4"),c=r("da92");function l(t){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},l(t)}function u(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */u=function(){return e};var t,e={},r=Object.prototype,i=r.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},o=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",c=a.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function d(t,e,r,i){var a=e&&e.prototype instanceof w?e:w,o=Object.create(a.prototype),s=new I(i||[]);return n(o,"_invoke",{value:O(t,r,s)}),o}function v(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=d;var h="suspendedStart",p="suspendedYield",m="executing",y="completed",_={};function w(){}function g(){}function b(){}var x={};f(x,o,(function(){return this}));var L=Object.getPrototypeOf,C=L&&L(L(P([])));C&&C!==r&&i.call(C,o)&&(x=C);var k=b.prototype=w.prototype=Object.create(x);function D(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function V(t,e){function r(n,a,o,s){var c=v(t[n],t,a);if("throw"!==c.type){var u=c.arg,f=u.value;return f&&"object"==l(f)&&i.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,o,s)}),(function(t){r("throw",t,o,s)})):e.resolve(f).then((function(t){u.value=t,o(u)}),(function(t){return r("throw",t,o,s)}))}s(c.arg)}var a;n(this,"_invoke",{value:function(t,i){function n(){return new e((function(e,n){r(t,i,e,n)}))}return a=a?a.then(n,n):n()}})}function O(e,r,i){var n=h;return function(a,o){if(n===m)throw Error("Generator is already running");if(n===y){if("throw"===a)throw o;return{value:t,done:!0}}for(i.method=a,i.arg=o;;){var s=i.delegate;if(s){var c=S(s,i);if(c){if(c===_)continue;return c}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if(n===h)throw n=y,i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);n=m;var l=v(e,r,i);if("normal"===l.type){if(n=i.done?y:p,l.arg===_)continue;return{value:l.arg,done:i.done}}"throw"===l.type&&(n=y,i.method="throw",i.arg=l.arg)}}}function S(e,r){var i=r.method,n=e.iterator[i];if(n===t)return r.delegate=null,"throw"===i&&e.iterator.return&&(r.method="return",r.arg=t,S(e,r),"throw"===r.method)||"return"!==i&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+i+"' method")),_;var a=v(n,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,_;var o=a.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,_):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,_)}function j(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(j,this),this.reset(!0)}function P(e){if(e||""===e){var r=e[o];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,a=function r(){for(;++n<e.length;)if(i.call(e,n))return r.value=e[n],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(l(e)+" is not iterable")}return g.prototype=b,n(k,"constructor",{value:b,configurable:!0}),n(b,"constructor",{value:g,configurable:!0}),g.displayName=f(b,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,f(t,c,"GeneratorFunction")),t.prototype=Object.create(k),t},e.awrap=function(t){return{__await:t}},D(V.prototype),f(V.prototype,s,(function(){return this})),e.AsyncIterator=V,e.async=function(t,r,i,n,a){void 0===a&&(a=Promise);var o=new V(d(t,r,i,n),a);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},D(k),f(k,c,"Generator"),f(k,o,(function(){return this})),f(k,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var i in e)r.push(i);return r.reverse(),function t(){for(;r.length;){var i=r.pop();if(i in e)return t.value=i,t.done=!1,t}return t.done=!0,t}},e.values=P,I.prototype={constructor:I,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(E),!e)for(var r in this)"t"===r.charAt(0)&&i.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function n(i,n){return s.type="throw",s.arg=e,r.next=i,n&&(r.method="next",r.arg=t),!!n}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],s=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var c=i.call(o,"catchLoc"),l=i.call(o,"finallyLoc");if(c&&l){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&i.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var a=n;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=t,o.arg=e,a?(this.method="next",this.next=a.finallyLoc,_):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),_},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),E(r),_}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var i=r.completion;if("throw"===i.type){var n=i.arg;E(r)}return n}}throw Error("illegal catch attempt")},delegateYield:function(e,r,i){return this.delegate={iterator:P(e),resultName:r,nextLoc:i},"next"===this.method&&(this.arg=t),_}},e}function f(t,e){return m(t)||p(t,e)||v(t,e)||d()}function d(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function v(t,e){if(t){if("string"==typeof t)return h(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?h(t,e):void 0}}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,i=Array(e);r<e;r++)i[r]=t[r];return i}function p(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var i,n,a,o,s=[],c=!0,l=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(i=a.call(r)).done)&&(s.push(i.value),s.length!==e);c=!0);}catch(t){l=!0,n=t}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw n}}return s}}function m(t){if(Array.isArray(t))return t}function y(t,e,r,i,n,a,o){try{var s=t[a](o),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(i,n)}function _(t){return function(){var e=this,r=arguments;return new Promise((function(i,n){var a=t.apply(e,r);function o(t){y(a,i,n,o,s,"next",t)}function s(t){y(a,i,n,o,s,"throw",t)}o(void 0)}))}}var w={name:"DeliveryOrderBox",props:{type:{type:String,default:"detail"},params:{type:Object,default:function(){}},api:{type:String,default:"apiBackgroundDrpVendorDataVendorDeliveryDetailListPost"},infoData:{type:Object,default:function(){}}},components:{VehicleInformation:o["default"],DriverInformation:s["default"]},data:function(){return{isLoading:!1,tableData:[],tableSettings:[{label:"物资名称",key:"materials_name"},{label:"配送数量",key:"purchase_count"},{label:"成本价",key:"unit_price"},{label:"合计金额",key:"total_price"},{label:"保质期",key:"valid_date"}],detailData:{},totalPrice:0}},computed:{},watch:{},mounted:function(){this.initLoad()},methods:{initLoad:function(){this.getDataInfo()},getDataInfo:function(){var t=this;return _(u().mark((function e(){var r,i,n,o,s,l;return u().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.isLoading){e.next=2;break}return e.abrupt("return");case 2:if(r=t.api,t.api||"detail"!==t.type||(r="apiBackgroundDrpVendorDataVendorDeliveryDetailListPost"),r){e.next=6;break}return e.abrupt("return",t.$message.error("缺少参数，请检查！"));case 6:return t.isLoading=!0,e.next=9,t.$to(t.$apis[r](t.params));case 9:if(i=e.sent,n=f(i,2),o=n[0],s=n[1],t.isLoading=!1,t.detailData={},!o){e.next=18;break}return t.$message.error(o.message),e.abrupt("return");case 18:0===s.code?(l=s.data||{},t.totalPrice=0,null!==l&&void 0!==l&&l.ingredient_data&&(l.ingredient_data=l.ingredient_data.map((function(e){return t.totalPrice=c["a"].plus(t.totalPrice,e.total_price),e.purchase_count=e.purchase_count+e.purchase_unit,e.unit_price="￥"+Object(a["i"])(e.unit_price)+"/"+e.purchase_unit,e.total_price="￥"+Object(a["i"])(e.total_price),e.valid_date=e.start_valid_date+"-"+e.end_valid_date,e}))),t.totalPrice=Object(a["i"])(t.totalPrice),t.detailData=l||{}):t.$message.error(s.msg);case 19:case"end":return e.stop()}}),e)})))()}}},g=w,b=(r("4b05"),r("2877")),x=Object(b["a"])(g,i,n,!1,null,"1bafcdeb",null);e["default"]=x.exports},"2c74":function(t,e,r){"use strict";r.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"vehicleInformation-box"},[t._l(t.vehicleList,(function(r,i){return e("div",{key:i,staticClass:"vehicle-item"},[e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("车辆类型：")]),t._v(t._s(r.car_type_alias)+" ")]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("车牌号：")]),t._v(t._s(r.plate_number)+" ")]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("车辆图片：")]),e("div",{staticClass:"form-img-box"},t._l(r.car_img,(function(i,n){return e("el-image",{key:i,staticClass:"detault-img m-r-6 pointer",attrs:{src:i,fit:"contain"},on:{click:function(e){return t.clickViewerHandler(r.car_img,n)}}})})),1)]),t.showDivider?e("el-divider"):t._e()],1)})),e("image-viewer",{attrs:{"initial-index":t.imgIndex,"z-index":3e3,"on-close":t.closeViewer,"preview-src-list":t.previewSrcList},model:{value:t.showViewer,callback:function(e){t.showViewer=e},expression:"showViewer"}})],2)},n=[],a={name:"VehicleInformation",props:{vehicleList:{type:Array,default:function(){return[]}},showDivider:{type:Boolean,default:!1}},data:function(){return{imgIndex:0,previewSrcList:[],showViewer:!1}},computed:{},watch:{},created:function(){},mounted:function(){},methods:{clickViewerHandler:function(t,e){this.previewSrcList=t||[],this.imgIndex=e,this.showViewer=!0},closeViewer:function(){this.showViewer=!1}}},o=a,s=(r("5cc2"),r("2877")),c=Object(s["a"])(o,i,n,!1,null,"6526e0fe",null);e["default"]=c.exports},"47b5":function(t,e,r){},"4b05":function(t,e,r){"use strict";r("4b8b")},"4b8b":function(t,e,r){},"5cc2":function(t,e,r){"use strict";r("47b5")},"788a":function(t,e,r){"use strict";r("eeb4")},d8a4:function(t,e,r){"use strict";r.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"driverInformation-box"},[t._l(t.driverList,(function(r,i){return e("div",{key:i,staticClass:"driver-item"},[e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("司机姓名：")]),t._v(t._s(r.name)+" ")]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("联系方式：")]),t._v(t._s(r.phone)+" ")]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("证件信息：")]),e("div",{staticClass:"form-img-box"},[t._l(r.health_certificate,(function(i,n){return e("div",{key:i,staticClass:"form-img"},[e("el-image",{staticClass:"detault-img m-r-6 pointer",attrs:{src:i,fit:"contain"},on:{click:function(e){return t.clickViewerHandler(r.health_certificate,n)}}}),e("div",{staticClass:"text-center"},[t._v("健康证")])],1)})),t._l(r.driving_licence,(function(i,n){return e("div",{key:i,staticClass:"form-img"},[e("el-image",{staticClass:"detault-img m-r-6 pointer",attrs:{src:i,fit:"contain"},on:{click:function(e){return t.clickViewerHandler(r.driving_licence,n)}}}),e("div",{staticClass:"text-center"},[t._v("驾驶证")])],1)}))],2)]),t.showDivider?e("el-divider"):t._e()],1)})),e("image-viewer",{attrs:{"initial-index":t.imgIndex,"z-index":3e3,"on-close":t.closeViewer,"preview-src-list":t.previewSrcList},model:{value:t.showViewer,callback:function(e){t.showViewer=e},expression:"showViewer"}})],2)},n=[],a={name:"DriverInformation",props:{driverList:{type:Array,default:function(){return[]}},showDivider:{type:Boolean,default:!1}},data:function(){return{imgIndex:0,previewSrcList:[],showViewer:!1}},computed:{},watch:{},created:function(){},mounted:function(){},methods:{clickViewerHandler:function(t,e){this.previewSrcList=t||[],this.imgIndex=e,this.showViewer=!0},closeViewer:function(){this.showViewer=!1}}},o=a,s=(r("788a"),r("2877")),c=Object(s["a"])(o,i,n,!1,null,"51d05e80",null);e["default"]=c.exports},eeb4:function(t,e,r){}}]);