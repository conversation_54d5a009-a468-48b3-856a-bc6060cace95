(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-menu-admin-nutritionAnalysis","add_meal_month_recipes"],{"86aa":function(e,n,t){"use strict";t.r(n);var i=function(){var e=this,n=e._self._c;return n("div",{staticClass:"container-wrapper"},[n("div",{staticClass:"table-wrapper p-t-20"},[n("menu-nutrition-analysis",{attrs:{menuType:e.menu_type,menuId:e.menu_id}})],1)])},a=[],u=t("f60f"),s={name:"AddWeekRecipesSeparate",data:function(){return{menu_type:"",menu_id:""}},components:{menuNutritionAnalysis:u["default"]},created:function(){this.initLoad()},methods:{initLoad:function(){this.menu_type=this.$route.query.menu_type,this.menu_id=String(this.$route.query.id)}}},r=s,m=t("2877"),o=Object(m["a"])(r,i,a,!1,null,"4b9e8c12",null);n["default"]=o.exports}}]);