(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-login","view-login-components-DoubleFactor","view-login-components-SelectCompany"],{"12ed":function(e,t,r){"use strict";r("f4e3")},"1d61":function(e,t,r){},"577f":function(e,t,r){},"9ed6":function(e,t,r){"use strict";r.r(t);var o=function(){var e=this,t=e._self._c;return t("div",{attrs:{id:"login-main"}},[t("div",{staticClass:"login-wrapper"},[e._m(0),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loginFormState.loading,expression:"loginFormState.loading"}],staticClass:"login-form"},[t("div",{staticClass:"form-wrapper"},[t("div",{staticClass:"verify-pop"},[t("verify-code",{ref:"verifyCode",attrs:{visible:e.isShowVerify,"is-number":!0},on:{success:e.verifySuccess,refresh:e.verifyRefresh}})],1),t("div",["qrcode"===e.loginType?t("div",{staticClass:"form-top"},[t("div",{staticClass:"welcome-text",staticStyle:{"margin-left":"100px"}},[e._v("扫码登录")]),t("img",{staticClass:"code-img",attrs:{src:r("5526"),alt:"login-wechat-code"},on:{click:function(t){e.loginType="account"}}})]):"forgetPwd"===e.loginType?t("div",{staticClass:"form-top"},[t("div",{staticClass:"welcome-text"},[e._v("找回密码")])]):"changePwd"===e.loginType?t("div",{staticClass:"form-top"},[t("div",{staticClass:"welcome-text"},[e._v("修改密码")])]):t("div",{staticClass:"form-top"},[t("div",{staticClass:"welcome-text"},[e._v("欢迎登录")]),t("img",{staticClass:"code-img",attrs:{src:r("4f25"),alt:"login-wechat-code"},on:{click:function(t){e.loginType="qrcode"}}})])]),"qrcode"===e.loginType?t("div",{staticStyle:{height:"270px","margin-top":"20px",position:"relative",overflow:"hidden"}},[t("wx-login",{attrs:{appid:e.appid,scope:"snsapi_login",redirect_uri:e.redirect_uri,href:"data:text/css;base64,LmltcG93ZXJCb3h7CiB3aWR0aDphdXRvOwp9Ci5pbXBvd2VyQm94IC50aXRsZSB7CiBkaXNwbGF5OiBub25lOwp9Ci5pbXBvd2VyQm94IC5xcmNvZGUgewogYm9yZGVyOiBub25lOwogd2lkdGg6IDIwMHB4Owp9Ci5pbXBvd2VyQm94IC5zdGF0dXMgewp3aWR0aDoyMDBweDsKbWFyZ2luOiBhdXRvOwp0ZXh0LWFsaWduOiBjZW50ZXI7Cn0KLmltcG93ZXJCb3ggLnN0YXR1c19icm93c2VyIHA6bnRoLWNoaWxkKDIpewogZGlzcGxheTogbm9uZTsKfQouaW1wb3dlckJveCAuc3RhdHVzX3N1Y2MgLnN0YXR1c190eHQgcHsKIGRpc3BsYXk6IG5vbmU7Cn0KLmltcG93ZXJCb3ggLnN0YXR1c19mYWlsIC5zdGF0dXNfdHh0IHB7CiBkaXNwbGF5OiBub25lOwp9Cg=="}})],1):"changePwd"===e.loginType?t("div",[t("el-form",{ref:"changePwdFormRef",staticClass:"form",attrs:{model:e.loginFormState,rules:e.rules,"label-width":"0"}},[t("div",{staticStyle:{"margin-top":"40px"}},[t("el-form-item",{attrs:{prop:"oldPassword"}},[t("el-input",{attrs:{"show-password":"",maxlength:"46",placeholder:e.$t("placeholder.oldPassword"),clearable:""},model:{value:e.loginFormState.oldPassword,callback:function(t){e.$set(e.loginFormState,"oldPassword",t)},expression:"loginFormState.oldPassword"}})],1),t("el-form-item",{attrs:{prop:"newPassword"}},[t("el-input",{attrs:{"show-password":"",maxlength:"46",placeholder:e.$t("placeholder.changeNewPassword"),clearable:""},model:{value:e.loginFormState.newPassword,callback:function(t){e.$set(e.loginFormState,"newPassword",t)},expression:"loginFormState.newPassword"}})],1),t("el-form-item",{attrs:{prop:"checkPassword"}},[t("el-input",{attrs:{"show-password":"",maxlength:"46",placeholder:e.$t("placeholder.checkPassword"),clearable:""},model:{value:e.loginFormState.checkPassword,callback:function(t){e.$set(e.loginFormState,"checkPassword",t)},expression:"loginFormState.checkPassword"}})],1),t("el-form-item",[t("el-button",{staticClass:"login-btn",staticStyle:{"margin-top":"20px"},attrs:{loading:e.loginFormState.loading},on:{click:e.checkChangePwdValidate}},[e._v(" 确定 ")])],1),t("el-form-item",[t("el-button",{staticClass:"cancel-btn ps-plain-btn",on:{click:e.gotoQrcodeLogin}},[e._v("返回")])],1)],1)])],1):t("div",["forgetPwd"!=e.loginType?t("el-tabs",{on:{"tab-click":e.tabHandleClick},model:{value:e.loginType,callback:function(t){e.loginType=t},expression:"loginType"}},[t("el-tab-pane",{attrs:{label:"密码",name:"account"}}),t("el-tab-pane",{attrs:{label:"验证码",name:"sms"}})],1):e._e(),t("el-form",{ref:"loginFormRef",staticClass:"form",attrs:{model:e.loginFormState,rules:e.rules,"label-width":"0"}},["account"===e.loginType?t("div",[t("el-form-item",{staticClass:"account",attrs:{prop:"account"}},[t("el-input",{attrs:{maxlength:"48",placeholder:e.$t("placeholder.account"),clearable:"","prefix-icon":"el-icon-user"},on:{input:e.changeAccountHandle},model:{value:e.loginFormState.account,callback:function(t){e.$set(e.loginFormState,"account",t)},expression:"loginFormState.account"}})],1),t("el-form-item",{staticClass:"password",attrs:{prop:"password"}},[t("el-input",{attrs:{type:"password",maxlength:"46",placeholder:e.$t("placeholder.password"),clearable:"","prefix-icon":"el-icon-lock"},model:{value:e.loginFormState.password,callback:function(t){e.$set(e.loginFormState,"password","string"===typeof t?t.trim():t)},expression:"loginFormState.password"}})],1),t("el-form-item",{staticClass:"verify-code-box",attrs:{label:"",prop:"verifyCode"}},[t("el-input",{staticClass:"verify-code",attrs:{placeholder:"请输入图形验证码",autocomplete:"off"},model:{value:e.loginFormState.verifyCode,callback:function(t){e.$set(e.loginFormState,"verifyCode",t)},expression:"loginFormState.verifyCode"}}),t("el-image",{staticClass:"verify-code-img",attrs:{src:e.verifyUrl,fit:"contain"},on:{click:e.getVerifyCodeHandle}},[t("div",{staticClass:"el-image__error",attrs:{slot:"error"},on:{click:e.getVerifyCodeHandle},slot:"error"},[e._v("加载失败")])])],1),e.isExpire?t("span",{staticClass:"font-size-12",staticStyle:{color:"red"}},[e._v(e._s(e.tipText(0))),t("span",{staticStyle:{color:"#2694F1","text-decoration":"underline"}},[e._v(e._s(e.tipText(1)))])]):e._e(),t("el-form-item",[t("div",{staticClass:"password-tools"},[t("el-checkbox",{staticClass:"remember",model:{value:e.isRemember,callback:function(t){e.isRemember=t},expression:"isRemember"}},[e._v(" "+e._s(e.$t("login.rememberPassword"))+" ")]),t("span",{staticClass:"forget-password",on:{click:e.forgetHandle}},[e._v(" "+e._s(e.$t("login.forgetPassword"))+" ")])],1)]),t("el-form-item",{staticClass:"login"},[t("el-button",{staticClass:"login-btn",attrs:{loading:e.loginFormState.loading},on:{click:e.checkLoginFormHandle}},[e._v(" "+e._s(e.$t("login.loginBtn"))+" ")])],1),e.userErrorLogin?t("div",{staticStyle:{"text-align":"center","font-size":"14px",color:"red"}},[e._v(e._s(e.errorLoginMsg)),e.showChangePwdTip?t("a",{staticClass:"change-pwd-a",attrs:{src:"javascript:void"},on:{click:function(t){e.loginType="changePwd"}}},[e._v("修改密码")]):e._e()]):e._e()],1):e._e(),"sms"===e.loginType?t("div",[t("el-form-item",{staticClass:"phone",attrs:{prop:"phone"}},[t("el-input",{attrs:{maxlength:"48",placeholder:e.$t("placeholder.phone"),clearable:"","prefix-icon":"el-icon-phone"},model:{value:e.loginFormState.phone,callback:function(t){e.$set(e.loginFormState,"phone",t)},expression:"loginFormState.phone"}})],1),t("el-form-item",{staticClass:"phone-code",attrs:{prop:"smsCode"}},[t("verification-code",{attrs:{sendAuthCode:e.sendAuthCode,disabled:e.sendCodeDisabled,"reset-handle":e.resetHandle},on:{click:e.getVerCode},model:{value:e.loginFormState.smsCode,callback:function(t){e.$set(e.loginFormState,"smsCode",t)},expression:"loginFormState.smsCode"}})],1),t("el-form-item",{staticClass:"login"},[t("el-button",{staticClass:"login-btn",attrs:{loading:e.loginFormState.loading},on:{click:e.checkLoginFormHandle}},[e._v(" "+e._s(e.$t("login.loginBtn"))+" ")])],1)],1):e._e(),"forgetPwd"===e.loginType&&1===e.loginFormState.forgetStatus?t("div",{staticStyle:{"margin-top":"40px"}},[t("el-form-item",{staticClass:"phone",attrs:{prop:"phone"}},[t("el-input",{attrs:{maxlength:"48",placeholder:e.$t("placeholder.phone"),clearable:"","prefix-icon":"el-icon-phone"},on:{input:e.changePhoneHandle},model:{value:e.loginFormState.phone,callback:function(t){e.$set(e.loginFormState,"phone",t)},expression:"loginFormState.phone"}})],1),t("el-form-item",{staticClass:"phone-code",staticStyle:{"margin-bottom":"5px"},attrs:{prop:"smsCode"}},[t("verification-code",{attrs:{sendAuthCode:e.sendAuthCode,disabled:e.sendCodeDisabled,"reset-handle":e.resetHandle},on:{click:e.getVerCode},model:{value:e.loginFormState.smsCode,callback:function(t){e.$set(e.loginFormState,"smsCode",t)},expression:"loginFormState.smsCode"}})],1),t("div",{staticStyle:{"font-size":"12px",color:"#c0c4cc",margin:"20px 0"}},[e._v(" 无法获取短信验证码请联系管理员 ")]),t("el-form-item",[t("el-button",{staticClass:"login-btn",staticStyle:{"margin-top":"20px"},attrs:{loading:e.loginFormState.loading},on:{click:function(t){return e.checkPwdValidate(0)}}},[e._v(" 下一步 ")])],1),t("el-form-item",[t("el-button",{staticClass:"cancel-btn ps-plain-btn",on:{click:e.gotoQrcodeLogin}},[e._v("返回")])],1)],1):e._e(),"forgetPwd"===e.loginType&&0===e.loginFormState.forgetStatus?t("div",{staticStyle:{"margin-top":"40px"}},[t("el-form-item",{attrs:{prop:"newPassword"}},[t("el-input",{attrs:{"show-password":"",placeholder:e.$t("placeholder.newPassword"),clearable:""},model:{value:e.loginFormState.newPassword,callback:function(t){e.$set(e.loginFormState,"newPassword",t)},expression:"loginFormState.newPassword"}})],1),t("el-form-item",{attrs:{prop:"checkPassword"}},[t("el-input",{attrs:{"show-password":"",placeholder:e.$t("placeholder.checkPassword"),clearable:""},model:{value:e.loginFormState.checkPassword,callback:function(t){e.$set(e.loginFormState,"checkPassword",t)},expression:"loginFormState.checkPassword"}})],1),t("el-form-item",[t("el-button",{staticClass:"login-btn",staticStyle:{"margin-top":"20px"},attrs:{loading:e.loginFormState.loading},on:{click:function(t){return e.checkPwdValidate(1)}}},[e._v(" 完成 ")])],1),t("el-form-item",[t("el-button",{staticClass:"cancel-btn ps-plain-btn",on:{click:e.gotoQrcodeLogin}},[e._v("取消")])],1)],1):e._e()])],1)]),e.agreementList.length?t("div",{staticClass:"agreement m-t-20"},[e._v("登录即代表你同意 "),e._l(e.agreementList,(function(r){return t("span",{key:r.id},[e._v("《"),t("span",{staticClass:"blue pointer",on:{click:function(t){return e.gotoAgreement(r)}}},[e._v(e._s(r.agreement_type_alias))]),e._v("》")])}))],2):e._e()])]),t("select-company",{attrs:{show:e.showCompany,type:e.loginType,"company-list":e.dialogData,confirmhandle:e.dialogConfirmHandle,closehandle:e.dialogClosehandle}}),e.userInfo.is_double_factor?t("double-factor",{attrs:{userInfo:e.userInfo},on:{doubleConfirm:e.doubleConfirmHandle,doubleCancel:e.doubleCancelHandle}}):e._e()],1)},n=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"login-welcome"},[t("img",{staticClass:"welcome-logo",attrs:{src:r("e343"),alt:"login-welcome"}})])}],a=r("8237"),i=r.n(a),s=r("ed08"),c=r("365c"),l=r("bfde"),u=[{agreement_type_alias:"信息保护协议",agreement_type:"IPA"},{agreement_type_alias:"平台隐私政策",agreement_type:"MPA"},{agreement_type_alias:"平台服务协议",agreement_type:"MTS"}],d=r("b373"),f=r("a76d"),h=r("403c"),g=r("e762a");function p(e){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},p(e)}function m(e,t){return C(e)||b(e,t)||v(e,t)||y()}function y(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function v(e,t){if(e){if("string"==typeof e)return w(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?w(e,t):void 0}}function w(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,o=Array(t);r<t;r++)o[r]=e[r];return o}function b(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var o,n,a,i,s=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(o=a.call(r)).done)&&(s.push(o.value),s.length!==t);c=!0);}catch(e){l=!0,n=e}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(l)throw n}}return s}}function C(e){if(Array.isArray(e))return e}function x(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */x=function(){return t};var e,t={},r=Object.prototype,o=r.hasOwnProperty,n=Object.defineProperty||function(e,t,r){e[t]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",c=a.toStringTag||"@@toStringTag";function l(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,r){return e[t]=r}}function u(e,t,r,o){var a=t&&t.prototype instanceof v?t:v,i=Object.create(a.prototype),s=new $(o||[]);return n(i,"_invoke",{value:P(e,r,s)}),i}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var f="suspendedStart",h="suspendedYield",g="executing",m="completed",y={};function v(){}function w(){}function b(){}var C={};l(C,i,(function(){return this}));var S=Object.getPrototypeOf,k=S&&S(S(D([])));k&&k!==r&&o.call(k,i)&&(C=k);var L=b.prototype=v.prototype=Object.create(C);function F(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function _(e,t){function r(n,a,i,s){var c=d(e[n],e,a);if("throw"!==c.type){var l=c.arg,u=l.value;return u&&"object"==p(u)&&o.call(u,"__await")?t.resolve(u.__await).then((function(e){r("next",e,i,s)}),(function(e){r("throw",e,i,s)})):t.resolve(u).then((function(e){l.value=e,i(l)}),(function(e){return r("throw",e,i,s)}))}s(c.arg)}var a;n(this,"_invoke",{value:function(e,o){function n(){return new t((function(t,n){r(e,o,t,n)}))}return a=a?a.then(n,n):n()}})}function P(t,r,o){var n=f;return function(a,i){if(n===g)throw Error("Generator is already running");if(n===m){if("throw"===a)throw i;return{value:e,done:!0}}for(o.method=a,o.arg=i;;){var s=o.delegate;if(s){var c=E(s,o);if(c){if(c===y)continue;return c}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(n===f)throw n=m,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);n=g;var l=d(t,r,o);if("normal"===l.type){if(n=o.done?m:h,l.arg===y)continue;return{value:l.arg,done:o.done}}"throw"===l.type&&(n=m,o.method="throw",o.arg=l.arg)}}}function E(t,r){var o=r.method,n=t.iterator[o];if(n===e)return r.delegate=null,"throw"===o&&t.iterator.return&&(r.method="return",r.arg=e,E(t,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+o+"' method")),y;var a=d(n,t.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,y;var i=a.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,y):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function O(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function $(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function D(t){if(t||""===t){var r=t[i];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,a=function r(){for(;++n<t.length;)if(o.call(t,n))return r.value=t[n],r.done=!1,r;return r.value=e,r.done=!0,r};return a.next=a}}throw new TypeError(p(t)+" is not iterable")}return w.prototype=b,n(L,"constructor",{value:b,configurable:!0}),n(b,"constructor",{value:w,configurable:!0}),w.displayName=l(b,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,l(e,c,"GeneratorFunction")),e.prototype=Object.create(L),e},t.awrap=function(e){return{__await:e}},F(_.prototype),l(_.prototype,s,(function(){return this})),t.AsyncIterator=_,t.async=function(e,r,o,n,a){void 0===a&&(a=Promise);var i=new _(u(e,r,o,n),a);return t.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},F(L),l(L,c,"Generator"),l(L,i,(function(){return this})),l(L,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var o in t)r.push(o);return r.reverse(),function e(){for(;r.length;){var o=r.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},t.values=D,$.prototype={constructor:$,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(O),!t)for(var r in this)"t"===r.charAt(0)&&o.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(o,n){return s.type="throw",s.arg=t,r.next=o,n&&(r.method="next",r.arg=e),!!n}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],s=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=o.call(i,"catchLoc"),l=o.call(i,"finallyLoc");if(c&&l){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&o.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var a=n;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,y):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),O(r),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var o=r.completion;if("throw"===o.type){var n=o.arg;O(r)}return n}}throw Error("illegal catch attempt")},delegateYield:function(t,r,o){return this.delegate={iterator:D(t),resultName:r,nextLoc:o},"next"===this.method&&(this.arg=e),y}},t}function S(e,t,r,o,n,a,i){try{var s=e[a](i),c=s.value}catch(e){return void r(e)}s.done?t(c):Promise.resolve(c).then(o,n)}function k(e){return function(){var t=this,r=arguments;return new Promise((function(o,n){var a=e.apply(t,r);function i(e){S(a,o,n,i,s,"next",e)}function s(e){S(a,o,n,i,s,"throw",e)}i(void 0)}))}}var L={name:"Login",components:{Filings:l["a"],SelectCompany:d["default"],DoubleFactor:f["default"],VerifyCode:h["a"]},data:function(){var e=this,t=function(e,t,r){if(!t)return r(new Error("账号不能为空"));var o=/^\w{5,20}$/;o.test(t)?r():r(new Error("账号长度5到20位，只支持数字、大小写英文或下划线组合"))},r=function(e,t,r){var o=/^(?=.*[0-9])(?=.*[a-zA-Z])(.{8,20})$/;if(!t)return r(new Error("密码不能为空"));o.test(t)?r():r(new Error("密码长度8到20位，字母和数组组合"))},o=function(e,t,r){if(!t)return r(new Error("请再次输入密码"));var o=/^[0-9A-Za-z]{8,20}$/;o.test(t)?r():r(new Error("密码长度8~20位，英文加数字"))},n=function(t,r,o){if(!r)return e.sendCodeDisabled=!0,o(new Error("手机号不能为空"));var n=/^1[3456789]\d{9}$/;n.test(r)?(e.sendCodeDisabled=!1,o()):(e.sendCodeDisabled=!0,o(new Error("手机号格式错误")))};return{isRemember:!1,loginFormState:{account:"",password:"",phone:"",smsCode:"",forgetStatus:1,newPassword:"",checkPassword:"",loading:!1,verifyCode:""},redirect_uri:"",appid:"",rules:{account:[{validator:t,trigger:"change"}],password:[{validator:r,trigger:"change"}],oldPassword:[{validator:r,trigger:"change"}],newPassword:[{validator:r,trigger:"change"}],checkPassword:[{validator:o,trigger:"change"}],phone:[{validator:n,trigger:"change"}],smsCode:[{required:!0,message:"请输入验证码",trigger:"change"}]},redirect:void 0,otherQuery:{},messageHandle:null,loginType:"qrcode",sendCodeDisabled:!0,sendAuthCode:!0,userErrorLogin:!1,errorLoginMsg:"您还有5次尝试机会",errorLoginData:{},showChangePwdTip:!1,token:"",verifyUrl:"",agreementList:u,showCompany:!1,dialogData:[],companyId:"",expireText:"",isExpire:!1,loginData:{},userInfo:{},isShowVerify:!1,answerList:[],answer:""}},watch:{$route:{handler:function(e){var t=e.query;t&&(this.redirect=t.redirect,this.otherQuery=this.getOtherQuery(t)),t.code&&(this.loginType="qrcode",this.loginFormState.qrcode=t.code,this.loginHanlde()),t.token&&this.loginHanlde(this.$route.query.token)},immediate:!0},loginType:function(){"account"===this.loginType&&this.getVerifyCodeHandle()}},computed:{tipText:function(){var e=this;return function(t){var r=e.expireText.slice(0,18),o=e.expireText.slice(-10);return 0===t?r:o}}},created:function(){var e=this;return k(x().mark((function t(){var r,o;return x().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.determineBrowser(),e.$route.query.token&&e.loginHanlde(e.$route.query.token),r=Object(s["u"])("v4_login"),r)try{r=JSON.parse(decodeURIComponent(r)),e.loginFormState.account=r.account,e.loginFormState.password=r.password,e.isRemember=!0}catch(n){}if(o=Object(s["u"])("errorLoginData"),o)try{e.errorLoginData=JSON.parse(decodeURIComponent(o))}catch(n){}e.getWxLoginData(),e.getVerifyCodeHandle();case 8:case"end":return t.stop()}}),t)})))()},mounted:function(){document.addEventListener("keydown",this.enterKeydowHandle);try{this.$msgbox&&this.$msgbox.close&&this.$msgbox.close()}catch(e){}},methods:{enterKeydowHandle:function(e){13===e.keyCode&&this.checkLoginFormHandle()},checkLoginFormHandle:function(e){var t=this;"pushi1"===this.loginFormState.account?this.userErrorLogin=!0:this.userErrorLogin=!1,this.$refs.loginFormRef.validate((function(e){if(e)"sms"===t.loginType?t.getPhoneCompanyInfo():t.loginHanlde();else{if(t.messageHandle)return;t.messageHandle=t.$message({type:"error",message:"请检查登录信息！",onClose:function(){t.messageHandle=null}})}}))},loginHanlde:function(e){var t=this;return k(x().mark((function r(){var o,n,a,l,u,d;return x().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!t.loginFormState.loading){r.next=2;break}return r.abrupt("return");case 2:return t.loginFormState.loading=!0,t.isRemember?Object(s["S"])("v4_login",encodeURIComponent(JSON.stringify({account:t.loginFormState.account,password:t.loginFormState.password}))):Object(s["N"])("v4_login"),o={token:e,mode:t.loginType,company_id:t.companyId},t.agreementList.length&&(o.agreement_types=t.agreementList.map((function(e){return e.agreement_type}))),e||("account"===t.loginType?(o.username=t.loginFormState.account,o.password=t.loginFormState.password?i()(t.loginFormState.password):"",o.verify_code=t.loginFormState.verifyCode):"sms"===t.loginType?(o.phone=t.loginFormState.phone,o.sms_code=t.loginFormState.smsCode,o.code=t.answer):"qrcode"===t.loginType&&(o.code=t.loginFormState.qrcode)),r.next=9,Object(s["Z"])(c["a"].apiBackgroundLoginPost(o));case 9:if(n=r.sent,a=m(n,2),l=a[0],u=a[1],!l){r.next=16;break}return t.loginFormState.loading=!1,r.abrupt("return",t.$message.error(l.message));case 16:if(t.getVerifyCodeHandle(),t.loginData=u,0!==u.code&&5!==u.code){r.next=23;break}5===u.code&&(d={code:u.code,msg:u.msg},t.$store.dispatch("user/setStopServiceMsg",d)),"sms"!==t.loginType&&u.data.is_double_factor?(t.userInfo=u.data,Object(s["U"])("PWDV4TOKEN",u.data.token)):t.setLoginData(u),r.next=50;break;case 23:if(3!==u.code){r.next=29;break}u.data.msg=u.msg,Object(s["U"])("ISEXPIRECHANGEPWD","1"),t.setLoginData(u),r.next=50;break;case 29:if(4!==u.code){r.next=39;break}t.loginFormState.loading=!1,Object(s["U"])("PWDV4TOKEN",u.data.token),t.token=u.data.token,t.errorLoginMsg="登录密码已过期，请前往",t.showChangePwdTip=!0,t.userErrorLogin=!0,t.$message.error(u.msg),r.next=50;break;case 39:if(1!==u.code){r.next=47;break}return u.data&&void 0!==u.data.login_error_count&&(t.userErrorLogin=!0,t.errorLoginMsg="您还有".concat(5-u.data.login_error_count,"次尝试机会")),t.expireText=u.msg,t.isExpire=!0,t.loginFormState.loading=!1,r.abrupt("return");case 47:t.loginFormState.loading=!1,t.$message.error(u.msg),"qrcode"===t.loginType&&t.$router.replace({name:"Login"});case 50:case"end":return r.stop()}}),r)})))()},doubleConfirmHandle:function(){this.setLoginData(this.loginData)},doubleCancelHandle:function(){this.loginData={},this.userInfo={},this.isLoading=!1,this.loginFormState.loading=!1},setLoginData:function(e){var t=this;return k(x().mark((function r(){return x().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,t.$store.dispatch("user/login",e.data);case 2:Object(s["N"])("errorLoginData"),"sms"===t.loginType&&Object(s["U"])("CHECKDOUBLEFACTOR","1"),t.$message({type:"success",message:"登录成功",duration:1e3}),Reflect.has(e.data,e.data.is_channel_account)?t.$router.push({path:"/homechannel"}):t.$router.push({path:"/"});case 6:case"end":return r.stop()}}),r)})))()},setLoginErrorCount:function(e){void 0===this.errorLoginData[e]&&(this.errorLoginData[e]={count:0,time:0}),this.errorLoginData[e].count+=1,this.errorLoginData[e].time=(new Date).getTime(),this.errorLoginData[e].count<5?this.errorLoginMsg="您还有".concat(5-this.errorLoginData[e].count,"次尝试机会"):this.errorLoginMsg="",Object(s["S"])("errorLoginData",encodeURIComponent(JSON.stringify(this.errorLoginData)))},changeAccountHandle:function(e){this.userErrorLogin=!1},checkLoginError:function(e){var t=!1;if(this.errorLoginData[e]&&this.errorLoginData[e].count>5){var r=(new Date).getTime()-this.errorLoginData[e].time;r<6e5&&(t=!0,this.errorLoginMsg="",this.$message.error("账号已锁定10分钟! 请于"+Object(s["M"])(this.errorLoginData[e].time+6e5)+"后再尝试登录"))}return t},getPhoneCode:function(){var e=this;return k(x().mark((function t(){var r,o,n,a,i,c;return x().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return r=0,"forgetPwd"===e.loginType&&(r=2),o={phone:e.loginFormState.phone,choices:r,code:e.answer},t.next=5,Object(s["Z"])(e.$apis.apiBackgroundVerificationCodePost(o));case 5:if(n=t.sent,a=m(n,2),i=a[0],c=a[1],e.isLoading=!1,!i){t.next=13;break}return e.$message.error(i.message),t.abrupt("return");case 13:0===c.code?(e.sendAuthCode=!1,e.$message.success("发送成功")):e.$message.error(c.msg);case 14:case"end":return t.stop()}}),t)})))()},resetHandle:function(e){this.sendAuthCode=!0},getOtherQuery:function(e){return Object.keys(e).reduce((function(t,r){return"redirect"!==r&&(t[r]=e[r]),t}),{})},forgetHandle:function(e){this.loginFormState.phone="",this.loginFormState.forgetStatus=1,this.loginType="forgetPwd",this.sendAuthCode=!0,this.sendCodeDisabled=!0},tabHandleClick:function(){var e=this;this.$nextTick((function(){e.sendAuthCode=!0,e.sendCodeDisabled=!0,e.isShowVerify=!1,e.loginFormState.verifyCode="",e.loginFormState.phone="",e.$refs.loginFormRef.clearValidate()}))},gotoQrcodeLogin:function(){var e=this;"changePwd"===this.loginType&&this.$refs.changePwdFormRef?(this.$refs.changePwdFormRef.resetFields(),Object(s["O"])("PWDV4TOKEN")):this.$refs.loginFormRef&&this.$refs.loginFormRef.resetFields(),this.$nextTick((function(){e.loginType="qrcode",e.loginFormState.forgetStatus=1}))},checkPwdValidate:function(e){var t=this;this.$refs.loginFormRef.validate((function(r){r?0===e?t.getPhoneCompanyInfo():t.changePassword(e):t.$message.error("请输入正确的信息！")}))},changePassword:function(e){var t=this;return k(x().mark((function r(){var o,n,a,c,l;return x().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(o={sms_code:t.loginFormState.smsCode,phone:t.loginFormState.phone,choices:e,company_id:t.companyId,code:t.answer},!e){r.next=7;break}if(t.loginFormState.newPassword!==t.loginFormState.checkPassword){r.next=6;break}o.new_password=i()(t.loginFormState.newPassword),r.next=7;break;case 6:return r.abrupt("return",t.$message.error("密码输入不一致"));case 7:return t.loginFormState.loading=!0,r.next=10,Object(s["Z"])(t.$apis.apiBackgroundFindPasswordPost(o));case 10:if(n=r.sent,a=m(n,2),c=a[0],l=a[1],t.loginFormState.loading=!1,!c){r.next=18;break}return t.$message.error(c.message),r.abrupt("return");case 18:0===l.code?(t.loginFormState.forgetStatus=e,t.loginFormState.newPassword="",t.loginFormState.checkPassword="",e&&(t.loginFormState.phone="",t.loginType="account",t.companyId="",t.$message.success("修改成功"))):t.$message.error(l.msg);case 19:case"end":return r.stop()}}),r)})))()},getWxLoginData:function(){var e=this;return k(x().mark((function t(){var r,o,n,a;return x().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(s["Z"])(e.$apis.apiBackgroundWechatValidatePost());case 2:if(r=t.sent,o=m(r,2),n=o[0],a=o[1],!n){t.next=9;break}return e.$message.error(n.message),t.abrupt("return");case 9:0===a.code?(e.appid=a.data.appid,e.redirect_uri=a.data.redirect_uri?encodeURIComponent(a.data.redirect_uri+"/#/login"):""):e.$message.error(a.msg);case 10:case"end":return t.stop()}}),t)})))()},checkChangePwdValidate:function(){var e=this;this.$refs.changePwdFormRef.validate((function(t){t?e.changePwdHandle():e.$message.error("请输入正确的信息！")}))},changePwdHandle:function(e){var t=this;return k(x().mark((function e(){var r,o;return x().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r={choices:2,password:i()(t.loginFormState.oldPassword)},t.loginFormState.oldPassword!==t.loginFormState.newPassword){e.next=3;break}return e.abrupt("return",t.$message.error("新密码与旧密码不可重复"));case 3:if(t.loginFormState.newPassword!==t.loginFormState.checkPassword){e.next=7;break}r.new_password=i()(t.loginFormState.newPassword),e.next=8;break;case 7:return e.abrupt("return",t.$message.error("新密码两次输入不一致"));case 8:return t.loginFormState.loading=!0,e.next=11,t.$apis.apiBackgroundModifyUserinfoPost(r);case 11:o=e.sent,t.loginFormState.loading=!1,0===o.code?(t.loginFormState.oldPassword="",t.loginFormState.newPassword="",t.loginFormState.checkPassword="",t.loginFormState.password="",t.loginFormState.phone="",t.loginType="account",t.showChangePwdTip=!1,t.userErrorLogin=!1,t.errorLoginMsg="",Object(s["O"])("PWDV4TOKEN"),t.$message.success("修改成功")):t.$message.error(o.msg);case 14:case"end":return e.stop()}}),e)})))()},getVerifyCodeHandle:Object(s["d"])((function(){this.getVerifyCode()}),300),getVerifyCode:function(){var e=this;return k(x().mark((function t(){var r;return x().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundGetLoginVerifyCodePost();case 2:r=t.sent,0===r.code?e.verifyUrl=r.data:e.$message.error(r.msg);case 4:case"end":return t.stop()}}),t)})))()},gotoAgreement:function(e){var t=window.location.origin+"/#/agreement?type="+e.agreement_type+"&key=AGREEMENTLIST";window.open(t,"_blank")},determineBrowser:function(){var e="http://sh-v4.debug.packertec.com/",t="http://sh-v4-staging.packertec.com/",r="http://sh-v4.packertec.com/";Object(s["J"])()&&this.$confirm("检测您当前在手机环境，是否前往商户移动端？","提示",{confirmButtonText:"立即前往",cancelButtonText:"留在后台",center:!0,customClass:"myMsgDialog",beforeClose:function(o,n,a){if("confirm"===o){var i="";switch("production"){case"development":i=e;break;case"staging":i=t;break;case"production":i=r;break;default:break}window.location.href=i}a()}}).then((function(){})).catch((function(){}))},getPhoneCompanyInfo:function(){var e=this;return k(x().mark((function t(){var r,o,n,a,i;return x().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return r=0,"forgetPwd"===e.loginType&&(r=2),t.next=4,e.$to(e.$apis.apiBackgroundGetPhoneCompanyInfoPost({phone:e.loginFormState.phone,sms_code:e.loginFormState.smsCode,sms_type:r,code:e.answer}));case 4:if(o=t.sent,n=m(o,2),a=n[0],i=n[1],e.isLoading=!1,!a){t.next=12;break}return e.$message.error(a.message),t.abrupt("return");case 12:0===i.code?(i.data.length>1&&(e.showCompany=!0,e.dialogData=i.data),1===i.data.length&&e.dialogConfirmHandle(i.data[0])):e.$message.error(i.msg);case 13:case"end":return t.stop()}}),t)})))()},dialogConfirmHandle:function(e){this.companyId=e.company_id,this.showCompany=!1,"forgetPwd"===this.loginType?this.changePassword(0):this.loginHanlde()},dialogClosehandle:function(){this.showCompany=!1},getVerCode:function(e){var t=this;return k(x().mark((function r(){var o,n,a,i;return x().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(t.loginFormState.phone&&/^1[3456789]\d{9}$/.test(t.loginFormState.phone)){r.next=2;break}return r.abrupt("return");case 2:return r.next=4,t.$apis.apiBackgroundGetSmsVerifyCodePost({phone:t.loginFormState.phone});case 4:if(o=r.sent,0===o.code){if(n=o.data||{},t.answer=n.key||"",a=g["a"].decode(n.key)?JSON.parse(g["a"].decode(n.key)):"",t.answerList=[],a&&"object"===p(a))for(i in a)t.answerList.push(a[i]);t.$refs.verifyCode&&(t.$refs.verifyCode.setAnswerList(t.answerList),e&&t.$refs.verifyCode.reset()),t.isShowVerify=!0}else t.$message.error(o.msg);case 6:case"end":return r.stop()}}),r)})))()},verifySuccess:function(e){this.isShowVerify=!1,this.getPhoneCode()},verifyRefresh:function(){this.getVerCode(!0)}},beforeDestroy:function(){document.removeEventListener("keydown",this.enterKeydowHandle)}},F=L,_=(r("12ed"),r("2877")),P=Object(_["a"])(F,o,n,!1,null,null,null);t["default"]=P.exports},a76d:function(e,t,r){"use strict";r.r(t);var o=function(){var e=this,t=e._self._c;return t("div",{attrs:{calss:"ps-dialog-doublefactor"}},[t("el-dialog",{attrs:{"custom-class":"ps-dialog "+e.customClass,title:e.title,visible:e.visible,width:e.width,top:e.top,"close-on-click-modal":!1,"destroy-on-close":!0,"close-on-press-escape":!1,"show-close":!1,center:e.center},on:{"update:visible":function(t){e.visible=t},closed:e.handleClose}},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],staticClass:"content"},[t("el-form",{ref:"doubleFormRef",staticClass:"form",attrs:{model:e.checkFormData,rules:e.rules,"label-width":"0"}},[t("el-form-item",{staticClass:"phone",attrs:{prop:"phone"}},[e._v(" 手机号: "+e._s(e.userInfo.mobile)+" ")]),t("el-form-item",{staticClass:"phone-code",attrs:{prop:"smsCode"}},[t("verification-code",{attrs:{sendAuthCode:e.sendAuthCode,disabled:e.sendCodeDisabled,"reset-handle":e.resetHandle},on:{click:e.getPhoneCode},model:{value:e.checkFormData.smsCode,callback:function(t){e.$set(e.checkFormData,"smsCode",t)},expression:"checkFormData.smsCode"}})],1)],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"check-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickConfirmHandle}},[e._v(e._s(e.confirmText))]),t("el-button",{staticClass:"ps-warn-text",staticStyle:{"margin-left":"0"},attrs:{disabled:e.isLoading,type:"text"},on:{click:e.layoutOutHandle}},[e._v("取 消")])],1)]),t("dialog-message",{attrs:{width:"450px",title:e.messageTitle,"show-close":!1,message:e.messageContent,"confirm-text":e.massageConfirmText,show:e.showDialog},on:{"update:show":function(t){e.showDialog=t},close:function(t){return e.closeTimeDialogHandle("close")},confirm:e.closeTimeDialogHandle}})],1)},n=[],a=r("ed08");r("b2e5");function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return t};var e,t={},r=Object.prototype,o=r.hasOwnProperty,n=Object.defineProperty||function(e,t,r){e[t]=r.value},a="function"==typeof Symbol?Symbol:{},c=a.iterator||"@@iterator",l=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function d(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(e){d=function(e,t,r){return e[t]=r}}function f(e,t,r,o){var a=t&&t.prototype instanceof w?t:w,i=Object.create(a.prototype),s=new $(o||[]);return n(i,"_invoke",{value:P(e,r,s)}),i}function h(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var g="suspendedStart",p="suspendedYield",m="executing",y="completed",v={};function w(){}function b(){}function C(){}var x={};d(x,c,(function(){return this}));var S=Object.getPrototypeOf,k=S&&S(S(D([])));k&&k!==r&&o.call(k,c)&&(x=k);var L=C.prototype=w.prototype=Object.create(x);function F(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function _(e,t){function r(n,a,s,c){var l=h(e[n],e,a);if("throw"!==l.type){var u=l.arg,d=u.value;return d&&"object"==i(d)&&o.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,s,c)}),(function(e){r("throw",e,s,c)})):t.resolve(d).then((function(e){u.value=e,s(u)}),(function(e){return r("throw",e,s,c)}))}c(l.arg)}var a;n(this,"_invoke",{value:function(e,o){function n(){return new t((function(t,n){r(e,o,t,n)}))}return a=a?a.then(n,n):n()}})}function P(t,r,o){var n=g;return function(a,i){if(n===m)throw Error("Generator is already running");if(n===y){if("throw"===a)throw i;return{value:e,done:!0}}for(o.method=a,o.arg=i;;){var s=o.delegate;if(s){var c=E(s,o);if(c){if(c===v)continue;return c}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(n===g)throw n=y,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);n=m;var l=h(t,r,o);if("normal"===l.type){if(n=o.done?y:p,l.arg===v)continue;return{value:l.arg,done:o.done}}"throw"===l.type&&(n=y,o.method="throw",o.arg=l.arg)}}}function E(t,r){var o=r.method,n=t.iterator[o];if(n===e)return r.delegate=null,"throw"===o&&t.iterator.return&&(r.method="return",r.arg=e,E(t,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+o+"' method")),v;var a=h(n,t.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,v;var i=a.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,v):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function O(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function $(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function D(t){if(t||""===t){var r=t[c];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,a=function r(){for(;++n<t.length;)if(o.call(t,n))return r.value=t[n],r.done=!1,r;return r.value=e,r.done=!0,r};return a.next=a}}throw new TypeError(i(t)+" is not iterable")}return b.prototype=C,n(L,"constructor",{value:C,configurable:!0}),n(C,"constructor",{value:b,configurable:!0}),b.displayName=d(C,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,C):(e.__proto__=C,d(e,u,"GeneratorFunction")),e.prototype=Object.create(L),e},t.awrap=function(e){return{__await:e}},F(_.prototype),d(_.prototype,l,(function(){return this})),t.AsyncIterator=_,t.async=function(e,r,o,n,a){void 0===a&&(a=Promise);var i=new _(f(e,r,o,n),a);return t.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},F(L),d(L,u,"Generator"),d(L,c,(function(){return this})),d(L,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var o in t)r.push(o);return r.reverse(),function e(){for(;r.length;){var o=r.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},t.values=D,$.prototype={constructor:$,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(O),!t)for(var r in this)"t"===r.charAt(0)&&o.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(o,n){return s.type="throw",s.arg=t,r.next=o,n&&(r.method="next",r.arg=e),!!n}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],s=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=o.call(i,"catchLoc"),l=o.call(i,"finallyLoc");if(c&&l){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&o.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var a=n;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,v):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),O(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var o=r.completion;if("throw"===o.type){var n=o.arg;O(r)}return n}}throw Error("illegal catch attempt")},delegateYield:function(t,r,o){return this.delegate={iterator:D(t),resultName:r,nextLoc:o},"next"===this.method&&(this.arg=e),v}},t}function c(e,t){return h(e)||f(e,t)||u(e,t)||l()}function l(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(e,t){if(e){if("string"==typeof e)return d(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?d(e,t):void 0}}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,o=Array(t);r<t;r++)o[r]=e[r];return o}function f(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var o,n,a,i,s=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(o=a.call(r)).done)&&(s.push(o.value),s.length!==t);c=!0);}catch(e){l=!0,n=e}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(l)throw n}}return s}}function h(e){if(Array.isArray(e))return e}function g(e,t,r,o,n,a,i){try{var s=e[a](i),c=s.value}catch(e){return void r(e)}s.done?t(c):Promise.resolve(c).then(o,n)}function p(e){return function(){var t=this,r=arguments;return new Promise((function(o,n){var a=e.apply(t,r);function i(e){g(a,o,n,i,s,"next",e)}function s(e){g(a,o,n,i,s,"throw",e)}i(void 0)}))}}var m={name:"DoubleFactor",props:{title:{type:String,default:"验证登录"},width:{type:String,default:"330px"},top:{type:String,default:"15vh"},confirmText:{type:String,default:"验 证"},customClass:{type:String,default:function(){return"ps-dialog-doublefactor"}},center:Boolean,userInfo:{type:Object}},data:function(){return{isLoading:!1,visible:!1,sendAuthCode:!0,sendCodeDisabled:!1,checkFormData:{phone:"",smsCode:""},rules:{smsCode:[{required:!0,message:"请输入验证码",trigger:"change"}]},showDialog:!1,countDownHandle:null,messageType:"",messageTitle:"",messageContent:"",massageConfirmText:""}},computed:{},watch:{},created:function(){},mounted:function(){this.visible=!("1"===Object(a["x"])("CHECKDOUBLEFACTOR")),this.userInfo.mobile||(this.visible=!1),document.addEventListener("keydown",this.enterKeydowHandler)},methods:{enterKeydowHandler:function(e){13===e.keyCode&&this.clickConfirmHandle()},getPhoneCode:function(){var e=this;return p(s().mark((function t(){var r,o,n,i;return s().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(a["Z"])(e.$apis.apiBackgroundVerificationCodeAutoPost());case 2:if(r=t.sent,o=c(r,2),n=o[0],i=o[1],e.isLoading=!1,!n){t.next=10;break}return e.$message.error(n.message),t.abrupt("return");case 10:0===i.code?(e.sendAuthCode=!1,e.$message.success("发送成功")):e.$message.error(i.msg);case 11:case"end":return t.stop()}}),t)})))()},resetHandle:function(e){this.sendAuthCode=!0},clickConfirmHandle:function(){var e=this;this.$refs.doubleFormRef.validate((function(t){t&&e.checkSmsCode()}))},checkSmsCode:function(){var e=this;return p(s().mark((function t(){var r,o,n,i;return s().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.isLoading){t.next=2;break}return t.abrupt("return");case 2:return e.isLoading=!0,t.next=5,Object(a["Z"])(e.$apis.apiBackgroundCheckVerificationCodePost({sms_code:e.checkFormData.smsCode}));case 5:if(r=t.sent,o=c(r,2),n=o[0],i=o[1],e.isLoading=!1,!n){t.next=13;break}return e.$message.error(n.message),t.abrupt("return");case 13:0===i.code?(e.visible=!1,Object(a["U"])("CHECKDOUBLEFACTOR","1"),e.$message.success("验证成功"),e.showChangePwdHandle(),e.$emit("doubleConfirm")):e.$message.error(i.msg);case 14:case"end":return t.stop()}}),t)})))()},clickCancleHandle:function(){this.visible=!1,this.$emit("cancel")},handleClose:function(e){this.isLoading=!1,this.$emit("close")},countDown:function(){var e=Number(Object(a["x"])("REQUESTTIME")),t=(new Date).getTime()-e;if(t>18e5&&!this.showDialog)return clearTimeout(this.countDownHandle),this.messageType="1",this.massageConfirmText="重新登录",this.messageTitle="确定登出",this.messageContent="由于长时间没有操作，登录已过期，请重新登录。",void(this.showDialog=!0);this.countDownHandle=setTimeout(this.countDown,1e3)},closeTimeDialogHandle:function(e){this.showDialog=!1,this.layoutOutHandle()},layoutOutHandle:function(e){this.visible=!1,this.$store.dispatch("user/logout"),this.$emit("doubleCancel")},showChangePwdHandle:function(){if(this.userInfo.last_change_pwd_time&&this.userInfo.is_expire_change_pwd){var e=(new Date).getTime()-new Date(this.userInfo.last_change_pwd_time.replace(new RegExp(/-/gm),"/")).getTime();if(e<=7776e6){if(e>=75168e5){this.messageType="2",this.massageConfirmText="修改密码",this.messageTitle="提示";var t=parseInt(90-Math.floor(e/864e5));this.messageContent=t?"您的登录密码".concat(t,"天后即将过期，为了不影响您正常使用，建议及时修改。"):"您的登录密码1天后即将过期，为了不影响您正常使用，建议及时修改。","1"!==Object(a["x"])("ISEXPIRECHANGEPWD")&&(this.showDialog=!0)}}else this.messageType="3",this.massageConfirmText="确 定",this.messageContent="帐号失效，无法登陆",this.showDialog=!0}}},beforeDestroy:function(){document.removeEventListener("keydown",this.enterKeydowHandler),this.countDownHandle&&clearTimeout(this.countDownHandle)}},y=m,v=(r("e6b5"),r("2877")),w=Object(v["a"])(y,o,n,!1,null,null,null);t["default"]=w.exports},b373:function(e,t,r){"use strict";r.r(t);var o=function(){var e=this,t=e._self._c;return t("dialog-message",{attrs:{show:e.showDialog,loading:e.dialogLoading,title:e.dialogTitle,width:"380px","footer-center":"","destroy-on-close":""},on:{"update:show":function(t){e.showDialog=t},close:e.closeDialog}},[t("el-form",{ref:"dialogFormRef",staticClass:"select-company",attrs:{model:e.dialogForm,rules:e.dialogrules,"label-position":"left","label-width":"0",size:""}},[t("el-form-item",{attrs:{label:"",prop:"id"}},[t("ul",{staticClass:"company-ul text-center m-auto"},e._l(e.companyList,(function(r){return t("li",{key:r.company_id,class:["company",e.dialogForm.id===r.company_id?"active":""],on:{click:function(t){e.dialogForm.id=r.company_id}}},[e._v(e._s(r.company_name))])})),0)])],1),t("div",{staticClass:"footer-center",attrs:{slot:"tool"},slot:"tool"},[t("el-button",{staticClass:"ps-cancel-btn w-110",attrs:{disabled:e.dialogLoading},on:{click:e.cancleDialog}},[e._v("取消")]),t("el-button",{staticClass:"ps-btn w-110",attrs:{disabled:e.dialogLoading,type:"primary"},on:{click:e.confirmDialog}},[e._v("确定")])],1)],1)},n=[];function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function i(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */i=function(){return t};var e,t={},r=Object.prototype,o=r.hasOwnProperty,n=Object.defineProperty||function(e,t,r){e[t]=r.value},s="function"==typeof Symbol?Symbol:{},c=s.iterator||"@@iterator",l=s.asyncIterator||"@@asyncIterator",u=s.toStringTag||"@@toStringTag";function d(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(e){d=function(e,t,r){return e[t]=r}}function f(e,t,r,o){var a=t&&t.prototype instanceof w?t:w,i=Object.create(a.prototype),s=new $(o||[]);return n(i,"_invoke",{value:P(e,r,s)}),i}function h(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var g="suspendedStart",p="suspendedYield",m="executing",y="completed",v={};function w(){}function b(){}function C(){}var x={};d(x,c,(function(){return this}));var S=Object.getPrototypeOf,k=S&&S(S(D([])));k&&k!==r&&o.call(k,c)&&(x=k);var L=C.prototype=w.prototype=Object.create(x);function F(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function _(e,t){function r(n,i,s,c){var l=h(e[n],e,i);if("throw"!==l.type){var u=l.arg,d=u.value;return d&&"object"==a(d)&&o.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,s,c)}),(function(e){r("throw",e,s,c)})):t.resolve(d).then((function(e){u.value=e,s(u)}),(function(e){return r("throw",e,s,c)}))}c(l.arg)}var i;n(this,"_invoke",{value:function(e,o){function n(){return new t((function(t,n){r(e,o,t,n)}))}return i=i?i.then(n,n):n()}})}function P(t,r,o){var n=g;return function(a,i){if(n===m)throw Error("Generator is already running");if(n===y){if("throw"===a)throw i;return{value:e,done:!0}}for(o.method=a,o.arg=i;;){var s=o.delegate;if(s){var c=E(s,o);if(c){if(c===v)continue;return c}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(n===g)throw n=y,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);n=m;var l=h(t,r,o);if("normal"===l.type){if(n=o.done?y:p,l.arg===v)continue;return{value:l.arg,done:o.done}}"throw"===l.type&&(n=y,o.method="throw",o.arg=l.arg)}}}function E(t,r){var o=r.method,n=t.iterator[o];if(n===e)return r.delegate=null,"throw"===o&&t.iterator.return&&(r.method="return",r.arg=e,E(t,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+o+"' method")),v;var a=h(n,t.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,v;var i=a.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,v):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function O(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function $(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function D(t){if(t||""===t){var r=t[c];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function r(){for(;++n<t.length;)if(o.call(t,n))return r.value=t[n],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(a(t)+" is not iterable")}return b.prototype=C,n(L,"constructor",{value:C,configurable:!0}),n(C,"constructor",{value:b,configurable:!0}),b.displayName=d(C,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,C):(e.__proto__=C,d(e,u,"GeneratorFunction")),e.prototype=Object.create(L),e},t.awrap=function(e){return{__await:e}},F(_.prototype),d(_.prototype,l,(function(){return this})),t.AsyncIterator=_,t.async=function(e,r,o,n,a){void 0===a&&(a=Promise);var i=new _(f(e,r,o,n),a);return t.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},F(L),d(L,u,"Generator"),d(L,c,(function(){return this})),d(L,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var o in t)r.push(o);return r.reverse(),function e(){for(;r.length;){var o=r.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},t.values=D,$.prototype={constructor:$,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(O),!t)for(var r in this)"t"===r.charAt(0)&&o.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(o,n){return s.type="throw",s.arg=t,r.next=o,n&&(r.method="next",r.arg=e),!!n}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],s=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=o.call(i,"catchLoc"),l=o.call(i,"finallyLoc");if(c&&l){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&o.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var a=n;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,v):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),O(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var o=r.completion;if("throw"===o.type){var n=o.arg;O(r)}return n}}throw Error("illegal catch attempt")},delegateYield:function(t,r,o){return this.delegate={iterator:D(t),resultName:r,nextLoc:o},"next"===this.method&&(this.arg=e),v}},t}function s(e,t,r,o,n,a,i){try{var s=e[a](i),c=s.value}catch(e){return void r(e)}s.done?t(c):Promise.resolve(c).then(o,n)}function c(e){return function(){var t=this,r=arguments;return new Promise((function(o,n){var a=e.apply(t,r);function i(e){s(a,o,n,i,c,"next",e)}function c(e){s(a,o,n,i,c,"throw",e)}i(void 0)}))}}var l={name:"",components:{},props:{show:{type:Boolean},type:{type:String,default:"login"},dialogTitle:{type:String,default:"请选择你需要登录的项目点"},companyList:{type:Array,default:function(){return[]}},closehandle:Function,confirmhandle:Function},data:function(){return{dialogLoading:!1,dialogForm:{id:""},dialogrules:{id:[{required:!0,message:"请先选择项目点",trigger:"change"}]}}},computed:{showDialog:{get:function(){return this.show,this.show},set:function(e){}}},watch:{},created:function(){},mounted:function(){},methods:{initData:function(){this.dialogForm={}},resetForm:function(){this.dialogForm={id:""},this.$refs.dialogFormRef&&this.$refs.dialogFormRef.clearValidate()},closeDialog:function(){this.resetForm(),this.closehandle&&this.closehandle()},cancleDialog:function(){this.closehandle&&this.closehandle()},confirmDialog:function(){var e=this;this.$refs.dialogFormRef.validate(function(){var t=c(i().mark((function t(r){return i().wrap((function(t){while(1)switch(t.prev=t.next){case 0:r&&e.confirmhandle&&e.confirmhandle({company_id:e.dialogForm.id});case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())}}},u=l,d=(r("d107"),r("2877")),f=Object(d["a"])(u,o,n,!1,null,"08b05d70",null);t["default"]=f.exports},d107:function(e,t,r){"use strict";r("577f")},e6b5:function(e,t,r){"use strict";r("1d61")},f4e3:function(e,t,r){}}]);