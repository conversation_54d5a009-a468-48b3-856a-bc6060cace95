(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["warehousing"],{"38ef":function(t,e,a){"use strict";a("8c7b")},"8c7b":function(t,e,a){},cf18:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t._self._c;return e("div",{staticClass:"Warehousing"},[e("h3",{staticClass:"m-t-20"},[t._v("仓库管理/入库单/入库")]),e("div",{staticClass:"form-container"},[e("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formRef",staticClass:"m-l-20 m-t-10",attrs:{model:t.formData,rules:t.formRules,"label-width":"80px",size:"small"}},[e("el-form-item",{staticClass:"vertical-middle clearfix",attrs:{label:"",prop:"","label-width":"0"}},[e("span",{staticClass:"m-l-12 vertical-middle"},[e("span",{staticClass:"label-text"},[t._v("当前仓库：")]),t._v(" "+t._s(t.detailData.warehouse_name)+" ")]),e("span",{staticClass:"m-l-12 vertical-middle"},[e("span",{staticClass:"label-text m-l-60"},[t._v("单据编号：")]),t._v(" "+t._s(t.detailData.trade_no)+" ")]),e("el-form-item",{staticClass:"inline-block m-b-0 m-l-60",attrs:{label:"入库时间：",prop:"entryTime","label-width":"100px"}},[e("el-date-picker",{staticClass:"ps-picker",staticStyle:{width:"auto"},attrs:{type:"datetime",placeholder:"选择日期","popper-class":"ps-poper-picker",format:"yyyy-MM-dd HH:mm","value-format":"yyyy-MM-dd HH:mm"},model:{value:t.formData.entryTime,callback:function(e){t.$set(t.formData,"entryTime",e)},expression:"formData.entryTime"}})],1),e("span",{staticClass:"float-r"},[e("el-button",{staticClass:"ps-btn",on:{click:function(e){return t.addMaterials("entry_info")}}},[t._v("添加物资")])],1)],1),e("div",{staticClass:"red inbound-tips"},[t._v("相同物资存在多条数据，物资名称、入库价、有效期、供应商名称如一致，将在保存时进行数据合并")]),e("div",{staticClass:"red inbound-tips"},[t._v("相同物资，入库价和有效期必须一致，否则将无法保存入库")]),e("el-form-item",{attrs:{label:"","label-width":"20px"}},[e("el-table",{ref:"tableRef",attrs:{data:t.formData.tableData,stripe:"",size:"small",border:"","max-height":"600","header-row-class-name":"ps-table-header-row"}},t._l(t.materialsTableSettings,(function(a){return e("table-column",{key:a.key,attrs:{col:a},scopedSlots:t._u([{key:"count",fn:function(a){var r=a.row,i=a.index;return[e("el-form-item",{staticClass:"m-b-0",attrs:{label:"","label-width":"0",rules:t.formRules.number,prop:"tableData."+i+".count"}},[e("el-input",{staticClass:"ps-input",attrs:{placeholder:"请输入",maxlength:6},model:{value:r.count,callback:function(e){t.$set(r,"count",e)},expression:"row.count"}})],1)]}},{key:"daterange",fn:function(a){var r=a.row,i=a.index;return[e("el-form-item",{staticClass:"m-b-0",attrs:{label:"","label-width":"0",rules:t.formRules.valid_date,prop:"tableData."+i+".valid_date"}},[e("span",{staticClass:"inline-block ps-btn-span pointer"},[t._v(" "+t._s(r.valid_date&&r.valid_date.length>1?"".concat(r.valid_date[0],"至").concat(r.valid_date[1]):"请选择")+" "),e("el-date-picker",{staticClass:"ps-picker",staticStyle:{width:"auto"},attrs:{type:"daterange",placeholder:"选择日期","popper-class":"ps-poper-picker","value-format":"yyyy-MM-dd"},on:{change:t.changeValidateHandle},model:{value:r.valid_date,callback:function(e){t.$set(r,"valid_date",e)},expression:"row.valid_date"}})],1)])]}},{key:"supplier",fn:function(a){var r=a.row,i=a.index;return[r.select_purchase?e("span",[t._v(t._s(r.supplier_manage_name))]):e("el-form-item",{staticClass:"m-b-0",attrs:{label:"",prop:"tableData."+i+".supplier_manage_id",rules:t.formRules.supplier_manage_id}},[e("el-select",{staticClass:"ps-select",attrs:{filterable:"","popper-class":"ps-popper-select",placeholder:"请选择"},on:{change:function(e){return t.changeSupplier(e,i)}},model:{value:r.supplier_manage_id,callback:function(e){t.$set(r,"supplier_manage_id",e)},expression:"row.supplier_manage_id"}},t._l(r.supplier_list,(function(t){return e("el-option",{key:t.supplier_manage_id,attrs:{label:t.supplier_manage_name,value:t.supplier_manage_id}})})),1)],1)]}},{key:"entryPrice",fn:function(a){var r=a.row,i=a.index;return[e("el-form-item",{staticClass:"m-b-0",attrs:{label:"","label-width":"0",rules:t.formRules.entry_price,prop:"tableData."+i+".entry_price"}},[e("el-input",{staticClass:"ps-input",attrs:{maxlength:5,placeholder:"请输入"},on:{change:t.changeValidateHandle},model:{value:r.entry_price,callback:function(e){t.$set(r,"entry_price",e)},expression:"row.entry_price"}})],1)]}},{key:"operation",fn:function(a){var r=a.row,i=a.index;return[e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(e){return e.stopPropagation(),t.addMaterials("replace_materials",r,i)}}},[t._v(" 替换物资 ")]),e("el-button",{staticClass:"ps-warn",attrs:{type:"text",size:"small"},on:{click:function(e){return e.stopPropagation(),t.deleteMaterials(i,r)}}},[t._v(" 删除 ")])]}}],null,!0)})})),1)],1),e("el-form-item",{attrs:{label:"合计",prop:"remark"}},[t._v(t._s(t.totlePrice)+"元")]),e("el-form-item",{attrs:{label:"入库备注",prop:"remark"}},[e("el-input",{staticClass:"ps-textarea w-280",attrs:{type:"textarea",rows:3,maxlength:50},model:{value:t.formData.remark,callback:function(e){t.$set(t.formData,"remark",e)},expression:"formData.remark"}})],1),e("el-form-item",{attrs:{label:"上传附件"}},[e("file-upload",{ref:"fileUploadRef",attrs:{fileList:t.formData.fileLists,type:"enclosure",prefix:"inventory","show-file-list":!1,accept:".jpeg,.jpg,.png,.bmp",rename:!1,multiple:!0,limit:9,"before-upload":t.beforeUpload},on:{fileLists:t.getFileLists},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{staticClass:"ps-origin",attrs:{loading:a.loading,size:"small",type:"text"}},[t._v(" 上传"+t._s(a.loading?"中":"")+" ")])]}}])})],1),t.previewList.length?e("el-form-item",[e("el-collapse",{staticStyle:{"max-width":"60%"},model:{value:t.activeCollapse,callback:function(e){t.activeCollapse=e},expression:"activeCollapse"}},[e("el-collapse-item",{attrs:{title:t.collapseTitle,name:"1"}},t._l(t.previewList,(function(a,r){return e("div",{key:a+r,staticClass:"img-item"},[e("el-image",{staticClass:"upload-img m-r-6",attrs:{"preview-src-list":t.previewList,"initial-index":r,src:a,fit:"contain"}}),e("span",{staticClass:"img-tools"},[e("i",{staticClass:"el-icon-delete",on:{click:function(e){return e.stopPropagation(),t.deleteUploadImg(r)}}})])],1)})),0)],1)],1):t._e(),e("el-form-item",[e("el-button",{staticClass:"ps-origin-btn",attrs:{size:"medium"},on:{click:t.backHandle}},[t._v("取 消")]),e("el-button",{staticClass:"ps-origin-btn",attrs:{size:"medium"},on:{click:t.submitHandle}},[t._v("确 认")])],1)],1)],1),e("choose-list-dialog",{attrs:{showdialog:t.showDialog,title:t.dialogTitle,type:t.dialogType,api:t.dialogApi,detailApi:t.dialogDetailApi,"search-setting":t.dialogSearchSetting,"table-settings":t.dialogTableSettings,params:t.dialogParams,defaultSelect:t.dialogSelect,rowKey:t.dialogRowKey,showSelectLen:t.showSelectLen},on:{"update:showdialog":function(e){t.showDialog=e},confirmChoose:t.confirmChooseHandle}})],1)},i=[],n=a("ed08"),o=a("f63a"),s=a("5a0c"),l=a("e173"),c=a("4778"),u=a("e925"),d=a("da92");function p(t){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},p(t)}function f(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */f=function(){return e};var t,e={},a=Object.prototype,r=a.hasOwnProperty,i=Object.defineProperty||function(t,e,a){t[e]=a.value},n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",s=n.asyncIterator||"@@asyncIterator",l=n.toStringTag||"@@toStringTag";function c(t,e,a){return Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,a){return t[e]=a}}function u(t,e,a,r){var n=e&&e.prototype instanceof v?e:v,o=Object.create(n.prototype),s=new E(r||[]);return i(o,"_invoke",{value:S(t,a,s)}),o}function d(t,e,a){try{return{type:"normal",arg:t.call(e,a)}}catch(t){return{type:"throw",arg:t}}}e.wrap=u;var m="suspendedStart",h="suspendedYield",g="executing",_="completed",y={};function v(){}function b(){}function w(){}var D={};c(D,o,(function(){return this}));var k=Object.getPrototypeOf,x=k&&k(k(P([])));x&&x!==a&&r.call(x,o)&&(D=x);var L=w.prototype=v.prototype=Object.create(D);function C(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function j(t,e){function a(i,n,o,s){var l=d(t[i],t,n);if("throw"!==l.type){var c=l.arg,u=c.value;return u&&"object"==p(u)&&r.call(u,"__await")?e.resolve(u.__await).then((function(t){a("next",t,o,s)}),(function(t){a("throw",t,o,s)})):e.resolve(u).then((function(t){c.value=t,o(c)}),(function(t){return a("throw",t,o,s)}))}s(l.arg)}var n;i(this,"_invoke",{value:function(t,r){function i(){return new e((function(e,i){a(t,r,e,i)}))}return n=n?n.then(i,i):i()}})}function S(e,a,r){var i=m;return function(n,o){if(i===g)throw Error("Generator is already running");if(i===_){if("throw"===n)throw o;return{value:t,done:!0}}for(r.method=n,r.arg=o;;){var s=r.delegate;if(s){var l=$(s,r);if(l){if(l===y)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===m)throw i=_,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=g;var c=d(e,a,r);if("normal"===c.type){if(i=r.done?_:h,c.arg===y)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(i=_,r.method="throw",r.arg=c.arg)}}}function $(e,a){var r=a.method,i=e.iterator[r];if(i===t)return a.delegate=null,"throw"===r&&e.iterator.return&&(a.method="return",a.arg=t,$(e,a),"throw"===a.method)||"return"!==r&&(a.method="throw",a.arg=new TypeError("The iterator does not provide a '"+r+"' method")),y;var n=d(i,e.iterator,a.arg);if("throw"===n.type)return a.method="throw",a.arg=n.arg,a.delegate=null,y;var o=n.arg;return o?o.done?(a[e.resultName]=o.value,a.next=e.nextLoc,"return"!==a.method&&(a.method="next",a.arg=t),a.delegate=null,y):o:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,y)}function O(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function T(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(O,this),this.reset(!0)}function P(e){if(e||""===e){var a=e[o];if(a)return a.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,n=function a(){for(;++i<e.length;)if(r.call(e,i))return a.value=e[i],a.done=!1,a;return a.value=t,a.done=!0,a};return n.next=n}}throw new TypeError(p(e)+" is not iterable")}return b.prototype=w,i(L,"constructor",{value:w,configurable:!0}),i(w,"constructor",{value:b,configurable:!0}),b.displayName=c(w,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,c(t,l,"GeneratorFunction")),t.prototype=Object.create(L),t},e.awrap=function(t){return{__await:t}},C(j.prototype),c(j.prototype,s,(function(){return this})),e.AsyncIterator=j,e.async=function(t,a,r,i,n){void 0===n&&(n=Promise);var o=new j(u(t,a,r,i),n);return e.isGeneratorFunction(a)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},C(L),c(L,l,"Generator"),c(L,o,(function(){return this})),c(L,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),a=[];for(var r in e)a.push(r);return a.reverse(),function t(){for(;a.length;){var r=a.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=P,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(T),!e)for(var a in this)"t"===a.charAt(0)&&r.call(this,a)&&!isNaN(+a.slice(1))&&(this[a]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var a=this;function i(r,i){return s.type="throw",s.arg=e,a.next=r,i&&(a.method="next",a.arg=t),!!i}for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n],s=o.completion;if("root"===o.tryLoc)return i("end");if(o.tryLoc<=this.prev){var l=r.call(o,"catchLoc"),c=r.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return i(o.catchLoc,!0);if(this.prev<o.finallyLoc)return i(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return i(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return i(o.finallyLoc)}}}},abrupt:function(t,e){for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var n=i;break}}n&&("break"===t||"continue"===t)&&n.tryLoc<=e&&e<=n.finallyLoc&&(n=null);var o=n?n.completion:{};return o.type=t,o.arg=e,n?(this.method="next",this.next=n.finallyLoc,y):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.finallyLoc===t)return this.complete(a.completion,a.afterLoc),T(a),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.tryLoc===t){var r=a.completion;if("throw"===r.type){var i=r.arg;T(a)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,a,r){return this.delegate={iterator:P(e),resultName:a,nextLoc:r},"next"===this.method&&(this.arg=t),y}},e}function m(t,e){return v(t)||y(t,e)||g(t,e)||h()}function h(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function g(t,e){if(t){if("string"==typeof t)return _(t,e);var a={}.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?_(t,e):void 0}}function _(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,r=Array(e);a<e;a++)r[a]=t[a];return r}function y(t,e){var a=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=a){var r,i,n,o,s=[],l=!0,c=!1;try{if(n=(a=a.call(t)).next,0===e){if(Object(a)!==a)return;l=!1}else for(;!(l=(r=n.call(a)).done)&&(s.push(r.value),s.length!==e);l=!0);}catch(t){c=!0,i=t}finally{try{if(!l&&null!=a.return&&(o=a.return(),Object(o)!==o))return}finally{if(c)throw i}}return s}}function v(t){if(Array.isArray(t))return t}function b(t,e,a,r,i,n,o){try{var s=t[n](o),l=s.value}catch(t){return void a(t)}s.done?e(l):Promise.resolve(l).then(r,i)}function w(t){return function(){var e=this,a=arguments;return new Promise((function(r,i){var n=t.apply(e,a);function o(t){b(n,r,i,o,s,"next",t)}function s(t){b(n,r,i,o,s,"throw",t)}o(void 0)}))}}var D={name:"Warehousing",mixins:[o["a"]],components:{ChooseListDialog:c["default"]},data:function(){return{isLoading:!1,warehouseId:"",warehouseName:"",detailData:{},formData:{entryType:"",entryTime:"",remark:"",fileLists:[],tableData:[]},formRules:{name:[{required:!0,message:"请选择",trigger:"change"}],unit:[{required:!0,message:"请选择单位",trigger:"change"}],valid_date:[{validator:this.validateEntryValidDate,trigger:"blur"}],entryType:[{required:!0,message:"请选择入库类型",trigger:"change"}],number:[{validator:l["j"],trigger:"change"}],entry_price:[{validator:this.validateEntryPrice,trigger:"change"}]},pickerOptions:{disabledDate:function(t){return t.getTime()<s().subtract(1,"day").valueOf()}},materialsTableSettings:[{label:"物资名称",key:"materials_name"},{label:"预计入库数量",key:"expected_entry_count"},{label:"实际入库数量",key:"count",type:"slot",slotName:"count"},{label:"单位",key:"unit_name"},{label:"参考单价",key:"ref_unit_price",type:"money"},{label:"入库价",key:"entry_price",type:"slot",slotName:"entryPrice"},{label:"有效期",key:"valid_date",type:"slot",slotName:"daterange",minWidth:"80px"},{label:"供应商名称",key:"supplier_manage_id",type:"slot",slotName:"supplier",minWidth:"100px"},{label:"操作",key:"operation",type:"slot",slotName:"operation"}],activeCollapse:[],showDialog:!1,dialogLoading:!1,dialogTitle:"选择物资",dialogType:"",dialogData:{},remoteLoading:!1,dialogTableSettings:[],dialogSearchSetting:{},dialogParams:{warehouse_id:this.$route.query.warehouse_id},dialogApi:"",dialogDetailApi:"",dialogSelect:[],dialogRowKey:"materials_id",showSelectLen:!1,validateFieldList:[]}},computed:{previewList:function(){var t=this.formData.fileLists.map((function(t){return t.url}));return this.setPreviewListTitle(t),t},totlePrice:function(){var t=this.formData.tableData.reduce((function(t,e){return d["a"].plus(d["a"].times(e.count,e.entry_price||0),t)}),0);return t}},created:function(){this.warehouseId=this.$route.query.warehouse_id,this.warehouseName=this.$route.query.warehouse_name,this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getInboundOrderDetail()},searchHandle:Object(n["d"])((function(){this.initLoad()}),300),getInboundOrderDetail:function(){var t=this;return w(f().mark((function e(){var a,r,i,o,s,l;return f().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.isLoading){e.next=2;break}return e.abrupt("return");case 2:return t.isLoading=!0,a={id:+t.$route.query.id,warehouse_id:t.warehouseId},e.next=6,Object(n["Z"])(t.$apis.apiBackgroundDrpEntryInfoDetailsPost(a));case 6:if(r=e.sent,i=m(r,2),o=i[0],s=i[1],t.isLoading=!1,!o){e.next=14;break}return t.$message.error(o.message),e.abrupt("return");case 14:if(0!==s.code){e.next=25;break}if(s.data){e.next=17;break}return e.abrupt("return");case 17:t.detailData=s.data,t.formData.entryType=s.data.inventory_entry_type,t.formData.entryTime=s.data.entry_time,t.formData.remark=s.data.remark,s.data.image_json&&s.data.image_json.length>0?(l=[],s.data.image_json.forEach((function(t,e){l.push({url:t,name:Object(n["F"])(t),status:"success",uid:(new Date).getTime()+e})})),t.formData.fileLists=l):t.formData.fileLists=[],s.data.entry_data&&(t.formData.tableData=s.data.entry_data.map((function(t){return t.start_valid_date&&t.end_valid_date?t.valid_date=[t.start_valid_date,t.end_valid_date]:t.valid_date=[],t.count=t.expected_entry_count,t.entry_price=t.entry_price/100,t.supplier_list=t.price_info,t.image_json=t.image_json||[],t.is_origin=!0,Object(n["f"])(t)}))),e.next=26;break;case 25:t.$message.error(s.msg);case 26:case"end":return e.stop()}}),e)})))()},changeSupplier:function(t,e){var a=this.formData.tableData[e],r=a.supplier_list.find((function(e){return e.supplier_manage_id===t}));this.$set(a,"ref_unit_price",r.ref_unit_price),this.$set(a,"supplier_manage_name",r.supplier_manage_name)},inputHandle:function(t,e,a){e.is_origin||this.$set(e,"expected_entry_count",t)},addMaterials:function(t,e,a){this.dialogType=t,e?(this.dialogData=e,this.dialogData.index=a):this.dialogData={},"entry_info"===t&&(this.dialogTitle="添加物资",this.dialogApi="apiBackgroundDrpMaterialsGetMaterialsListPost",this.showSelectLen=!0,this.dialogSearchSetting={name:{type:"input",value:"",label:"物资名称",placeholder:"请输入"}},this.dialogTableSettings=[{label:"",key:"selection",type:"selection",reserveSelection:!0},{label:"物资名称",key:"materials_name"},{label:"当前库存",key:"current_num"},{label:"入库数量",key:"count",type:"slot",slotName:"count"},{label:"有效期",key:"date",type:"slot",slotName:"date"}],this.dialogRowKey="materials_id"),"replace_materials"===t&&(this.dialogTitle="替换物资",this.dialogApi="apiBackgroundDrpMaterialsGetMaterialsListPost",this.showSelectLen=!1,this.dialogSearchSetting={name:{type:"input",value:"",label:"物资名称",placeholder:"请输入"}},this.dialogTableSettings=[{label:"",key:"id",type:"slot",slotName:"radio",width:"80px"},{label:"物资名称",key:"materials_name"},{label:"当前库存",key:"current_num"}],this.dialogRowKey="materials_id",this.dialogSelect=e.materials_id),this.showDialog=!0},confirmChooseHandle:function(t){this.showDialog=!1;var e=[],a=[];if(t.data.forEach((function(t){if(t.price_info&&t.price_info.length>0){var r={materials_name:t.materials_name,materials_id:t.materials_id,current_num:t.current_num,count:t.count,expected_entry_count:t.count,unit_name:t.unit_name,unit_id:t.unit_id,ref_unit_price:"",entry_price:"",supplier_manage_id:"",supplier_manage_name:"",supplier_list:t.price_info,valid_date:t.date,image_json:[]},i=null;t.price_info.forEach((function(t){i?t.ref_unit_price<i.ref_unit_price&&(i=t):i=t})),i&&(r.supplier_manage_id=i.supplier_manage_id,r.supplier_manage_name=i.supplier_manage_name,r.ref_unit_price=i.ref_unit_price||0),a.push(r)}else e.push(t.materials_name)})),"entry_info"===t.type&&a.length>0&&(this.formData.tableData.length>0?this.formData.tableData=this.mergeArrays(this.formData.tableData,a):this.formData.tableData=a),e.length>0&&this.$notify.error({title:"错误",message:"物资：".concat(e.join("，"),"未关联供应商！")}),"replace_materials"===t.type&&a.length>0){var r=a[0],i=this.formData.tableData[this.dialogData.index];r.materials_id!==i.materials_id&&(this.$set(i,"materials_name",r.materials_name),this.$set(i,"materials_id",r.materials_id),this.$set(i,"current_num",r.current_num),this.$set(i,"unit_id",r.unit_id),this.$set(i,"unit_name",r.unit_name),this.$set(i,"supplier_manage_id",r.supplier_manage_id),this.$set(i,"supplier_manage_name",r.supplier_manage_name),this.$set(i,"supplier_list",r.supplier_list),this.$set(i,"ref_unit_price",r.ref_unit_price)),this.formData.tableData=this.mergeArrays(this.formData.tableData,[])}},mergeArrays:function(t,e){for(var a={},r=0;r<t.length;r++){var i=t[r],n="".concat(i.materials_id,"_").concat(i.supplier_manage_id,"_").concat(i.entry_price,"_").concat(JSON.stringify(i.valid_date));a[n]?a[n].count=d["a"].plus(a[n].count,i.count):a[n]=i}return e.forEach((function(t){var e="".concat(t.materials_id,"_").concat(t.supplier_manage_id,"_").concat(t.entry_price,"_").concat(JSON.stringify(t.valid_date));a[e]?(a[e].select_purchase&&(a[e].select_purchase=!1),a[e].count=d["a"].plus(a[e].count,t.count),0===a[e].valid_date.length&&(a[e].valid_date=t.valid_date),t.image_json&&t.image_json.length>0&&(a[e].image_json=t.image_json)):a[e]=t})),Object.values(a)},deleteMaterials:function(t){var e=this;this.formData.tableData.splice(t,1),this.validateFieldList=[],this.formData.tableData.forEach((function(t,a){e.validateFieldList.push("tableData.".concat(a,".entry_price"),"tableData.".concat(a,".valid_date"))})),this.changeValidateHandle()},getFileLists:function(t){this.formData.fileLists=t},beforeUpload:function(t){var e=[".jpeg",".jpg",".png",".bmp"];if(!e.includes(Object(n["A"])(t.name)))return this.$message.error("请检查上传文件格式！"),!1;var a=t.size/1024/1024<=2;return a||this.$message.error("上传附件大小不能超过 2M"),a},setPreviewListTitle:function(t){this.collapseTitle="查看附件("+t.length+")"},deleteUploadImg:function(t){var e=this.$refs.fileUploadRef;this.formData.fileLists[t]&&e&&e.spliceFileData(this.formData.fileLists[t].uid),this.formData.fileLists.splice(t,1)},formatParams:function(){var t={inventory_entry_type:this.formData.entryType,warehouse_id:this.warehouseId,remark:this.formData.remark,is_warehousing:!0};return this.formData.entryTime&&(t.entry_time=this.formData.entryTime),t.entry_data=this.uniqueMaterials(this.formData.tableData).map((function(t){return{materials_id:t.materials_id,name:t.name,count:t.count,unit_id:t.unit_id,unit_name:t.unit_name,entry_price:Object(n["Y"])(t.entry_price),start_valid_date:t.valid_date[0],end_valid_date:t.valid_date[1],supplier_manage_id:t.supplier_manage_id,image_json:t.image_json}})),t.image_json=this.previewList,t},uniqueMaterials:function(t){for(var e=Object(n["f"])(t),a={},r=0;r<e.length;r++){var i=e[r],o="".concat(i.materials_id,"_").concat(i.supplier_manage_id,"_").concat(i.entry_price,"_").concat(JSON.stringify(i.valid_date));a[o]?(a[o].count=d["a"].plus(a[o].count,i.count),i.image_json&&i.image_json.length>0&&(a[o].image_json=i.image_json)):a[o]=i}return Object.values(a)},mergeSupplierMaterials:function(){this.$confirm("同一供应商、相同物资、相同入库价、相同有效期的数据已合并",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",showCancelButton:!1,center:!0,beforeClose:function(t,e,a){a()}}).then((function(t){})).catch((function(t){}))},submitHandle:function(){var t=this;this.$refs.formRef.validate((function(e){if(e){if(!t.formData.tableData.length)return t.$message.error("请先选择物资！");var a="apiBackgroundDrpEntryInfoModifyPost",r=t.formatParams();r.id=+t.$route.query.id,t.showConfirmDialog(a,r)}}))},showConfirmDialog:function(t,e){var a=this;this.$confirm("同一供应商、相同物资、相同入库价、相同有效期的数据将会合并",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(r,i,n){n(),"confirm"===r&&a.sendDataHandle(t,e)}}).then((function(t){})).catch((function(t){}))},sendDataHandle:function(t,e){var a=this;return w(f().mark((function r(){var i,o,s,l;return f().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!a.isLoading){r.next=2;break}return r.abrupt("return");case 2:return a.isLoading=!0,r.next=5,Object(n["Z"])(a.$apis[t](e));case 5:if(i=r.sent,o=m(i,2),s=o[0],l=o[1],a.isLoading=!1,!s){r.next=13;break}return a.$message.error(s.message),r.abrupt("return");case 13:0===l.code?(a.$message.success(l.mgs||"成功"),a.$backVisitedViewsPath(a.$route.path,"InboundOrder")):a.$message.error(l.msg);case 14:case"end":return r.stop()}}),r)})))()},backHandle:function(){this.$backVisitedViewsPath(this.$route.path,"InboundOrder")},validateEntryPrice:function(t,e,a,r,i){try{var n=t.field.split("."),o=this.formData[n[0]][Number(n[1])];if(e)if(Object(u["k"])(e)){for(var s=!0,l=[],c=0;c<this.formData.tableData.length;c++){var d=this.formData.tableData[c];if(o.materials_id===d.materials_id&&o.supplier_manage_id===d.supplier_manage_id&&d.entry_price&&(l.push("tableData.".concat(c,".entry_price")),Number(e)!==Number(d.entry_price)&&(s=!1,this.validateFieldList.length>0)))break}0===this.validateFieldList.length&&(this.validateFieldList=l),s?a():a(new Error("入库价不一致"))}else a(new Error("格式错误"));else a(new Error("请输入"))}catch(p){a(new Error("校验出错了"))}},validateEntryValidDate:function(t,e,a,r,i){try{for(var n=t.field.split("."),o=this.formData[n[0]][Number(n[1])],s=!0,l=[],c=0;c<this.formData.tableData.length;c++){var u=this.formData.tableData[c];if(o.materials_id===u.materials_id&&o.supplier_manage_id===u.supplier_manage_id&&(l.push("tableData.".concat(c,".valid_date")),JSON.stringify(e)!==JSON.stringify(u.valid_date)&&(s=!1,this.validateFieldList.length>0)))break}0===this.validateFieldList.length&&(this.validateFieldList=l),s?a():a(new Error("有效期不一致"))}catch(d){a(new Error("校验出错了"))}},changeValidateHandle:Object(n["d"])((function(t){var e=this;if(this.validateFieldList.length>0){var a=this.$refs.formRef,r=this.validateFieldList.length;a.validateField(this.validateFieldList.slice(0),(function(t){r--,0===r&&(e.validateFieldList=[])}))}}),200)}},k=D,x=(a("38ef"),a("2877")),L=Object(x["a"])(k,r,i,!1,null,"343ddc2e",null);e["default"]=L.exports}}]);