(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-menu-admin-weekMenuFoodSeting","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-meal-report-MealPackageRule"],{"67f0":function(e,t,a){"use strict";a("f417")},c9d9:function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"d",(function(){return o})),a.d(t,"b",(function(){return l})),a.d(t,"c",(function(){return s})),a.d(t,"e",(function(){return c})),a.d(t,"f",(function(){return u})),a.d(t,"g",(function(){return d}));var n=a("5a0c"),r=a("da92"),i=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],o=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],l={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},s=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],c=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],u=(n().subtract(7,"day").format("YYYY-MM-DD"),n().format("YYYY-MM-DD"),function(e){return e?"number"===typeof e?r["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:r["a"].divide(e,100).toFixed(2):"0.00"}),d=function(e){return r["a"].times(e,100)}},f417:function(e,t,a){},f430:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e._self._c;return t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}]},[t("div",{staticClass:"box-header-week"},[t("span",[e._v("本周菜谱（"+e._s(e.dateRangeString)+"）")]),e._m(0)]),e.isNutritionGuidance?t("div",{staticClass:"header-wrapper"},[t("div",{staticClass:"marker-wrapper"},e._l(e.markerList,(function(a){return t("div",{key:a.label},[t("span",{class:a.className}),t("span",{staticStyle:{"margin-right":"10px"}},[e._v(e._s(a.label))])])})),0)]):e._e(),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],attrs:{data:e.tableData,border:""}},e._l(e.columns,(function(a){return t("el-table-column",{key:a.column,attrs:{label:a.title,prop:a.column},scopedSlots:e._u([{key:"header",fn:function(a){return[t("div",{staticStyle:{position:"relative"}},[t("div",[e._v(e._s(e.columns[a.$index]["title"]))]),t("span",[e._v(e._s(e.columns[a.$index]["date"]))]),e.columns[a.$index]["date"]?t("el-link",{staticClass:"copy-text",attrs:{underline:!1},on:{click:function(t){return e.handleCopy(e.columns[a.$index]["title"],e.columns[a.$index]["date"])}}},[t("i",{staticClass:"el-icon-document-copy"}),e._v(" 复制 ")]):e._e()],1)]}},{key:"default",fn:function(a){return["meal_type"===a.column.property?t("span",[e._v(e._s(a.row.meal_type))]):e.isNutritionGuidance&&6===a.$index?t("div",[t("div",[t("span",{staticClass:"marker-box",staticStyle:{"background-color":"#f59a23"}}),t("span",[e._v("不足：")]),t("span",[e._v(" "+e._s(a.row.data[e.weekLabelList.findIndex((function(e){return e===a.column.label}))].insufficientTotal)+" ")])]),t("div",[t("span",{staticClass:"marker-box",staticStyle:{"background-color":"#5dbf6e"}}),t("span",[e._v("适宜：")]),t("span",[e._v(" "+e._s(a.row.data[e.weekLabelList.findIndex((function(e){return e===a.column.label}))].suitableTotal)+" ")])]),t("div",[t("span",{staticClass:"marker-box",staticStyle:{"background-color":"#d9001c"}}),t("span",[e._v("过量：")]),e._v(" "+e._s(a.row.data[e.weekLabelList.findIndex((function(e){return e===a.column.label}))].overdoseTotal)+" ")])]):t("div",e._l(e.weekLabelList,(function(n,r){return t("div",{key:n},[a.column.label===n?t("div",{staticClass:"meal-cell-wrapper",class:{"meal-cell-wrapper-bg":!(a.row.data[r]&&(a.row.data[r].food_count||a.row.data[r].set_meal_count))},staticStyle:{cursor:"pointer"},on:{click:function(t){return e.handleMealEdit(a.row.data[r],a.column.property,a.row.field)}}},[t("div",{staticClass:"operate"},[a.row.data[r]&&(a.row.data[r].food_count||a.row.data[r].set_meal_count)?t("div",[t("span",{staticStyle:{cursor:"pointer"},attrs:{slot:"reference"},slot:"reference"},[e._v(" 已配餐（"+e._s(a.row.data[r].food_count+a.row.data[r].set_meal_count)+"） ")])]):t("span",{staticClass:"copy"},[e._v("未配餐")]),a.row.data[r]&&(a.row.data[r].food_count||a.row.data[r].set_meal_count)?t("span",{staticClass:"edit",on:{click:function(t){return t.stopPropagation(),e.handleMealCopy(a.row.data[r].id)}}},[e._v(" 复制 ")]):e._e()]),e.isNutritionGuidance&&("breakfast"===a.row.data[r].meal_type||"lunch"===a.row.data[r].meal_type||"dinner"===a.row.data[r].meal_type)&&a.row.data[r]&&a.row.data[r].food_count?t("div",[t("div",{staticClass:"progress-box"},[t("span",{staticClass:"progress-title"},[e._v("能量摄入")]),t("el-progress",{staticClass:"progress-content",attrs:{"show-text":!1,color:e.percentageColor(a.row.data[r].total_nutrition.energy_kcal),percentage:e.percentageTotal(a.row.data[r].total_nutrition.energy_kcal)}})],1),t("div",{staticClass:"progress-box"},[t("span",{staticClass:"progress-title"},[e._v("三大营养元素")]),t("el-progress",{staticClass:"progress-content",attrs:{"show-text":!1,color:e.percentageColor(a.row.data[r].total_nutrition.total_axunge_protein_carbohydrate),percentage:e.percentageTotal(a.row.data[r].total_nutrition.total_axunge_protein_carbohydrate)}})],1),t("div",{staticClass:"progress-box"},[t("span",{staticClass:"progress-title"},[e._v("食物多样性")]),t("el-progress",{staticClass:"progress-content",attrs:{"show-text":!1,color:e.percentageColor(a.row.data[r].total_nutrition.total_cereals_eggsandmeat_fruit_vegetable),percentage:e.percentageTotal(a.row.data[r].total_nutrition.total_cereals_eggsandmeat_fruit_vegetable)}})],1)]):e._e()]):e._e()])})),0)]}}],null,!0)})})),1),t("el-dialog",{attrs:{title:"复制到",visible:e.showCopyDialog,"close-on-press-escape":!1,"close-on-click-modal":!1,"destroy-on-close":!0,width:"600px"},on:{"update:visible":function(t){e.showCopyDialog=t}}},[t("p",[t("span",{staticStyle:{"margin-right":"48px"}},[e._v("已选："+e._s(e.selectedDate))])]),e._v(" 复制到： "),t("div",{staticStyle:{padding:"0 20px"}},[t("el-checkbox-group",{model:{value:e.checkList,callback:function(t){e.checkList=t},expression:"checkList"}},e._l(e.weekLabelList,(function(a){return t("el-checkbox",{key:a,staticStyle:{"margin-top":"10px"},attrs:{disabled:e.selectedDate===a,label:a}})})),1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{size:"small"},on:{click:function(t){e.showCopyDialog=!1}}},[e._v("取 消")]),t("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.confirmCopy}},[e._v("确 定")])],1)]),e.showCopyMealDialog?t("div",[t("el-dialog",{attrs:{title:"复制到",visible:e.showCopyMealDialog,"close-on-press-escape":!1,"close-on-click-modal":!1,"destroy-on-close":!0,width:"600px"},on:{"update:visible":function(t){e.showCopyMealDialog=t}}},[t("p",[t("span",{staticStyle:{"margin-right":"48px"}},[e._v("已选："+e._s(e.selectedDate))])]),e._v(" 复制到： "),t("div",{staticStyle:{padding:"0 20px"}},e._l(e.weekLabelList,(function(a,n){return t("div",{key:a,staticStyle:{"margin-top":"12px"}},[t("el-checkbox",{model:{value:e.mealData[n].selected,callback:function(t){e.$set(e.mealData[n],"selected",t)},expression:"mealData[idx].selected"}},[e._v(e._s(a))]),t("el-select",{staticStyle:{"margin-left":"20px"},attrs:{size:"mini"},model:{value:e.mealData[n].meal_type,callback:function(t){e.$set(e.mealData[n],"meal_type",t)},expression:"mealData[idx].meal_type"}},e._l(e.mealTypeList,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)})),0),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{size:"small"},on:{click:e.cancelMealCopyHandler}},[e._v("取 消")]),t("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.confirmCopyMeal}},[e._v("确 定")])],1)])],1):e._e(),e.selectModelDialogVisible?t("select-model-dialog",{ref:"selectModelDialog",attrs:{isshow:e.selectModelDialogVisible,formDataDialog:e.selectModelDialogInfo,width:"900px"},on:{"update:isshow":function(t){e.selectModelDialogVisible=t},clickSelect:e.clickSelect}}):e._e()],1)},r=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"meal-type"},[t("div")])}],i=a("5a0c"),o=a("c9d9"),l=a("b9c1"),s=a("ed08");function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function u(e,t){return p(e)||f(e,t)||b(e,t)||d()}function d(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(e,t){var a=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=a){var n,r,i,o,l=[],s=!0,c=!1;try{if(i=(a=a.call(e)).next,0===t){if(Object(a)!==a)return;s=!1}else for(;!(s=(n=i.call(a)).done)&&(l.push(n.value),l.length!==t);s=!0);}catch(e){c=!0,r=e}finally{try{if(!s&&null!=a.return&&(o=a.return(),Object(o)!==o))return}finally{if(c)throw r}}return l}}function p(e){if(Array.isArray(e))return e}function m(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */m=function(){return t};var e,t={},a=Object.prototype,n=a.hasOwnProperty,r=Object.defineProperty||function(e,t,a){e[t]=a.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function u(e,t,a){return Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,a){return e[t]=a}}function d(e,t,a,n){var i=t&&t.prototype instanceof b?t:b,o=Object.create(i.prototype),l=new $(n||[]);return r(o,"_invoke",{value:S(e,a,l)}),o}function f(e,t,a){try{return{type:"normal",arg:e.call(t,a)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var p="suspendedStart",h="suspendedYield",y="executing",g="completed",v={};function b(){}function _(){}function w(){}var k={};u(k,o,(function(){return this}));var x=Object.getPrototypeOf,D=x&&x(x(T([])));D&&D!==a&&n.call(D,o)&&(k=D);var L=w.prototype=b.prototype=Object.create(k);function C(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function M(e,t){function a(r,i,o,l){var s=f(e[r],e,i);if("throw"!==s.type){var u=s.arg,d=u.value;return d&&"object"==c(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){a("next",e,o,l)}),(function(e){a("throw",e,o,l)})):t.resolve(d).then((function(e){u.value=e,o(u)}),(function(e){return a("throw",e,o,l)}))}l(s.arg)}var i;r(this,"_invoke",{value:function(e,n){function r(){return new t((function(t,r){a(e,n,t,r)}))}return i=i?i.then(r,r):r()}})}function S(t,a,n){var r=p;return function(i,o){if(r===y)throw Error("Generator is already running");if(r===g){if("throw"===i)throw o;return{value:e,done:!0}}for(n.method=i,n.arg=o;;){var l=n.delegate;if(l){var s=E(l,n);if(s){if(s===v)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(r===p)throw r=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r=y;var c=f(t,a,n);if("normal"===c.type){if(r=n.done?g:h,c.arg===v)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r=g,n.method="throw",n.arg=c.arg)}}}function E(t,a){var n=a.method,r=t.iterator[n];if(r===e)return a.delegate=null,"throw"===n&&t.iterator.return&&(a.method="return",a.arg=e,E(t,a),"throw"===a.method)||"return"!==n&&(a.method="throw",a.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=f(r,t.iterator,a.arg);if("throw"===i.type)return a.method="throw",a.arg=i.arg,a.delegate=null,v;var o=i.arg;return o?o.done?(a[t.resultName]=o.value,a.next=t.nextLoc,"return"!==a.method&&(a.method="next",a.arg=e),a.delegate=null,v):o:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,v)}function N(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function I(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function $(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(N,this),this.reset(!0)}function T(t){if(t||""===t){var a=t[o];if(a)return a.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,i=function a(){for(;++r<t.length;)if(n.call(t,r))return a.value=t[r],a.done=!1,a;return a.value=e,a.done=!0,a};return i.next=i}}throw new TypeError(c(t)+" is not iterable")}return _.prototype=w,r(L,"constructor",{value:w,configurable:!0}),r(w,"constructor",{value:_,configurable:!0}),_.displayName=u(w,s,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===_||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,u(e,s,"GeneratorFunction")),e.prototype=Object.create(L),e},t.awrap=function(e){return{__await:e}},C(M.prototype),u(M.prototype,l,(function(){return this})),t.AsyncIterator=M,t.async=function(e,a,n,r,i){void 0===i&&(i=Promise);var o=new M(d(e,a,n,r),i);return t.isGeneratorFunction(a)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},C(L),u(L,s,"Generator"),u(L,o,(function(){return this})),u(L,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),a=[];for(var n in t)a.push(n);return a.reverse(),function e(){for(;a.length;){var n=a.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=T,$.prototype={constructor:$,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(I),!t)for(var a in this)"t"===a.charAt(0)&&n.call(this,a)&&!isNaN(+a.slice(1))&&(this[a]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var a=this;function r(n,r){return l.type="throw",l.arg=t,a.next=n,r&&(a.method="next",a.arg=e),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],l=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var s=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(s&&c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(e,t){for(var a=this.tryEntries.length-1;a>=0;--a){var r=this.tryEntries[a];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.finallyLoc===e)return this.complete(a.completion,a.afterLoc),I(a),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.tryLoc===e){var n=a.completion;if("throw"===n.type){var r=n.arg;I(a)}return r}}throw Error("illegal catch attempt")},delegateYield:function(t,a,n){return this.delegate={iterator:T(t),resultName:a,nextLoc:n},"next"===this.method&&(this.arg=e),v}},t}function h(e,t,a,n,r,i,o){try{var l=e[i](o),s=l.value}catch(e){return void a(e)}l.done?t(s):Promise.resolve(s).then(n,r)}function y(e){return function(){var t=this,a=arguments;return new Promise((function(n,r){var i=e.apply(t,a);function o(e){h(i,n,r,o,l,"next",e)}function l(e){h(i,n,r,o,l,"throw",e)}o(void 0)}))}}function g(e){return w(e)||_(e)||b(e)||v()}function v(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function b(e,t){if(e){if("string"==typeof e)return k(e,t);var a={}.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?k(e,t):void 0}}function _(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function w(e){if(Array.isArray(e))return k(e)}function k(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,n=Array(t);a<t;a++)n[a]=e[a];return n}var x={name:"",mounted:function(){this.initLoad()},props:{menuType:{type:String,default:""},menuId:{type:String,default:""}},components:{selectModelDialog:l["default"]},data:function(){return{isLoading:!1,copyDate:"",currentCopyId:null,mealData:[],mealTypeList:[{label:"全部",value:"all"}].concat(g(o["a"])),showCopyDialog:!1,showCopyMealDialog:!1,selectedDate:"",checkList:[],weekLabelList:["星期一","星期二","星期三","星期四","星期五","星期六","星期日"],dateRangeString:"",columns:[],tableData:[],isNutritionGuidance:!1,markerList:[{className:"marker-primary",label:"不足"},{className:"marker-secondary",label:"适宜"},{className:"marker-thridary",label:"过量"}],dialogMenuPeviewInfo:{},selectModelDialogVisible:!1,selectModelDialogInfo:{}}},methods:{initLoad:function(){this.initDateRangeString(),this.requestDetailData()},confirmCopy:function(){var e=this;return y(m().mark((function t(){var a,n,r;return m().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a=[],e.checkList.forEach((function(t){var n=e.weekLabelList.findIndex((function(e){return e===t}));a.push(n+1)})),a.length){t.next=4;break}return t.abrupt("return",e.$message.error("请选择日期"));case 4:return n={copy_date:e.copyDate.replace("年","-").replace("月","-").replace("日",""),id:e.$route.query.id,weekdays:a},t.next=7,e.$apis.apiBackgroundFoodMenuWeeklyCopyDayFoodPost(n);case 7:r=t.sent,0===r.code?(e.$message.success("操作成功"),e.showCopyDialog=!1,e.initLoad()):e.$message.error(r.msg);case 9:case"end":return t.stop()}}),t)})))()},cancelMealCopyHandler:function(){for(var e=[],t=1;t<8;t++)e.push({value:t,selected:!1,meal_type:void 0});this.mealData=e,this.showCopyMealDialog=!1},confirmCopyMeal:function(){var e=this;return y(m().mark((function t(){var a,n,r,i,o;return m().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a=[],n=e.mealData.filter((function(e){return e.selected})),r=!0,n.forEach((function(e){e.meal_type||(r=!1),a.push({weekday:e.value,meal_type:e.meal_type})})),r){t.next=6;break}return t.abrupt("return",e.$message.error("请选择餐段！"));case 6:return i={target_data:a,id:e.currentCopyId},t.next=9,e.$apis.apiBackgroundFoodMenuWeeklyCopyMealTypeFoodPost(i);case 9:o=t.sent,0===o.code?(e.$message.success("操作成功！"),e.showCopyMealDialog=!1,e.initLoad()):e.$message.error(o.msg);case 11:case"end":return t.stop()}}),t)})))()},handleCopy:function(e,t){this.copyDate=t,this.selectedDate=e,this.showCopyDialog=!0,this.checkList=[]},handleMealCopy:function(e){this.currentCopyId=e,this.showCopyMealDialog=!0},handleMealEdit:function(e,t,a){"afternoon"!==a&&"supper"!==a&&"morning"!==a||(this.isNutritionGuidance=!1),this.$router.push({name:"MerchantMenuCatering",query:{isNutritionGuidance:this.isNutritionGuidance?"true":"false",currentEditDate:t,menuType:this.menuType,menuId:this.menuId,currentEditMealType:a,data:this.$encodeQuery(e)}})},initDateRangeString:function(){for(var e=[],t=[],a=1;a<8;a++)e.push({value:a,selected:!1,meal_type:void 0});o["a"].forEach((function(e){return t.push({meal_type:e.label,field:e.value,data:[]})})),this.mealData=e,this.tableData=t},requestDetailData:function(){var e=this;return y(m().mark((function t(){var a,n,r,i,o,l;return m().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a=Number(e.$route.query.id),a){t.next=4;break}return e.$message.error("id获取失败"),t.abrupt("return");case 4:return e.isLoading=!0,t.next=7,Object(s["Z"])(e.$apis.apiBackgroundFoodMenuWeeklyWeeklyDetailPost({id:a}));case 7:if(n=t.sent,r=u(n,2),i=r[0],o=r[1],e.isLoading=!1,!i){t.next=15;break}return e.$message.error(i.message),t.abrupt("return");case 15:0===o.code?(l=Object(s["f"])(Object.keys(o.data.daily_data)),e.dateRangeString="".concat(l[0]," 至 ").concat(l[l.length-1]),e.isNutritionGuidance=o.data.is_nutrition_guidance,e.$emit("guidanceChange",e.isNutritionGuidance),sessionStorage.setItem("mealDailyData",JSON.stringify(o.data.daily_data)),e.handleMealData(o.data.daily_data)):e.$message.error(o.msg);case 16:case"end":return t.stop()}}),t)})))()},handleMealData:function(e){for(var t=this,a=Object.keys(e),n=[{title:"餐段",column:"meal_type"}],r=0;r<a.length;r++)n.push({title:this.weekLabelList[r],column:a[r],date:i(a[r]).format("YYYY年MM月DD日")});var o={meal_type:"合计",field:"summary",data:[]};this.columns=n,a.forEach((function(a){var n=0,r=0,i=0;t.tableData.forEach((function(t){var o=e[a].foods.filter((function(e){return e.meal_type===t.field}));if(t.data.push((null===o||void 0===o?void 0:o.length)>0?o[0]:{}),o&&o.length){var l=o[0].total_nutrition;if("breakfast"===o[0].meal_type||"lunch"===o[0].meal_type||"dinner"===o[0].meal_type)for(var s in l)l[s]>=120?i+=1:l[s]>=80?r+=1:l[s]<80&&(n+=1)}})),o.data.push({insufficientTotal:n,suitableTotal:r,overdoseTotal:i})})),this.isNutritionGuidance&&this.tableData.push(o)},percentageTotal:function(e){var t=0;return e&&(t=parseInt(e)>=100?100:parseInt(e/120*100)),t},percentageColor:function(e){var t="";return e&&(parseInt(e)>=120?t="#ea5b55":parseInt(e)>=80?t="#5dbf6e":parseInt(e)<80&&(t="#e89e42")),t},getModifyNutritionGuidance:function(){var e=this;return y(m().mark((function t(){var a,n,r,i,o;return m().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a={id:e.menuId,operate:0,menu_type:e.menuType},e.isLoading=!0,t.next=4,Object(s["Z"])(e.$apis.apiBackgroundFoodMenuModifyNutritionGuidancePost(a));case 4:if(n=t.sent,r=u(n,2),i=r[0],o=r[1],e.isLoading=!1,!i){t.next=12;break}return e.$message.error(i.message),t.abrupt("return");case 12:0===o.code?(e.$message.success("关闭营养指导"),e.requestDetailData()):e.$message.error(o.msg);case 13:case"end":return t.stop()}}),t)})))()},changeGuidance:function(e){this.selectModelDialogInfo={menuType:this.menuType,menuId:this.menuId},this.isNutritionGuidance=!1,e&&(this.selectModelDialogVisible=!0),e||(this.getModifyNutritionGuidance(),this.tableData.pop())},clickSelect:function(e){this.selectModelDialogVisible=!1,this.requestDetailData()}}},D=x,L=(a("67f0"),a("2877")),C=Object(L["a"])(D,n,r,!1,null,"d1cca6a2",null);t["default"]=C.exports}}]);