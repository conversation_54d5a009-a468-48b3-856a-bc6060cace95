(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-supervision-and-management-food-safety-management-MorningInspectionRecord","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-meal-report-MealPackageRule","view-merchant-order-RechargeOrder","view-merchant-order-RefundOrder","view-merchant-report-report-management-FoodSaleRanking","view-merchant-supervision-and-management-food-safety-management-compontents-PicDetailsDialog","view-merchant-supervision-and-management-food-safety-management-constants"],{"681d":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("dialog-message",{attrs:{show:e.visible,title:e.title,showFooter:e.showFooter,loading:e.isLoading,customClass:"ps-dialog",width:e.width},on:{"update:show":function(t){e.visible=t},"update:loading":function(t){e.isLoading=t},close:e.handleClose}},[e.images?t("div",{staticClass:"ps-flex"},[t("div",[t("img",{staticClass:"pic-tag2",attrs:{src:e.images.scene_img}}),t("div",{staticClass:"m-t-10 text-center font-size-24"},[e._v(e._s(e.name))])]),t("img",{staticClass:"pic-tag2 m-l-10",attrs:{src:e.images.picture_img}}),t("img",{staticClass:"pic-tag2 m-l-10",attrs:{src:e.images.picture_back_img}})]):t("div",[t("span",{staticStyle:{color:"#999"}},[e._v("暂无图片")])]),t("template",{slot:"tool"},[t("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickCancleHandle}},[e._v(" 关闭 ")])],1)])],2)},a=[],i={name:"PicDetailsDialog",props:{loading:Boolean,showFooter:{type:Boolean,default:!0},title:{type:String,default:"晨检图片"},width:{type:String,default:"1560px"},images:{type:Object,default:function(){return{}}},name:{type:String,default:""},isshow:Boolean},data:function(){return{isLoading:!1}},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},watch:{visible:function(){}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){},clickCancleHandle:function(){this.visible=!1},handleClose:function(e){this.isLoading=!1,this.visible=!1}}},o=i,l=(r("7b44"),r("2877")),c=Object(l["a"])(o,n,a,!1,null,"72618e98",null);t["default"]=c.exports},"7b44":function(e,t,r){"use strict";r("c7d7")},"87ac":function(e,t,r){"use strict";var n=r("ed08"),a=r("2f62");function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function o(e,t){return f(e)||u(e,t)||c(e,t)||l()}function l(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function c(e,t){if(e){if("string"==typeof e)return s(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?s(e,t):void 0}}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function u(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,i,o,l=[],c=!0,s=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw a}}return l}}function f(e){if(Array.isArray(e))return e}function p(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */p=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},l=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function f(e,t,r,n){var i=t&&t.prototype instanceof b?t:b,o=Object.create(i.prototype),l=new C(n||[]);return a(o,"_invoke",{value:P(e,r,l)}),o}function h(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var d="suspendedStart",m="suspendedYield",y="executing",g="completed",v={};function b(){}function _(){}function w(){}var k={};u(k,l,(function(){return this}));var S=Object.getPrototypeOf,x=S&&S(S(j([])));x&&x!==r&&n.call(x,l)&&(k=x);var O=w.prototype=b.prototype=Object.create(k);function L(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function E(e,t){function r(a,o,l,c){var s=h(e[a],e,o);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==i(f)&&n.call(f,"__await")?t.resolve(f.__await).then((function(e){r("next",e,l,c)}),(function(e){r("throw",e,l,c)})):t.resolve(f).then((function(e){u.value=e,l(u)}),(function(e){return r("throw",e,l,c)}))}c(s.arg)}var o;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){r(e,n,t,a)}))}return o=o?o.then(a,a):a()}})}function P(t,r,n){var a=d;return function(i,o){if(a===y)throw Error("Generator is already running");if(a===g){if("throw"===i)throw o;return{value:e,done:!0}}for(n.method=i,n.arg=o;;){var l=n.delegate;if(l){var c=T(l,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===d)throw a=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=y;var s=h(t,r,n);if("normal"===s.type){if(a=n.done?g:m,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(a=g,n.method="throw",n.arg=s.arg)}}}function T(t,r){var n=r.method,a=t.iterator[n];if(a===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,T(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=h(a,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var o=i.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,v):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function D(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function N(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function C(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(D,this),this.reset(!0)}function j(t){if(t||""===t){var r=t[l];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,o=function r(){for(;++a<t.length;)if(n.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(i(t)+" is not iterable")}return _.prototype=w,a(O,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:_,configurable:!0}),_.displayName=u(w,s,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===_||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,u(e,s,"GeneratorFunction")),e.prototype=Object.create(O),e},t.awrap=function(e){return{__await:e}},L(E.prototype),u(E.prototype,c,(function(){return this})),t.AsyncIterator=E,t.async=function(e,r,n,a,i){void 0===i&&(i=Promise);var o=new E(f(e,r,n,a),i);return t.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},L(O),u(O,s,"Generator"),u(O,l,(function(){return this})),u(O,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=j,C.prototype={constructor:C,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(N),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(n,a){return l.type="throw",l.arg=t,r.next=n,a&&(r.method="next",r.arg=e),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],l=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var c=n.call(o,"catchLoc"),s=n.call(o,"finallyLoc");if(c&&s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),N(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;N(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:j(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),v}},t}function h(e,t,r,n,a,i,o){try{var l=e[i](o),c=l.value}catch(e){return void r(e)}l.done?t(c):Promise.resolve(c).then(n,a)}function d(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){h(i,n,a,o,l,"next",e)}function l(e){h(i,n,a,o,l,"throw",e)}o(void 0)}))}}function m(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function y(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?m(Object(r),!0).forEach((function(t){g(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function g(e,t,r){return(t=v(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function v(e){var t=b(e,"string");return"symbol"==i(t)?t:t+""}function b(e,t){if("object"!=i(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}t["a"]={data:function(){return{currentTableSetting:[],dialogPrintVisible:!1}},computed:y({},Object(a["c"])(["userInfo"])),mounted:function(){},methods:{initPrintSetting:function(){var e=this;return d(p().mark((function t(){var r,a;return p().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return r=[],t.next=3,e.getPrintSettingInfo();case 3:r=t.sent;try{r=r?Object(n["E"])(r):Object(n["E"])(e.tableSetting)}catch(i){r=Object(n["E"])(e.tableSetting)}r.length<12?(a=Object(n["m"])(e.tableSetting,r),a=e.deleteWidthKey(a),e.currentTableSetting=a):e.currentTableSetting=Object(n["m"])(e.tableSetting,r);case 6:case"end":return t.stop()}}),t)})))()},getPrintSettingInfo:function(){var e=this;return d(p().mark((function t(){var r,a,i,l,c;return p().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return r=null,t.next=3,Object(n["Z"])(e.$apis.apiBackgroundAdminAccountGetAccountPrintInfoPost({id:e.userInfo.account_id,print_key:e.printType}));case 3:if(a=t.sent,i=o(a,2),l=i[0],c=i[1],!l){t.next=10;break}return e.$message.error(l.message),t.abrupt("return",r);case 10:return 0===c.code?r=c.data:e.$message.error(c.msg),t.abrupt("return",r);case 12:case"end":return t.stop()}}),t)})))()},setPrintSettingInfo:function(e,t){var r=this;return d(p().mark((function a(){var i,l,c,s;return p().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,Object(n["Z"])(r.$apis.apiBackgroundAdminAccountSetAccountPrintInfoPost(y({id:r.userInfo.account_id,print_key:r.printType,print_list:e},t)));case 2:if(i=a.sent,l=o(i,2),c=l[0],s=l[1],!c){a.next=9;break}return r.$message.error(c.message),a.abrupt("return");case 9:0===s.code?r.$message.success("设置成功"):r.$message.error(s.msg);case 10:case"end":return a.stop()}}),a)})))()},openPrintSetting:function(){this.dialogPrintVisible=!0},confirmPrintDialog:function(e,t){var r=this;return d(p().mark((function a(){var i;return p().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(!e){a.next=6;break}return i=Object(n["f"])(e),i.length<12&&(i=r.deleteWidthKey(i)),a.next=5,r.setPrintSettingInfo(i,t);case 5:r.currentTableSetting=i;case 6:case"end":return a.stop()}}),a)})))()},deleteWidthKey:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"children";function r(e){e.map((function(e){e.width&&delete e.width,e.minWidth&&delete e.minWidth,e[t]&&e[t].length>0&&r(e[t])}))}return r(e),e},indexMethod:function(e){return(this.page-1)*this.pageSize+(e+1)},setCollectData:function(e){var t=this;this.collect.forEach((function(r){e.data.collect&&void 0!==e.data.collect[r.key]&&t.$set(r,"value",e.data.collect[r.key])}))},setSummaryData:function(e){var t=e.data.collect;t[this.tableSetting[0].key]="合计",this.tableData.length&&this.tableData.push(t)}},beforeDestroy:function(){}}},b440:function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"morning-wrapper container-wrapper"},[t("refresh-tool",{on:{refreshPage:e.refreshHandle}}),t("div",{staticClass:"tab-box"},[t("el-radio-group",{staticClass:"ps-radio-btn",on:{change:e.changeTabHandle},model:{value:e.tabType,callback:function(t){e.tabType=t},expression:"tabType"}},e._l(e.tabTypeList,(function(r){return t("el-radio-button",{directives:[{name:"permission",rawName:"v-permission",value:[r.permissions],expression:"[tab.permissions]"}],key:r.value,attrs:{label:r.value}},[e._v(e._s(r.label))])})),1)],1),t("div",{staticClass:"tab-item m-t-20"},[t("search-form",{ref:"searchRef",attrs:{loading:e.isLoading,"form-setting":e.searchFormSetting,"label-width":"120px",autoSearch:!1},on:{search:e.searchHandle,reset:e.resetHandler}}),t("div",{staticClass:"table-wrapper"},[t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[e._v("数据列表")]),t("div",{staticClass:"align-r"},[t("el-button",{attrs:{size:"mini"},on:{click:e.gotoExport}},[e._v("导出Excel")]),t("button-icon",{attrs:{color:"plain"},on:{click:e.gotoPrint}},[e._v("打印")]),t("button-icon",{attrs:{color:"plain"},on:{click:e.openPrintSetting}},[e._v("报表设置")])],1)]),t("div",{staticClass:"table-content"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData,height:e.maxHeight,"max-height":e.maxHeight,stripe:"","header-row-class-name":"ps-table-header-row","empty-text":"暂无数据，请查询"}},e._l(e.currentTableSetting,(function(r,n){return t("table-column",{key:n,attrs:{col:r},scopedSlots:e._u([{key:"images",fn:function(r){var n=r.row;return[t("div",{staticClass:"ps-origin pointer",on:{click:function(t){return e.handlerImageDetail(n)}}},[e._v("查看")])]}},{key:"checkResult",fn:function(r){var n=r.row;return[t("div",{class:"不合格"===n.check_result_alias?"ps-red":""},[e._v(" "+e._s(n.check_result_alias)+" ")])]}},{key:"temperature",fn:function(t){var r=t.row;return[e._v(" "+e._s(r.temperature?r.temperature+"°C":"")+" ")]}},{key:"checkStatus",fn:function(r){var n=r.row;return[t("div",{class:n.check_status?"":"ps-red"},[e._v(" "+e._s(n.check_status?"已晨检":"未晨检")+" ")])]}}],null,!0)})})),1)],1),"detail"!==e.tabType&&e.getIsDateRange?t("table-statistics",{attrs:{statistics:e.collect}}):e._e(),t("pagination",{attrs:{onPaginationChange:e.onPaginationChange,"current-page":e.currentPage,"page-size":e.pageSize,"page-sizes":[10,20,50,100,500],layout:"total, prev, pager, next, sizes, jumper",total:e.totalCount},on:{"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t},"update:pageSize":function(t){e.pageSize=t},"update:page-size":function(t){e.pageSize=t}}})],1)],1),e.dialogPrintVisible?t("print-setting",{attrs:{tableSetting:e.tableSetting,defaultCheckedSetting:e.currentTableSetting,show:e.dialogPrintVisible,extraParams:{printType:e.printType}},on:{"update:show":function(t){e.dialogPrintVisible=t},confirm:e.confirmPrintDialog}}):e._e(),t("pic-details-dialog",{attrs:{isshow:e.showPicDialog,images:e.imageList,name:e.picName},on:{"update:isshow":function(t){e.showPicDialog=t}}})],1)},a=[],i=r("e49c"),o=r("f63a"),l=r("681d"),c=r("87ac"),s=r("2f62"),u=r("ed08");function f(e){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},f(e)}function p(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */p=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,r){return e[t]=r}}function u(e,t,r,n){var i=t&&t.prototype instanceof b?t:b,o=Object.create(i.prototype),l=new C(n||[]);return a(o,"_invoke",{value:P(e,r,l)}),o}function h(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var d="suspendedStart",m="suspendedYield",y="executing",g="completed",v={};function b(){}function _(){}function w(){}var k={};s(k,o,(function(){return this}));var S=Object.getPrototypeOf,x=S&&S(S(j([])));x&&x!==r&&n.call(x,o)&&(k=x);var O=w.prototype=b.prototype=Object.create(k);function L(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function E(e,t){function r(a,i,o,l){var c=h(e[a],e,i);if("throw"!==c.type){var s=c.arg,u=s.value;return u&&"object"==f(u)&&n.call(u,"__await")?t.resolve(u.__await).then((function(e){r("next",e,o,l)}),(function(e){r("throw",e,o,l)})):t.resolve(u).then((function(e){s.value=e,o(s)}),(function(e){return r("throw",e,o,l)}))}l(c.arg)}var i;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){r(e,n,t,a)}))}return i=i?i.then(a,a):a()}})}function P(t,r,n){var a=d;return function(i,o){if(a===y)throw Error("Generator is already running");if(a===g){if("throw"===i)throw o;return{value:e,done:!0}}for(n.method=i,n.arg=o;;){var l=n.delegate;if(l){var c=T(l,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===d)throw a=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=y;var s=h(t,r,n);if("normal"===s.type){if(a=n.done?g:m,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(a=g,n.method="throw",n.arg=s.arg)}}}function T(t,r){var n=r.method,a=t.iterator[n];if(a===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,T(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=h(a,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var o=i.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,v):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function D(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function N(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function C(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(D,this),this.reset(!0)}function j(t){if(t||""===t){var r=t[o];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,i=function r(){for(;++a<t.length;)if(n.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(f(t)+" is not iterable")}return _.prototype=w,a(O,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:_,configurable:!0}),_.displayName=s(w,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===_||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,s(e,c,"GeneratorFunction")),e.prototype=Object.create(O),e},t.awrap=function(e){return{__await:e}},L(E.prototype),s(E.prototype,l,(function(){return this})),t.AsyncIterator=E,t.async=function(e,r,n,a,i){void 0===i&&(i=Promise);var o=new E(u(e,r,n,a),i);return t.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},L(O),s(O,c,"Generator"),s(O,o,(function(){return this})),s(O,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=j,C.prototype={constructor:C,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(N),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(n,a){return l.type="throw",l.arg=t,r.next=n,a&&(r.method="next",r.arg=e),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],l=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var c=n.call(o,"catchLoc"),s=n.call(o,"finallyLoc");if(c&&s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),N(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;N(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:j(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),v}},t}function h(e,t,r,n,a,i,o){try{var l=e[i](o),c=l.value}catch(e){return void r(e)}l.done?t(c):Promise.resolve(c).then(n,a)}function d(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){h(i,n,a,o,l,"next",e)}function l(e){h(i,n,a,o,l,"throw",e)}o(void 0)}))}}function m(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function y(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?m(Object(r),!0).forEach((function(t){g(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function g(e,t,r){return(t=v(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function v(e){var t=b(e,"string");return"symbol"==f(t)?t:t+""}function b(e,t){if("object"!=f(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=f(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var _={name:"MorningInspectionRecord",mixins:[o["a"],c["a"]],components:{PicDetailsDialog:l["default"]},data:function(){return{isLoading:!1,tabType:"",tabTypeList:[{label:"晨检明细",value:"detail",permissions:"background_fund_supervision.canteen_safety_management.morning_check_detail"},{label:"晨检汇总",value:"summary",permissions:"background_fund_supervision.canteen_safety_management.morning_check_collect"}],tableSetting:[],currentTableSetting:[],tableData:[],pageSize:10,totalCount:0,currentPage:1,searchFormSetting:Object(u["f"])(i["SEARCH_SETTING_MORNING_INSPECTION_DETAILS"]),collect:[{key:"total_count",value:0,label:"本日汇总：应晨检",unit:"人"},{key:"check_count",value:0,label:"实际晨检",unit:"人"},{key:"not_check",value:"",label:"未晨检",unit:"人"}],printType:"SupervisionCanteenSafetyCheckDetail",showPicDialog:!1,imageList:{},picName:"",maxHeight:460}},computed:y(y({},Object(s["c"])(["userInfo","organization","allPermissions"])),{},{getIsDateRange:function(){try{var e=this.searchFormSetting.select_time.value[0],t=this.searchFormSetting.select_time.value[1];return e===t}catch(r){return!1}}}),created:function(){},mounted:function(){this.initLoad()},methods:{initLoad:function(){var e=this;return d(p().mark((function t(){return p().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.initTabList();case 1:case"end":return t.stop()}}),t)})))()},initTabList:function(){var e=this,t=[];this.tabTypeList.forEach((function(r){e.allPermissions.includes(r.permissions)&&t.push(r)})),this.tabTypeList=t,this.tabType=this.tabTypeList.length?this.tabTypeList[0].value:"",this.setTabDataHandle(this.tabType)},resetHandler:function(){this.currentPage=1,this.setTabDataHandle()},refreshHandle:function(){var e=this;return d(p().mark((function t(){return p().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.currentPage=1,e.initTabList();case 2:case"end":return t.stop()}}),t)})))()},searchHandle:function(e){var t=this;return d(p().mark((function e(){return p().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.currentPage=1,t.getDataList();case 2:case"end":return e.stop()}}),e)})))()},changeTabHandle:function(e){this.setTabDataHandle(e)},setTabDataHandle:function(e){"detail"===this.tabType?(this.searchFormSetting=Object(u["f"])(i["SEARCH_SETTING_MORNING_INSPECTION_DETAILS"]),this.tableSetting=Object(u["f"])(i["TABLE_HEAD_DATA_MORNING_INSPECTION_DETAILS"]),this.printType="SupervisionCanteenSafetyCheckDetail"):(this.searchFormSetting=Object(u["f"])(i["SEARCH_SETTING_MORNING_INSPECTION_SUMMARY"]),this.tableSetting=Object(u["f"])(i["TABLE_HEAD_DATA_MORNING_INSPECTION_SUMMARY"]),this.printType="SupervisionCanteenSafetyCheckCollect"),this.initPrintSetting(),this.tableData=[],this.getDataList()},formatQueryParams:function(e){var t={};for(var r in e)""!==e[r].value&&null!==e[r].value&&"全部"!==e[r].value&&("select_time"!==r?t[r]=e[r].value:e[r].value.length>0&&("detail"===this.tabType?(t.start_time=e[r].value[0]+" 00:00:00",t.end_time=e[r].value[1]+" 23:59:59"):(t.start_date=e[r].value[0],t.end_date=e[r].value[1])));return t},getDataList:function(){var e=this;return d(p().mark((function t(){var r,n,a;return p().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.isLoading=!0,r=y(y({},e.formatQueryParams(e.searchFormSetting)),{},{page:e.currentPage,page_size:e.pageSize}),"detail"!==e.tabType){t.next=8;break}return t.next=5,e.$apis.apiBackgroundFundSupervisionCanteenManagementMorningCheckDetailPost(r);case 5:t.t0=t.sent,t.next=11;break;case 8:return t.next=10,e.$apis.apiBackgroundFundSupervisionCanteenManagementMorningCheckCollectPost(r);case 10:t.t0=t.sent;case 11:n=t.t0,e.isLoading=!1,e.tableData=[],n&&0===n.code?(a=n.data||{},e.totalCount=a.count,e.tableData=Object(u["f"])(a.results),e.$refs.tableData&&e.$refs.tableData.doLayout(),e.setCollectData(n)):e.$message.error(n.msg);case 15:case"end":return t.stop()}}),t)})))()},onPaginationChange:function(e){this.currentPage=e.current,this.pageSize=e.pageSize,this.getDataList()},gotoExport:function(){var e={type:this.printType,params:y(y({},this.formatQueryParams(this.searchFormSetting)),{},{page:this.currentPage,page_size:this.pageSize})};"detail"===this.tabType?e.url="apiBackgroundFundSupervisionCanteenManagementMorningCheckDetailExportPost":e.url="apiBackgroundFundSupervisionCanteenManagementMorningCheckCollectExportPost",this.exportHandle(e)},gotoPrint:function(){var e=this.formatQueryParams(this.searchFormSetting),t=Object(u["f"])(this.currentTableSetting);t=t.filter((function(e){return"images"!==e.key}));var r=this.$router.resolve({name:"Print",query:{print_date_state:!0,print_type:this.printType,print_title:"detail"===this.tabType?"晨检明细":"晨检汇总",result_key:"results",api:"detail"===this.tabType?"apiBackgroundFundSupervisionCanteenManagementMorningCheckDetailPost":"apiBackgroundFundSupervisionCanteenManagementMorningCheckCollectPost",show_summary:!1,show_print_header_and_footer:!0,table_setting:JSON.stringify(t),current_table_setting:JSON.stringify(t),collect:"detail"!==this.tabType&&this.getIsDateRange?JSON.stringify(this.collect):null,push_summary:!1,params:JSON.stringify(y(y({},e),{},{page:1,page_size:this.totalCount||10})),isMerge:"0"}}),n=r.href;window.open(n,"_blank")},handlerImageDetail:function(e){var t={scene_img:e.images.scene_img,picture_img:e.images.picture_img,picture_back_img:e.images.picture_back_img};this.imageList=t,this.picName=e.name,this.showPicDialog=!0}}},w=_,k=r("2877"),S=Object(k["a"])(w,n,a,!1,null,"9cf91f8c",null);t["default"]=S.exports},c7d7:function(e,t,r){},c9d9:function(e,t,r){"use strict";r.d(t,"a",(function(){return i})),r.d(t,"d",(function(){return o})),r.d(t,"b",(function(){return l})),r.d(t,"c",(function(){return c})),r.d(t,"e",(function(){return s})),r.d(t,"f",(function(){return u})),r.d(t,"g",(function(){return f}));var n=r("5a0c"),a=r("da92"),i=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],o=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],l={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},c=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],s=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],u=(n().subtract(7,"day").format("YYYY-MM-DD"),n().format("YYYY-MM-DD"),function(e){return e?"number"===typeof e?a["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:a["a"].divide(e,100).toFixed(2):"0.00"}),f=function(e){return a["a"].times(e,100)}},e49c:function(e,t,r){"use strict";r.r(t),r.d(t,"recentSevenDayTime",(function(){return f})),r.d(t,"recentSevenDay",(function(){return p})),r.d(t,"recentThreeDay",(function(){return h})),r.d(t,"recentCurrentDay",(function(){return d})),r.d(t,"SEARCH_SETTING_MORNING_INSPECTION_DETAILS",(function(){return m})),r.d(t,"SEARCH_SETTING_MORNING_INSPECTION_SUMMARY",(function(){return y})),r.d(t,"TABLE_HEAD_DATA_MORNING_INSPECTION_DETAILS",(function(){return g})),r.d(t,"TABLE_HEAD_DATA_MORNING_INSPECTION_SUMMARY",(function(){return v})),r.d(t,"SEARCH_SETTING_MORNING_SAMPLE_RECORD",(function(){return b})),r.d(t,"TABLE_HEAD_DATA_SAMPLE_RECORD",(function(){return _}));var n=r("5a0c"),a=r("c9d9");function i(e){return s(e)||c(e)||l(e)||o()}function o(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function l(e,t){if(e){if("string"==typeof e)return u(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?u(e,t):void 0}}function c(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function s(e){if(Array.isArray(e))return u(e)}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var f=[n().subtract(7,"day").format("YYYY-MM-DD HH:mm:ss"),n().format("YYYY-MM-DD HH:mm:ss")],p=[n().subtract(7,"day").format("YYYY-MM-DD"),n().format("YYYY-MM-DD")],h=[n().subtract(3,"day").format("YYYY-MM-DD"),n().format("YYYY-MM-DD")],d=[n().subtract(0,"day").format("YYYY-MM-DD"),n().format("YYYY-MM-DD")],m={select_time:{type:"daterange",label:"晨检时间",value:p,clearable:!1},org_id:{type:"organizationSelect",value:"",label:"组织名称",listNameKey:"name",listValueKey:"id",checkStrictly:!0,multiple:!1,collapseTags:!0,clearable:!0},name:{type:"input",value:"",label:"晨检人",dataList:[],placeholder:"请输入",maxlength:20,clearable:!0},check_result:{type:"select",value:"全部",label:"晨检结果",dataList:[{label:"全部",value:"全部"},{label:"合格",value:"0"},{label:"不合格",value:"1"}],clearable:!0}},y={select_time:{type:"daterange",label:"晨检时间",value:d,clearable:!1},org_id:{type:"organizationSelect",value:"",label:"组织名称",listNameKey:"name",listValueKey:"id",checkStrictly:!0,multiple:!1,collapseTags:!0,clearable:!0},name:{type:"input",value:"",label:"晨检人",dataList:[],placeholder:"请输入",maxlength:20,clearable:!0},is_check:{type:"select",value:"全部",label:"晨检状态",dataList:[{label:"全部",value:"全部"},{label:"已晨检",value:!0},{label:"未晨检",value:!1}],clearable:!0}},g=[{label:"晨检时间",key:"check_time"},{label:"组织名称",key:"org_name"},{label:"晨检人姓名",key:"name"},{label:"晨检结果",key:"check_result_alias",type:"slot",slotName:"checkResult"},{label:"体温",key:"temperature",type:"slot",slotName:"temperature"},{label:"手部识别结果",key:"hand_result"},{label:"是否有腹泻和咽喉炎症",key:"risk_type_one_alias"},{label:"健康证是否有效",key:"health_certificate_status_alias"},{label:"不合格原因",key:"remark"},{label:"图片",key:"images",type:"slot",slotName:"images"}],v=[{label:"晨检时间",key:"check_time"},{label:"晨检人姓名",key:"name"},{label:"晨检状态",key:"check_status",type:"slot",slotName:"checkStatus"},{label:"晨检次数",key:"count"},{label:"合格数",key:"qualified_count"},{label:"不合格数",key:"unqualified_count"}],b={date_type:{type:"select",value:"reserved_time",label:"",width:"120px",dataList:[{label:"留样时间",value:"reserved_time"},{label:"入柜时间",value:"entry_date"},{label:"离柜时间",value:"exit_time"}],clearable:!1},selecttime:{type:"daterange",filterable:!0,defaultExpandAll:!0,clearable:!1,label:"",value:h,placeholder:"请选择",dataList:[]},org_id:{type:"organizationSelect",value:"",label:"组织名称",listNameKey:"name",listValueKey:"id",checkStrictly:!0,multiple:!1,collapseTags:!0},meal_type:{type:"select",value:[],clearable:!0,label:"留样餐段",multiple:!0,collapseTags:!0,dataList:i(a["a"])},menu_type:{type:"select",value:"",label:"留样菜谱",placeholder:"请选择",dataList:[],clearable:!0},food_name:{type:"input",label:"菜品",clearable:!0,value:"",placeholder:"请输入",maxlength:20},reserved_user:{type:"input",value:"",label:"留样员",placeholder:"请输入"},sample_exit_user:{type:"input",value:"",label:"取样员",placeholder:"请输入"},sample_entry_user:{type:"input",value:"",label:"入柜员",placeholder:"请输入"},reserved_status:{type:"select",value:"all",label:"留样状态",dataList:[{label:"全部",value:"all"},{label:"已留样",value:"reserved"},{label:"未留样",value:"not_reserved"}]},entry_cupboard:{type:"select",value:"all",label:"入柜状态",dataList:[{label:"全部",value:"all"},{label:"是",value:!0},{label:"否",value:!1}]},entry_device_ids:{type:"select",value:[],label:"入柜设备",dataList:[],multiple:!0,placeholder:"请选择",clearable:!0,listNameKey:"device_name",listValueKey:"device_no"}},_=[{label:"留样时间",key:"reserved_time",width:"180px"},{label:"所属组织",key:"org_name"},{label:"所属菜谱",key:"menu_name"},{label:"餐段",key:"meal_type_alias"},{label:"菜品",key:"food_name",showTooltip:!0,width:"130px"},{label:"留样状态",key:"reserved_status_alias"},{label:"留样数量",key:"food_count"},{label:"留样重量",key:"food_weight",type:"slot",slotName:"foodWeight"},{label:"是否入柜",key:"entry_cupboard",type:"slot",slotName:"entryCupboard"},{label:"入柜时间",key:"entry_cupboard_time",width:"180px"},{label:"存放时长",key:"store_time",width:"180px"},{label:"离柜时间",key:"exit_cupboard_time",width:"180px"},{label:"入柜设备",key:"entry_device"},{label:"当前柜内温度",key:"temperature",type:"slot",slotName:"temperature"},{label:"留样员",key:"reserved_user_name",type:"slot",slotName:"reservedUserName",width:"180px",showTooltip:!0},{label:"入柜员",key:"sample_entry_user",type:"slot",slotName:"sampleEntryUser",width:"180px",showTooltip:!0},{label:"取样员",key:"sample_exit_user",type:"slot",slotName:"sampleExitUser",width:"180px",showTooltip:!0},{label:"未入柜原因",key:"not_entry_reason",type:"slot",slotName:"notEntryReason",showTooltip:!0,width:"180px"},{label:"留样照片",key:"operation",type:"slot",slotName:"operation",fixed:"right"}]}}]);