(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-components-menu-analysisDetailedDialog","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-AreaFood","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-components-menu-menuPreviewDialog","view-merchant-meal-management-meal-report-MealPackageRule"],{1650:function(e,t,n){"use strict";n("a648")},"1a30":function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e._self._c;return t("dialog-message",{attrs:{show:e.visible,title:e.title,customClass:"ps-dialog",width:e.width,height:200},on:{"update:show":function(t){e.visible=t},close:e.handleClose}},[t("div",{staticClass:"analysis-detailed-dialog"},[t("div",{staticClass:"m-t-10"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"detailedTableData",staticStyle:{width:"100%"},attrs:{data:e.detailedTableData,border:"","header-row-class-name":"ps-table-header-row","span-method":e.objectSpanMethod}},e._l(e.tableSetting,(function(n){return t("el-table-column",{key:n.column,attrs:{prop:n.column,label:n.label,align:"center",width:n.width},scopedSlots:e._u([{key:"default",fn:function(r){return["need_current_value"===n.column?t("div",[t("span",{style:{color:"".concat(e.formatColor(r.row))}},[e._v(" "+e._s(r.row.current_value)+" ")]),e._v(" / "),t("span",[e._v(e._s(r.row.need_value))])]):"foodScale"===n.column?t("div",[t("span",{style:{color:r.row.showModify?"#ea5b55":""}},[e._v(" "+e._s(r.row.foodScale)+"% ")])]):"operation"!==n.column?t("div",[e._v(e._s(r.row[n.column]))]):t("div",[r.row.showModify?t("el-button",{staticClass:"ps-warn",attrs:{type:"text",size:"small"},on:{click:function(t){return e.modifyAnalysis(r.row)}}},[e._v(" 修改 ")]):e._e()],1)]}}],null,!0)})})),1)],1)]),t("template",{slot:"tool"},[t("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:e.clickCancleHandle}},[e._v(" 取消 ")])],1)])],2)},a=[],o=n("cc06"),i=n("ed08"),u=n("c9d9");function l(e){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},l(e)}function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,a=Object.defineProperty||function(e,t,n){e[t]=n.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",u=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function f(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{f({},"")}catch(e){f=function(e,t,n){return e[t]=n}}function d(e,t,n,r){var o=t&&t.prototype instanceof b?t:b,i=Object.create(o.prototype),u=new C(r||[]);return a(i,"_invoke",{value:S(e,n,u)}),i}function m(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var h="suspendedStart",y="suspendedYield",p="executing",g="completed",v={};function b(){}function w(){}function _(){}var k={};f(k,i,(function(){return this}));var x=Object.getPrototypeOf,D=x&&x(x(K([])));D&&D!==n&&r.call(D,i)&&(k=D);var L=_.prototype=b.prototype=Object.create(k);function O(e){["next","throw","return"].forEach((function(t){f(e,t,(function(e){return this._invoke(t,e)}))}))}function E(e,t){function n(a,o,i,u){var c=m(e[a],e,o);if("throw"!==c.type){var s=c.arg,f=s.value;return f&&"object"==l(f)&&r.call(f,"__await")?t.resolve(f.__await).then((function(e){n("next",e,i,u)}),(function(e){n("throw",e,i,u)})):t.resolve(f).then((function(e){s.value=e,i(s)}),(function(e){return n("throw",e,i,u)}))}u(c.arg)}var o;a(this,"_invoke",{value:function(e,r){function a(){return new t((function(t,a){n(e,r,t,a)}))}return o=o?o.then(a,a):a()}})}function S(t,n,r){var a=h;return function(o,i){if(a===p)throw Error("Generator is already running");if(a===g){if("throw"===o)throw i;return{value:e,done:!0}}for(r.method=o,r.arg=i;;){var u=r.delegate;if(u){var l=M(u,r);if(l){if(l===v)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===h)throw a=g,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=p;var c=m(t,n,r);if("normal"===c.type){if(a=r.done?g:y,c.arg===v)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(a=g,r.method="throw",r.arg=c.arg)}}}function M(t,n){var r=n.method,a=t.iterator[r];if(a===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,M(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var o=m(a,t.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,v;var i=o.arg;return i?i.done?(n[t.resultName]=i.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,v):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function N(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function j(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function C(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(N,this),this.reset(!0)}function K(t){if(t||""===t){var n=t[i];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,o=function n(){for(;++a<t.length;)if(r.call(t,a))return n.value=t[a],n.done=!1,n;return n.value=e,n.done=!0,n};return o.next=o}}throw new TypeError(l(t)+" is not iterable")}return w.prototype=_,a(L,"constructor",{value:_,configurable:!0}),a(_,"constructor",{value:w,configurable:!0}),w.displayName=f(_,s,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,f(e,s,"GeneratorFunction")),e.prototype=Object.create(L),e},t.awrap=function(e){return{__await:e}},O(E.prototype),f(E.prototype,u,(function(){return this})),t.AsyncIterator=E,t.async=function(e,n,r,a,o){void 0===o&&(o=Promise);var i=new E(d(e,n,r,a),o);return t.isGeneratorFunction(n)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},O(L),f(L,s,"Generator"),f(L,i,(function(){return this})),f(L,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=K,C.prototype={constructor:C,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(j),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function a(r,a){return u.type="throw",u.arg=t,n.next=r,a&&(n.method="next",n.arg=e),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],u=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var l=r.call(i,"catchLoc"),c=r.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),j(n),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var a=r.arg;j(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:K(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),v}},t}function s(e,t){return y(e)||h(e,t)||d(e,t)||f()}function f(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(e,t){if(e){if("string"==typeof e)return m(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?m(e,t):void 0}}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function h(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,u=[],l=!0,c=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=o.call(n)).done)&&(u.push(r.value),u.length!==t);l=!0);}catch(e){c=!0,a=e}finally{try{if(!l&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(c)throw a}}return u}}function y(e){if(Array.isArray(e))return e}function p(e,t,n,r,a,o,i){try{var u=e[o](i),l=u.value}catch(e){return void n(e)}u.done?t(l):Promise.resolve(l).then(r,a)}function g(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var o=e.apply(t,n);function i(e){p(o,r,a,i,u,"next",e)}function u(e){p(o,r,a,i,u,"throw",e)}i(void 0)}))}}var v={name:"analysisDetailedDialog",props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:""},width:{type:String,default:"600px"},isshow:Boolean,confirm:Function,formDataDialog:{type:Object,default:function(){return{}}},dateNutritionData:{type:Object,default:function(){return{}}}},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},data:function(){return{isLoading:!1,tabsType:"detailed",detailedTableData:[],tableSetting:[{label:"日期",column:"date"},{label:"餐段",column:"mealName"},{label:"菜品名称",column:"foodName",width:"150"},{label:"",column:"foodNutrition"},{label:"统计摄入/推荐摄入",column:"need_current_value",width:"150"},{label:"占比",column:"foodScale"},{label:"操作",column:"operation"}],mergeOpts:{useKeyList:{date:["mealName"],mealName:["need_current_value"]},mergeKeyList:["date"]}}},created:function(){this.getMenuFoodNutrition()},mounted:function(){},methods:{getMenuFoodNutrition:function(){var e=this;return g(c().mark((function t(){var n,r,a,l,f,d;return c().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(i["Z"])(e.$apis.apiBackgroundFoodMenuMenuFoodNutritionPost({id:e.formDataDialog.id,menu_type:e.formDataDialog.menu_type,date:e.formDataDialog.date,nutrition_type:e.formDataDialog.nutrition_type}));case 3:if(n=t.sent,r=s(n,2),a=r[0],l=r[1],e.isLoading=!1,!a){t.next=11;break}return e.$message.error(a.message),t.abrupt("return");case 11:0===l.code?(e.tableSetting[3].label=e.formDataDialog.table_name,f=[],d=l.data,u["a"].forEach((function(t){d[t.value]&&("exceed"===d[t.value].result&&(d[t.value].food_list[0].showModify=!0),"unreached"===d[t.value].result&&(d[t.value].food_list[d[t.value].food_list.length-1].showModify=!0),d[t.value].food_list.forEach((function(n){var r={date:e.formDataDialog.date,mealName:t.label,mealKey:t.value,current_value:d[t.value].current_value,need_value:d[t.value].need_value,result:d[t.value].result,foodName:"".concat(n.name,"(").concat(n.weight,"g)"),foodNutrition:n.nutrition,foodScale:n.scale,id:n.id,showModify:!!n.showModify&&n.showModify};f.push(r)})))})),e.detailedTableData=f,e.rowMergeArrs=Object(o["a"])(e.detailedTableData,e.mergeOpts)):e.$message.error(l.msg);case 12:case"end":return t.stop()}}),t)})))()},formatColor:function(e){var t="";switch(e.result){case"unreached":t="#e89e42";break;case"reached":t="#5dbf6e";break;case"exceed":t="#ea5b55";break}return t},modifyAnalysis:function(e){var t=this;return g(c().mark((function n(){var r,a,o,u,l,f,d,m,h,y;return c().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(r="",a=s(r,2),o=a[0],u=a[1],t.isLoading=!0,"week"!==t.formDataDialog.menu_type){n.next=12;break}return n.next=6,Object(i["Z"])(t.$apis.apiBackgroundFoodMenuWeeklyWeeklyDetailPost({id:t.formDataDialog.id}));case 6:l=n.sent,f=s(l,2),o=f[0],u=f[1],n.next=19;break;case 12:return n.next=15,Object(i["Z"])(t.$apis.apiBackgroundFoodMenuMonthlyMonthlyDetailPost({id:t.formDataDialog.id}));case 15:d=n.sent,m=s(d,2),o=m[0],u=m[1];case 19:if(!o){n.next=22;break}return t.$message.error(o.message),n.abrupt("return");case 22:t.isLoading=!1,0===u.code?(h=u.data.daily_data,y=h[t.formDataDialog.date].foods,y.forEach((function(n){n.meal_type===e.mealKey&&(sessionStorage.setItem("mealDailyData",JSON.stringify(t.dateNutritionData)),t.$router.push({name:"MerchantMenuCatering",query:{isNutritionGuidance:"true",currentEditDate:t.formDataDialog.date,menuType:t.formDataDialog.menu_type,menuId:t.formDataDialog.id,currentEditMealType:e.mealKey,data:t.$encodeQuery(n),food_id:e.id}}))}))):t.$message.error(u.msg);case 24:case"end":return n.stop()}}),n)})))()},clickConfirmHandle:function(){this.visible=!1},clickCancleHandle:function(){this.visible=!1},handleClose:function(e){this.visible=!1},objectSpanMethod:function(e){e.row;var t=e.column,n=e.rowIndex,r=e.columnIndex,a=Object.keys(this.mergeOpts.useKeyList),i=this.mergeOpts.useKeyList&&a.length;if(i)for(var u in this.mergeOpts.useKeyList)if(this.mergeOpts.useKeyList[u].includes(t.property))return Object(o["b"])(this.rowMergeArrs,t.property,n,r);if(this.mergeOpts.mergeKeyList&&this.mergeOpts.mergeKeyList.length&&this.mergeOpts.mergeKeyList.includes(t.property))return Object(o["b"])(this.rowMergeArrs,t.property,n,r)}}},b=v,w=(n("1650"),n("2877")),_=Object(w["a"])(b,r,a,!1,null,"7465d60d",null);t["default"]=_.exports},a648:function(e,t,n){},c9d9:function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"d",(function(){return i})),n.d(t,"b",(function(){return u})),n.d(t,"c",(function(){return l})),n.d(t,"e",(function(){return c})),n.d(t,"f",(function(){return s})),n.d(t,"g",(function(){return f}));var r=n("5a0c"),a=n("da92"),o=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],i=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],u={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},l=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],c=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],s=(r().subtract(7,"day").format("YYYY-MM-DD"),r().format("YYYY-MM-DD"),function(e){return e?"number"===typeof e?a["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:a["a"].divide(e,100).toFixed(2):"0.00"}),f=function(e){return a["a"].times(e,100)}},cc06:function(e,t,n){"use strict";function r(e,t){var n={},r=t.useKeyList&&Object.keys(t.useKeyList).length;return r&&Object.keys(t.useKeyList).forEach((function(r,o){t.useKeyList[r].forEach((function(t,o){n[t]={row:[],mergeNum:0,key:r},n=a(n,e,t,r)}))})),t.mergeKeyList&&t.mergeKeyList.forEach((function(t,r){n[t]={row:[],mergeNum:0},n=a(n,e,t)})),n}function a(e,t,n,r){return t.forEach((function(a,i){if(0===i)e[n].row.push(1),e[n].mergeNum=i;else{var u=r?a[r]===t[i-1][r]:!r,l=a[n]===t[i-1][n]&&u;if(l){var c=o(e[n].row);e[n].row[c]+=1,e[n].row.push(0),e[n].mergeNum=i}else e[n].row.push(1),e[n].mergeNum=i}})),e}function o(e){var t=e.length-1;while(t>0){if(e[t])break;t--}return t}function i(e,t,n,r){var a=e[t].row[n],o=a>0?1:0;return[a,o]}n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return i}))}}]);