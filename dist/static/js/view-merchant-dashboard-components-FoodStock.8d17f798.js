(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-dashboard-components-FoodStock"],{"66c2":function(e,t,r){"use strict";r.r(t);var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"food-stock"},[e._m(0),t("div",{staticClass:"stall content"},e._l(e.pageData,(function(r,o){return t("div",{key:o},[t("div",{staticClass:"stall-lists"},e._l(r.stallList,(function(r,o){return t("div",{key:o,staticClass:"stall-item"},[t("span",{staticClass:"stall-name"},[e._v(e._s(r.name))]),t("el-button",{staticClass:"ps-bule block-btn",attrs:{type:"text",size:"small"},on:{click:function(t){return e.openDialog(o)}}},[e._v("编辑")]),t("div",{staticClass:"food"},e._l(r.food_list,(function(r,o){return t("div",{key:o,staticClass:"food-item"},[t("img",{staticClass:"food-item-img",attrs:{src:r.img_url,alt:""}}),t("div",{staticClass:"food-item-name"},[e._v(e._s(r.food_name))]),t("div",{staticClass:"food-item-stock"},[t("div",[e._v("余量")]),t("div",[e._v(e._s(r.inventory)+"份")])])])})),0)],1)})),0)])})),0),t("el-dialog",{attrs:{title:"选择组织",visible:e.showDialog,width:"500px","custom-class":"ps-dialog"},on:{"update:visible":function(t){e.showDialog=t}}},[t("el-form",{ref:"dialogFormRef",staticClass:"dialog-form",attrs:{model:e.dialogForm,"label-width":"120px",rules:e.dialogFormRules}},[t("div",[t("el-form-item",{attrs:{label:"显示组织：",prop:"orgId"}},[t("organization-select",{staticClass:"search-item-w ps-input w-250",attrs:{placeholder:"请选择所属组织",isLazy:!1,multiple:!1,"check-strictly":!0,"append-to-body":!0},on:{change:e.changeOrg},model:{value:e.dialogForm.orgId,callback:function(t){e.$set(e.dialogForm,"orgId",t)},expression:"dialogForm.orgId"}})],1),t("el-form-item",{attrs:{label:"菜谱："}},[t("el-radio-group",{staticClass:"ps-radio",on:{change:e.changeMenuType},model:{value:e.dialogForm.menuType,callback:function(t){e.$set(e.dialogForm,"menuType",t)},expression:"dialogForm.menuType"}},[t("el-radio",{attrs:{label:"week"}},[e._v("周菜谱")]),t("el-radio",{attrs:{label:"month"}},[e._v("月菜谱")])],1)],1),t("el-form-item",{attrs:{label:"设备类型：",prop:"deviceType"}},[t("el-select",{staticClass:"ps-select w-250",attrs:{multiple:"",placeholder:"请选择设备类型","popper-class":"ps-popper-select"},on:{change:e.deviceTypeChange},model:{value:e.dialogForm.deviceType,callback:function(t){e.$set(e.dialogForm,"deviceType",t)},expression:"dialogForm.deviceType"}},e._l(e.deviceList,(function(e){return t("el-option",{key:e.key,attrs:{label:e.name,value:e.key}})})),1)],1),t("el-form-item",{attrs:{label:"设备型号：",prop:e.isDisabledModel?"":"deviceModel"}},[t("el-select",{staticClass:"ps-select w-250",attrs:{multiple:"",placeholder:"请选择设备型号","popper-class":"ps-popper-select",disabled:e.isDisabledModel},on:{change:e.deviceModelChange},model:{value:e.dialogForm.deviceModel,callback:function(t){e.$set(e.dialogForm,"deviceModel",t)},expression:"dialogForm.deviceModel"}},e._l(e.deviceModelList,(function(e){return t("el-option",{key:e.key,attrs:{label:e.key,value:e.key}})})),1)],1),t("el-form-item",{attrs:{label:"菜谱：",prop:"menuId"}},[t("el-select",{staticClass:"ps-select w-250",attrs:{multiple:""},model:{value:e.dialogForm.menuId,callback:function(t){e.$set(e.dialogForm,"menuId",t)},expression:"dialogForm.menuId"}},e._l(e.menuList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1)]),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-cancel-btn",on:{click:function(t){e.showDialog=!1}}},[e._v("取 消")]),t("el-button",{staticClass:"ps-btn",attrs:{type:"primary"},on:{click:e.confirmDialog}},[e._v("确 定")])],1)],1)],1)},i=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"header"},[t("div",{staticClass:"header-time"},[e._v("2022年12月12号09:00:06 星期三 ")]),t("div",{staticClass:"header-title"},[e._v("朴食智慧食堂菜品余量")]),t("div",{staticClass:"header-account"})])}],n=r("cbfb");function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function c(e){return u(e)||l(e)||d(e)||s()}function s(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(e,t){if(e){if("string"==typeof e)return p(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?p(e,t):void 0}}function l(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function u(e){if(Array.isArray(e))return p(e)}function p(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,o=Array(t);r<t;r++)o[r]=e[r];return o}function f(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */f=function(){return t};var e,t={},r=Object.prototype,o=r.hasOwnProperty,i=Object.defineProperty||function(e,t,r){e[t]=r.value},n="function"==typeof Symbol?Symbol:{},c=n.iterator||"@@iterator",s=n.asyncIterator||"@@asyncIterator",d=n.toStringTag||"@@toStringTag";function l(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,r){return e[t]=r}}function u(e,t,r,o){var n=t&&t.prototype instanceof b?t:b,a=Object.create(n.prototype),c=new C(o||[]);return i(a,"_invoke",{value:j(e,r,c)}),a}function p(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var m="suspendedStart",h="suspendedYield",g="executing",v="completed",y={};function b(){}function _(){}function k(){}var w={};l(w,c,(function(){return this}));var I=Object.getPrototypeOf,L=I&&I(I(O([])));L&&L!==r&&o.call(L,c)&&(w=L);var F=k.prototype=b.prototype=Object.create(w);function x(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function M(e,t){function r(i,n,c,s){var d=p(e[i],e,n);if("throw"!==d.type){var l=d.arg,u=l.value;return u&&"object"==a(u)&&o.call(u,"__await")?t.resolve(u.__await).then((function(e){r("next",e,c,s)}),(function(e){r("throw",e,c,s)})):t.resolve(u).then((function(e){l.value=e,c(l)}),(function(e){return r("throw",e,c,s)}))}s(d.arg)}var n;i(this,"_invoke",{value:function(e,o){function i(){return new t((function(t,i){r(e,o,t,i)}))}return n=n?n.then(i,i):i()}})}function j(t,r,o){var i=m;return function(n,a){if(i===g)throw Error("Generator is already running");if(i===v){if("throw"===n)throw a;return{value:e,done:!0}}for(o.method=n,o.arg=a;;){var c=o.delegate;if(c){var s=T(c,o);if(s){if(s===y)continue;return s}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(i===m)throw i=v,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);i=g;var d=p(t,r,o);if("normal"===d.type){if(i=o.done?v:h,d.arg===y)continue;return{value:d.arg,done:o.done}}"throw"===d.type&&(i=v,o.method="throw",o.arg=d.arg)}}}function T(t,r){var o=r.method,i=t.iterator[o];if(i===e)return r.delegate=null,"throw"===o&&t.iterator.return&&(r.method="return",r.arg=e,T(t,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+o+"' method")),y;var n=p(i,t.iterator,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,y;var a=n.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,y):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function D(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function C(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(D,this),this.reset(!0)}function O(t){if(t||""===t){var r=t[c];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,n=function r(){for(;++i<t.length;)if(o.call(t,i))return r.value=t[i],r.done=!1,r;return r.value=e,r.done=!0,r};return n.next=n}}throw new TypeError(a(t)+" is not iterable")}return _.prototype=k,i(F,"constructor",{value:k,configurable:!0}),i(k,"constructor",{value:_,configurable:!0}),_.displayName=l(k,d,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===_||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,k):(e.__proto__=k,l(e,d,"GeneratorFunction")),e.prototype=Object.create(F),e},t.awrap=function(e){return{__await:e}},x(M.prototype),l(M.prototype,s,(function(){return this})),t.AsyncIterator=M,t.async=function(e,r,o,i,n){void 0===n&&(n=Promise);var a=new M(u(e,r,o,i),n);return t.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},x(F),l(F,d,"Generator"),l(F,c,(function(){return this})),l(F,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var o in t)r.push(o);return r.reverse(),function e(){for(;r.length;){var o=r.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},t.values=O,C.prototype={constructor:C,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(E),!t)for(var r in this)"t"===r.charAt(0)&&o.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function i(o,i){return c.type="throw",c.arg=t,r.next=o,i&&(r.method="next",r.arg=e),!!i}for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n],c=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var s=o.call(a,"catchLoc"),d=o.call(a,"finallyLoc");if(s&&d){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!d)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&o.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var n=i;break}}n&&("break"===e||"continue"===e)&&n.tryLoc<=t&&t<=n.finallyLoc&&(n=null);var a=n?n.completion:{};return a.type=e,a.arg=t,n?(this.method="next",this.next=n.finallyLoc,y):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),E(r),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var o=r.completion;if("throw"===o.type){var i=o.arg;E(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,r,o){return this.delegate={iterator:O(t),resultName:r,nextLoc:o},"next"===this.method&&(this.arg=e),y}},t}function m(e,t,r,o,i,n,a){try{var c=e[n](a),s=c.value}catch(e){return void r(e)}c.done?t(s):Promise.resolve(s).then(o,i)}function h(e){return function(){var t=this,r=arguments;return new Promise((function(o,i){var n=e.apply(t,r);function a(e){m(n,o,i,a,c,"next",e)}function c(e){m(n,o,i,a,c,"throw",e)}a(void 0)}))}}var g={name:"FoodStock",components:{OrganizationSelect:n["a"]},props:{type:String,templateInfo:[Object,Array]},data:function(){return{stallList:[{stall_id:1,name:"好好吃档口1",food_list:[{id:1,food_name:"叉烧",inventory:10,img_url:"https://cashier-v4.debug.packertec.com/api/temporary/import_food/jpg/20221119/163bc887da43ada.jpg?_tk=431524d6b5c8a294d316735c52de6e5d"},{id:2,food_name:"橙子",inventory:10,img_url:"https://packer-data-files.oss-cn-shenzhen.aliyuncs.com/cashier_v4/foodImage/png/20230131/foodImage16751328327429623b5258874f055.png?_tk=89241813d7020625e467493d51d3e258"},{id:3,food_name:"茶叶蛋",inventory:10,img_url:"https://cashier-v4.debug.packertec.com/api/temporary/import_food/jpg/20221119/163bc887e1c1293.jpg?_tk=431524d6b5c8a294d316735c52de6e5d"},{id:4,food_name:"炒饭",inventory:10,img_url:"https://cashier-v4.debug.packertec.com/api/temporary/import_food/jpg/20221119/163bc887da6bd0c.jpg?_tk=431524d6b5c8a294d316735c52de6e5d"}]},{stall_id:2,name:"真美味档口2",food_list:[{id:1,food_name:"咕噜肉",inventory:10,img_url:"https://cashier-v4.debug.packertec.com/api/temporary/import_food/jpg/20221119/163bc887f5214b8.jpg?_tk=431524d6b5c8a294d316735c52de6e5d"},{id:2,food_name:"荷兰豆炒鱿鱼",inventory:10,img_url:"https://cashier-v4.debug.packertec.com/api/temporary/import_food/jpg/20221119/2392da7315b6f8.jpg?_tk=431524d6b5c8a294d316735c52de6e5d"},{id:3,food_name:"胡萝卜丝炒蛋",inventory:10,img_url:"https://cashier-v4.debug.packertec.com/api/temporary/import_food/jpg/20221119/163bc887fbaa3c7.jpg?_tk=431524d6b5c8a294d316735c52de6e5d"},{id:4,food_name:"胡萝卜青瓜炒虾仁",inventory:10,img_url:"https://cashier-v4.debug.packertec.com/api/temporary/import_food/jpg/20221119/163bc887f6697a2.jpg?_tk=431524d6b5c8a294d316735c52de6e5d"}]},{stall_id:3,name:"自选档口3",food_list:[{id:1,food_name:"鸡蛋饺",inventory:10,img_url:"https://cashier-v4.debug.packertec.com/api/temporary/import_food/jpg/20221119/163bc887f7cddf7.jpg?_tk=431524d6b5c8a294d316735c52de6e5d"},{id:2,food_name:"鸡块",inventory:10,img_url:"https://cashier-v4.debug.packertec.com/api/temporary/import_food/jpg/20221119/163bc887fff37e7.jpg?_tk=431524d6b5c8a294d316735c52de6e5d"},{id:3,food_name:"煎蛋",inventory:10,img_url:"https://cashier-v4.debug.packertec.com/api/temporary/import_food/jpg/20221119/163bc8f34fded65.jpg?_tk=7359fed4d63b1810abd8c46a81418b4f"},{id:4,food_name:"姜葱鸡",inventory:10,img_url:"https://cashier-v4.debug.packertec.com/api/temporary/import_food/jpg/20221119/163bc8f34fac01a.jpg?_tk=7359fed4d63b1810abd8c46a81418b4f"}]},{stall_id:4,name:"烧腊档口4",food_list:[{id:1,food_name:"酱油鸡",inventory:10,img_url:"https://cashier-v4.debug.packertec.com/api/temporary/import_food/jpg/20221119/163bc8f3562c4c2.jpg?_tk=7359fed4d63b1810abd8c46a81418b4f"},{id:2,food_name:"韭菜炒鸡蛋",inventory:10,img_url:"https://cashier-v4.debug.packertec.com/api/temporary/import_food/jpg/20221119/163bc8f35f2d1c4.jpg?_tk=a2db6a287a84df43830e4dadb2952401"},{id:3,food_name:"苦瓜炒鸡蛋",inventory:10,img_url:"https://cashier-v4.debug.packertec.com/api/temporary/import_food/jpg/20221119/163bc8f3737dd98.jpg?_tk=a2db6a287a84df43830e4dadb2952401"},{id:4,food_name:"手撕鸡",inventory:10,img_url:"https://cashier-v4.debug.packertec.com/api/temporary/uploads/png/20221215/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_2022121415424016436a330968e64.png?_tk=9b880baf8bf13dc5aea545b01f110697"}]}],pageData:[],dialogForm:{orgId:"",menuType:"week",deviceType:"",deviceModel:"",menuId:[]},dialogFormRules:{orgId:[{required:!0,message:"请选择显示组织",trigger:"blur"}],deviceType:[{required:!0,message:"请选择设备类型",trigger:"blur"}],deviceModel:[{required:!0,message:"请选择设备型号",trigger:"blur"}],menuId:[{required:!0,message:"请选择菜谱",trigger:"blur"}]},menuIndex:"",showDialog:!1,deviceList:[],deviceModelList:[],isDisabledModel:!1,menuList:[],dataInfo:[]}},created:function(){var e=this;"add"===this.type?this.stallList.map((function(t){e.dataInfo.push({orgId:"",menuType:"week",deviceType:"",deviceModel:"",menuId:[]})})):this.dataInfo=this.templateInfo,this.formateDate(),this.getOrgDeviceList()},methods:{formateDate:function(){this.pageData=[];for(var e=0;e<this.stallList.length;e+=4)this.pageData.push({stallList:this.stallList.slice(e,e+4)})},openDialog:function(e){this.showDialog=!0,this.menuIndex=e,this.dialogForm.orgId=this.dataInfo[e].orgId,this.dialogForm.menuType=this.dataInfo[e].menuType,this.dialogForm.deviceType=this.dataInfo[e].deviceType,this.dialogForm.deviceModel=this.dataInfo[e].deviceModel,this.dialogForm.menuId=this.dataInfo[e].menuId,this.dialogForm.deviceType.length?this.getDeviceModel():this.isDisabledModel=!1,this.checkGetMemuList()},confirmDialog:function(){var e=this;this.$refs.dialogFormRef.validate((function(t){t&&(e.dataInfo[e.menuIndex].orgId=e.dialogForm.orgId,e.dataInfo[e.menuIndex].menuType=e.dialogForm.menuType,e.dataInfo[e.menuIndex].deviceType=e.dialogForm.deviceType,e.dataInfo[e.menuIndex].deviceModel=e.dialogForm.deviceModel,e.dataInfo[e.menuIndex].menuId=e.dialogForm.menuId,e.dialogForm={orgId:"",menuType:"week",deviceType:"",deviceModel:"",menuId:[]},e.showDialog=!1,e.$emit("comfirm",e.dataInfo))}))},changeOrg:function(){this.dialogForm.menuId=[],this.menuList=[],this.checkGetMemuList()},changeMenuType:function(){this.dialogForm.menuId=[],this.menuList=[],this.checkGetMemuList()},deviceTypeChange:function(){this.dialogForm.menuId=[],this.menuList=[],this.dialogForm.deviceModel=[],this.deviceModelList=[],this.dialogForm.deviceType.length?this.getDeviceModel():this.isDisabledModel=!1,this.checkGetMemuList()},deviceModelChange:function(){this.dialogForm.menuId=[],this.menuList=[],this.checkGetMemuList()},checkGetMemuList:function(){this.dialogForm.deviceType.length&&this.dialogForm.orgId&&(-1!==this.dialogForm.deviceType.indexOf("H5")||-1!==this.dialogForm.deviceType.indexOf("MAPP")||this.dialogForm.deviceModel.length)&&this.getMenuList()},getMenuList:function(){var e=this;return h(f().mark((function t(){var r;return f().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundDatascreenKanbanTempGetKanbanSettingPost({organization_id:[e.dialogForm.orgId],device_type:e.dialogForm.deviceType,device_model:e.dialogForm.deviceModel,menu_type:e.dialogForm.menuType});case 2:r=t.sent,0===r.code?e.menuList=r.data:e.$message.error(r.msg);case 4:case"end":return t.stop()}}),t)})))()},getOrgDeviceList:function(){var e=this;return h(f().mark((function t(){var r;return f().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundDeviceDeviceDeviceTypePost({source:"self"});case 2:r=t.sent,0===r.code?e.deviceList=[{name:"H5",key:"H5"},{name:"小程序",key:"MAPP"}].concat(c(r.data)):e.$message.error(r.msg);case 4:case"end":return t.stop()}}),t)})))()},getDeviceModel:function(){var e=this;return h(f().mark((function t(){var r,o;return f().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(r=e.dialogForm.deviceType.filter((function(e){return"H5"!==e&&"MAPP"!==e})),r.length){t.next=6;break}return e.isDisabledModel=!0,t.abrupt("return");case 6:e.isDisabledModel=!1;case 7:return t.next=9,e.$apis.apiBackgroundDeviceDeviceDeviceModelPost({device_types:r});case 9:o=t.sent,0===o.code?e.deviceModelList=o.data:e.$message.error(o.msg);case 11:case"end":return t.stop()}}),t)})))()}}},v=g,y=(r("96801"),r("2877")),b=Object(y["a"])(v,o,i,!1,null,null,null);t["default"]=b.exports},8559:function(e,t,r){},96801:function(e,t,r){"use strict";r("8559")}}]);