(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-system-components-downloadQrCode"],{"122b":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"container-wrapper download-qrcode clearfix"},t._l(t.qrCodeList,(function(r,n){return e("div",{key:r.type,class:["float-l",n%2==0?"m-r-60":""]},[e("div",{staticClass:"margin-top-20"},[e("el-radio-group",{attrs:{size:"small"},on:{change:function(e){return t.changeQrcoeType(e,r)}},model:{value:r.selectRadio,callback:function(e){t.$set(r,"selectRadio",e)},expression:"item.selectRadio"}},t._l(r.radioList,(function(n){return e("el-radio-button",{key:n.value,class:["recharge"===r.type?"ps-green-radio-btn":"ps-radio-btn"],attrs:{label:n.value}},[t._v(t._s(n.label))])})),1)],1),e("div",{staticClass:"margin-top-20"},[e("p",{staticClass:"margin-top-20"},[t._v(t._s("(".concat(t.labelName,")").concat(r.label)))]),e("qrcode",{attrs:{id:r.type,value:r.qr_code,options:r.qrcode_options,tag:"img",margin:10,alt:""}}),e("div",[e("el-radio-group",{class:["recharge"===r.type?"ps-green-radio":"ps-radio"],on:{change:function(e){return t.changeSpecHandle(r)}},model:{value:r.spec_type,callback:function(e){t.$set(r,"spec_type",e)},expression:"item.spec_type"}},t._l(t.specList,(function(r){return e("el-radio",{key:r.value,attrs:{label:r.value}},[t._v(t._s(r.label))])})),1)],1)],1),e("div",{staticClass:"margin-top-20"},[e("el-button",{class:["recharge"===r.type?"ps-green-btn":"ps-origin-btn"],attrs:{size:"small",type:"primary"},on:{click:function(e){return t.downloadHandle(r,n)}}},[t._v("下载二维码")])],1)])})),0)},o=[],a=r("ed08"),i=r("b2e5"),c=r.n(i),l=r("21a6"),u=r.n(l);function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function f(t,e){return v(t)||y(t,e)||p(t,e)||h()}function h(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function p(t,e){if(t){if("string"==typeof t)return d(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?d(t,e):void 0}}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function y(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,c=[],l=!0,u=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=a.call(r)).done)&&(c.push(n.value),c.length!==e);l=!0);}catch(t){u=!0,o=t}finally{try{if(!l&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw o}}return c}}function v(t){if(Array.isArray(t))return t}function m(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */m=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",c=a.asyncIterator||"@@asyncIterator",l=a.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var a=e&&e.prototype instanceof b?e:b,i=Object.create(a.prototype),c=new P(n||[]);return o(i,"_invoke",{value:q(t,r,c)}),i}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",d="suspendedYield",y="executing",v="completed",g={};function b(){}function w(){}function _(){}var L={};u(L,i,(function(){return this}));var x=Object.getPrototypeOf,O=x&&x(x(A([])));O&&O!==r&&n.call(O,i)&&(L=O);var j=_.prototype=b.prototype=Object.create(L);function E(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function r(o,a,i,c){var l=h(t[o],t,a);if("throw"!==l.type){var u=l.arg,f=u.value;return f&&"object"==s(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,i,c)}),(function(t){r("throw",t,i,c)})):e.resolve(f).then((function(t){u.value=t,i(u)}),(function(t){return r("throw",t,i,c)}))}c(l.arg)}var a;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return a=a?a.then(o,o):o()}})}function q(e,r,n){var o=p;return function(a,i){if(o===y)throw Error("Generator is already running");if(o===v){if("throw"===a)throw i;return{value:t,done:!0}}for(n.method=a,n.arg=i;;){var c=n.delegate;if(c){var l=S(c,n);if(l){if(l===g)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=y;var u=h(e,r,n);if("normal"===u.type){if(o=n.done?v:d,u.arg===g)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(o=v,n.method="throw",n.arg=u.arg)}}}function S(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,S(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var a=h(o,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,g;var i=a.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,g):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function C(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function N(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function P(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(C,this),this.reset(!0)}function A(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(s(e)+" is not iterable")}return w.prototype=_,o(j,"constructor",{value:_,configurable:!0}),o(_,"constructor",{value:w,configurable:!0}),w.displayName=u(_,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,u(t,l,"GeneratorFunction")),t.prototype=Object.create(j),t},e.awrap=function(t){return{__await:t}},E(k.prototype),u(k.prototype,c,(function(){return this})),e.AsyncIterator=k,e.async=function(t,r,n,o,a){void 0===a&&(a=Promise);var i=new k(f(t,r,n,o),a);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},E(j),u(j,l,"Generator"),u(j,i,(function(){return this})),u(j,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=A,P.prototype={constructor:P,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(N),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return c.type="throw",c.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],c=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),u=n.call(i,"finallyLoc");if(l&&u){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=t,i.arg=e,a?(this.method="next",this.next=a.finallyLoc,g):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),N(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;N(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:A(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),g}},e}function g(t,e,r,n,o,a,i){try{var c=t[a](i),l=c.value}catch(t){return void r(t)}c.done?e(l):Promise.resolve(l).then(n,o)}function b(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var a=t.apply(e,r);function i(t){g(a,n,o,i,c,"next",t)}function c(t){g(a,n,o,i,c,"throw",t)}i(void 0)}))}}var w={name:"DownloadQrCode",components:{qrcode:c.a},props:{type:String,infoData:{type:Object,default:function(){return{}}},organizationData:Object,restoreHandle:Function},data:function(){return{qrcodeType:"consume",labelName:"",formOperate:"detail",isLoading:!1,specList:[{label:"50*50",value:50},{label:"55*55",value:55},{label:"60*60",value:60}],qrCodeList:[]}},computed:{checkIsFormStatus:function(){var t=!1;switch(this.operate){case"add":t=!0;break;case"detail":t="detail"!==this.formOperate;break;default:t="detail"!==this.formOperate;break}return t}},watch:{},created:function(){},mounted:function(){this.labelName=this.infoData.level_name+"-"+this.infoData.name,this.initLoad()},methods:{initLoad:function(){var t=this;return b(m().mark((function e(){return m().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.getQrCode();case 1:case"end":return e.stop()}}),e)})))()},refreshHandle:function(){this.initLoad()},searchHandle:Object(a["d"])((function(){}),300),downloadHandle:function(t,e){u.a.saveAs(document.getElementById(t.type).getAttribute("src"),this.labelName+"-"+t.label+".jpg")},getQrCode:function(){var t=this;return b(m().mark((function e(){var r,n,o,i;return m().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(a["Z"])(t.$apis.apiBackgroundPaymentPayInfoGetConsumeQrcodePost({ids:[],organization:t.organizationData.id,type:["payment","recharge","register"]}));case 3:if(r=e.sent,n=f(r,2),o=n[0],i=n[1],t.isLoading=!1,!o){e.next=11;break}return t.$message.error(o.message),e.abrupt("return");case 11:0===i.code?t.setQrCodeData(i.data):t.$message.error(i.msg);case 12:case"end":return e.stop()}}),e)})))()},setQrCodeData:function(t){var e=this;t&&t.length&&(this.qrCodeList=t.map((function(t){if(t.label="payment"===t.type?"收款二维码":"充值二维码",t.spec_type=50,t.qrcode_options={width:5*t.spec_type,height:5*t.spec_type},t.selectRadio=t.type+"_h5","payment"===t.type&&(t.radioList=[],t.h5_url)){var r=Object(a["v"])(t.h5_url);r.organization_name=e.labelName,t.qr_code=t.h5_url.split("?")[0]+"?"+Object(a["L"])(r,!0),t.radioList.push({label:"消费收款码-H5",value:t.type+"_h5",url:t.qr_code})}if("recharge"===t.type){if(t.radioList=[],t.h5_url){var n=Object(a["v"])(t.h5_url);n.organization_name=e.labelName,t.qr_code=t.h5_url.split("?")[0]+"?"+Object(a["L"])(n,!0),t.radioList.push({label:"充值收款-H5",value:t.type+"_h5",url:t.qr_code})}if(t.mini_url){var o=Object(a["v"])(t.mini_url);o.organization_name=e.labelName;var i=t.mini_url.split("?")[0]+"?"+Object(a["L"])(o,!0);t.qr_code||(t.qr_code=i),t.radioList.push({label:"充值收款-小程序",value:t.type+"_mini",url:i})}}if("register"===t.type&&(t.radioList=[],t.h5_url)){var c=Object(a["v"])(t.h5_url);c.organization_name=e.labelName,t.qr_code=t.h5_url.split("?")[0]+"?"+Object(a["L"])(c,!0),t.radioList.push({label:"用户自注册",value:t.type+"_h5",url:t.qr_code})}return t})))},changeSpecHandle:function(t){t.qrcode_options={width:5*t.spec_type,height:5*t.spec_type}},changeQrcoeType:function(t,e){var r="";e.radioList.forEach((function(e){e.value===t&&(r=e.url)})),this.$set(e,"qr_code",r)}}},_=w,L=(r("1b74"),r("2877")),x=Object(L["a"])(_,n,o,!1,null,null,null);e["default"]=x.exports},"1b74":function(t,e,r){"use strict";r("21b5")},"21b5":function(t,e,r){}}]);