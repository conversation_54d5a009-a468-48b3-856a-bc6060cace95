(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-inventory-components-related-document-RelatedDocument","view-merchant-inventory-components-related-document-PurchaseOrder","view-merchant-inventory-components-related-document-SettlementOrder"],{1862:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"delivery-order-box"},[e("div",{staticClass:"info-wrap"},[e("div",{staticClass:"title"},[t._v("基本信息")]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("供应商名称：")]),t._v(t._s(t.infoData.supplier_manage_name))]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("单据编号：")]),t._v(t._s(t.detailData.trade_no))]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("配送日期：")]),t._v(t._s(t.detailData.delivery_date))]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("预计送达时间：")]),t._v(t._s(t.detailData.expect_arrival_date))]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("配送状态：")]),t._v(t._s(t.detailData.order_status_alias))]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("创建时间：")]),t._v(t._s(t.detailData.create_time))])]),e("div",{staticClass:"info-wrap"},[e("div",{staticClass:"title"},[t._v("配送信息")]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("配送温度：")]),t._v(t._s(t.detailData.delivery_temperature)+"°c")])]),e("DriverInformation",{attrs:{"driver-list":t.detailData.driver_info}}),e("VehicleInformation",{attrs:{"vehicle-list":t.detailData.vehicle_info}}),e("div",{staticClass:"info-wrap"},[e("div",{staticClass:"title"},[t._v("物资信息")]),"detail"!==t.type?e("div",[t._v("合计金额：￥"+t._s(t.totalPrice))]):t._e(),e("el-table",{ref:"tableData",staticStyle:{width:"100%"},attrs:{data:t.detailData.ingredient_data,stripe:"",size:"mini","max-height":"600","header-row-class-name":"ps-table-header-row"}},t._l(t.tableSettings,(function(t){return e("table-column",{key:t.key,attrs:{col:t}})})),1)],1)],1)},a=[],i=r("ed08"),o=r("2c74"),c=r("d8a4"),s=r("da92");function l(t){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},l(t)}function u(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */u=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function p(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,o=Object.create(i.prototype),c=new T(n||[]);return a(o,"_invoke",{value:j(t,r,c)}),o}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=p;var h="suspendedStart",v="suspendedYield",y="executing",m="completed",g={};function b(){}function _(){}function w(){}var x={};f(x,o,(function(){return this}));var L=Object.getPrototypeOf,O=L&&L(L(N([])));O&&O!==r&&n.call(O,o)&&(x=O);var D=w.prototype=b.prototype=Object.create(x);function S(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function E(t,e){function r(a,i,o,c){var s=d(t[a],t,i);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==l(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,o,c)}),(function(t){r("throw",t,o,c)})):e.resolve(f).then((function(t){u.value=t,o(u)}),(function(t){return r("throw",t,o,c)}))}c(s.arg)}var i;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return i=i?i.then(a,a):a()}})}function j(e,r,n){var a=h;return function(i,o){if(a===y)throw Error("Generator is already running");if(a===m){if("throw"===i)throw o;return{value:t,done:!0}}for(n.method=i,n.arg=o;;){var c=n.delegate;if(c){var s=k(c,n);if(s){if(s===g)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===h)throw a=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=y;var l=d(e,r,n);if("normal"===l.type){if(a=n.done?m:v,l.arg===g)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(a=m,n.method="throw",n.arg=l.arg)}}}function k(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,k(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var i=d(a,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,g;var o=i.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,g):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function C(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function P(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(C,this),this.reset(!0)}function N(e){if(e||""===e){var r=e[o];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,i=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(l(e)+" is not iterable")}return _.prototype=w,a(D,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:_,configurable:!0}),_.displayName=f(w,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===_||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,f(t,s,"GeneratorFunction")),t.prototype=Object.create(D),t},e.awrap=function(t){return{__await:t}},S(E.prototype),f(E.prototype,c,(function(){return this})),e.AsyncIterator=E,e.async=function(t,r,n,a,i){void 0===i&&(i=Promise);var o=new E(p(t,r,n,a),i);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},S(D),f(D,s,"Generator"),f(D,o,(function(){return this})),f(D,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=N,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(P),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return c.type="throw",c.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],c=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var s=n.call(o,"catchLoc"),l=n.call(o,"finallyLoc");if(s&&l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,g):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),P(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;P(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:N(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),g}},e}function f(t,e){return y(t)||v(t,e)||d(t,e)||p()}function p(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(t,e){if(t){if("string"==typeof t)return h(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?h(t,e):void 0}}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function v(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,i,o,c=[],s=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);s=!0);}catch(t){l=!0,a=t}finally{try{if(!s&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw a}}return c}}function y(t){if(Array.isArray(t))return t}function m(t,e,r,n,a,i,o){try{var c=t[i](o),s=c.value}catch(t){return void r(t)}c.done?e(s):Promise.resolve(s).then(n,a)}function g(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var i=t.apply(e,r);function o(t){m(i,n,a,o,c,"next",t)}function c(t){m(i,n,a,o,c,"throw",t)}o(void 0)}))}}var b={name:"DeliveryOrderBox",props:{type:{type:String,default:"detail"},params:{type:Object,default:function(){}},api:{type:String,default:"apiBackgroundDrpVendorDataVendorDeliveryDetailListPost"},infoData:{type:Object,default:function(){}}},components:{VehicleInformation:o["default"],DriverInformation:c["default"]},data:function(){return{isLoading:!1,tableData:[],tableSettings:[{label:"物资名称",key:"materials_name"},{label:"配送数量",key:"purchase_count"},{label:"成本价",key:"unit_price"},{label:"合计金额",key:"total_price"},{label:"保质期",key:"valid_date"}],detailData:{},totalPrice:0}},computed:{},watch:{},mounted:function(){this.initLoad()},methods:{initLoad:function(){this.getDataInfo()},getDataInfo:function(){var t=this;return g(u().mark((function e(){var r,n,a,o,c,l;return u().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.isLoading){e.next=2;break}return e.abrupt("return");case 2:if(r=t.api,t.api||"detail"!==t.type||(r="apiBackgroundDrpVendorDataVendorDeliveryDetailListPost"),r){e.next=6;break}return e.abrupt("return",t.$message.error("缺少参数，请检查！"));case 6:return t.isLoading=!0,e.next=9,t.$to(t.$apis[r](t.params));case 9:if(n=e.sent,a=f(n,2),o=a[0],c=a[1],t.isLoading=!1,t.detailData={},!o){e.next=18;break}return t.$message.error(o.message),e.abrupt("return");case 18:0===c.code?(l=c.data||{},t.totalPrice=0,null!==l&&void 0!==l&&l.ingredient_data&&(l.ingredient_data=l.ingredient_data.map((function(e){return t.totalPrice=s["a"].plus(t.totalPrice,e.total_price),e.purchase_count=e.purchase_count+e.purchase_unit,e.unit_price="￥"+Object(i["i"])(e.unit_price)+"/"+e.purchase_unit,e.total_price="￥"+Object(i["i"])(e.total_price),e.valid_date=e.start_valid_date+"-"+e.end_valid_date,e}))),t.totalPrice=Object(i["i"])(t.totalPrice),t.detailData=l||{}):t.$message.error(c.msg);case 19:case"end":return e.stop()}}),e)})))()}}},_=b,w=(r("4b05"),r("2877")),x=Object(w["a"])(_,n,a,!1,null,"1bafcdeb",null);e["default"]=x.exports},"4b05":function(t,e,r){"use strict";r("4b8b")},"4b8b":function(t,e,r){},"4e0f":function(t,e,r){},"651d":function(t,e,r){"use strict";r("e8c5")},"76b7":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"delivery-order-box"},[e("div",{staticClass:"info-wrap"},[e("div",{staticClass:"title"},[t._v("基本信息")]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("单据编号：")]),t._v(t._s(t.detailData.trade_no))]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("创建时间：")]),t._v(t._s(t.detailData.create_time))]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("采购日期：")]),t._v(t._s(t.detailData.purchase_time))]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("经手人：")]),t._v(t._s(t.detailData.account_name))])]),e("div",{staticClass:"info-wrap"},[e("div",{staticClass:"title"},[t._v("物资信息")]),e("div",[t._v("合计金额：￥"+t._s(t.totalPrice))]),e("el-table",{ref:"tableData",staticStyle:{width:"100%"},attrs:{data:t.detailData.materials_detail,stripe:"",size:"mini","header-row-class-name":"ps-table-header-row"}},t._l(t.tableSettings,(function(t){return e("table-column",{key:t.key,attrs:{col:t}})})),1)],1)])},a=[],i=r("ed08"),o=r("da92");function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function p(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,o=Object.create(i.prototype),c=new T(n||[]);return a(o,"_invoke",{value:j(t,r,c)}),o}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=p;var h="suspendedStart",v="suspendedYield",y="executing",m="completed",g={};function b(){}function _(){}function w(){}var x={};f(x,o,(function(){return this}));var L=Object.getPrototypeOf,O=L&&L(L(N([])));O&&O!==r&&n.call(O,o)&&(x=O);var D=w.prototype=b.prototype=Object.create(x);function S(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function E(t,e){function r(a,i,o,s){var l=d(t[a],t,i);if("throw"!==l.type){var u=l.arg,f=u.value;return f&&"object"==c(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,o,s)}),(function(t){r("throw",t,o,s)})):e.resolve(f).then((function(t){u.value=t,o(u)}),(function(t){return r("throw",t,o,s)}))}s(l.arg)}var i;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return i=i?i.then(a,a):a()}})}function j(e,r,n){var a=h;return function(i,o){if(a===y)throw Error("Generator is already running");if(a===m){if("throw"===i)throw o;return{value:t,done:!0}}for(n.method=i,n.arg=o;;){var c=n.delegate;if(c){var s=k(c,n);if(s){if(s===g)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===h)throw a=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=y;var l=d(e,r,n);if("normal"===l.type){if(a=n.done?m:v,l.arg===g)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(a=m,n.method="throw",n.arg=l.arg)}}}function k(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,k(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var i=d(a,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,g;var o=i.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,g):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function C(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function P(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(C,this),this.reset(!0)}function N(e){if(e||""===e){var r=e[o];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,i=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(c(e)+" is not iterable")}return _.prototype=w,a(D,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:_,configurable:!0}),_.displayName=f(w,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===_||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,f(t,u,"GeneratorFunction")),t.prototype=Object.create(D),t},e.awrap=function(t){return{__await:t}},S(E.prototype),f(E.prototype,l,(function(){return this})),e.AsyncIterator=E,e.async=function(t,r,n,a,i){void 0===i&&(i=Promise);var o=new E(p(t,r,n,a),i);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},S(D),f(D,u,"Generator"),f(D,o,(function(){return this})),f(D,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=N,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(P),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return c.type="throw",c.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],c=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var s=n.call(o,"catchLoc"),l=n.call(o,"finallyLoc");if(s&&l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,g):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),P(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;P(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:N(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),g}},e}function l(t,e){return h(t)||d(t,e)||f(t,e)||u()}function u(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(t,e){if(t){if("string"==typeof t)return p(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?p(t,e):void 0}}function p(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function d(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,i,o,c=[],s=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);s=!0);}catch(t){l=!0,a=t}finally{try{if(!s&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw a}}return c}}function h(t){if(Array.isArray(t))return t}function v(t,e,r,n,a,i,o){try{var c=t[i](o),s=c.value}catch(t){return void r(t)}c.done?e(s):Promise.resolve(s).then(n,a)}function y(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var i=t.apply(e,r);function o(t){v(i,n,a,o,c,"next",t)}function c(t){v(i,n,a,o,c,"throw",t)}o(void 0)}))}}var m={name:"DeliveryOrderBox",props:{type:{type:String,default:"detail"},params:{type:Object,default:function(){}},api:{type:String,default:""},infoData:{type:Object,default:function(){}}},components:{},data:function(){return{isLoading:!1,tableData:[],tableSettings:[{label:"物资名称",key:"materials_name"},{label:"采购数量",key:"count"},{label:"成本价",key:"ref_unit_price"},{label:"合计金额",key:"total"}],detailData:{},totalPrice:0}},computed:{},watch:{},mounted:function(){this.initLoad()},methods:{initLoad:function(){this.getDeliveryNoteInfo()},getDeliveryNoteInfo:function(){var t=this;return y(s().mark((function e(){var r,n,a,c,u,f;return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.isLoading){e.next=2;break}return e.abrupt("return");case 2:if(r=t.api,r){e.next=5;break}return e.abrupt("return",t.$message.error("缺少参数，请检查！"));case 5:return t.isLoading=!0,e.next=8,t.$to(t.$apis[r](t.params));case 8:if(n=e.sent,a=l(n,2),c=a[0],u=a[1],t.isLoading=!1,t.detailData={},!c){e.next=17;break}return t.$message.error(c.message),e.abrupt("return");case 17:0===u.code?(f=u.data||{},t.totalPrice=0,null!==f&&void 0!==f&&f.materials_detail&&(f.materials_detail=f.materials_detail.map((function(e){return t.totalPrice=o["a"].plus(t.totalPrice,e.total),e.count=e.count+(e.unit_name||""),e.ref_unit_price="￥"+Object(i["i"])(e.ref_unit_price)+"/"+e.unit_name,e.total="￥"+Object(i["i"])(e.total),e}))),t.totalPrice=Object(i["i"])(t.totalPrice),t.detailData=f||{}):t.$message.error(u.msg);case 18:case"end":return e.stop()}}),e)})))()}}},g=m,b=(r("651d"),r("2877")),_=Object(b["a"])(g,n,a,!1,null,"664e4f80",null);e["default"]=_.exports},"79d9":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("custom-drawer",t._g(t._b({staticClass:"related-drawer-wrapper",attrs:{title:t.title,show:t.visible,direction:"rtl",wrapperClosable:!1,size:t.size,"destroy-on-close":"",fixedFooter:"","confirm-text":"确定"},on:{"update:show":function(e){t.visible=e},close:t.handlerClose,cancel:t.clickCancleHandle,confirm:t.clickConfirmHandle}},"custom-drawer",t.$attrs,!1),t.$listeners),[e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"related-document p-l-20 p-r-20"},["detail"!==t.type?e("div",[e("el-radio-group",{staticClass:"ps-radio-btn m-t-10",on:{change:t.changeTabHandle},model:{value:t.tabType,callback:function(e){t.tabType=e},expression:"tabType"}},t._l(t.tabTypeList,(function(r){return e("el-radio-button",{key:r.value,attrs:{label:r.value}},[t._v(t._s(r.label))])})),1),"purchase"===t.tabType?e("PurchaseOrder",{attrs:{type:t.type,params:t.apiParams,api:t.api,infoData:t.infoData}}):t._e(),"delivery"===t.tabType?e("DeliveryOrder",{attrs:{type:t.type,params:t.apiParams,api:t.api,infoData:t.infoData}}):t._e(),"receiving"===t.tabType?e("ReceiptOrder",{attrs:{type:t.type,params:t.apiParams,api:t.api,infoData:t.infoData}}):t._e(),"finalstatement"===t.tabType?e("SettlementOrder",{attrs:{type:t.type,params:t.apiParams,api:t.api,infoData:t.infoData}}):t._e()],1):e("div",["purchase"===t.orderType?e("PurchaseOrder",{attrs:{type:t.type,params:t.apiParams,api:t.api,infoData:t.infoData}}):t._e(),"delivery"===t.orderType?e("DeliveryOrder",{attrs:{type:t.type,params:t.apiParams,api:t.api,infoData:t.infoData}}):t._e(),"receiving"===t.orderType?e("ReceiptOrder",{attrs:{type:t.type,params:t.apiParams,api:t.api,infoData:t.infoData}}):t._e(),"finalstatement"===t.orderType?e("SettlementOrder",{attrs:{type:t.type,params:t.apiParams,api:t.api,infoData:t.infoData}}):t._e()],1)])])},a=[],i=r("1862"),o=r("393b6"),c=r("7dd1"),s=r("76b7"),l={name:"RelatedDocument",props:{showdialog:Boolean,loading:Boolean,title:{type:String,default:"关联单据"},size:{type:[Number,String],default:680},orderType:{type:String,default:""},type:{type:String,default:""},params:{type:Object,default:function(){}},infoData:{type:Object,default:function(){}},api:{type:String,default:""},confirm:Function},components:{DeliveryOrder:i["default"],ReceiptOrder:o["default"],SettlementOrder:c["default"],PurchaseOrder:s["default"]},data:function(){return{isLoading:!1,tabType:"",defaultTabList:[{label:"采购单",value:"purchase"},{label:"配送单",value:"delivery"},{label:"收货单",value:"receiving"},{label:"结算单",value:"finalstatement"}],tabTypeList:[]}},computed:{visible:{get:function(){return this.showdialog},set:function(t){this.$emit("update:showdialog",t)}},apiParams:function(){var t={};return t="detail"===this.type?{id:this.infoData.id}:{main_type:this.orderType,second_type:this.tabType,main_id:this.infoData.id},t}},watch:{showdialog:function(t){t&&this.initLoad()}},mounted:function(){},methods:{initLoad:function(){var t=this;"detail"!==this.type&&(this.tabTypeList=this.defaultTabList.filter((function(e){return e.value!==t.orderType})),this.tabType=this.tabTypeList[0].value)},changeTabHandle:function(t){switch(t){case"deliveryOrder":break;case"receiptOrder":break;case"settlementOrder":break;case"purchaseOrder":break}},clickConfirmHandle:function(){this.visible=!1},clickCancleHandle:function(){this.visible=!1},handlerClose:function(){this.tabType=""}}},u=l,f=(r("cd7f"),r("2877")),p=Object(f["a"])(u,n,a,!1,null,"1f665142",null);e["default"]=p.exports},"7ad6":function(t,e,r){},"7dd1":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"settlement-order-box"},[e("div",{staticClass:"info-wrap"},[e("div",{staticClass:"title"},[t._v("基本信息")]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("单据编号：")]),t._v(t._s(t.detailData.trade_no))]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("创建时间：")]),t._v(t._s(t.detailData.create_time))]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("结算时间：")]),t._v(t._s(t.detailData.expect_arrival_date))]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("结算状态：")]),t._v(t._s(t.detailData.expect_arrival_date))]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("结算金额：")]),t._v(t._s(t.detailData.expect_arrival_date))]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("审批人：")]),t._v(t._s(t.detailData.expect_arrival_date))]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("审批时间：")]),t._v(t._s(t.detailData.expect_arrival_date))]),"detail"===t.type?e("div",[t._m(0),t._m(1),e("div",{staticClass:"form-item"},[t._v("xxxx"),e("el-button",{directives:[{name:"clipboard",rawName:"v-clipboard:copy",value:"xxxx",expression:"'xxxx'",arg:"copy"},{name:"clipboard",rawName:"v-clipboard:success",value:t.clipboardSuccess,expression:"clipboardSuccess",arg:"success"}]},[t._v("复制")])],1)]):t._e()])])},a=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("单据凭证：")]),t._v("--")])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("关联收货单据：")]),t._v("--")])}],i=r("ed08");function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},s=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function p(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,o=Object.create(i.prototype),c=new T(n||[]);return a(o,"_invoke",{value:j(t,r,c)}),o}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=p;var h="suspendedStart",v="suspendedYield",y="executing",m="completed",g={};function b(){}function _(){}function w(){}var x={};f(x,s,(function(){return this}));var L=Object.getPrototypeOf,O=L&&L(L(N([])));O&&O!==r&&n.call(O,s)&&(x=O);var D=w.prototype=b.prototype=Object.create(x);function S(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function E(t,e){function r(a,i,c,s){var l=d(t[a],t,i);if("throw"!==l.type){var u=l.arg,f=u.value;return f&&"object"==o(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,c,s)}),(function(t){r("throw",t,c,s)})):e.resolve(f).then((function(t){u.value=t,c(u)}),(function(t){return r("throw",t,c,s)}))}s(l.arg)}var i;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return i=i?i.then(a,a):a()}})}function j(e,r,n){var a=h;return function(i,o){if(a===y)throw Error("Generator is already running");if(a===m){if("throw"===i)throw o;return{value:t,done:!0}}for(n.method=i,n.arg=o;;){var c=n.delegate;if(c){var s=k(c,n);if(s){if(s===g)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===h)throw a=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=y;var l=d(e,r,n);if("normal"===l.type){if(a=n.done?m:v,l.arg===g)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(a=m,n.method="throw",n.arg=l.arg)}}}function k(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,k(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var i=d(a,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,g;var o=i.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,g):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function C(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function P(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(C,this),this.reset(!0)}function N(e){if(e||""===e){var r=e[s];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,i=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(o(e)+" is not iterable")}return _.prototype=w,a(D,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:_,configurable:!0}),_.displayName=f(w,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===_||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,f(t,u,"GeneratorFunction")),t.prototype=Object.create(D),t},e.awrap=function(t){return{__await:t}},S(E.prototype),f(E.prototype,l,(function(){return this})),e.AsyncIterator=E,e.async=function(t,r,n,a,i){void 0===i&&(i=Promise);var o=new E(p(t,r,n,a),i);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},S(D),f(D,u,"Generator"),f(D,s,(function(){return this})),f(D,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=N,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(P),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return c.type="throw",c.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],c=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var s=n.call(o,"catchLoc"),l=n.call(o,"finallyLoc");if(s&&l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,g):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),P(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;P(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:N(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),g}},e}function s(t,e){return d(t)||p(t,e)||u(t,e)||l()}function l(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(t,e){if(t){if("string"==typeof t)return f(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?f(t,e):void 0}}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function p(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,i,o,c=[],s=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);s=!0);}catch(t){l=!0,a=t}finally{try{if(!s&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw a}}return c}}function d(t){if(Array.isArray(t))return t}function h(t,e,r,n,a,i,o){try{var c=t[i](o),s=c.value}catch(t){return void r(t)}c.done?e(s):Promise.resolve(s).then(n,a)}function v(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var i=t.apply(e,r);function o(t){h(i,n,a,o,c,"next",t)}function c(t){h(i,n,a,o,c,"throw",t)}o(void 0)}))}}var y={name:"RelatedDocument",props:{type:{type:String,default:"detail"},params:{type:Object,default:function(){}},api:{type:String,default:"apiBackgroundDrpVendorDataVendorDeliveryDetailListPost"},infoData:{type:Object,default:function(){}}},components:{},data:function(){return{isLoading:!1,tabType:"settlementOrder",tableData:[],tableSettings:[{label:"物资名称",key:"name"},{label:"收货数量",key:"name1"},{label:"单价",key:"name2"},{label:"合计金额",key:"name3"},{label:"保质期",key:"name4"}],vehicleList:[],detailData:{},totalPrice:0}},computed:{},watch:{},mounted:function(){this.initLoad()},methods:{initLoad:function(){"detail"!==this.type?this.getDataInfo():this.detailData=Object(i["f"])(this.infoData)},getDataInfo:function(){var t=this;return v(c().mark((function e(){var r,n,a,i;return c().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.isLoading){e.next=2;break}return e.abrupt("return");case 2:if(t.api){e.next=4;break}return e.abrupt("return",t.$message.error("缺少参数，请检查！"));case 4:return t.isLoading=!0,e.next=7,t.$to(t.$apis[t.api](t.params));case 7:if(r=e.sent,n=s(r,2),a=n[0],i=n[1],t.isLoading=!1,t.detailData={},!a){e.next=16;break}return t.$message.error(a.message),e.abrupt("return");case 16:0===i.code?t.detailData=i.data:t.$message.error(i.msg);case 17:case"end":return e.stop()}}),e)})))()},clipboardSuccess:function(){this.$message({message:"复制成功",type:"success",duration:1500})}}},m=y,g=(r("e965"),r("2877")),b=Object(g["a"])(m,n,a,!1,null,"e4a69c76",null);e["default"]=b.exports},cd7f:function(t,e,r){"use strict";r("7ad6")},e8c5:function(t,e,r){},e965:function(t,e,r){"use strict";r("4e0f")}}]);