(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-diet-manage-ThreeMealList","view-super-health-system-threemeallist"],{6009:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"SuperMemberList container-wrapper"},[e("refresh-tool",{on:{refreshPage:t.refreshHandle}}),e("search-form",{ref:"searchRef",attrs:{"form-setting":t.searchFormSetting},on:{search:t.searchHandle}}),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("数据列表")]),e("div",{staticClass:"align-r"},[e("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(e){return t.gotoAddOrEdit("add")}}},[t._v("新建")])],1)]),e("div",{staticClass:"table-content"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},t._l(t.tableSettings,(function(r){return e("table-column",{key:r.key,attrs:{col:r},scopedSlots:t._u([{key:"operation",fn:function(r){var n=r.row;return["enable"===n.status?e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.clickStatus(n,"disable")}}},[t._v(" 关闭 ")]):t._e(),"disable"===n.status?e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.clickStatus(n,"enable")}}},[t._v(" 开启 ")]):t._e(),e("el-button",{attrs:{type:"text",disabled:"enable"===n.status,size:"small"},on:{click:function(e){return t.gotoAddOrEdit("modify",n)}}},[t._v(" 编辑 ")])]}}],null,!0)})})),1)],1),e("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[e("el-pagination",{staticClass:"ps-text",attrs:{"current-page":t.currentPage,"page-size":t.pageSize,layout:"total, prev, pager, next",total:t.totalCount,background:"","popper-class":"ps-popper-select"},on:{"current-change":t.handleCurrentChange}})],1)])],1)},a=[],o=r("ed08"),i=r("dfd5");function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function u(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */u=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function h(t,e,r,n){var o=e&&e.prototype instanceof b?e:b,i=Object.create(o.prototype),s=new C(n||[]);return a(i,"_invoke",{value:P(t,r,s)}),i}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var d="suspendedStart",y="suspendedYield",g="executing",v="completed",m={};function b(){}function w(){}function L(){}var O={};f(O,i,(function(){return this}));var S=Object.getPrototypeOf,x=S&&S(S(F([])));x&&x!==r&&n.call(x,i)&&(O=x);var k=L.prototype=b.prototype=Object.create(O);function _(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function j(t,e){function r(a,o,i,u){var c=p(t[a],t,o);if("throw"!==c.type){var l=c.arg,f=l.value;return f&&"object"==s(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,i,u)}),(function(t){r("throw",t,i,u)})):e.resolve(f).then((function(t){l.value=t,i(l)}),(function(t){return r("throw",t,i,u)}))}u(c.arg)}var o;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return o=o?o.then(a,a):a()}})}function P(e,r,n){var a=d;return function(o,i){if(a===g)throw Error("Generator is already running");if(a===v){if("throw"===o)throw i;return{value:t,done:!0}}for(n.method=o,n.arg=i;;){var s=n.delegate;if(s){var u=E(s,n);if(u){if(u===m)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===d)throw a=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=g;var c=p(e,r,n);if("normal"===c.type){if(a=n.done?v:y,c.arg===m)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(a=v,n.method="throw",n.arg=c.arg)}}}function E(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,E(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var o=p(a,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,m;var i=o.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function T(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function M(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function C(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(T,this),this.reset(!0)}function F(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,o=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(s(e)+" is not iterable")}return w.prototype=L,a(k,"constructor",{value:L,configurable:!0}),a(L,"constructor",{value:w,configurable:!0}),w.displayName=f(L,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,L):(t.__proto__=L,f(t,l,"GeneratorFunction")),t.prototype=Object.create(k),t},e.awrap=function(t){return{__await:t}},_(j.prototype),f(j.prototype,c,(function(){return this})),e.AsyncIterator=j,e.async=function(t,r,n,a,o){void 0===o&&(o=Promise);var i=new j(h(t,r,n,a),o);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},_(k),f(k,l,"Generator"),f(k,i,(function(){return this})),f(k,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=F,C.prototype={constructor:C,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(M),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return s.type="throw",s.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],s=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var u=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(u&&c){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(u){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,m):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),M(r),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;M(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:F(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),m}},e}function c(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function l(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?c(Object(r),!0).forEach((function(e){f(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function f(t,e,r){return(e=h(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function h(t){var e=p(t,"string");return"symbol"==s(e)?e:e+""}function p(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function d(t,e){return b(t)||m(t,e)||g(t,e)||y()}function y(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function g(t,e){if(t){if("string"==typeof t)return v(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?v(t,e):void 0}}function v(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function m(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,o,i,s=[],u=!0,c=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(s.push(n.value),s.length!==e);u=!0);}catch(t){c=!0,a=t}finally{try{if(!u&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(c)throw a}}return s}}function b(t){if(Array.isArray(t))return t}function w(t,e,r,n,a,o,i){try{var s=t[o](i),u=s.value}catch(t){return void r(t)}s.done?e(u):Promise.resolve(u).then(n,a)}function L(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var o=t.apply(e,r);function i(t){w(o,n,a,i,s,"next",t)}function s(t){w(o,n,a,i,s,"throw",t)}i(void 0)}))}}var O={name:"SuperMemberList",props:{},data:function(){return{isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableSettings:[{label:"名称",key:"name"},{label:"餐段",key:"meal_type_alias"},{label:"浏览数",key:"read_count"},{label:"操作人",key:"operator_name"},{label:"操作时间",key:"update_time"},{label:"状态",key:"status_alias"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"200"}],tableData:[{name:1}],searchFormSetting:{name:{type:"input",label:"名称",value:"",placeholder:"请输入名称"},meal_type:{type:"select",value:"",label:"餐段",dataList:[{label:"全部",value:""},{label:"早餐",value:"breakfast"},{label:"午餐",value:"lunch"},{label:"晚餐",value:"dinner"}]},status:{type:"select",value:"",label:"状态",dataList:[{name:"全部",id:""},{name:"开启",id:"enable"},{name:"关闭",id:"disable"}],listNameKey:"name",listValueKey:"id"}}}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){var t=Object(o["x"])("allFoodList");JSON.parse(t)&&JSON.parse(t).length||this.getFoodlist(),this.getThreeMealList()},searchHandle:Object(o["d"])((function(){this.dialogVisible=!1,this.currentPage=1,this.getThreeMealList()}),300),refreshHandle:function(){this.currentPage=1,this.tableData=[],this.initLoad()},getThreeMealList:function(){var t=this;return L(u().mark((function e(){var r,n,a,i;return u().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(o["Z"])(t.$apis.apiBackgroundHealthyThreeMealListPost(l(l({},t.formatQueryParams(t.searchFormSetting)),{},{page:t.currentPage,page_size:t.pageSize})));case 3:if(r=e.sent,n=d(r,2),a=n[0],i=n[1],t.isLoading=!1,!a){e.next=11;break}return t.$message.error(a.message),e.abrupt("return");case 11:0===i.code?(t.tableData=i.data.results,t.totalCount=i.data.count):t.$message.error(i.msg);case 12:case"end":return e.stop()}}),e)})))()},clickStatus:function(t,e){this.setThreeMealModifyStatus(t,e)},setThreeMealModifyStatus:function(t,e){var r=this;return L(u().mark((function n(){var a,i,s,c;return u().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return r.isLoading=!0,n.next=3,Object(o["Z"])(r.$apis.apiBackgroundHealthyThreeMealModifyStatusPost({ids:[t.id],status:e}));case 3:if(a=n.sent,i=d(a,2),s=i[0],c=i[1],r.isLoading=!1,!s){n.next=11;break}return r.$message.error(s.message),n.abrupt("return");case 11:0===c.code?r.getThreeMealList():r.$message.error(c.msg);case 12:case"end":return n.stop()}}),n)})))()},handleSizeChange:function(t){this.pageSize=t,this.getThreeMealList()},handleCurrentChange:function(t){this.currentPage=t,this.getThreeMealList()},gotoAddOrEdit:function(t,e,r){var n={};"modify"===t&&(n={id:e.id}),this.$router.push({name:"SuperAddOrEditThreeMealList",params:{type:t},query:n})},formatQueryParams:function(t){var e={};for(var r in t)""!==t[r].value&&("select_time"!==r?e[r]=t[r].value:t[r].value&&t[r].value.length>0&&(e.start_time=t[r].value[0],e.end_time=t[r].value[1]));return e},getFoodlist:i["getFoodlist"]}},S=O,x=(r("c6d0"),r("2877")),k=Object(x["a"])(S,n,a,!1,null,"a663e4b4",null);e["default"]=k.exports},"822d":function(t,e,r){},c6d0:function(t,e,r){"use strict";r("822d")},dfd5:function(t,e,r){"use strict";function n(){var t=this;return new Promise((function(e,r){t.$apis.apiBackgroundAdminFoodListPost({page:1,page_size:999999}).then((function(t){sessionStorage.setItem("allFoodList",t.data.results?JSON.stringify(t.data.results):"[]"),e(t)})).catch((function(t){r(t)}))}))}r.r(e),r.d(e,"getFoodlist",(function(){return n}))}}]);