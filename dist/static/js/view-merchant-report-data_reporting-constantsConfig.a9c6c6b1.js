(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-report-data_reporting-constantsConfig"],{f2ba:function(e,a,l){"use strict";l.r(a),l.d(a,"recentSevenDay",(function(){return i})),l.d(a,"DeviceCostForm",(function(){return s}));var t=l("5a0c"),i=[t().subtract(7,"day").format("YYYY-MM-DD"),t().format("YYYY-MM-DD")],s={select_time:{type:"daterange",label:"搜索日期",clearable:!1,value:i},org_ids:{type:"organizationSelect",value:[],label:"消费点",listNameKey:"name",listValueKey:"id",dataList:[],checkStrictly:!0,multiple:!0,collapseTags:!0,clearable:!0,customOrgChange:!0},device_ids:{type:"select",value:[],label:"设备名称",placeholder:"请选择设备名称",listNameKey:"device_name",listValueKey:"device_id",dataList:[],clearable:!0,collapseTags:!0,multiple:!0},device_type:{type:"select",value:"",label:"设备类型",listNameKey:"name",listValueKey:"key",dataList:[],clearable:!0},add_data_list:{type:"select",label:"其他",value:[],multiple:!0,collapseTags:!0,placeholder:"请选择对账状态",dataList:[{label:"加入餐补",value:"add_food_subsidy_fee"},{label:"加入访客记账",value:"add_order_visitor_fee"}]}}}}]);