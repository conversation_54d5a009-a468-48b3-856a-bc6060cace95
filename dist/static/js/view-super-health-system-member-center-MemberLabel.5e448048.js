(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-member-center-MemberLabel","view-super-health-system-member-center-constants"],{"0873":function(e,t,a){"use strict";a("fbe5")},"83d09":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"SuperMemberLabel container-wrapper"},[t("refresh-tool",{on:{refreshPage:e.refreshHandle}}),t("search-form",{ref:"searchRef",attrs:{"form-setting":e.searchFormSetting},on:{search:e.searchHandle,reset:e.resetHandler}}),t("div",{staticClass:"table-wrapper"},[t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[e._v("数据列表")]),t("div",{staticClass:"align-r"},[t("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(t){return e.gotoAddOrEdit("add")}}},[e._v("新建")])],1)]),t("div",{staticClass:"table-content"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},[t("el-table-column",{attrs:{prop:"name",label:"标签名称",align:"center",width:"180"}}),t("el-table-column",{attrs:{prop:"type",label:"标签类型",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s("auto"===a.row.type?"自动":"手动"))])]}}])}),t("el-table-column",{attrs:{prop:"level",label:"标签条件",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return["auto"===a.row.type?t("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.openDialog(a.row)}}},[e._v("查看")]):t("div",[e._v("无")])]}}])}),t("el-table-column",{attrs:{prop:"remark",label:"说明",align:"center","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{prop:"update_time",label:"操作时间",align:"center"}}),t("el-table-column",{attrs:{prop:"operator_name",label:"操作人",align:"center"}}),t("el-table-column",{attrs:{fixed:"right",label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.gotoAddOrEdit("edit",a.row)}}},[e._v("编辑")]),t("el-button",{staticClass:"ps-red",attrs:{type:"text",size:"small"},on:{click:function(t){return e.delMemberLabel(a.row.id)}}},[e._v("删除")])]}}])})],1)],1),t("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[t("el-pagination",{staticClass:"ps-text",attrs:{"current-page":e.currentPage,"page-size":e.pageSize,layout:"total, prev, pager, next",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"current-change":e.handleCurrentChange}})],1)]),t("el-dialog",{attrs:{title:"标签条件",visible:e.dialogVisible,width:"400px",top:"20vh","custom-class":"ps-dialog","close-on-click-modal":!1},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("div",[e.selectInfo.member_gt_days>-1?t("div",{staticClass:"dialog-item"},[e._v("会员有效期大于："+e._s(e.selectInfo.member_gt_days)+"天")]):e._e(),e.selectInfo.year_gt_count>-1?t("div",{staticClass:"dialog-item"},[e._v("会员年卡购买次数大于："+e._s(e.selectInfo.year_gt_count)+"次")]):e._e(),e.selectInfo.season_gt_count>-1?t("div",{staticClass:"dialog-item"},[e._v("会员季卡购买次数大于："+e._s(e.selectInfo.season_gt_count)+"次")]):e._e(),e.selectInfo.month_gt_count>-1?t("div",{staticClass:"dialog-item"},[e._v("会员月卡购买次数大于："+e._s(e.selectInfo.month_gt_count)+"次")]):e._e(),e.selectInfo.week_gt_count>-1?t("div",{staticClass:"dialog-item"},[e._v("会员畅享卡购买次数大于："+e._s(e.selectInfo.week_gt_count)+"次")]):e._e(),e.selectInfo.member_lt_days>-1?t("div",{staticClass:"dialog-item"},[e._v("会员有效期小于："+e._s(e.selectInfo.member_lt_days)+"天")]):e._e(),e.selectInfo.year_lt_count>-1?t("div",{staticClass:"dialog-item"},[e._v("会员年卡购买次数小于："+e._s(e.selectInfo.year_lt_count)+"次")]):e._e(),e.selectInfo.season_lt_count>-1?t("div",{staticClass:"dialog-item"},[e._v("会员季卡购买次数小于："+e._s(e.selectInfo.season_lt_count)+"次")]):e._e(),e.selectInfo.month_lt_count>-1?t("div",{staticClass:"dialog-item"},[e._v("会员月卡购买次数小于："+e._s(e.selectInfo.month_lt_count)+"次")]):e._e(),e.selectInfo.not_buy?t("div",{staticClass:"dialog-item"},[e._v("从未购买过会员")]):e._e()]),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-btn",attrs:{type:"primary"},on:{click:function(t){e.dialogVisible=!1}}},[e._v("关 闭")])],1)])],1)},n=[],l=a("ed08"),o=a("c8c2");function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return t};var e,t={},a=Object.prototype,r=a.hasOwnProperty,n=Object.defineProperty||function(e,t,a){e[t]=a.value},l="function"==typeof Symbol?Symbol:{},o=l.iterator||"@@iterator",c=l.asyncIterator||"@@asyncIterator",u=l.toStringTag||"@@toStringTag";function p(e,t,a){return Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{p({},"")}catch(e){p=function(e,t,a){return e[t]=a}}function m(e,t,a,r){var l=t&&t.prototype instanceof v?t:v,o=Object.create(l.prototype),i=new x(r||[]);return n(o,"_invoke",{value:O(e,a,i)}),o}function y(e,t,a){try{return{type:"normal",arg:e.call(t,a)}}catch(e){return{type:"throw",arg:e}}}t.wrap=m;var d="suspendedStart",f="suspendedYield",b="executing",h="completed",_={};function v(){}function g(){}function w(){}var k={};p(k,o,(function(){return this}));var E=Object.getPrototypeOf,A=E&&E(E(I([])));A&&A!==a&&r.call(A,o)&&(k=A);var L=w.prototype=v.prototype=Object.create(k);function C(e){["next","throw","return"].forEach((function(t){p(e,t,(function(e){return this._invoke(t,e)}))}))}function N(e,t){function a(n,l,o,s){var c=y(e[n],e,l);if("throw"!==c.type){var u=c.arg,p=u.value;return p&&"object"==i(p)&&r.call(p,"__await")?t.resolve(p.__await).then((function(e){a("next",e,o,s)}),(function(e){a("throw",e,o,s)})):t.resolve(p).then((function(e){u.value=e,o(u)}),(function(e){return a("throw",e,o,s)}))}s(c.arg)}var l;n(this,"_invoke",{value:function(e,r){function n(){return new t((function(t,n){a(e,r,t,n)}))}return l=l?l.then(n,n):n()}})}function O(t,a,r){var n=d;return function(l,o){if(n===b)throw Error("Generator is already running");if(n===h){if("throw"===l)throw o;return{value:e,done:!0}}for(r.method=l,r.arg=o;;){var i=r.delegate;if(i){var s=S(i,r);if(s){if(s===_)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(n===d)throw n=h,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n=b;var c=y(t,a,r);if("normal"===c.type){if(n=r.done?h:f,c.arg===_)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(n=h,r.method="throw",r.arg=c.arg)}}}function S(t,a){var r=a.method,n=t.iterator[r];if(n===e)return a.delegate=null,"throw"===r&&t.iterator.return&&(a.method="return",a.arg=e,S(t,a),"throw"===a.method)||"return"!==r&&(a.method="throw",a.arg=new TypeError("The iterator does not provide a '"+r+"' method")),_;var l=y(n,t.iterator,a.arg);if("throw"===l.type)return a.method="throw",a.arg=l.arg,a.delegate=null,_;var o=l.arg;return o?o.done?(a[t.resultName]=o.value,a.next=t.nextLoc,"return"!==a.method&&(a.method="next",a.arg=e),a.delegate=null,_):o:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,_)}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function D(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function x(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function I(t){if(t||""===t){var a=t[o];if(a)return a.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,l=function a(){for(;++n<t.length;)if(r.call(t,n))return a.value=t[n],a.done=!1,a;return a.value=e,a.done=!0,a};return l.next=l}}throw new TypeError(i(t)+" is not iterable")}return g.prototype=w,n(L,"constructor",{value:w,configurable:!0}),n(w,"constructor",{value:g,configurable:!0}),g.displayName=p(w,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===g||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,p(e,u,"GeneratorFunction")),e.prototype=Object.create(L),e},t.awrap=function(e){return{__await:e}},C(N.prototype),p(N.prototype,c,(function(){return this})),t.AsyncIterator=N,t.async=function(e,a,r,n,l){void 0===l&&(l=Promise);var o=new N(m(e,a,r,n),l);return t.isGeneratorFunction(a)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},C(L),p(L,u,"Generator"),p(L,o,(function(){return this})),p(L,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),a=[];for(var r in t)a.push(r);return a.reverse(),function e(){for(;a.length;){var r=a.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=I,x.prototype={constructor:x,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(D),!t)for(var a in this)"t"===a.charAt(0)&&r.call(this,a)&&!isNaN(+a.slice(1))&&(this[a]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var a=this;function n(r,n){return i.type="throw",i.arg=t,a.next=r,n&&(a.method="next",a.arg=e),!!n}for(var l=this.tryEntries.length-1;l>=0;--l){var o=this.tryEntries[l],i=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var s=r.call(o,"catchLoc"),c=r.call(o,"finallyLoc");if(s&&c){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(e,t){for(var a=this.tryEntries.length-1;a>=0;--a){var n=this.tryEntries[a];if(n.tryLoc<=this.prev&&r.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var l=n;break}}l&&("break"===e||"continue"===e)&&l.tryLoc<=t&&t<=l.finallyLoc&&(l=null);var o=l?l.completion:{};return o.type=e,o.arg=t,l?(this.method="next",this.next=l.finallyLoc,_):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),_},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.finallyLoc===e)return this.complete(a.completion,a.afterLoc),D(a),_}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.tryLoc===e){var r=a.completion;if("throw"===r.type){var n=r.arg;D(a)}return n}}throw Error("illegal catch attempt")},delegateYield:function(t,a,r){return this.delegate={iterator:I(t),resultName:a,nextLoc:r},"next"===this.method&&(this.arg=e),_}},t}function c(e,t,a,r,n,l,o){try{var i=e[l](o),s=i.value}catch(e){return void a(e)}i.done?t(s):Promise.resolve(s).then(r,n)}function u(e){return function(){var t=this,a=arguments;return new Promise((function(r,n){var l=e.apply(t,a);function o(e){c(l,r,n,o,i,"next",e)}function i(e){c(l,r,n,o,i,"throw",e)}o(void 0)}))}}var p={name:"SuperMemberLabel",props:{},data:function(){return{isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[],searchFormSetting:{name:{type:"input",label:"标签名称",value:"",placeholder:"请输入标签名称"},type:{type:"select",value:[],label:"标签类型",dataList:[{value:"auto",label:"自动"},{value:"manual",label:"手动"}],clearable:!0}},dialogVisible:!1,selectInfo:{labels:[]}}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getMemberLabel()},searchHandle:Object(l["d"])((function(){this.currentPage=1,this.getMemberLabel()}),300),refreshHandle:function(){this.currentPage=1,this.tableData=[],this.$refs.searchRef&&this.$refs.searchRef.resetForm(),this.initLoad()},resetHandler:function(){this.currentPage=1,this.tableData=[],this.initLoad()},getMemberLabel:function(){var e=this;return u(s().mark((function t(){var a,r;return s().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,a=Object(o["getRequestParams"])(e.searchFormSetting,e.currentPage,e.pageSize),t.next=4,e.$apis.apiBackgroundMemberMemberLabelListPost(a);case 4:r=t.sent,e.isLoading=!1,0===r.code?(e.tableData=r.data.results,e.totalCount=r.data.count):e.$message.error(r.msg);case 7:case"end":return t.stop()}}),t)})))()},handleSizeChange:function(e){this.pageSize=e,this.getMemberLabel()},handleCurrentChange:function(e){this.currentPage=e,this.getMemberLabel()},delMemberLabel:function(e){var t=this;return u(s().mark((function a(){return s().wrap((function(a){while(1)switch(a.prev=a.next){case 0:t.$confirm("确定删除会员标签？","提示",{confirmButtonText:t.$t("dialog.confirm_btn"),cancelButtonText:t.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var a=u(s().mark((function a(r,n,l){var o;return s().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if("confirm"!==r){a.next=9;break}return a.next=3,t.$apis.apiBackgroundMemberMemberLabelDeletePost({ids:[e]});case 3:o=a.sent,0===o.code?(t.$message.success("删除成功"),t.getMemberLabel()):t.$message.error(o.msg),l(),n.confirmButtonLoading=!1,a.next=10;break;case 9:n.confirmButtonLoading||l();case 10:case"end":return a.stop()}}),a)})));function r(e,t,r){return a.apply(this,arguments)}return r}()}).then((function(e){})).catch((function(e){}));case 1:case"end":return a.stop()}}),a)})))()},openDialog:function(e){this.dialogVisible=!0,this.selectInfo=e},gotoAddOrEdit:function(e,t){var a={};"edit"===e&&(a={data:encodeURIComponent(JSON.stringify(t))}),this.$router.push({name:"SuperAddOrEditMemberLabel",params:{type:e},query:a})}}},m=p,y=(a("0873"),a("2877")),d=Object(y["a"])(m,r,n,!1,null,"49a0d96d",null);t["default"]=d.exports},c8c2:function(e,t,a){"use strict";a.r(t),a.d(t,"getRequestParams",(function(){return u})),a.d(t,"RECENTSEVEN",(function(){return p})),a.d(t,"DIC_OBTAIN_TYPE",(function(){return m})),a.d(t,"DIC_SEND_TYPE",(function(){return y})),a.d(t,"DIC_MEMBER_STATUS",(function(){return d})),a.d(t,"DIC_TRIGGER_TYPE",(function(){return f})),a.d(t,"DIC_MENBER_STATUS",(function(){return b})),a.d(t,"DIC_PERMISSION_TYPE",(function(){return h})),a.d(t,"DIC_MEMBER_CYCLE",(function(){return _})),a.d(t,"SEARCH_FORM_RECORD_DATA",(function(){return v})),a.d(t,"SEARCH_FORM_EXCLUSIVE_DATA",(function(){return g})),a.d(t,"TABLE_HEAD_RECORD_DATA",(function(){return w})),a.d(t,"TABLE_HEAD_EXCLUSIVE_DATA",(function(){return k})),a.d(t,"SEARCH_FORM_SMS_MANAGER_DATA",(function(){return E})),a.d(t,"TABLE_HEAD_SEND_DATA",(function(){return A})),a.d(t,"TABLE_HEAD_CHARGE_INDEX_DATA",(function(){return L})),a.d(t,"TABLE_HEAD_CHARGE_INDEX_CONTENT",(function(){return C})),a.d(t,"SEARCH_FORM_CHARGE_DETAIL_DATA",(function(){return N})),a.d(t,"TABLE_HEAD_CHARGE_DETAIL_DATA",(function(){return O})),a.d(t,"SEARCH_FORM_PERMISSION_MANAGER_DATA",(function(){return S})),a.d(t,"TABLE_HEAD_PERMISSION_MANAGER_DATA",(function(){return T})),a.d(t,"SEARCH_FORM_KEY_MANAGER_DATA",(function(){return D})),a.d(t,"TABLE_HEAD_KEY_MANAGER_DATA",(function(){return x})),a.d(t,"SEARCH_FORM_PROMOTIONAL_PICTURE_DATA",(function(){return I})),a.d(t,"TABLE_HEAD_PROMOTIONAL_PICTURE_DATA",(function(){return M}));var r=a("5a0c");function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function l(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,r)}return a}function o(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?l(Object(a),!0).forEach((function(t){i(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):l(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function i(e,t,a){return(t=s(t))in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function s(e){var t=c(e,"string");return"symbol"==n(t)?t:t+""}function c(e,t){if("object"!=n(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var r=a.call(e,t||"default");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var u=function(e,t,a){var r,n={};Object.keys(e).forEach((function(t){("select_time"!==t&&""!==e[t].value&&e[t].value&&0!==e[t].value.length||"boolean"===typeof e[t].value)&&(n[t]=e[t].value)}));var l=o({page:t,page_size:a},n);return 2===(null===(r=e.select_time)||void 0===r||null===(r=r.value)||void 0===r?void 0:r.length)&&(l.start_date=e.select_time.value[0],l.end_date=e.select_time.value[1]),l},p=[r().subtract(7,"day").format("YYYY-MM-DD"),r().format("YYYY-MM-DD")],m=[{name:"线上购买",value:"buy",label:"buy"},{name:"手动发放",value:"manual_release",label:"manual_release"}],y=[{name:"消费通知",value:"consume",label:"consume"},{name:"点餐提醒",value:"order",label:"order"},{name:"充值提醒",value:"recharge",label:"recharge"},{name:"余额提醒",value:"balance",label:"balance"},{name:"提现通知",value:"withdraw",label:"withdraw"},{name:"通行通知",value:"is_pass",label:"is_pass"}],d=[{name:"使用中",value:"using",label:"using"},{name:"已过期",value:"expired",label:"expired"},{name:"非会员",value:"non",label:"non"}],f=[{name:"全部标签",value:"all",label:"all"},{name:"任意标签",value:"any",label:"any"}],b=[{name:"开启",value:!0,label:!0},{name:"关闭",value:!1,label:!1}],h=[{name:"关联链接",value:"association"},{name:"固定模块",value:"fixed"}],_=[{name:"月卡",value:"month"},{name:"季卡",value:"season"},{name:"年卡",value:"year"},{name:"永久会员",value:"permanent"},{name:"畅享会员",value:"week"}],v={select_time:{type:"daterange",label:"购买时间",clearable:!0,format:"yyyy-MM-dd",value:p},member_permission_name:{type:"input",label:"权益",value:"",placeholder:"请输入权益"},nickname:{type:"input",label:"用户姓名",value:"",placeholder:"请输入用户姓名"},phone:{type:"input",label:"手机号",value:"",placeholder:"请输入手机号"},receive_type:{type:"select",value:"",label:"获取方式",dataList:m,multiple:!1,collapseTags:!0,listNameKey:"name",listValueKey:"value",clearable:!0}},g={member_permissions:{type:"select",value:[],label:"权益",dataList:[],multiple:!1,collapseTags:!0,listNameKey:"name",listValueKey:"id",clearable:!0}},w=[{label:"订单号",key:"trade_no",width:"200",fixed:"left"},{label:"购买时间",key:"create_time",width:"200"},{label:"权益",key:"member_permission_name",width:"140"},{label:"金额",key:"origin_fee",width:"140",type:"slot",slotName:"price"},{label:"有效期",key:"days",width:"120",type:"slot",slotName:"days"},{label:"用户姓名",key:"nickname",width:"150"},{label:"手机号",key:"phone",width:"150"},{label:"操作员",key:"operator_name"},{label:"获取方式",key:"receive_type_alias"}],k=[{label:"序号",key:"index",width:"100",fixed:"left"},{label:"权益",key:"member_permissions_name",width:"200",type:"slot",slotName:"permission"},{label:"有效期",key:"days",width:"200",type:"slot",slotName:"days"},{label:"价格",key:"origin_fee",width:"200",type:"slot",slotName:"price"},{label:"说明",key:"remark",width:"200"},{label:"操作员",key:"operator_name"},{label:"修改时间",key:"update_time",width:"200"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"200"}],E={select_time:{type:"daterange",label:"推送时间",clearable:!0,format:"yyyy-MM-dd",value:p},name:{type:"input",label:"用户名",value:"",placeholder:"请输用户名"},phone:{type:"input",label:"手机号",value:"",placeholder:"请输入手机号"},organization_id:{type:"select",multiple:!0,filterable:!0,collapseTags:!0,label:"消费点",value:[],placeholder:"请选择消费点",listNameKey:"name",listValueKey:"id",dataList:[]},company_id:{type:"CompanySelect",value:[],label:"所属项目点",dataList:[],multiple:!0,checkStrictly:!0,clearable:!0,labelWidth:"100px",companyOpts:{label:"name",value:"company"},role:"super"},receive_type:{type:"select",value:[],label:"推送类型",dataList:y,multiple:!1,collapseTags:!0,listNameKey:"name",clearable:!0}},A=[{label:"推送时间",key:"create_time",width:"200"},{label:"用户名",key:"user_name",width:"150"},{label:"手机号",key:"phone",width:"150"},{label:"所属项目点",key:"company_name",width:"150"},{label:"消费点",key:"organization_name",width:"150"},{label:"推送类型",key:"push_type_alias",width:"150"},{label:"推送内容",key:"remark"},{label:"短信服务商",key:"sms_type_alias"}],L=[{label:"会员类型",key:"member_cycle_alias",width:"140"},{label:"说明",key:"remark",width:"250",type:"slot",slotName:"remark",align:"center"},{label:"基础价格（元）",key:"origin_fee",type:"slot",slotName:"price"},{label:"操作时间",key:"update_time"},{label:"操作人",key:"operator_name"},{label:"操作",key:"contact_name",type:"slot",slotName:"operation"}],C=[{member_cycle_alias:"年卡",origin_fee:"155622",remark:"说明说明说明说明"},{member_cycle_alias:"季卡",origin_fee:"26",remark:"11111"},{member_cycle_alias:"月卡",origin_fee:"32",remark:"2222"},{member_cycle_alias:"畅享会员",price:"44",remark:"333"},{member_cycle_alias:"永久会员",origin_fee:"522",remark:"66666666"}],N={name:{type:"input",label:"名称",value:"",placeholder:"请输入名称"},member_labels:{type:"select",value:[],label:"会员标签",dataList:[],multiple:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",clearable:!0},trigger_type:{type:"select",value:[],label:"标签触发类型",dataList:f,collapseTags:!0,listNameKey:"name",listValueKey:"value",clearable:!0,labelWidth:"200"},is_enable:{type:"select",value:[],label:"状态",dataList:b,listNameKey:"name",listValueKey:"value",clearable:!0}},O=[{label:"名称",key:"name",width:"140"},{label:"会员标签",key:"member_labels",width:"250",type:"slot",slotName:"label"},{label:"标签触发类型",key:"trigger_type",type:"slot",slotName:"trigger"},{label:"优惠折扣",key:"discount"},{label:"优惠价",key:"origin_fee",type:"money"},{label:"限购次数",key:"buy_count",type:"slot",slotName:"buyCount"},{label:"说明",key:"remark",showTooltip:!0},{label:"操作时间",key:"update_time"},{label:"操作人",key:"operator_name"},{label:"状态",key:"status",type:"slot",slotName:"status"},{label:"操作",key:"contact_name",type:"slot",slotName:"operation"}],S={name:{type:"input",label:"权限名称",value:"",placeholder:"请输入权限名称"},type:{type:"select",value:[],label:"类型",dataList:h,collapseTags:!0,listNameKey:"name",listValueKey:"value",clearable:!0},associate_url:{type:"input",label:"关联链接",value:"",placeholder:"请输入关联链接"}},T=[{label:"权限名称",key:"name",width:"140"},{label:"图标",key:"icon_url",width:"250",type:"slot",slotName:"image"},{label:"类型",key:"type_alias"},{label:"键值",key:"permission_key"},{label:"关联链接",key:"associate_url",width:"250",type:"slot",slotName:"associateUrl"},{label:"权限说明",key:"remark",showTooltip:!0,width:"200"},{label:"操作时间",key:"update_time"},{label:"操作人",key:"operator_name"},{label:"是否需要会员",key:"status",type:"slot",slotName:"status"},{label:"操作",key:"contact_name",type:"slot",slotName:"operation"}],D={name:{type:"input",label:"名称",value:"",placeholder:"请输入名称"}},x=[{label:"名称",key:"name",width:"140"},{label:"键值",key:"permission_key"},{label:"说明",key:"remark"},{label:"操作时间",key:"update_time"},{label:"操作人",key:"operator_name"},{label:"操作",key:"operation",type:"slot",slotName:"operation"}],I={name:{type:"input",label:"权限名称",value:"",placeholder:"请输入权限名称"},type:{type:"select",value:[],label:"类型",dataList:h,collapseTags:!0,listNameKey:"name",listValueKey:"value",clearable:!0},associate_url:{type:"input",label:"关联链接",value:"",placeholder:"请输入关联链接"}},M=[{label:"名称",key:"name",width:"140"},{label:"图片",key:"url",width:"250",type:"slot",slotName:"image"},{label:"优先级",key:"priority"},{label:"状态",key:"is_release",type:"slot",slotName:"status"},{label:"显示界面",key:"ui_type_alias",type:"slot",slotName:"UIType"},{label:"操作时间",key:"update_time"},{label:"操作人",key:"operator_name"},{label:"操作",key:"contact_name",type:"slot",slotName:"operation"}]},fbe5:function(e,t,a){}}]);