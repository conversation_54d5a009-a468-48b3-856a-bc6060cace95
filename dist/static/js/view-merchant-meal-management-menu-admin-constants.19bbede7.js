(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-menu-admin-constants"],{1062:function(e,l,a){"use strict";a.r(l),a.d(l,"recentSevenDay",(function(){return n})),a.d(l,"MENUCATERINGFOOD",(function(){return u})),a.d(l,"MENUCATERINGSETMEAL",(function(){return i}));var t=a("5a0c"),n=[t().subtract(7,"day").format("YYYY-MM-DD"),t().format("YYYY-MM-DD")],u={name:{type:"input",label:"菜品名称",value:"",placeholder:""},count_type:{type:"select",label:"菜品价格类型",value:"",placeholder:"",labelWidth:"125px",dataList:[{label:"菜品价格",value:1},{label:"称重价格",value:2},{label:"菜品价格+称重价格",value:3}]},label_filter:{type:"select",label:"",value:"",placeholder:"",dataList:[{label:"请选择",value:"select",disabled:!0},{label:"不包含",value:"Exclude"},{label:"等于",value:"Equal"}]},label_list:{type:"treeselect",label:"",value:[],placeholder:"选择标签",multiple:!0,limit:1,level:1,valueConsistsOf:"LEAF_PRIORITY",normalizer:function(e){return{id:e.id,label:e.name,children:e.label_list}},dataList:[]}},i={name:{type:"input",label:"套餐名称",value:"",placeholder:""}}}}]);