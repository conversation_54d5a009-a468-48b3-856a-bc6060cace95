(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-store-admin-admin-components-GoodsInfo-AddGoodsDialog"],{"7b4f":function(e,t,r){},a93c:function(e,t,r){"use strict";r("7b4f")},cb78:function(e,t,r){"use strict";r.r(t);var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"goods-dialog"},[t("el-dialog",{attrs:{title:e.dialogTitle,visible:e.visible,width:"1250px",top:"10vh","custom-class":"ps-dialog","show-close":"","close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t}}},[t("div",{staticClass:"ps-goods-dialog"},[t("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"formData",attrs:{model:e.formData,inline:"","label-width":"120px",rules:e.rules}},[t("el-form-item",{attrs:{label:"商品名称",prop:"name"}},[t("el-input",{staticClass:"ps-input",attrs:{maxlength:"30"},model:{value:e.formData.name,callback:function(t){e.$set(e.formData,"name",t)},expression:"formData.name"}})],1),t("el-form-item",{attrs:{label:"分类",prop:"goods_category_id"}},[t("el-select",{staticClass:"ps-select",staticStyle:{width:"180px"},attrs:{"popper-class":"ps-popper-select",clearable:"",filterable:"",placeholder:"请选择分类"},model:{value:e.formData.goods_category_id,callback:function(t){e.$set(e.formData,"goods_category_id",t)},expression:"formData.goods_category_id"}},e._l(e.goodsCategoryList,(function(e){return t("el-option",{key:e.name,attrs:{label:e.name,value:e.id}})})),1)],1),t("el-form-item",{attrs:{label:"供应商",prop:"supplier_id"}},[t("el-select",{staticClass:"ps-select",staticStyle:{width:"180px"},attrs:{"popper-class":"ps-popper-select",clearable:"",filterable:"",placeholder:"请选择分类"},model:{value:e.formData.supplier_id,callback:function(t){e.$set(e.formData,"supplier_id",t)},expression:"formData.supplier_id"}},e._l(e.supplierList,(function(e){return t("el-option",{key:e.name,attrs:{label:e.name,value:e.id}})})),1)],1),t("el-form-item",{attrs:{label:"一品多码",prop:"is_multi_barcode"}},[t("el-radio-group",{staticClass:"ps-radio-btn",attrs:{size:"medium",disabled:e.isRadioDisabledRule()},on:{change:e.changeMultiBarcode},model:{value:e.formData.is_multi_barcode,callback:function(t){e.$set(e.formData,"is_multi_barcode",t)},expression:"formData.is_multi_barcode"}},[t("el-radio-button",{attrs:{label:!0}},[e._v("是")]),t("el-radio-button",{attrs:{label:!1}},[e._v("否")])],1)],1),t("el-form-item",{attrs:{label:"上下架",prop:"sale_status"}},[t("el-radio-group",{staticClass:"ps-radio-btn",attrs:{size:"medium"},model:{value:e.formData.sale_status,callback:function(t){e.$set(e.formData,"sale_status",t)},expression:"formData.sale_status"}},[t("el-radio-button",{attrs:{label:1}},[e._v("上架")]),t("el-radio-button",{attrs:{label:0}},[e._v("下架")])],1)],1),e.formData.is_multi_spec?e._e():t("el-form-item",{key:1,staticClass:"form-item-block",attrs:{label:"条码",prop:"barcode",width:"120px"}},[t("div",{staticClass:"extended-barcode ps-flex flex-align-c"},[t("el-input",{staticClass:"ps-input w-300",attrs:{placeholder:""},model:{value:e.formData.barcode,callback:function(t){e.$set(e.formData,"barcode",t)},expression:"formData.barcode"}},[t("template",{slot:"prepend"},[e._v("主条码")]),t("el-tooltip",{staticClass:"item",attrs:{slot:"append",effect:"dark",content:"主条码用于各种操作界面及报表展示",placement:"bottom"},slot:"append"},[t("el-button",{attrs:{icon:"el-icon-warning-outline",type:"primary"}})],1)],2),t("el-button",{staticClass:"ps-green-btn m-l-10",attrs:{slot:"append",type:"primary"},on:{click:function(t){return e.clickGenerateUniqueID("barcode")}},slot:"append"},[e._v(" 生成 ")])],1)]),e.formData.is_multi_barcode?t("div",e._l(e.formData.extendedBarcodeList,(function(r,o){return t("el-form-item",{key:o,staticClass:"form-item-block",attrs:{label:" ",width:"120px",prop:"extendedBarcodeList."+o+".barcode",rules:e.extendedBarcodeRules}},[t("div",{staticClass:"extended-barcode ps-flex flex-align-c p-b-10"},[t("el-input",{staticClass:"ps-input w-300",attrs:{placeholder:""},model:{value:r.barcode,callback:function(t){e.$set(r,"barcode",t)},expression:"extendedBarcodeItem.barcode"}},[t("template",{slot:"prepend"},[e._v("扩展条码")])],2),t("div",[t("el-button",{staticClass:"ps-green-btn m-l-10",attrs:{slot:"append",type:"primary"},on:{click:function(t){return e.clickGenerateUniqueID("extendedBarcode",o)}},slot:"append"},[e._v(" 生成 ")])],1),t("i",{staticClass:"el-icon-circle-plus-outline p-l-10",staticStyle:{"font-size":"20px"},on:{click:e.clickAddExtendedBarcode}}),e.formData.extendedBarcodeList.length>1?t("i",{staticClass:"el-icon-remove-outline p-l-10",staticStyle:{"font-size":"20px"},on:{click:function(t){return e.clickDelectExtendedBarcode(o)}}}):e._e()],1)])})),1):e._e(),t("el-form-item",{attrs:{label:"多规格",prop:"is_multi_spec"}},[t("el-radio-group",{staticClass:"ps-radio-btn",attrs:{size:"medium",disabled:e.isRadioDisabledRule()},on:{change:e.changeMultiSpec},model:{value:e.formData.is_multi_spec,callback:function(t){e.$set(e.formData,"is_multi_spec",t)},expression:"formData.is_multi_spec"}},[t("el-radio-button",{attrs:{label:!0}},[e._v("是")]),t("el-radio-button",{attrs:{label:!1}},[e._v("否")])],1)],1),t("el-form-item",{staticClass:"form-item-block",attrs:{label:"主规格"}},[t("el-table",{key:e.formData.is_multi_spec?"multi":"single",staticClass:"ps-table",attrs:{data:e.formData.goods_list,border:"","header-row-class-name":"ps-table-header-row"}},[t("el-table-column",{attrs:{prop:"image",label:"图片",align:"center",width:"120px"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-upload",{directives:[{name:"loading",rawName:"v-loading",value:r.row.goodsUploadDisabled,expression:"scope.row.goodsUploadDisabled"}],ref:"fileUpload".concat(r.$index),attrs:{action:"/api/background/file/upload",data:e.uploadParams,"file-list":r.row.fileLists,"on-success":function(t,o,a){return e.uploadSuccess(t,o,a,r.row,r.$index)},"before-upload":function(t){return e.beforeFoodImgUpload(t,r.row,r.$index)},limit:1,multiple:!1,"show-file-list":!1,headers:e.headersOpts}},[r.row.goods_image?t("img",{staticClass:"avatar",attrs:{src:r.row.goods_image},on:{click:function(t){return e.clearFileHandle(r.$index,r.row)}}}):t("el-button",{attrs:{type:"text"}},[e._v("上传")])],1)]}}])}),t("el-table-column",{attrs:{prop:"spec",label:"*规格",align:"center",width:"120px"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-input",{staticClass:"ps-input",attrs:{size:"mini",maxlength:"10",onkeyup:"value=value.replace(/^\\D*(\\d*(?:\\.\\d{0,2})?).*$/g, '$1')"},model:{value:r.row.spec,callback:function(t){e.$set(r.row,"spec",t)},expression:"scope.row.spec"}})]}}])}),t("el-table-column",{attrs:{prop:"spec",label:"*单位",align:"center",width:"120px"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-select",{staticClass:"ps-select",attrs:{size:"mini",clearable:"",filterable:"","popper-class":"ps-popper-select"},model:{value:r.row.goods_unit,callback:function(t){e.$set(r.row,"goods_unit",t)},expression:"scope.row.goods_unit"}},e._l(e.unitList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)]}}])}),t("el-table-column",{attrs:{prop:"cost_price",label:"成本价",align:"center",width:"120px"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-input",{staticClass:"ps-input",attrs:{size:"mini"},model:{value:r.row.cost_price,callback:function(t){e.$set(r.row,"cost_price",t)},expression:"scope.row.cost_price"}})]}}])}),t("el-table-column",{attrs:{prop:"sales_price",label:"*零售价",align:"center",width:"120px"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-input",{staticClass:"ps-input",attrs:{size:"mini",maxlength:"4"},model:{value:r.row.sales_price,callback:function(t){e.$set(r.row,"sales_price",t)},expression:"scope.row.sales_price"}})]}}])}),t("el-table-column",{attrs:{prop:"stock_num",label:"库存",align:"center",width:"120px"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-input",{staticClass:"ps-input",attrs:{size:"mini",maxlength:"4",disabled:r.row.modify,onkeyup:"value=value.replace(/[^\\d]/g,'')"},model:{value:r.row.stock_num,callback:function(t){e.$set(r.row,"stock_num",t)},expression:"scope.row.stock_num"}})]}}])}),e.formData.is_multi_spec?t("el-table-column",{attrs:{prop:"barcode",label:"*条码",align:"center",width:"210px"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("div",{staticClass:"ps-flex flex-align-c"},[t("el-input",{staticClass:"ps-input",attrs:{size:"mini"},model:{value:r.row.barcode,callback:function(t){e.$set(r.row,"barcode",t)},expression:"scope.row.barcode"}}),t("el-button",{staticClass:"ps-green-btn m-l-10",attrs:{slot:"append",size:"mini",type:"primary"},on:{click:function(t){return e.clickGenerateUniqueID("multiSpec",r.$index)}},slot:"append"},[e._v(" 生成 ")])],1)]}}],null,!1,1862051830)}):e._e(),e.formData.is_multi_spec?t("el-table-column",{attrs:{label:"操作",align:"center",fixed:"right",width:"120px"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:e.clickAddGoods}},[e._v(" 新增 ")]),"add"===e.type&&e.formData.goods_list.length>1||"modify"===e.type&&r.row.stock_num<=0&e.formData.goods_list.length>1?t("el-button",{staticClass:"ps-warn",attrs:{type:"text",size:"small"},on:{click:function(t){return e.clickDelectGoods(r.$index)}}},[e._v(" 删除 ")]):e._e()]}}],null,!1,2097658431)}):e._e()],1)],1)],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-cancel-btn",on:{click:e.canceDialogHandle}},[e._v("取 消")]),t("el-button",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],staticClass:"ps-btn",attrs:{type:"primary"},on:{click:e.clickDetermineDialog}},[e._v(" 确定 ")])],1)])],1)},a=[],i=r("da92"),s=r("ed08");function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,o)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach((function(t){d(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function d(e,t,r){return(t=u(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function u(e){var t=p(e,"string");return"symbol"==n(t)?t:t+""}function p(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,t||"default");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function f(e,t){return _(e)||h(e,t)||g(e,t)||m()}function m(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function g(e,t){if(e){if("string"==typeof e)return b(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?b(e,t):void 0}}function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,o=Array(t);r<t;r++)o[r]=e[r];return o}function h(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var o,a,i,s,n=[],l=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(o=i.call(r)).done)&&(n.push(o.value),n.length!==t);l=!0);}catch(e){c=!0,a=e}finally{try{if(!l&&null!=r.return&&(s=r.return(),Object(s)!==s))return}finally{if(c)throw a}}return n}}function _(e){if(Array.isArray(e))return e}function y(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */y=function(){return t};var e,t={},r=Object.prototype,o=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},s=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function d(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(e){d=function(e,t,r){return e[t]=r}}function u(e,t,r,o){var i=t&&t.prototype instanceof _?t:_,s=Object.create(i.prototype),n=new G(o||[]);return a(s,"_invoke",{value:O(e,r,n)}),s}function p(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var f="suspendedStart",m="suspendedYield",g="executing",b="completed",h={};function _(){}function v(){}function w(){}var x={};d(x,s,(function(){return this}));var D=Object.getPrototypeOf,k=D&&D(D(E([])));k&&k!==r&&o.call(k,s)&&(x=k);var L=w.prototype=_.prototype=Object.create(x);function $(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function S(e,t){function r(a,i,s,l){var c=p(e[a],e,i);if("throw"!==c.type){var d=c.arg,u=d.value;return u&&"object"==n(u)&&o.call(u,"__await")?t.resolve(u.__await).then((function(e){r("next",e,s,l)}),(function(e){r("throw",e,s,l)})):t.resolve(u).then((function(e){d.value=e,s(d)}),(function(e){return r("throw",e,s,l)}))}l(c.arg)}var i;a(this,"_invoke",{value:function(e,o){function a(){return new t((function(t,a){r(e,o,t,a)}))}return i=i?i.then(a,a):a()}})}function O(t,r,o){var a=f;return function(i,s){if(a===g)throw Error("Generator is already running");if(a===b){if("throw"===i)throw s;return{value:e,done:!0}}for(o.method=i,o.arg=s;;){var n=o.delegate;if(n){var l=C(n,o);if(l){if(l===h)continue;return l}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(a===f)throw a=b,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);a=g;var c=p(t,r,o);if("normal"===c.type){if(a=o.done?b:m,c.arg===h)continue;return{value:c.arg,done:o.done}}"throw"===c.type&&(a=b,o.method="throw",o.arg=c.arg)}}}function C(t,r){var o=r.method,a=t.iterator[o];if(a===e)return r.delegate=null,"throw"===o&&t.iterator.return&&(r.method="return",r.arg=e,C(t,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+o+"' method")),h;var i=p(a,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,h;var s=i.arg;return s?s.done?(r[t.resultName]=s.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,h):s:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,h)}function j(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function B(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function G(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(j,this),this.reset(!0)}function E(t){if(t||""===t){var r=t[s];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,i=function r(){for(;++a<t.length;)if(o.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(n(t)+" is not iterable")}return v.prototype=w,a(L,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:v,configurable:!0}),v.displayName=d(w,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===v||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,d(e,c,"GeneratorFunction")),e.prototype=Object.create(L),e},t.awrap=function(e){return{__await:e}},$(S.prototype),d(S.prototype,l,(function(){return this})),t.AsyncIterator=S,t.async=function(e,r,o,a,i){void 0===i&&(i=Promise);var s=new S(u(e,r,o,a),i);return t.isGeneratorFunction(r)?s:s.next().then((function(e){return e.done?e.value:s.next()}))},$(L),d(L,c,"Generator"),d(L,s,(function(){return this})),d(L,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var o in t)r.push(o);return r.reverse(),function e(){for(;r.length;){var o=r.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},t.values=E,G.prototype={constructor:G,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(B),!t)for(var r in this)"t"===r.charAt(0)&&o.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(o,a){return n.type="throw",n.arg=t,r.next=o,a&&(r.method="next",r.arg=e),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var s=this.tryEntries[i],n=s.completion;if("root"===s.tryLoc)return a("end");if(s.tryLoc<=this.prev){var l=o.call(s,"catchLoc"),c=o.call(s,"finallyLoc");if(l&&c){if(this.prev<s.catchLoc)return a(s.catchLoc,!0);if(this.prev<s.finallyLoc)return a(s.finallyLoc)}else if(l){if(this.prev<s.catchLoc)return a(s.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<s.finallyLoc)return a(s.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&o.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var s=i?i.completion:{};return s.type=e,s.arg=t,i?(this.method="next",this.next=i.finallyLoc,h):this.complete(s)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),h},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),B(r),h}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var o=r.completion;if("throw"===o.type){var a=o.arg;B(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,o){return this.delegate={iterator:E(t),resultName:r,nextLoc:o},"next"===this.method&&(this.arg=e),h}},t}function v(e,t,r,o,a,i,s){try{var n=e[i](s),l=n.value}catch(e){return void r(e)}n.done?t(l):Promise.resolve(l).then(o,a)}function w(e){return function(){var t=this,r=arguments;return new Promise((function(o,a){var i=e.apply(t,r);function s(e){v(i,o,a,s,n,"next",e)}function n(e){v(i,o,a,s,n,"throw",e)}s(void 0)}))}}var x={props:{isshow:Boolean,type:{type:String,default:""},dialogModifyId:{type:Number},dialogTitle:{type:String,default:"新增商品"},goodsCategoryId:{type:String,default:""},confirm:Function},data:function(){var e=function(e,t,r){var o=/^[a-zA-Z0-9_-]+$/;if("barcode"===e.field&&!t)return r(new Error("商品条码必填"));t&&!o.test(t)?r(new Error("条码由：数字、字母、'_'、'-'组成")):r()};return{specRule:/^-?\d+(\.\d{1,2})?$/,isLoading:!1,rules:{name:[{required:!0,message:"请输入名称",trigger:["blur","change"]}],goods_category_id:[{required:!0,message:"请选择分类",trigger:["blur","change"]}],barcode:[{required:!0,validator:e,trigger:["blur","change"]}]},goodsCategoryList:[],supplierList:[],unitList:[],extendedBarcodeRules:{validator:e,trigger:["blur","change"]},formData:{name:"",goods_category_id:"",supplier_id:"",is_multi_barcode:null,barcode:"",extendedBarcodeList:[{barcode:""}],sale_status:null,is_multi_spec:null,goods_list:[{goodsUploadDisabled:!1,fileLists:[],goods_image:"",spec:"",goods_unit:null,cost_price:"",sales_price:"",stock_num:"",barcode:""}]},goodsDetails:{},headersOpts:{TOKEN:Object(s["B"])()},uploadParams:{prefix:"goods"}}},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},created:function(){var e=this;return w(y().mark((function t(){return y().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.getGoodsUnitList(),e.getFoodIngredientSupplierList(),e.getGoodsCategoryList(),"modify"===e.type?e.getGoodsDetails():e.initAddGoodsData();case 4:case"end":return t.stop()}}),t)})))()},mounted:function(){},methods:{initAddGoodsData:function(){this.formData={name:"",goods_category_id:"",supplier_id:"",is_multi_barcode:!1,barcode:"",extendedBarcodeList:[{barcode:""}],sale_status:1,is_multi_spec:!0,goods_list:[{goodsUploadDisabled:!1,fileLists:[],goods_image:"",spec:"",goods_unit:null,cost_price:"",sales_price:"",stock_num:"",barcode:""}]}},getGoodsUnitList:function(){var e=this;return w(y().mark((function t(){var r,o,a,i;return y().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(s["Z"])(e.$apis.apiBackgroundStoreGoodsUnitListPost({page:1,page_size:99999}));case 3:if(r=t.sent,o=f(r,2),a=o[0],i=o[1],e.isLoading=!1,!a){t.next=11;break}return e.$message.error(a.message),t.abrupt("return");case 11:0===i.code?e.unitList=i.data.results:e.$message.error(i.msg);case 12:case"end":return t.stop()}}),t)})))()},getFoodIngredientSupplierList:function(){var e=this;return w(y().mark((function t(){var r;return y().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,e.$apis.apiBackgroundFoodIngredientSupplierListPost({page:1,page_size:99999});case 3:r=t.sent,e.isLoading=!1,0===r.code?e.supplierList=r.data.results:e.$message.error(r.msg);case 6:case"end":return t.stop()}}),t)})))()},getGoodsCategoryList:function(){var e=this;return w(y().mark((function t(){var r,o,a,i;return y().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(s["Z"])(e.$apis.apiBackgroundStoreGoodsCategoryListPost({page:1,page_size:99999}));case 2:if(r=t.sent,o=f(r,2),a=o[0],i=o[1],!a){t.next=9;break}return e.$message.error(a.message),t.abrupt("return");case 9:0===i.code?(e.goodsCategoryList=i.data.results,e.goodsCategoryId&&(e.formData.goods_category_id=e.goodsCategoryId)):e.$message.error(i.msg);case 10:case"end":return t.stop()}}),t)})))()},getGoodsDetails:function(){var e=this;return w(y().mark((function t(){var r,o,a,n,l,c;return y().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(s["Z"])(e.$apis.apiBackgroundStoreGoodsDetailsPost({id:e.dialogModifyId}));case 3:if(r=t.sent,o=f(r,2),a=o[0],n=o[1],e.isLoading=!1,!a){t.next=11;break}return e.$message.error(a.message),t.abrupt("return");case 11:0===n.code?(e.goodsDetails=n.data,l=n.data,c={name:l.name,goods_category_id:String(l.goods_category_id),supplier_id:l.supplier_id,is_multi_barcode:l.is_multi_barcode,barcode:l.barcode,extendedBarcodeList:[{barcode:""}],sale_status:l.sale_status,is_multi_spec:l.is_multi_spec,goods_list:[{goodsUploadDisabled:!1,fileLists:[],goods_image:"",spec:"",goods_unit:null,cost_price:"",sales_price:"",stock_num:"",barcode:""}]},l.extended_barcode.length?c.extendedBarcodeList=l.extended_barcode.map((function(e){return{barcode:e,modify:!0}})):c.extendedBarcodeList=[{barcode:""}],l.goods_list.length?c.goods_list=l.goods_list.map((function(e){return e.cost_price=i["a"].divide(e.cost_price,100),e.sales_price=i["a"].divide(e.sales_price,100),e.modify=!0,e.goodsUploadDisabled=!1,e.goods_image&&(e.fileLists=[{uid:(new Date).getTime()+Math.floor(150*Math.random()),url:e.goods_image?e.goods_image:""}]),e})):c.goods_list=[{goodsUploadDisabled:!1,fileLists:[],goods_image:"",spec:"",goods_unit:null,cost_price:"",sales_price:"",stock_num:"",barcode:""}],e.formData=c):e.$message.error(n.msg);case 12:case"end":return t.stop()}}),t)})))()},canceDialogHandle:function(){this.visible=!1},clickDetermineDialog:function(){var e=this;this.$refs.formData.validate((function(t){if(t){var r=/^(?:[1-9]\d*|0)(?:\.\d{1,2})?$/,o=/^[a-zA-Z0-9_-]+$/,a={name:e.formData.name,goods_category_id:e.formData.goods_category_id,is_multi_barcode:e.formData.is_multi_barcode,extended_barcode:[],sale_status:e.formData.sale_status,is_multi_spec:e.formData.is_multi_spec,goods_list:[]};if(e.formData.supplier_id&&(a.supplier_id=e.formData.supplier_id),e.formData.is_multi_barcode&&e.formData.extendedBarcodeList.length){if(e.formData.extendedBarcodeList.some((function(t){return t.barcode===e.formData.barcode})))return e.$message.error("条码有相同,请修改");for(var s=new Set,n=0;n<e.formData.extendedBarcodeList.length;n++){if(s.has(e.formData.extendedBarcodeList[n].barcode))return e.$message.error("条码有相同,请修改");s.add(e.formData.extendedBarcodeList[n].barcode),e.formData.extendedBarcodeList[n].barcode&&a.extended_barcode.push(e.formData.extendedBarcodeList[n].barcode)}}if(e.formData.goods_list.length)for(var l=new Set,c=0;c<e.formData.goods_list.length;c++){var d=e.formData.goods_list[c];if(!d.spec)return e.$message.error("多规格下的第".concat(c+1,"条，请输入规格"));if(!d.goods_unit)return e.$message.error("多规格下的第".concat(c+1,"条，请选择单位"));if(d.cost_price&&!r.test(d.cost_price))return e.$message.error("多规格下的第".concat(c+1,"条，请正确输入成本价"));if(Number(d.sales_price)<0||!r.test(d.sales_price))return e.$message.error("多规格下的第".concat(c+1,"条，请正确输入零售价"));if(e.formData.is_multi_spec&&!d.barcode)return e.$message.error("多规格下的第".concat(c+1,"条，请输入条码"));if(e.formData.is_multi_spec&&!o.test(d.barcode))return e.$message.error("多规格下的第".concat(c+1,"条，条码由：数字、字母、'_'、'-'组成"));if(l.has(d.barcode))return e.$message.error("条码有相同,请修改");l.add(d.barcode);var u={goods_image:d.goods_image?d.goods_image:"",spec:d.spec,goods_unit:d.goods_unit,cost_price:i["a"].times(d.cost_price,100),sales_price:i["a"].times(d.sales_price,100),stock_num:d.stock_num?Number(d.stock_num):0,barcode:e.formData.is_multi_spec?d.barcode:e.formData.barcode};"modify"===e.type&&d.id&&(u.id=d.id),a.goods_list.push(u)}e.formData.is_multi_spec?a.goods_list.length&&(a.barcode=a.goods_list[0].barcode):a.barcode=e.formData.barcode,e.setGoodsAddOrModify(a)}}))},setGoodsAddOrModify:function(e){var t=this;return w(y().mark((function r(){var o,a,i,n,l,d,u,p;return y().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(t.isLoading=!0,o="",a=f(o,2),i=a[0],n=a[1],"modify"!==t.type){r.next=12;break}return r.next=6,Object(s["Z"])(t.$apis.apiBackgroundStoreGoodsModifyPost(c({id:t.dialogModifyId},e)));case 6:l=r.sent,d=f(l,2),i=d[0],n=d[1],r.next=19;break;case 12:return r.next=15,Object(s["Z"])(t.$apis.apiBackgroundStoreGoodsAddPost(e));case 15:u=r.sent,p=f(u,2),i=p[0],n=p[1];case 19:if(t.isLoading=!1,!i){r.next=23;break}return t.$message.error(i.message),r.abrupt("return");case 23:0===n.code?(t.$message.success(n.msg),t.visible=!1,t.$emit("confirm","search")):t.$message.error(n.msg);case 24:case"end":return r.stop()}}),r)})))()},clickGenerateUniqueID:function(e,t){var r=Object(s["q"])();switch(e){case"barcode":this.formData.barcode=r;break;case"extendedBarcode":this.formData.extendedBarcodeList[t].barcode=r;break;case"multiSpec":this.formData.goods_list[t].barcode=r;break;default:break}},changeMultiBarcode:function(e){e&&(this.formData.is_multi_spec=!1),this.initBarcodeMultiSpec()},changeMultiSpec:function(e){e&&(this.formData.is_multi_barcode=!1),this.initBarcodeMultiSpec()},initBarcodeMultiSpec:function(){var e=this;this.$nextTick((function(t){e.$refs.formData.clearValidate(["barcode"])}))},clickAddExtendedBarcode:function(){this.formData.extendedBarcodeList.push({barcode:""})},clickDelectExtendedBarcode:function(e){this.formData.extendedBarcodeList.splice(e,1)},clickAddGoods:function(){this.formData.goods_list.push({goodsUploadDisabled:!1,fileLists:[],goods_image:"",spec:"",goods_unit:null,cost_price:"",sales_price:"",stock_num:"",barcode:""})},clickDelectGoods:function(e){this.formData.goods_list.splice(e,1)},clearFileHandle:function(e,t){t.fileLists=[],t.goodsUploadDisabled=!0,this.$refs["fileUpload"+e].clearFiles()},beforeFoodImgUpload:function(e,t,r){t.goodsUploadDisabled=!0;var o=[".jpeg",".jpg",".png",".bmp"],a=e.size/1024/1024<1;return o.includes(Object(s["A"])(e.name))?a?void 0:(this.$message.error("上传图片大小不能超过 1MB!"),!1):(this.$message.error("上传图片只能是 JPG 格式或者PNG格式!"),!1)},uploadSuccess:function(e,t,r,o,a){e&&0===e.code?(this.formData.goods_list[a].fileLists=r,this.formData.goods_list[a].goods_image=e.data.public_url):this.$message.error(e.msg),o.goodsUploadDisabled=!1},isRadioDisabledRule:function(){var e=!1;return"modify"===this.type&&(this.goodsDetails.is_multi_barcode||this.goodsDetails.is_multi_spec)&&(e=!0),e}}},D=x,k=(r("a93c"),r("2877")),L=Object(k["a"])(D,o,a,!1,null,"ebba2542",null);t["default"]=L.exports}}]);