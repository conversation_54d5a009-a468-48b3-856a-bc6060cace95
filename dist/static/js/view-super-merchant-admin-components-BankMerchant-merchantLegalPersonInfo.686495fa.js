(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-merchant-admin-components-BankMerchant-merchantLegalPersonInfo","view-super-merchant-admin-components-BankMerchant-customInput","view-super-merchant-admin-components-BankMerchant-merchantBaseInfo","view-super-merchant-admin-constants-bankMerchantConstants"],{5845:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"container-wrapper"},[t("div",{staticClass:"table-wrapper"},[e._m(0),t("div",{staticClass:"m-l-40 m-r-40"},[t("el-form",{ref:"legalPersonInfo",attrs:{model:e.params,"label-width":"250px","label-position":"left",rules:e.legalInfoRules}},[t("div",{staticClass:"ps-flex flex-wrap"},[t("el-form-item",{staticClass:"item-style",attrs:{label:"法定代表人姓名：",prop:"contact_name"}},[t("custom-input",{attrs:{value:e.params.contact_name,maxLength:10,natureType:"contact_name",disabled:e.isDisabledEdit},on:{inputChange:e.inputChangeLegalInfo}})],1),t("el-form-item",{staticClass:"item-style",attrs:{label:"法定代表人证件类型：",prop:"certificate_type"}},[t("el-select",{staticClass:"ps-select w-180",attrs:{placeholder:"请选择","popper-class":"ps-popper-select",disabled:e.isDisabledEdit},on:{change:function(t){return e.changeSelect(t,"certificate_type")}},model:{value:e.params.certificate_type,callback:function(t){e.$set(e.params,"certificate_type",t)},expression:"params.certificate_type"}},e._l(e.dicCertificateType,(function(e){return t("el-option",{key:e.value,attrs:{label:e.name,value:e.value}})})),1)],1),t("el-form-item",{staticClass:"item-style",attrs:{label:"法定代表人证件编号：",prop:"certificate_no"}},[t("custom-input",{attrs:{value:e.params.certificate_no,maxLength:30,natureType:"certificate_no",disabled:e.isDisabledEdit},on:{inputChange:e.inputChangeLegalInfo}})],1)],1),t("div",{staticClass:"ps-flex flex-wrap"},[t("el-form-item",{staticClass:"item-style",attrs:{label:"法定代表人证件有效期开始时间：",prop:"certificate_beg_date"}},[t("custom-input",{attrs:{value:e.params.certificate_beg_date,maxLength:8,natureType:"certificate_beg_date",placeholder:e.placeholderTxt,disabled:e.isDisabledEdit,type:"text"},on:{inputChange:e.inputChangeLegalInfo}})],1),t("el-form-item",{staticClass:"item-style",attrs:{label:"法定代表人证件有效期结束时间：",prop:"fr_cert_end_date"}},[t("custom-input",{attrs:{value:e.params.fr_cert_end_date,maxLength:8,natureType:"fr_cert_end_date",placeholder:e.placeholderTxt,disabled:e.isDisabledEdit,type:"text"},on:{inputChange:e.inputChangeLegalInfo}})],1),t("div",{staticClass:"text-gray-12 m-t-18"},[e._v("若证件有效期为长期，结束时间请填写：20991231")])],1),t("div",{staticClass:"ps-flex flex-wrap"},[t("el-form-item",{staticClass:"item-style",attrs:{label:"法定代表人证件居住地址：",prop:"fr_residence"}},[t("custom-input",{attrs:{value:e.params.fr_residence,maxLength:50,natureType:"fr_residence",disabled:e.isDisabledEdit},on:{inputChange:e.inputChangeLegalInfo}})],1),t("el-form-item",{staticClass:"item-style",attrs:{label:"法定代表人是否为受益所有人：",prop:"fr_is_controller"}},[t("el-select",{staticClass:"ps-select w-180",attrs:{placeholder:"请选择","popper-class":"ps-popper-select",disabled:e.isDisabledEdit},on:{change:function(t){return e.changeSelect(t,"fr_is_controller")}},model:{value:e.params.fr_is_controller,callback:function(t){e.$set(e.params,"fr_is_controller",t)},expression:"params.fr_is_controller"}},e._l(e.dicIsNotType,(function(e){return t("el-option",{key:e.name,attrs:{label:e.name,value:e.value}})})),1)],1),t("el-form-item",{staticClass:"item-style",attrs:{label:"法定代表人是否为实际办理业务人员：",prop:"fr_is_agent"}},[t("el-select",{staticClass:"ps-select w-180",attrs:{placeholder:"请选择","popper-class":"ps-popper-select",disabled:e.isDisabledEdit},on:{change:function(t){return e.changeSelect(t,"fr_is_agent")}},model:{value:e.params.fr_is_agent,callback:function(t){e.$set(e.params,"fr_is_agent",t)},expression:"params.fr_is_agent"}},e._l(e.dicIsNotType,(function(e){return t("el-option",{key:e.name,attrs:{label:e.name,value:e.value}})})),1)],1)],1)])],1)])])},l=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[e._v("法定代表人信息")])])}],r=a("c3cc"),i=a("ddcc"),s=a("ed08"),u=a("e173"),c={name:"merchantLegalPersonInfo",props:{subParams:{type:Object,default:function(){return{}}}},data:function(){return{params:this.subParams,dicCertificateType:Object(s["f"])(i["DIC_CERTIFICATE_TYPE"]),dicIsNotType:Object(s["f"])(i["DIC_IS_NOT"]),isDisabledEdit:this.subParams.isDisabledEdit,placeholderTxt:"例如：20230101",legalInfoRules:{contact_name:[{required:!0,message:"请输入法定代表人姓名",trigger:"blur"}],certificate_type:[{required:!0,message:"请选择法定代表人证件类型",trigger:"change"}],certificate_no:[{required:!0,message:"请输入法定代表人证件编号",trigger:"blur"}],certificate_beg_date:[{required:!0,message:"请输入法定代表人证件有效期开始时间",trigger:"blur"}],fr_cert_end_date:[{required:!0,message:"请选择法定代表人证件有效期结束时间",trigger:"change"},{validator:u["d"],trigger:"blur"}],fr_residence:[{required:!0,message:"请输入法定代表人证件居住地址",trigger:"blur"}],fr_is_controller:[{required:!1,message:"请选择法定代表人是否为受益所有人",trigger:"change"}],fr_is_agent:[{required:!1,message:"请选择法定代表人是否为实际办理业务人员",trigger:"change"}]}}},components:{customInput:r["default"]},watch:{subParams:{handler:function(e){this.params=Object(s["f"])(e),this.setRulesRequire(e),this.isDisabledEdit=this.params.isDisabledEdit},deep:!0}},methods:{changeSelect:function(e,t){this.$set(this.params,t,e),this.$emit("changeSelect",e,t)},inputChangeLegalInfo:function(e,t){this.$set(this.params,t,e),this.$emit("inputChange",e,t)},checkParams:function(){var e=this;return new Promise((function(t){e.$refs.legalPersonInfo.validate((function(e){t(!!e)}))}))},setRulesRequire:function(e){var t=this,a=Object(s["f"])(this.legalInfoRules);a.fr_is_controller[0].required=!("2"!==e.sub_mch_type&&"3"!==e.sub_mch_type&&"4"!==e.sub_mch_type),a.fr_is_agent[0].required=!("2"!==e.sub_mch_type&&"3"!==e.sub_mch_type&&"4"!==e.sub_mch_type),this.$set(this,"legalInfoRules",a),this.$nextTick((function(){Reflect.has(t.$refs.legalPersonInfo,"clearValidate")&&t.$refs.legalPersonInfo.clearValidate()}))}}},o=c,_=(a("6cf6"),a("2877")),d=Object(_["a"])(o,n,l,!1,null,"294bb858",null);t["default"]=d.exports},"6cf6":function(e,t,a){"use strict";a("ab70")},ab70:function(e,t,a){},c3cc:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"ps-flex"},[t("el-input",{class:"textarea"!=e.inputType?"w-180":"w-350 h-100",attrs:{placeholder:e.inputPlaceHolder,autocomplete:"off",type:e.inputType,disabled:e.inputDisabled,clearable:"",rows:e.inputRows,maxlength:e.inputMaxLength,autosize:{minRows:e.inputRows,maxRows:e.inputRows}},on:{input:e.handlerInputChange},model:{value:e.inputContent,callback:function(t){e.inputContent=t},expression:"inputContent"}}),t("div",{staticClass:"m-l-10 m-t-10"},[e._v(e._s(e.currentLength)+"/"+e._s(e.inputMaxLength))])],1)},l=[],r={name:"customInput",props:{value:{type:String,default:""},type:{type:String,default:"text"},disabled:{type:Boolean,default:!1},maxLength:{type:Number,default:50},placeholder:{type:String,default:"请输入"},natureType:{type:String,default:""},rows:{type:Number,default:2}},data:function(){return{inputContent:this.value,inputType:this.type,inputDisabled:this.disabled,inputRows:this.rows,currentLength:this.value?this.value.length:0,inputMaxLength:this.maxLength,inputPlaceHolder:this.placeholder}},watch:{value:function(e){this.inputContent=e},disabled:function(e){this.inputDisabled=e}},methods:{handlerInputChange:function(e){var t=e?e.length:0;t>this.maxLength&&(this.$message.error("超出最大字符"+this.maxLength+"限制"),e=e.slice(0,this.maxLength),this.inputContent=e,t=e.length),this.$set(this,"currentLength",t),this.$emit("inputChange",e,this.natureType)}}},i=r,s=a("2877"),u=Object(s["a"])(i,n,l,!1,null,"7537d78b",null);t["default"]=u.exports},ddcc:function(e,t,a){"use strict";a.r(t),a.d(t,"TABLE_HEAD_DATA",(function(){return n})),a.d(t,"SEARCH_FORM_SET_DATA",(function(){return l})),a.d(t,"DIC_MERCHANT_STATUS",(function(){return r})),a.d(t,"DIC_MERCHANT_TYPE",(function(){return i})),a.d(t,"DIC_MERCHANT_ID_TYPE",(function(){return s})),a.d(t,"DIC_PERSON_MERCHANT_CATEGORY",(function(){return u})),a.d(t,"DIC_MERCHANT_CONTACT_ID",(function(){return c})),a.d(t,"DIC_CERTIFICATE_TYPE",(function(){return o})),a.d(t,"DIC_ACCOUNT_TYPE",(function(){return _})),a.d(t,"DIC_IS_NOT",(function(){return d})),a.d(t,"UPLOAD_DIALOG_DATA_LIST",(function(){return f})),a.d(t,"PRINT_BANK_TABBLE_SETTING",(function(){return m})),a.d(t,"DEFAULT_CHANNEL_TABLE_SETTING",(function(){return p}));var n=[{label:"二级商户编号",key:"sub_mch_id",width:"140",fixed:"left"},{label:"一级商户",key:"php_path",width:"120"},{label:"二级商户名称",key:"sub_mch_name",width:"120"},{label:"二级商户类型",key:"sub_mch_type_alias",width:"120"},{label:"二级商户证件类型",key:"company_cert_type_alias",width:"140"},{label:"二级商户证件有效期结束时间",key:"end_certificate_validity",width:"140"},{label:"法定代表人",key:"contact_name",width:"120"},{label:"法定代表人证件类型",key:"certificate_type_alias",width:"150"},{label:"法定代表人证件编号",key:"certificate_no",width:"150"},{label:"法定代表人证件有效结束时间",key:"fr_cert_end_date",width:"140"},{label:"银行账号",key:"account",width:"120"},{label:"银行账户户名",key:"account_name",width:"120"},{label:"开户银行名称",key:"bank_name",width:"120"},{label:"银行预留手机号",key:"mobile_phone",width:"120"},{label:"账户类型",key:"account_type_alias",width:"120"},{label:"二级商户状态",key:"is_passed_alias",width:"120"},{label:"历史二级商户号",key:"history_sub_mch_ids",type:"slot",slotName:"detail",width:"120"},{label:"签署组织",key:"organization_name",width:"120",type:"slot",slotName:"organizationName"},{label:"签署账号",key:"get_agreement_info",width:"120",type:"slot",slotName:"accountName"},{label:"是否签署协议",key:"is_sign",width:"120",type:"slot",slotName:"isSign"},{label:"操作人",key:"operator",width:"120"},{label:"操作时间",key:"update_time",width:"120"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"200"}],l={sub_mch_id:{type:"input",value:"",label:"二级商户编号",placeholder:"请输入二级商户编号"},sub_mch_name:{type:"input",value:"",label:"二级商户名称",placeholder:"请输入二级商户名称"},is_passed:{type:"select",label:"商户状态",value:[],placeholder:"请选择商户状态",listNameKey:"name",listValueKey:"value",dataList:[]},php_path:{type:"input",value:"",label:"一级商户",placeholder:"请输入一级商户"}},r=[{name:"全部",value:"",label:""},{name:"已验证,未审核(不可交易)",value:"0",label:"VERIFIED_NOT_REVIEWED_0"},{name:"已验证,审核通过",value:"1",label:"VERIFIED_REVIEWED"},{name:"已验证,未审核(暂可交易)",value:"2",label:"VERIFIED_NOT_REVIEWED_2"},{name:"未验证,未审核",value:"3",label:"NOT_VERIFIED_NOT_REVIEWED"},{name:"已解约",value:"4",label:"TERMINATED"},{name:"已关闭",value:"5",label:"CLOSED"},{name:"审核拒绝",value:"8",label:"AUDIT_REJECTED"},{name:"驳回",value:"9",label:"TURN_DOWN"}],i=[{name:"个人商户",value:"1",label:"INDIVIDUAL"},{name:"企业",value:"2",label:"ENTERPRISE"},{name:"个体工商户",value:"3",label:"INDIVIDUAL_BUSINESS"},{name:"政府、金融机构及事业单位",value:"4",label:"BUSINESS_UNIT"}],s=[{name:"个体工商户营业执照",value:"610049",label:"INDIVIDUAL_LICENSE"},{name:"企业营业执照",value:"610047",label:"ENTERPRISE_LICENSE"},{name:"组织机构代码",value:"610001",label:"ORGANIZATION_CODE"},{name:"统一社会信用代码",value:"611009",label:"CREDIT_CODE"},{name:"事业单位法人证书",value:"610170",label:"BUSINESS_UNIT_CERTIFICATE"},{name:"社会团体登记证书",value:"610023",label:"SOCIAL_GROUPS_CERTIFICATE"},{name:"民办非企业登记证书",value:"610025",label:"PRIVATE_CERTIFICATE"},{name:"农民专业合作社营业执照",value:"610079",label:"FARMER_LICENSE"},{name:"主管部门颁居民委员会批文",value:"610033",label:"COMMITTEE_APPROVAL"},{name:"政府主管部门批文",value:"610037",label:"GOVERNMENT_APPROVAL"},{name:"财政部门证明",value:"610039",label:"FINANCIAL_PROVE"},{name:"其他机构证件标识",value:"619999",label:"OTHER"}],u=[{name:"有固定经营场所的实体商户",value:"0",label:"FIXED"},{name:"无固定经营场所的实体商户",value:"1",label:"NOT_FIXED"},{name:"网络商户",value:"2",label:"NET"}],c=[{name:"商户信息核实联系人",value:"01",label:"VERIFY_CONTACT"},{name:"商户巡检联系人",value:"02",label:"INSPECTION_CONTACT"},{name:"客户投诉处理联系人",value:"03",label:"COMPLAINT_HANDLING_CONTACT"}],o=[{name:"身份证",value:"110001",label:"ID_CARD"},{name:"临时居民身份证",value:"110003",label:"TEMPORARY_ID_CARD"},{name:"中国人民解放军军人身份证件",value:"110007",label:"MILITARY_ID"},{name:"中国人民武装警察身份证件",value:"110009",label:"POLICE_ID"},{name:"港澳居民来往内地通行证",value:"110019",label:"HONG_KONG_AND_MACAU_PASS"},{name:"台湾居民来往大陆通行证",value:"110021",label:"TAIWAN_PASS"},{name:"中华人民共和国护照",value:"110023",label:"CHINESE_PASSPORT"},{name:"外国护照",value:"110025",label:"FOREIGN_PASSPORT"},{name:"其他证件",value:"119999",label:"OTHER"}],_=[{name:"借记卡",value:"401",label:"DEBIT_CARD"},{name:"企业户",value:"601",label:"ENTERPRISE_HOUSEHOLD"},{name:"二类户",value:"701",label:"CLASS_II_HOUSEHOLDS"},{name:"三类户",value:"702",label:"CLASS_III_HOUSEHOLDS"}],d=[{name:"是",value:!0},{name:"否",value:!1}],f=[{name:"法人身份证人像面照片",required:!0,fileName:"",fileKey:"id_card_face_url",fileUrl:""},{name:"法人身份证国徽面照片",required:!0,fileName:"",fileKey:"id_card_national_emblem_url",fileUrl:""},{name:"法人护照、通行证照片",required:!0,fileName:"",fileKey:"passport_url",fileUrl:""},{name:"个体工商户/企业营业执照照片",required:!0,fileName:"",fileKey:"license_url",fileUrl:""},{name:"辅助证明材料",required:!0,fileName:"",fileKey:"auxiliary_proof_url",fileUrl:""},{name:"政府机关/事业单位/社会组织登记证书照片",required:!0,fileName:"",fileKey:"certificate_url",fileUrl:""},{name:"法定代表人授权函",required:!1,fileName:"",fileKey:"authorization_letter_url",fileUrl:""},{name:"定位证明材料",required:!1,fileName:"",fileKey:"gps_prove_url",fileUrl:""},{name:"固定经营场所证明材料",required:!1,fileName:"",fileKey:"fixed_place_prove_url",fileUrl:""},{name:"合法合规用途证明材料：",required:!1,fileName:"",fileKey:"use_prove_url",fileUrl:""}],m=[{key:"ori_sub_mer_no",label:"*原二级商户号"},{key:"sub_merchant_short_name",label:"*二级商户名称"},{key:"sub_mch_type",label:"*二级商户类型"},{key:"sub_mch_name",label:"*二级商户经营名称"},{key:"service_phone",label:"*二级商户客服电话"},{key:"industry",label:"*二级商户所属行业"},{key:"business_range",label:"二级商户经营范围"},{key:"address",label:"*二级商户实际经营地址"},{key:"company_cert_type",label:"二级商户证件类型"},{key:"company_cert_no",label:"二级商户证件编号"},{key:"end_certificate_validity",label:"二级商户证件有效期"},{key:"sub_mer_class",label:"个人商户类别"},{key:"account",label:"*银行账号"},{key:"account_name",label:"*银行账户户名"},{key:"bank_name",label:"*开户银行名称"},{key:"mobile_phone",label:"*银行预留手机号"},{key:"account_type",label:"*账户类型"},{key:"apply_service",label:"*申请服务"},{key:"sub_mer_contact_name",label:"*联系人姓名"},{key:"mer_mobile_phone",label:"*联系人手机号码"},{key:"sub_mer_contact_cert",label:"*联系人证件号码"},{key:"sub_mer_contact_mail",label:"联系人邮箱"},{key:"sub_mer_contact_type",label:"*商户联系人业务标识"},{key:"contact_name",label:"*法定代表人姓名"},{key:"certificate_type",label:"*法定代表人证件类型"},{key:"certificate_no",label:"*法定代表人证件编号"},{key:"certificate_beg_date",label:"*法定代表人证件有效期开始时间"},{key:"fr_cert_end_date",label:"*法定代表人证件有效期结束时间"},{key:"fr_residence",label:"*法定代表人证件居住地址"},{key:"fr_is_controller",label:"法定代表人是否为受益所有人"},{key:"fr_is_agent",label:"法定代表人是否为实际办理业务人员"},{key:"controller_name",label:"受益所有人姓名"},{key:"controller_cert_type",label:"受益所有人证件类型"},{key:"controller_cert_no",label:"受益所有人证件号码"},{key:"controller_cert_beg_date",label:"受益所有人证件有效期开始时间"},{key:"controller_cert_end_date",label:"受益所有人证件有效期结束时间"},{key:"controller_residence",label:"受益所有人证件居住地址"},{key:"agent_name",label:"授权办理业务人员姓名"},{key:"agent_cert_type",label:"授权办理业务人员证件类型"},{key:"agent_cert_no",label:"授权办理业务人员证件号码"},{key:"agent_cert_beg_date",label:"授权办理业务人员证件有效期开始时间"},{key:"agent_cert_end_date",label:"授权办理业务人员证件有效期结束时间"},{key:"agent_residence",label:"授权办理业务人员证件居住地址"}],p=[{label:"所属项目",key:"name",align:"left"},{label:"所属地址",key:"district_alias",type:"slot",slotName:"districtAlias"},{label:"监管渠道",key:"supervision_channel_name"},{label:"操作",key:"operation",type:"slot",slotName:"operation",width:"200"}]},e173:function(e,t,a){"use strict";a.d(t,"e",(function(){return l})),a.d(t,"l",(function(){return r})),a.d(t,"b",(function(){return i})),a.d(t,"a",(function(){return s})),a.d(t,"d",(function(){return u})),a.d(t,"k",(function(){return c})),a.d(t,"c",(function(){return o})),a.d(t,"h",(function(){return _})),a.d(t,"f",(function(){return d})),a.d(t,"g",(function(){return f})),a.d(t,"j",(function(){return m})),a.d(t,"i",(function(){return p}));var n=a("e925"),l=function(e,t,a){if(!t)return a();Object(n["c"])(t)?a():a(new Error("邮箱格式错误！"))},r=function(e,t,a){if(!t)return a();Object(n["g"])(t)?a():a(new Error("电话格式错误！"))},i=function(e,t,a){if(!t)return a();Object(n["i"])(t)?a():a(new Error("长度5到20位，只支持数字、大小写英文或下划线组合"))},s=function(e,t,a){if(!t)return a();Object(n["e"])(t)?a():a(new Error("密码长度8~20位，英文加数字"))},u=function(e,t,a){if(!t||"长期"===t)return a();if(Object(n["d"])(t)){var l=t.toString().trim().replace(" ","");if(8!==l.length)return a();l=l.slice(0,4)+"/"+l.slice(4,6)+"/"+l.slice(6,l.length);var r=new Date(l).getTime();if(isNaN(r))return a(new Error("请输入正确的日期"));var i=(new Date).getTime();r<i&&a(new Error("有效期必须大于当前日期")),a()}a(new Error("请输入yyyyMMdd格式的日期"))},c=function(e,t,a){if(!t)return a();Object(n["h"])(t)?a():a(new Error("电话/座机格式错误！"))},o=function(e,t,a){Object(n["m"])(t)?a():a(new Error("金额格式有误"))},_=function(e,t,a){if(""===t)return a(new Error("不能为空"));Object(n["b"])(t)?0===Number(t)?a(new Error("请输入大于0的数字")):a():a(new Error("请输入数字"))},d=function(e,t,a){t?0===Number(t)?a(new Error("请输入大于0的数字！")):Object(n["l"])(t)?a():a(new Error("最多2位数字可保留一位小数!")):a(new Error("请输入！"))},f=function(e,t,a){t?0===Number(t)?a(new Error("请输入大于0的数字！")):Object(n["n"])(t)?a():a(new Error("最多1位数字可保留3位小数!")):a(new Error("请输入！"))},m=function(e,t,a){t?Object(n["k"])(t)&&0!==Number(t)?a():a(new Error("格式错误")):a(new Error("请输入必填项"))},p=function(e,t,a){t?Object(n["d"])(t)?a():a(new Error("请输入数字")):a()}},e925:function(e,t,a){"use strict";a.d(t,"c",(function(){return n})),a.d(t,"g",(function(){return l})),a.d(t,"i",(function(){return r})),a.d(t,"e",(function(){return i})),a.d(t,"h",(function(){return s})),a.d(t,"f",(function(){return u})),a.d(t,"d",(function(){return c})),a.d(t,"m",(function(){return o})),a.d(t,"l",(function(){return _})),a.d(t,"n",(function(){return d})),a.d(t,"j",(function(){return f})),a.d(t,"b",(function(){return m})),a.d(t,"k",(function(){return p})),a.d(t,"a",(function(){return b}));var n=function(e){return/^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(e)},l=function(e){return/^([0-9]{3,4}-)?[0-9]{7,8}$/.test(e.toString())},r=function(e){return/^\w{5,20}$/.test(e)},i=function(e){return/^(?=.*[0-9])(?=.*[a-zA-Z])(.{8,20})$/.test(e)},s=function(e){return/(^([0-9]{3,4}-)?[0-9]{7,8}$)|(^1[3,4,5,6,7,8,9][0-9]{9}$)/.test(e.toString())},u=function(e){return/^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(e.toString())},c=function(e){return/\d/.test(e)},o=function(e){return/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(e)},_=function(e){return/^(([1-9]?[0-9])|([1-9]?[0-9]\.[1-9])|([1-9]?[1-9]\.[0]))$/.test(e)},d=function(e){return/^(([0-9])|([0-9]\.\d{1,3})|([1-9]\.[0]))$/.test(e)},f=function(e){return/^-?(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(e)},m=function(e){return/^[0-9]+$/.test(e)},p=function(e){return/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1}|[1-9][0-9]*\.\d{1})))$/.test(e)},b=function(e){return/^[a-zA-Z0-9]+$/.test(e)}}}]);