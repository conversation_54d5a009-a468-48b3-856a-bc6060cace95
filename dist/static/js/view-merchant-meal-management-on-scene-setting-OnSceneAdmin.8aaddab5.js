(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-on-scene-setting-OnSceneAdmin"],{"0de6":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"OnSceneAdmin container-wrapper"},[e("refresh-tool",{on:{refreshPage:t.refreshHandle}}),e("search-form",{ref:"searchRef",attrs:{"form-setting":t.searchFormSetting,"label-width":"105px"},on:{search:t.searchHandle}}),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("数据列表")]),e("div",{staticClass:"align-r"},[e("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(e){return t.addOrEditMeal("add","device")}}},[t._v("设备堂食")]),e("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(e){return t.addOrEditMeal("add","normal")}}},[t._v("新增堂食")]),e("button-icon",{attrs:{color:"plain",type:"del"},on:{click:function(e){return t.mulOperation("mulDel")}}},[t._v("删除")])],1)]),e("div",{staticClass:"table-content"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:"","header-row-class-name":"ps-table-header-row","cell-style":{textAlign:"center"},"header-cell-style":{textAlign:"center"}},on:{"selection-change":t.handleSelectionChange}},[e("el-table-column",{attrs:{type:"selection",width:"50",align:"center","class-name":"ps-checkbox"}}),e("el-table-column",{attrs:{align:"center",prop:"organization_alias",label:"组织"}}),e("el-table-column",{attrs:{align:"center",prop:"consume_organizations_alias",label:"适用消费点"},scopedSlots:t._u([{key:"default",fn:function(r){return t._l(r.row.report_organization_alias,(function(r){return e("el-tag",{key:r,staticStyle:{"margin-right":"8px"}},[t._v(" "+t._s(r)+" ")])}))}}])}),e("el-table-column",{attrs:{align:"center",prop:"card_user_groups_alias",label:"适用设备/分组"},scopedSlots:t._u([{key:"default",fn:function(r){return t._l(r.row.report_card_groups_alias,(function(r){return e("el-tag",{key:r,staticStyle:{"margin-right":"8px"}},[t._v(" "+t._s(r)+" ")])}))}}])}),e("el-table-column",{attrs:{align:"center",prop:"menu_type_alias",label:"适用餐段"},scopedSlots:t._u([{key:"default",fn:function(r){return t._l(r.row.meal_type_detail.meal_type_verbose,(function(r){return e("el-tag",{key:r,staticStyle:{margin:"0 8px 8px 0"}},[t._v(" "+t._s(r)+" ")])}))}}])}),e("el-table-column",{attrs:{prop:"create_time",label:"创建时间",width:"160"}}),e("el-table-column",{attrs:{prop:"is_open",label:"状态"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},on:{change:function(e){return t.isOpenSetting(r.row)}},model:{value:r.row.is_open,callback:function(e){t.$set(r.row,"is_open",e)},expression:"scope.row.is_open"}})]}}])}),e("el-table-column",{attrs:{label:"操作",width:"180"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.addOrEditMeal("edit",r.row.sceneType,r.row)}}},[t._v("编辑")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.mulOperation("del",r.row)}}},[t._v("删除")])]}}])})],1)],1),e("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[e("el-pagination",{staticClass:"ps-text",attrs:{"current-page":t.currentPage,"page-sizes":[10,20,50,100,500],"page-size":t.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:t.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)])],1)},o=[],a=r("ed08");function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},s=a.iterator||"@@iterator",l=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function p(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{p({},"")}catch(t){p=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var a=e&&e.prototype instanceof b?e:b,i=Object.create(a.prototype),c=new M(n||[]);return o(i,"_invoke",{value:j(t,r,c)}),i}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var d="suspendedStart",g="suspendedYield",v="executing",y="completed",m={};function b(){}function w(){}function _(){}var x={};p(x,s,(function(){return this}));var L=Object.getPrototypeOf,S=L&&L(L(R([])));S&&S!==r&&n.call(S,s)&&(x=S);var O=_.prototype=b.prototype=Object.create(x);function k(t){["next","throw","return"].forEach((function(e){p(t,e,(function(t){return this._invoke(e,t)}))}))}function P(t,e){function r(o,a,c,s){var l=h(t[o],t,a);if("throw"!==l.type){var u=l.arg,p=u.value;return p&&"object"==i(p)&&n.call(p,"__await")?e.resolve(p.__await).then((function(t){r("next",t,c,s)}),(function(t){r("throw",t,c,s)})):e.resolve(p).then((function(t){u.value=t,c(u)}),(function(t){return r("throw",t,c,s)}))}s(l.arg)}var a;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return a=a?a.then(o,o):o()}})}function j(e,r,n){var o=d;return function(a,i){if(o===v)throw Error("Generator is already running");if(o===y){if("throw"===a)throw i;return{value:t,done:!0}}for(n.method=a,n.arg=i;;){var c=n.delegate;if(c){var s=E(c,n);if(s){if(s===m)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===d)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var l=h(e,r,n);if("normal"===l.type){if(o=n.done?y:g,l.arg===m)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(o=y,n.method="throw",n.arg=l.arg)}}}function E(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,E(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var a=h(o,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,m;var i=a.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function C(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function z(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function M(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(C,this),this.reset(!0)}function R(e){if(e||""===e){var r=e[s];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(i(e)+" is not iterable")}return w.prototype=_,o(O,"constructor",{value:_,configurable:!0}),o(_,"constructor",{value:w,configurable:!0}),w.displayName=p(_,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,p(t,u,"GeneratorFunction")),t.prototype=Object.create(O),t},e.awrap=function(t){return{__await:t}},k(P.prototype),p(P.prototype,l,(function(){return this})),e.AsyncIterator=P,e.async=function(t,r,n,o,a){void 0===a&&(a=Promise);var i=new P(f(t,r,n,o),a);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},k(O),p(O,u,"Generator"),p(O,s,(function(){return this})),p(O,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=R,M.prototype={constructor:M,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(z),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return c.type="throw",c.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],c=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var s=n.call(i,"catchLoc"),l=n.call(i,"finallyLoc");if(s&&l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=t,i.arg=e,a?(this.method="next",this.next=a.finallyLoc,m):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),z(r),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;z(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:R(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),m}},e}function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function l(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach((function(e){u(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function u(t,e,r){return(e=p(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function p(t){var e=f(t,"string");return"symbol"==i(e)?e:e+""}function f(t,e){if("object"!=i(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function h(t,e,r,n,o,a,i){try{var c=t[a](i),s=c.value}catch(t){return void r(t)}c.done?e(s):Promise.resolve(s).then(n,o)}function d(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var a=t.apply(e,r);function i(t){h(a,n,o,i,c,"next",t)}function c(t){h(a,n,o,i,c,"throw",t)}i(void 0)}))}}var g={name:"OnSceneAdmin",data:function(){return{isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[],searchFormSetting:{report_organizations:{type:"organizationSelect",label:"组织",value:[],placeholder:"请选择",multiple:!0,collapseTags:!0,checkStrictly:!0,dataList:[]},report_card_groups:{type:"groupSelect",label:"适用分组",value:[],placeholder:"请选择",multiple:!0}},selectListId:[]}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getMealReportList()},searchHandle:Object(a["d"])((function(){this.currentPage=1,this.getMealReportList()}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.currentPage=1,this.initLoad()},formatQueryParams:function(t){var e={};for(var r in t)""!==t[r].value&&null!==t[r].value&&0!==t[r].value.length&&("select_date"!==r?e[r]=t[r].value:t[r].value.length>0&&(e.start_date=t[r].value[0],e.end_date=t[r].value[1]));return e},getMealReportList:function(){var t=this;return d(c().mark((function e(){var r;return c().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.$apis.apiBackgroundReportMealReportMealSettingsListPost(l(l({},t.formatQueryParams(t.searchFormSetting)),{},{page:t.currentPage,page_size:t.pageSize}));case 3:r=e.sent,t.isLoading=!1,0===r.code?(t.tableData=r.data.results,t.totalCount=r.data.count):t.$message.error(r.msg);case 6:case"end":return e.stop()}}),e)})))()},handleSizeChange:function(t){this.pageSize=t,this.getMealReportList()},handleCurrentChange:function(t){this.currentPage=t,this.getMealReportList()},handleSelectionChange:function(t){var e=this;this.selectListId=[];var r=Object.freeze(t);r.map((function(t){e.selectListId.push(t.id)}))},switchFn:function(t){},mulOperation:function(t,e){var r=this;if(!e&&!this.selectListId.length)return this.$message.error("请先选择数据！");var n="提示",o="";switch(t){case"del":o="确定删除该报餐设置？";break;case"mulDel":o="确定删除所选报餐设置？";break}this.$confirm("".concat(o),"".concat(n),{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var n=d(c().mark((function n(o,a,i){return c().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if("confirm"!==o){n.next=13;break}a.confirmButtonLoading=!0,n.t0=t,n.next="del"===n.t0?5:"mulDel"===n.t0?7:9;break;case 5:return r.deleteSetting([e.id]),n.abrupt("break",9);case 7:return r.deleteSetting(r.selectListId),n.abrupt("break",9);case 9:i(),a.confirmButtonLoading=!1,n.next=14;break;case 13:a.confirmButtonLoading||i();case 14:case"end":return n.stop()}}),n)})));function o(t,e,r){return n.apply(this,arguments)}return o}()}).then((function(t){})).catch((function(t){}))},deleteSetting:function(t){var e=this;return d(c().mark((function r(){var n;return c().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,e.$apis.apiBackgroundReportMealReportMealSettingsDeletePost({ids:t});case 2:n=r.sent,0===n.code?(e.$message.success(n.msg),e.getMealReportList()):e.$message.error(n.msg);case 4:case"end":return r.stop()}}),r)})))()},isOpenSetting:function(t){var e=this;return d(c().mark((function r(){var n,o;return c().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return n={id:t.id,is_open:t.is_open},r.next=3,e.$apis.apiBackgroundReportMealReportMealSettingsModifyOpenStatusPost(n);case 3:o=r.sent,0===o.code?(e.$message.success(o.msg),e.getMealReportList()):e.$message.error(o.msg);case 5:case"end":return r.stop()}}),r)})))()},addOrEditMeal:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.$router.push({name:"AddOrEditOnScene",params:{type:t,sceneType:e},query:{data:encodeURIComponent(JSON.stringify(r))}})}}},v=g,y=r("2877"),m=Object(y["a"])(v,n,o,!1,null,"038f1321",null);e["default"]=m.exports}}]);