(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-user-health-records-detail-BasicInfo"],{"1f11":function(a,t,s){"use strict";s("a1e9")},3895:function(a,t,s){"use strict";s.r(t);var e=function(){var a=this,t=a._self._c;return t("div",{staticClass:"detail-basic-info"},[t("div",{staticClass:"basic-info records-wrapp-bg m-b-20"},[t("div",{staticClass:"p-b-10",staticStyle:{"font-weight":"bold"}},[a._v("基本属性")]),t("div",{staticClass:"ps-flex flex-align-c p-b-20"},[t("el-image",{staticStyle:{width:"50px",height:"50px","border-radius":"50px"},attrs:{fit:"fill",src:a.formData.head_image?a.formData.head_image:"男"===a.formData.gender?s("abc7"):s("89ce")}}),t("div",{staticClass:"p-l-50"},[t("div",[t("span",{staticClass:"p-r-20 info-name"},[a._v(a._s(a.formData.name))]),t("span",[a._v(a._s(a.formData.gender))])]),t("div",{staticClass:"info-id"},[t("span",{staticStyle:{color:"#23282d",opacity:"0.4"}},[a._v("用户ID：")]),t("span",[a._v(a._s(a.formData.user_id))])]),t("div",{staticClass:"info-id"},[t("span",{staticStyle:{color:"#23282d",opacity:"0.4"}},[a._v("档案ID：")]),t("span",[a._v(a._s(a.formData.id))])])])],1),t("el-form",{ref:"form",attrs:{size:"mini",model:a.formData,"label-position":"left","label-width":"80px"}},[t("el-form-item",{attrs:{label:"手机号：","label-width":"100px"}},[t("span",[a._v(a._s(a.formData.phone))])]),t("el-form-item",{attrs:{label:"出生日期：","label-width":"100px"}},[t("span",[a._v(a._s(a.formData.birthday))])]),t("el-form-item",{attrs:{label:"年龄：","label-width":"100px"}},[t("span",[a._v(a._s(a.formData.age)+"岁")])]),t("el-form-item",{attrs:{label:"身高：","label-width":"100px"}},[t("span",[a._v(a._s(a.formData.height)+"cm")])]),t("el-form-item",{attrs:{label:"体重：","label-width":"100px"}},[t("span",[a._v(a._s(a.formData.weight)+"kg")])]),t("el-form-item",{attrs:{label:"BMI：","label-width":"100px"}},[t("span",[a._v(a._s(a.formData.bmi))]),t("span",{staticClass:"info-bmi-status m-l-10"},[a._v(a._s(a.formData.bmi_text))])]),t("el-form-item",{attrs:{label:"体脂率：","label-width":"100px"}},[t("span",[a._v(a._s(a.formData.fat))]),t("span",{staticClass:"info-fat-status m-l-10"},[a._v(a._s(a.formData.fat_text))])])],1)],1)])},i=[],l={props:{formInfoData:{type:Object,default:function(){return{}}}},data:function(){return{formData:{}}},watch:{formInfoData:function(a){this.formData=a}},mounted:function(){},methods:{}},o=l,r=(s("1f11"),s("2877")),f=Object(r["a"])(o,e,i,!1,null,null,null);t["default"]=f.exports},a1e9:function(a,t,s){}}]);