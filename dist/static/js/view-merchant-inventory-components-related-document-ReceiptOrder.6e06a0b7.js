(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-inventory-components-related-document-ReceiptOrder","purchase_order_list","view-merchant-inventory-components-related-document-DriverInformation","view-merchant-inventory-components-related-document-PurchaseRelatedDocument","view-merchant-inventory-components-related-document-VehicleInformation"],{"2c5d":function(t,e,r){"use strict";r("db19")},"2c74":function(t,e,r){"use strict";r.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"vehicleInformation-box"},[t._l(t.vehicleList,(function(r,i){return e("div",{key:i,staticClass:"vehicle-item"},[e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("车辆类型：")]),t._v(t._s(r.car_type_alias)+" ")]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("车牌号：")]),t._v(t._s(r.plate_number)+" ")]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("车辆图片：")]),e("div",{staticClass:"form-img-box"},t._l(r.car_img,(function(i,a){return e("el-image",{key:i,staticClass:"detault-img m-r-6 pointer",attrs:{src:i,fit:"contain"},on:{click:function(e){return t.clickViewerHandler(r.car_img,a)}}})})),1)]),t.showDivider?e("el-divider"):t._e()],1)})),e("image-viewer",{attrs:{"initial-index":t.imgIndex,"z-index":3e3,"on-close":t.closeViewer,"preview-src-list":t.previewSrcList},model:{value:t.showViewer,callback:function(e){t.showViewer=e},expression:"showViewer"}})],2)},a=[],n={name:"VehicleInformation",props:{vehicleList:{type:Array,default:function(){return[]}},showDivider:{type:Boolean,default:!1}},data:function(){return{imgIndex:0,previewSrcList:[],showViewer:!1}},computed:{},watch:{},created:function(){},mounted:function(){},methods:{clickViewerHandler:function(t,e){this.previewSrcList=t||[],this.imgIndex=e,this.showViewer=!0},closeViewer:function(){this.showViewer=!1}}},o=n,c=(r("5cc2"),r("2877")),s=Object(c["a"])(o,i,a,!1,null,"6526e0fe",null);e["default"]=s.exports},"393b6":function(t,e,r){"use strict";r.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"receipt-order-box"},["detail"!==t.type?e("div",{staticClass:"info-wrap"},[e("div",{staticClass:"title"},[t._v("基本信息")]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("单据编号：")]),t._v(t._s(t.detailData.trade_no))]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("创建时间：")]),t._v(t._s(t.detailData.create_time))]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("收货时间：")]),t._v(t._s(t.detailData.create_time))])]):t._e(),e("div",{staticClass:"info-wrap"},[e("div",{staticClass:"title"},[t._v("配送信息")]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("配送温度：")]),t._v(t._s(t.detailData.delivery_temperature)+"°c")]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("单据凭证：")]),t.detailData.purchase_certificate&&t.detailData.purchase_certificate.length>0?e("div",{staticClass:"form-img-box"},t._l(t.detailData.purchase_certificate,(function(r,i){return e("el-image",{key:r,staticClass:"detault-img m-r-6 pointer",attrs:{src:r,fit:"contain","preview-src-list":t.detailData.purchase_certificate,"initial-index":i}})})),1):t._e()])]),e("DriverInformation",{attrs:{"driver-list":t.detailData.driver_info}}),e("VehicleInformation",{attrs:{"vehicle-list":t.detailData.vehicle_info}}),"receiptOrder"===t.tabType?e("div",{staticClass:"info-wrap"},[e("div",{staticClass:"title"},[t._v("核验人员信息")]),t.detailData.check_accounts&&t.detailData.check_accounts.length>0?e("ul",{staticClass:"check-box flex flex-wrap"},t._l(t.detailData.check_accounts,(function(r,i){return e("li",{key:i,staticClass:"check-item m-r-20"},[e("div",{staticClass:"flex"},[e("span",[t._v(t._s(r.role_name)+"：")]),e("span",[t._v(t._s(r.username))])]),e("div",{staticClass:"flex"},[e("span",[t._v("收货实况人脸：")]),e("span",[e("el-image",{staticClass:"face-img",attrs:{src:r.face_url,fit:"contain","preview-src-list":[r.face_url]}})],1)])])})),0):e("div",[t._v("无")])]):t._e(),e("div",{staticClass:"info-wrap"},[e("div",{staticClass:"title"},[t._v(t._s("detail"!==t.type?"物资核验信息":"合格物资信息"))]),e("div",[t._v("合计金额：￥"+t._s(t.totalPrice))]),e("el-table",{ref:"tableData",staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:"",size:"mini","max-height":"600","header-row-class-name":"ps-table-header-row"}},t._l(t.tableSettings,(function(r){return e("table-column",{key:r.key,attrs:{col:r},scopedSlots:t._u([{key:"img",fn:function(r){var i=r.row;return[i.check_file&&i.check_file.length>0?e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.clickViewerHandler(i)}}},[t._v("查看")]):e("span",[t._v("--")])]}}],null,!0)})})),1)],1),"detail"===t.type?e("div",{staticClass:"info-wrap"},[e("div",{staticClass:"title"},[t._v("不合格物资信息")]),e("div",[t._v("合计金额：￥"+t._s(t.noPassTotalPrice))]),e("el-table",{ref:"tableData",staticStyle:{width:"100%"},attrs:{data:t.noPassTableData,stripe:"",size:"mini","max-height":"600","header-row-class-name":"ps-table-header-row"}},t._l(t.noPassTableSettings,(function(r){return e("table-column",{key:r.key,attrs:{col:r},scopedSlots:t._u([{key:"img",fn:function(r){var i=r.row;return[i.check_file&&i.check_file.length>0?e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.clickViewerHandler(i)}}},[t._v("查看")]):e("span",[t._v("--")])]}}],null,!0)})})),1)],1):t._e(),e("image-viewer",{attrs:{"initial-index":t.imgIndex,"z-index":3e3,"on-close":t.closeViewer,"preview-src-list":t.previewSrcList},model:{value:t.showViewer,callback:function(e){t.showViewer=e},expression:"showViewer"}})],1)},a=[],n=r("ed08"),o=r("2c74"),c=r("d8a4"),s=r("da92");function l(t){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},l(t)}function u(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */u=function(){return e};var t,e={},r=Object.prototype,i=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",c=n.asyncIterator||"@@asyncIterator",s=n.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function d(t,e,r,i){var n=e&&e.prototype instanceof w?e:w,o=Object.create(n.prototype),c=new I(i||[]);return a(o,"_invoke",{value:S(t,r,c)}),o}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=d;var h="suspendedStart",v="suspendedYield",m="executing",_="completed",y={};function w(){}function g(){}function b(){}var x={};f(x,o,(function(){return this}));var k=Object.getPrototypeOf,C=k&&k(k(E([])));C&&C!==r&&i.call(C,o)&&(x=C);var L=b.prototype=w.prototype=Object.create(x);function D(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function V(t,e){function r(a,n,o,c){var s=p(t[a],t,n);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==l(f)&&i.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,o,c)}),(function(t){r("throw",t,o,c)})):e.resolve(f).then((function(t){u.value=t,o(u)}),(function(t){return r("throw",t,o,c)}))}c(s.arg)}var n;a(this,"_invoke",{value:function(t,i){function a(){return new e((function(e,a){r(t,i,e,a)}))}return n=n?n.then(a,a):a()}})}function S(e,r,i){var a=h;return function(n,o){if(a===m)throw Error("Generator is already running");if(a===_){if("throw"===n)throw o;return{value:t,done:!0}}for(i.method=n,i.arg=o;;){var c=i.delegate;if(c){var s=O(c,i);if(s){if(s===y)continue;return s}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if(a===h)throw a=_,i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);a=m;var l=p(e,r,i);if("normal"===l.type){if(a=i.done?_:v,l.arg===y)continue;return{value:l.arg,done:i.done}}"throw"===l.type&&(a=_,i.method="throw",i.arg=l.arg)}}}function O(e,r){var i=r.method,a=e.iterator[i];if(a===t)return r.delegate=null,"throw"===i&&e.iterator.return&&(r.method="return",r.arg=t,O(e,r),"throw"===r.method)||"return"!==i&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+i+"' method")),y;var n=p(a,e.iterator,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,y;var o=n.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function j(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function P(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(j,this),this.reset(!0)}function E(e){if(e||""===e){var r=e[o];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,n=function r(){for(;++a<e.length;)if(i.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return n.next=n}}throw new TypeError(l(e)+" is not iterable")}return g.prototype=b,a(L,"constructor",{value:b,configurable:!0}),a(b,"constructor",{value:g,configurable:!0}),g.displayName=f(b,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,f(t,s,"GeneratorFunction")),t.prototype=Object.create(L),t},e.awrap=function(t){return{__await:t}},D(V.prototype),f(V.prototype,c,(function(){return this})),e.AsyncIterator=V,e.async=function(t,r,i,a,n){void 0===n&&(n=Promise);var o=new V(d(t,r,i,a),n);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},D(L),f(L,s,"Generator"),f(L,o,(function(){return this})),f(L,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var i in e)r.push(i);return r.reverse(),function t(){for(;r.length;){var i=r.pop();if(i in e)return t.value=i,t.done=!1,t}return t.done=!0,t}},e.values=E,I.prototype={constructor:I,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(P),!e)for(var r in this)"t"===r.charAt(0)&&i.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(i,a){return c.type="throw",c.arg=e,r.next=i,a&&(r.method="next",r.arg=t),!!a}for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n],c=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var s=i.call(o,"catchLoc"),l=i.call(o,"finallyLoc");if(s&&l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&i.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var n=a;break}}n&&("break"===t||"continue"===t)&&n.tryLoc<=e&&e<=n.finallyLoc&&(n=null);var o=n?n.completion:{};return o.type=t,o.arg=e,n?(this.method="next",this.next=n.finallyLoc,y):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),P(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var i=r.completion;if("throw"===i.type){var a=i.arg;P(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,i){return this.delegate={iterator:E(e),resultName:r,nextLoc:i},"next"===this.method&&(this.arg=t),y}},e}function f(t,e){return m(t)||v(t,e)||p(t,e)||d()}function d(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function p(t,e){if(t){if("string"==typeof t)return h(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?h(t,e):void 0}}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,i=Array(e);r<e;r++)i[r]=t[r];return i}function v(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var i,a,n,o,c=[],s=!0,l=!1;try{if(n=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(i=n.call(r)).done)&&(c.push(i.value),c.length!==e);s=!0);}catch(t){l=!0,a=t}finally{try{if(!s&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw a}}return c}}function m(t){if(Array.isArray(t))return t}function _(t,e,r,i,a,n,o){try{var c=t[n](o),s=c.value}catch(t){return void r(t)}c.done?e(s):Promise.resolve(s).then(i,a)}function y(t){return function(){var e=this,r=arguments;return new Promise((function(i,a){var n=t.apply(e,r);function o(t){_(n,i,a,o,c,"next",t)}function c(t){_(n,i,a,o,c,"throw",t)}o(void 0)}))}}var w={name:"ReceiptOrderBox",props:{type:{type:String,default:"detail"},params:{type:Object,default:function(){}},api:{type:String,default:"apiBackgroundDrpVendorDataVendorReceivingDetailListPost"},infoData:{type:Object,default:function(){}}},components:{VehicleInformation:o["default"],DriverInformation:c["default"]},data:function(){return{isLoading:!1,tabType:"receiptOrder",tableData:[],tableSettings:[{label:"物资名称",key:"materials_name"},{label:"收货数量",key:"receive_count"},{label:"单价",key:"unit_price"},{label:"合计金额",key:"total_price"},{label:"保质期",key:"valid_date"},{label:"核验附件",key:"check_file",type:"slot",slotName:"img"}],noPassTableSettings:[{label:"物资名称",key:"materials_name"},{label:"收货数量",key:"receive_count"},{label:"单价",key:"unit_price"},{label:"合计金额",key:"total_price"},{label:"保质期",key:"valid_date"},{label:"核验附件",key:"check_file",type:"slot",slotName:"img"}],vehicleList:[],detailData:{},imgIndex:0,previewSrcList:[],showViewer:!1,totalPrice:0,noPassTableData:[],noPassTotalPrice:0}},computed:{},watch:{},mounted:function(){this.initLoad()},methods:{initLoad:function(){this.getDataInfo()},getDataInfo:function(){var t=this;return y(u().mark((function e(){var r,i,a,o,c,l,d,p;return u().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.isLoading){e.next=2;break}return e.abrupt("return");case 2:if(r=t.api,t.api||"detail"!==t.type||(r="apiBackgroundDrpVendorDataVendorReceivingDetailListPost"),r){e.next=6;break}return e.abrupt("return",t.$message.error("缺少参数，请检查！"));case 6:return t.isLoading=!0,e.next=9,t.$to(t.$apis[r](t.params));case 9:if(i=e.sent,a=f(i,2),o=a[0],c=a[1],t.isLoading=!1,t.detailData={},!o){e.next=18;break}return t.$message.error(o.message),e.abrupt("return");case 18:0===c.code?(l=c.data||{},t.totalPrice=0,t.noPassTotalPrice=0,d=0,p=0,t.noPassTableData=[],t.tableData=[],null!==l&&void 0!==l&&l.ingredient_data&&l.ingredient_data.map((function(e){"detail"===t.type?1===e.is_pass?(d=s["a"].plus(d,e.total_price),e.receive_count=(e.receive_count||0)+(e.purchase_unit||""),e.unit_price="￥"+Object(n["i"])(e.unit_price)+(e.purchase_unit?"/"+e.purchase_unit:""),e.total_price="￥"+Object(n["i"])(e.total_price),e.valid_date=e.start_valid_date+"-"+e.end_valid_date,t.tableData.push(e)):(p=s["a"].plus(p,e.total_price),e.receive_count=(e.receive_count||0)+(e.purchase_unit||""),e.unit_price="￥"+Object(n["i"])(e.unit_price)+(e.purchase_unit?"/"+e.purchase_unit:""),e.total_price="￥"+Object(n["i"])(e.total_price),e.valid_date=e.start_valid_date+"-"+e.end_valid_date,t.noPassTableData.push(e)):(d=s["a"].plus(d,e.total_price),e.receive_count=(e.receive_count||0)+(e.purchase_unit||""),e.unit_price="￥"+Object(n["i"])(e.unit_price)+(e.purchase_unit?"/"+e.purchase_unit:""),e.total_price="￥"+Object(n["i"])(e.total_price),e.valid_date=e.start_valid_date+"-"+e.end_valid_date,t.tableData.push(e))})),t.totalPrice=Object(n["i"])(d),t.noPassTotalPrice=Object(n["i"])(p),t.detailData=l||{}):t.$message.error(c.msg);case 19:case"end":return e.stop()}}),e)})))()},clickViewerHandler:function(t,e){this.previewSrcList=t.check_file||[],this.imgIndex=e||0,this.showViewer=!0},closeViewer:function(){this.showViewer=!1}}},g=w,b=(r("2c5d"),r("2877")),x=Object(b["a"])(g,i,a,!1,null,"8ad29810",null);e["default"]=x.exports},"47b5":function(t,e,r){},"5cc2":function(t,e,r){"use strict";r("47b5")},"788a":function(t,e,r){"use strict";r("eeb4")},d8a4:function(t,e,r){"use strict";r.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"driverInformation-box"},[t._l(t.driverList,(function(r,i){return e("div",{key:i,staticClass:"driver-item"},[e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("司机姓名：")]),t._v(t._s(r.name)+" ")]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("联系方式：")]),t._v(t._s(r.phone)+" ")]),e("div",{staticClass:"form-item"},[e("span",{staticClass:"form-label"},[t._v("证件信息：")]),e("div",{staticClass:"form-img-box"},[t._l(r.health_certificate,(function(i,a){return e("div",{key:i,staticClass:"form-img"},[e("el-image",{staticClass:"detault-img m-r-6 pointer",attrs:{src:i,fit:"contain"},on:{click:function(e){return t.clickViewerHandler(r.health_certificate,a)}}}),e("div",{staticClass:"text-center"},[t._v("健康证")])],1)})),t._l(r.driving_licence,(function(i,a){return e("div",{key:i,staticClass:"form-img"},[e("el-image",{staticClass:"detault-img m-r-6 pointer",attrs:{src:i,fit:"contain"},on:{click:function(e){return t.clickViewerHandler(r.driving_licence,a)}}}),e("div",{staticClass:"text-center"},[t._v("驾驶证")])],1)}))],2)]),t.showDivider?e("el-divider"):t._e()],1)})),e("image-viewer",{attrs:{"initial-index":t.imgIndex,"z-index":3e3,"on-close":t.closeViewer,"preview-src-list":t.previewSrcList},model:{value:t.showViewer,callback:function(e){t.showViewer=e},expression:"showViewer"}})],2)},a=[],n={name:"DriverInformation",props:{driverList:{type:Array,default:function(){return[]}},showDivider:{type:Boolean,default:!1}},data:function(){return{imgIndex:0,previewSrcList:[],showViewer:!1}},computed:{},watch:{},created:function(){},mounted:function(){},methods:{clickViewerHandler:function(t,e){this.previewSrcList=t||[],this.imgIndex=e,this.showViewer=!0},closeViewer:function(){this.showViewer=!1}}},o=n,c=(r("788a"),r("2877")),s=Object(c["a"])(o,i,a,!1,null,"51d05e80",null);e["default"]=s.exports},db19:function(t,e,r){},eeb4:function(t,e,r){}}]);