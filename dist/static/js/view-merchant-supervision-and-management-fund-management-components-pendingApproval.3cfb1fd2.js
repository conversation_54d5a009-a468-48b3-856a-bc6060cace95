(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-supervision-and-management-fund-management-components-pendingApproval"],{"2aa7":function(t,e,a){},"64e8":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t._self._c;return e("div",[e("search-form",{ref:"searchRef",attrs:{"form-setting":t.searchFormSetting,autoSearch:!1},on:{search:t.searchHandle}}),e("div",{staticClass:"table-wrapper"},[t._m(0),e("div",{staticClass:"table-content"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableData",attrs:{data:t.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},t._l(t.tableSetting,(function(a,r){return e("table-column",{key:r,attrs:{col:a},scopedSlots:t._u([{key:"applyFee",fn:function(e){var a=e.row;return[t._v(" "+t._s(t.computedFee(a.apply_fee))+" ")]}},{key:"operation",fn:function(a){var r=a.row;return[e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.getDetail(r,"approve")}}},[t._v("审批")]),e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.getDetail(r,"detail")}}},[t._v("详情")])]}}],null,!0)})})),1),e("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[e("el-pagination",{staticClass:"ps-text",attrs:{"current-page":t.currentPage,"page-sizes":[10,20,50,100,500],"page-size":t.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:t.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1)]),e("div",{staticClass:"ps-el-drawer"},[e("el-drawer",{attrs:{title:"详情",visible:t.detailDrawerShow,"show-close":!1,size:"40%"}},[e("div",{staticClass:"p-20"},[e("el-form",{ref:"detailDrawerFormRef",attrs:{model:t.detailDrawerForm,"label-width":"100px","label-position":"top"}},[e("el-form-item",{attrs:{label:"申请信息"}},[e("table",{staticClass:"m-l-30"},[e("tr",[e("td",{staticStyle:{width:"80px"}},[t._v("申请人：")]),e("td",[t._v(t._s(t.detailDrawerForm.approvalInfo.applicant))])]),e("tr",[e("td",{staticStyle:{width:"80px"}},[t._v("申请来源：")]),e("td",[t._v(t._s(t.detailDrawerForm.approvalInfo.applicationSource))])]),e("tr",[e("td",{staticStyle:{width:"80px"}},[t._v("申请内容：")]),e("td",[t._v(t._s(t.detailDrawerForm.approvalInfo.applicationContent))])]),e("tr",[e("td",{staticStyle:{width:"80px"}},[t._v("申请金额：")]),e("td",[t._v(t._s(t.computedFee(t.detailDrawerForm.approvalInfo.amountApplied)))])])])]),e("el-form-item",{attrs:{label:"单据信息"}},[t.detailDrawerForm.documentInfo.length?e("div",{staticClass:"m-l-30"},t._l(t.detailDrawerForm.documentInfo,(function(a,r){return e("div",{key:r},[t._v(t._s(a))])})),0):e("div",{staticClass:"m-l-30"},[e("span",[t._v("暂无内容")])])]),e("el-form-item",{attrs:{label:"凭证/附件"}},[t.detailDrawerForm.applicationDocuments.length?e("div",{staticClass:"m-l-30 flex-col"},t._l(t.detailDrawerForm.applicationDocuments,(function(a,r){return e("div",{key:r,staticClass:"w-350 flex-b-c m-r-10 m-b-10"},[e("div",{staticClass:"origin"},[t._v(t._s(a.name))]),e("div",{staticClass:"flex"},[t.computedFileType(a.name)?e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.handleClick(a.url)}}},[t._v("查看")]):t._e(),e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.downloadFile(a.url)}}},[t._v("下载")])],1)])})),0):e("div",{staticClass:"m-l-30"},[e("span",[t._v("暂无凭证/附件")])])]),e("el-form-item",{attrs:{label:""},scopedSlots:t._u([{key:"label",fn:function(){return[e("div",{staticClass:"ps-flex-align-c flex-align-c"},[e("div",{staticClass:"m-r-10"},[t._v("收款信息")]),t.computedShow?e("div",{staticClass:"red"},[t._v("当前收款账号非该供应商绑定账号，请注意")]):t._e()])]},proxy:!0}])},[e("table",{staticClass:"m-l-30"},[e("tr",[e("td",[t._v("供应商名称：")]),e("td",[t._v(t._s(t.detailDrawerForm.collectionInformation.collectingSupplier))])]),e("tr",[e("td",[t._v("收款人：")]),e("td",[t._v(t._s(t.detailDrawerForm.collectionInformation.beneficiaryAccountName))])]),e("tr",[e("td",[t._v("收款账号：")]),e("td",[t._v(t._s(t.detailDrawerForm.collectionInformation.receivablesAccount))])]),e("tr",[e("td",[t._v("收款银行：")]),e("td",[t._v(t._s(t.detailDrawerForm.collectionInformation.receivingBank))])])])]),e("el-form-item",{attrs:{label:"审批状态"}},[e("el-timeline",{staticClass:"p-t-10 m-l-35"},t._l(t.detailDrawerForm.approvalStatus,(function(a,r){return e("el-timeline-item",{key:r,attrs:{icon:a.icon,color:a.color,size:"large",timestamp:a.status_alias,placement:"top"}},t._l(a.data,(function(o,n){return e("div",{key:n,class:["and_approve"===t.approveMethod&&0!==r?"bg-grey":"","m-b-10"]},["and_approve"!==t.approveMethod?e("div",{staticClass:"flex-col"},[e("div",{staticClass:"w-350 flex-b-c"},[e("div",[t._v(t._s(o.operator))]),"PENDING"!==o.status?e("div",{staticClass:"w-150 flex-b-c"},["PENDING"!==o.status?e("div",[t._v(t._s(o.timestamp))]):t._e(),e("i",{class:o.icon,style:{color:o.color,fontSize:"18px"}})]):t._e()]),r>0&&"REVOKE"!==a.status&&o.reason?e("div",{staticStyle:{color:"#000"}},[t._v(" 审批意见："+t._s(o.reason)+" ")]):t._e()]):e("div",t._l(o,(function(a,o){return e("div",{key:o,staticClass:"flex-col"},[e("div",{staticClass:"w-350 flex-b-c"},[e("div",[t._v(t._s(a.operator))]),"PENDING"!==a.status?e("div",{staticClass:"w-150 flex-b-c"},["PENDING"!==a.status?e("div",[t._v(t._s(a.timestamp))]):t._e(),e("i",{class:[a.icon,"icon"],style:{color:a.color,fontSize:"18px"}})]):t._e()]),r>0&&"REVOKE"!==a.status&&a.reason?e("div",{staticStyle:{color:"#000"}},[t._v(" 审批意见："+t._s(a.reason)+" ")]):t._e()])})),0)])})),0)})),1)],1),e("el-form-item",{attrs:{label:""}},[e("div",{staticClass:"flex-start"},[e("div",{staticClass:"f-w-700 m-r-10",staticStyle:{color:"#606266"}},[e("span",{staticClass:"red"},[t._v("*")]),t._v("核定金额")]),e("div",[t._v(t._s(t.computedFee(t.detailDrawerForm.authorizedAmount)))])])]),e("el-form-item",{attrs:{label:"",rules:[{required:t.isNeedToRequire,message:"请输入审批意见",trigger:["change","blur"]}],prop:"remark"}},[e("div",{staticClass:"flex-start"},[e("div",{staticClass:"f-w-700 m-r-10",staticStyle:{color:"#606266",width:"70px"}},[e("span",{staticClass:"red"},[t._v("*")]),t._v("审批意见")]),e("el-input",{attrs:{type:"textarea",autosize:{minRows:4,maxRows:6},resize:"none","show-word-limit":"",maxlength:"100",disabled:t.canNotEdit},model:{value:t.detailDrawerForm.remark,callback:function(e){t.$set(t.detailDrawerForm,"remark",e)},expression:"detailDrawerForm.remark"}})],1)])],1),e("div",{staticClass:"ps-el-drawer-footer"},[e("el-button",{staticClass:"w-100",on:{click:t.cancelHandle}},[t._v("关闭")]),e("el-button",{directives:[{name:"show",rawName:"v-show",value:!t.canNotEdit,expression:"!canNotEdit"}],staticClass:"w-100",attrs:{type:"primary"},on:{click:function(e){return t.agreeHandle()}}},[t._v("同意")]),e("el-button",{directives:[{name:"show",rawName:"v-show",value:!t.canNotEdit,expression:"!canNotEdit"}],staticClass:"w-100",attrs:{type:"danger"},on:{click:function(e){return t.rejectHandle()}}},[t._v("拒绝")])],1)],1)])],1),t.showImagePreview?e("el-image-viewer",{staticStyle:{"z-index":"3000"},attrs:{"url-list":t.previewList,"hide-on-click-modal":"",teleported:"","on-close":t.closePreview}}):t._e()],1)},o=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("数据列表")])])}],n=a("ed08"),i=a("5a0c"),s=a.n(i),c=a("21a6"),l=a.n(c),p=a("08a9");function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function d(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,r)}return a}function f(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?d(Object(a),!0).forEach((function(e){v(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):d(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function v(t,e,a){return(e=h(e))in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function h(t){var e=m(t,"string");return"symbol"==u(e)?e:e+""}function m(t,e){if("object"!=u(t)||!t)return t;var a=t[Symbol.toPrimitive];if(void 0!==a){var r=a.call(t,e||"default");if("object"!=u(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function _(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */_=function(){return e};var t,e={},a=Object.prototype,r=a.hasOwnProperty,o=Object.defineProperty||function(t,e,a){t[e]=a.value},n="function"==typeof Symbol?Symbol:{},i=n.iterator||"@@iterator",s=n.asyncIterator||"@@asyncIterator",c=n.toStringTag||"@@toStringTag";function l(t,e,a){return Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function(t,e,a){return t[e]=a}}function p(t,e,a,r){var n=e&&e.prototype instanceof w?e:w,i=Object.create(n.prototype),s=new j(r||[]);return o(i,"_invoke",{value:C(t,a,s)}),i}function d(t,e,a){try{return{type:"normal",arg:t.call(e,a)}}catch(t){return{type:"throw",arg:t}}}e.wrap=p;var f="suspendedStart",v="suspendedYield",h="executing",m="completed",g={};function w(){}function y(){}function b(){}var E={};l(E,i,(function(){return this}));var k=Object.getPrototypeOf,D=k&&k(k(I([])));D&&D!==a&&r.call(D,i)&&(E=D);var x=b.prototype=w.prototype=Object.create(E);function F(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function a(o,n,i,s){var c=d(t[o],t,n);if("throw"!==c.type){var l=c.arg,p=l.value;return p&&"object"==u(p)&&r.call(p,"__await")?e.resolve(p.__await).then((function(t){a("next",t,i,s)}),(function(t){a("throw",t,i,s)})):e.resolve(p).then((function(t){l.value=t,i(l)}),(function(t){return a("throw",t,i,s)}))}s(c.arg)}var n;o(this,"_invoke",{value:function(t,r){function o(){return new e((function(e,o){a(t,r,e,o)}))}return n=n?n.then(o,o):o()}})}function C(e,a,r){var o=f;return function(n,i){if(o===h)throw Error("Generator is already running");if(o===m){if("throw"===n)throw i;return{value:t,done:!0}}for(r.method=n,r.arg=i;;){var s=r.delegate;if(s){var c=P(s,r);if(c){if(c===g)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===f)throw o=m,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=h;var l=d(e,a,r);if("normal"===l.type){if(o=r.done?m:v,l.arg===g)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(o=m,r.method="throw",r.arg=l.arg)}}}function P(e,a){var r=a.method,o=e.iterator[r];if(o===t)return a.delegate=null,"throw"===r&&e.iterator.return&&(a.method="return",a.arg=t,P(e,a),"throw"===a.method)||"return"!==r&&(a.method="throw",a.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var n=d(o,e.iterator,a.arg);if("throw"===n.type)return a.method="throw",a.arg=n.arg,a.delegate=null,g;var i=n.arg;return i?i.done?(a[e.resultName]=i.value,a.next=e.nextLoc,"return"!==a.method&&(a.method="next",a.arg=t),a.delegate=null,g):i:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,g)}function N(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function A(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function j(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(N,this),this.reset(!0)}function I(e){if(e||""===e){var a=e[i];if(a)return a.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,n=function a(){for(;++o<e.length;)if(r.call(e,o))return a.value=e[o],a.done=!1,a;return a.value=t,a.done=!0,a};return n.next=n}}throw new TypeError(u(e)+" is not iterable")}return y.prototype=b,o(x,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:y,configurable:!0}),y.displayName=l(b,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===y||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,l(t,c,"GeneratorFunction")),t.prototype=Object.create(x),t},e.awrap=function(t){return{__await:t}},F(S.prototype),l(S.prototype,s,(function(){return this})),e.AsyncIterator=S,e.async=function(t,a,r,o,n){void 0===n&&(n=Promise);var i=new S(p(t,a,r,o),n);return e.isGeneratorFunction(a)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},F(x),l(x,c,"Generator"),l(x,i,(function(){return this})),l(x,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),a=[];for(var r in e)a.push(r);return a.reverse(),function t(){for(;a.length;){var r=a.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=I,j.prototype={constructor:j,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(A),!e)for(var a in this)"t"===a.charAt(0)&&r.call(this,a)&&!isNaN(+a.slice(1))&&(this[a]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var a=this;function o(r,o){return s.type="throw",s.arg=e,a.next=r,o&&(a.method="next",a.arg=t),!!o}for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n],s=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var c=r.call(i,"catchLoc"),l=r.call(i,"finallyLoc");if(c&&l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(t,e){for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var n=o;break}}n&&("break"===t||"continue"===t)&&n.tryLoc<=e&&e<=n.finallyLoc&&(n=null);var i=n?n.completion:{};return i.type=t,i.arg=e,n?(this.method="next",this.next=n.finallyLoc,g):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.finallyLoc===t)return this.complete(a.completion,a.afterLoc),A(a),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.tryLoc===t){var r=a.completion;if("throw"===r.type){var o=r.arg;A(a)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,a,r){return this.delegate={iterator:I(e),resultName:a,nextLoc:r},"next"===this.method&&(this.arg=t),g}},e}function g(t,e){return k(t)||E(t,e)||y(t,e)||w()}function w(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function y(t,e){if(t){if("string"==typeof t)return b(t,e);var a={}.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?b(t,e):void 0}}function b(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,r=Array(e);a<e;a++)r[a]=t[a];return r}function E(t,e){var a=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=a){var r,o,n,i,s=[],c=!0,l=!1;try{if(n=(a=a.call(t)).next,0===e){if(Object(a)!==a)return;c=!1}else for(;!(c=(r=n.call(a)).done)&&(s.push(r.value),s.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=a.return&&(i=a.return(),Object(i)!==i))return}finally{if(l)throw o}}return s}}function k(t){if(Array.isArray(t))return t}function D(t,e,a,r,o,n,i){try{var s=t[n](i),c=s.value}catch(t){return void a(t)}s.done?e(c):Promise.resolve(c).then(r,o)}function x(t){return function(){var e=this,a=arguments;return new Promise((function(r,o){var n=t.apply(e,a);function i(t){D(n,r,o,i,s,"next",t)}function s(t){D(n,r,o,i,s,"throw",t)}i(void 0)}))}}var F={components:{ElImageViewer:p["a"]},data:function(){var t=[s()().subtract(7,"day").format("YYYY-MM-DD"),s()().format("YYYY-MM-DD")];return{searchFormSetting:{select_time:{type:"daterange",label:"日期筛选",clearable:!1,value:t},approve_no:{type:"input",label:"申请单号",value:"",placeholder:"请输入申请单号"},apply_source:{type:"select",label:"申请来源",value:"",placeholder:"请选择申请来源",dataList:[{label:"全部",value:""},{label:"财务申请",value:"cw"},{label:"采购单转化",value:"cgd"}]}},isLoading:!1,tableData:[],tableSetting:[{label:"申请单号",key:"approve_no"},{label:"申请时间",key:"apply_time"},{label:"申请金额",key:"apply_fee",type:"slot",slotName:"applyFee"},{label:"申请人",key:"operator"},{label:"申请来源",key:"apply_source_alias"},{label:"申请内容",key:"apply_content",showTooltip:!0},{label:"申请备注",key:"apply_remark",showTooltip:!0},{label:"操作",key:"operation",type:"slot",slotName:"operation",width:"200"}],currentPage:1,pageSize:10,totalCount:0,selectData:{},detailDrawerShow:!1,detailDrawerForm:{approvalInfo:{applicant:"",applicationSource:"",applicationContent:"",amountApplied:""},documentInfo:[],applicationDocuments:[],collectionInformation:{supplierId:"",collectingSupplier:"",beneficiaryAccountName:"",receivablesAccount:"",receivingBank:""},approvalStatus:[],authorizedAmount:"",remark:""},isNeedToRequire:!1,supplierList:[],canNotEdit:!1,approveMethod:"",showImagePreview:!1,previewList:[]}},computed:{computedFee:function(){return function(t){return"￥"+Object(n["i"])(t,100)}},computedShow:function(){var t=this,e=this.supplierList.filter((function(e){return e.id.toString()===t.detailDrawerForm.collectionInformation.supplierId}));return!!e.length&&e[0].account_receivable!==this.detailDrawerForm.collectionInformation.receivablesAccount},computedFileType:function(){return function(t){var e=["jpeg","jpg","png","tiff","JPEG","PNG","BMP","TIFF","HEIF","JPG"],a=t.split(".")[1];return!!e.includes(a)}}},created:function(){this.getDataList(),this.getSupplierList()},methods:{searchHandle:Object(n["d"])((function(t){t&&"search"===t&&(this.currentPage=1,this.getDataList())}),300),getDataList:function(){var t=this;this.isLoading=!0;var e=Object(n["w"])(this.searchFormSetting,this.currentPage,this.pageSize);e.date_type="apply",this.$apis.apiBackgroundFundSupervisionFinanceApprovePendingListPost(e).then((function(e){t.isLoading=!1,0===e.code?(t.tableData=Object(n["f"])(e.data.results||[]),t.totalCount=e.data.count):t.$message.error(e.msg)}))},handleSizeChange:function(t){this.pageSize=t,this.getDataList()},handleCurrentChange:function(t){this.currentPage=t,this.getDataList()},gotoExport:function(){var t=Object(n["w"])(this.searchFormSetting,this.currentPage,this.totalCount);t.approval_status="pending";var e={url:"apiBackgroundApproveApproveFundListExportPost",params:t};this.exportHandle(e)},getDetail:function(t,e){this.selectData=t,this.approveMethod=t.approve_method,this.detailDrawerForm.approvalInfo.applicant=t.operator,this.detailDrawerForm.approvalInfo.applicationSource=t.apply_source_alias,this.detailDrawerForm.approvalInfo.applicationContent=t.apply_content,this.detailDrawerForm.approvalInfo.amountApplied=t.apply_fee,this.detailDrawerForm.documentInfo=[],this.detailDrawerForm.applicationDocuments=Object(n["f"])(t.image_json||[]),this.detailDrawerForm.collectionInformation.supplierId=t.supplier_manage_id,this.detailDrawerForm.collectionInformation.collectingSupplier=t.supplier_manage_name,this.detailDrawerForm.collectionInformation.beneficiaryAccountName=t.account_person,this.detailDrawerForm.collectionInformation.receivablesAccount=t.account_number,this.detailDrawerForm.collectionInformation.receivingBank=t.account_bank,this.detailDrawerForm.approvalStatus=[{icon:"el-icon-check",color:"#14ce84",status_alias:"提交申请",status:"pending",data:[{icon:"el-icon-success",color:"#14ce84",status_alias:"提交申请",status:"pending",account_id:"",timestamp:t.apply_time,operator:"".concat(t.operator)}]}],this.detailDrawerForm.authorizedAmount=t.apply_fee,this.getDetailRemark(),this.getApprovalProcess(),this.canNotEdit="detail"===e,this.detailDrawerShow=!0},getSupplierList:function(){var t=this;this.$apis.apiBackgroundFundSupervisionAppropriationSupplierManageListPost().then((function(e){0===e.code?t.supplierList=Object(n["f"])(e.data):t.$message.error(e.msg)}))},getDetailRemark:function(){var t=this;return x(_().mark((function e(){var a,r,o,i;return _().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(n["Z"])(t.$apis.apiBackgroundFundSupervisionFinanceApproveGetApproveRulePost());case 2:if(a=e.sent,r=g(a,2),o=r[0],i=r[1],!o){e.next=9;break}return t.$message.error(o.message),e.abrupt("return");case 9:0===i.code?t.isNeedToRequire=i.data:t.$message.error(i.msg);case 10:case"end":return e.stop()}}),e)})))()},getApprovalProcess:function(){var t=this;return x(_().mark((function e(){var a,r,o,i,s,c,l,p,u,d;return _().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(n["Z"])(t.$apis.apiBackgroundFundSupervisionFinanceApproveApproveAccountsPost({id:t.selectData.appropriation_id}));case 2:if(a=e.sent,r=g(a,2),o=r[0],i=r[1],!o){e.next=9;break}return t.$message.error(o.message),e.abrupt("return");case 9:if(0!==i.code){e.next=27;break}s=i.data,c=[],e.t0=t.approveMethod,e.next="one_by_one_approve"===e.t0?15:"and_approve"===e.t0?17:"or_approve"===e.t0?20:23;break;case 15:return s.forEach((function(e){var a={icon:"el-icon-check",color:"#ff9b45",status_alias:"待审批",status:"pending",data:[]},r=[];if(e.approve_account_info&&e.approve_account_info.length){e.approve_account_info.forEach((function(e){var o="PENDING"===e.approve_status||"AGREE"===e.approve_status,n={icon:o?"el-icon-success":"el-icon-error",color:t.switchColor(e.approve_status),status_alias:e.approve_status_alias,status:e.approve_status,account_id:e.account_id,timestamp:e.approve_time,operator:"".concat(e.account_name),reason:e.approve_reason};r.push(e.approve_status),a.data.push(n)}));var o=r.some((function(t){return"AGREE"===t})),n=r.some((function(t){return"REJECT"===t}));a.icon=o?"el-icon-check":n?"el-icon-close":"el-icon-more",a.color=o?"#14ce84":n?"#fd594e":"#ff9b45",a.status_alias=o?"审批通过":n?"拒绝审批":"待审批",a.status=o?"AGREE":n?"REJECT":"PENDING"}c.push(a)})),e.abrupt("break",23);case 17:return l={icon:"agree"===s[0].approve_status?"el-icon-check":"pending"===s[0].approve_status?"el-icon-more":"el-icon-close",color:t.switchColor(s[0].approve_status),status_alias:s[0].approve_status_alias,status:s[0].approve_status,data:[]},s[0].approve_account_info&&s[0].approve_account_info.length&&(s[0].approve_account_info.forEach((function(e){if(e.length){var a=[];e.forEach((function(e){var r="PENDING"===e.approve_status||"AGREE"===e.approve_status,o={icon:r?"el-icon-success":"el-icon-error",color:t.switchColor(e.approve_status),status_alias:e.approve_status_alias,status:e.approve_status,account_id:e.account_id,timestamp:e.approve_time,operator:"".concat(e.account_name),reason:e.approve_reason};a.push(o)})),l.data.push(a)}})),c.push(l)),e.abrupt("break",23);case 20:return p={icon:"agree"===s[0].approve_status?"el-icon-check":"pending"===s[0].approve_status?"el-icon-more":"el-icon-close",color:t.switchColor(s[0].approve_status),status_alias:s[0].approve_status_alias,status:s[0].approve_status,data:[]},s[0].approve_account_info&&s[0].approve_account_info.length&&(s[0].approve_account_info.forEach((function(e){e.length&&e.forEach((function(e){var a="PENDING"===e.approve_status||"AGREE"===e.approve_status,r={icon:a?"el-icon-success":"el-icon-error",color:t.switchColor(e.approve_status),status_alias:e.approve_status_alias,status:e.approve_status,account_id:e.account_id,timestamp:e.approve_time,operator:"".concat(e.account_name),reason:e.approve_reason};p.data.push(r)}))})),c.push(p)),e.abrupt("break",23);case 23:t.addRejectStatus(s,c),"and_approve"!==t.approveMethod?(u=t.detailDrawerForm.approvalStatus).push.apply(u,c):(d=Object(n["f"])(t.detailDrawerForm.approvalStatus[0]),d.data=[[d.data[0]]],t.detailDrawerForm.approvalStatus=[d].concat(c)),e.next=28;break;case 27:t.$message.error(i.msg);case 28:case"end":return e.stop()}}),e)})))()},getZJApprovalProcess:function(t){var e=this;return x(_().mark((function a(){var r,o,i,s,c,l,p,u,d;return _().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,Object(n["Z"])(e.$apis.apiBackgroundFundSupervisionFinanceApproveZjApproveAccountsPost({id:e.selectData.id}));case 2:if(r=a.sent,o=g(r,2),i=o[0],s=o[1],!i){a.next=9;break}return e.$message.error(i.message),a.abrupt("return");case 9:if(0!==s.code){a.next=31;break}c=s.data,l=[],a.t0=e.zjApproveMethod,a.next="one_by_one_approve"===a.t0?15:"and_approve"===a.t0?22:"or_approve"===a.t0?25:28;break;case 15:if(c.forEach((function(t){var a={icon:"el-icon-check",color:"#ff9b45",status_alias:"待审批",status:"pending",data:[]},r=[];if(t.approve_account_info&&t.approve_account_info.length){t.approve_account_info.forEach((function(t){var o="PENDING"===t.approve_status||"AGREE"===t.approve_status,n={icon:o?"el-icon-success":"el-icon-error",color:e.switchColor(t.approve_status),status_alias:t.approve_status_alias,status:t.approve_status,account_id:t.account_id,timestamp:t.approve_time,operator:"".concat(t.account_name),reason:t.approve_reason};r.push(t.approve_status),a.data.push(n)}));var o=r.some((function(t){return"AGREE"===t})),n=r.some((function(t){return"REJECT"===t}));a.icon=o?"el-icon-check":n?"el-icon-close":"el-icon-more",a.color=o?"#14ce84":n?"#fd594e":"#ff9b45",a.status_alias=o?"审批通过":n?"拒绝审批":"待审批",a.status=o?"AGREE":n?"REJECT":"PENDING"}l.push(a)})),!c[0].approve_platform||"zj"!==c[0].approve_platform){a.next=21;break}return p={icon:"el-icon-check",color:"#14ce84",status_alias:"待资金监管平台审批",status:"pending",data:[]},l.push(p),a.next=21,e.getZJApprovalProcess(l);case 21:return a.abrupt("break",28);case 22:return u={icon:"agree"===c[0].approve_status?"el-icon-check":"pending"===c[0].approve_status?"el-icon-more":"el-icon-close",color:e.switchColor(c[0].approve_status),status_alias:c[0].approve_status_alias,status:c[0].approve_status,data:[]},c[0].approve_account_info&&c[0].approve_account_info.length&&(c[0].approve_account_info.forEach((function(t){if(t.length){var a=[];t.forEach((function(t){var r="PENDING"===t.approve_status||"AGREE"===t.approve_status,o={icon:r?"el-icon-success":"el-icon-error",color:e.switchColor(t.approve_status),status_alias:t.approve_status_alias,status:t.approve_status,account_id:t.account_id,timestamp:t.approve_time,operator:"".concat(t.account_name),reason:t.approve_reason};a.push(o)})),u.data.push(a)}})),l.push(u)),a.abrupt("break",28);case 25:return d={icon:"agree"===c[0].approve_status?"el-icon-check":"pending"===c[0].approve_status?"el-icon-more":"el-icon-close",color:e.switchColor(c[0].approve_status),status_alias:c[0].approve_status_alias,status:c[0].approve_status,data:[]},c[0].approve_account_info&&c[0].approve_account_info.length&&(c[0].approve_account_info.forEach((function(t){t.length&&t.forEach((function(t){var a="PENDING"===t.approve_status||"AGREE"===t.approve_status,r={icon:a?"el-icon-success":"el-icon-error",color:e.switchColor(t.approve_status),status_alias:t.approve_status_alias,status:t.approve_status,account_id:t.account_id,timestamp:t.approve_time,operator:"".concat(t.account_name),reason:t.approve_reason};d.data.push(r)}))})),l.push(d)),a.abrupt("break",28);case 28:l.forEach((function(e){t.push(e)})),a.next=32;break;case 31:e.$message.error(s.msg);case 32:case"end":return a.stop()}}),a)})))()},addRejectStatus:function(t,e){var a=this;if("REVOKE"===t.approval_status){var r={icon:"el-icon-error",color:"#909399",status_alias:"撤销申请",status:"REVOKE",timestamp:t.create_time,operator:"".concat(t.creator," (").concat(t.username,")")},o={icon:"el-icon-close",color:"#909399",status_alias:"撤销申请",status:"REVOKE",data:[]},i=[];switch(t.approve_record&&t.approve_record.record&&t.approve_record.record.length&&(i=Object(n["f"])(t.approve_record.record)),t.approve_method){case"one_by_one_approve":e.pop(),e.forEach((function(t){var e=[];t.data.forEach((function(t){var r=i.filter((function(e){return e.account_id===t.account_id}));if(r.length){var o="PENDING"===r[0].status||"AGREE"===r[0].status;t.icon=o?"el-icon-success":"el-icon-error",t.color=a.switchColor(r[0].status),t.status_alias=r[0].content,t.status=r[0].status,t.timestamp=r[0].time}else t.icon="",t.timestamp="";e.push(t.status)}));var r=e.some((function(t){return"REJECT"===t}));t.icon=r?"el-icon-close":"el-icon-check",t.color=r?a.switchColor(""):a.switchColor("AGREE"),t.status_alias=r?"":"审批通过",t.status=r?"":"AGREE"})),o.data=[f({},r)],e.push(o);break;case"and_approve":e[0].data.forEach((function(t){t.forEach((function(t){var e=i.filter((function(e){return e.account_id===t.account_id}));e.length?(t.icon="AGREE"===e[0].status?"el-icon-success":"el-icon-error",t.color=a.switchColor(e[0].status),t.status_alias=e[0].content,t.status=e[0].status,t.timestamp=e[0].time):(t.icon="",t.timestamp="")}))})),e[0].icon="el-icon-more",e[0].color=this.switchColor("PENDING"),e[0].status_alias="待审批",e[0].status="PENDING",o.data=[[f({},r)]],e.push(o);break;case"or_approve":e.pop(),o.data=[f({},r)],e.push(o);break}}},switchColor:function(t){var e="";switch(t){case"PENDING":e="#ff9b45";break;case"AGREE":e="#14ce84";break;case"REJECT":e="#fd594e";break;case"pending":e="#ff9b45";break;case"agree":e="#14ce84";break;case"reject":e="#fd594e";break;default:e="#909399"}return e},cancelHandle:function(){this.$refs.detailDrawerFormRef&&this.$refs.detailDrawerFormRef.resetFields(),this.detailDrawerShow=!1},agreeHandle:function(){var t=this;this.getDetailRemark(),this.$nextTick((function(){t.$refs.detailDrawerFormRef.validate((function(e){if(e){var a={id:t.selectData.id,reason:t.detailDrawerForm.remark?t.detailDrawerForm.remark:void 0};t.$apis.apiBackgroundFundSupervisionFinanceApproveAgreeApprovePost(a).then((function(e){0===e.code?(t.$message.success("操作成功"),t.$refs.detailDrawerFormRef.resetFields(),t.detailDrawerShow=!1,t.getDataList()):t.$message.error(e.msg)}))}else t.$message.error("请确认表单信息填写是否正确")}))}))},rejectHandle:function(){var t=this;this.isNeedToRequire=!0,this.$nextTick((function(){t.$refs.detailDrawerFormRef.validate((function(e){if(e){var a={id:t.selectData.id,reason:t.detailDrawerForm.remark};t.$apis.apiBackgroundFundSupervisionFinanceApproveRejectApprovePost(a).then((function(e){0===e.code?(t.$message.success("操作成功"),t.$refs.detailDrawerFormRef.resetFields(),t.detailDrawerShow=!1,t.getDataList()):t.$message.error(e.msg)}))}else t.$message.error("请确认表单信息填写是否正确")}))}))},handleClick:function(t){this.previewList=[t],document.body.style.overflow="hidden",this.showImagePreview=!0},closePreview:function(){this.previewList=[],this.showImagePreview=!1,document.body.style.overflow="auto"},downloadFile:function(t){this.step=1;var e=t.split("/"),a=e[e.length-1];l.a.saveAs(t,a)}}},S=F,C=(a("de80"),a("2877")),P=Object(C["a"])(S,r,o,!1,null,"87d2e102",null);e["default"]=P.exports},de80:function(t,e,a){"use strict";a("2aa7")}}]);