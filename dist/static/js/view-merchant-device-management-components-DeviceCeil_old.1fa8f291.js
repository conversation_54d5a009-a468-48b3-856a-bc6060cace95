(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-device-management-components-DeviceCeil_old"],{"283b":function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t._self._c;return e("div",[e("div",{staticClass:"table-wrapper",staticStyle:{"margin-bottom":"20px"}},[e("div",{staticClass:"table-header",staticStyle:{display:"flex","justify-content":"space-between"}},[t._m(0),e("div",{staticStyle:{"padding-right":"20px"}},[e("div",[t._v("当前设备："+t._s(t.deviceName))])])]),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],key:t.content<PERSON><PERSON>,staticClass:"content-wrapper"},[e("div",[t._v("餐格大小： "),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.mulCeilOperation("big")}}},[t._v("大餐格")]),e("el-button",{attrs:{type:"warning"},on:{click:function(e){return t.mulCeilOperation("little")}}},[t._v("小餐格")])],1),e("div",{staticClass:"title"},[t._v("主柜：3，5号柜为操作屏")]),e("div",{staticClass:"ceil-list"},t._l(t.mainCupboard,(function(n,r){return e("div",{key:r,class:["ceil-item",t.ceilColor(n.type)],on:{click:function(e){return t.selectCeil("main",r)}}},[t._v(" "+t._s(n.num)+" "),e("div",{class:[n.isSelect?"select-mask":"un-select"]},[t._v("已选中: "+t._s(n.num))]),n.lock?e("i",{staticClass:"ceil-lock el-icon-lock"}):t._e()])})),0),e("div",{staticClass:"title"},[t._v("副柜：")]),e("div",t._l(t.assistantCupboard,(function(n,r){return e("div",{key:r,staticClass:"ceil-list"},t._l(n,(function(n,i){return e("div",{key:i,class:["ceil-item",t.ceilColor(n.type)],on:{click:function(e){return t.selectCeil("assistant",r,i)}}},[t._v(" "+t._s(n.num)+" "),e("div",{class:[n.isSelect?"select-mask":"un-select"]},[t._v("已选中: "+t._s(n.num))]),n.lock?e("i",{staticClass:"ceil-lock el-icon-lock"}):t._e()])})),0)})),0)])]),e("div",{staticClass:"m-l-30"},[e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.mulCeilOperation("lock")}}},[t._v("锁定餐格")]),e("el-button",{attrs:{type:"warning"},on:{click:function(e){return t.mulCeilOperation("unlock")}}},[t._v("解锁餐格")])],1)])},i=[function(){var t=this,e=t._self._c;return e("div",{staticStyle:{display:"flex","align-items":"center"}},[e("div",{staticClass:"table-title"},[t._v("取餐柜设置")])])}];function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function a(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */a=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(t,e,n){t[e]=n.value},s="function"==typeof Symbol?Symbol:{},c=s.iterator||"@@iterator",u=s.asyncIterator||"@@asyncIterator",l=s.toStringTag||"@@toStringTag";function f(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,n){return t[e]=n}}function p(t,e,n,r){var o=e&&e.prototype instanceof b?e:b,a=Object.create(o.prototype),s=new P(r||[]);return i(a,"_invoke",{value:E(t,n,s)}),a}function h(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=p;var d="suspendedStart",v="suspendedYield",m="executing",y="completed",g={};function b(){}function w(){}function L(){}var C={};f(C,c,(function(){return this}));var _=Object.getPrototypeOf,k=_&&_(_(T([])));k&&k!==n&&r.call(k,c)&&(C=k);var x=L.prototype=b.prototype=Object.create(C);function O(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function n(i,a,s,c){var u=h(t[i],t,a);if("throw"!==u.type){var l=u.arg,f=l.value;return f&&"object"==o(f)&&r.call(f,"__await")?e.resolve(f.__await).then((function(t){n("next",t,s,c)}),(function(t){n("throw",t,s,c)})):e.resolve(f).then((function(t){l.value=t,s(l)}),(function(t){return n("throw",t,s,c)}))}c(u.arg)}var a;i(this,"_invoke",{value:function(t,r){function i(){return new e((function(e,i){n(t,r,e,i)}))}return a=a?a.then(i,i):i()}})}function E(e,n,r){var i=d;return function(o,a){if(i===m)throw Error("Generator is already running");if(i===y){if("throw"===o)throw a;return{value:t,done:!0}}for(r.method=o,r.arg=a;;){var s=r.delegate;if(s){var c=N(s,r);if(c){if(c===g)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===d)throw i=y,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=m;var u=h(e,n,r);if("normal"===u.type){if(i=r.done?y:v,u.arg===g)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(i=y,r.method="throw",r.arg=u.arg)}}}function N(e,n){var r=n.method,i=e.iterator[r];if(i===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,N(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var o=h(i,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,g;var a=o.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,g):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,g)}function $(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function P(t){this.tryEntries=[{tryLoc:"root"}],t.forEach($,this),this.reset(!0)}function T(e){if(e||""===e){var n=e[c];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,a=function n(){for(;++i<e.length;)if(r.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return a.next=a}}throw new TypeError(o(e)+" is not iterable")}return w.prototype=L,i(x,"constructor",{value:L,configurable:!0}),i(L,"constructor",{value:w,configurable:!0}),w.displayName=f(L,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,L):(t.__proto__=L,f(t,l,"GeneratorFunction")),t.prototype=Object.create(x),t},e.awrap=function(t){return{__await:t}},O(S.prototype),f(S.prototype,u,(function(){return this})),e.AsyncIterator=S,e.async=function(t,n,r,i,o){void 0===o&&(o=Promise);var a=new S(p(t,n,r,i),o);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},O(x),f(x,l,"Generator"),f(x,c,(function(){return this})),f(x,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=T,P.prototype={constructor:P,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function i(r,i){return s.type="throw",s.arg=e,n.next=r,i&&(n.method="next",n.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(c&&u){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),j(n),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;j(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:T(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),g}},e}function s(t,e,n,r,i,o,a){try{var s=t[o](a),c=s.value}catch(t){return void n(t)}s.done?e(c):Promise.resolve(c).then(r,i)}function c(t){return function(){var e=this,n=arguments;return new Promise((function(r,i){var o=t.apply(e,n);function a(t){s(o,r,i,a,c,"next",t)}function c(t){s(o,r,i,a,c,"throw",t)}a(void 0)}))}}var u={name:"DeviceCeil",props:{deviceNo:[String,Number],deviceName:String,ceilList:Array},data:function(){return{isLoading:!1,mainCupboard:[],assistantCupboard:[],lockList:[],selectList:[],contentKey:0}},created:function(){this.initLoad()},methods:{initLoad:function(){var t=this;return c(a().mark((function e(){return a().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.cupboardLock();case 2:t.getCupboardCeil();case 3:case"end":return e.stop()}}),e)})))()},cupboardLock:function(){var t=this;return c(a().mark((function e(){var n;return a().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.apiBackgroundDeviceDeviceGetCupboardCeilForbiddenListPost({device_no:Number(t.deviceNo)});case 2:n=e.sent,0===n.code?t.lockList=n.data.ceil_forbidden_list:t.$message.error(n.msg);case 4:case"end":return e.stop()}}),e)})))()},getCupboardCeil:function(){var t=this;return c(a().mark((function e(){var n,r,i,o,s;return a().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.$apis.apiBackgroundDeviceDeviceGetCupboardCeilTypePost({device_no:t.deviceNo});case 3:if(n=e.sent,t.isLoading=!1,0===n.code){for(t.mainCupboard=[],t.assistantCupboard=[],r=0,i=1,o=0;o<t.ceilList.length;o++)for(o&&(t.assistantCupboard[o-1]=[]),r+=Number(t.ceilList[o]),i;i<=r;i++)s=void 0,n.data.little_ceil_list&&-1!==n.data.little_ceil_list.indexOf(i)&&(s="little"),n.data.big_ceil_list&&-1!==n.data.big_ceil_list.indexOf(i)&&(s="big"),3!==i&&5!==i||(s="screen"),0===o?t.mainCupboard.push({num:i,type:s,isSelect:!1}):t.assistantCupboard[o-1].push({num:i,type:s,isSelect:!1});t.mainCupboard.map((function(e){-1===t.lockList.indexOf(e.num)?e.lock=!1:e.lock=!0})),t.assistantCupboard.map((function(e){e.map((function(e){-1===t.lockList.indexOf(e.num)?e.lock=!1:e.lock=!0}))}))}else t.$message.error(n.msg);case 6:case"end":return e.stop()}}),e)})))()},ceilColor:function(t){return"little"===t?"little":"big"===t?"big":"screen"===t?"screen":void 0},selectCeil:function(t,e,n){if("main"===t){if("screen"===this.mainCupboard[e].type)return this.$message.error("3，5号柜为操作屏,不可进行设置");this.mainCupboard[e].isSelect=!this.mainCupboard[e].isSelect,this.mainCupboard[e].isSelect?this.selectList.push(this.mainCupboard[e].num):this.selectList.splice(this.selectList.indexOf(this.mainCupboard[e].num),1)}else"assistant"===t&&(this.assistantCupboard[e][n].isSelect=!this.assistantCupboard[e][n].isSelect,this.assistantCupboard[e][n].isSelect?this.selectList.push(this.assistantCupboard[e][n].num):this.selectList.splice(this.selectList.indexOf(this.assistantCupboard[e][n].num),1));this.contentKey=Math.random()},mulCeilOperation:function(t){var e=this;if(!this.selectList.length)return this.$message.error("请先选择餐格！（点击餐格进行选择）");var n="",r=[],i=[],o=[],s=[],u=this.mainCupboard.filter((function(t){return-1!==e.selectList.indexOf(t.num)}));switch(this.assistantCupboard.map((function(t){u=u.concat(t.filter((function(t){return-1!==e.selectList.indexOf(t.num)})))})),t){case"big":n="确定将选中餐格设置为大餐格吗？",r=this.selectList;break;case"little":n="确定将选中餐格设置为小餐格吗？",i=this.selectList;break;case"lock":if(-1===u.findIndex((function(t){return!1===t.lock})))return this.$message.error("餐格已是锁定/解锁状态");n="被锁餐格将变为不可用，确定锁定餐格？",o=this.selectList;break;case"unlock":if(-1===u.findIndex((function(t){return!0===t.lock})))return this.$message.error("餐格已是锁定/解锁状态");n="确定解锁餐格？",s=this.selectList;break}this.mainCupboard.map((function(t){-1===e.selectList.indexOf(t.num)&&("big"===t.type?r.push(t.num):"little"===t.type&&i.push(t.num),t.lock?o.push(t.num):s.push(t.num))})),this.assistantCupboard.map((function(t){t.map((function(t){-1===e.selectList.indexOf(t.num)&&("big"===t.type?r.push(t.num):"little"===t.type&&i.push(t.num),t.lock?o.push(t.num):s.push(t.num))}))})),this.$confirm("".concat(n),"提示",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var n=c(a().mark((function n(c,u,l){var f,p;return a().wrap((function(n){while(1)switch(n.prev=n.next){case 0:"confirm"===c?(u.confirmButtonLoading=!0,"big"===t||"little"===t?(f={device_no:e.deviceNo,big_ceil_list:r,little_ceil_list:i},e.setCupboardCeilType(f)):(p={device_no:e.deviceNo,ceil_forbidden_list:o,ceil_allow_list:s},e.setCupboardCeilLock(p)),l(),u.confirmButtonLoading=!1):u.confirmButtonLoading||l();case 1:case"end":return n.stop()}}),n)})));function u(t,e,r){return n.apply(this,arguments)}return u}()}).then((function(t){})).catch((function(t){}))},setCupboardCeilType:function(t){var e=this;return c(a().mark((function n(){var r;return a().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!e.isLoading){n.next=2;break}return n.abrupt("return");case 2:return e.isLoading=!0,n.next=5,e.$apis.apiBackgroundDeviceDeviceSetCupboardCeilTypePost(t);case 5:r=n.sent,e.isLoading=!1,0===r.code?(e.$message.success("设置成功"),e.initLoad(),e.selectList=[]):e.$message.error(r.msg);case 8:case"end":return n.stop()}}),n)})))()},setCupboardCeilLock:function(t){var e=this;return c(a().mark((function n(){var r;return a().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!e.isLoading){n.next=2;break}return n.abrupt("return");case 2:return e.isLoading=!0,n.next=5,e.$apis.apiBackgroundDeviceDeviceSetCupboardCeilForbiddenPost(t);case 5:r=n.sent,e.isLoading=!1,0===r.code?(e.$message.success("设置成功"),e.initLoad(),e.selectList=[]):e.$message.error(r.msg);case 8:case"end":return n.stop()}}),n)})))()}}},l=u,f=(n("3a21"),n("2877")),p=Object(f["a"])(l,r,i,!1,null,"fc0ee332",null);e["default"]=p.exports},"3a21":function(t,e,n){"use strict";n("ad70")},ad70:function(t,e,n){}}]);