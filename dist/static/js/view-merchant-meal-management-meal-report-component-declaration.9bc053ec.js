(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-meal-report-component-declaration"],{c741:function(e,t,n){"use strict";n.r(t),n.d(t,"RECENTSEVENTDAY",(function(){return a})),n.d(t,"PICKEROPTIONS",(function(){return o})),n.d(t,"MEALDETAILS_TABLE_CLOUMN",(function(){return p})),n.d(t,"MEALDETAILS_TABLE_DATE",(function(){return i})),n.d(t,"HEADCOUNT_TABLE_CLOUMN",(function(){return _}));var r=n("5a0c"),a=[r().subtract(7,"day").format("YYYY-MM-DD"),r().format("YYYY-MM-DD")],o={shortcuts:[{text:"最近一周",onClick:function(e){var t=new Date,n=new Date;n.setTime(n.getTime()-6048e5),e.$emit("pick",[n,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,n=new Date;n.setTime(n.getTime()-2592e6),e.$emit("pick",[n,t])}},{text:"最近三个月",onClick:function(e){var t=new Date,n=new Date;n.setTime(n.getTime()-7776e6),e.$emit("pick",[n,t])}}]},p=[{label:"总单号",prop:"unified_trade_no",width:"170"},{label:"订单号",prop:"trade_no",width:"190"},{label:"分组",prop:"payer_group_name"},{label:"姓名",prop:"name",width:"90"},{label:"人员编号",prop:"person_no",width:"90"},{label:"部门",prop:"payer_department_group_name"},{label:"创建时间",prop:"create_time"},{label:"报餐时间",prop:"report_date"},{label:"用餐时间",prop:"dining_time",width:"95"},{label:"报餐餐段",prop:"meal_type_alias",width:"80"},{label:"报餐方式",prop:"report_meal_type_alias",width:"80"},{label:"扣费方式",prop:"consume_type_alias",width:"80"},{label:"份数",prop:"count",width:"50"},{label:"订单金额",prop:"pay_fee",width:"80"},{label:"取餐状态",prop:"take_meal_status_alias",width:"80"},{label:"报餐消费点",prop:"consumption_name",width:"100"}],i=[{total_number:"142",order_number:"111",grouping:"",username:"",user_number:"",department:"",change_time:"",dinner_time:"",meal_type:"",deduction_type:"",total_numcountber:"",order_money:"",get_meal_state:"",consumption:""},{total_number:"142",order_number:"111",grouping:"",username:"",user_number:"",department:"",change_time:"",dinner_time:"",meal_type:"",deduction_type:"",total_numcountber:"",order_money:"",get_meal_state:"",consumption:""},{total_number:"22",order_number:"111",grouping:"",username:"",user_number:"",department:"",change_time:"",dinner_time:"",meal_type:"",deduction_type:"",total_numcountber:"",order_money:"",get_meal_state:"",consumption:""}],_=[{label:"时间",prop:"report_date"},{label:"人员编号",prop:"person_no"},{label:"姓名",prop:"name"},{label:"分组",prop:"card_user_groups"},{label:"部门",prop:"department_group_name"}]}}]);