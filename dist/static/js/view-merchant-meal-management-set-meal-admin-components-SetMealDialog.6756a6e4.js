(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-set-meal-admin-components-SetMealDialog"],{"4b35":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",[e("div",[t._v(" 分类： "),e("el-select",{attrs:{size:"small"},model:{value:t.category_id,callback:function(e){t.category_id=e},expression:"category_id"}},t._l(t.foodCategoryList,(function(t){return e("el-option",{key:t.id,attrs:{value:t.id,label:t.name}})})),1),e("el-input",{staticStyle:{width:"200px",margin:"0 20px"},attrs:{size:"small",placeholder:"请输入"},on:{input:t.search<PERSON>and<PERSON>},model:{value:t.keyword,callback:function(e){t.keyword=e},expression:"keyword"}}),e("el-button",{attrs:{size:"small",type:"primary"},on:{click:t.searchHandler}},[t._v("搜索")])],1),e("div",{staticStyle:{margin:"20px 0"}},[e("el-checkbox",{on:{change:t.handleCheckChange},model:{value:t.selectAll,callback:function(e){t.selectAll=e},expression:"selectAll"}},[t._v("全选")])],1),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"content-body"},t._l(t.foodsListData,(function(r){return e("div",{key:r.id,staticClass:"item"},[e("el-checkbox",{attrs:{value:r.selected,disabled:r.disabled},on:{change:function(e){return t.handleCheckboxChange(r)}}},[t._v(" "+t._s(r.name)+" ")])],1)})),0)])},o=[],i=r("ed08");function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},c=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function d(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{d({},"")}catch(t){d=function(t,e,r){return t[e]=r}}function h(t,e,r,n){var i=e&&e.prototype instanceof w?e:w,a=Object.create(i.prototype),s=new D(n||[]);return o(a,"_invoke",{value:S(t,r,s)}),a}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var p="suspendedStart",y="suspendedYield",v="executing",g="completed",m={};function w(){}function b(){}function L(){}var x={};d(x,c,(function(){return this}));var F=Object.getPrototypeOf,_=F&&F(F(P([])));_&&_!==r&&n.call(_,c)&&(x=_);var k=L.prototype=w.prototype=Object.create(x);function E(t){["next","throw","return"].forEach((function(e){d(t,e,(function(t){return this._invoke(e,t)}))}))}function O(t,e){function r(o,i,s,c){var l=f(t[o],t,i);if("throw"!==l.type){var u=l.arg,d=u.value;return d&&"object"==a(d)&&n.call(d,"__await")?e.resolve(d.__await).then((function(t){r("next",t,s,c)}),(function(t){r("throw",t,s,c)})):e.resolve(d).then((function(t){u.value=t,s(u)}),(function(t){return r("throw",t,s,c)}))}c(l.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function S(e,r,n){var o=p;return function(i,a){if(o===v)throw Error("Generator is already running");if(o===g){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var s=n.delegate;if(s){var c=I(s,n);if(c){if(c===m)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var l=f(e,r,n);if("normal"===l.type){if(o=n.done?g:y,l.arg===m)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(o=g,n.method="throw",n.arg=l.arg)}}}function I(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,I(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,m;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function C(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function D(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(C,this),this.reset(!0)}function P(e){if(e||""===e){var r=e[c];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(a(e)+" is not iterable")}return b.prototype=L,o(k,"constructor",{value:L,configurable:!0}),o(L,"constructor",{value:b,configurable:!0}),b.displayName=d(L,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,L):(t.__proto__=L,d(t,u,"GeneratorFunction")),t.prototype=Object.create(k),t},e.awrap=function(t){return{__await:t}},E(O.prototype),d(O.prototype,l,(function(){return this})),e.AsyncIterator=O,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new O(h(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},E(k),d(k,u,"Generator"),d(k,c,(function(){return this})),d(k,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=P,D.prototype={constructor:D,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return s.type="throw",s.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),l=n.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),j(r),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;j(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:P(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),m}},e}function c(t,e,r,n,o,i,a){try{var s=t[i](a),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,o)}function l(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){c(i,n,o,a,s,"next",t)}function s(t){c(i,n,o,a,s,"throw",t)}a(void 0)}))}}var u={name:"EditMealFoods",props:{mealType:{type:String},alreadyFoodData:{type:Array}},data:function(){return{isLoading:!1,category_id:"",keyword:"",selectAll:!1,allValue:0,foodCategoryList:[],foodsListData:[],selectFoodIds:[],selectFoodList:[],disabledFoodIds:[]}},mounted:function(){this.initSelectData(),this.requestFoodCate(),this.requestMenuWeeklyFoodList()},methods:{initSelectData:function(){var t=this;this.selectFoodIds=[],this.selectFoodList=[],this.alreadyFoodData.length&&this.alreadyFoodData.map((function(e){t.selectFoodIds.push(e.food_id),t.disabledFoodIds.push(e.food_id)}))},searchHandler:Object(i["d"])((function(){this.requestMenuWeeklyFoodList()}),300),getPostData:function(){return this.selectFoodList},handleCheckChange:function(t){var e=this;this.foodsListData.forEach((function(r){r.disabled||(r.selected=t,t?e.selectFoodIds.includes(r.id)||(e.selectFoodIds.push(r.id),e.selectFoodList.push(r)):(e.selectFoodIds.splice(e.selectFoodIds.indexOf(r.id),1),e.selectFoodList.splice(e.selectFoodList.indexOf(r.id),1)))}))},handleCheckboxChange:function(t){var e=this.foodsListData.find((function(e){return e.id===t.id}));e.selected=!e.selected,e.selected&&!this.selectFoodIds.includes(t.id)?(this.selectFoodIds.push(t.id),this.selectFoodList.push(t)):(this.selectFoodIds.splice(this.selectFoodIds.indexOf(t.id),1),this.selectFoodList.splice(this.selectFoodList.indexOf(t.id),1))},requestMenuWeeklyFoodList:function(){var t=this;return l(s().mark((function e(){var r,n;return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,r={},t.category_id&&(r.category_id=t.category_id),t.keyword&&(r.name=t.keyword),e.next=6,t.$apis.apiBackgroundFoodSetMealSetMealFoodListPost(r);case 6:n=e.sent,t.isLoading=!1,0===n.code?(t.selectAll=!1,t.foodsListData=n.data.map((function(e){return e.selected=t.selectFoodIds.includes(e.id),e.disabled=t.disabledFoodIds.includes(e.id),e.status=1,e.spec_id=e.spec_list[0].id,e.food_price=e.spec_list[0].food_price,e}))):t.$message.error(n.msg);case 9:case"end":return e.stop()}}),e)})))()},requestFoodCate:function(){var t=this;return l(s().mark((function e(){var r,n;return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.apiBackgroundFoodMenuWeeklyCategoryListPost();case 2:r=e.sent,0===r.code?(n=r.data.map((function(t){return{name:t.name,id:t.id}})),n.unshift({name:"全部",id:""}),t.foodCategoryList=n):t.$message.error(r.msg);case 4:case"end":return e.stop()}}),e)})))()}}},d=u,h=(r("6d2d"),r("2877")),f=Object(h["a"])(d,n,o,!1,null,null,null);e["default"]=f.exports},"6d2d":function(t,e,r){"use strict";r("82bd")},"82bd":function(t,e,r){}}]);