(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-member-center-AddOrEditMemberLabel"],{2761:function(e,t,r){},"68e8":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"AddOrEditMemberLabel container-wrapper"},[t("div",{staticClass:"table-wrapper"},[t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[e._v(e._s("add"===e.type?"新建":"编辑")+"会员标签")])]),t("div",[t("el-form",{ref:"memberFormRef",attrs:{model:e.memberForm,rules:e.memberFormRules,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"标签名称：",prop:"name"}},[t("el-input",{staticClass:"ps-input w-180",attrs:{maxlength:"15"},model:{value:e.memberForm.name,callback:function(t){e.$set(e.memberForm,"name",t)},expression:"memberForm.name"}})],1),t("el-form-item",{attrs:{label:"标签类型："}},[t("el-radio-group",{model:{value:e.memberForm.labelType,callback:function(t){e.$set(e.memberForm,"labelType",t)},expression:"memberForm.labelType"}},[t("el-radio",{staticClass:"ps-radio",attrs:{label:"auto"}},[e._v("自动")]),t("el-radio",{staticClass:"ps-radio",attrs:{label:"manual"}},[e._v("手动")])],1)],1),t("el-form-item",[t("el-radio-group",{staticClass:"ps-radio-btn",model:{value:e.memberForm.labelDirection,callback:function(t){e.$set(e.memberForm,"labelDirection",t)},expression:"memberForm.labelDirection"}},[t("el-radio-button",{staticClass:"ps-radio",attrs:{label:"positive"}},[e._v("正向标签")]),t("el-radio-button",{staticClass:"ps-radio",attrs:{label:"negative"}},[e._v("负面标签")])],1)],1),"auto"===e.memberForm.labelType?t("div",["positive"===e.memberForm.labelDirection?t("div",[t("el-form-item",{attrs:{prop:"validate"}},[t("el-checkbox",{staticClass:"ps-checkbox",model:{value:e.memberForm.isValidate,callback:function(t){e.$set(e.memberForm,"isValidate",t)},expression:"memberForm.isValidate"}},[e._v("会员有效期大于")]),t("el-input",{staticClass:"ps-input w-180 m-l-5",attrs:{type:"number"},model:{value:e.memberForm.validate,callback:function(t){e.$set(e.memberForm,"validate",t)},expression:"memberForm.validate"}}),t("div",{staticClass:"ps-inline m-l-5"},[e._v("天数")])],1),t("el-form-item",{attrs:{prop:"yearCard"}},[t("el-checkbox",{staticClass:"ps-checkbox",model:{value:e.memberForm.isYearCard,callback:function(t){e.$set(e.memberForm,"isYearCard",t)},expression:"memberForm.isYearCard"}},[e._v("会员年卡购买次数大于")]),t("el-input",{staticClass:"ps-input w-180 m-l-5",attrs:{type:"number"},model:{value:e.memberForm.yearCard,callback:function(t){e.$set(e.memberForm,"yearCard",t)},expression:"memberForm.yearCard"}}),t("div",{staticClass:"ps-inline m-l-5"},[e._v("次")])],1),t("el-form-item",{attrs:{prop:"seasonCard"}},[t("el-checkbox",{staticClass:"ps-checkbox",model:{value:e.memberForm.isSeasonCard,callback:function(t){e.$set(e.memberForm,"isSeasonCard",t)},expression:"memberForm.isSeasonCard"}},[e._v("会员季卡购买次数大于")]),t("el-input",{staticClass:"ps-input w-180 m-l-5",attrs:{type:"number"},model:{value:e.memberForm.seasonCard,callback:function(t){e.$set(e.memberForm,"seasonCard",t)},expression:"memberForm.seasonCard"}}),t("div",{staticClass:"ps-inline m-l-5"},[e._v("次")])],1),t("el-form-item",{attrs:{prop:"monthCard"}},[t("el-checkbox",{staticClass:"ps-checkbox",model:{value:e.memberForm.isMonthCard,callback:function(t){e.$set(e.memberForm,"isMonthCard",t)},expression:"memberForm.isMonthCard"}},[e._v("会员月卡购买次数大于")]),t("el-input",{staticClass:"ps-input w-180 m-l-5",attrs:{type:"number"},model:{value:e.memberForm.monthCard,callback:function(t){e.$set(e.memberForm,"monthCard",t)},expression:"memberForm.monthCard"}}),t("div",{staticClass:"ps-inline m-l-5"},[e._v("次")])],1),t("el-form-item",{attrs:{prop:"enjoyCard"}},[t("el-checkbox",{staticClass:"ps-checkbox",model:{value:e.memberForm.isEnjoyCard,callback:function(t){e.$set(e.memberForm,"isEnjoyCard",t)},expression:"memberForm.isEnjoyCard"}},[e._v("会员畅享卡购买次数大于")]),t("el-input",{staticClass:"ps-input w-180 m-l-5",attrs:{type:"number"},model:{value:e.memberForm.enjoyCard,callback:function(t){e.$set(e.memberForm,"enjoyCard",t)},expression:"memberForm.enjoyCard"}}),t("div",{staticClass:"ps-inline m-l-5"},[e._v("次")])],1)],1):e._e(),"negative"===e.memberForm.labelDirection?t("div",[t("el-form-item",{attrs:{prop:"minValidate"}},[t("el-checkbox",{staticClass:"ps-checkbox",model:{value:e.memberForm.isMinValidate,callback:function(t){e.$set(e.memberForm,"isMinValidate",t)},expression:"memberForm.isMinValidate"}},[e._v("会员有效期小于")]),t("el-input",{staticClass:"ps-input w-180 m-l-5",attrs:{type:"number"},model:{value:e.memberForm.minValidate,callback:function(t){e.$set(e.memberForm,"minValidate",t)},expression:"memberForm.minValidate"}}),t("div",{staticClass:"ps-inline m-l-5"},[e._v("天数")])],1),t("el-form-item",{attrs:{prop:"minYearCard"}},[t("el-checkbox",{staticClass:"ps-checkbox",model:{value:e.memberForm.isMinYearCard,callback:function(t){e.$set(e.memberForm,"isMinYearCard",t)},expression:"memberForm.isMinYearCard"}},[e._v("会员年卡购买次数小于")]),t("el-input",{staticClass:"ps-input w-180 m-l-5",attrs:{type:"number"},model:{value:e.memberForm.minYearCard,callback:function(t){e.$set(e.memberForm,"minYearCard",t)},expression:"memberForm.minYearCard"}}),t("div",{staticClass:"ps-inline m-l-5"},[e._v("次")])],1),t("el-form-item",{attrs:{prop:"minSeasonCard"}},[t("el-checkbox",{staticClass:"ps-checkbox",model:{value:e.memberForm.isMinSeasonCard,callback:function(t){e.$set(e.memberForm,"isMinSeasonCard",t)},expression:"memberForm.isMinSeasonCard"}},[e._v("会员季卡购买次数小于")]),t("el-input",{staticClass:"ps-input w-180 m-l-5",attrs:{type:"number"},model:{value:e.memberForm.minSeasonCard,callback:function(t){e.$set(e.memberForm,"minSeasonCard",t)},expression:"memberForm.minSeasonCard"}}),t("div",{staticClass:"ps-inline m-l-5"},[e._v("次")])],1),t("el-form-item",{attrs:{prop:"minMonthCard"}},[t("el-checkbox",{staticClass:"ps-checkbox",model:{value:e.memberForm.isMinMonthCard,callback:function(t){e.$set(e.memberForm,"isMinMonthCard",t)},expression:"memberForm.isMinMonthCard"}},[e._v("会员月卡购买次数小于")]),t("el-input",{staticClass:"ps-input w-180 m-l-5",attrs:{type:"number"},model:{value:e.memberForm.minMonthCard,callback:function(t){e.$set(e.memberForm,"minMonthCard",t)},expression:"memberForm.minMonthCard"}}),t("div",{staticClass:"ps-inline m-l-5"},[e._v("次")])],1)],1):e._e(),e._e(),t("el-form-item",[t("div",{staticClass:"m-l-20 label-list"},e._l(e.labelNameList,(function(r,a){return t("div",{key:r,staticClass:"label-list-item"},[t("span",{staticClass:"m-r-5"},[e._v("标签"+e._s(r))]),t("i",{staticClass:"el-icon-close del-icon",on:{click:function(t){return e.delLabel(a)}}})])})),0)])],1):e._e(),t("el-form-item",{attrs:{prop:"remark",label:"标签说明"}},[t("el-input",{staticClass:"w-remark",attrs:{type:"textarea",rows:3,placeholder:"请输入内容",maxlength:"50","show-word-limit":""},model:{value:e.memberForm.remark,callback:function(t){e.$set(e.memberForm,"remark",t)},expression:"memberForm.remark"}})],1),t("el-form-item",[t("el-button",{staticClass:"ps-origin-btn w-150",attrs:{size:"small",type:"primary"},on:{click:e.saveSetting}},[e._v("保存")])],1)],1)],1)])])},n=[];function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function o(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */o=function(){return t};var e,t={},r=Object.prototype,a=r.hasOwnProperty,n=Object.defineProperty||function(e,t,r){e[t]=r.value},s="function"==typeof Symbol?Symbol:{},m=s.iterator||"@@iterator",l=s.asyncIterator||"@@asyncIterator",c=s.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function d(e,t,r,a){var i=t&&t.prototype instanceof y?t:y,o=Object.create(i.prototype),s=new j(a||[]);return n(o,"_invoke",{value:L(e,r,s)}),o}function h(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var b="suspendedStart",p="suspendedYield",f="executing",g="completed",v={};function y(){}function _(){}function C(){}var F={};u(F,m,(function(){return this}));var w=Object.getPrototypeOf,k=w&&w(w(Y([])));k&&k!==r&&a.call(k,m)&&(F=k);var x=C.prototype=y.prototype=Object.create(F);function D(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function M(e,t){function r(n,o,s,m){var l=h(e[n],e,o);if("throw"!==l.type){var c=l.arg,u=c.value;return u&&"object"==i(u)&&a.call(u,"__await")?t.resolve(u.__await).then((function(e){r("next",e,s,m)}),(function(e){r("throw",e,s,m)})):t.resolve(u).then((function(e){c.value=e,s(c)}),(function(e){return r("throw",e,s,m)}))}m(l.arg)}var o;n(this,"_invoke",{value:function(e,a){function n(){return new t((function(t,n){r(e,a,t,n)}))}return o=o?o.then(n,n):n()}})}function L(t,r,a){var n=b;return function(i,o){if(n===f)throw Error("Generator is already running");if(n===g){if("throw"===i)throw o;return{value:e,done:!0}}for(a.method=i,a.arg=o;;){var s=a.delegate;if(s){var m=S(s,a);if(m){if(m===v)continue;return m}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(n===b)throw n=g,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);n=f;var l=h(t,r,a);if("normal"===l.type){if(n=a.done?g:p,l.arg===v)continue;return{value:l.arg,done:a.done}}"throw"===l.type&&(n=g,a.method="throw",a.arg=l.arg)}}}function S(t,r){var a=r.method,n=t.iterator[a];if(n===e)return r.delegate=null,"throw"===a&&t.iterator.return&&(r.method="return",r.arg=e,S(t,r),"throw"===r.method)||"return"!==a&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+a+"' method")),v;var i=h(n,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var o=i.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,v):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function $(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function j(e){this.tryEntries=[{tryLoc:"root"}],e.forEach($,this),this.reset(!0)}function Y(t){if(t||""===t){var r=t[m];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function r(){for(;++n<t.length;)if(a.call(t,n))return r.value=t[n],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(i(t)+" is not iterable")}return _.prototype=C,n(x,"constructor",{value:C,configurable:!0}),n(C,"constructor",{value:_,configurable:!0}),_.displayName=u(C,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===_||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,C):(e.__proto__=C,u(e,c,"GeneratorFunction")),e.prototype=Object.create(x),e},t.awrap=function(e){return{__await:e}},D(M.prototype),u(M.prototype,l,(function(){return this})),t.AsyncIterator=M,t.async=function(e,r,a,n,i){void 0===i&&(i=Promise);var o=new M(d(e,r,a,n),i);return t.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},D(x),u(x,c,"Generator"),u(x,m,(function(){return this})),u(x,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var a in t)r.push(a);return r.reverse(),function e(){for(;r.length;){var a=r.pop();if(a in t)return e.value=a,e.done=!1,e}return e.done=!0,e}},t.values=Y,j.prototype={constructor:j,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(E),!t)for(var r in this)"t"===r.charAt(0)&&a.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(a,n){return s.type="throw",s.arg=t,r.next=a,n&&(r.method="next",r.arg=e),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var m=a.call(o,"catchLoc"),l=a.call(o,"finallyLoc");if(m&&l){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(m){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),E(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var a=r.completion;if("throw"===a.type){var n=a.arg;E(r)}return n}}throw Error("illegal catch attempt")},delegateYield:function(t,r,a){return this.delegate={iterator:Y(t),resultName:r,nextLoc:a},"next"===this.method&&(this.arg=e),v}},t}function s(e,t,r,a,n,i,o){try{var s=e[i](o),m=s.value}catch(e){return void r(e)}s.done?t(m):Promise.resolve(m).then(a,n)}function m(e){return function(){var t=this,r=arguments;return new Promise((function(a,n){var i=e.apply(t,r);function o(e){s(i,a,n,o,m,"next",e)}function m(e){s(i,a,n,o,m,"throw",e)}o(void 0)}))}}var l={name:"AddOrEditMemberLabel",components:{},props:{},data:function(){var e=this,t=function(e,t,r){if(""===t)return r(new Error("不能为空"));var a=/^\d+$/;a.test(t)?r():r(new Error("请输入正整数"))},r=function(e,t,r,a){if(a){var n=/^[0-9]{1,4}$/;n.test(t)?r():r(new Error("请输入的纯数字，上限为9999"))}else!a&&t.length>0?r(new Error("亲，要勾选左手边的选项喔")):r()};return{isLoading:!1,type:"",settingData:{},memberForm:{name:"",labelType:"auto",labelDirection:"positive",isTotalCount:!1,totalCount:"",isContinueCount:!1,continueCount:"",isLevel:!1,level:"",isIntegral:!1,integral:"",isLabel:!1,label:[],isNotLoginCount:!1,notLoginCount:"",isNotSignCount:!1,notSignCount:"",isNeverLogin:!1,isNeverSign:!1,isValidate:!1,validate:"",isYearCard:!1,yearCard:"",isSeasonCard:!1,seasonCard:"",isMonthCard:!1,monthCard:"",isEnjoyCard:!1,enjoyCard:"",remark:"",isMinValidate:!1,minValidate:"",isMinYearCard:!1,minYearCard:"",isMinSeasonCard:!1,minSeasonCard:"",isMinMonthCard:!1,minMonthCard:""},memberFormRules:{name:[{required:!0,message:"请输入会员名称",trigger:"blur"}],totalCount:[{validator:t,trigger:"blur"}],continueCount:[{validator:t,trigger:"blur"}],level:[{required:!0,message:"请选择会员等级",trigger:"change"}],integral:[{validator:t,trigger:"blur"}],validate:[{validator:function(t,a,n){return r(t,a,n,e.memberForm.isValidate)},trigger:"blur"}],yearCard:[{validator:function(t,a,n){return r(t,a,n,e.memberForm.isYearCard)},trigger:"blur"}],seasonCard:[{validator:function(t,a,n){return r(t,a,n,e.memberForm.isSeasonCard)},trigger:"blur"}],monthCard:[{validator:function(t,a,n){return r(t,a,n,e.memberForm.isMonthCard)},trigger:"blur"}],enjoyCard:[{validator:function(t,a,n){return r(t,a,n,e.memberForm.isEnjoyCard)},trigger:"blur"}],minValidate:[{validator:function(t,a,n){return r(t,a,n,e.memberForm.isMinValidate)},trigger:"blur"}],minYearCard:[{validator:function(t,a,n){return r(t,a,n,e.memberForm.isMinYearCard)},trigger:"blur"}],minSeasonCard:[{validator:function(t,a,n){return r(t,a,n,e.memberForm.isMinSeasonCard)},trigger:"blur"}],minMonthCard:[{validator:function(t,a,n){return r(t,a,n,e.memberForm.isMinMonthCard)},trigger:"blur"}],remark:[{required:!0,message:"请输入标签说明",trigger:"blur"}]},levelList:[],userLabelList:[],labelNameList:[]}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.$route.query.data&&(this.settingData=JSON.parse(decodeURIComponent(this.$route.query.data))),this.$route.params.type&&(this.type=this.$route.params.type,"edit"===this.type&&this.initData()),this.getMemberLevel(),this.getUserLabelList()},initData:function(){this.memberForm.name=this.settingData.name,this.memberForm.labelType=this.settingData.type,this.memberForm.labelDirection=this.settingData.direction,this.memberForm.isValidate=!(!Reflect.has(this.settingData,"member_gt_days")||null===this.settingData.member_gt_days||-1===this.settingData.member_gt_days),this.memberForm.validate=Reflect.has(this.settingData,"member_gt_days")&&-1!==this.settingData.member_gt_days?this.settingData.member_gt_days:"",this.memberForm.isYearCard=!(!Reflect.has(this.settingData,"year_gt_count")||null===this.settingData.year_gt_count||-1===this.settingData.year_gt_count),this.memberForm.yearCard=Reflect.has(this.settingData,"year_gt_count")&&-1!==this.settingData.year_gt_count?this.settingData.year_gt_count:"",this.memberForm.isSeasonCard=!(!Reflect.has(this.settingData,"season_gt_count")||null===this.settingData.season_gt_count||-1===this.settingData.season_gt_count),this.memberForm.seasonCard=Reflect.has(this.settingData,"season_gt_count")&&-1!==this.settingData.season_gt_count?this.settingData.season_gt_count:"",this.memberForm.isMonthCard=!(!Reflect.has(this.settingData,"month_gt_count")||null===this.settingData.month_gt_count||-1===this.settingData.month_gt_count),this.memberForm.monthCard=Reflect.has(this.settingData,"month_gt_count")&&-1!==this.settingData.month_gt_count?this.settingData.month_gt_count:"",this.memberForm.isEnjoyCard=!(!Reflect.has(this.settingData,"week_gt_count")||null===this.settingData.week_gt_count||-1===this.settingData.week_gt_count),this.memberForm.enjoyCard=Reflect.has(this.settingData,"week_gt_count")&&-1!==this.settingData.week_gt_count?this.settingData.week_gt_count:"",this.memberForm.isMinValidate=!(!Reflect.has(this.settingData,"member_lt_days")||null===this.settingData.member_lt_days||-1===this.settingData.member_lt_days),this.memberForm.minValidate=Reflect.has(this.settingData,"member_lt_days")&&-1!==this.settingData.member_lt_days?this.settingData.member_lt_days:"",this.memberForm.isMinYearCard=!(!Reflect.has(this.settingData,"year_lt_count")||null===this.settingData.year_lt_count||-1===this.settingData.year_lt_count),this.memberForm.minYearCard=Reflect.has(this.settingData,"year_lt_count")&&-1!==this.settingData.year_lt_count?this.settingData.year_lt_count:"",this.memberForm.isMinSeasonCard=!(!Reflect.has(this.settingData,"season_lt_count")||null===this.settingData.season_lt_count||-1===this.settingData.season_lt_count),this.memberForm.minSeasonCard=Reflect.has(this.settingData,"season_lt_count")&&-1!==this.settingData.season_lt_count?this.settingData.season_lt_count:"",this.memberForm.isMinMonthCard=!(!Reflect.has(this.settingData,"month_lt_count")||null===this.settingData.month_lt_count||-1===this.settingData.month_lt_count),this.memberForm.minMonthCard=Reflect.has(this.settingData,"month_lt_count")&&-1!==this.settingData.month_lt_count?this.settingData.month_lt_count:"",this.memberForm.remark=this.settingData.remark},saveSetting:function(){var e=this;if("auto"===this.memberForm.labelType&&"positive"===this.memberForm.labelDirection&&!this.memberForm.isValidate&&!this.memberForm.isYearCard&&!this.memberForm.isSeasonCard&&!this.memberForm.isMonthCard&&!this.memberForm.isEnjoyCard||"auto"===this.memberForm.labelType&&"negative"===this.memberForm.labelDirection&&!this.memberForm.isMinValidate&&!this.memberForm.isMinYearCard&&!this.memberForm.isMinSeasonCard&&!this.memberForm.isMinMonthCard)return this.$message.error("请选择标签条件");this.$refs.memberFormRef.validate((function(t){if(t){var r,a={name:e.memberForm.name,type:e.memberForm.labelType,direction:e.memberForm.labelDirection,remark:e.memberForm.remark,member_gt_days:e.memberForm.isValidate&&""!==e.memberForm.validate?parseInt(e.memberForm.validate):-1,year_gt_count:e.memberForm.isYearCard&&""!==e.memberForm.yearCard?parseInt(e.memberForm.yearCard):-1,season_gt_count:e.memberForm.isSeasonCard&&""!==e.memberForm.seasonCard?parseInt(e.memberForm.seasonCard):-1,month_gt_count:e.memberForm.isMonthCard&&""!==e.memberForm.monthCard?parseInt(e.memberForm.monthCard):-1,week_gt_count:e.memberForm.isEnjoyCard&&""!==e.memberForm.enjoyCard?parseInt(e.memberForm.enjoyCard):-1,member_lt_days:e.memberForm.isMinValidate&&""!==e.memberForm.minValidate?parseInt(e.memberForm.minValidate):-1,year_lt_count:e.memberForm.isMinYearCard&&""!==e.memberForm.minYearCard?parseInt(e.memberForm.minYearCard):-1,season_lt_count:e.memberForm.isMinSeasonCard&&""!==e.memberForm.minSeasonCard?parseInt(e.memberForm.minSeasonCard):-1,month_lt_count:e.memberForm.isMinMonthCard&&""!==e.memberForm.minMonthCard?parseInt(e.memberForm.minMonthCard):-1};switch(e.type){case"add":r=e.$apis.apiBackgroundMemberMemberLabelAddPost(a);break;case"edit":a.id=Number(e.settingData.id),r=e.$apis.apiBackgroundMemberMemberLabelModifyPost(a);break}e.confirmOperation(r)}}))},confirmOperation:function(e){var t=this;return m(o().mark((function r(){var a;return o().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!t.isLoading){r.next=2;break}return r.abrupt("return");case 2:return t.isLoading=!0,r.next=5,e;case 5:a=r.sent,t.isLoading=!1,0===a.code?(t.$message.success("成功"),t.$closeCurrentTab(t.$route.path)):t.$message.error(a.msg);case 8:case"end":return r.stop()}}),r)})))()},getMemberLevel:function(){var e=this;return m(o().mark((function t(){var r;return o().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,e.$apis.apiBackgroundMemberMemberGradeListPost({page:1,page_size:99999});case 3:r=t.sent,e.isLoading=!1,0===r.code?e.levelList=r.data.results:e.$message.error(r.msg);case 6:case"end":return t.stop()}}),t)})))()},getUserLabelList:function(){var e=this;return m(o().mark((function t(){var r;return o().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,e.$apis.apiBackgroundHealthyLabelListPost({page:1,page_size:99999,type:"user"});case 3:r=t.sent,e.isLoading=!1,0===r.code?e.userLabelList=r.data.results:e.$message.error(r.msg);case 6:case"end":return t.stop()}}),t)})))()},addLabel:function(){this.memberForm.permissions.push("")},delLabel:function(e){this.memberForm.permissions.splice(e,1)}}},c=l,u=(r("8dd8"),r("2877")),d=Object(u["a"])(c,a,n,!1,null,"6152e179",null);t["default"]=d.exports},"8dd8":function(e,t,r){"use strict";r("2761")}}]);