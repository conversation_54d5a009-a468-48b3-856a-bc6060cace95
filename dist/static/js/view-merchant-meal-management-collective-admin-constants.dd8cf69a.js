(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-collective-admin-constants"],{8426:function(e,a,l){"use strict";l.r(a),l.d(a,"COLLECTIVE_ADMIN",(function(){return t}));var t={select_date:{type:"datetimerange",label:"创建时间",format:"yyyy-MM-dd HH:mm:ss",value:[]},name:{type:"input",label:"名称",value:"",placeholder:"请输入名字"},min_age:{type:"input",label:"最小年龄",value:"",placeholder:"请输入最小年龄"},max_age:{type:"input",label:"最大年龄",value:"",placeholder:"请输入最大年龄"}}}}]);