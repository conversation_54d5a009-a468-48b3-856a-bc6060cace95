(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-points-admin-PointsCommodity","view-super-health-system-points-admin-components-commodiyDetailsDrawer","view-super-health-system-points-admin-components-constantsConfig"],{4908:function(t,e,r){},6168:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",[e("div",{staticClass:"booking-meal-wrapper container-wrapper"},[e("refresh-tool",{on:{refreshPage:t.refreshHandle}}),e("search-form",{ref:"searchRef",attrs:{"form-setting":t.searchFormSetting},on:{search:t.searchHandle}}),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("数据列表")]),e("div",{staticClass:"align-r"},[e("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(e){return t.addAndEditCommodity("add")}}},[t._v(" 新建 ")])],1)]),e("div",{staticClass:"table-content"},[e("el-table",{ref:"tableData",staticClass:"ps-table-tree",staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},t._l(t.tableSettings,(function(r){return e("table-column",{key:r.key,attrs:{col:r},scopedSlots:t._u([{key:"image",fn:function(t){var r=t.row;return[e("el-image",{staticStyle:{width:"65px",height:"62px"},attrs:{src:r.images_url[0],"preview-src-list":[r.images_url[0]]}})]}},{key:"commodityType",fn:function(r){var n=r.row;return[e("div",[e("div",[t._v(t._s(n.commodity_type_alias))]),"virtual"===n.commodity_type?e("div",[e("span",[t._v(t._s(n.virtual_commodity_type_alias))]),"ai_nutritionist"===n.virtual_commodity_type&&n.commodity_extra?e("span",[t._v(" "+t._s(n.commodity_extra.count)+"次 ")]):t._e(),"member"===n.virtual_commodity_type&&n.commodity_extra?e("span",[t._v(" "+t._s(n.commodity_extra.day)+"天 ")]):t._e()]):t._e(),"physical"===n.commodity_type?e("div",[e("span",[t._v("商品编码："+t._s(n.physical_code))])]):t._e()])]}},{key:"grounding",fn:function(r){var n=r.row;return[n.is_permanent?e("div",[t._v("永久")]):e("div",[e("div",[t._v(t._s(n.start_date)+"至")]),e("div",[t._v(t._s(n.end_date))])])]}},{key:"points",fn:function(r){var n=r.row;return[e("span",[t._v(t._s("money"===n.commodity_price_type?"--":n.points))])]}},{key:"buy_stock_num",fn:function(r){var n=r.row;return[-1===n.buy_stock_num?e("div",[e("div",[t._v("不限制")])]):e("div",[t._v(t._s(n.buy_stock_num))])]}},{key:"buy_limit",fn:function(r){var n=r.row;return[e("div",[e("span",[t._v(t._s(n.buy_limit_type_alias))]),"non"!==n.buy_limit_type?e("span",[t._v(t._s(n.buy_limit_num)+"次")]):t._e()])]}},{key:"status",fn:function(r){var n=r.row;return[e("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},on:{change:function(e){return t.switchStatus(n)}},model:{value:n.is_enable,callback:function(e){t.$set(n,"is_enable",e)},expression:"row.is_enable"}})]}},{key:"operation",fn:function(r){var n=r.row;return[e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.clickDetailsCommodity(n)}}},[t._v(" 查看 ")]),e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small",disabled:n.is_enable},on:{click:function(e){return t.addAndEditCommodity("modify",n)}}},[t._v(" 编辑 ")]),e("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small",disabled:n.is_enable},on:{click:function(e){return t.deleteHandler(n)}}},[t._v(" 删除 ")])]}}],null,!0)})})),1)],1),e("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[e("el-pagination",{staticClass:"ps-text",attrs:{"current-page":t.currentPage,"page-sizes":[10,20,50,100,500],"page-size":t.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:t.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)])],1),t.commodiyModifyDrawerVisible?e("commodiy-modify-drawer",{attrs:{isshow:t.commodiyModifyDrawerVisible,drawerType:t.drawerType,drawerModifyData:t.drawerModifyData,collectData:t.collectData},on:{"update:isshow":function(e){t.commodiyModifyDrawerVisible=e},clickSaveDrawer:t.clickSaveDrawer}}):t._e(),t.commodiyDetailsDrawerVisible?e("commodiy-details-drawer",{attrs:{isshow:t.commodiyDetailsDrawerVisible,commodiyId:t.drawerModifyData.id},on:{"update:isshow":function(e){t.commodiyDetailsDrawerVisible=e}}}):t._e()],1)},a=[],o=r("ed08"),i=r("70ec"),l=r("21c9"),c=r("9ac4");function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function u(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */u=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",l=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function d(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{d({},"")}catch(t){d=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var o=e&&e.prototype instanceof g?e:g,i=Object.create(o.prototype),l=new E(n||[]);return a(i,"_invoke",{value:O(t,r,l)}),i}function y(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",m="suspendedYield",h="executing",v="completed",b={};function g(){}function _(){}function w(){}var x={};d(x,i,(function(){return this}));var D=Object.getPrototypeOf,L=D&&D(D(T([])));L&&L!==r&&n.call(L,i)&&(x=L);var k=w.prototype=g.prototype=Object.create(x);function P(t){["next","throw","return"].forEach((function(e){d(t,e,(function(t){return this._invoke(e,t)}))}))}function M(t,e){function r(a,o,i,l){var c=y(t[a],t,o);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==s(d)&&n.call(d,"__await")?e.resolve(d.__await).then((function(t){r("next",t,i,l)}),(function(t){r("throw",t,i,l)})):e.resolve(d).then((function(t){u.value=t,i(u)}),(function(t){return r("throw",t,i,l)}))}l(c.arg)}var o;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return o=o?o.then(a,a):a()}})}function O(e,r,n){var a=p;return function(o,i){if(a===h)throw Error("Generator is already running");if(a===v){if("throw"===o)throw i;return{value:t,done:!0}}for(n.method=o,n.arg=i;;){var l=n.delegate;if(l){var c=S(l,n);if(c){if(c===b)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===p)throw a=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=h;var s=y(e,r,n);if("normal"===s.type){if(a=n.done?v:m,s.arg===b)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(a=v,n.method="throw",n.arg=s.arg)}}}function S(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,S(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),b;var o=y(a,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,b;var i=o.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,b):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,b)}function C(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(C,this),this.reset(!0)}function T(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,o=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(s(e)+" is not iterable")}return _.prototype=w,a(k,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:_,configurable:!0}),_.displayName=d(w,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===_||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,d(t,c,"GeneratorFunction")),t.prototype=Object.create(k),t},e.awrap=function(t){return{__await:t}},P(M.prototype),d(M.prototype,l,(function(){return this})),e.AsyncIterator=M,e.async=function(t,r,n,a,o){void 0===o&&(o=Promise);var i=new M(f(t,r,n,a),o);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},P(k),d(k,c,"Generator"),d(k,i,(function(){return this})),d(k,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=T,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return l.type="throw",l.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],l=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var c=n.call(i,"catchLoc"),s=n.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,b):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),b},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),j(r),b}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;j(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:T(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),b}},e}function d(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function f(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d(Object(r),!0).forEach((function(e){y(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function y(t,e,r){return(e=p(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function p(t){var e=m(t,"string");return"symbol"==s(e)?e:e+""}function m(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function h(t,e){return w(t)||_(t,e)||b(t,e)||v()}function v(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function b(t,e){if(t){if("string"==typeof t)return g(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?g(t,e):void 0}}function g(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function _(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,o,i,l=[],c=!0,s=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(l.push(n.value),l.length!==e);c=!0);}catch(t){s=!0,a=t}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}function w(t){if(Array.isArray(t))return t}function x(t,e,r,n,a,o,i){try{var l=t[o](i),c=l.value}catch(t){return void r(t)}l.done?e(c):Promise.resolve(c).then(n,a)}function D(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var o=t.apply(e,r);function i(t){x(o,n,a,i,l,"next",t)}function l(t){x(o,n,a,i,l,"throw",t)}i(void 0)}))}}var L={name:"PointsCommodity",components:{CommodiyModifyDrawer:l["default"],CommodiyDetailsDrawer:c["default"]},data:function(){return{isLoading:!1,tableSettings:[{label:"商品图片",key:"image",type:"slot",slotName:"image",width:"150px"},{label:"商品名称",key:"name"},{label:"商品类型",key:"commodity_type_alias",type:"slot",slotName:"commodityType",width:"150px"},{label:"上架时间",key:"grounding",type:"slot",slotName:"grounding"},{label:"优先级",key:"priority"},{label:"兑换积分",key:"points",type:"slot",slotName:"points"},{label:"支付金额（元）",key:"fee",type:"money"},{label:"销售数量",key:"stock_num"},{label:"当前库存",key:"buy_stock_num",type:"slot",slotName:"buy_stock_num"},{label:"可兑换数",key:"buy_limit",type:"slot",slotName:"buy_limit"},{label:"创建时间",key:"create_time"},{label:"修改时间",key:"update_time"},{label:"操作人",key:"operator_name"},{label:"状态",key:"is_enable",type:"slot",slotName:"status"},{label:"操作",key:"operation",type:"slot",slotName:"operation"}],tableData:[],pageSize:10,totalCount:0,currentPage:1,total:0,searchFormSetting:Object(o["f"])(i["POINTS_COMMODITY"]),commodiyModifyDrawerVisible:!1,commodiyDetailsDrawerVisible:!1,drawerType:"",drawerModifyData:{},collectData:{}}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getPointsPointsCommodityList()},refreshHandle:function(){this.currentPage=1,this.$refs.searchRef.resetForm(),this.tableData=[],this.initLoad()},searchHandle:Object(o["d"])((function(){this.currentPage=1,this.getPointsPointsCommodityList()}),300),clickSaveDrawer:function(){this.getPointsPointsCommodityList()},getPointsPointsCommodityList:function(){var t=this;return D(u().mark((function e(){var r,n,a,i;return u().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(o["Z"])(t.$apis.apiBackgroundMemberPointsPointsCommodityListPost(f({page:t.currentPage,page_size:t.pageSize},t.formatQueryParams(t.searchFormSetting))));case 3:if(r=e.sent,n=h(r,2),a=n[0],i=n[1],t.isLoading=!1,!a){e.next=11;break}return t.$message.error(a.message),e.abrupt("return");case 11:0===i.code?(t.total=i.data.count,t.totalCount=i.data.count,t.tableData=i.data.results,t.collectData=i.data.collect):t.$message.error(i.msg);case 12:case"end":return e.stop()}}),e)})))()},addAndEditCommodity:function(t,e){this.drawerType=t,"modify"===t&&(this.drawerModifyData=Object(o["f"])(e)),this.commodiyModifyDrawerVisible=!0},clickDetailsCommodity:function(t){this.drawerModifyData=Object(o["f"])(t),this.commodiyDetailsDrawerVisible=!0},switchStatus:function(t){var e=this;this.$confirm("确定要".concat(t.is_enable?"上架":"下架","该商品？"),"提示",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var r=D(u().mark((function r(n,a,i){var l,c,s,d;return u().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if("confirm"!==n){r.next=15;break}return a.confirmButtonLoading=!0,r.next=4,Object(o["Z"])(e.$apis.apiBackgroundMemberPointsPointsCommodityModifyPost(t));case 4:if(l=r.sent,c=h(l,2),s=c[0],d=c[1],a.confirmButtonLoading=!1,!s){r.next=12;break}return e.$message.error(s.message),r.abrupt("return");case 12:0===d.code?(i(),e.$message.success(d.msg),e.getPointsPointsCommodityList()):e.$message.error(d.msg),r.next=16;break;case 15:a.confirmButtonLoading||(i(),t.is_enable=!t.is_enable);case 16:case"end":return r.stop()}}),r)})));function n(t,e,n){return r.apply(this,arguments)}return n}()}).then((function(t){})).catch((function(t){}))},deleteHandler:function(t){var e=this;this.$confirm("确定删除？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-warn",center:!0,beforeClose:function(){var r=D(u().mark((function r(n,a,i){var l,c,s,d;return u().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if("confirm"!==n){r.next=20;break}if(!e.dialogLoading){r.next=3;break}return r.abrupt("return",e.$message.error("请勿重复提交！"));case 3:return e.dialogLoading=!0,a.confirmButtonLoading=!0,r.next=7,Object(o["Z"])(e.$apis.apiBackgroundMemberPointsPointsCommodityDeletePost({ids:[t.id]}));case 7:if(l=r.sent,c=h(l,2),s=c[0],d=c[1],e.dialogLoading=!1,!s){r.next=15;break}return e.$message.error(s.message),r.abrupt("return");case 15:0===d.code?(e.$message.success(d.msg),e.currentPage>1&&1===e.tableData.length&&e.currentPage--,e.getPointsPointsCommodityList()):e.$message.error(d.msg),i(),a.confirmButtonLoading=!1,r.next=21;break;case 20:a.confirmButtonLoading||i();case 21:case"end":return r.stop()}}),r)})));function n(t,e,n){return r.apply(this,arguments)}return n}()}).then((function(t){})).catch((function(t){}))},handleSizeChange:function(t){this.pageSize=t,this.getPointsPointsCommodityList()},handleCurrentChange:function(t){this.currentPage=t,this.getPointsPointsCommodityList()},formatQueryParams:function(t){var e={};for(var r in t)""!==t[r].value&&null!==t[r].value&&0!==t[r].value.length&&("select_date"!==r?e[r]=t[r].value:t[r].value.length>0&&(e.start_create_time=t[r].value[0],e.end_create_time=t[r].value[1]));return e}}},k=L,P=r("2877"),M=Object(P["a"])(k,n,a,!1,null,"4b949fe5",null);e["default"]=M.exports},"70ec":function(t,e,r){"use strict";r.r(e),r.d(e,"recentSevenDay",(function(){return a})),r.d(e,"PAYMENT_ORDER",(function(){return o})),r.d(e,"REFUND_ORDER",(function(){return i})),r.d(e,"POINTS_COMMODITY",(function(){return l})),r.d(e,"POINTS_TASK",(function(){return c}));var n=r("5a0c"),a=[n().subtract(7,"day").format("YYYY-MM-DD"),n().format("YYYY-MM-DD")],o={time_type:{type:"select",value:"create_time",maxWidth:"120px",dataList:[{label:"创建时间",value:"create_time"},{label:"支付时间",value:"pay_time"}]},select_date:{type:"daterange",value:a,format:"yyyy-MM-dd",clearable:!1},trade_no:{type:"input",value:"",label:"订单号",labelWidth:"100px",placeholder:"请输入订单号",clearable:!0},commodity_name:{type:"input",value:"",label:"商品名称",labelWidth:"100px",placeholder:"请输入商品名称",clearable:!0},commodity_type:{type:"select",value:"",label:"商品类型",clearable:!0,dataList:[{label:"虚拟商品",value:"virtual"},{label:"实物商品",value:"physical"}]},is_enable:{type:"select",value:"",label:"状态",clearable:!0,dataList:[{label:"全部",value:""},{label:"上架中",value:!0},{label:"已下架",value:!1}]},user_name:{type:"input",value:"",label:"用户名称",labelWidth:"100px",placeholder:"请输入用户名称",clearable:!0},user_phone:{type:"input",value:"",label:"手机号",labelWidth:"100px",placeholder:"请输入手机号",clearable:!0}},i={time_type:{type:"select",value:"create_time",maxWidth:"120px",dataList:[{label:"创建时间",value:"create_time"},{label:"退款时间",value:"finish_time"}]},select_date:{type:"daterange",value:a,format:"yyyy-MM-dd",clearable:!1},trade_no:{type:"input",value:"",label:"订单号",labelWidth:"100px",placeholder:"请输入订单号",clearable:!0},commodity_name:{type:"input",value:"",label:"商品名称",labelWidth:"100px",placeholder:"请输入商品名称",clearable:!0},commodity_type:{type:"select",value:"",label:"商品类型",clearable:!0,dataList:[{label:"虚拟商品",value:"virtual"},{label:"实物商品",value:"physical"}]},user_name:{type:"input",value:"",label:"用户名称",labelWidth:"100px",placeholder:"请输入用户名称",clearable:!0},origin_trade_no:{type:"input",value:"",label:"原订单",labelWidth:"100px",placeholder:"请输入原订单",clearable:!0}},l={time_type:{type:"select",value:"create_time",maxWidth:"120px",dataList:[{label:"创建时间",value:"create_time"},{label:"修改时间",value:"update_time"}]},select_date:{type:"daterange",value:a,format:"yyyy-MM-dd",clearable:!1},name:{type:"input",value:"",label:"商品名称",labelWidth:"100px",placeholder:"请输入商品名称",clearable:!0},commodity_type:{type:"select",value:"",label:"商品类型",clearable:!0,dataList:[{label:"虚拟商品",value:"virtual"},{label:"实物商品",value:"physical"}]},is_enable:{type:"select",value:"",label:"状态",clearable:!0,dataList:[{label:"全部",value:""},{label:"上架中",value:!0},{label:"已下架",value:!1}]}},c={select_date:{type:"daterange",label:"创建时间",value:[],format:"yyyy-MM-dd",clearable:!1},name:{type:"input",value:"",label:"任务名称",labelWidth:"100px",placeholder:"请输入任务名称",clearable:!0}}},"9ac4":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"drawer-box"},[e("customDrawer",{attrs:{show:t.visible,loading:t.isLoading,title:"商品详情",confirmShow:!1,cancelText:"关 闭",cancelClass:"ps-btn",size:800},on:{"update:show":function(e){t.visible=e}}},[e("div",{staticClass:"drawer-container"},[e("div",{staticClass:"drawer-content"},[e("el-form",{ref:"drawerFormDataRef",attrs:{model:t.drawerFormData,"label-width":"70px"}},[e("el-form-item",{attrs:{label:"商品名称"}},[e("span",[t._v(t._s(t.drawerModifyData.name))])]),e("el-form-item",{attrs:{label:"识别图片"}},[e("div",{staticStyle:{display:"flex","flex-wrap":"wrap"}},t._l(t.drawerModifyData.images_url,(function(t,r){return e("el-image",{key:r,staticStyle:{width:"100px",height:"100px","margin-right":"10px"},attrs:{src:t,"preview-src-list":[t]}})})),1)]),e("el-form-item",{attrs:{label:"上架时间"}},[t.drawerModifyData.is_permanent?e("div",[t._v("永久")]):e("div",[e("div",[t._v(t._s(t.drawerModifyData.start_date)+"至 "+t._s(t.drawerModifyData.end_date))])])]),e("el-form-item",{attrs:{label:"商品类型"}},[e("div",{staticStyle:{display:"flex"}},[e("div",[t._v(t._s(t.drawerModifyData.commodity_type_alias))]),"virtual"===t.drawerModifyData.commodity_type?e("div",[t._v(" （ "),e("span",[t._v(t._s(t.drawerModifyData.virtual_commodity_type_alias)+",")]),"ai_nutritionist"===t.drawerModifyData.virtual_commodity_type&&t.drawerModifyData.commodity_extra?e("span",[t._v(" "+t._s(t.drawerModifyData.commodity_extra.count)+"次 ")]):t._e(),"member"===t.drawerModifyData.virtual_commodity_type&&t.drawerModifyData.commodity_extra?e("span",[t._v(" "+t._s(t.drawerModifyData.commodity_extra.day)+"天 ")]):t._e(),t._v(" ） ")]):t._e(),"physical"===t.drawerModifyData.commodity_type?e("div",{staticStyle:{"padding-left":"20px"}},[e("span",[t._v("商品编码：（"+t._s(t.drawerModifyData.physical_code)+"）")])]):t._e()])]),e("el-form-item",{attrs:{label:"商品价格"}},["money"===t.drawerModifyData.commodity_price_type||"money_points"===t.drawerModifyData.commodity_price_type?e("span",[t._v(" "+t._s(t._f("formatMoney")(t.drawerModifyData.fee))+"元 ")]):t._e(),"money_points"===t.drawerModifyData.commodity_price_type?e("span",[t._v("+")]):t._e(),"points"===t.drawerModifyData.commodity_price_type||"money_points"===t.drawerModifyData.commodity_price_type?e("span",[t._v(" "+t._s(t.drawerModifyData.points)+"积分 ")]):t._e()]),e("el-form-item",{attrs:{label:"库存数量"}},[-1===t.drawerModifyData.buy_stock_num?e("div",[e("div",[t._v("不限制")])]):e("div",[t._v(t._s(t.drawerModifyData.buy_stock_num))])]),e("el-form-item",{attrs:{label:"可兑换数"}},[e("div",[e("span",[t._v(t._s(t.drawerModifyData.buy_limit_type_alias))]),"non"!==t.drawerModifyData.buy_limit_type?e("span",[t._v(" "+t._s(t.drawerModifyData.buy_limit_num)+"次 ")]):t._e()])]),e("el-form-item",{attrs:{label:"图文详情"}},[e("TinymceUeditor",{attrs:{content:t.drawerModifyData.details,disabled:!0},model:{value:t.drawerModifyData.details,callback:function(e){t.$set(t.drawerModifyData,"details",e)},expression:"drawerModifyData.details"}})],1),e("el-form-item",{attrs:{label:"优先级"}},[e("div",[t._v(" "+t._s(t.drawerModifyData.priority)+" ")])])],1)],1)])])],1)},a=[],o=r("ed08"),i=r("56f9");function l(t){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},l(t)}function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function d(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{d({},"")}catch(t){d=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var o=e&&e.prototype instanceof g?e:g,i=Object.create(o.prototype),l=new E(n||[]);return a(i,"_invoke",{value:O(t,r,l)}),i}function y(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",m="suspendedYield",h="executing",v="completed",b={};function g(){}function _(){}function w(){}var x={};d(x,i,(function(){return this}));var D=Object.getPrototypeOf,L=D&&D(D(T([])));L&&L!==r&&n.call(L,i)&&(x=L);var k=w.prototype=g.prototype=Object.create(x);function P(t){["next","throw","return"].forEach((function(e){d(t,e,(function(t){return this._invoke(e,t)}))}))}function M(t,e){function r(a,o,i,c){var s=y(t[a],t,o);if("throw"!==s.type){var u=s.arg,d=u.value;return d&&"object"==l(d)&&n.call(d,"__await")?e.resolve(d.__await).then((function(t){r("next",t,i,c)}),(function(t){r("throw",t,i,c)})):e.resolve(d).then((function(t){u.value=t,i(u)}),(function(t){return r("throw",t,i,c)}))}c(s.arg)}var o;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return o=o?o.then(a,a):a()}})}function O(e,r,n){var a=p;return function(o,i){if(a===h)throw Error("Generator is already running");if(a===v){if("throw"===o)throw i;return{value:t,done:!0}}for(n.method=o,n.arg=i;;){var l=n.delegate;if(l){var c=S(l,n);if(c){if(c===b)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===p)throw a=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=h;var s=y(e,r,n);if("normal"===s.type){if(a=n.done?v:m,s.arg===b)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(a=v,n.method="throw",n.arg=s.arg)}}}function S(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,S(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),b;var o=y(a,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,b;var i=o.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,b):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,b)}function C(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(C,this),this.reset(!0)}function T(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,o=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(l(e)+" is not iterable")}return _.prototype=w,a(k,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:_,configurable:!0}),_.displayName=d(w,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===_||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,d(t,u,"GeneratorFunction")),t.prototype=Object.create(k),t},e.awrap=function(t){return{__await:t}},P(M.prototype),d(M.prototype,s,(function(){return this})),e.AsyncIterator=M,e.async=function(t,r,n,a,o){void 0===o&&(o=Promise);var i=new M(f(t,r,n,a),o);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},P(k),d(k,u,"Generator"),d(k,i,(function(){return this})),d(k,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=T,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return l.type="throw",l.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],l=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var c=n.call(i,"catchLoc"),s=n.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,b):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),b},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),j(r),b}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;j(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:T(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),b}},e}function s(t,e){return p(t)||y(t,e)||d(t,e)||u()}function u(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(t,e){if(t){if("string"==typeof t)return f(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?f(t,e):void 0}}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function y(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,o,i,l=[],c=!0,s=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(l.push(n.value),l.length!==e);c=!0);}catch(t){s=!0,a=t}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}function p(t){if(Array.isArray(t))return t}function m(t,e,r,n,a,o,i){try{var l=t[o](i),c=l.value}catch(t){return void r(t)}l.done?e(c):Promise.resolve(c).then(n,a)}function h(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var o=t.apply(e,r);function i(t){m(o,n,a,i,l,"next",t)}function l(t){m(o,n,a,i,l,"throw",t)}i(void 0)}))}}var v={props:{isshow:Boolean,commodiyId:{type:Number,default:function(){return null}}},components:{TinymceUeditor:i["a"]},data:function(){return{isLoading:!1,drawerFormData:{},drawerModifyData:{}}},computed:{visible:{get:function(){return this.isshow},set:function(t){this.$emit("update:isshow",t)}}},mounted:function(){this.getPointsPointsCommodityDetail()},methods:{getPointsPointsCommodityDetail:function(){var t=this;return h(c().mark((function e(){var r,n,a,i;return c().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(o["Z"])(t.$apis.apiBackgroundMemberPointsPointsCommodityDetailPost({page:1,page_size:99,id:t.commodiyId}));case 3:if(r=e.sent,n=s(r,2),a=n[0],i=n[1],t.isLoading=!1,!a){e.next=11;break}return t.$message.error(a.message),e.abrupt("return");case 11:0===i.code?t.drawerModifyData=i.data:t.$message.error(i.msg);case 12:case"end":return e.stop()}}),e)})))()},closeClick:function(){this.visible=!1}}},b=v,g=(r("ee37"),r("2877")),_=Object(g["a"])(b,n,a,!1,null,"d802b31c",null);e["default"]=_.exports},ee37:function(t,e,r){"use strict";r("4908")}}]);