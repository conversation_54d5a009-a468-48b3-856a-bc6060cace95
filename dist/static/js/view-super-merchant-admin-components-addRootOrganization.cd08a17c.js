(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-merchant-admin-components-addRootOrganization","view-merchant-consumption-rules-service-admin-AddChargeServiceRule","view-merchant-meal-management-components-mealFoodList-FoodDiscountDialog"],{"8ccd":function(t,e,r){"use strict";r("f862")},c938:function(t){t.exports=JSON.parse('[{"id":"1","name":"互联网/电子商务"},{"id":"2","name":"IT软件与服务"},{"id":"3","name":"IT硬件与设备"},{"id":"4","name":"电子技术"},{"id":"5","name":"通信与运营商"},{"id":"6","name":"网络游戏"},{"id":"7","name":"银行"},{"id":"8","name":"基金|理财|信托"},{"id":"9","name":"保险"},{"id":"10","name":"餐饮"},{"id":"11","name":"酒店"},{"id":"12","name":"旅游"},{"id":"13","name":"快递"},{"id":"14","name":"物流"},{"id":"15","name":"仓储"},{"id":"16","name":"培训"},{"id":"17","name":"院校"},{"id":"18","name":"学术科研"},{"id":"19","name":"交警"},{"id":"20","name":"博物馆"},{"id":"21","name":"公共事业|非盈利机构"},{"id":"22","name":"医药医疗"},{"id":"23","name":"护理美容"},{"id":"24","name":"保健与卫生"},{"id":"25","name":"汽车相关"},{"id":"26","name":"摩托车相关"},{"id":"27","name":"火车相关"},{"id":"28","name":"飞机相关"},{"id":"29","name":"建筑"},{"id":"30","name":"物业"},{"id":"31","name":"消费品"},{"id":"32","name":"法律"},{"id":"33","name":"会展"},{"id":"34","name":"中介服务"},{"id":"35","name":"认证"},{"id":"36","name":"审计"},{"id":"37","name":"传媒"},{"id":"38","name":"体育"},{"id":"39","name":"娱乐休闲"},{"id":"40","name":"印刷"},{"id":"41","name":"其它"},{"id":"42","name":"小学"},{"id":"43","name":"中学"},{"id":"44","name":"大学"},{"id":"45","name":"医院"}]')},d0dd:function(t,e,r){"use strict";r.d(e,"a",(function(){return a})),r.d(e,"b",(function(){return i})),r.d(e,"g",(function(){return n})),r.d(e,"c",(function(){return s})),r.d(e,"f",(function(){return o})),r.d(e,"d",(function(){return l})),r.d(e,"e",(function(){return c}));var a=function(t,e,r){if(e){var a=/^-?(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;a.test(e)?r():r(new Error("金额格式有误"))}else r(new Error("请输入金额"))},i=function(t,e,r){if(e){var a=/^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;a.test(e)?r():r(new Error("金额格式有误"))}else r()},n=function(t,e,r){if(!e)return r(new Error("手机号不能为空"));var a=/^1[3456789]\d{9}$/;a.test(e)?r():r(new Error("请输入正确手机号"))},s=function(t,e,r){if(!e)return r(new Error("金额有误"));var a=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;a.test(e)?r():r(new Error("金额格式有误"))},o=function(t,e,r){if(""===e)return r(new Error("不能为空"));var a=/^\d+$/;a.test(e)?r():r(new Error("请输入正确数字"))},l=function(t,e,r){if(""!==e){var a=/^(\+|-)?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;a.test(e)?r():r(new Error("金额格式有误"))}else r(new Error("请输入金额"))},c=function(t,e,r){var a=/^[\u4E00-\u9FA5\w-]+$/;a.test(e)?r():r(new Error("格式不正确，不能包含特殊字符"))}},e173:function(t,e,r){"use strict";r.d(e,"e",(function(){return i})),r.d(e,"l",(function(){return n})),r.d(e,"b",(function(){return s})),r.d(e,"a",(function(){return o})),r.d(e,"d",(function(){return l})),r.d(e,"k",(function(){return c})),r.d(e,"c",(function(){return m})),r.d(e,"h",(function(){return f})),r.d(e,"f",(function(){return u})),r.d(e,"g",(function(){return d})),r.d(e,"j",(function(){return p})),r.d(e,"i",(function(){return h}));var a=r("e925"),i=function(t,e,r){if(!e)return r();Object(a["c"])(e)?r():r(new Error("邮箱格式错误！"))},n=function(t,e,r){if(!e)return r();Object(a["g"])(e)?r():r(new Error("电话格式错误！"))},s=function(t,e,r){if(!e)return r();Object(a["i"])(e)?r():r(new Error("长度5到20位，只支持数字、大小写英文或下划线组合"))},o=function(t,e,r){if(!e)return r();Object(a["e"])(e)?r():r(new Error("密码长度8~20位，英文加数字"))},l=function(t,e,r){if(!e||"长期"===e)return r();if(Object(a["d"])(e)){var i=e.toString().trim().replace(" ","");if(8!==i.length)return r();i=i.slice(0,4)+"/"+i.slice(4,6)+"/"+i.slice(6,i.length);var n=new Date(i).getTime();if(isNaN(n))return r(new Error("请输入正确的日期"));var s=(new Date).getTime();n<s&&r(new Error("有效期必须大于当前日期")),r()}r(new Error("请输入yyyyMMdd格式的日期"))},c=function(t,e,r){if(!e)return r();Object(a["h"])(e)?r():r(new Error("电话/座机格式错误！"))},m=function(t,e,r){Object(a["m"])(e)?r():r(new Error("金额格式有误"))},f=function(t,e,r){if(""===e)return r(new Error("不能为空"));Object(a["b"])(e)?0===Number(e)?r(new Error("请输入大于0的数字")):r():r(new Error("请输入数字"))},u=function(t,e,r){e?0===Number(e)?r(new Error("请输入大于0的数字！")):Object(a["l"])(e)?r():r(new Error("最多2位数字可保留一位小数!")):r(new Error("请输入！"))},d=function(t,e,r){e?0===Number(e)?r(new Error("请输入大于0的数字！")):Object(a["n"])(e)?r():r(new Error("最多1位数字可保留3位小数!")):r(new Error("请输入！"))},p=function(t,e,r){e?Object(a["k"])(e)&&0!==Number(e)?r():r(new Error("格式错误")):r(new Error("请输入必填项"))},h=function(t,e,r){e?Object(a["d"])(e)?r():r(new Error("请输入数字")):r()}},e925:function(t,e,r){"use strict";r.d(e,"c",(function(){return a})),r.d(e,"g",(function(){return i})),r.d(e,"i",(function(){return n})),r.d(e,"e",(function(){return s})),r.d(e,"h",(function(){return o})),r.d(e,"f",(function(){return l})),r.d(e,"d",(function(){return c})),r.d(e,"m",(function(){return m})),r.d(e,"l",(function(){return f})),r.d(e,"n",(function(){return u})),r.d(e,"j",(function(){return d})),r.d(e,"b",(function(){return p})),r.d(e,"k",(function(){return h})),r.d(e,"a",(function(){return b}));var a=function(t){return/^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(t)},i=function(t){return/^([0-9]{3,4}-)?[0-9]{7,8}$/.test(t.toString())},n=function(t){return/^\w{5,20}$/.test(t)},s=function(t){return/^(?=.*[0-9])(?=.*[a-zA-Z])(.{8,20})$/.test(t)},o=function(t){return/(^([0-9]{3,4}-)?[0-9]{7,8}$)|(^1[3,4,5,6,7,8,9][0-9]{9}$)/.test(t.toString())},l=function(t){return/^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(t.toString())},c=function(t){return/\d/.test(t)},m=function(t){return/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(t)},f=function(t){return/^(([1-9]?[0-9])|([1-9]?[0-9]\.[1-9])|([1-9]?[1-9]\.[0]))$/.test(t)},u=function(t){return/^(([0-9])|([0-9]\.\d{1,3})|([1-9]\.[0]))$/.test(t)},d=function(t){return/^-?(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(t)},p=function(t){return/^[0-9]+$/.test(t)},h=function(t){return/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1}|[1-9][0-9]*\.\d{1})))$/.test(t)},b=function(t){return/^[a-zA-Z0-9]+$/.test(t)}},f850:function(t,e,r){"use strict";r.r(e);var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"container-wrapper super-add-organization is-fixed-footer"},[e("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"organizationFormRef",staticClass:"organization-form-wrapper",attrs:{rules:t.formDataRuls,model:t.formData,"hide-required-asterisk":!0,size:"small"}},["add"===t.operate?e("div",{staticClass:"add-title"},[t._v("新建组织")]):t._e(),e("div",{staticClass:"l-title clearfix"},[e("span",{staticClass:"float-l min-title-h"},[t._v("基本信息")]),t.checkIsFormStatus?t._e():e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.organization.modify"],expression:"['background.admin.organization.modify']"}],staticClass:"float-r",attrs:{size:"mini"},on:{click:t.changeOperate}},[t._v(" 编辑 ")])],1),e("div",{staticClass:"item-box clearfix"},[e("div",{staticClass:"item-b-l"},[t._v(t._s(t.labelName))]),e("div",{staticClass:"item-b-r"},[e("el-form-item",{staticClass:"block-label",attrs:{prop:"name"},scopedSlots:t._u([{key:"label",fn:function(){return[e("span",{staticClass:"warn"},[t._v("*")]),t._v(" 组织名称： ")]},proxy:!0}])},[t.checkIsFormStatus?e("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:t.formData.name,callback:function(e){t.$set(t.formData,"name",e)},expression:"formData.name"}}):e("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.name))])],1)],1)]),e("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"当前组织层次：",prop:"levelName"}},[e("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.levelName))])]),e("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"官网：",prop:"url"}},[t.checkIsFormStatus?e("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:t.formData.url,callback:function(e){t.$set(t.formData,"url",e)},expression:"formData.url"}}):e("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.url))])],1),e("el-row",{staticClass:"form-item-row-box",attrs:{gutter:24}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"固定电话：",prop:"tel"}},[t.checkIsFormStatus?e("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:t.formData.tel,callback:function(e){t.$set(t.formData,"tel",e)},expression:"formData.tel"}}):e("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.tel))])],1)],1),e("el-col",{attrs:{span:12}},[e("div",{staticClass:"form-item-box"},[e("el-form-item",{staticClass:"block-label",attrs:{label:"组织邮箱：",prop:"mailAddress"}},[t.checkIsFormStatus?e("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:t.formData.mailAddress,callback:function(e){t.$set(t.formData,"mailAddress",e)},expression:"formData.mailAddress"}}):e("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.mailAddress))])],1)],1)])],1),e("el-row",{staticClass:"form-item-row-box",attrs:{gutter:24}},[e("el-col",{staticClass:"block-label form-item-box",attrs:{span:12}},[e("el-form-item",{attrs:{label:"行业性质：",prop:"industry"},scopedSlots:t._u([{key:"label",fn:function(){return[e("div",[e("span",{staticClass:"warn"},[t._v("*")]),t._v(" 行业性质： ")])]},proxy:!0}])},[e("el-select",{staticClass:"ps-select",staticStyle:{width:"100%"},attrs:{placeholder:"请选择行业性质",size:"small",disabled:!t.checkIsFormStatus},model:{value:t.formData.industry,callback:function(e){t.$set(t.formData,"industry",e)},expression:"formData.industry"}},t._l(t.industryTypeList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)],1),e("el-col",{staticClass:"block-label form-item-box",attrs:{span:12}},[e("el-form-item",{attrs:{prop:"district"},scopedSlots:t._u([{key:"label",fn:function(){return[e("div",{staticClass:"flex-b-c"},[e("div",[e("span",{staticClass:"warn"},[t._v("*")]),t._v(" 所在地址： ")]),e("el-button",{staticClass:"ps-origin-text",attrs:{type:"text",disabled:!t.checkIsFormStatus},on:{click:t.openMessageBox}},[t._v("同步到下级")])],1)]},proxy:!0},{key:"default",fn:function(){return[e("el-cascader",{ref:"cascaderRef",staticStyle:{display:"block"},attrs:{size:"small",options:t.addrOptions,disabled:!t.checkIsFormStatus,filterable:""},on:{blur:t.getAddress},model:{value:t.formData.district,callback:function(e){t.$set(t.formData,"district",e)},expression:"formData.district"}})]},proxy:!0}])})],1),e("el-col",{staticClass:"block-label form-item-box",attrs:{span:12}},[e("el-form-item",{staticClass:"block-label",attrs:{label:"所属渠道",prop:"channel_id"}},[e("el-cascader",{ref:"channelMul",staticClass:"ps-select",staticStyle:{width:"80%"},attrs:{placeholder:"请选择",clearable:"",options:t.channelTreeList,"show-all-levels":!1,props:t.cascaderProps,disabled:!t.checkIsFormStatus,filterable:""},model:{value:t.formData.channel_id,callback:function(e){t.$set(t.formData,"channel_id",e)},expression:"formData.channel_id"}})],1)],1)],1),e("div",{staticClass:"form-line"}),e("div",{staticClass:"l-title clearfix"},[e("span",{staticClass:"float-l min-title-h"},[t._v("功能配置")])]),e("div",[e("el-button",{class:["w-100","m-l-10","m-b-10",-1===t.versionData.tollVersion?"is-click":""],attrs:{size:"small",disabled:!t.checkIsFormStatus},on:{click:function(e){return t.selectThisVersion(null,!0)}}},[t._v("自由配置")]),t._l(t.versionList,(function(r,a){return e("el-button",{key:a,class:["w-100","m-b-10",t.versionData.tollVersion===r.id?"is-click":""],attrs:{size:"small",disabled:!t.checkIsFormStatus},on:{click:function(e){return t.selectThisVersion(r,!0)}}},[t._v(t._s(r.name))])}))],2),"add"===t.operate?e("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"添加组织层级：",prop:"initOrganizationLevel"}},[e("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:t.formData.initOrganizationLevel,callback:function(e){t.$set(t.formData,"initOrganizationLevel",e)},expression:"formData.initOrganizationLevel"}},t._l(t.levelList,(function(t){return e("el-option",{key:t.level,attrs:{label:t.name,value:t.level}})})),1)],1):t._e(),e("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"",prop:"permission"}},[e("span",{staticClass:"ps-flex-align-c"},[e("div",{staticClass:"warn m-r-5"},[t._v("*")]),e("div",{staticClass:"f-w-700",staticStyle:{color:"#606266"}},[t._v("食堂管理系统-功能配置：")]),e("el-button",{staticClass:"w-80",attrs:{size:"small",type:"text",disabled:!t.checkIsFormStatus},on:{click:function(e){return t.showDrawer("merchant")}}},[t._v("去配置")]),e("span",{staticClass:"font-size-12 origin"},[t._v("（"+t._s(t.formData.permission.length)+" 个）")])],1)]),e("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:""}},[e("span",{staticClass:"ps-flex-align-c"},[e("div",{staticClass:"f-w-700",staticStyle:{color:"#606266"}},[t._v("商户移动端-菜单配置：")]),e("el-button",{staticClass:"w-80",attrs:{size:"small",type:"text",disabled:!t.checkIsFormStatus},on:{click:function(e){return t.showDrawer("merchant_app")}}},[t._v("去配置")]),e("span",{staticClass:"font-size-12 origin"},[t._v("（"+t._s(t.formData.merchantAppPermission.length)+" 个）")])],1)]),"root"===t.type?e("el-form-item",{staticClass:"block-label form-item-box fixed-login-box",attrs:{prop:"username"},scopedSlots:t._u([{key:"label",fn:function(){return[e("span",{staticClass:"warn"},[t._v("*")]),t._v(" 账号： ")]},proxy:!0}],null,!1,2466304201)},["root"===t.type&&"add"!==t.operate?e("span",{staticClass:"fixed-login"},[e("el-button",{attrs:{type:"text",size:"mini"},on:{click:t.gotoLogin}},[t._v("登录")])],1):t._e(),"root"===t.type&&"add"===t.operate?e("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:t.formData.username,callback:function(e){t.$set(t.formData,"username",e)},expression:"formData.username"}}):e("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.username))])],1):t._e(),"root"===t.type&&"add"===t.operate?e("el-form-item",{staticClass:"block-label form-item-box",attrs:{prop:"password"},scopedSlots:t._u([{key:"label",fn:function(){return[e("span",{staticClass:"warn"},[t._v("*")]),t._v(" 密码： ")]},proxy:!0}],null,!1,1334163231)},[e("el-input",{staticClass:"ps-input",attrs:{disabled:!t.checkIsFormStatus,size:"small"},model:{value:t.formData.password,callback:function(e){t.$set(t.formData,"password",e)},expression:"formData.password"}}),e("div",{staticStyle:{"margin-top":"3px",color:"#f56c6c","line-height":"1","font-size":"12px"}},[t._v(" 密码有效期为90天，请在期限前重置密码 ")])],1):t._e(),e("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"",prop:""}},[e("span",[t._v(" 到期修改密码 "),e("el-switch",{attrs:{disabled:!t.checkIsFormStatus,"active-color":"#ff9b45"},model:{value:t.formData.isExpireChangePwd,callback:function(e){t.$set(t.formData,"isExpireChangePwd",e)},expression:"formData.isExpireChangePwd"}}),t.formData.isExpireChangePwd?e("el-checkbox",{staticClass:"ps-checkbox",staticStyle:{"margin-left":"10px"},attrs:{disabled:!t.checkIsFormStatus},model:{value:t.formData.allowJumpChangePwd,callback:function(e){t.$set(t.formData,"allowJumpChangePwd",e)},expression:"formData.allowJumpChangePwd"}},[t._v(" 允许跳过本次 ")]):t._e()],1)]),e("div",{staticClass:"form-line"}),e("div",{staticClass:"l-title clearfix"},[e("span",{staticClass:"float-l min-title-h"},[t._v("联系方式")])]),e("el-row",{staticClass:"form-item-row-box",attrs:{gutter:24}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{staticClass:"block-label",attrs:{label:"联系人：",prop:"contact"}},[t.checkIsFormStatus?e("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:t.formData.contact,callback:function(e){t.$set(t.formData,"contact",e)},expression:"formData.contact"}}):e("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.contact))])],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{staticClass:"block-label",attrs:{prop:"mobile"},scopedSlots:t._u([{key:"label",fn:function(){return[e("span",{staticClass:"warn"},[t._v("*")]),t._v(" 手机号码: ")]},proxy:!0}])},[t.checkIsFormStatus?e("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:t.formData.mobile,callback:function(e){t.$set(t.formData,"mobile",e)},expression:"formData.mobile"}}):e("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.mobile))])],1)],1)],1),e("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"短信模板：",prop:"smsTemplateId"}},[t.checkIsFormStatus?e("el-input",{staticClass:"ps-input",attrs:{type:"textarea",rows:3},model:{value:t.formData.smsTemplateId,callback:function(e){t.$set(t.formData,"smsTemplateId",e)},expression:"formData.smsTemplateId"}}):e("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.smsTemplateId))])],1),e("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"备注：",prop:"remark"}},[t.checkIsFormStatus?e("el-input",{staticClass:"ps-input",attrs:{type:"textarea",rows:3},model:{value:t.formData.remark,callback:function(e){t.$set(t.formData,"remark",e)},expression:"formData.remark"}}):e("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.remark))])],1),e("div",{staticClass:"form-line"}),e("div",{staticClass:"l-title clearfix"},[e("span",{staticClass:"float-l min-title-h"},[t._v("其它设置")])]),e("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"钱包设置",prop:""}},[e("el-checkbox",{staticClass:"ps-checkbox",attrs:{disabled:!t.checkIsFormStatus},model:{value:t.formData.storeWalletOn,callback:function(e){t.$set(t.formData,"storeWalletOn",e)},expression:"formData.storeWalletOn"}},[t._v(" 储值钱包 ")]),e("el-checkbox",{staticClass:"ps-checkbox",attrs:{disabled:!t.checkIsFormStatus},model:{value:t.formData.electronicWalletOn,callback:function(e){t.$set(t.formData,"electronicWalletOn",e)},expression:"formData.electronicWalletOn"}},[t._v(" 电子钱包 ")]),e("el-checkbox",{staticClass:"ps-checkbox",attrs:{disabled:!t.checkIsFormStatus},model:{value:t.formData.subsidyWalletOn,callback:function(e){t.$set(t.formData,"subsidyWalletOn",e)},expression:"formData.subsidyWalletOn"}},[t._v(" 补贴钱包 ")]),e("el-checkbox",{staticClass:"ps-checkbox",attrs:{disabled:!t.checkIsFormStatus},model:{value:t.formData.complimentaryWalletOn,callback:function(e){t.$set(t.formData,"complimentaryWalletOn",e)},expression:"formData.complimentaryWalletOn"}},[t._v(" 赠送钱包 ")]),e("el-checkbox",{staticClass:"ps-checkbox",attrs:{disabled:!t.checkIsFormStatus},model:{value:t.formData.otherWalletOn,callback:function(e){t.$set(t.formData,"otherWalletOn",e)},expression:"formData.otherWalletOn"}},[t._v(" 第三方钱包 ")])],1),e("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"",prop:""}},[e("span",{staticStyle:{"margin-right":"25px"}},[t._v(" 线下组合支付 "),e("el-switch",{attrs:{disabled:!t.checkIsFormStatus,"active-color":"#ff9b45"},model:{value:t.formData.combineWalletOn,callback:function(e){t.$set(t.formData,"combineWalletOn",e)},expression:"formData.combineWalletOn"}})],1),e("span",[t._v(" 线上组合支付 "),e("el-switch",{attrs:{disabled:!t.checkIsFormStatus,"active-color":"#ff9b45"},model:{value:t.formData.onlineCombineWallet_on,callback:function(e){t.$set(t.formData,"onlineCombineWallet_on",e)},expression:"formData.onlineCombineWallet_on"}})],1)]),e("el-form-item",{staticClass:"form-item-box",attrs:{label:"开关设置",prop:""}},[e("span",{staticStyle:{"margin-right":"25px"}},[t._v(" 人脸支付 "),e("el-switch",{attrs:{disabled:!t.checkIsFormStatus,"active-color":"#ff9b45"},model:{value:t.formData.facepay,callback:function(e){t.$set(t.formData,"facepay",e)},expression:"formData.facepay"}})],1),e("span",[t._v(" 支持退款 "),e("el-switch",{attrs:{disabled:!t.checkIsFormStatus,"active-color":"#ff9b45"},model:{value:t.formData.refundOn,callback:function(e){t.$set(t.formData,"refundOn",e)},expression:"formData.refundOn"}})],1)]),e("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"",prop:""}},[e("span",[t._v(" 是否农行项目点展示 "),e("el-switch",{attrs:{disabled:!t.checkIsFormStatus,"active-color":"#ff9b45"},model:{value:t.formData.isAbcProject,callback:function(e){t.$set(t.formData,"isAbcProject",e)},expression:"formData.isAbcProject"}})],1)]),e("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"",prop:""}},[e("span",[t._v("人脸更新消息提醒：")]),e("el-switch",{staticClass:"m-r-20",attrs:{disabled:!t.checkIsFormStatus,"active-color":"#ff9b45"},model:{value:t.formData.enableUpdateNotify,callback:function(e){t.$set(t.formData,"enableUpdateNotify",e)},expression:"formData.enableUpdateNotify"}}),t.formData.enableUpdateNotify?e("div",{staticStyle:{"margin-left":"125px"}},[e("span",{staticClass:"m-r-20"},[t._v("上传人脸时间每隔")]),e("el-form-item",{staticClass:"inline-label form-item-box m-t-2 m-b-2 m-r-20",attrs:{label:"",prop:"faceUpdateTime"}},[e("el-select",{staticClass:"w-110",attrs:{clearable:"",disabled:!t.checkIsFormStatus,placeholder:"请选择"},model:{value:t.formData.faceUpdateTime,callback:function(e){t.$set(t.formData,"faceUpdateTime",e)},expression:"formData.faceUpdateTime"}},t._l(t.faceUploadOptions,(function(t){return e("el-option",{key:t.value,attrs:{label:t.name,value:t.value}})})),1)],1),"auto"===t.formData.faceUpdateTime?e("el-form-item",{staticClass:"inline-label form-item-box m-t-2 m-b-2 m-r-20",attrs:{label:"",prop:"customFaceDate"}},[e("el-input",{staticClass:"w-100",attrs:{disabled:!t.checkIsFormStatus},model:{value:t.formData.customFaceDate,callback:function(e){t.$set(t.formData,"customFaceDate",e)},expression:"formData.customFaceDate"}}),e("span",{staticClass:"m-l-10"},[t._v("天")])],1):t._e(),e("span",{},[t._v("进行消息提醒")]),e("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"",prop:""}},[e("span",{staticStyle:{"vertical-align":"top"}},[t._v("提醒内容：")]),e("el-input",{staticStyle:{width:"70%"},attrs:{disabled:!t.checkIsFormStatus,type:"textarea",rows:2},model:{value:t.formData.notifyMsg,callback:function(e){t.$set(t.formData,"notifyMsg",e)},expression:"formData.notifyMsg"}})],1)],1):t._e()],1),"add"!==t.operate?e("div",[e("div",{staticClass:"form-line"}),e("div",{staticClass:"l-title clearfix"},[e("span",{staticClass:"float-l min-title-h"},[t._v(" 第三方设置 "),e("el-switch",{staticStyle:{"margin-left":"15px"},attrs:{disabled:!t.checkIsFormStatus,"active-color":"#ff9b45"},model:{value:t.formData.isThirdInterface,callback:function(e){t.$set(t.formData,"isThirdInterface",e)},expression:"formData.isThirdInterface"}})],1)]),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loadingThirdInfo,expression:"loadingThirdInfo"},{name:"show",rawName:"v-show",value:t.formData.isThirdInterface,expression:"formData.isThirdInterface"}]},[e("el-row",{staticClass:"form-item-row-box",attrs:{gutter:24}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{staticClass:"block-label",attrs:{label:"应用key：",prop:"thirdAppKey"}},[t.checkIsFormStatus?e("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:t.formData.thirdAppKey,callback:function(e){t.$set(t.formData,"thirdAppKey",e)},expression:"formData.thirdAppKey"}}):e("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.thirdAppKey))])],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{staticClass:"block-label",attrs:{label:"应用secret：",prop:"thirdSecretKey"}},[t.checkIsFormStatus?e("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:t.formData.thirdSecretKey,callback:function(e){t.$set(t.formData,"thirdSecretKey",e)},expression:"formData.thirdSecretKey"}}):e("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:t.formData.thirdSecretKey,placement:"top"}},[e("div",{staticClass:"item-form-text ellipsis"},[t._v(t._s(t.formData.thirdSecretKey))])])],1)],1)],1),e("el-form-item",{staticClass:"block-label",attrs:{label:"应用名称：",prop:"thirdAppName"}},[t.checkIsFormStatus?e("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:t.formData.thirdAppName,callback:function(e){t.$set(t.formData,"thirdAppName",e)},expression:"formData.thirdAppName"}}):e("div",{staticClass:"item-form-text ellipsis"},[t._v(t._s(t.formData.thirdAppName))])],1),e("el-row",{staticClass:"form-item-row-box",attrs:{gutter:24}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{staticClass:"block-label",attrs:{label:"跳转地址：",prop:"thirdAppUrl"}},[t.checkIsFormStatus?e("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:t.formData.thirdAppUrl,callback:function(e){t.$set(t.formData,"thirdAppUrl",e)},expression:"formData.thirdAppUrl"}}):e("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.thirdAppUrl))])],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{staticClass:"block-label",attrs:{label:"回调地址：",prop:"thirdAppCallbackUrl"}},[t.checkIsFormStatus?e("el-input",{staticClass:"ps-input",attrs:{placeholder:"http://127.0.0.1/?userId={0}&bb=1，{0}将会被替换掉",size:"small"},model:{value:t.formData.thirdAppCallbackUrl,callback:function(e){t.$set(t.formData,"thirdAppCallbackUrl",e)},expression:"formData.thirdAppCallbackUrl"}}):e("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.thirdAppCallbackUrl))])],1)],1)],1),t.checkIsFormStatus?e("el-form-item",{staticClass:"block-center",attrs:{label:"",prop:""}},[e("el-button",{staticClass:"ps-origin-btn",attrs:{type:"primary"},on:{click:t.generateThirdAppinfo}},[t._v(" 重新生成 ")])],1):t._e()],1)]):t._e(),t.checkIsFormStatus?e("div",{staticClass:"form-footer"},[e("el-button",{attrs:{size:"small"},on:{click:t.cancelFormHandle}},[t._v("取消")]),e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.organization.add_root","background.admin.organization.modify"],expression:"[\n          'background.admin.organization.add_root',\n          'background.admin.organization.modify'\n        ]"}],staticClass:"ps-origin-btn",attrs:{type:"primary",size:"small"},on:{click:t.sendFormdataHandle}},[t._v(" 保存 ")])],1):t._e()],1),e("ConfigurationList",{attrs:{isShow:t.drawerShow,type:t.drawerType},on:{"update:isShow":function(e){t.drawerShow=e},"update:is-show":function(e){t.drawerShow=e},refreshPermission:t.refreshPermission}})],1)},i=[],n=r("ed08"),s=r("ef6c"),o=r("c938"),l=r("d0dd"),c=r("e173"),m=r("b164"),f=r("2f62");function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function d(t,e){return b(t)||h(t,e)||y(t,e)||p()}function p(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function h(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var a,i,n,s,o=[],l=!0,c=!1;try{if(n=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(a=n.call(r)).done)&&(o.push(a.value),o.length!==e);l=!0);}catch(t){c=!0,i=t}finally{try{if(!l&&null!=r.return&&(s=r.return(),Object(s)!==s))return}finally{if(c)throw i}}return o}}function b(t){if(Array.isArray(t))return t}function v(t){return k(t)||D(t)||y(t)||g()}function g(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function y(t,e){if(t){if("string"==typeof t)return w(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?w(t,e):void 0}}function D(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function k(t){if(Array.isArray(t))return w(t)}function w(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,a=Array(e);r<e;r++)a[r]=t[r];return a}function x(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */x=function(){return e};var t,e={},r=Object.prototype,a=r.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},n="function"==typeof Symbol?Symbol:{},s=n.iterator||"@@iterator",o=n.asyncIterator||"@@asyncIterator",l=n.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,r){return t[e]=r}}function m(t,e,r,a){var n=e&&e.prototype instanceof g?e:g,s=Object.create(n.prototype),o=new T(a||[]);return i(s,"_invoke",{value:$(t,r,o)}),s}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=m;var d="suspendedStart",p="suspendedYield",h="executing",b="completed",v={};function g(){}function y(){}function D(){}var k={};c(k,s,(function(){return this}));var w=Object.getPrototypeOf,_=w&&w(w(j([])));_&&_!==r&&a.call(_,s)&&(k=_);var C=D.prototype=g.prototype=Object.create(k);function O(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function r(i,n,s,o){var l=f(t[i],t,n);if("throw"!==l.type){var c=l.arg,m=c.value;return m&&"object"==u(m)&&a.call(m,"__await")?e.resolve(m.__await).then((function(t){r("next",t,s,o)}),(function(t){r("throw",t,s,o)})):e.resolve(m).then((function(t){c.value=t,s(c)}),(function(t){return r("throw",t,s,o)}))}o(l.arg)}var n;i(this,"_invoke",{value:function(t,a){function i(){return new e((function(e,i){r(t,a,e,i)}))}return n=n?n.then(i,i):i()}})}function $(e,r,a){var i=d;return function(n,s){if(i===h)throw Error("Generator is already running");if(i===b){if("throw"===n)throw s;return{value:t,done:!0}}for(a.method=n,a.arg=s;;){var o=a.delegate;if(o){var l=A(o,a);if(l){if(l===v)continue;return l}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(i===d)throw i=b,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);i=h;var c=f(e,r,a);if("normal"===c.type){if(i=a.done?b:p,c.arg===v)continue;return{value:c.arg,done:a.done}}"throw"===c.type&&(i=b,a.method="throw",a.arg=c.arg)}}}function A(e,r){var a=r.method,i=e.iterator[a];if(i===t)return r.delegate=null,"throw"===a&&e.iterator.return&&(r.method="return",r.arg=t,A(e,r),"throw"===r.method)||"return"!==a&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+a+"' method")),v;var n=f(i,e.iterator,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,v;var s=n.arg;return s?s.done?(r[e.resultName]=s.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):s:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function P(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function j(e){if(e||""===e){var r=e[s];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,n=function r(){for(;++i<e.length;)if(a.call(e,i))return r.value=e[i],r.done=!1,r;return r.value=t,r.done=!0,r};return n.next=n}}throw new TypeError(u(e)+" is not iterable")}return y.prototype=D,i(C,"constructor",{value:D,configurable:!0}),i(D,"constructor",{value:y,configurable:!0}),y.displayName=c(D,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===y||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,D):(t.__proto__=D,c(t,l,"GeneratorFunction")),t.prototype=Object.create(C),t},e.awrap=function(t){return{__await:t}},O(S.prototype),c(S.prototype,o,(function(){return this})),e.AsyncIterator=S,e.async=function(t,r,a,i,n){void 0===n&&(n=Promise);var s=new S(m(t,r,a,i),n);return e.isGeneratorFunction(r)?s:s.next().then((function(t){return t.done?t.value:s.next()}))},O(C),c(C,l,"Generator"),c(C,s,(function(){return this})),c(C,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var a in e)r.push(a);return r.reverse(),function t(){for(;r.length;){var a=r.pop();if(a in e)return t.value=a,t.done=!1,t}return t.done=!0,t}},e.values=j,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(P),!e)for(var r in this)"t"===r.charAt(0)&&a.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function i(a,i){return o.type="throw",o.arg=e,r.next=a,i&&(r.method="next",r.arg=t),!!i}for(var n=this.tryEntries.length-1;n>=0;--n){var s=this.tryEntries[n],o=s.completion;if("root"===s.tryLoc)return i("end");if(s.tryLoc<=this.prev){var l=a.call(s,"catchLoc"),c=a.call(s,"finallyLoc");if(l&&c){if(this.prev<s.catchLoc)return i(s.catchLoc,!0);if(this.prev<s.finallyLoc)return i(s.finallyLoc)}else if(l){if(this.prev<s.catchLoc)return i(s.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<s.finallyLoc)return i(s.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&a.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var n=i;break}}n&&("break"===t||"continue"===t)&&n.tryLoc<=e&&e<=n.finallyLoc&&(n=null);var s=n?n.completion:{};return s.type=t,s.arg=e,n?(this.method="next",this.next=n.finallyLoc,v):this.complete(s)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),P(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var a=r.completion;if("throw"===a.type){var i=a.arg;P(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,r,a){return this.delegate={iterator:j(e),resultName:r,nextLoc:a},"next"===this.method&&(this.arg=t),v}},e}function _(t,e,r,a,i,n,s){try{var o=t[n](s),l=o.value}catch(t){return void r(t)}o.done?e(l):Promise.resolve(l).then(a,i)}function C(t){return function(){var e=this,r=arguments;return new Promise((function(a,i){var n=t.apply(e,r);function s(t){_(n,a,i,s,o,"next",t)}function o(t){_(n,a,i,s,o,"throw",t)}s(void 0)}))}}function O(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,a)}return r}function S(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?O(Object(r),!0).forEach((function(e){$(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):O(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function $(t,e,r){return(e=A(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function A(t){var e=E(t,"string");return"symbol"==u(e)?e:e+""}function E(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var a=r.call(t,e||"default");if("object"!=u(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var P={name:"SuperAddRootOrganization",components:{ConfigurationList:m["default"]},props:{type:String,infoData:{type:Object,default:function(){return{}}},treeData:Object,id:[String,Number],operate:String,restoreHandle:Function},data:function(){var t=this,e=function(t,e,r){if(!e)return r(new Error("账号不能为空"));var a=/^\w{5,20}$/;a.test(e)?r():r(new Error("账号长度5到20位，只支持数字、大小写英文或下划线组合"))},r=function(e,r,a){var i=/^(?=.*[0-9])(?=.*[a-zA-Z])(.{8,20})$/;if(!r)return"modify"===t.formOperate?void a():a(new Error("密码不能为空"));i.test(r)?a():a(new Error("密码长度8到20位，字母和数组组合"))},a=function(t,e,r){e.length>0?r():r(new Error("功能菜单配置不能为空！"))},i=function(t,e,r){if(""===e||"0"===e)return r(new Error("请输入大于0的数字"));var a=/^\d+$/;a.test(e)?r():r(new Error("请输入正确数字"))};return{labelName:"",formOperate:"detail",isLoading:!1,industryTypeList:o,addrOptions:s["regionData"],formData:{id:"",appid:"",secretKey:"",name:"",levelName:"",initOrganizationLevel:"",tollVersion:"",permission:[],merchantAppPermission:[],appPermission:[],username:"",password:"",url:"",district:[],contact:"",mobile:"",mailAddress:"",tel:"",industry:"",remark:"",facepay:!1,refundOn:!1,refundPassword:"",storeWalletOn:!1,electronicWalletOn:!1,subsidyWalletOn:!1,complimentaryWalletOn:!1,otherWalletOn:!1,isThirdInterface:!1,combineWalletOn:!1,onlineCombineWallet_on:!1,thirdAppKey:"",thirdSecretKey:"",thirdAppName:"",thirdAppUrl:"",thirdAppCallbackUrl:"",smsTemplateId:"",isAbcProject:!1,isExpireChangePwd:!1,allowJumpChangePwd:!1,enableUpdateNotify:!1,faceUpdateTime:"",notifyMsg:"",isWalletPayOrderAsc:!1,channel_id:[],is_member_on:!1},formDataRuls:{name:[{required:!0,message:"组织名称不能为空",trigger:"blur"},{validator:l["e"],trigger:"blur"}],district:[{required:!0,message:"所在地址不能为空",trigger:"blur"}],level_name:[{required:!0,message:"层级名称不能为空",trigger:"blur"}],mobile:[{required:!0,validator:l["g"],trigger:"blur"}],username:[{required:!0,validator:e,trigger:"blur"}],password:[{required:!0,validator:r,trigger:"blur"}],industry:[{required:!0,message:"请选择",trigger:"blur"}],refundPassword:[{validator:r,trigger:"blur"}],tel:[{validator:c["l"],trigger:"blur"}],mailAddress:[{validator:c["e"],trigger:"blur"}],permission:[{required:!0,validator:a,trigger:"blur"}],faceUpdateTime:[{required:!0,message:"请选择人脸更新天数",trigger:"blur"}],customFaceDate:[{validator:i,trigger:"blur"}]},levelList:[],loadingThirdInfo:!1,faceUploadOptions:[{name:"60天",value:60},{name:"90天",value:90},{name:"180天",value:180},{name:"1年",value:365},{name:"自定义",value:"auto"}],cascaderProps:{label:"name",value:"id",children:"children_list",checkStrictly:!0},channelTreeList:[],drawerType:"",drawerShow:!1,versionList:[],versionData:{},defaultVersionData:{},addressData:{}}},computed:S(S({},Object(f["c"])(["permissionData"])),{},{checkIsFormStatus:function(){var t=!1;switch(this.operate){case"add":t=!0;break;case"detail":t="detail"!==this.formOperate;break;default:t="detail"!==this.formOperate;break}return t}}),watch:{operate:function(t,e){t||(this.formOperate="detail")}},created:function(){},mounted:function(){this.initLoad()},methods:{initLoad:function(){var t=this;return C(x().mark((function e(){return x().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.initDic();case 2:return t.getLevelList(t.id),t.getPermissionTreeList(t.id),t.getMobileList(t.id),e.next=7,t.getVersionList();case 7:t.id&&"add"!==t.operate&&t.initInfoHandle(),t.operate&&(t.formOperate=t.operate),t.treeData&&"add"!==t.operate&&(t.labelName=t.treeData.name.substring(0,1)),"add"===t.operate&&(t.labelName="朴");case 11:case"end":return e.stop()}}),e)})))()},refreshHandle:function(){this.initLoad()},searchHandle:Object(n["d"])((function(){}),300),getAddress:function(t){this.addressData=this.$refs.cascaderRef.getCheckedNodes()},initInfoHandle:function(){var t,e=this,r=function(){var r=e.infoData[Object(n["b"])(a)];if(r)switch(a){case"industry":e.formData[a]=r.toString();break;case"district":e.formData[a]=JSON.parse(r);break;case"channel_id":null!==r&&(t=e.getParentsById(e.channelTreeList,r),e.formData[a]=t);break;case"faceUpdateTime":var i=!1;e.faceUploadOptions.forEach((function(t){t.value==r&&(i=!0)})),i?e.formData[a]=r:r&&(e.formData[a]="auto",e.formData.customFaceDate=r);break;case"tollVersion":e.formData.tollVersion=r,e.versionData.tollVersion=r||-1;break;case"permission":e.formData.permission=[].concat(v(e.defaultVersionData.permission||[]),v(r));break;case"merchantAppPermission":e.formData.merchantAppPermission=[].concat(v(e.defaultVersionData.merchant_app_permission||[]),v(r));break;case"appPermission":e.formData.appPermission=[].concat(v(e.defaultVersionData.app_permission||[]),v(r));break;default:e.formData[a]=r;break}};for(var a in this.formData)r();var i={tollVersion:this.formData.tollVersion||-1,permission:this.formData.permission||[],merchant_app_permission:this.formData.merchantAppPermission||[],app_permission:this.formData.appPermission||[]};this.versionData=Object(n["f"])(i),this.$store.dispatch("permission/setPermissionData",this.versionData)},deleteEmptyChildren:function(t,e){e=e||"children_list";var r=this;function a(t){t&&t.length&&t.map((function(t){r.checkIsFormStatus?t.isDisabled=!1:t.isDisabled=!0,t[e]&&t[e].length>0?a(t[e]):r.$delete(t,e)}))}return a(t),t},getLevelList:function(t){var e=this;return C(x().mark((function r(){var a,i,s,o,l;return x().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return a={},t&&(a.company_id=t),r.next=4,Object(n["Z"])(e.$apis.apiBackgroundAdminOrganizationGetLevelNameMapPost(a));case 4:if(i=r.sent,s=d(i,2),o=s[0],l=s[1],!o){r.next=11;break}return e.$message.error(o.message),r.abrupt("return");case 11:0===l.code?(e.levelList=l.data,l.data.length>0&&"add"===e.operate&&(e.formData.levelName=l.data[0].name)):e.$message.error(l.msg);case 12:case"end":return r.stop()}}),r)})))()},getPermissionTreeList:function(){var t=this;return C(x().mark((function e(){var r,a,i,s;return x().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(n["Z"])(t.$apis.apiBackgroundAdminOrganizationGetMerchantPermissionsPost());case 2:if(r=e.sent,a=d(r,2),i=a[0],s=a[1],!i){e.next=9;break}return t.$message.error(i.message),e.abrupt("return");case 9:0===s.code?(t.permissionTree=t.deleteEmptyChildren(s.data,"children"),"add"===t.operate&&(t.formData.permission=Object(n["E"])(t.permissionTree,"key"))):t.$message.error(s.msg);case 10:case"end":return e.stop()}}),e)})))()},getMobileList:function(){var t=this;return C(x().mark((function e(){var r,a,i,s;return x().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(n["Z"])(t.$apis.apiBackgroundAdminOrganizationGetMerchantMobilePermissions());case 2:if(r=e.sent,a=d(r,2),i=a[0],s=a[1],!i){e.next=9;break}return t.$message.error(i.message),e.abrupt("return");case 9:0===s.code?(t.mobileTree=t.deleteEmptyChildren(s.data,"children"),"add"===t.operate&&(t.formData.merchantAppPermission=Object(n["E"])(t.mobileTree,"key"))):t.$message.error(s.msg);case 10:case"end":return e.stop()}}),e)})))()},clickSelectPermissionTree:function(t){this.formData.permission=1===t?Object(n["E"])(this.permissionTree,"key"):[]},clickSelectMobileTree:function(t){this.formData.merchantAppPermission=1===t?Object(n["E"])(this.mobileTree,"key"):null},changeOperate:function(){switch(this.operate){case"add":break;default:"detail"===this.formOperate?this.formOperate="modify":this.formOperate="detail";break}this.permissionTree=this.deleteEmptyChildren(this.permissionTree,"children"),this.mobileTree=this.deleteEmptyChildren(this.mobileTree,"children")},cancelFormHandle:function(){"add"===this.operate?this.$refs.organizationFormRef.resetFields():(this.$refs.organizationFormRef.clearValidate(),this.formOperate="detail",this.permissionTree=this.deleteEmptyChildren(this.permissionTree,"children"),this.mobileTree=this.deleteEmptyChildren(this.mobileTree,"children")),this.restoreHandle(this.type,this.formOperate)},generateThirdAppinfo:function(){var t=this;return C(x().mark((function e(){var r,a,i,s;return x().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.loadingThirdInfo=!0,e.next=3,Object(n["Z"])(t.$apis.apiBackgroundAdminOrganizationGenerateThirdAppinfoPost({id:t.id}));case 3:if(r=e.sent,a=d(r,2),i=a[0],s=a[1],t.loadingThirdInfo=!1,!i){e.next=11;break}return t.$message.error(i.message),e.abrupt("return");case 11:0===s.code?(t.formData.thirdAppKey=s.data.third_app_key,t.formData.thirdSecretKey=s.data.third_secret_key):t.$message.error(s.msg);case 12:case"end":return e.stop()}}),e)})))()},sendFormdataHandle:function(){var t=this;this.$refs.organizationFormRef.validate((function(e){e&&("add"===t.operate?t.addRootOrganization(t.formatData()):t.modifyOrganization(t.formatData()))}))},formatData:function(){var t={status:"enable"};for(var e in this.formData){var r=this.formData[e];if(""!==r){switch(e){case"district":r=JSON.stringify(r);break;case"channel_id":if(this.formData.channel_id&&0!==this.formData.channel_id.length){var a=null;if(this.$refs.channelMul){var i=this.$refs.channelMul.getCheckedNodes({leafOnly:!1});i&&i.length>0&&(a=i[0].value)}r=a}else r=null;break;case"password":break;case"refundPassword":break;case"thirdAppUrl":r=encodeURIComponent(r);break;case"faceUpdateTime":r="auto"===r?this.formData.customFaceDate:r;break;case"tollVersion":r=r&&-1!==r?this.formData.tollVersion:null}"levelName"!==e&&"customFaceDate"!==e&&(t[Object(n["b"])(e)]=r)}"modify"===this.formOperate&&(t.company=this.treeData.company)}if(null!==t.channel_id&&0!==t.channel_id.length||delete t.channel_id,this.formData.tollVersion&&-1!==this.formData.tollVersion){var s=this.getDifference(this.formData.permission,this.defaultVersionData.permission);t.permission=Object(n["f"])(s);var o=this.getDifference(this.formData.merchantAppPermission,this.defaultVersionData.merchant_app_permission);t.merchant_app_permission=Object(n["f"])(o);var l=this.getDifference(this.formData.appPermission,this.defaultVersionData.app_permission);t.app_permission=Object(n["f"])(l)}else t.permission=Object(n["f"])(this.formData.permission),t.merchant_app_permission=Object(n["f"])(this.formData.merchantAppPermission),t.app_permission=Object(n["f"])(this.formData.appPermission);return t},getPermissionLevelParent:function(t){var e=this,r=[];function a(t,e,r){for(var i=[],n=0;n<t.length;n++){var s=t[n];if(s.key===e){i=r;break}r.push(e),s.children&&s.children.length>0&&a(s.children,e,r)}return i}return t.forEach((function(t){var i=[],n=a(e.permissionTree,t,i),s=a(e.mobileTree,t,i);r=r.concat(n),r=r.concat(s)})),r},addRootOrganization:function(t){var e=this;return C(x().mark((function r(){var a,i,s,o;return x().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return e.isLoading=!0,r.next=3,Object(n["Z"])(e.$apis.apiBackgroundAdminOrganizationAddRootPost(t));case 3:if(a=r.sent,i=d(a,2),s=i[0],o=i[1],e.isLoading=!1,!s){r.next=11;break}return e.$message.error(s.message),r.abrupt("return");case 11:0===o.code?(e.$message.success("添加成功"),e.restoreHandle(e.type,e.formOperate)):e.$message.error(o.msg);case 12:case"end":return r.stop()}}),r)})))()},modifyOrganization:function(t){var e=this;return C(x().mark((function r(){var a,i,s,o;return x().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return e.isLoading=!0,r.next=3,Object(n["Z"])(e.$apis.apiBackgroundAdminOrganizationModifyPost(t));case 3:if(a=r.sent,i=d(a,2),s=i[0],o=i[1],e.isLoading=!1,!s){r.next=11;break}return e.$message.error(s.message),r.abrupt("return");case 11:0===o.code?(e.$message.success("修改成功"),e.formOperate="detail",e.restoreHandle(e.type,e.formOperate)):e.$message.error(o.msg);case 12:case"end":return r.stop()}}),r)})))()},gotoLogin:function(){if(this.infoData.login_token){var t=document.createElement("a");t.href=location.origin+"/#/login?token="+this.infoData.login_token,t.target="_blank",t.click(),t=null}else this.$message.error("无法获取token!")},initDic:function(){var t=this;return C(x().mark((function e(){var r;return x().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.getChannelTreeList();case 2:r=e.sent,Array.isArray(r)&&r.length>0&&(t.channelTreeList=Object(n["f"])(r));case 4:case"end":return e.stop()}}),e)})))()},getChannelTreeList:function(){var t=this;return new Promise((function(e){t.$apis.apiBackgroundAdminChannelTreeListPost().then((function(t){if(Reflect.has(t,"code")&&0===t.code){var r=t.data||{};Object(n["T"])(r.results,"children_list"),e(r.results)}e([])})).catch((function(t){e([])}))}))},getParentsById:function(t,e){for(var r in t){if(t[r].id===e)return[t[r].id];if(t[r].children_list){var a=this.getParentsById(t[r].children_list,e);if(void 0!==a)return a.unshift(t[r].id),a}}},showDrawer:function(t){-1===this.versionData.tollVersion&&this.selectThisVersion(null,!1),this.drawerType=t,this.drawerShow=!0},getVersionList:function(){var t=this;return C(x().mark((function e(){return x().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.apiBackgroundAdminBackgroundTollVersionListPost({page:1,page_size:9999}).then((function(e){if(0===e.code){t.versionList=Object(n["f"])(e.data.results)||[];var r=t.versionList.filter((function(e){return e.id===t.infoData.toll_version}));t.$store.dispatch("permission/setVersionPermissionData",r[0]),t.defaultVersionData=Object(n["f"])(r[0]||[])}else t.$message.error(e.msg)}));case 2:case"end":return e.stop()}}),e)})))()},selectThisVersion:function(t,e){this.formData.tollVersion=t?t.id:-1,this.formData.permission=t?t.permission:e?[]:this.formData.permission,this.formData.appPermission=t?t.app_permission:e?[]:this.formData.appPermission,this.formData.merchantAppPermission=t?t.merchant_app_permission:e?[]:this.formData.merchantAppPermission;var r={tollVersion:t?t.id:-1,permission:t?t.permission:e?[]:this.formData.permission,app_permission:t?t.app_permission:e?[]:this.formData.appPermission,merchant_app_permission:t?t.merchant_app_permission:e?[]:this.formData.merchantAppPermission};this.versionData=Object(n["f"])(r),this.defaultVersionData=Object(n["f"])(r),this.$store.dispatch("permission/setVersionPermissionData",r),this.$store.dispatch("permission/setPermissionData",this.versionData)},refreshPermission:function(t){var e=this.removeDuplicates(t);"merchant"===this.drawerType?(this.versionData.permission=Object(n["f"])(e),this.formData.permission=Object(n["f"])(e)):(this.versionData.merchant_app_permission=Object(n["f"])(e),this.formData.merchantAppPermission=Object(n["f"])(e)),this.$store.dispatch("permission/setPermissionData",this.versionData)},getDifference:function(t,e){var r=t.filter((function(t){return!e.includes(t)})),a=e.filter((function(e){return!t.includes(e)}));return r.concat(a)},removeDuplicates:function(t){return v(new Set(t))},openMessageBox:function(){var t=this;this.$confirm("此操作将当前组织地址同步到所有未填写地址的下级, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.synchronizeToSubordinates()})).catch((function(){t.$message({type:"info",message:"已取消同步到下级"})}))},synchronizeToSubordinates:function(){var t=this;this.$apis.apiBackgroundAdminOrganizationSynchronizationOrgDistrictPost({organization_id:this.formData.id,district:JSON.stringify(this.formData.district)}).then((function(e){0===e.code?t.$message.success("同步成功"):t.$message.error(e.msg)}))}}},T=P,j=(r("8ccd"),r("2877")),I=Object(j["a"])(T,a,i,!1,null,null,null);e["default"]=I.exports},f862:function(t,e,r){t.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}}}]);