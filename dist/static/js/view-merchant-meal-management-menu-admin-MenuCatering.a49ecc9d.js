(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-menu-admin-MenuCatering","view-merchant-meal-management-menu-admin-constants"],{1062:function(t,e,o){"use strict";o.r(e),o.d(e,"recentSevenDay",(function(){return r})),o.d(e,"MENUCATERINGFOOD",(function(){return n})),o.d(e,"MENUCATERINGSETMEAL",(function(){return a}));var i=o("5a0c"),r=[i().subtract(7,"day").format("YYYY-MM-DD"),i().format("YYYY-MM-DD")],n={name:{type:"input",label:"菜品名称",value:"",placeholder:""},count_type:{type:"select",label:"菜品价格类型",value:"",placeholder:"",labelWidth:"125px",dataList:[{label:"菜品价格",value:1},{label:"称重价格",value:2},{label:"菜品价格+称重价格",value:3}]},label_filter:{type:"select",label:"",value:"",placeholder:"",dataList:[{label:"请选择",value:"select",disabled:!0},{label:"不包含",value:"Exclude"},{label:"等于",value:"Equal"}]},label_list:{type:"treeselect",label:"",value:[],placeholder:"选择标签",multiple:!0,limit:1,level:1,valueConsistsOf:"LEAF_PRIORITY",normalizer:function(t){return{id:t.id,label:t.name,children:t.label_list}},dataList:[]}},a={name:{type:"input",label:"套餐名称",value:"",placeholder:""}}},"1eed":function(t,e,o){"use strict";o.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"container-wrapper has-organization"},[e("refresh-tool",{on:{refreshPage:t.refreshHandle}}),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],attrs:{id:"menu-catering"}},[e("div",{staticClass:"organization-tree"},[e("el-input",{staticClass:"tree-search ps-input",attrs:{placeholder:"请选择分类"},model:{value:t.treeFilterText,callback:function(e){t.treeFilterText=e},expression:"treeFilterText"}}),e("div",{class:["all-tree",t.searchForm.category_id?"":"is-current"],on:{click:function(e){return t.treeHandleNodeClick("","all")}}},[t._m(0),e("span",[t._v(t._s("food"===t.tabType?"全部菜品":"全部套餐"))]),"food"===t.tabType?e("span",[t._v("（已选"+t._s(t.selectFoodIds.length)+"）")]):t._e(),"setMeal"===t.tabType?e("span",[t._v("（已选"+t._s(t.setMealSelectFoodIds.length)+"）")]):t._e()]),e("el-tree",{directives:[{name:"loading",rawName:"v-loading",value:t.treeLoading,expression:"treeLoading"}],ref:"tree",class:{"tree-box":t.searchForm.category_id},attrs:{data:t.treeList,props:t.treeProps,"filter-node-method":t.filterTreeNode,"check-on-click-node":!0,"default-expand-all":!0,"highlight-current":!0,"current-node-key":t.searchForm.category_id,"node-key":"id"},on:{"node-click":t.treeHandleNodeClick},scopedSlots:t._u([{key:"default",fn:function(o){var i=o.node,r=o.data;return e("span",{staticClass:"custom-tree-node"},[e("span",{staticClass:"ellipsis tree-lable"},[t._v(t._s(i.label))]),e("span",{staticStyle:{color:"#fda04d"}},[r.number?e("span",[t._v("（已选"+t._s(r.number)+"）")]):t._e()])])}}])})],1),e("div",{staticClass:"account-list"},[e("search-form",{ref:"searchRef",staticClass:"search-lt-shadow",attrs:{"form-setting":t.searchFormSetting},on:{search:t.searchHandle}},[e("template",{slot:"perv"},[e("div",{staticClass:"tab"},[e("div",{class:["tab-item","food"===t.tabType?"active":""],on:{click:function(e){return t.tabClick("food")}}},[t._v(" 菜品 ")]),e("div",{class:["tab-item","setMeal"===t.tabType?"active":""],on:{click:function(e){return t.tabClick("setMeal")}}},[t._v(" 套餐 ")])])])],2),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("数据列表")]),e("div",{staticClass:"align-r"},[e("button-icon",{attrs:{color:"plain",type:"Import"},on:{click:function(e){return t.importHandler("import")}}},[t._v(" 导入菜品/商品 ")])],1)]),e("div",{staticClass:"table-content ps-flex-align-c flex-align-c"},[e("el-checkbox",{staticClass:"ps-checkbox",on:{change:t.setMealhandleCheckChange},model:{value:t.setMealSelectAll,callback:function(e){t.setMealSelectAll=e},expression:"setMealSelectAll"}},[t._v(" 全选 ")]),-1===t.setMealAllValue?e("el-select",{staticClass:"ps-select",staticStyle:{width:"120px",margin:"0 10px"},attrs:{"popper-class":"ps-popper-select",placeholder:"请选择",disabled:!t.setMealSelectAll},model:{value:t.setMealAllValue,callback:function(e){t.setMealAllValue=e},expression:"setMealAllValue"}},[e("el-option",{attrs:{label:"无数量限制",value:-1}}),e("el-option",{attrs:{label:"限制数量",value:0}})],1):e("el-input",{staticStyle:{width:"120px",margin:"0 10px"},attrs:{size:"small",disabled:!t.setMealSelectAll},model:{value:t.setMealAllValue,callback:function(e){t.setMealAllValue=t._n(e)},expression:"setMealAllValue"}},[e("i",{staticClass:"el-input__icon el-icon-circle-close",staticStyle:{cursor:"pointer"},attrs:{slot:"suffix"},on:{click:function(e){t.setMealAllValue=-1}},slot:"suffix"})]),e("span",[t._v("限制单人可点数量：")]),e("el-input",{staticStyle:{width:"100px",margin:"0 10px"},attrs:{disabled:!t.setMealSelectAll,size:"small"},model:{value:t.setMealAllFoodBuyLimitNum,callback:function(e){t.setMealAllFoodBuyLimitNum=t._n(e)},expression:"setMealAllFoodBuyLimitNum"}}),e("el-button",{staticClass:"ps-origin-btn",attrs:{disabled:!t.setMealSelectAll,size:"small",type:"primary"},on:{click:function(e){return t.setMealSetAll(t.setMealSelectAll)}}},[t._v(" 确定 ")]),"setMeal"===t.tabType?e("div",{staticClass:"p-l-20"},[e("el-checkbox",{staticClass:"ps-checkbox",model:{value:t.setMealFoodDisplay,callback:function(e){t.setMealFoodDisplay=e},expression:"setMealFoodDisplay"}},[t._v(" 套餐中包含菜品不单独显示 ")])],1):t._e(),t.isNutritionGuidance&&"food"===t.tabType?e("div",{staticClass:"p-l-20"},[e("i",{staticClass:"el-icon-success",staticStyle:{color:"#5dbf6e"}}),e("span",[t._v("推荐菜品")])]):t._e(),t.isNutritionGuidance&&"food"===t.tabType?e("div",{staticClass:"p-l-20"},[e("i",{staticClass:"el-icon-warning",staticStyle:{color:"#fd953c"}}),e("span",[t._v("不推荐菜品")])]):t._e()],1),e("div",{staticClass:"table-content",class:{"ps-flex-bw":t.isNutritionGuidance&&"food"===t.tabType}},[e("div",{class:{"table-box":t.isNutritionGuidance&&"food"===t.tabType}},[e("el-table",{ref:"tableData",staticClass:"ps-table",staticStyle:{width:"100%"},attrs:{data:t.tableData.slice((t.currentPage-1)*t.pageSize,t.currentPage*t.pageSize),height:"550","row-key":"id",stripe:"","header-row-class-name":"ps-table-header-row"},on:{select:t.handleSelectionSelect,"select-all":t.handleSelectionAll}},[e("el-table-column",{attrs:{type:"selection",width:"55","class-name":"ps-checkbox",align:"center","reserve-selection":!0}}),e("el-table-column",{attrs:{prop:"name",label:"菜品名",align:"center"},scopedSlots:t._u([{key:"default",fn:function(o){return[o.row.recommend_food?e("i",{staticClass:"el-icon-success",staticStyle:{color:"#5dbf6e"}}):t._e(),0===o.row.recommend_food?e("i",{staticClass:"el-icon-warning",staticStyle:{color:"#fd953c"}}):t._e(),e("div",{style:{color:o.row.food_nutrition_status?"red":""}},[t._v(" "+t._s(o.row.name)+" "),"food"===t.tabType?e("span",[t._v("("+t._s(o.row.weight)+")g")]):t._e()])]}}])}),"food"===t.tabType?e("el-table-column",{key:"ingredient_info",attrs:{prop:"ingredient_info",label:"食材组成",align:"center",width:"200"},scopedSlots:t._u([{key:"default",fn:function(o){return[o.row.ingredient_info&&o.row.ingredient_info.length?e("div",{staticClass:"ps-flex-align-c flex-wrap flex-justify-c",style:{color:o.row.food_nutrition_status?"red":""}},t._l(o.row.ingredient_info,(function(i,r){return e("div",{key:r},[e("span",{staticClass:"p-t-10"},[t._v(" "+t._s(r===o.row.ingredient_info.length-1&&i.ingredient_name||i.ingredient_name+"、")+" ")])])})),0):t._e()]}}],null,!1,2093952214)}):t._e(),"setMeal"===t.tabType?e("el-table-column",{key:"ingredient_info",attrs:{prop:"ingredient_info",label:"菜品组成",align:"center",width:"200"},scopedSlots:t._u([{key:"default",fn:function(o){return[e("div",[t._v(t._s(o.row.food_group_name))])]}}],null,!1,1225055979)}):t._e(),e("el-table-column",{attrs:{prop:"mobile",label:"营养元素",align:"center",width:"200"},scopedSlots:t._u([{key:"default",fn:function(o){return[e("div",{staticClass:"ps-flex-align-c flex-wrap flex-justify-c",style:{color:o.row.food_nutrition_status?"red":""}},[t._v(" "+t._s(o.row.ingredient_category_count+"类")+"/ "),t._l(t.mainNutritionList,(function(i,r){return e("div",{key:r},[t._v(" "+t._s(r===t.mainNutritionList.length-1&&i.name+":"+o.row.main_nutrition[i.key]+i.unit||i.name+":"+o.row.main_nutrition[i.key]+i.unit+"/")+" ")])}))],2)]}}])}),e("el-table-column",{attrs:{prop:"settingStocksNum",label:"库存",align:"center",width:"150"},scopedSlots:t._u([{key:"default",fn:function(o){return[-1===o.row.settingStocksNum?e("el-select",{staticClass:"ps-select",staticStyle:{width:"120px"},attrs:{"popper-class":"ps-popper-select",placeholder:"请选择",disabled:t.tableDisableSelect(o.row)},on:{change:function(e){return t.changeFoodNum(o.row)}},model:{value:o.row.settingStocksNum,callback:function(e){t.$set(o.row,"settingStocksNum",e)},expression:"scope.row.settingStocksNum"}},[e("el-option",{attrs:{label:"无数量限制",value:-1}}),e("el-option",{attrs:{label:"限制数量",value:0}})],1):e("el-input",{staticStyle:{width:"120px",margin:"0 10px"},attrs:{size:"small",disabled:t.tableDisableSelect(o.row)},on:{change:function(e){return t.changeFoodNum(o.row)}},model:{value:o.row.settingStocksNum,callback:function(e){t.$set(o.row,"settingStocksNum",e)},expression:"scope.row.settingStocksNum"}},[e("i",{staticClass:"el-input__icon el-icon-circle-close",staticStyle:{cursor:"pointer"},attrs:{slot:"suffix"},on:{click:function(e){return t.inputClearStock(o.row)}},slot:"suffix"})])]}}])}),e("el-table-column",{attrs:{prop:"selectedFoodBuyLimitNum",label:"限制单人可点数量",align:"center",width:"150"},scopedSlots:t._u([{key:"default",fn:function(o){return[e("el-input",{staticStyle:{width:"100px",margin:"0 10px"},attrs:{size:"small",disabled:t.tableDisableSelect(o.row)},on:{change:function(e){return t.changeFoodBuyLimitNum(o.row)}},model:{value:o.row.selectedFoodBuyLimitNum,callback:function(e){t.$set(o.row,"selectedFoodBuyLimitNum",e)},expression:"scope.row.selectedFoodBuyLimitNum"}})]}}])})],1),e("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[e("el-pagination",{staticClass:"ps-text",attrs:{"current-page":t.currentPage,"page-size":t.pageSize,layout:"prev, pager, next, total, jumper",total:t.tableData.length,background:"","popper-class":"ps-popper-select"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1),t.isNutritionGuidance&&"food"===t.tabType?e("div",{staticClass:"nutrition-box"},[e("div",[t._v("营养摄入统计")]),e("div",{staticClass:"percent_box"},[e("el-progress",{staticClass:"percent",attrs:{type:"circle",percentage:t.totleNutritionInfo.percentsEnergyKcal>=100?100:t.totleNutritionInfo.percentsEnergyKcal,color:"#ff9b45","stroke-width":7,"show-text":!1}}),e("div",{staticClass:"text_box"},[e("div",{staticClass:"text"},[e("span",{staticStyle:{color:"#ff9b45"}},[t._v(t._s(t.formatNumberWithUnit(t.totleNutritionInfo.reportEnergyKcal)))]),t._v(" /"+t._s(t.formatNumberWithUnit(t.needNutritionInfo.energy_kcal))+" ")]),e("div",{staticClass:"tips"},[t._v("总能量kcal")])])],1),e("div",{staticClass:"analysis-box"},[e("div",{staticClass:"analysis-strip"},[e("span",{staticClass:"tag",style:{backgroundColor:"".concat(t.percentColorName(t.totleNutritionInfo.percentsCarbohydrate).color)}},[t._v(" "+t._s(t.percentColorName(t.totleNutritionInfo.percentsCarbohydrate).name)+" ")]),e("span",{staticClass:"tips"},[t._v("总碳水")]),e("el-progress",{staticClass:"progress-content",attrs:{percentage:t.totleNutritionInfo.percentsCarbohydrate>=100?100:t.totleNutritionInfo.percentsCarbohydrate,"show-text":!1,color:t.percentColorName(t.totleNutritionInfo.percentsCarbohydrate).color}}),e("span",{staticClass:"progress-data"},[e("span",[t._v(t._s(t.totleNutritionInfo.reportCarbohydrate.toFixed(1)))]),t._v(" /"+t._s(t.needNutritionInfo.carbohydrate)+"g ")])],1),e("div",{staticClass:"analysis-strip"},[e("span",{staticClass:"tag",style:{backgroundColor:"".concat(t.percentColorName(t.totleNutritionInfo.percentsProtein).color)}},[t._v(" "+t._s(t.percentColorName(t.totleNutritionInfo.percentsProtein).name)+" ")]),e("span",{staticClass:"tips"},[t._v("总蛋白质")]),e("el-progress",{staticClass:"progress-content",attrs:{percentage:t.totleNutritionInfo.percentsProtein>=100?100:t.totleNutritionInfo.percentsProtein,"show-text":!1,color:t.percentColorName(t.totleNutritionInfo.percentsProtein).color}}),e("span",{staticClass:"progress-data"},[e("span",[t._v(t._s(t.totleNutritionInfo.reportProtein.toFixed(1)))]),t._v(" /"+t._s(t.needNutritionInfo.protein)+"g ")])],1),e("div",{staticClass:"analysis-strip"},[e("span",{staticClass:"tag",style:{backgroundColor:"".concat(t.percentColorName(t.totleNutritionInfo.percentsAxunge).color)}},[t._v(" "+t._s(t.percentColorName(t.totleNutritionInfo.percentsAxunge).name)+" ")]),e("span",{staticClass:"tips"},[t._v("总脂肪")]),e("el-progress",{staticClass:"progress-content",attrs:{percentage:t.totleNutritionInfo.percentsAxunge>=100?100:t.totleNutritionInfo.percentsAxunge,color:t.percentColorName(t.totleNutritionInfo.percentsAxunge).color,"show-text":!1}}),e("span",{staticClass:"progress-data"},[e("span",[t._v(t._s(t.totleNutritionInfo.reportAxunge.toFixed(1)))]),t._v(" /"+t._s(t.needNutritionInfo.axunge)+"g ")])],1)]),e("div",{staticClass:"title-table"},[t._v("食物多样性")]),t.totleNutritionInfo.foodDiversityNutrition?e("el-table",{ref:"progressTableData",staticClass:"ps-table",staticStyle:{width:"100%"},attrs:{data:t.totleNutritionInfo.foodDiversityNutrition,stripe:"","header-row-class-name":"ps-table-header-row"}},[e("el-table-column",{attrs:{prop:"name",label:"类别",align:"center"}}),e("el-table-column",{attrs:{prop:"needValue",label:"推荐摄入量",align:"center"}}),e("el-table-column",{attrs:{prop:"value",label:"当前摄入量",align:"center"}})],1):t._e()],1):t._e()]),"food"===t.tabType?e("div",{staticClass:"p-r-20 p-l-20"},[e("span",{staticClass:"p-r-20"},[t._v("已选菜品："+t._s(t.selectFoodIds.length))]),e("span",[t._v("总人数："+t._s(t.collectiveNumber)+"人")])]):t._e(),e("div",{staticClass:"ps-flex-bw p-t-10 p-r-20 p-l-20 p-b-40"},["food"===t.tabType?e("div",{staticStyle:{flex:"1"}},[e("span",[t._v("菜品提醒：")]),t._l(t.foodsTips,(function(o,i){return e("el-tag",{key:i,staticClass:"m-r-10 m-b-10",attrs:{size:"small",type:"warning"}},[e("el-popover",{attrs:{placement:"top",trigger:"hover"}},[e("div",[t._v(" "+t._s(o.tips)+" ")]),e("i",{staticClass:"el-icon-warning ps-i",attrs:{slot:"reference"},slot:"reference"})]),t._v(" "+t._s(o.name)+" ")],1)}))],2):t._e(),"setMeal"===t.tabType?e("div",{staticClass:"p-r-20 p-l-20"},[e("span",{staticClass:"p-r-20"},[t._v("已选菜品："+t._s(t.selectFoodIds.length))]),e("span",[t._v("总人数："+t._s(t.collectiveNumber)+"人")])]):t._e(),e("div",[e("el-button",{staticClass:"ps-cancel-btn",attrs:{size:"small"},on:{click:t.closeHandler}},[t._v("返 回")]),e("el-button",{staticClass:"ps-btn",attrs:{size:"small",type:"primary"},on:{click:function(e){return t.confirmEditMeal("determine")}}},[t._v(" 确 定 ")]),t.showMealContinue()?e("el-button",{staticClass:"ps-green-btn",attrs:{size:"small",type:"primary"},on:{click:function(e){return t.confirmEditMeal("continue")}}},[t._v(" 继续配餐 ")]):t._e()],1)])])],1)])],1)},r=[function(){var t=this,e=t._self._c;return e("i",{staticClass:"tree-search-icon"},[e("img",{attrs:{src:o("25a5"),alt:""}})])}],n=o("ed08"),a=o("1062"),s=o("7676"),l=o.n(s),c=o("da92");function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function d(t,e){var o=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),o.push.apply(o,i)}return o}function f(t){for(var e=1;e<arguments.length;e++){var o=null!=arguments[e]?arguments[e]:{};e%2?d(Object(o),!0).forEach((function(e){p(t,e,o[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):d(Object(o)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(o,e))}))}return t}function p(t,e,o){return(e=h(e))in t?Object.defineProperty(t,e,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[e]=o,t}function h(t){var e=m(t,"string");return"symbol"==u(e)?e:e+""}function m(t,e){if("object"!=u(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var i=o.call(t,e||"default");if("object"!=u(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function g(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */g=function(){return e};var t,e={},o=Object.prototype,i=o.hasOwnProperty,r=Object.defineProperty||function(t,e,o){t[e]=o.value},n="function"==typeof Symbol?Symbol:{},a=n.iterator||"@@iterator",s=n.asyncIterator||"@@asyncIterator",l=n.toStringTag||"@@toStringTag";function c(t,e,o){return Object.defineProperty(t,e,{value:o,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,o){return t[e]=o}}function d(t,e,o,i){var n=e&&e.prototype instanceof v?e:v,a=Object.create(n.prototype),s=new T(i||[]);return r(a,"_invoke",{value:x(t,o,s)}),a}function f(t,e,o){try{return{type:"normal",arg:t.call(e,o)}}catch(t){return{type:"throw",arg:t}}}e.wrap=d;var p="suspendedStart",h="suspendedYield",m="executing",y="completed",b={};function v(){}function _(){}function F(){}var S={};c(S,a,(function(){return this}));var N=Object.getPrototypeOf,L=N&&N(N(A([])));L&&L!==o&&i.call(L,a)&&(S=L);var M=F.prototype=v.prototype=Object.create(S);function k(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function o(r,n,a,s){var l=f(t[r],t,n);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==u(d)&&i.call(d,"__await")?e.resolve(d.__await).then((function(t){o("next",t,a,s)}),(function(t){o("throw",t,a,s)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return o("throw",t,a,s)}))}s(l.arg)}var n;r(this,"_invoke",{value:function(t,i){function r(){return new e((function(e,r){o(t,i,e,r)}))}return n=n?n.then(r,r):r()}})}function x(e,o,i){var r=p;return function(n,a){if(r===m)throw Error("Generator is already running");if(r===y){if("throw"===n)throw a;return{value:t,done:!0}}for(i.method=n,i.arg=a;;){var s=i.delegate;if(s){var l=C(s,i);if(l){if(l===b)continue;return l}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if(r===p)throw r=y,i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);r=m;var c=f(e,o,i);if("normal"===c.type){if(r=i.done?y:h,c.arg===b)continue;return{value:c.arg,done:i.done}}"throw"===c.type&&(r=y,i.method="throw",i.arg=c.arg)}}}function C(e,o){var i=o.method,r=e.iterator[i];if(r===t)return o.delegate=null,"throw"===i&&e.iterator.return&&(o.method="return",o.arg=t,C(e,o),"throw"===o.method)||"return"!==i&&(o.method="throw",o.arg=new TypeError("The iterator does not provide a '"+i+"' method")),b;var n=f(r,e.iterator,o.arg);if("throw"===n.type)return o.method="throw",o.arg=n.arg,o.delegate=null,b;var a=n.arg;return a?a.done?(o[e.resultName]=a.value,o.next=e.nextLoc,"return"!==o.method&&(o.method="next",o.arg=t),o.delegate=null,b):a:(o.method="throw",o.arg=new TypeError("iterator result is not an object"),o.delegate=null,b)}function I(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(I,this),this.reset(!0)}function A(e){if(e||""===e){var o=e[a];if(o)return o.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,n=function o(){for(;++r<e.length;)if(i.call(e,r))return o.value=e[r],o.done=!1,o;return o.value=t,o.done=!0,o};return n.next=n}}throw new TypeError(u(e)+" is not iterable")}return _.prototype=F,r(M,"constructor",{value:F,configurable:!0}),r(F,"constructor",{value:_,configurable:!0}),_.displayName=c(F,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===_||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,F):(t.__proto__=F,c(t,l,"GeneratorFunction")),t.prototype=Object.create(M),t},e.awrap=function(t){return{__await:t}},k(w.prototype),c(w.prototype,s,(function(){return this})),e.AsyncIterator=w,e.async=function(t,o,i,r,n){void 0===n&&(n=Promise);var a=new w(d(t,o,i,r),n);return e.isGeneratorFunction(o)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},k(M),c(M,l,"Generator"),c(M,a,(function(){return this})),c(M,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),o=[];for(var i in e)o.push(i);return o.reverse(),function t(){for(;o.length;){var i=o.pop();if(i in e)return t.value=i,t.done=!1,t}return t.done=!0,t}},e.values=A,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(E),!e)for(var o in this)"t"===o.charAt(0)&&i.call(this,o)&&!isNaN(+o.slice(1))&&(this[o]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var o=this;function r(i,r){return s.type="throw",s.arg=e,o.next=i,r&&(o.method="next",o.arg=t),!!r}for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n],s=a.completion;if("root"===a.tryLoc)return r("end");if(a.tryLoc<=this.prev){var l=i.call(a,"catchLoc"),c=i.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return r(a.catchLoc,!0);if(this.prev<a.finallyLoc)return r(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return r(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return r(a.finallyLoc)}}}},abrupt:function(t,e){for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o];if(r.tryLoc<=this.prev&&i.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var n=r;break}}n&&("break"===t||"continue"===t)&&n.tryLoc<=e&&e<=n.finallyLoc&&(n=null);var a=n?n.completion:{};return a.type=t,a.arg=e,n?(this.method="next",this.next=n.finallyLoc,b):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),b},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var o=this.tryEntries[e];if(o.finallyLoc===t)return this.complete(o.completion,o.afterLoc),E(o),b}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var o=this.tryEntries[e];if(o.tryLoc===t){var i=o.completion;if("throw"===i.type){var r=i.arg;E(o)}return r}}throw Error("illegal catch attempt")},delegateYield:function(e,o,i){return this.delegate={iterator:A(e),resultName:o,nextLoc:i},"next"===this.method&&(this.arg=t),b}},e}function y(t,e){return S(t)||F(t,e)||v(t,e)||b()}function b(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function v(t,e){if(t){if("string"==typeof t)return _(t,e);var o={}.toString.call(t).slice(8,-1);return"Object"===o&&t.constructor&&(o=t.constructor.name),"Map"===o||"Set"===o?Array.from(t):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?_(t,e):void 0}}function _(t,e){(null==e||e>t.length)&&(e=t.length);for(var o=0,i=Array(e);o<e;o++)i[o]=t[o];return i}function F(t,e){var o=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=o){var i,r,n,a,s=[],l=!0,c=!1;try{if(n=(o=o.call(t)).next,0===e){if(Object(o)!==o)return;l=!1}else for(;!(l=(i=n.call(o)).done)&&(s.push(i.value),s.length!==e);l=!0);}catch(t){c=!0,r=t}finally{try{if(!l&&null!=o.return&&(a=o.return(),Object(a)!==a))return}finally{if(c)throw r}}return s}}function S(t){if(Array.isArray(t))return t}function N(t,e,o,i,r,n,a){try{var s=t[n](a),l=s.value}catch(t){return void o(t)}s.done?e(l):Promise.resolve(l).then(i,r)}function L(t){return function(){var e=this,o=arguments;return new Promise((function(i,r){var n=t.apply(e,o);function a(t){N(n,i,r,a,s,"next",t)}function s(t){N(n,i,r,a,s,"throw",t)}a(void 0)}))}}var M={name:"MenuCatering",inject:["reload"],data:function(){return{tabType:"food",treeLoading:!1,treeList:[],treeFilterText:"",treeProps:{children:"food_category",label:"name"},searchForm:{category_id:""},checkList:[],tableData:[],pageSize:8,totalCount:0,totalPageSize:0,currentPage:1,isLoading:!1,searchFormSetting:Object(n["f"])(a["MENUCATERINGFOOD"]),setMealSelectAll:!1,setMealAllValue:-1,setMealAllFoodBuyLimitNum:"",progressTableData:[],isNutritionGuidance:!1,currentEditData:{},selectFoodIds:[],allFoodList:{},setMealSelectFoodIds:[],setMealAllFoodList:{},setMealFoodDisplay:!1,regNum:/^\d+$/,foodsTips:[],mainNutritionList:[{name:"能量",key:"energy_kcal",value:0,unit:"kcal"},{name:"脂肪",key:"axunge",value:0,unit:"g"},{name:"碳水化物",key:"carbohydrate",value:0,unit:"g"},{name:"蛋白质",key:"protein",value:0,unit:"g"}],setMealList:[],foodsList:[],collectiveNumber:0,needNutritionInfo:{},mealDailyData:{},menuId:this.$route.query.menuId,menuType:this.$route.query.menuType,currentEditDate:this.$route.query.currentEditDate,currentEditMealType:this.$route.query.currentEditMealType,analysisFoodId:"",isFirst:!0}},computed:{totleNutritionInfo:function(){return this.inNutrition()},selectFoodInfoList:function(){var t=this,e=[];return this.tableData.forEach((function(o){t.selectFoodIds.includes(o.id)&&e.push(o)})),e}},watch:{treeFilterText:function(t){this.$refs.tree.filter(t)}},created:function(){this.$route.query.food_id&&(this.analysisFoodId=this.$route.query.food_id),"true"===this.$route.query.isNutritionGuidance&&(this.isNutritionGuidance=!0,this.getMealTypeNutrition()),this.mealDailyData=JSON.parse(sessionStorage.getItem("mealDailyData")),this.currentEditData=this.$decodeQuery(this.$route.query.data),"week"===this.menuType&&this.currentEditData.id?this.getWeeklyFoodDetail():(this.initSelectData(),this.initSetMealSelectData()),this.initLoad()},mounted:function(){},methods:{getWeeklyFoodDetail:function(){var t=this;return L(g().mark((function e(){var o,i,r,a;return g().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(n["Z"])(t.$apis.apiBackgroundFoodMenuWeeklyWeeklyFoodDetailPost({id:t.currentEditData.id}));case 2:if(o=e.sent,i=y(o,2),r=i[0],a=i[1],!r){e.next=9;break}return t.$message.error(r.message),e.abrupt("return");case 9:0===a.code?(t.currentEditData=a.data,t.initSelectData(),t.initSetMealSelectData()):t.$message.error(a.msg);case 10:case"end":return e.stop()}}),e)})))()},inNutrition:function(){var t={reportEnergyKcal:0,reportCarbohydrate:0,reportProtein:0,reportAxunge:0,percentsEnergyKcal:0,percentsCarbohydrate:0,percentsProtein:0,percentsAxunge:0,foodDiversityNutrition:[{name:"谷物",needValue:this.needNutritionInfo.cereals,value:0,key:"cereals"},{name:"鱼禽肉蛋",needValue:this.needNutritionInfo.eggsandmeat,value:0,key:"eggsandmeat"},{name:"水果",needValue:this.needNutritionInfo.fruit,value:0,key:"fruit"},{name:"蔬菜",needValue:this.needNutritionInfo.vegetable,value:0,key:"vegetable"}]};return this.selectFoodInfoList&&this.selectFoodInfoList.length&&(this.selectFoodInfoList.forEach((function(e){var o=e.settingStocksNum>0?e.settingStocksNum:0;t.reportEnergyKcal=c["a"].plus(t.reportEnergyKcal,e.main_nutrition.energy_kcal*o*e.weight/100),t.reportAxunge=c["a"].plus(t.reportAxunge,e.main_nutrition.axunge*o*e.weight/100),t.reportCarbohydrate=c["a"].plus(t.reportCarbohydrate,e.main_nutrition.carbohydrate*o*e.weight/100),t.reportProtein=c["a"].plus(t.reportProtein,e.main_nutrition.protein*o*e.weight/100),t.foodDiversityNutrition.forEach((function(t){t.value+=parseInt(e.food_diversity_nutrition[t.key]*o)}))})),Object.keys(this.needNutritionInfo)&&Object.keys(this.needNutritionInfo).length&&(t.percentsEnergyKcal=parseInt(t.reportEnergyKcal/this.needNutritionInfo.energy_kcal*100),t.percentsCarbohydrate=parseInt(t.reportCarbohydrate/this.needNutritionInfo.carbohydrate*100),t.percentsProtein=parseInt(t.reportProtein/this.needNutritionInfo.protein*100),t.percentsAxunge=parseInt(t.reportAxunge/this.needNutritionInfo.axunge*100))),t},percentColorName:function(t){var e={color:"",name:""};return parseInt(t)>=120?(e.color="#ea5b55",e.name="过量"):parseInt(t)>=80?(e.color="#5dbf6e",e.name="适宜"):parseInt(t)<80&&(e.color="#e89e42",e.name="不足"),e},initLoad:function(){var t=this;this.$nextTick((function(){t.$refs.tableData.clearSelection()})),"food"===this.tabType?(this.searchFormSetting=Object(n["f"])(a["MENUCATERINGFOOD"]),this.getMenuFoodSortList(),this.getAllLabelGroupList()):(this.searchFormSetting=Object(n["f"])(a["MENUCATERINGSETMEAL"]),this.getSetMealCategoryList())},initFunLoad:function(){var t=this;this.currentPage=1,this.$nextTick((function(){t.$refs.tableData.clearSelection()})),this.searchForm.category_id?this.resetTreeFoodNumber(this.searchForm.category_id):this.resetTreeFoodNumber(),"food"===this.tabType?this.getMenuFoodList():this.setMealAllSetMealList()},initSelectData:function(){this.currentEditData&&this.currentEditData.food_data&&(this.selectFoodIds=this.currentEditData.food_data.map((function(t){return t.id}))),this.getMenuFoodList()},initSetMealSelectData:function(){this.currentEditData&&this.currentEditData.set_meal_data&&(this.setMealSelectFoodIds=this.currentEditData.set_meal_data.map((function(t){return t.id}))),this.setMealFoodDisplay=this.currentEditData.set_meal_food_display,this.setMealAllSetMealList()},setCurrentHasFoodIds:function(t){var e=this.selectFoodIds.filter((function(e){return t.includes(e)}));this.selectFoodIds=e},setCurrentHasMealIds:function(t){var e=this.setMealSelectFoodIds.filter((function(e){return t.includes(e)}));this.setMealSelectFoodIds=e},refreshHandle:function(){var t=this;this.$refs.searchRef.resetForm(),this.searchForm.category_id="",this.currentPage=1,this.$nextTick((function(){t.$refs.tableData.clearSelection()})),this.initLoad(),this.initFunLoad()},searchHandle:Object(n["d"])((function(t){t&&"search"===t&&this.initFunLoad()}),300),getMenuFoodList:function(){var t=this;return L(g().mark((function e(){var o,i,r,a,s,l,c;return g().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return o=f({page:1,page_size:99999,id:t.menuId,menu_type:t.menuType},t.formatQueryParams(t.searchFormSetting)),t.searchForm.category_id&&(o.category=t.searchForm.category_id),t.isLoading=!0,e.next=5,Object(n["Z"])(t.$apis.apiBackgroundFoodMenuFoodListPost(o));case 5:if(i=e.sent,r=y(i,2),a=r[0],s=r[1],t.isLoading=!1,!a){e.next=13;break}return t.$message.error(a.message),e.abrupt("return");case 13:0===s.code?(t.foodsList.length||t.searchForm.category_id||t.searchFormSetting.name.value||t.searchFormSetting.count_type.value||t.searchFormSetting.label_filter.value||t.searchFormSetting.label_list.value.length||(t.isFirst=!1,l=s.data.foods.map((function(t){return t.id})),t.setCurrentHasFoodIds(l)),c=0,t.collectiveNumber=s.data.collective_number,t.foodsTips=s.data.foods_tips,t.foodsList=s.data.foods.map((function(e){var o,i,r,n,a={category:e.category,name:e.food_name,selected:t.selectFoodIds.includes(e.id),id:e.id,settingStocksNum:(null===(o=t.currentEditData)||void 0===o||null===(o=o.setting_stocks)||void 0===o?void 0:o[e.id])>=0?null===(i=t.currentEditData)||void 0===i||null===(i=i.setting_stocks)||void 0===i?void 0:i[e.id]:-1,selectedFoodBuyLimitNum:(null===(r=t.currentEditData)||void 0===r||null===(r=r.food_buy_limit)||void 0===r?void 0:r[e.id])||0,stock:(null===(n=t.currentEditData)||void 0===n||null===(n=n.current_stocks)||void 0===n?void 0:n[e.id])||0,status:1,ingredient_category_count:e.ingredient_category_count,ingredient_info:e.ingredient_info,main_nutrition:e.main_nutrition,food_diversity_nutrition:e.food_diversity_nutrition,weight:e.weight,recommend_food:e.recommend_food,food_nutrition_status:0};e.id===Number(t.analysisFoodId)&&(a.food_nutrition_status=1);var s=t.allFoodList[a.id.toString()];return a.selected&&s&&(s.settingStocksNum>a.settingStocksNum&&(a.settingStocksNum=s.settingStocksNum),s.selectedFoodBuyLimitNum>a.selectedFoodBuyLimitNum&&(a.selectedFoodBuyLimitNum=s.selectedFoodBuyLimitNum)),t.selectFoodIds.includes(e.id)&&(c+=1,t.$nextTick((function(){t.$refs.tableData.toggleRowSelection(a)}))),a})).sort((function(t,e){var o=t.selected.toString().charCodeAt()-e.selected.toString().charCodeAt();return o>0?-1:1})).sort((function(t,e){var o=t.food_nutrition_status.toString().charCodeAt()-e.food_nutrition_status.toString().charCodeAt();return o>0?-1:1})),"food"===t.tabType&&(t.tableData=t.foodsList,setTimeout((function(){t.categorySelectNumber("all")}),200)),"food"===t.tabType&&c&&c===t.foodsList.length?t.setMealSelectAll=!0:t.setMealSelectAll=!1,!t.allFoodList.length&&t.foodsList.length&&Object.freeze(Object(n["f"])(t.foodsList)).forEach((function(e){t.allFoodList[e.id.toString()]=e})),t.setMealAllValue=-1,t.setMealAllFoodBuyLimitNum=""):t.$message.error(s.msg);case 14:case"end":return e.stop()}}),e)})))()},setMealAllSetMealList:function(){var t=this;return L(g().mark((function e(){var o,i,r,a,s,l,c;return g().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return o=f({},t.formatQueryParams(t.searchFormSetting)),t.searchForm.category_id&&(o.category_id=t.searchForm.category_id),e.next=4,Object(n["Z"])(t.$apis.apiBackgroundFoodSetMealAllSetMealListPost(o));case 4:if(i=e.sent,r=y(i,2),a=r[0],s=r[1],!a){e.next=11;break}return t.$message.error(a.message),e.abrupt("return");case 11:0===s.code?(t.setMealList.length||t.searchForm.category_id||t.searchFormSetting.name.value||(t.isFirst=!1,l=s.data.map((function(t){return t.id})),t.setCurrentHasMealIds(l)),c=0,t.setMealList=s.data.map((function(e){var o,i,r,n={category:e.category,name:e.name,selected:t.setMealSelectFoodIds.includes(e.id),id:e.id,settingStocksNum:(null===(o=t.currentEditData)||void 0===o||null===(o=o.set_meal_setting_stocks)||void 0===o?void 0:o[e.id])||-1,selectedFoodBuyLimitNum:(null===(i=t.currentEditData)||void 0===i||null===(i=i.set_meal_buy_limit)||void 0===i?void 0:i[e.id])||0,stock:(null===(r=t.currentEditData)||void 0===r||null===(r=r.set_meal_current_stocks)||void 0===r?void 0:r[e.id])||0,status:1,ingredient_category_count:e.ingredient_category_count,main_nutrition:e.main_nutrition,food_group_name:""};e.food_group&&e.food_group.length&&(n.food_group_name=e.food_group.join("、"));var a=t.setMealAllFoodList[n.id.toString()];return n.selected&&a&&(a.settingStocksNum>n.settingStocksNum&&(n.settingStocksNum=a.settingStocksNum),a.selectedFoodBuyLimitNum>n.selectedFoodBuyLimitNum&&(n.selectedFoodBuyLimitNum=a.selectedFoodBuyLimitNum)),t.setMealSelectFoodIds.includes(e.id)&&"setMeal"===t.tabType&&(c+=1,t.$nextTick((function(){t.$refs.tableData.toggleRowSelection(n)}))),n})).sort((function(t,e){var o=t.selected.toString().charCodeAt()-e.selected.toString().charCodeAt();return o>0?-1:1})),"setMeal"===t.tabType&&(t.tableData=t.setMealList,setTimeout((function(){t.categorySelectNumber("all")}),200)),"setMeal"===t.tabType&&c&&c===t.setMealList.length?t.setMealSelectAll=!0:t.setMealSelectAll=!1,t.searchForm.category_id||t.setMealAllFoodList.length||!t.setMealList.length||Object.freeze(Object(n["f"])(t.setMealList)).forEach((function(e){t.setMealAllFoodList[e.id.toString()]=e})),t.setMealAllValue=-1,t.setMealAllFoodBuyLimitNum=""):t.$message.error(s.msg);case 12:case"end":return e.stop()}}),e)})))()},tableDisableSelect:function(t){var e=!1;return"food"===this.tabType?e=!this.selectFoodIds.includes(t.id):"setMeal"===this.tabType&&(e=!this.setMealSelectFoodIds.includes(t.id)),e},getMenuFoodSortList:function(){var t=this;return L(g().mark((function e(){var o,i,r,a,s;return g().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.treeLoading=!0,e.next=3,Object(n["Z"])(t.$apis.apiBackgroundFoodMenuFoodSortListPost());case 3:if(o=e.sent,i=y(o,2),r=i[0],a=i[1],t.treeLoading=!1,!r){e.next=11;break}return t.$message.error(r.message),e.abrupt("return");case 11:0===a.code?(s=a.data.map((function(t){return t.id=t.id+"_1",t.food_category&&t.food_category.length&&t.food_category.forEach((function(t){t.number=0})),t})),t.treeList=t.deleteEmptyGroup(s)):t.$message.error(a.msg);case 12:case"end":return e.stop()}}),e)})))()},getSetMealCategoryList:function(){var t=this;return L(g().mark((function e(){var o,i,r,a,s;return g().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.treeLoading=!0,e.next=3,Object(n["Z"])(t.$apis.apiBackgroundFoodSetMealCategoryListPost({page:1,page_size:9999}));case 3:if(o=e.sent,i=y(o,2),r=i[0],a=i[1],t.treeLoading=!1,!r){e.next=11;break}return t.$message.error(r.message),e.abrupt("return");case 11:0===a.code?(s=a.data.results.map((function(t){return t.number=0,t})),t.treeList=t.deleteEmptyGroup(s)):t.$message.error(a.msg);case 12:case"end":return e.stop()}}),e)})))()},deleteEmptyGroup:function(t){var e=this;function o(t){t.map((function(t){t.food_category&&t.food_category.length>0?o(t.food_category):e.$delete(t,"food_category")}))}return o(t),t},treeHandleNodeClick:function(t,e){"all"===e?(this.searchForm.category_id="",this.initFunLoad()):!t.food_category&&t.id&&(t.number&&(t.number=0),this.searchForm.category_id=t.id,this.initFunLoad())},filterTreeNode:function(t,e){return!t||-1!==e.name.indexOf(t)},handleSelectionSelect:function(t,e){var o=this.tableData.find((function(t){return t.id===e.id}));o.selected=!o.selected,"food"===this.tabType?(o.selected&&!this.selectFoodIds.includes(e.id)?this.selectFoodIds.push(o.id):this.selectFoodIds.splice(this.selectFoodIds.indexOf(o.id),1),this.selectFoodIds.length===this.tableData.length?this.setMealSelectAll=!0:this.setMealSelectAll=!1):"setMeal"===this.tabType&&(o.selected&&!this.setMealSelectFoodIds.includes(e.id)?this.setMealSelectFoodIds.push(o.id):this.setMealSelectFoodIds.splice(this.setMealSelectFoodIds.indexOf(o.id),1),this.setMealSelectFoodIds.length===this.tableData.length?this.setMealSelectAll=!0:this.setMealSelectAll=!1),this.categorySelectNumber("",o,!0)},categorySelectNumber:function(t,e,o){var i=this,r=[];r="all"===t?this.tableData:[e];var a=Object(n["f"])(this.treeList);a.forEach((function(t){t.food_category&&t.food_category.length?(i.updateTreeChildren(t.id,t.food_category),t.food_category.forEach((function(e){r.forEach((function(r){"food"===i.tabType&&(i.selectFoodIds.includes(r.id)&&e.id===r.category?(e.number+=1,i.updateTreeChildren(t.id,t.food_category)):o&&!i.selectFoodIds.includes(r.id)&&e.id===r.category&&(e.number-=1))}))}))):"setMeal"===i.tabType&&r.forEach((function(e){i.setMealSelectFoodIds.includes(e.id)&&t.id===e.category?t.number+=1:o&&!i.setMealSelectFoodIds.includes(e.id)&&t.id===e.category&&(t.number-=1)}))})),"setMeal"===this.tabType&&(this.treeList=a,this.searchForm.category_id&&this.$nextTick((function(){i.$refs.tree.setCurrentKey(i.searchForm.category_id)})))},updateTreeChildren:function(t,e){var o=this;this.$nextTick((function(){var i=o.$refs.tree;i.updateKeyChildren(t,e)})),this.searchForm.category_id&&this.$nextTick((function(){o.$refs.tree.setCurrentKey(o.searchForm.category_id)}))},handleSelectionAll:function(t){},tabClick:function(t){this.searchForm.category_id="",this.tabType=t,this.tableData=[],this.initLoad(),this.initFunLoad()},setMealhandleCheckChange:function(t){var e=this;"food"===this.tabType?this.tableData.forEach((function(o){o.selected=t,t?(e.allFoodList[o.id.toString()]?e.allFoodList[o.id.toString()]=Object(n["f"])(o):(e.allFoodList[o.id.toString()].settingStocksNum=o.settingStocksNum,e.allFoodList[o.id.toString()].selectedFoodBuyLimitNum=o.selectedFoodBuyLimitNum),e.selectFoodIds.includes(o.id)||(e.selectFoodIds.push(o.id),e.$refs.tableData.toggleRowSelection(o))):(e.selectFoodIds.splice(e.selectFoodIds.indexOf(o.id),1),e.allFoodList[o.id.toString()].selected=!1,e.$nextTick((function(){e.$refs.tableData.clearSelection()})))})):"setMeal"===this.tabType&&this.tableData.forEach((function(o){o.selected=t,t?(e.setMealAllFoodList[o.id.toString()]?e.setMealAllFoodList[o.id.toString()]=Object(n["f"])(o):(e.setMealAllFoodList[o.id.toString()].settingStocksNum=o.settingStocksNum,e.setMealAllFoodList[o.id.toString()].selectedFoodBuyLimitNum=o.selectedFoodBuyLimitNum),e.setMealSelectFoodIds.includes(o.id)||(e.setMealSelectFoodIds.push(o.id),e.$refs.tableData.toggleRowSelection(o))):(e.setMealSelectFoodIds.splice(e.setMealSelectFoodIds.indexOf(o.id),1),e.setMealAllFoodList[o.id.toString()].selected=!1,e.$nextTick((function(){e.$refs.tableData.clearSelection()})))})),t?(this.searchForm.category_id?this.resetTreeFoodNumber(this.searchForm.category_id):this.resetTreeFoodNumber(),this.categorySelectNumber("all")):this.resetTreeFoodNumber(this.searchForm.category_id)},setMealSetAll:function(t){var e=this;return this.setMealAllFoodBuyLimitNum&&!this.regNum.test(this.setMealAllFoodBuyLimitNum)||-1!==this.setMealAllValue&&(this.setMealAllValue%1!==0||this.setMealAllValue<0)?this.$message.error("数量有误，请输入正整数"):-1!==this.setMealAllValue&&this.setMealAllFoodBuyLimitNum>this.setMealAllValue?this.$message.error("菜品限制单人数量，不能大于菜品份数"):void this.tableData.forEach((function(t){t.settingStocksNum=e.setMealAllValue,t.selectedFoodBuyLimitNum=e.setMealAllFoodBuyLimitNum,"food"===e.tabType?(e.selectFoodIds.includes(t.id)||e.selectFoodIds.push(t.id),e.allFoodList[t.id.toString()]?e.allFoodList[t.id.toString()]=Object(n["f"])(t):(e.allFoodList[t.id.toString()].settingStocksNum=t.settingStocksNum,e.allFoodList[t.id.toString()].selectedFoodBuyLimitNum=t.selectedFoodBuyLimitNum)):"setMeal"===e.tabType&&(e.setMealSelectFoodIds.includes(t.id)||e.setMealSelectFoodIds.push(t.id),e.setMealAllFoodList[t.id.toString()]?e.setMealAllFoodList[t.id.toString()]=Object(n["f"])(t):(e.setMealAllFoodList[t.id.toString()].selectedNum=t.selectedNum,e.setMealAllFoodList[t.id.toString()].selectedFoodBuyLimitNum=t.selectedFoodBuyLimitNum))}))},changeFoodNum:function(t){"food"===this.tabType?this.allFoodList[t.id.toString()].settingStocksNum=t.settingStocksNum:"setMeal"===this.tabType&&(this.setMealAllFoodList[t.id.toString()].settingStocksNum=t.settingStocksNum)},inputClearStock:function(t){t.settingStocksNum=-1,"food"===this.tabType?this.allFoodList[t.id.toString()].settingStocksNum=-1:"setMeal"===this.tabType&&(this.setMealAllFoodList[t.id.toString()].settingStocksNum=-1)},changeFoodBuyLimitNum:function(t){"food"===this.tabType?this.allFoodList[t.id.toString()].selectedFoodBuyLimitNum=t.selectedFoodBuyLimitNum:"setMeal"===this.tabType&&(this.setMealAllFoodList[t.id.toString()].selectedFoodBuyLimitNum=t.selectedFoodBuyLimitNum)},confirmEditMeal:function(t){var e=this,o={use_date:this.currentEditDate,setting_stock:{},set_meal_setting_stock:{},food_buy_limit:{},set_meal_buy_limit:{},id:Number(this.$route.query.menuId),meal_type:this.currentEditMealType};this.selectFoodIds.length&&this.selectFoodIds.forEach((function(t){var i,r;o.setting_stock[t]=e.allFoodList&&e.allFoodList[t]?Number(e.allFoodList[t].settingStocksNum):null===(i=e.currentEditData)||void 0===i||null===(i=i.setting_stocks)||void 0===i?void 0:i[t],o.food_buy_limit[t]=e.allFoodList&&e.allFoodList[t]?e.allFoodList[t].selectedFoodBuyLimitNum:null===(r=e.currentEditData)||void 0===r||null===(r=r.food_buy_limit)||void 0===r?void 0:r[t]})),"setMeal"===this.tabType&&(o.set_meal_food_display=this.setMealFoodDisplay),this.setMealSelectFoodIds.length&&this.setMealSelectFoodIds.forEach((function(t){var i,r;o.set_meal_setting_stock[t]=e.setMealAllFoodList&&e.setMealAllFoodList[t]?Number(e.setMealAllFoodList[t].settingStocksNum):null===(i=e.currentEditData)||void 0===i||null===(i=i.set_meal_setting_stocks)||void 0===i?void 0:i[t],o.set_meal_buy_limit[t]=e.setMealAllFoodList&&e.setMealAllFoodList[t]?e.setMealAllFoodList[t].selectedFoodBuyLimitNum:null===(r=e.currentEditData)||void 0===r||null===(r=r.set_meal_buy_limit)||void 0===r?void 0:r[t]})),o.setting_stock=JSON.stringify(o.setting_stock),o.food_buy_limit=JSON.stringify(o.food_buy_limit),o.set_meal_setting_stock=JSON.stringify(o.set_meal_setting_stock),o.set_meal_buy_limit=JSON.stringify(o.set_meal_buy_limit);var i=this.selectFoodIds.every((function(t){return!e.allFoodList||!Reflect.has(e.allFoodList,t)||(e.allFoodList&&Reflect.has(e.allFoodList,t)&&e.allFoodList[t].selectedFoodBuyLimitNum&&!e.regNum.test(e.allFoodList[t].selectedFoodBuyLimitNum)?(e.$message.error("数量有误，请输入正整数"),!1):-1===e.allFoodList[t].settingStocksNum||(e.regNum.test(e.allFoodList[t].settingStocksNum)?!(-1!==e.allFoodList[t].settingStocksNum&&Number(e.allFoodList[t].selectedFoodBuyLimitNum)>Number(e.allFoodList[t].settingStocksNum))||(e.$message.error("菜品限制单人数量，不能大于菜品份数"),!1):(e.$message.error("数量有误，请输入正整数"),!1)))})),r=this.setMealSelectFoodIds.every((function(t){if(e.setMealAllFoodList&&Reflect.has(e.setMealAllFoodList,t))return e.setMealAllFoodList[t].selectedFoodBuyLimitNum&&!e.regNum.test(e.setMealAllFoodList[t].selectedFoodBuyLimitNum)?(e.$message.error("数量有误，请输入正整数"),!1):-1===e.setMealAllFoodList[t].settingStocksNum||(e.regNum.test(e.setMealAllFoodList[t].settingStocksNum)?!(-1!==e.setMealAllFoodList[t].settingStocksNum&&Number(e.setMealAllFoodList[t].selectedFoodBuyLimitNum)>Number(e.setMealAllFoodList[t].settingStocksNum))||(e.$message.error("套餐限制单人数量，不能大于套餐份数"),!1):(e.$message.error("数量有误，请输入正整数"),!1))}));i&&r&&this.getFoodMenuMenuFoodModify(o,t)},showMealContinue:function(){var t=Object.keys(this.mealDailyData),e=!0;return"morning"===this.currentEditMealType&&t.indexOf(this.currentEditDate)===t.length-1&&(e=!1),e},initContinueCatering:function(t){var e=this,o=["breakfast","lunch","afternoon","dinner","supper","morning"],i=Object.keys(t),r={};if("morning"===this.currentEditMealType){if(i.indexOf(this.currentEditDate)===i.length-1)return this.$message.error("已经是最后一天了");this.currentEditMealType="breakfast",this.currentEditDate=i[this.currentEditDate.indexOf(this.currentEditDate)+1]}else this.currentEditMealType=o[o.indexOf(this.currentEditMealType)+1];t[this.currentEditDate].foods&&t[this.currentEditDate].foods.length&&t[this.currentEditDate].foods.forEach((function(t){t.meal_type===e.currentEditMealType&&(r=t)})),this.$router.replace({query:l()(this.$route.query,{isNutritionGuidance:this.isNutritionGuidance?"true":"false",currentEditDate:this.currentEditDate,currentEditMealType:this.currentEditMealType,data:this.$encodeQuery(r)})}),Object.keys(r)&&Object.keys(r).length&&this.reload()},clickContinueCatering:function(){"week"===this.$route.query.menuType?this.getweekDetail():this.getMonthlyDetail()},getMonthlyDetail:function(t,e){var o=this;return L(g().mark((function t(){var e,i,r,a;return g().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return o.isLoading=!0,t.next=3,Object(n["Z"])(o.$apis.apiBackgroundFoodMenuMonthlyMonthlyDetailPost({id:o.menuId}));case 3:if(e=t.sent,i=y(e,2),r=i[0],a=i[1],o.isLoading=!1,!r){t.next=11;break}return o.$message.error(r.message),t.abrupt("return");case 11:0===a.code?o.initContinueCatering(a.data.daily_data):o.$message.error(a.msg);case 12:case"end":return t.stop()}}),t)})))()},getweekDetail:function(t,e){var o=this;return L(g().mark((function t(){var e,i,r,a;return g().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return o.isLoading=!0,t.next=3,Object(n["Z"])(o.$apis.apiBackgroundFoodMenuWeeklyWeeklyDetailPost({id:o.menuId}));case 3:if(e=t.sent,i=y(e,2),r=i[0],a=i[1],o.isLoading=!1,!r){t.next=11;break}return o.$message.error(r.message),t.abrupt("return");case 11:0===a.code?o.initContinueCatering(a.data.daily_data):o.$message.error(a.msg);case 12:case"end":return t.stop()}}),t)})))()},getFoodMenuMenuFoodModify:function(t,e){var o=this;return L(g().mark((function i(){var r,a,s,l,c,u,d,f;return g().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(o.isLoading=!0,r="",a=y(r,2),s=a[0],l=a[1],"week"!==o.$route.query.menuType){i.next=12;break}return i.next=6,Object(n["Z"])(o.$apis.apiBackgroundFoodMenuWeeklyMenuFoodModifyPost(t));case 6:c=i.sent,u=y(c,2),s=u[0],l=u[1],i.next=19;break;case 12:return i.next=15,Object(n["Z"])(o.$apis.apiBackgroundFoodMenuMonthlyMenuFoodModifyPost(t));case 15:d=i.sent,f=y(d,2),s=f[0],l=f[1];case 19:if(o.isLoading=!1,!s){i.next=23;break}return o.$message.error(s.message),i.abrupt("return");case 23:0===l.code?(o.$message.success("操作成功！"),"continue"===e?o.clickContinueCatering():o.$closeCurrentTab(o.$route.path)):o.$message.error(l.msg);case 24:case"end":return i.stop()}}),i)})))()},formatQueryParams:function(t){var e={};for(var o in t)""!==t[o].value&&null!==t[o].value&&("select_time"!==o?e[o]=t[o].value:t[o].value&&t[o].value.length>0&&(e.start_date=t[o].value[0],e.end_date=t[o].value[1]));return e},getAllLabelGroupList:function(){var t=this;return L(g().mark((function e(){var o,i,r,a;return g().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(n["Z"])(t.$apis.apiBackgroundHealthyLabelGroupAllLabelGroupListPost({is_admin:!0,type:"food",page:1,page_size:999999}));case 3:if(o=e.sent,i=y(o,2),r=i[0],a=i[1],t.isLoading=!1,!r){e.next=11;break}return t.$message.error(r.message),e.abrupt("return");case 11:0===a.code?(a.data.results.map((function(t){return t.id="".concat(t.id,"_1"),t.label_list.length||(t.isDisabled=!0),t})),t.searchFormSetting.label_list.dataList=a.data.results):t.$message({type:"error",duration:1e3,message:a.msg});case 12:case"end":return e.stop()}}),e)})))()},resetTreeFoodNumber:function(t){var e=this;this.treeList.forEach((function(o){"food"===e.tabType&&o.food_category&&o.food_category.length?o.food_category.forEach((function(e){t?e.id===t&&(e.number=0):e.number=0})):"setMeal"===e.tabType&&(t?o.id===t&&(o.number=0):o.number=0)}))},getMealTypeNutrition:function(){var t=this;return L(g().mark((function e(){var o,i,r,a;return g().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(n["Z"])(t.$apis.apiBackgroundFoodMenuMealTypeNutritionPost({id:t.menuId,menu_type:t.menuType,date:t.currentEditDate,meal_type:t.currentEditMealType}));case 3:if(o=e.sent,i=y(o,2),r=i[0],a=i[1],t.isLoading=!1,!r){e.next=11;break}return t.$message.error(r.message),e.abrupt("return");case 11:0===a.code?t.needNutritionInfo=a.data.need_nutrition:t.$message({type:"error",duration:1e3,message:a.msg});case 12:case"end":return e.stop()}}),e)})))()},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,o,i){"confirm"===e?t.$closeCurrentTab(t.$route.path):o.confirmButtonLoading||i()}}).then((function(t){})).catch((function(t){}))},importHandler:function(t){this.$router.push({name:"MerchantImportCommodity",params:{type:t}})},handleSizeChange:function(t){this.pageSize=t},handleCurrentChange:function(t){this.currentPage=t},formatNumberWithUnit:n["o"]}},k=M,w=(o("bb2a"),o("2877")),x=Object(w["a"])(k,i,r,!1,null,null,null);e["default"]=x.exports},"9cbb":function(t,e,o){t.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}},bb2a:function(t,e,o){"use strict";o("9cbb")}}]);