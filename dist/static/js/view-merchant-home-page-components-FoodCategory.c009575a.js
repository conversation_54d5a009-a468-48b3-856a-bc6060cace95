(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-home-page-components-FoodCategory"],{4960:function(e,t,i){},"894e":function(e,t,i){"use strict";i("4960")},d4e4:function(e,t,i){"use strict";i.r(t);var s=function(){var e=this,t=e._self._c;return t("div",[t("tree-select",e._g(e._b({attrs:{multiple:!0,options:e.treeList,limit:1,limitText:function(e){return"+"+e},"default-expand-level":1,normalizer:e.normalizer,placeholder:"请选择","no-children-text":"暂无更多",noOptionsText:"暂无分类",noResultsText:"暂无更多","search-nested":"","value-consists-of":"LEAF_PRIORITY",appendToBody:!0},model:{value:e.selectData,callback:function(t){e.selectData=t},expression:"selectData"}},"tree-select",e.$attrs,!1),e.$listeners))],1)},n=[],o=i("ed08"),r={name:"",components:{},props:{onlyFirstList:{type:Boolean,default:!1}},data:function(){return{selectData:[],treeList:[],normalizer:function(e){return{id:e.id,label:e.name,children:e.children}},firstLevelList:[],secondLevelList:[]}},computed:{},watch:{},created:function(){this.getCategory()},mounted:function(){},methods:{getCategory:function(){var e=this,t={page:1,page_size:999999},i=this.$apis.apiBackgroundFoodFoodSortListPost(t),s=this.$apis.apiBackgroundFoodFoodCategoryListPost(t);this.onlyFirstList?i.then((function(t){0===t.code&&(e.firstLevelList=t.data.results),e.treeList=e.arrayToTree(e.firstLevelList,[])})):Promise.all([i,s]).then((function(t){t.forEach((function(t,i){if(0===t.code)switch(i){case 0:e.firstLevelList=t.data.results;break;case 1:e.secondLevelList=t.data.results;break}})),e.treeList=e.arrayToTree(e.firstLevelList,e.secondLevelList)})).catch((function(e){}))},arrayToTree:function(e,t){var i=Object(o["f"])(e);return t.forEach((function(e){for(var t=0;t<i.length;t++){var s=i[t];e.sort==s.id?(s.isDisabled=!1,s.children?s.children.push(e):s.children=[e]):s.children||(s.children=[],s.isDisabled=!0)}})),i},reset:function(){this.selectData=[]}}},a=r,c=(i("894e"),i("2877")),l=Object(c["a"])(a,s,n,!1,null,null,null);t["default"]=l.exports}}]);