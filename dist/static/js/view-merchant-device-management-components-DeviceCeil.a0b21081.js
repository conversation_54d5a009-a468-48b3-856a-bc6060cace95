(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-device-management-components-DeviceCeil"],{"2ca1":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",[e("div",{staticClass:"table-wrapper",staticStyle:{"margin-bottom":"20px"}},[e("div",{staticClass:"table-header",staticStyle:{display:"flex","justify-content":"space-between"}},[t._m(0),e("div",{staticStyle:{"padding-right":"20px"}},[e("div",[t._v("当前设备："+t._s(t.deviceName))])])]),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"content-wrapper"},[e("div",{staticClass:"ceil-top flex-center"},[e("div",[t._v(" 餐格设置： "),e("el-button",{staticClass:"origin-btn",attrs:{type:"primary"},on:{click:function(e){return t.setCupboardCeilLock("big")}}},[t._v("大餐格")]),e("el-button",{attrs:{type:"plain"},on:{click:function(e){return t.setCupboardCeilLock("little")}}},[t._v("小餐格")]),t._v(" 根据取餐柜可用餐格设置可被分配的餐格，避免出现不可用餐格被系统自动分配 ")],1),e("div",[e("span",{staticClass:"green-text"},[t._v(t._s(t.canUseCeilNum)+"个可用")]),e("span",[t._v("/"+t._s(t.totalCeilNum)+"个")])])]),e("div",{staticClass:"ceil-list"},t._l(t.cupboardCeilList,(function(n,i){return e("div",{key:i,staticClass:"ceil-list-item",class:[-1===t.selectList.indexOf(n.ceil_no)?"":"active-ceil"],on:{click:function(e){return t.selectCeil(n)}}},[e("div",{staticClass:"ceil-list-item-top flex-center"},[e("div",[e("span",{staticClass:"ceil-item-size"},[t._v(t._s(n.ceil_size?"大":"小"))]),t._v(t._s(n.ceil_no)+" ")])]),e("div",[e("div",[t._v("订单号："+t._s(n.trade_no?n.trade_no:"--"))]),e("div",[t._v("取餐码："+t._s(n.take_meal_number?n.take_meal_number:"--"))]),e("div",[t._v("存餐码："+t._s(n.put_meal_number?n.put_meal_number:"--"))])]),e("div",[n.forbidden?e("img",{staticClass:"ceil-lock",attrs:{src:r("d4a3"),alt:"",srcset:""}}):t._e()])])})),0)])]),e("div",{staticClass:"button-footer flex-center"},[e("div",{staticStyle:{"min-width":"130px"}},[e("el-checkbox",{staticClass:"ps-checkbox",on:{change:t.selectAllCeil},model:{value:t.selectAll,callback:function(e){t.selectAll=e},expression:"selectAll"}}),t._v(" 全选，已选"+t._s(t.selectList.length)+"个 ")],1),e("div",{staticStyle:{display:"flex","flex-wrap":"wrap"}},[e("div",{staticClass:"btn",on:{click:function(e){return t.setCupboardCeilLock("lock")}}},[t._v("锁定餐格")]),e("div",{staticClass:"btn",on:{click:function(e){return t.setCupboardCeilLock("unlock")}}},[t._v("解锁餐格")]),e("div",{staticClass:"btn",on:{click:function(e){return t.cupboardMessageSend(406)}}},[t._v("开柜")]),e("div",{staticClass:"btn",on:{click:function(e){return t.cupboardMessageSend(400)}}},[t._v("开灯")]),e("div",{staticClass:"btn",on:{click:function(e){return t.cupboardMessageSend(401)}}},[t._v("关灯")]),e("div",{staticClass:"btn",on:{click:function(e){return t.cupboardMessageSend(402)}}},[t._v("开加热")]),e("div",{staticClass:"btn",on:{click:function(e){return t.cupboardMessageSend(403)}}},[t._v("关加热")]),e("div",{staticClass:"btn",on:{click:function(e){return t.cupboardMessageSend(404)}}},[t._v("开消毒")]),e("div",{staticClass:"btn",on:{click:function(e){return t.cupboardMessageSend(405)}}},[t._v("关消毒")]),e("div",{staticClass:"btn",on:{click:function(e){return t.cupboardMessageSend(310)}}},[t._v("销单")])])])])},i=[function(){var t=this,e=t._self._c;return e("div",{staticStyle:{display:"flex","align-items":"center"}},[e("div",{staticClass:"table-title"},[t._v("取餐柜设置")])])}];function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function a(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */a=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},c="function"==typeof Symbol?Symbol:{},s=c.iterator||"@@iterator",l=c.asyncIterator||"@@asyncIterator",u=c.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function d(t,e,r,n){var o=e&&e.prototype instanceof y?e:y,a=Object.create(o.prototype),c=new P(n||[]);return i(a,"_invoke",{value:N(t,r,c)}),a}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=d;var h="suspendedStart",v="suspendedYield",g="executing",b="completed",m={};function y(){}function _(){}function L(){}var w={};f(w,s,(function(){return this}));var C=Object.getPrototypeOf,k=C&&C(C(A([])));k&&k!==r&&n.call(k,s)&&(w=k);var x=L.prototype=y.prototype=Object.create(w);function S(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function E(t,e){function r(i,a,c,s){var l=p(t[i],t,a);if("throw"!==l.type){var u=l.arg,f=u.value;return f&&"object"==o(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,c,s)}),(function(t){r("throw",t,c,s)})):e.resolve(f).then((function(t){u.value=t,c(u)}),(function(t){return r("throw",t,c,s)}))}s(l.arg)}var a;i(this,"_invoke",{value:function(t,n){function i(){return new e((function(e,i){r(t,n,e,i)}))}return a=a?a.then(i,i):i()}})}function N(e,r,n){var i=h;return function(o,a){if(i===g)throw Error("Generator is already running");if(i===b){if("throw"===o)throw a;return{value:t,done:!0}}for(n.method=o,n.arg=a;;){var c=n.delegate;if(c){var s=O(c,n);if(s){if(s===m)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===h)throw i=b,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=g;var l=p(e,r,n);if("normal"===l.type){if(i=n.done?b:v,l.arg===m)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(i=b,n.method="throw",n.arg=l.arg)}}}function O(e,r){var n=r.method,i=e.iterator[n];if(i===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,O(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var o=p(i,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,m;var a=o.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function j(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function $(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function P(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(j,this),this.reset(!0)}function A(e){if(e||""===e){var r=e[s];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,a=function r(){for(;++i<e.length;)if(n.call(e,i))return r.value=e[i],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(o(e)+" is not iterable")}return _.prototype=L,i(x,"constructor",{value:L,configurable:!0}),i(L,"constructor",{value:_,configurable:!0}),_.displayName=f(L,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===_||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,L):(t.__proto__=L,f(t,u,"GeneratorFunction")),t.prototype=Object.create(x),t},e.awrap=function(t){return{__await:t}},S(E.prototype),f(E.prototype,l,(function(){return this})),e.AsyncIterator=E,e.async=function(t,r,n,i,o){void 0===o&&(o=Promise);var a=new E(d(t,r,n,i),o);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},S(x),f(x,u,"Generator"),f(x,s,(function(){return this})),f(x,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=A,P.prototype={constructor:P,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach($),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function i(n,i){return c.type="throw",c.arg=e,r.next=n,i&&(r.method="next",r.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],c=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var s=n.call(a,"catchLoc"),l=n.call(a,"finallyLoc");if(s&&l){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,m):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),$(r),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var i=n.arg;$(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:A(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),m}},e}function c(t,e,r,n,i,o,a){try{var c=t[o](a),s=c.value}catch(t){return void r(t)}c.done?e(s):Promise.resolve(s).then(n,i)}function s(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var o=t.apply(e,r);function a(t){c(o,n,i,a,s,"next",t)}function s(t){c(o,n,i,a,s,"throw",t)}a(void 0)}))}}var l={name:"DeviceCeil",props:{deviceNo:[String,Number],deviceName:String,ceilList:Array},data:function(){return{isLoading:!1,cupboardCeilList:[],selectList:[],selectAll:!1,canUseCeilNum:0,totalCeilNum:0}},created:function(){this.initLoad()},methods:{initLoad:function(){var t=this;return s(a().mark((function e(){return a().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.selectList=[],t.selectAll=!1,t.getCupboardInfo();case 3:case"end":return e.stop()}}),e)})))()},getCupboardInfo:function(){var t=this;return s(a().mark((function e(){var r;return a().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.apiBackgroundDeviceDeviceGetCupboardCeilInfoListPost({device_no:Number(t.deviceNo)});case 2:r=e.sent,0===r.code?(t.cupboardCeilList=r.data.info,t.canUseCeilNum=r.data.can_use_ceil_num,t.totalCeilNum=r.data.total_ceil_num):t.$message.error(r.msg);case 4:case"end":return e.stop()}}),e)})))()},selectCeil:function(t){var e=this.selectList.indexOf(t.ceil_no);-1===e?this.selectList.push(t.ceil_no):this.selectList.splice(e,1)},selectAllCeil:function(){var t=this;this.selectAll?this.cupboardCeilList.map((function(e){t.selectList.push(e.ceil_no)})):this.selectList=[]},cupboardMessageSend:function(t){var e=this;return s(a().mark((function r(){var n,i,o,c,s;return a().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(e.selectList.length){r.next=2;break}return r.abrupt("return",e.$message.error("请先选择餐格！（点击餐格进行选择）"));case 2:if(n={device_no:e.deviceNo,ceil_no_list:e.selectList,mqtt_code:t},i=[],310!==t){r.next=13;break}if(o=e.cupboardCeilList.filter((function(t){return-1!==e.selectList.indexOf(t.ceil_no)})),-1!==o.findIndex((function(t){return 0!==t.trade_no.length}))){r.next=10;break}return r.abrupt("return",e.$message.error("所选餐格暂无订单"));case 10:c=o.filter((function(t){return 0!==t.trade_no.length})),c.map((function(t){i.push(t.trade_no)})),n.trade_nos=i;case 13:if(!e.isLoading){r.next=15;break}return r.abrupt("return",e.$message.error("请勿重复操作"));case 15:return e.isLoading=!0,r.next=18,e.$apis.apiBackgroundDeviceDeviceCupboardMessageSendPost(n);case 18:s=r.sent,e.isLoading=!1,0===s.code?(e.$message.success("操作成功"),e.initLoad(),e.selectList=[]):e.$message.error(s.msg);case 21:case"end":return r.stop()}}),r)})))()},setCupboardCeilLock:function(t){var e=this;return s(a().mark((function r(){var n,i,o,c,s,l,u,f;return a().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(e.selectList.length){r.next=2;break}return r.abrupt("return",e.$message.error("请先选择餐格！（点击餐格进行选择）"));case 2:n=[],i=[],o=[],c=[],r.t0=t,r.next="big"===r.t0?9:"little"===r.t0?11:"lock"===r.t0?13:"unlock"===r.t0?15:17;break;case 9:return n=e.selectList,r.abrupt("break",17);case 11:return i=e.selectList,r.abrupt("break",17);case 13:return o=e.selectList,r.abrupt("break",17);case 15:return c=e.selectList,r.abrupt("break",17);case 17:if(s=e.cupboardCeilList.filter((function(t){return-1===e.selectList.indexOf(t.ceil_no)})),s.map((function(t){t.forbidden?o.push(t.ceil_no):c.push(t.ceil_no),t.ceil_size?n.push(t.ceil_no):i.push(t.ceil_no)})),l={device_no:e.deviceNo},"big"===t||"little"===t?(l.big_ceil_list=n,l.little_ceil_list=i,u=e.$apis.apiBackgroundDeviceDeviceSetCupboardCeilTypePost(l)):"lock"!==t&&"unlock"!==t||(l.ceil_forbidden_list=o,l.ceil_allow_list=c,u=e.$apis.apiBackgroundDeviceDeviceSetCupboardCeilForbiddenPost(l)),!e.isLoading){r.next=23;break}return r.abrupt("return",e.$message.error("请勿重复操作"));case 23:return e.isLoading=!0,r.next=26,u;case 26:f=r.sent,e.isLoading=!1,0===f.code?(e.$message.success("操作成功"),e.initLoad()):e.$message.error(f.msg);case 29:case"end":return r.stop()}}),r)})))()}}},u=l,f=(r("6319"),r("2877")),d=Object(f["a"])(u,n,i,!1,null,"57ea47a8",null);e["default"]=d.exports},"3f32":function(t,e,r){},6319:function(t,e,r){"use strict";r("3f32")}}]);