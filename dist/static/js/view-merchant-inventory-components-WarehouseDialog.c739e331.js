(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-inventory-components-WarehouseDialog"],{"1ace":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("dialog-message",{attrs:{show:t.showDialog,loading:t.isLoading,title:t.dialogTitle,width:"435px","footer-center":""},on:{"update:show":function(e){t.showDialog=e},close:t.closeDialog}},[e("el-form",{ref:"dialogFormRef",staticClass:"add-warehouse",attrs:{model:t.dialogForm,rules:t.dialogrules,"label-position":"left","label-width":"80px",size:"medium"}},[e("el-form-item",{attrs:{label:"仓库名称",prop:"name","label-width":"80px"}},[e("el-input",{staticClass:"ps-input",attrs:{maxlength:15,disabled:t.isDisabled},model:{value:t.dialogForm.name,callback:function(e){t.$set(t.dialogForm,"name",e)},expression:"dialogForm.name"}})],1),"SOME"===t.dialogForm.show_type?e("el-form-item",{attrs:{label:"可见组织","label-width":"80px",prop:"org_ids"}},[e("organization-select",{staticClass:"search-item-w ps-input",attrs:{placeholder:"请选择组织",isLazy:!1,multiple:!0,"check-strictly":!0,role:"merchant","append-to-body":!0,filterable:!1},model:{value:t.dialogForm.org_ids,callback:function(e){t.$set(t.dialogForm,"org_ids",e)},expression:"dialogForm.org_ids"}})],1):t._e(),e("el-form-item",{attrs:{label:"状态","label-width":"60px",prop:"status"}},[e("el-radio-group",{staticClass:"ps-radio",model:{value:t.dialogForm.status,callback:function(e){t.$set(t.dialogForm,"status",e)},expression:"dialogForm.status"}},[e("el-radio",{attrs:{label:"enable"}},[t._v("使用")]),e("el-radio",{attrs:{label:"disable"}},[t._v("禁用")])],1)],1)],1),e("div",{staticClass:"footer-center m-t-20",attrs:{slot:"tool"},slot:"tool"},[e("el-button",{staticClass:"ps-cancel-btn w-150",attrs:{disabled:t.isLoading},on:{click:t.cancleDialog}},[t._v("取消")]),e("el-button",{staticClass:"ps-btn w-150",attrs:{disabled:t.isLoading,type:"primary"},on:{click:t.confirmDialog}},[t._v("确定")])],1)],1)},o=[],i=r("cbfb"),a=r("2f62");function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function l(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */l=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function d(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,a=Object.create(i.prototype),s=new $(n||[]);return o(a,"_invoke",{value:E(t,r,s)}),a}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=d;var p="suspendedStart",g="suspendedYield",m="executing",y="completed",v={};function b(){}function w(){}function _(){}var x={};f(x,a,(function(){return this}));var O=Object.getPrototypeOf,L=O&&O(O(A([])));L&&L!==r&&n.call(L,a)&&(x=L);var F=_.prototype=b.prototype=Object.create(x);function j(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function D(t,e){function r(o,i,a,l){var c=h(t[o],t,i);if("throw"!==c.type){var u=c.arg,f=u.value;return f&&"object"==s(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,a,l)}),(function(t){r("throw",t,a,l)})):e.resolve(f).then((function(t){u.value=t,a(u)}),(function(t){return r("throw",t,a,l)}))}l(c.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function E(e,r,n){var o=p;return function(i,a){if(o===m)throw Error("Generator is already running");if(o===y){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var s=n.delegate;if(s){var l=S(s,n);if(l){if(l===v)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=m;var c=h(e,r,n);if("normal"===c.type){if(o=n.done?y:g,c.arg===v)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o=y,n.method="throw",n.arg=c.arg)}}}function S(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,S(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=h(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function P(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function $(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function A(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(s(e)+" is not iterable")}return w.prototype=_,o(F,"constructor",{value:_,configurable:!0}),o(_,"constructor",{value:w,configurable:!0}),w.displayName=f(_,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,f(t,u,"GeneratorFunction")),t.prototype=Object.create(F),t},e.awrap=function(t){return{__await:t}},j(D.prototype),f(D.prototype,c,(function(){return this})),e.AsyncIterator=D,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new D(d(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},j(F),f(F,u,"Generator"),f(F,a,(function(){return this})),f(F,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=A,$.prototype={constructor:$,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(P),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return s.type="throw",s.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=n.call(a,"catchLoc"),c=n.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),P(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;P(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:A(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function c(t,e){return d(t)||f(t,e)||y(t,e)||u()}function u(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,s=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(s.push(n.value),s.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}function d(t){if(Array.isArray(t))return t}function h(t,e,r,n,o,i,a){try{var s=t[i](a),l=s.value}catch(t){return void r(t)}s.done?e(l):Promise.resolve(l).then(n,o)}function p(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){h(i,n,o,a,s,"next",t)}function s(t){h(i,n,o,a,s,"throw",t)}a(void 0)}))}}function g(t){return b(t)||v(t)||y(t)||m()}function m(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function y(t,e){if(t){if("string"==typeof t)return w(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?w(t,e):void 0}}function v(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function b(t){if(Array.isArray(t))return w(t)}function w(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function _(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function x(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?_(Object(r),!0).forEach((function(e){O(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):_(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function O(t,e,r){return(e=L(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function L(t){var e=F(t,"string");return"symbol"==s(e)?e:e+""}function F(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var j={name:"",components:{OrganizationSelect:i["a"]},props:{show:{type:Boolean},type:{type:String,default:"add"},dialogLoading:Boolean,infoData:{type:Object,default:function(){return{}}},closehandle:Function,confirmhandle:Function},data:function(){return{isLoading:!1,dialogTitle:"新增仓库",showDialog:!1,dialogContent:"",dialogForm:{name:"",organization_id:"",is_show:!0,show_type:"ONLY",org_ids:[],is_default:!1,status:"enable"},dialogrules:{name:[{required:!0,message:"请输入仓库名称",trigger:"blur"}],organization_id:[{required:!0,message:"请选择所属组织",trigger:"blur"}],org_ids:[{required:!0,message:"请选择可见组织",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"blur"}],show_type:[{required:!0,message:"请选择",trigger:"blur"}]}}},computed:x({isDisabled:function(t){return"modify"===this.type}},Object(a["c"])(["organization"])),watch:{show:function(t){this.dialogTitle="add"===this.type?"新增仓库":"编辑仓库",this.showDialog=t,"modify"===this.type&&this.initData(this.infoData)}},created:function(){},mounted:function(){},methods:{initData:function(t){this.dialogForm={name:t.name,organization_id:t.organization_id,status:t.status,show_type:t.type,org_ids:"SOME"===t.type?g(t.organizations):[]}},clearHandle:function(){var t=this.$refs.dialogFormRef;t&&t.clearValidate(),this.dialogForm={name:"",organization_id:"",is_show:!0,show_type:"ONLY",org_ids:[],is_default:!1,status:"enable"}},closeDialog:function(){this.clearHandle(),this.closehandle&&this.closehandle()},cancleDialog:function(){this.closehandle&&this.closehandle()},confirmDialog:function(){var t=this;this.$refs.dialogFormRef.validate((function(e){e&&("add"===t.type?t.sendAddFormData():t.sendModifyFormData())}))},formatFormData:function(){var t={name:this.dialogForm.name,organization_id:this.dialogForm.organization_id,status:this.dialogForm.status};return t.show_type=this.dialogForm.show_type,"SOME"===this.dialogForm.show_type&&(t.org_ids=this.dialogForm.org_ids),"modify"===this.type&&(t.id=this.infoData.id),t},sendAddFormData:function(){var t=this;return p(l().mark((function e(){var r,n,o,i;return l().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.isLoading){e.next=2;break}return e.abrupt("return");case 2:return t.isLoading=!0,e.next=5,t.$to(t.$apis.apiBackgroundDrpWarehouseAddPost(t.formatFormData()));case 5:if(r=e.sent,n=c(r,2),o=n[0],i=n[1],t.isLoading=!1,!o){e.next=13;break}return t.$message.error(o.message),e.abrupt("return");case 13:0===i.code?(t.$message.success(i.msg),t.clearHandle(),t.confirmhandle&&t.confirmhandle()):t.$message.error(i.msg);case 14:case"end":return e.stop()}}),e)})))()},sendModifyFormData:function(){var t=this;return p(l().mark((function e(){var r,n,o,i;return l().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.isLoading){e.next=2;break}return e.abrupt("return");case 2:return t.isLoading=!0,e.next=5,t.$to(t.$apis.apiBackgroundDrpWarehouseModifyPost(t.formatFormData()));case 5:if(r=e.sent,n=c(r,2),o=n[0],i=n[1],t.isLoading=!1,!o){e.next=13;break}return t.$message.error(o.message),e.abrupt("return");case 13:0===i.code?(t.$message.success(i.msg),t.clearHandle(),t.confirmhandle&&t.confirmhandle()):t.$message.error(i.msg);case 14:case"end":return e.stop()}}),e)})))()}}},D=j,E=(r("9e7c"),r("2877")),S=Object(E["a"])(D,n,o,!1,null,"7762fa1a",null);e["default"]=S.exports},"9e7c":function(t,e,r){"use strict";r("f2df")},f2df:function(t,e,r){}}]);