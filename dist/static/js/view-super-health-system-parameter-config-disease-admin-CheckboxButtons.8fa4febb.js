(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-parameter-config-disease-admin-CheckboxButtons"],{"8aac":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"checkbox-buttons"},[e("el-checkbox-group",t._g(t._b({},"el-checkbox-group",t.$attrs,!1),t.$listeners),t._l(t.dataList,(function(n){return e("el-checkbox-button",{key:n[t.options.value],attrs:{label:n[t.options.value],disabled:n.disabled||t.disabledList.includes(n[t.options.value])}},[t._v(" "+t._s(n[t.options.label])+" ")])})),1)],1)},o=[],s={name:"CheckboxButton",inheritAttrs:!1,props:{options:{type:Object,default:function(){return{value:"id",label:"name"}}},dataList:{type:Array,default:function(){return[]}},disabledList:{type:Array,default:function(){return[]}}},data:function(){return{value:[]}},computed:{},watch:{},created:function(){},mounted:function(){},methods:{}},u=s,i=(n("93903"),n("2877")),c=Object(i["a"])(u,a,o,!1,null,null,null);e["default"]=c.exports},93903:function(t,e,n){"use strict";n("eac5a")},eac5a:function(t,e,n){}}]);