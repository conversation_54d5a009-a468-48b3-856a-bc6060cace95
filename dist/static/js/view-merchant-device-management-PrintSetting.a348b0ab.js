(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-device-management-PrintSetting"],{"699b":function(t,e,r){},"7f8a":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"PrintSetting container-wrapper"},[e("refresh-tool",{on:{refreshPage:t.refreshHandle}}),e("search-form",{ref:"searchRef",attrs:{"form-setting":t.searchFormSetting},on:{search:t.searchHandle}}),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("数据列表")]),e("div",{staticClass:"align-r"},[e("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(e){return t.gotoAddPrintDevice("add")}}},[t._v("新建打印设备")]),e("button-icon",{attrs:{color:"plain",type:"del"},on:{click:function(e){return t.mulOperation("mulDel")}}},[t._v("批量删除")])],1)]),e("div",{staticClass:"table-content"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:"","header-row-class-name":"ps-table-header-row"},on:{"selection-change":t.handleSelectionChange}},[e("el-table-column",{attrs:{type:"selection",width:"50",align:"center","class-name":"ps-checkbox"}}),e("el-table-column",{attrs:{prop:"consume_addr",label:"所属消费点",align:"center"}}),e("el-table-column",{attrs:{prop:"device_type_alias",label:"设备类型",align:"center"}}),e("el-table-column",{attrs:{prop:"create_time",label:"创建时间",align:"center"}}),e("el-table-column",{attrs:{fixed:"right",label:"操作",width:"180",align:"center"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.gotoAddPrintDevice("edit",r.row.device_no)}}},[t._v("编辑")]),e("el-button",{staticClass:"ps-red",attrs:{type:"text",size:"small"},on:{click:function(e){return t.mulOperation("del",r.row.device_no)}}},[t._v("删除")])]}}])})],1)],1),e("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[e("el-pagination",{staticClass:"ps-text",attrs:{"current-page":t.currentPage,"page-sizes":[10,20,50,100,500],"page-size":t.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:t.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)])],1)},i=[],o=r("ed08");function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},s=o.iterator||"@@iterator",u=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function h(t,e,r,n){var o=e&&e.prototype instanceof b?e:b,a=Object.create(o.prototype),c=new z(n||[]);return i(a,"_invoke",{value:j(t,r,c)}),a}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var d="suspendedStart",v="suspendedYield",g="executing",y="completed",m={};function b(){}function w(){}function L(){}var x={};f(x,s,(function(){return this}));var P=Object.getPrototypeOf,_=P&&P(P($([])));_&&_!==r&&n.call(_,s)&&(x=_);var S=L.prototype=b.prototype=Object.create(x);function O(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function r(i,o,c,s){var u=p(t[i],t,o);if("throw"!==u.type){var l=u.arg,f=l.value;return f&&"object"==a(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,c,s)}),(function(t){r("throw",t,c,s)})):e.resolve(f).then((function(t){l.value=t,c(l)}),(function(t){return r("throw",t,c,s)}))}s(u.arg)}var o;i(this,"_invoke",{value:function(t,n){function i(){return new e((function(e,i){r(t,n,e,i)}))}return o=o?o.then(i,i):i()}})}function j(e,r,n){var i=d;return function(o,a){if(i===g)throw Error("Generator is already running");if(i===y){if("throw"===o)throw a;return{value:t,done:!0}}for(n.method=o,n.arg=a;;){var c=n.delegate;if(c){var s=C(c,n);if(s){if(s===m)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===d)throw i=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=g;var u=p(e,r,n);if("normal"===u.type){if(i=n.done?y:v,u.arg===m)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(i=y,n.method="throw",n.arg=u.arg)}}}function C(e,r){var n=r.method,i=e.iterator[n];if(i===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,C(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var o=p(i,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,m;var a=o.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function D(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function z(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(D,this),this.reset(!0)}function $(e){if(e||""===e){var r=e[s];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function r(){for(;++i<e.length;)if(n.call(e,i))return r.value=e[i],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(a(e)+" is not iterable")}return w.prototype=L,i(S,"constructor",{value:L,configurable:!0}),i(L,"constructor",{value:w,configurable:!0}),w.displayName=f(L,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,L):(t.__proto__=L,f(t,l,"GeneratorFunction")),t.prototype=Object.create(S),t},e.awrap=function(t){return{__await:t}},O(k.prototype),f(k.prototype,u,(function(){return this})),e.AsyncIterator=k,e.async=function(t,r,n,i,o){void 0===o&&(o=Promise);var a=new k(h(t,r,n,i),o);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},O(S),f(S,l,"Generator"),f(S,s,(function(){return this})),f(S,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=$,z.prototype={constructor:z,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(E),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function i(n,i){return c.type="throw",c.arg=e,r.next=n,i&&(r.method="next",r.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],c=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var s=n.call(a,"catchLoc"),u=n.call(a,"finallyLoc");if(s&&u){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,m):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),E(r),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var i=n.arg;E(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:$(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),m}},e}function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function u(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach((function(e){l(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function l(t,e,r){return(e=f(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function f(t){var e=h(t,"string");return"symbol"==a(e)?e:e+""}function h(t,e){if("object"!=a(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=a(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function p(t,e,r,n,i,o,a){try{var c=t[o](a),s=c.value}catch(t){return void r(t)}c.done?e(s):Promise.resolve(s).then(n,i)}function d(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var o=t.apply(e,r);function a(t){p(o,n,i,a,c,"next",t)}function c(t){p(o,n,i,a,c,"throw",t)}a(void 0)}))}}var v={name:"PrintSetting",components:{},props:{},data:function(){return{isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[],searchFormSetting:{org_ids:{type:"consumeSelect",label:"消费点",value:"",placeholder:"请选择消费点"},device_type:{type:"select",label:"设备类型",value:[],placeholder:"请选择设备类型",listNameKey:"name",listValueKey:"key",dataList:[]}},selectListId:[]}},created:function(){this.initLoad()},watch:{$route:{handler:function(t){"change"===t.params.type&&this.initLoad()},immediate:!0}},mounted:function(){},methods:{initLoad:function(){this.getPrintSettingList(),this.getDeviceType()},searchHandle:Object(o["d"])((function(){this.currentPage=1,this.getPrintSettingList()}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.initLoad()},formatQueryParams:function(t){var e={};for(var r in t)""!==t[r].value&&null!==t[r].value&&0!==t[r].value.length&&("select_date"!==r?e[r]=t[r].value:t[r].value.length>0&&(e.start_date=t[r].value[0],e.end_date=t[r].value[1]));return e},getPrintSettingList:function(){var t=this;return d(c().mark((function e(){var r;return c().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.$apis.apiBackgroundDeviceDevicePrintConfListPost(u(u({},t.formatQueryParams(t.searchFormSetting)),{},{page:t.currentPage,page_size:t.pageSize}));case 3:r=e.sent,t.isLoading=!1,0===r.code?(t.tableData=r.data.results,t.totalCount=r.data.count):t.$message.error(r.msg);case 6:case"end":return e.stop()}}),e)})))()},handleSizeChange:function(t){this.pageSize=t,this.getPrintSettingList()},handleCurrentChange:function(t){this.currentPage=t,this.getPrintSettingList()},handleSelectionChange:function(t){var e=this;this.selectListId=[];var r=Object.freeze(t);r.map((function(t){e.selectListId.push(t.device_no)}))},mulOperation:function(t,e){var r=this;if(!e&&!this.selectListId.length)return this.$message.error("请先选择数据！");var n="提示",i="";switch(t){case"mulDel":i="确定批量删除所选打印设备吗？";break;case"del":i="确定删除该打印设备吗？";break}this.$confirm("".concat(i),"".concat(n),{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var n=d(c().mark((function n(i,o,a){var s;return c().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if("confirm"!==i){n.next=15;break}o.confirmButtonLoading=!0,s={},n.t0=t,n.next="mulDel"===n.t0?6:"del"===n.t0?8:10;break;case 6:return s.device_nos=r.selectListId,n.abrupt("break",10);case 8:return s.device_nos=[e],n.abrupt("break",10);case 10:r.delPrintDevice(s),a(),o.confirmButtonLoading=!1,n.next=16;break;case 15:o.confirmButtonLoading||a();case 16:case"end":return n.stop()}}),n)})));function i(t,e,r){return n.apply(this,arguments)}return i}()}).then((function(t){})).catch((function(t){}))},delPrintDevice:function(t){var e=this;return d(c().mark((function r(){var n;return c().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,e.$apis.apiBackgroundDeviceDevicePrintConfBatchDeletePost(t);case 2:n=r.sent,0===n.code?(e.$message.success("删除成功"),e.getPrintSettingList()):e.$message.error(n.msg);case 4:case"end":return r.stop()}}),r)})))()},gotoAddPrintDevice:function(t,e){this.$router.push({name:"MerchantAddPrintDevice",params:{type:t},query:{type:t,id:e}})},getDeviceType:function(){var t=this;return d(c().mark((function e(){var r;return c().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.apiBackgroundDeviceDeviceDeviceTypePost();case 2:r=e.sent,0===r.code?t.searchFormSetting.device_type.dataList=r.data:t.$message.error(r.msg);case 4:case"end":return e.stop()}}),e)})))()}}},g=v,y=(r("8ca1"),r("2877")),m=Object(y["a"])(g,n,i,!1,null,"366d5916",null);e["default"]=m.exports},"8ca1":function(t,e,r){"use strict";r("699b")}}]);