(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-order-constants"],{"7bfc":function(e,l,a){"use strict";a.r(l),a.d(l,"recentSevenDay",(function(){return u})),a.d(l,"DEFAULT_NUTRITION",(function(){return b})),a.d(l,"ELEMENT_NUTRITION",(function(){return d})),a.d(l,"VITAMIN_NUTRITION",(function(){return c})),a.d(l,"NUTRITION_LIST",(function(){return s})),a.d(l,"DEVICE_STATUS",(function(){return m})),a.d(l,"PAYMENTSTATE",(function(){return k})),a.d(l,"MEALTYPE",(function(){return h})),a.d(l,"WITHDRAW_ORDER_SEARCH",(function(){return v})),a.d(l,"PAYMENT_ORDER_TYPE",(function(){return f})),a.d(l,"APPEAL_STATUS",(function(){return E})),a.d(l,"DEAL_STATUS",(function(){return w})),a.d(l,"SUB_PAYWAY",(function(){return g})),a.d(l,"PAYMENTMETHOD",(function(){return R})),a.d(l,"APPEAL_ORDER_SEARCH_PENDING",(function(){return N})),a.d(l,"APPEAL_ORDER_SEARCH_PROCESSED",(function(){return A})),a.d(l,"CONSUMPTION_FAILURE",(function(){return D})),a.d(l,"APPROVE_ORDER",(function(){return O})),a.d(l,"CONSUMPTION_SCENE_TABLE",(function(){return L})),a.d(l,"CONSUMPTION_RESERVATION_TABLE",(function(){return C})),a.d(l,"GETMEALTYPE",(function(){return x})),a.d(l,"getRequestParams",(function(){return I})),a.d(l,"RECENTSEVEN",(function(){return P})),a.d(l,"RECENTONE",(function(){return U})),a.d(l,"REFUND_SCENE_TABLE",(function(){return G})),a.d(l,"REFUND_RESERVATION_TABLE",(function(){return H})),a.d(l,"REFUNDSTATUS",(function(){return Y})),a.d(l,"REFUNDTYPE",(function(){return F})),a.d(l,"REFUND_SCENE_SEARCH",(function(){return z})),a.d(l,"REFUND_RESERVATION_SEARCH",(function(){return M})),a.d(l,"RECHARGE_TABLE",(function(){return B})),a.d(l,"RECHARGE_REFUND_TABLE",(function(){return W})),a.d(l,"RECHARGE_TYPEARR",(function(){return j})),a.d(l,"RECHARGE_STATEARR",(function(){return V})),a.d(l,"REFUND_STATEARR",(function(){return K})),a.d(l,"APPEAL_PENDING_TABLE",(function(){return J})),a.d(l,"APPEAL_PROCESSED_TABLE",(function(){return q})),a.d(l,"CONSUMPTION_SCENE_AUTO_TABLE",(function(){return X})),a.d(l,"REFUND_ORDER_AUTO_TABLE",(function(){return Z})),a.d(l,"COLLECT_ORDER_AUTO_TABLE",(function(){return Q})),a.d(l,"REFUND_TYPE",(function(){return $})),a.d(l,"REFUND_TYPE_LIST",(function(){return ee})),a.d(l,"CONSUMPTION_SCENE_AUTO_SEARCH",(function(){return le})),a.d(l,"REFUND_ORDER_AUTO_SEARCH",(function(){return ae})),a.d(l,"COLLECT_ORDER_AUTO_SEARCH",(function(){return te})),a.d(l,"DIC_DATE_TYPE_REUND",(function(){return ye})),a.d(l,"DIC_DATE_TYPE",(function(){return ne})),a.d(l,"DIC_EXAMINE_STATUS",(function(){return ie})),a.d(l,"DIC_BASE_INFO_TABLE_SETTING",(function(){return oe})),a.d(l,"RECHARGE_REFUNDE_SEARCH_SETTING",(function(){return re})),a.d(l,"RECHARGE_REFUNDE_SEARCH_SETTING_AGREE",(function(){return pe})),a.d(l,"RECHARGE_REFUNDE_TABLE_SETTING_PENDING",(function(){return _e})),a.d(l,"RECHARGE_REFUNDE_TABLE_SETTING_AGREE",(function(){return ue})),a.d(l,"RECHARGE_REFUNDE_TABLE_SETTING_REFUND",(function(){return be})),a.d(l,"RECHARGE_REFUNDE_TABLE_SETTING_CANCEL",(function(){return de})),a.d(l,"RECHARGE_WITHDRAW_SEARCH_SETTING",(function(){return ce})),a.d(l,"RECHARGE_WITHDRAW_SEARCH_SETTING_ORG",(function(){return se})),a.d(l,"RECHARGE_WITHDRAW_SEARCH_SETTING_ORDER",(function(){return me})),a.d(l,"RECHARGE_WITHDRAW_TABLE_SETTING_PENDING",(function(){return ke})),a.d(l,"RECHARGE_WITHDRAW_TABLE_SETTING_AGREEN",(function(){return he})),a.d(l,"RECHARGE_WITHDRAW_TABLE_SETTING_REFUND",(function(){return ve})),a.d(l,"RECHARGE_WITHDRAW_TABLE_SETTING_CANCEL",(function(){return fe}));var t=a("ed08"),y=a("5a0c");function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function i(e,l){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);l&&(t=t.filter((function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable}))),a.push.apply(a,t)}return a}function o(e){for(var l=1;l<arguments.length;l++){var a=null!=arguments[l]?arguments[l]:{};l%2?i(Object(a),!0).forEach((function(l){r(e,l,a[l])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):i(Object(a)).forEach((function(l){Object.defineProperty(e,l,Object.getOwnPropertyDescriptor(a,l))}))}return e}function r(e,l,a){return(l=p(l))in e?Object.defineProperty(e,l,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[l]=a,e}function p(e){var l=_(e,"string");return"symbol"==n(l)?l:l+""}function _(e,l){if("object"!=n(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var t=a.call(e,l||"default");if("object"!=n(t))return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===l?String:Number)(e)}var u=[y().subtract(7,"day").format("YYYY-MM-DD"),y().format("YYYY-MM-DD")],b=[{name:"千焦耳",key:"energy_mj",unit:"kj",type:"default"},{name:"千卡",key:"energy_kcal",unit:"Kcal",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"}],d=[{key:"Ca",name:"钙",unit:"mg",type:"element"},{key:"P",name:"磷",unit:"mg",type:"element"},{key:"K",name:"钾",unit:"mg",type:"element"},{key:"Na",name:"钠",unit:"mg",type:"element"},{name:"镁",key:"Mg",unit:"mg",type:"element"},{key:"Fe",name:"铁",unit:"mg",type:"element"},{key:"I",name:"碘",unit:"μg",type:"element"},{key:"Se",name:"硒",unit:"μg",type:"element"},{key:"Zn",name:"锌",unit:"μg",type:"element"},{key:"Cu",name:"铜",unit:"μg",type:"element"},{key:"F",name:"氟",unit:"μg",type:"element"},{key:"Cr",name:"铬",unit:"μg",type:"element"},{key:"Mo",name:"钼",unit:"μg",type:"element"},{key:"Mn",name:"锰",unit:"μg",type:"element"}],c=[{key:"VA",name:"维生素A",unit:"μg",type:"vitamin"},{key:"VD",name:"维生素D",unit:"μg",type:"vitamin"},{key:"VE",name:"维生素E",unit:"μg",type:"vitamin"},{key:"VK",name:"维生素K",unit:"μg",type:"vitamin"},{key:"VB1",name:"维生素B1",unit:"mg",type:"vitamin"},{key:"VB2",name:"维生素B2",unit:"mg",type:"vitamin"},{key:"VB6",name:"维生素B6",unit:"mg",type:"vitamin"},{key:"VB12",name:"维生素B12",unit:"μg",type:"vitamin"},{key:"VC",name:"维生素C",unit:"mg",type:"vitamin"},{key:"VB5",name:"泛酸",unit:"mg",type:"vitamin"},{key:"VM",name:"叶酸",unit:"μg",type:"vitamin"},{key:"VB3",name:"烟酸",unit:"μg",type:"vitamin"},{key:"Choline",name:" 胆碱",unit:"mg",type:"vitamin"},{key:"Nicotinamide",name:"烟酰胺",unit:"mg",type:"vitamin"},{key:"VH",name:"生物素",unit:"mg",type:"vitamin"}],s=[].concat(b,d,c),m=[{label:"全部",value:""},{label:"在线",value:"ONLINE"},{label:"离线",value:"OFFLINE"}],k=[{label:"待支付",value:"ORDER_PAYING"},{label:"支付成功",value:"ORDER_SUCCESS"},{label:"支付失败",value:"ORDER_FAILED"},{label:"交易冲正中",value:"ORDER_REVERSALING"},{label:"交易冲正",value:"ORDER_REVERSAL"},{label:"退款中",value:"ORDER_REFUNDING"},{label:"已退款",value:"ORDER_REFUND_SUCCESS"},{label:"关闭(用户未支付)",value:"ORDER_CLOSE"},{label:"过期",value:"ORDER_TIME_OUT"},{label:"未知",value:"ORDER_UNKNOWN"}],h=[{id:1,label:"全部",value:"all"},{id:2,label:"早餐",value:"breakfast"},{id:3,label:"午餐",value:"lunch"},{id:4,label:"下午茶",value:"afternoon"},{id:5,label:"晚餐",value:"dinner"},{id:6,label:"夜宵",value:"supper"},{id:7,label:"凌晨餐",value:"morning"}],v={selectTime:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"提现时间",clearable:!0,value:Object(t["t"])(-7)},orderStatus:{type:"select",label:"提现状态",value:"",placeholder:"请选择",dataList:[{label:"提现中",value:"ORDER_PAYING"},{label:"提现失败",value:"ORDER_FAILED"},{label:"提现成功",value:"ORDER_SUCCESS"}]},tradeNo:{type:"input",value:"",label:"提现订单号",placeholder:"请输入"},name:{type:"input",value:"",label:"姓名",placeholder:"请输入"},phone:{type:"input",value:"",label:"手机号",placeholder:"请输入"},personNo:{type:"input",value:"",label:"人员编号",placeholder:"请输入"},account_username:{type:"input",value:"",label:"操作员",placeholder:"请输入操作员"}},f=[{label:"预约订单",value:"reservation"},{label:"报餐",value:"report_meal"},{label:"称重",value:"buffet"},{label:"到店就餐",value:"instore"},{label:"其他",value:"other"}],E=[{label:"全部",value:""},{label:"待审核",value:"wait"},{label:"已审核",value:"success"},{label:"已拒绝",value:"reject"},{label:"已撤回",value:"cancel"}],w=[{label:"全部",value:""},{label:"修改金额",value:"CHANGE_FEE"},{label:"整单不扣费",value:"ORDER_FREE"},{label:"驳回",value:"ORDER_REJECT"},{label:"退款",value:"ORDER_REFUND"},{label:"挂起中",value:"HANG_OUT"}],g=[{label:"储值钱包支付",value:"wallet"},{label:"电子钱包支付",value:"ewallet"},{label:"第三方钱包支付",value:"twallet"},{label:"授权代扣支付",value:"daikou"},{label:"数字人民币支付",value:"ermb"},{label:"JSAPI支付",value:"jsapi"},{label:"H5支付",value:"h5"},{label:"WAP支付",value:"wap"},{label:"小程序支付",value:"miniapp"},{label:"现金支付",value:"cash"},{label:"B扫C支付",value:"micropay"},{label:"C扫B支付",value:"scanpay"},{label:"刷卡支付",value:"cardpay"},{label:"刷脸支付",value:"facepay"},{label:"会员码支付",value:"facecode"},{label:"缴费方式支付",value:"jf"},{label:"快e付支付",value:"fastepay"}],R=[{label:"朴食储值支付",value:"PushiPay"},{label:"一卡通-鑫澳康支付",value:"OCOMPAY"},{label:"一卡通-石药支付",value:"SHIYAOPAY"},{label:"农行支付",value:"ABCPay"},{label:"建行支付",value:"CCBPAY"},{label:"中行支付",value:"BOCPAY"},{label:"工行支付",value:"ICBCPAY"},{label:"美团支付",value:"MEITUANPAY"},{label:"收钱吧支付",value:"ShouqianbaPay"},{label:"微信支付",value:"WechatPay"},{label:"未知",value:"UNKNOWN"},{label:"现金支付",value:"CashPay"}],T={trade_no:{type:"input",value:"",label:"订单号",placeholder:"请输入"},name:{type:"input",value:"",label:"姓名",placeholder:"请输入"},phone:{type:"input",value:"",label:"手机号",placeholder:"请输入"},person_no:{type:"input",value:"",label:"人员编号",placeholder:"请输入"}},S={payment_order_type:{type:"select",label:"订单类型",value:"",placeholder:"请选择",dataList:f},org_ids:{type:"organizationSelect",value:[],label:"消费点",checkStrictly:!0,isLazy:!1,multiple:!0},payer_group_ids:{type:"groupSelect",label:"分组",value:[],placeholder:"请选择分组",multiple:!0,collapseTags:!0},payer_department_group_ids:{type:"organizationDepartmentSelect",value:[],label:"部门"},appeal_status:{type:"select",label:"申诉状态",value:"",placeholder:"请选择",dataList:E},wallet_org:{type:"organizationSelect",value:[],label:"动账组织",checkStrictly:!0,isLazy:!1,multiple:!0}},N=o(o(o({select_time:{type:"datetimerange",label:"申诉时间",clearable:!0,value:Object(t["t"])(-7)}},T),{},{sub_payway:{type:"select",label:"支付类型",value:"",placeholder:"请选择",dataList:g},payway:{type:"select",label:"支付方式",value:"",placeholder:"请选择",dataList:R}},S),{},{deal_status:{type:"select",label:"处理状态",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"待处理",value:"PENDING"},{label:"挂起中",value:"HANG_OUT"}]},out_trade_no:{type:"input",value:"",label:"第三方订单号",placeholder:"请输入订单号"},only_rate_fee:{type:"checkbox",label:"",checkboxLabel:"只看手续费",value:!1}}),A=o(o(o({date_type:{type:"select",label:"",value:1,maxWidth:"130px",placeholder:"请选择",dataList:[{label:"申诉时间",value:1},{label:"处理时间",value:2}]},select_time:{type:"datetimerange",label:"",clearable:!0,value:Object(t["t"])(-7)}},T),S),{},{deal_status:{type:"select",label:"处理结果",value:"",placeholder:"请选择",dataList:w},account_username:{type:"input",value:"",label:"操作员",placeholder:"请输入操作员"},out_trade_no:{type:"input",value:"",label:"第三方订单号",placeholder:"请输入订单号"},only_rate_fee:{type:"checkbox",label:"",checkboxLabel:"只看手续费",value:!1}}),D={trade_no:{type:"input",value:"",label:"订单号",placeholder:"请输入要搜索的订单号"},name:{type:"input",value:"",label:"用户姓名",placeholder:"请输入要搜索的用户姓名"},phone:{type:"input",value:"",label:"手机号",placeholder:"请输入手机号"},person_no:{type:"input",value:"",label:"人员编号",placeholder:"请输入人员编号"},payer_department_group_ids:{type:"organizationDepartmentSelect",value:[],label:"部门"},payer_group_ids:{type:"groupSelect",label:"分组",value:[],placeholder:"请选择分组",multiple:!0,collapseTags:!0},consume_organization_ids:{type:"organizationSelect",value:[],label:"消费点",checkStrictly:!0,isLazy:!1,multiple:!0},meal_type_list:{type:"select",label:"餐段",value:[],multiple:!0,collapseTags:!0,placeholder:"请选择",dataList:h.slice(1)},select_time:{type:"datetimerange",label:"创建时间",clearable:!0,value:Object(t["t"])(-7)}},O={selectTime:{type:"daterange",label:"申请时间",clearable:!1,value:Object(t["t"])(-7,{format:"{y}-{m}-{d}"})},orderType:{type:"select",label:"订单类型",value:"",placeholder:"请选择",clearable:!0,dataList:[{label:"全部",value:""},{label:"预约订单",value:"reservation"},{label:"报餐订单",value:"report"}]},payerGroupIds:{type:"groupSelect",label:"分组",value:[],placeholder:"请选择分组",multiple:!0,clearable:!0,collapseTags:!0},payerDepartmentGroupIds:{type:"organizationDepartmentSelect",value:[],label:"部门",clearable:!0},organization_id:{type:"organizationSelect",value:"",label:"动账组织",checkStrictly:!0,isLazy:!1,clearable:!0,multiple:!1},reviewReason:{type:"select",label:"申诉原因",value:"",placeholder:"请选择",clearable:!0,dataList:[{label:"全部",value:""},{label:"食堂原因",value:"canteen"},{label:"个人原因",value:"self"}]}},L=[{label:"序号",key:"index",type:"index",width:"80"},{label:"总单号",key:"unified_out_trade_no",width:"150"},{label:"订单号",key:"trade_no",width:"150"},{label:"第三方订单号",key:"out_trade_no",width:"150"},{label:"创建时间",key:"create_time",width:"150",sortable:"custom"},{label:"支付时间",key:"pay_time",width:"150",sortable:"custom"},{label:"扣款时间",key:"deduction_time",width:"150",sortable:"custom"},{label:"订单金额",key:"origin_fee",type:"money"},{label:"优惠金额",key:"discount_fee",type:"money"},{label:"优惠类型",key:"discount_type_alias"},{label:"餐补",key:"food_subsidy_fee",type:"money"},{label:"手续费",key:"rate_fee",type:"money"},{label:"实收金额",key:"pay_fee",type:"money"},{label:"补贴动账",key:"subsidy_fee",type:"money"},{label:"储值动账",key:"wallet_fee",type:"money"},{label:"赠送动账",key:"complimentary_fee",type:"money"},{label:"补贴钱包余额",key:"subsidy_balance",type:"money"},{label:"储值钱包余额",key:"wallet_balance",type:"money"},{label:"赠送钱包余额",key:"complimentary_balance",type:"money"},{label:"支付方式",key:"sub_payway_alias"},{label:"支付类型",key:"payway_alias"},{label:"支付状态",key:"order_status_alias",type:"slot",slotName:"orderStatusAlias"},{label:"交易设备",key:"device_name"},{label:"设备状态",key:"pay_device_status_alias"},{label:"制作状态",key:"making_type_alias"},{label:"对账状态",key:"settle_status_alias"},{label:"动账组织",key:"wallet_org"},{label:"餐段",key:"meal_type_alias"},{label:"用户姓名",key:"name"},{label:"人员编号",key:"person_no"},{label:"手机号",key:"phone"},{label:"分组",key:"payer_group_name"},{label:"部门",key:"payer_department_group_name"},{label:"备注",key:"remark"},{label:"操作员",key:"controller"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"100"}],C=[{label:"序号",key:"index",type:"index",width:"80"},{label:"总单号",key:"unified_out_trade_no",width:"150"},{label:"订单号",key:"trade_no",width:"150"},{label:"第三方订单号",key:"out_trade_no",width:"150"},{label:"创建时间",key:"create_time",width:"150",sortable:"custom"},{label:"支付时间",key:"pay_time",width:"150",sortable:"custom"},{label:"预约时间",key:"reservation_date",width:"150",sortable:"custom"},{label:"报餐时间",key:"report_date",width:"150",sortable:"custom"},{label:"订单金额",key:"origin_fee",type:"money"},{label:"优惠金额",key:"discount_fee",type:"money"},{label:"优惠类型",key:"discount_type_alias"},{label:"券类型",key:"coupon_type_alias"},{label:"抵扣金额",key:"deduction_fee",type:"money"},{label:"手续费",key:"rate_fee",type:"money"},{label:"餐补",key:"food_subsidy_fee",type:"money"},{label:"服务费",key:"fuwu_fee",type:"money"},{label:"实收金额",key:"pay_fee",type:"money"},{label:"补贴动账",key:"subsidy_fee",type:"money"},{label:"储值动账",key:"wallet_fee",type:"money"},{label:"赠送动账",key:"complimentary_fee",type:"money"},{label:"补贴钱包余额",key:"subsidy_balance",type:"money"},{label:"储值钱包余额",key:"wallet_balance",type:"money"},{label:"赠送钱包余额",key:"complimentary_balance",type:"money"},{label:"支付方式",key:"sub_payway_alias"},{label:"支付类型",key:"payway_alias"},{label:"支付状态",key:"order_status_alias"},{label:"制作状态",key:"making_type_alias"},{label:"对账状态",key:"settle_status_alias"},{label:"动账组织",key:"wallet_org"},{label:"餐段",key:"meal_type_alias"},{label:"取餐方式",key:"take_meal_type_alias"},{label:"配送地址",key:"delivery_addr",width:"150",showTooltip:!0},{label:"用户姓名",key:"name"},{label:"人员编号",key:"person_no"},{label:"手机号",key:"phone"},{label:"分组",key:"payer_group_name"},{label:"部门",key:"payer_department_group_name"},{label:"备注",key:"remark"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"100"}],x=[{label:"堂食",value:"on_scene"},{label:"食堂自提",value:"bale"},{label:"外卖配送",value:"waimai"},{label:"配送柜自取",value:"cupboard"}],I=function(e,l,a){var t,y,n,i,r,p,_={};Object.keys(e).forEach((function(l){("select_time"!==l&&"create_time"!==l&&"payment_time"!==l&&"finish_time"!==l&&"pay_time"!==l&&"third_and_trade_no"!==l&&""!==e[l].value&&e[l].value||"boolean"===typeof e[l].value)&&(_[l]=e[l].value)}));var u=o({page:l,page_size:a},_);return 2===(null===(t=e.select_time)||void 0===t||null===(t=t.value)||void 0===t?void 0:t.length)&&(u.start_date=e.select_time.value[0],u.end_date=e.select_time.value[1]),2===(null===(y=e.pay_time)||void 0===y||null===(y=y.value)||void 0===y?void 0:y.length)&&(u.start_pay_time=e.pay_time.value[0]+"T00:00:00",u.end_pay_time=e.pay_time.value[1]+"T23:59:59"),2===(null===(n=e.create_time)||void 0===n||null===(n=n.value)||void 0===n?void 0:n.length)&&(u.start_create_time=e.create_time.value[0]+"T00:00:00",u.end_create_time=e.create_time.value[1]+"T23:59:59"),2===(null===(i=e.payment_time)||void 0===i||null===(i=i.value)||void 0===i?void 0:i.length)&&(u.start_payment_time=e.payment_time.value[0]+"T00:00:00",u.end_payment_time=e.payment_time.value[1]+"T23:59:59"),2===(null===(r=e.finish_time)||void 0===r||null===(r=r.value)||void 0===r?void 0:r.length)&&(u.start_finish_time=e.finish_time.value[0]+"T00:00:00",u.end_finish_time=e.finish_time.value[1]+"T23:59:59"),null!==(p=e.third_and_trade_no)&&void 0!==p&&p.value&&(u.trade_no=e.third_and_trade_no.value,u.out_trade_no=e.third_and_trade_no.value),e.pay_time&&e.pay_time.value&&2===e.pay_time.value.length&&(u.start_date=e.pay_time.value[0],u.end_date=e.pay_time.value[1]),Reflect.has(e,"sort_name")&&Reflect.has(e,"sort_type")&&(u.sort_name=e.sort_name,u.sort_type=e.sort_type),u},P=[y().subtract(7,"day").format("YYYY-MM-DD"),y().format("YYYY-MM-DD")],U=[y().subtract(0,"day").format("YYYY-MM-DD"),y().format("YYYY-MM-DD")],G=[{label:"序号",key:"index",type:"index",width:"80"},{label:"总单号",key:"unified_out_trade_no",width:"160"},{label:"退款单号",key:"refund_no",width:"160"},{label:"退款时间",key:"create_time",width:"150"},{label:"退款到账时间",key:"finish_time",width:"150"},{label:"原订单号",key:"origin_trade_no",width:"160"},{label:"第三方订单号",key:"out_trade_no",width:"160"},{label:"原订单金额",key:"origin_fee",type:"money"},{label:"原订单实收金额",key:"real_fee",type:"money"},{label:"手续费",key:"rate_fee",type:"money"},{label:"服务费",key:"fuwu_fee",type:"money"},{label:"退款金额",key:"refund_fee",type:"money"},{label:"餐补返还金额",key:"food_subsidy_fee",type:"money"},{label:"补贴动账",key:"subsidy_fee",type:"money"},{label:"储值动账",key:"wallet_fee",type:"money"},{label:"赠送动账",key:"complimentary_fee",type:"money"},{label:"动账组织",key:"wallet_org"},{label:"退款类型",key:"refund_type_alias"},{label:"对账状态",key:"settle_status_alias"},{label:"退款渠道",key:"org_wallet_name"},{label:"餐段",key:"meal_type_alias"},{label:"用户姓名",key:"payer_name"},{label:"人员编号",key:"payer_person_no"},{label:"手机号",key:"payer_phone"},{label:"分组",key:"payer_group"},{label:"部门",key:"payer_department_group"},{label:"退款状态",key:"order_status_alias"},{label:"备注",key:"remark",showTooltip:!0},{label:"操作员",key:"account_alias"}],H=[{label:"序号",key:"index",type:"index",width:"80"},{label:"总单号",key:"unified_out_trade_no",width:"160"},{label:"退款单号",key:"refund_no",width:"160"},{label:"退款时间",key:"create_time",width:"150"},{label:"退款到账时间",key:"finish_time",width:"150"},{label:"原订单号",key:"origin_trade_no",width:"160"},{label:"第三方订单号",key:"out_trade_no",width:"160"},{label:"原订单金额",key:"origin_fee",type:"money"},{label:"原订单实收金额",key:"real_fee",type:"money"},{label:"手续费",key:"rate_fee",type:"money"},{label:"服务费",key:"fuwu_fee",type:"money"},{label:"退款金额",key:"refund_fee",type:"money"},{label:"餐补返还金额",key:"food_subsidy_fee",type:"money"},{label:"补贴动账",key:"subsidy_fee",type:"money"},{label:"储值动账",key:"wallet_fee",type:"money"},{label:"赠送动账",key:"complimentary_fee",type:"money"},{label:"动账组织",key:"wallet_org"},{label:"退款类型",key:"refund_type_alias"},{label:"对账状态",key:"settle_status_alias"},{label:"退款渠道",key:"org_wallet_name"},{label:"餐段",key:"meal_type_alias"},{label:"取餐方式",key:"take_meal_type_alias"},{label:"用户姓名",key:"payer_name"},{label:"人员编号",key:"payer_person_no"},{label:"手机号",key:"payer_phone"},{label:"分组",key:"payer_group"},{label:"部门",key:"payer_department_group"},{label:"退款状态",key:"order_status_alias"},{label:"备注",key:"remark",showTooltip:!0},{label:"操作员",key:"account_alias"}],Y=[{label:"全部",value:""},{label:"退款中",value:"ORDER_REFUNDING"},{label:"已退款",value:"ORDER_SUCCESS"}],F=[{label:"全部",value:""},{label:"全额退款",value:"ORDER_ALL_REFUND"},{label:"部分退款",value:"ORDER_PART_REFUND"}],z={refund_no:{type:"input",value:"",label:"退款订单号",placeholder:"请输入退款订单号"},origin_trade_no:{type:"input",value:"",label:"原订单号",placeholder:"请输入原订单号"},name:{type:"input",value:"",label:"用户姓名",placeholder:"请输入用户姓名"},phone:{type:"input",value:"",label:"手机号",placeholder:"请输入手机号"},person_no:{type:"input",value:"",label:"人员编号",placeholder:"请输入人员编号"},order_status:{type:"select",label:"退款状态",value:"",placeholder:"请选择退款状态",dataList:Y},refund_type:{type:"select",label:"退款类型",value:"",placeholder:"请选择退款类型",dataList:F},payer_group_ids:{type:"groupSelect",label:"分组",value:[],placeholder:"请选择分组",multiple:!0,collapseTags:!0},consume_organization_ids:{type:"organizationSelect",value:[],label:"消费点",checkStrictly:!0,isLazy:!1,multiple:!0},payer_department_group_ids:{type:"organizationDepartmentSelect",value:[],label:"部门"},create_time:{type:"daterange",label:"退款时间",clearable:!1,value:P},account_username:{type:"input",value:"",label:"操作员",placeholder:"请输入操作员"},only_rate_fee:{type:"checkbox",label:"",checkboxLabel:"只看手续费",value:!1},only_debt_fee:{type:"checkbox",label:"",checkboxLabel:"只看透支",value:!1},out_trade_no:{type:"input",value:"",label:"第三方订单号",placeholder:"请输入订单号"}},M={refund_no:{type:"input",value:"",label:"退款订单号",placeholder:"请输入退款订单号"},origin_trade_no:{type:"input",value:"",label:"原订单号",placeholder:"请输入原订单号"},name:{type:"input",value:"",label:"用户姓名",placeholder:"请输入用户姓名"},phone:{type:"input",value:"",label:"手机号",placeholder:"请输入手机号"},person_no:{type:"input",value:"",label:"人员编号",placeholder:"请输入人员编号"},order_status:{type:"select",label:"退款状态",value:"",placeholder:"请选择退款状态",dataList:Y},refund_type:{type:"select",label:"退款类型",value:"",placeholder:"请选择退款类型",dataList:F},take_meal_type:{type:"select",label:"取餐方式",value:"",placeholder:"请选择取餐方式",dataList:x},payer_group_ids:{type:"groupSelect",label:"分组",value:[],placeholder:"请选择分组",multiple:!0,collapseTags:!0},consume_organization_ids:{type:"organizationSelect",value:[],label:"消费点",checkStrictly:!0,isLazy:!1,multiple:!0},payer_department_group_ids:{type:"organizationDepartmentSelect",value:[],label:"部门"},create_time:{type:"daterange",label:"退款时间",clearable:!1,value:P},account_username:{type:"input",value:"",label:"操作员",placeholder:"请输入操作员"},only_rate_fee:{type:"checkbox",label:"",checkboxLabel:"只看手续费",value:!1},only_debt_fee:{type:"checkbox",label:"",checkboxLabel:"只看透支",value:!1},out_trade_no:{type:"input",value:"",label:"第三方订单号",placeholder:"请输入订单号"}},B=[{label:"序号",key:"index",type:"index",width:"80"},{label:"充值订单号",key:"trade_no",width:"160"},{label:"充值时间",key:"create_time",width:"160"},{label:"充值到账时间",key:"finish_time",width:"150"},{label:"充值金额",key:"pay_fee",type:"money"},{label:"手续费",key:"rate_fee",type:"money"},{label:"储值钱包到账",key:"wallet_fee",type:"money"},{label:"赠送钱包到账",key:"complimentary_fee",type:"money"},{label:"动账钱包",key:"wallet_name"},{label:"姓名",key:"name"},{label:"人员编号",key:"person_no"},{label:"手机号码",key:"phone"},{label:"充值类型",key:"pay_scene_alias"},{label:"充值渠道",key:"payway_alias"},{label:"第三方订单号",key:"out_trade_no",width:"160"},{label:"交易流水号",key:"provider_trade_no",width:"160"},{label:"充值状态",key:"order_status_alias"},{label:"对账状态",key:"settle_status_alias"},{label:"退款状态",key:"refund_type_alias"},{label:"备注",key:"remark"},{label:"操作员",key:"account_alias"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"100"}],W=[{label:"序号",key:"index",type:"index",width:"80"},{label:"充值退款订单号",key:"refund_no",width:"160"},{label:"退款时间",key:"create_time",width:"160"},{label:"退款金额",key:"refund_fee",type:"money"},{label:"手续费",key:"rate_fee",type:"money"},{label:"原充值金额",key:"origin_fee",type:"money"},{label:"储值钱包到账",key:"wallet_fee",type:"money"},{label:"动账钱包",key:"wallet_name"},{label:"原赠送钱包到账",key:"complimentary_fee",type:"money"},{label:"赠送钱包退款金额",key:"complimentary_fee_2",type:"money"},{label:"姓名",key:"name"},{label:"人员编号",key:"person_no"},{label:"手机号码",key:"phone"},{label:"退款类型",key:"pay_scene_alias"},{label:"退款方式",key:"refund_type_alias"},{label:"退款渠道",key:"payway_alias"},{label:"第三方订单号",key:"out_trade_no",width:"160"},{label:"交易流水号",key:"provider_trade_no",width:"160"},{label:"对账状态",key:"settle_status_alias"},{label:"退款状态",key:"order_status_alias"},{label:"备注",key:"remark",showTooltip:!0},{label:"操作员",key:"account_alias"}],j=[{label:"全部",value:""},{label:"线上",value:"charge"},{label:"线下",value:"charge_offline"}],V=[{label:"全部",value:""},{label:"充值中",value:"ORDER_REVERSALING"},{label:"充值失败",value:"ORDER_FAILED"},{label:"充值成功",value:"ORDER_SUCCESS"}],K=[{label:"全部",value:""},{label:"退款中",value:"ORDER_REFUNDING"},{label:"退款失败",value:"ORDER_FAILED"},{label:"退款成功",value:"ORDER_SUCCESS"}],J=[{label:"序号",key:"index",type:"index",width:"80"},{label:"订单号",key:"trade_no",width:"160"},{label:"第三方订单号",key:"out_trade_no",width:"160"},{label:"申诉时间",key:"create_time",width:"160"},{label:"剩余处理时间",key:"residual_time",width:"160",type:"slot",slotName:"residual"},{label:"订单金额",key:"origin_fee"},{label:"实收金额",key:"pay_fee"},{label:"手续费",key:"watting_rate_fee"},{label:"补贴动账",key:"subsidy_fee"},{label:"储值动账",key:"wallet_fee"},{label:"赠送动账",key:"complimentary_fee"},{label:"补贴钱包余额",key:"subsidy_balance"},{label:"储值钱包余额",key:"wallet_balance"},{label:"赠送钱包余额",key:"complimentary_balance"},{label:"动账组织",key:"wallet_org"},{label:"订单类型",key:"payment_order_type_alias"},{label:"支付类型",key:"payway_alias"},{label:"支付方式",key:"sub_payway_alias"},{label:"支付状态",key:"order_status_alias"},{label:"对账状态",key:"settle_status_alias"},{label:"申诉状态",key:"appeal_status_alias"},{label:"处理状态",key:"deal_status_alias"},{label:"申诉原因",key:"appeal_reason"},{label:"姓名",key:"name"},{label:"人员编号",key:"person_no"},{label:"手机号",key:"phone"},{label:"分组",key:"payer_group_name"},{label:"部门",key:"payer_department_group_name"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"100"}],q=[{label:"序号",key:"index",type:"index",width:"80"},{label:"订单号",key:"trade_no",width:"160"},{label:"第三方订单号",key:"out_trade_no",width:"160"},{label:"申诉时间",key:"create_time",width:"160"},{label:"处理时间",key:"finish_time",width:"160"},{label:"退款单号",key:"refund_no",width:"160"},{label:"退款时间",key:"finish_time",width:"160"},{label:"订单金额",key:"origin_fee"},{label:"实收金额",key:"pay_fee"},{label:"手续费",key:"deal_rate_fee"},{label:"退款金额",key:"refund_fee"},{label:"补贴动账",key:"subsidy_fee"},{label:"储值动账",key:"wallet_fee"},{label:"赠送动账",key:"complimentary_fee"},{label:"补贴钱包余额",key:"subsidy_balance"},{label:"储值钱包余额",key:"wallet_balance"},{label:"赠送钱包余额",key:"complimentary_balance"},{label:"动账组织",key:"wallet_org"},{label:"订单类型",key:"payment_order_type_alias"},{label:"支付类型",key:"payway_alias"},{label:"支付方式",key:"sub_payway_alias"},{label:"支付状态",key:"order_status_alias"},{label:"对账状态",key:"settle_status_alias"},{label:"申诉状态",key:"appeal_status_alias"},{label:"处理结果",key:"deal_status_alias"},{label:"申诉原因",key:"appeal_reason"},{label:"姓名",key:"name"},{label:"人员编号",key:"person_no"},{label:"手机号",key:"phone"},{label:"分组",key:"payer_group_name"},{label:"部门",key:"payer_department_group_name"},{label:"操作员",key:"account_alias"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"100"}],X=[{label:"序号",key:"index",width:"80",type:"slot",slotName:"index"},{label:"总单号",key:"unified_out_trade_no",width:"150"},{label:"订单号",key:"trade_no",width:"150"},{label:"创建时间",key:"create_time",width:"150"},{label:"支付时间",key:"pay_time",width:"150"},{label:"扣款时间",key:"deduction_time",width:"150"},{label:"储值动账",key:"wallet_fee",type:"money"},{label:"补贴动账",key:"subsidy_fee",type:"money"},{label:"赠送动账",key:"complimentary_fee",type:"money"},{label:"订单金额",key:"origin_fee",type:"money"},{label:"优惠金额",key:"discount_fee",type:"money"},{label:"优惠类型",key:"discount_type_alias"},{label:"餐段",key:"meal_type_alias"},{label:"餐补",key:"food_subsidy_fee",type:"money"},{label:"手续费",key:"rate_fee",type:"money"},{label:"实收金额",key:"pay_fee",type:"money"},{label:"储值钱包余额",key:"wallet_balance",type:"money"},{label:"补贴钱包余额",key:"subsidy_balance",type:"money"},{label:"赠送钱包余额",key:"complimentary_balance",type:"money"},{label:"支付方式",key:"sub_payway_alias"},{label:"支付类型",key:"payway_alias"},{label:"支付状态",key:"order_status_alias"},{label:"交易设备",key:"device_name"},{label:"一级组织",key:"primary"},{label:"二级组织",key:"secondary"},{label:"动账组织",key:"wallet_org"},{label:"用户姓名",key:"name"},{label:"人员编号",key:"person_no"},{label:"手机号",key:"phone"},{label:"分组",key:"payer_group_name"},{label:"部门",key:"payer_department_group_name"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"100"}],Z=[{label:"序号",key:"index",width:"80",type:"slot",slotName:"index"},{label:"退款单号",key:"refund_no",width:"160"},{label:"总单号",key:"unified_out_trade_no",width:"160"},{label:"退款时间",key:"finish_time",width:"160"},{label:"原订单号",key:"origin_trade_no",width:"160"},{label:"第三方订单号",key:"out_trade_no",width:"160"},{label:"原订单金额",key:"origin_fee",type:"money"},{label:"原订单应收金额",key:"real_fee",type:"money"},{label:"手续费",key:"deal_rate_fee",type:"money"},{label:"退款金额",key:"refund_fee",type:"money"},{label:"储值动账",key:"wallet_fee",type:"money"},{label:"补贴动账",key:"subsidy_fee",type:"money"},{label:"赠送动账",key:"complimentary_fee",type:"money"},{label:"退款类型",key:"refund_type_alias"},{label:"对账状态",key:"settle_status_alias"},{label:"退款渠道",key:"payway_alias"},{label:"餐段",key:"meal_type_alias"},{label:"退款状态",key:"order_status_alias"},{label:"一级组织",key:"primary"},{label:"二级组织",key:"secondary"},{label:"动账组织",key:"wallet_org"},{label:"用户姓名",key:"payer_name"},{label:"人员编号",key:"payer_person_no"},{label:"手机号码",key:"payer_phone"},{label:"分组",key:"payer_group"},{label:"部门",key:"payer_department_group"},{label:"操作员",key:"account_alias"}],Q=[{label:"序号",key:"index",width:"80",type:"slot",slotName:"index"},{label:"商品名称",key:"name",width:"160"},{label:"单价",key:"raw_fee",width:"160",type:"money"},{label:"销售数量",key:"sale_count",width:"160",sortable:"custom"},{label:"销售额",key:"sale_money",width:"160",type:"money",sortable:"custom"},{label:"退款数量",key:"refund_count",width:"160"},{label:"退款金额",key:"refund_money",width:"160",type:"money"},{label:"实际销售数量",key:"real_sale_count",width:"160"},{label:"实收金额",key:"real_sale_money",width:"160",type:"money"}],$=[{label:"全部",value:""},{label:"部分退款",value:"ORDER_PART_REFUND"},{label:"全额退款",value:"ORDER_ALL_REFUND"}],ee=[{label:"部分退款",value:"ORDER_PART_REFUND"},{label:"全额退款",value:"ORDER_ALL_REFUND"},{label:"未退款",value:"NO_REFUND"}],le={date_type:{type:"select",value:"create_time",maxWidth:"130px",dataList:[{label:"创建时间",value:"create_time"},{label:"支付时间",value:"pay_time"},{label:"扣款时间",value:"deduction_time"}]},select_time:{type:"daterange",label:"",clearable:!1,value:P},payway_list:{type:"select",label:"支付类型",multiple:!0,collapseTags:!0,value:[],placeholder:"请选择",dataList:[]},sub_payway_list:{type:"select",label:"支付方式",multiple:!0,collapseTags:!0,value:[],placeholder:"请选择",dataList:[]},consume_organization_ids:{type:"organizationSelect",value:[],label:"消费点",checkStrictly:!0,isLazy:!1,multiple:!0,collapseTags:!0},wallet_org:{type:"organizationSelect",value:[],label:"动账组织",checkStrictly:!0,isLazy:!1,multiple:!0,collapseTags:!0},device_name_list:{type:"select",label:"交易设备",multiple:!0,checkStrictly:!0,value:[],listNameKey:"name",listValueKey:"key",placeholder:"请选择",clearable:!0,dataList:[],collapseTags:!0},meal_type_list:{type:"select",label:"餐段",value:[],multiple:!0,collapseTags:!0,placeholder:"请选择",dataList:h.slice(1)},payer_group_ids:{type:"groupSelect",label:"分组",value:[],placeholder:"请选择分组",multiple:!0,collapseTags:!0},payer_department_group_ids:{type:"organizationDepartmentSelect",value:[],label:"部门"},name:{type:"input",value:"",label:"用户姓名",placeholder:"请输入"},phone:{type:"input",value:"",label:"手机号",placeholder:"请输入"},trade_no:{type:"input",value:"",labelWidth:"145px",label:"总单号/订单号",placeholder:"请输入"},food_status:{type:"select",value:"",labelWidth:"145px",label:"出货状态",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"已出货",value:"success"},{label:"未出货",value:"fail"}]},order_status:{type:"select",label:"支付状态",value:"",placeholder:"请选择",dataList:k},only_discount:{type:"checkbox",label:"",checkboxLabel:"只看优惠",value:!1},only_rate_fee:{type:"checkbox",label:"",checkboxLabel:"只看手续费",value:!1},only_debt_fee:{type:"checkbox",label:"",checkboxLabel:"只看透支",value:!1}},ae={create_time:{type:"daterange",label:"退款时间",clearable:!1,value:P,format:"yyyy-MM-dd"},refund_no:{type:"input",value:"",labelWidth:"145px",label:"退款单号",placeholder:"请输入"},origin_trade_no:{type:"input",value:"",labelWidth:"145px",label:"原订单号",placeholder:"请输入"},payer_group_ids:{type:"groupSelect",label:"分组",value:[],placeholder:"请选择分组",multiple:!0,collapseTags:!0},payer_department_group_ids:{type:"organizationDepartmentSelect",value:[],label:"部门"},name:{type:"input",value:"",label:"用户姓名",placeholder:"请输入"},phone:{type:"input",value:"",label:"手机号码",placeholder:"请输入"},order_status:{type:"select",label:"退款状态",value:"",placeholder:"请选择",dataList:k},refund_type:{type:"select",label:"退款类型",value:"",placeholder:"请选择",dataList:$},consume_organization_ids:{type:"organizationSelect",value:[],label:"消费点",checkStrictly:!0,isLazy:!1,multiple:!0},only_rate_fee:{type:"checkbox",label:"",checkboxLabel:"只看手续费",value:!1},only_debt_fee:{type:"checkbox",label:"",checkboxLabel:"只看透支",value:!1}},te={pay_time:{type:"daterange",label:"支付时间",clearable:!1,value:U,format:"yyyy-MM-dd"},consume_organization_ids:{type:"organizationSelect",value:[],label:"消费点",checkStrictly:!0,isLazy:!1,multiple:!0},device_name_list:{type:"select",label:"交易设备",multiple:!0,value:[],listNameKey:"device_name",listValueKey:"device_name",placeholder:"请选择",clearable:!0,dataList:[],collapseTags:!0},meal_type_list:{type:"select",label:"餐段",value:[],multiple:!0,collapseTags:!0,placeholder:"请选择",dataList:h.slice(1)},food_name:{type:"input",value:"",label:"商品名称",placeholder:"请输入"}},ye=[{label:"提审时间",value:"apply_time"},{label:"处理时间",value:"process_time"},{label:"退款时间",value:"refund_time"}],ne=[{label:"提审时间",value:"apply_time"},{label:"处理时间",value:"process_time"},{label:"取消时间",value:"cancel_time"}],ie=[{name:"待审批",name2:"待处理",value:"applying"},{name:"已同意",value:"agree"},{name:"已拒绝",value:"refuse"},{name:"已撤回",value:"cancel"}],oe=[{label:"姓名",key:"name"},{label:"人员编号",key:"person_no"},{label:"手机号",key:"phone"},{label:"分组",key:"payer_group_name"},{label:"部门",key:"payer_department_group_name"}],re={time_type:{value:"apply_time"},select_time:{type:"datetimerange",label:"申请退款时间",clearable:!0,value:Object(t["t"])(-7)},charge_trade_no:{type:"input",value:"",label:"充值订单号",placeholder:"请输入"},name:{type:"input",value:"",label:"姓名",placeholder:"请输入"},person_no:{type:"input",value:"",label:"人员编号",placeholder:"请输入"},phone:{type:"input",value:"",label:"手机号",placeholder:"请输入"},charge_out_trade_no:{type:"input",value:"",label:"第三方订单号",placeholder:"请输入"},org_ids:{type:"organizationSelect",value:[],label:"动账组织",checkStrictly:!0,isLazy:!1,multiple:!0},card_info_department_group_ids:{type:"organizationDepartmentSelect",value:[],label:"部门",clearable:!0},card_info_group_ids:{type:"groupSelect",label:"分组",value:[],placeholder:"请选择分组",multiple:!0,clearable:!0,collapseTags:!0},payway:{type:"select",label:"充值类型",value:[],placeholder:"请选择",multiple:!0,collapseTags:!0,dataList:[]}},pe={time_type:{type:"select",value:"apply_time",maxWidth:"130px",dataList:[{label:"申请时间",value:"apply_time"},{label:"处理时间",value:"process_time"},{label:"退款时间",value:"refund_time"}]},select_time:{type:"datetimerange",label:"",clearable:!0,value:Object(t["t"])(-7)},refund_no:{type:"input",value:"",label:"充值退款订单号",placeholder:"请输入"},charge_trade_no:{type:"input",value:"",label:"充值订单号",placeholder:"请输入"},name:{type:"input",value:"",label:"姓名",placeholder:"请输入"},person_no:{type:"input",value:"",label:"人员编号",placeholder:"请输入"},phone:{type:"input",value:"",label:"手机号",placeholder:"请输入"},charge_out_trade_no:{type:"input",value:"",label:"第三方订单号",placeholder:"请输入"},org_ids:{type:"organizationSelect",value:[],label:"动账组织",checkStrictly:!0,isLazy:!1,multiple:!0},card_info_department_group_ids:{type:"organizationDepartmentSelect",value:[],label:"部门",clearable:!0},card_info_group_ids:{type:"groupSelect",label:"分组",value:[],placeholder:"请选择分组",multiple:!0,clearable:!0,collapseTags:!0},refund_type:{type:"select",label:"退款方式",value:"",placeholder:"请选择",dataList:F},payway:{type:"select",label:"退款渠道",value:"",placeholder:"请选择",multiple:!0,dataList:[],collapseTags:!0}},_e=[{label:"序号",key:"index",type:"index",width:"80"},{label:"充值订单号",key:"charge_trade_no",width:"160"},{label:"第三方订单号",key:"charge_out_trade_no",width:"160"},{label:"申请时间",key:"apply_time",width:"160"},{label:"充值时间",key:"charge_create_time",width:"160"},{label:"剩余处理时间",key:"residual_time",width:"160",type:"slot",slotName:"residual"},{label:"充值金额",key:"origin_fee",type:"money",unit:"¥"},{label:"手续费",key:"rate_fee",type:"money",unit:"¥"},{label:"储值钱包到账",key:"wallet_fee",type:"money",unit:"¥"},{label:"赠送钱包到账",key:"complimentary_fee",type:"money",unit:"¥"},{label:"动账组织",key:"wallet_org"}].concat(oe,[{label:"交易流水号",key:"provider_trade_no"},{label:"充值方式",key:"charge_sub_payway_alias"},{label:"充值类型",key:"charge_payway_alias"},{label:"申请原因",key:"apply_reason",showTooltip:!0,width:"160"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"100"}]),ue=[{label:"序号",key:"index",type:"index",width:"80"},{label:"原充值订单号",key:"charge_trade_no",width:"160"},{label:"充值退款订单号",key:"refund_no",width:"160"},{label:"申请时间",key:"apply_time",width:"160"},{label:"充值时间",key:"charge_create_time",width:"160"},{label:"处理时间",key:"process_time",width:"160"},{label:"退款时间",key:"refund_finish_time",width:"160"},{label:"退款金额",key:"refund_fee",width:"160",type:"money",unit:"¥"},{label:"手续费",key:"rate_fee",type:"money",unit:"¥"},{label:"动账组织",key:"wallet_org"}].concat(oe,[{label:"第三方订单号",key:"charge_out_trade_no"},{label:"交易流水号",key:"provider_trade_no"},{label:"退款类型",key:"pay_scene_alias"},{label:"退款方式",key:"charge_sub_payway_alias"},{label:"退款渠道",key:"charge_payway_alias"},{label:"申请原因",key:"apply_reason",showTooltip:!0,width:"160"},{label:"审批备注",key:"process_reason",showTooltip:!0,width:"160"},{label:"操作员",key:"operator_name"}]),be=[{label:"序号",key:"index",type:"index",width:"80"},{label:"充值订单号",key:"charge_trade_no",width:"160"},{label:"申请时间",key:"apply_time",width:"160"},{label:"充值时间",key:"charge_create_time",width:"160"},{label:"充值金额",key:"origin_fee",width:"160",type:"money",unit:"¥"},{label:"手续费",key:"rate_fee",type:"money",unit:"¥"},{label:"储值钱包到账",key:"wallet_fee",type:"money",unit:"¥"},{label:"赠送钱包到账",key:"complimentary_fee",type:"money",unit:"¥"},{label:"动账组织",key:"wallet_org"}].concat(oe,[{label:"第三方订单号",key:"charge_out_trade_no"},{label:"交易流水号",key:"provider_trade_no"},{label:"支付方式",key:"charge_sub_payway_alias"},{label:"支付类型",key:"charge_payway_alias"},{label:"申请原因",key:"apply_reason",showTooltip:!0,width:"160"},{label:"审批备注",key:"process_reason",showTooltip:!0,width:"160"},{label:"操作员",key:"operator_name"}]),de=[{label:"序号",key:"index",type:"index",width:"80"},{label:"充值订单号",key:"charge_trade_no",width:"160"},{label:"第三方订单号",key:"charge_out_trade_no",width:"160"},{label:"申请时间",key:"apply_time",width:"160"},{label:"撤销时间",key:"process_time",width:"160"},{label:"充值时间",key:"charge_create_time",width:"160"},{label:"充值金额",key:"origin_fee",width:"160",type:"money",unit:"¥"},{label:"手续费",key:"rate_fee",type:"money",unit:"¥"},{label:"储值钱包到账",key:"wallet_fee",type:"money",unit:"¥"},{label:"赠送钱包到账",key:"complimentary_fee",type:"money",unit:"¥"},{label:"动账组织",key:"wallet_org"}].concat(oe,[{label:"交易流水号",key:"provider_trade_no"},{label:"支付方式",key:"charge_sub_payway_alias"},{label:"支付类型",key:"charge_payway_alias"},{label:"申请原因",key:"apply_reason",showTooltip:!0,width:"160"}]),ce={time_type:{type:"select",value:"apply_time",maxWidth:"130px",dataList:[{label:"提审时间",value:"apply_time"}]},select_time:{type:"datetimerange",label:"",clearable:!1,format:"yyyy-MM-dd HH:mm:ss",value:Object(t["t"])(-7)},trade_no:{type:"input",value:"",label:"审批单号",placeholder:"请输入"},name:{type:"input",value:"",label:"姓名",placeholder:"请输入"},phone:{type:"input",value:"",label:"手机号",placeholder:"请输入"},person_no:{type:"input",value:"",label:"人员编号",placeholder:"请输入"},card_info_department_group_ids:{type:"organizationDepartmentSelect",value:[],label:"部门",clearable:!0},card_info_group_ids:{type:"groupSelect",label:"分组",value:[],placeholder:"请选择分组",multiple:!0,clearable:!0,collapseTags:!0}},se={type:"organizationSelect",value:[],label:"提现组织",checkStrictly:!0,isLazy:!1,multiple:!0},me={type:"input",value:"",label:"充值退款订单号",placeholder:"请输入"},ke=[{label:"序号",key:"index",type:"index",width:"80"},{label:"审批单号",key:"trade_no",width:"160"},{label:"提审时间",key:"apply_time",width:"160"},{label:"剩余处理时间",key:"process_time",width:"160",type:"slot",slotName:"residual"},{label:"申请提现金额",key:"apply_withdraw_fee",width:"160",type:"money",unit:"¥"},{label:"提现组织",key:"wallet_org"},{label:"提现渠道",key:"payway_alias"}].concat(oe,[{label:"申请原因",key:"apply_reason",showTooltip:!0,width:"160"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"100"}]),he=[{label:"序号",key:"index",type:"index",width:"80"},{label:"审批单号",key:"trade_no",width:"160"},{label:"提审时间",key:"apply_time",width:"160"},{label:"处理时间",key:"process_time",width:"160"},{label:"关联订单号",key:"order_refund_no",width:"160",type:"slot",slotName:"reativeTradeNo"},{label:"申请提现金额",key:"apply_withdraw_fee",type:"money",unit:"¥"},{label:"实际提现金额",key:"real_withdraw_fee",type:"money",unit:"¥"},{label:"提现组织",key:"wallet_org"},{label:"提现渠道",key:"payway_alias"}].concat(oe,[{label:"申请原因",key:"apply_reason",showTooltip:!0,width:"160"},{label:"审批备注",key:"process_reason",showTooltip:!0,width:"160"},{label:"操作员",key:"operator_name"}]),ve=[{label:"序号",key:"index",type:"index",width:"80"},{label:"审批单号",key:"trade_no",width:"160"},{label:"提审时间",key:"apply_time",width:"160"},{label:"处理时间",key:"process_time"},{label:"申请提现金额",key:"apply_withdraw_fee",type:"money",unit:"¥"},{label:"提现组织",key:"wallet_org"},{label:"提现渠道",key:"payway_alias"}].concat(oe,[{label:"申请原因",key:"apply_reason",showTooltip:!0,width:"160"},{label:"审批备注",key:"process_reason",showTooltip:!0,width:"160"},{label:"操作员",key:"operator_name"}]),fe=[{label:"序号",key:"index",type:"index",width:"80"},{label:"审批单号",key:"trade_no",width:"160"},{label:"提审时间",key:"apply_time",width:"160"},{label:"取消时间",key:"process_time",width:"160"},{label:"申请提现金额",key:"apply_withdraw_fee",type:"money",unit:"¥"},{label:"提现组织",key:"wallet_org"},{label:"提现渠道",key:"payway_alias"}].concat(oe,[{label:"申请原因",key:"apply_reason",showTooltip:!0,width:"160"}])}}]);