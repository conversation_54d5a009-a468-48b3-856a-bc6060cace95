(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-consumption-rules-service-admin-AddChargeServiceRule","view-merchant-meal-management-components-mealFoodList-FoodDiscountDialog"],{d0dd:function(r,n,e){"use strict";e.d(n,"a",(function(){return t})),e.d(n,"b",(function(){return o})),e.d(n,"g",(function(){return i})),e.d(n,"c",(function(){return u})),e.d(n,"f",(function(){return d})),e.d(n,"d",(function(){return c})),e.d(n,"e",(function(){return f}));var t=function(r,n,e){if(n){var t=/^-?(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;t.test(n)?e():e(new Error("金额格式有误"))}else e(new Error("请输入金额"))},o=function(r,n,e){if(n){var t=/^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;t.test(n)?e():e(new Error("金额格式有误"))}else e()},i=function(r,n,e){if(!n)return e(new Error("手机号不能为空"));var t=/^1[3456789]\d{9}$/;t.test(n)?e():e(new Error("请输入正确手机号"))},u=function(r,n,e){if(!n)return e(new Error("金额有误"));var t=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;t.test(n)?e():e(new Error("金额格式有误"))},d=function(r,n,e){if(""===n)return e(new Error("不能为空"));var t=/^\d+$/;t.test(n)?e():e(new Error("请输入正确数字"))},c=function(r,n,e){if(""!==n){var t=/^(\+|-)?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;t.test(n)?e():e(new Error("金额格式有误"))}else e(new Error("请输入金额"))},f=function(r,n,e){var t=/^[\u4E00-\u9FA5\w-]+$/;t.test(n)?e():e(new Error("格式不正确，不能包含特殊字符"))}}}]);