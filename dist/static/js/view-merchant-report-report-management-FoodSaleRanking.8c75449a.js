(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-report-report-management-FoodSaleRanking","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-meal-report-MealPackageRule","view-merchant-order-RechargeOrder","view-merchant-order-RefundOrder"],{"87ac":function(e,t,n){"use strict";var r=n("ed08"),i=n("2f62");function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function o(e,t){return s(e)||f(e,t)||c(e,t)||u()}function u(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function c(e,t){if(e){if("string"==typeof e)return l(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?l(e,t):void 0}}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function f(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,a,o,u=[],c=!0,l=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=a.call(n)).done)&&(u.push(r.value),u.length!==t);c=!0);}catch(e){l=!0,i=e}finally{try{if(!c&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function s(e){if(Array.isArray(e))return e}function h(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */h=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(e,t,n){e[t]=n.value},o="function"==typeof Symbol?Symbol:{},u=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function f(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{f({},"")}catch(e){f=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var a=t&&t.prototype instanceof b?t:b,o=Object.create(a.prototype),u=new I(r||[]);return i(o,"_invoke",{value:E(e,n,u)}),o}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=s;var m="suspendedStart",y="suspendedYield",p="executing",g="completed",v={};function b(){}function w(){}function k(){}var x={};f(x,u,(function(){return this}));var _=Object.getPrototypeOf,O=_&&_(_(V([])));O&&O!==n&&r.call(O,u)&&(x=O);var j=k.prototype=b.prototype=Object.create(x);function S(e){["next","throw","return"].forEach((function(t){f(e,t,(function(e){return this._invoke(t,e)}))}))}function P(e,t){function n(i,o,u,c){var l=d(e[i],e,o);if("throw"!==l.type){var f=l.arg,s=f.value;return s&&"object"==a(s)&&r.call(s,"__await")?t.resolve(s.__await).then((function(e){n("next",e,u,c)}),(function(e){n("throw",e,u,c)})):t.resolve(s).then((function(e){f.value=e,u(f)}),(function(e){return n("throw",e,u,c)}))}c(l.arg)}var o;i(this,"_invoke",{value:function(e,r){function i(){return new t((function(t,i){n(e,r,t,i)}))}return o=o?o.then(i,i):i()}})}function E(t,n,r){var i=m;return function(a,o){if(i===p)throw Error("Generator is already running");if(i===g){if("throw"===a)throw o;return{value:e,done:!0}}for(r.method=a,r.arg=o;;){var u=r.delegate;if(u){var c=L(u,r);if(c){if(c===v)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===m)throw i=g,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=p;var l=d(t,n,r);if("normal"===l.type){if(i=r.done?g:y,l.arg===v)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(i=g,r.method="throw",r.arg=l.arg)}}}function L(t,n){var r=n.method,i=t.iterator[r];if(i===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,L(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var a=d(i,t.iterator,n.arg);if("throw"===a.type)return n.method="throw",n.arg=a.arg,n.delegate=null,v;var o=a.arg;return o?o.done?(n[t.resultName]=o.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,v):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function A(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function D(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function I(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(A,this),this.reset(!0)}function V(t){if(t||""===t){var n=t[u];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,o=function n(){for(;++i<t.length;)if(r.call(t,i))return n.value=t[i],n.done=!1,n;return n.value=e,n.done=!0,n};return o.next=o}}throw new TypeError(a(t)+" is not iterable")}return w.prototype=k,i(j,"constructor",{value:k,configurable:!0}),i(k,"constructor",{value:w,configurable:!0}),w.displayName=f(k,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,k):(e.__proto__=k,f(e,l,"GeneratorFunction")),e.prototype=Object.create(j),e},t.awrap=function(e){return{__await:e}},S(P.prototype),f(P.prototype,c,(function(){return this})),t.AsyncIterator=P,t.async=function(e,n,r,i,a){void 0===a&&(a=Promise);var o=new P(s(e,n,r,i),a);return t.isGeneratorFunction(n)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},S(j),f(j,l,"Generator"),f(j,u,(function(){return this})),f(j,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=V,I.prototype={constructor:I,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(D),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function i(r,i){return u.type="throw",u.arg=t,n.next=r,i&&(n.method="next",n.arg=e),!!i}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],u=o.completion;if("root"===o.tryLoc)return i("end");if(o.tryLoc<=this.prev){var c=r.call(o,"catchLoc"),l=r.call(o,"finallyLoc");if(c&&l){if(this.prev<o.catchLoc)return i(o.catchLoc,!0);if(this.prev<o.finallyLoc)return i(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return i(o.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return i(o.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=e,o.arg=t,a?(this.method="next",this.next=a.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),D(n),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;D(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:V(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),v}},t}function d(e,t,n,r,i,a,o){try{var u=e[a](o),c=u.value}catch(e){return void n(e)}u.done?t(c):Promise.resolve(c).then(r,i)}function m(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var a=e.apply(t,n);function o(e){d(a,r,i,o,u,"next",e)}function u(e){d(a,r,i,o,u,"throw",e)}o(void 0)}))}}function y(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?y(Object(n),!0).forEach((function(t){g(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):y(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function g(e,t,n){return(t=v(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function v(e){var t=b(e,"string");return"symbol"==a(t)?t:t+""}function b(e,t){if("object"!=a(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=a(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}t["a"]={data:function(){return{currentTableSetting:[],dialogPrintVisible:!1}},computed:p({},Object(i["c"])(["userInfo"])),mounted:function(){},methods:{initPrintSetting:function(){var e=this;return m(h().mark((function t(){var n,i;return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return n=[],t.next=3,e.getPrintSettingInfo();case 3:n=t.sent;try{n=n?Object(r["E"])(n):Object(r["E"])(e.tableSetting)}catch(a){n=Object(r["E"])(e.tableSetting)}n.length<12?(i=Object(r["m"])(e.tableSetting,n),i=e.deleteWidthKey(i),e.currentTableSetting=i):e.currentTableSetting=Object(r["m"])(e.tableSetting,n);case 6:case"end":return t.stop()}}),t)})))()},getPrintSettingInfo:function(){var e=this;return m(h().mark((function t(){var n,i,a,u,c;return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return n=null,t.next=3,Object(r["Z"])(e.$apis.apiBackgroundAdminAccountGetAccountPrintInfoPost({id:e.userInfo.account_id,print_key:e.printType}));case 3:if(i=t.sent,a=o(i,2),u=a[0],c=a[1],!u){t.next=10;break}return e.$message.error(u.message),t.abrupt("return",n);case 10:return 0===c.code?n=c.data:e.$message.error(c.msg),t.abrupt("return",n);case 12:case"end":return t.stop()}}),t)})))()},setPrintSettingInfo:function(e,t){var n=this;return m(h().mark((function i(){var a,u,c,l;return h().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,Object(r["Z"])(n.$apis.apiBackgroundAdminAccountSetAccountPrintInfoPost(p({id:n.userInfo.account_id,print_key:n.printType,print_list:e},t)));case 2:if(a=i.sent,u=o(a,2),c=u[0],l=u[1],!c){i.next=9;break}return n.$message.error(c.message),i.abrupt("return");case 9:0===l.code?n.$message.success("设置成功"):n.$message.error(l.msg);case 10:case"end":return i.stop()}}),i)})))()},openPrintSetting:function(){this.dialogPrintVisible=!0},confirmPrintDialog:function(e,t){var n=this;return m(h().mark((function i(){var a;return h().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(!e){i.next=6;break}return a=Object(r["f"])(e),a.length<12&&(a=n.deleteWidthKey(a)),i.next=5,n.setPrintSettingInfo(a,t);case 5:n.currentTableSetting=a;case 6:case"end":return i.stop()}}),i)})))()},deleteWidthKey:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"children";function n(e){e.map((function(e){e.width&&delete e.width,e.minWidth&&delete e.minWidth,e[t]&&e[t].length>0&&n(e[t])}))}return n(e),e},indexMethod:function(e){return(this.page-1)*this.pageSize+(e+1)},setCollectData:function(e){var t=this;this.collect.forEach((function(n){e.data.collect&&void 0!==e.data.collect[n.key]&&t.$set(n,"value",e.data.collect[n.key])}))},setSummaryData:function(e){var t=e.data.collect;t[this.tableSetting[0].key]="合计",this.tableData.length&&this.tableData.push(t)}},beforeDestroy:function(){}}},c9d9:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"d",(function(){return o})),n.d(t,"b",(function(){return u})),n.d(t,"c",(function(){return c})),n.d(t,"e",(function(){return l})),n.d(t,"f",(function(){return f})),n.d(t,"g",(function(){return s}));var r=n("5a0c"),i=n("da92"),a=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],o=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],u={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},c=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],l=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],f=(r().subtract(7,"day").format("YYYY-MM-DD"),r().format("YYYY-MM-DD"),function(e){return e?"number"===typeof e?i["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:i["a"].divide(e,100).toFixed(2):"0.00"}),s=function(e){return i["a"].times(e,100)}}}]);