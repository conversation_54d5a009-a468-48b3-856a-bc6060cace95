(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-user-center-components-AutoRegisterDialog"],{"090d":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("dialog-message",{staticClass:"FormDialog",attrs:{show:t.visible,title:t.title,loading:t.isLoading,width:t.width,footerCenter:!0},on:{"update:show":function(e){t.visible=e},"update:loading":function(e){t.isLoading=e},close:t.handlerClose,cancel:t.clickCancleHandle,confirm:t.clickConfirmHandle}},[e("el-form",{ref:"formRef",attrs:{model:t.formData,rules:t.formRules,"label-width":"80px",size:t.formSize}},["approve"===t.type||"mul_approve"===t.type?e("div",[e("el-form-item",{attrs:{label:"所属组织",prop:"orgIds"}},[e("organization-select",{staticClass:"w-260 ps-input",attrs:{placeholder:"请选择",clearable:!0,multiple:!1,checkStrictly:!0,isLazy:!1,"collapse-tags":!0,"append-to-body":!0,filterable:""},on:{change:t.changeOrgHandle},model:{value:t.formData.orgIds,callback:function(e){t.$set(t.formData,"orgIds",e)},expression:"formData.orgIds"}})],1),e("el-form-item",{attrs:{label:"所属分组",prop:"groupId"}},[e("el-select",{staticClass:"ps-select w-260",attrs:{"popper-class":"ps-popper-select",filterable:"",disabled:!t.formData.orgIds},model:{value:t.formData.groupId,callback:function(e){t.$set(t.formData,"groupId",e)},expression:"formData.groupId"}},t._l(t.groupList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.group_name,value:t.id}})})),1)],1),e("el-form-item",{attrs:{label:"所属部门",prop:"departmentId"}},[e("select-tree",{staticClass:"w-260",attrs:{treeData:t.departmentList,treeProps:t.treeProps,multiple:!1,isLazy:!1,clearable:!0,checkStrictly:!0,"append-to-body":!0,disabled:!t.formData.orgIds,filterable:""},model:{value:t.formData.departmentId,callback:function(e){t.$set(t.formData,"departmentId",e)},expression:"formData.departmentId"}})],1)],1):t._e(),"reject"===t.type||"mul_reject"===t.type?e("el-form-item",{attrs:{label:"拒绝说明",prop:"reason"}},[e("el-input",{attrs:{type:"textarea",rows:4,maxlength:100,"show-word-limit":"",placeholder:""},model:{value:t.formData.reason,callback:function(e){t.$set(t.formData,"reason",e)},expression:"formData.reason"}})],1):t._e()],1)],1)},o=[],a=(r("ed08"),r("cbfb")),i=r("fb36");function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function u(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach((function(e){l(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function l(t,e,r){return(e=f(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function f(t){var e=p(t,"string");return"symbol"==c(e)?e:e+""}function p(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function d(t,e){return y(t)||v(t,e)||m(t,e)||h()}function h(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function m(t,e){if(t){if("string"==typeof t)return g(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?g(t,e):void 0}}function g(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function v(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,c=[],s=!0,u=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(c.push(n.value),c.length!==e);s=!0);}catch(t){u=!0,o=t}finally{try{if(!s&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw o}}return c}}function y(t){if(Array.isArray(t))return t}function b(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */b=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var a=e&&e.prototype instanceof y?e:y,i=Object.create(a.prototype),c=new A(n||[]);return o(i,"_invoke",{value:I(t,r,c)}),i}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var d="suspendedStart",h="suspendedYield",m="executing",g="completed",v={};function y(){}function w(){}function L(){}var x={};l(x,i,(function(){return this}));var _=Object.getPrototypeOf,k=_&&_(_($([])));k&&k!==r&&n.call(k,i)&&(x=k);var D=L.prototype=y.prototype=Object.create(x);function j(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function O(t,e){function r(o,a,i,s){var u=p(t[o],t,a);if("throw"!==u.type){var l=u.arg,f=l.value;return f&&"object"==c(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,i,s)}),(function(t){r("throw",t,i,s)})):e.resolve(f).then((function(t){l.value=t,i(l)}),(function(t){return r("throw",t,i,s)}))}s(u.arg)}var a;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return a=a?a.then(o,o):o()}})}function I(e,r,n){var o=d;return function(a,i){if(o===m)throw Error("Generator is already running");if(o===g){if("throw"===a)throw i;return{value:t,done:!0}}for(n.method=a,n.arg=i;;){var c=n.delegate;if(c){var s=S(c,n);if(s){if(s===v)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===d)throw o=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=m;var u=p(e,r,n);if("normal"===u.type){if(o=n.done?g:h,u.arg===v)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(o=g,n.method="throw",n.arg=u.arg)}}}function S(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,S(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var a=p(o,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,v;var i=a.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function P(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function A(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(P,this),this.reset(!0)}function $(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(c(e)+" is not iterable")}return w.prototype=L,o(D,"constructor",{value:L,configurable:!0}),o(L,"constructor",{value:w,configurable:!0}),w.displayName=l(L,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,L):(t.__proto__=L,l(t,u,"GeneratorFunction")),t.prototype=Object.create(D),t},e.awrap=function(t){return{__await:t}},j(O.prototype),l(O.prototype,s,(function(){return this})),e.AsyncIterator=O,e.async=function(t,r,n,o,a){void 0===a&&(a=Promise);var i=new O(f(t,r,n,o),a);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},j(D),l(D,u,"Generator"),l(D,i,(function(){return this})),l(D,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=$,A.prototype={constructor:A,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(E),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return c.type="throw",c.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],c=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var s=n.call(i,"catchLoc"),u=n.call(i,"finallyLoc");if(s&&u){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=t,i.arg=e,a?(this.method="next",this.next=a.finallyLoc,v):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),E(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;E(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:$(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function w(t,e,r,n,o,a,i){try{var c=t[a](i),s=c.value}catch(t){return void r(t)}c.done?e(s):Promise.resolve(s).then(n,o)}function L(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var a=t.apply(e,r);function i(t){w(a,n,o,i,c,"next",t)}function c(t){w(a,n,o,i,c,"throw",t)}i(void 0)}))}}var x={name:"AutoRegisterDialog",components:{OrganizationSelect:a["a"],SelectTree:i["a"]},props:{showdialog:Boolean,loading:Boolean,type:{type:String,default:"approve"},title:{type:String,default:""},width:{type:String,default:"460px"},formSize:{type:String,default:"medium"},InfoData:{type:Object,default:function(){return{}}},params:{type:Object,default:function(){return{}}}},data:function(){return{isLoading:!1,selectList:[],formData:{orgIds:"",groupId:"",departmentId:"",reason:""},formRules:{orgIds:[{required:!0,message:"请选择组织",trigger:"change"}],reason:[{required:!0,message:"请输入拒绝原因",trigger:"change"}]},groupList:[],departmentList:[],treeProps:{value:"id",label:"group_name",isLeaf:"is_leaf",children:"children_list"},remoteLoading:!1}},computed:{visible:{get:function(){return this.showdialog},set:function(t){this.$emit("update:showdialog",t)}},groupParams:function(){return{organization:this.formData.cardUserGroupIds}}},watch:{showdialog:function(t){t&&this.init()}},created:function(){},mounted:function(){},methods:{init:function(){var t=this;return L(b().mark((function e(){return b().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.formData={orgIds:"",groupId:"",departmentId:"",reason:""};case 1:case"end":return e.stop()}}),e)})))()},clickConfirmHandle:function(){var t=this;this.$refs.formRef.validate((function(e){if(e){var r={};switch(t.type){case"approve":r={source_organization_id:t.formData.orgIds,payer_group_id:t.formData.groupId||void 0,payer_department_group_id:t.formData.departmentId||void 0};break;case"mul_approve":r={source_organization_id:t.formData.orgIds,payer_group_id:t.formData.groupId||void 0,payer_department_group_id:t.formData.departmentId||void 0};break;case"reject":r.reject_reason=t.formData.reason;break;case"mul_reject":r.reject_reason=t.formData.reason;break}t.sendFormData(r)}else t.isLoading=!1}))},sendFormData:function(){var t=arguments,e=this;return L(b().mark((function r(){var n,o,a,i,c,s;return b().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return n=t.length>0&&void 0!==t[0]?t[0]:{},"approve"===e.type&&(o="apiBackgroundApproveApproveRegisterAgreeApprovePost"),"mul_approve"===e.type&&(o="apiBackgroundApproveApproveRegisterBulkAgreeApprovePost"),"reject"!==e.type&&"mul_reject"!==e.type||(o="apiBackgroundApproveApproveRegisterBulkRejectApprovePost"),e.isLoading=!0,r.next=7,e.$to(e.$apis[o](u(u({},n),e.params)));case 7:if(a=r.sent,i=d(a,2),c=i[0],s=i[1],e.isLoading=!1,!c){r.next=15;break}return e.$message.error(c.message||"出错了"),r.abrupt("return");case 15:0===s.code?(e.$message.success(s.msg),e.$emit("confirmForm"),e.visible=!1):e.$message.error(s.msg);case 16:case"end":return r.stop()}}),r)})))()},clickCancleHandle:function(){this.visible=!1,this.$emit("cancelForm")},handlerClose:function(t){this.isLoading=!1,this.$refs.formRef&&this.$refs.formRef.resetFields()},getGroupList:function(){var t=this;return L(b().mark((function e(){var r,n,o,a,i;return b().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.remoteLoading=!0,r={},t.formData.orgIds&&(r.organization_id=t.formData.orgIds),e.next=5,t.$to(t.$apis.apiBackgroundApproveApproveRegisterSelectCardUserGroupPost(r));case 5:if(n=e.sent,o=d(n,2),a=o[0],i=o[1],t.remoteLoading=!1,!a){e.next=12;break}return e.abrupt("return");case 12:if(0!==i.code){e.next=18;break}if(i.data){e.next=15;break}return e.abrupt("return");case 15:t.groupList=i.data,e.next=18;break;case 18:case"end":return e.stop()}}),e)})))()},getDepartmentList:function(){var t=this;return L(b().mark((function e(){var r,n,o,a,i;return b().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.remoteLoading=!0,r={},t.formData.orgIds&&(r.organization_id=t.formData.orgIds),e.next=5,t.$to(t.$apis.apiBackgroundApproveApproveRegisterSelectCardDepartmentGroupPost(r));case 5:if(n=e.sent,o=d(n,2),a=o[0],i=o[1],t.remoteLoading=!1,!a){e.next=12;break}return e.abrupt("return");case 12:if(0!==i.code){e.next=18;break}if(i.data){e.next=15;break}return e.abrupt("return");case 15:t.departmentList=i.data,e.next=18;break;case 18:case"end":return e.stop()}}),e)})))()},changeOrgHandle:function(t){this.getGroupList(),this.getDepartmentList()}}},_=x,k=(r("6506"),r("2877")),D=Object(k["a"])(_,n,o,!1,null,null,null);e["default"]=D.exports},6506:function(t,e,r){"use strict";r("e3f0")},e3f0:function(t,e,r){}}]);