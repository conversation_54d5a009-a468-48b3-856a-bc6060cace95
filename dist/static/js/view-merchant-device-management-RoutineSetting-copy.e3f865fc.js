(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-device-management-RoutineSetting-copy"],{"0b86":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"RoutineSetting container-wrapper"},[e("refresh-tool",{on:{refreshPage:t.refreshHandle}}),e("div",{staticClass:"table-wrapper",staticStyle:{"margin-top":"0px","margin-bottom":"50px"}},[e("div",{staticClass:"table-header",staticStyle:{display:"flex","justify-content":"space-between"}},[e("div",{staticStyle:{display:"flex","align-items":"center"}},[e("div",{staticClass:"table-title"},[t._v("消费点：")]),e("consume-select",{staticClass:"ps-input",attrs:{placeholder:"请选择",multiple:!0,"collapse-tags":!0},on:{change:t.changeOrg},model:{value:t.organizationIds,callback:function(e){t.organizationIds=e},expression:"organizationIds"}})],1),e("div",{staticStyle:{"padding-right":"20px"}},[e("el-button",{staticClass:"ps-origin-btn",attrs:{size:"small",type:"primary"},on:{click:t.checkForm}},[t._v("保存")])],1)]),e("div",[e("el-form",{ref:"settingForm",attrs:{model:t.settingForm,rules:t.settingFormRules,"label-width":"170px"}},[e("el-form-item",{attrs:{label:"绑定额度限制："}},[e("el-radio-group",{staticClass:"ps-radio",staticStyle:{"margin-right":"30px"},model:{value:t.settingForm.limit,callback:function(e){t.$set(t.settingForm,"limit",e)},expression:"settingForm.limit"}},[e("el-radio",{attrs:{label:"noLimit"}},[t._v("无限制")]),e("el-radio",{attrs:{label:"quotaLimit"}},[t._v("额度限制")])],1)],1),"quotaLimit"===t.settingForm.limit?e("el-form-item",{staticClass:"form-quota",attrs:{prop:"quota"}},[t._v(" 金额大于等于"),e("el-input",{staticClass:"margin-input w-180 ps-input",model:{value:t.settingForm.quota,callback:function(e){t.$set(t.settingForm,"quota",e)},expression:"settingForm.quota"}}),t._v("元，才能绑定托盘 ")],1):t._e(),e("div",[e("el-form-item",{staticClass:"form-weight",attrs:{label:"菜品余量不足提示：",prop:"notEnough"}},[t._v(" 设备菜品余量低于"),e("el-input",{staticClass:"margin-input w-180 ps-input",model:{value:t.settingForm.notEnough,callback:function(e){t.$set(t.settingForm,"notEnough",e)},expression:"settingForm.notEnough"}}),t._v("克，提示菜品余量不足 ")],1)],1),e("div",[e("el-form-item",{staticClass:"form-weight",attrs:{label:"菜品售罄提示：",prop:"sellOut"}},[t._v(" 设备菜品余量低于"),e("el-input",{staticClass:"margin-input w-180 ps-input",model:{value:t.settingForm.sellOut,callback:function(e){t.$set(t.settingForm,"sellOut",e)},expression:"settingForm.sellOut"}}),t._v("克，提示菜品售罄 ")],1)],1)],1)],1)])],1)},i=[],o=r("ed08"),a=r("7c9c");function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function u(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */u=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function h(t,e,r,n){var o=e&&e.prototype instanceof w?e:w,a=Object.create(o.prototype),s=new z(n||[]);return i(a,"_invoke",{value:S(t,r,s)}),a}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var g="suspendedStart",m="suspendedYield",d="executing",v="completed",y={};function w(){}function b(){}function x(){}var L={};f(L,a,(function(){return this}));var _=Object.getPrototypeOf,F=_&&_(_(C([])));F&&F!==r&&n.call(F,a)&&(L=F);var E=x.prototype=w.prototype=Object.create(L);function O(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function r(i,o,a,u){var c=p(t[i],t,o);if("throw"!==c.type){var l=c.arg,f=l.value;return f&&"object"==s(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,a,u)}),(function(t){r("throw",t,a,u)})):e.resolve(f).then((function(t){l.value=t,a(l)}),(function(t){return r("throw",t,a,u)}))}u(c.arg)}var o;i(this,"_invoke",{value:function(t,n){function i(){return new e((function(e,i){r(t,n,e,i)}))}return o=o?o.then(i,i):i()}})}function S(e,r,n){var i=g;return function(o,a){if(i===d)throw Error("Generator is already running");if(i===v){if("throw"===o)throw a;return{value:t,done:!0}}for(n.method=o,n.arg=a;;){var s=n.delegate;if(s){var u=j(s,n);if(u){if(u===y)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===g)throw i=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=d;var c=p(e,r,n);if("normal"===c.type){if(i=n.done?v:m,c.arg===y)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(i=v,n.method="throw",n.arg=c.arg)}}}function j(e,r){var n=r.method,i=e.iterator[n];if(i===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,j(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var o=p(i,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,y;var a=o.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function $(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function q(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function z(t){this.tryEntries=[{tryLoc:"root"}],t.forEach($,this),this.reset(!0)}function C(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function r(){for(;++i<e.length;)if(n.call(e,i))return r.value=e[i],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(s(e)+" is not iterable")}return b.prototype=x,i(E,"constructor",{value:x,configurable:!0}),i(x,"constructor",{value:b,configurable:!0}),b.displayName=f(x,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,x):(t.__proto__=x,f(t,l,"GeneratorFunction")),t.prototype=Object.create(E),t},e.awrap=function(t){return{__await:t}},O(k.prototype),f(k.prototype,c,(function(){return this})),e.AsyncIterator=k,e.async=function(t,r,n,i,o){void 0===o&&(o=Promise);var a=new k(h(t,r,n,i),o);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},O(E),f(E,l,"Generator"),f(E,a,(function(){return this})),f(E,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=C,z.prototype={constructor:z,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(q),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function i(n,i){return s.type="throw",s.arg=e,r.next=n,i&&(r.method="next",r.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var u=n.call(a,"catchLoc"),c=n.call(a,"finallyLoc");if(u&&c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,y):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),q(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var i=n.arg;q(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:C(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),y}},e}function c(t,e,r,n,i,o,a){try{var s=t[o](a),u=s.value}catch(t){return void r(t)}s.done?e(u):Promise.resolve(u).then(n,i)}function l(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var o=t.apply(e,r);function a(t){c(o,n,i,a,s,"next",t)}function s(t){c(o,n,i,a,s,"throw",t)}a(void 0)}))}}var f={name:"RoutineSetting",components:{ConsumeSelect:a["a"]},props:{},data:function(){var t=function(t,e,r){if(!e)return r(new Error("金额不能为空"));var n=/^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;n.test(e)?r():r(new Error("金额格式有误"))},e=function(t,e,r){if(e){var n=/^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/;n.test(e)||r(new Error("菜品余量格式有误"))}else r()};return{isLoading:!1,organizationList:[],organizationIds:[],settingForm:{limit:"noLimit",quota:"",notEnough:"",sellOut:""},settingFormRules:{quota:[{required:!0,validator:t,trigger:"blur"}],notEnough:[{validator:e,trigger:"blur"}],sellOut:[{validator:e,trigger:"blur"}]}}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getOrganizationTreeList()},refreshHandle:function(){},checkForm:function(){var t=this;this.$refs.settingForm.validate((function(e){if(!e)return!1;t.saveRoutineSetting()}))},saveRoutineSetting:function(){var t=this;return l(u().mark((function e(){var r,n;return u().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r={stall_nos:t.organizationIds,limit_fee:"quotaLimit"===t.settingForm.limit?Object(o["Y"])(t.settingForm.quota):null},t.settingForm.notEnough&&(r.warn_weight=t.settingForm.notEnough,r.out_weight=t.settingForm.sellOut),e.next=4,t.$apis.apiBackgroundDeviceBuffetAddPost(r);case 4:n=e.sent,0===n.code?t.$message.success("保存成功"):t.$message.error(n.msg);case 6:case"end":return e.stop()}}),e)})))()},getSettingDetail:function(){var t=this;return l(u().mark((function e(){var r;return u().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.apiBackgroundDeviceBuffetDetailsPost({stall_no:t.organizationIds[0]});case 2:r=e.sent,0===r.code?r.data&&(t.settingForm.limit=null===r.data.limit_fee?"noLimit":"quotaLimit",t.settingForm.quota=null===r.data.limit_fee?"":Object(o["i"])(r.data.limit_fee),t.settingForm.notEnough=r.data.warn_weight,t.settingForm.sellOut=r.data.out_weight):t.$message.error(r.msg);case 4:case"end":return e.stop()}}),e)})))()},resetForm:function(){this.settingForm.limit="noLimit",this.settingForm.quota="",this.settingForm.notEnough="",this.settingForm.sellOut=""},changeOrg:function(){1===this.organizationIds.length?this.getSettingDetail():this.resetForm()},getOrganizationTreeList:function(){var t=this;return l(u().mark((function e(){var r;return u().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.apiBackgroundOrganizationOrganizationTreeListPost();case 2:r=e.sent,0===r.code?t.organizationList=t.deleteEmptyGroup(r.data):t.$message.error(r.msg);case 4:case"end":return e.stop()}}),e)})))()},deleteEmptyGroup:function(t){var e=this;function r(t){t.map((function(t){t.children_list&&t.children_list.length>0?r(t.children_list):e.$delete(t,"children_list")}))}return r(t),t},normalizer:function(t){return{id:t.id,label:t.name,children:t.children_list}}}},h=f,p=(r("29ed"),r("2877")),g=Object(p["a"])(h,n,i,!1,null,null,null);e["default"]=g.exports},"29ed":function(t,e,r){"use strict";r("8d18")},"8d18":function(t,e,r){}}]);