(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-accessControl-attendance-attendance-AddOrEditAttendanceSetting","view-merchant-accessControl-attendance-constantsConfig"],{"1b8ff":function(t,e,n){},9210:function(t,e,n){"use strict";n.r(e),n.d(e,"weekList",(function(){return u})),n.d(e,"recentSevenDay",(function(){return m})),n.d(e,"punchStatuaList",(function(){return p})),n.d(e,"getRequestParams",(function(){return f}));var i=n("5a0c");function r(t){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}function a(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}function o(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?a(Object(n),!0).forEach((function(e){s(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):a(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function s(t,e,n){return(e=c(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function c(t){var e=l(t,"string");return"symbol"==r(e)?e:e+""}function l(t,e){if("object"!=r(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var i=n.call(t,e||"default");if("object"!=r(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var u=[{key:"all",name:"全部"},{key:"1",name:"周一"},{key:"2",name:"周二"},{key:"3",name:"周三"},{key:"4",name:"周四"},{key:"5",name:"周五"},{key:"6",name:"周六"},{key:"7",name:"周日"}],m=[i().subtract(7,"day").format("YYYY-MM-DD"),i().format("YYYY-MM-DD")],p=[{key:"sign_in",name:"签到"},{key:"sign_out",name:"签退"},{key:"be_late",name:"迟到"},{key:"leave_early",name:"早退"},{key:"for_leave",name:"请假"},{key:"absence_work",name:"缺卡"}],f=function(t,e,n){var i,r={};Object.keys(t).forEach((function(e){("select_time"!==e&&""!==t[e].value&&t[e].value||"boolean"===typeof t[e].value)&&(r[e]=t[e].value)}));var a=o({page:e,page_size:n},r);return 2===(null===(i=t.select_time)||void 0===i||null===(i=i.value)||void 0===i?void 0:i.length)&&(a.start_date=t.select_time.value[0]+" 00:00:00",a.end_date=t.select_time.value[1]+" 23:59:59"),a}},a5fe:function(t,e,n){"use strict";n("1b8ff")},f865:function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"AddOrEditAttendanceSetting container-wrapper"},[e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v(t._s("add"===t.type?"新建":"编辑")+"考勤设置")])]),e("div",[e("el-form",{ref:"settingFormRef",staticClass:"attendance-form",attrs:{model:t.settingForm,"status-icon":"",rules:t.settingFormRules,"label-width":"100px"},nativeOn:{submit:function(t){t.preventDefault()}}},[e("el-form-item",{attrs:{label:"名称",prop:"name"}},[e("el-input",{staticClass:"ps-input w-250",model:{value:t.settingForm.name,callback:function(e){t.$set(t.settingForm,"name",e)},expression:"settingForm.name"}})],1),e("el-form-item",{attrs:{label:"考勤组",prop:"attendanceGroup"}},[e("el-select",{staticClass:"ps-select w-250",attrs:{multiple:!0,placeholder:"请下拉选择","popper-class":"ps-popper-select"},model:{value:t.settingForm.attendanceGroup,callback:function(e){t.$set(t.settingForm,"attendanceGroup",e)},expression:"settingForm.attendanceGroup"}},t._l(t.attendanceGroupList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),e("el-form-item",{attrs:{label:"适用日期",prop:"week"}},[e("el-select",{staticClass:"ps-select w-250",attrs:{multiple:!0,placeholder:"请下拉选择","popper-class":"ps-popper-select"},on:{change:t.changeWeek},model:{value:t.settingForm.week,callback:function(e){t.$set(t.settingForm,"week",e)},expression:"settingForm.week"}},t._l(t.weekList,(function(t){return e("el-option",{key:t.key,attrs:{label:t.name,value:t.key}})})),1)],1),e("el-form-item",{attrs:{label:"考勤时段",prop:"timeRangeList"}},t._l(t.settingForm.timeRangeList,(function(i,r){return e("div",{key:r,staticClass:"time-setting"},[e("div",{staticClass:"time-range"},[e("el-form-item",{attrs:{label:"签到时间",prop:"timeRangeList."+r+".sign_in_time",rules:t.settingFormRules.sign_in_time}},[e("el-time-picker",{staticClass:"ps-picker w-150",attrs:{"value-format":"HH:mm",format:"HH:mm"},model:{value:i.sign_in_time,callback:function(e){t.$set(i,"sign_in_time",e)},expression:"item.sign_in_time"}})],1),e("el-form-item",{attrs:{label:"签退时间",prop:"timeRangeList."+r+".sign_out_time",rules:t.settingFormRules.sign_out_time}},[e("el-time-picker",{staticClass:"ps-picker w-150",attrs:{"value-format":"HH:mm",format:"HH:mm"},model:{value:i.sign_out_time,callback:function(e){t.$set(i,"sign_out_time",e)},expression:"item.sign_out_time"}})],1),t.settingForm.timeRangeList.length>1?e("img",{attrs:{src:n("1597"),alt:""},on:{click:function(e){return t.delTimeRange(r)}}}):t._e(),t.settingForm.timeRangeList.length-1===r?e("img",{attrs:{src:n("a851"),alt:""},on:{click:function(e){return t.addTimeRange()}}}):t._e()],1),e("el-form-item",{staticClass:"time-limit",attrs:{label:"打卡时间限制"}},[e("el-form-item",{attrs:{prop:"timeRangeList."+r+".sign_in_time_ahead",rules:t.settingFormRules.validateMin}},[t._v(" 签到前 "),e("el-input",{staticClass:"ps-input time-limit-input",model:{value:i.sign_in_time_ahead,callback:function(e){t.$set(i,"sign_in_time_ahead",e)},expression:"item.sign_in_time_ahead"}}),t._v(" 分钟可打签到卡 ")],1),e("el-form-item",{attrs:{prop:"timeRangeList."+r+".sign_out_time_ahead",rules:t.settingFormRules.validateMin}},[t._v(" 允许最晚 "),e("el-input",{staticClass:"ps-input time-limit-input",model:{value:i.sign_out_time_ahead,callback:function(e){t.$set(i,"sign_out_time_ahead",e)},expression:"item.sign_out_time_ahead"}}),t._v(" 分钟打签退卡 ")],1),e("el-form-item",{attrs:{prop:"timeRangeList."+r+".be_late_time_ahead",rules:t.settingFormRules.validateMin}},[e("el-input",{staticClass:"ps-input time-limit-input",model:{value:i.be_late_time_ahead,callback:function(e){t.$set(i,"be_late_time_ahead",e)},expression:"item.be_late_time_ahead"}}),t._v(" 分钟内打卡计为迟到，超过设定时间计为缺卡 ")],1),e("el-form-item",{attrs:{prop:"timeRangeList."+r+".leave_early_time_ahead",rules:t.settingFormRules.validateMin}},[t._v(" 提前 "),e("el-input",{staticClass:"ps-input time-limit-input",model:{value:i.leave_early_time_ahead,callback:function(e){t.$set(i,"leave_early_time_ahead",e)},expression:"item.leave_early_time_ahead"}}),t._v(" 分钟打卡算早退 ")],1)],1)],1)})),0),e("el-form-item",{attrs:{label:"节假日跳过"}},[e("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:t.settingForm.isSkipHoliday,callback:function(e){t.$set(t.settingForm,"isSkipHoliday",e)},expression:"settingForm.isSkipHoliday"}})],1),e("el-form-item",{staticClass:"special-date",attrs:{label:"特殊日期"}},[t._l(t.settingForm.skipDaysList,(function(n,i){return e("div",{key:i,staticClass:"no-skip-days"},[t._v(" "+t._s(t.formatDateText(n))+" "),e("div",{staticClass:"del-day",on:{click:function(e){return t.delSkipDay(i)}}},[e("i",{staticClass:"el-icon-error"})])])})),e("div",{staticClass:"hidden-date"},[e("img",{attrs:{src:n("a851"),alt:""}}),e("el-date-picker",{attrs:{type:"dates",clearable:!1,placeholder:"选择一个或多个日期",format:"MM-dd","value-format":"MM-dd","popper-class":"hidden-picker-year"},on:{change:t.changeSkipDays},model:{value:t.settingForm.skipDays,callback:function(e){t.$set(t.settingForm,"skipDays",e)},expression:"settingForm.skipDays"}})],1)],2),e("el-form-item",[e("el-button",{staticClass:"ps-origin-btn w-150",attrs:{size:"small",type:"primary"},on:{click:t.saveSetting}},[t._v("保存")])],1)],1)],1)])])},r=[],a=n("9210");function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return e};var t,e={},n=Object.prototype,i=n.hasOwnProperty,r=Object.defineProperty||function(t,e,n){t[e]=n.value},a="function"==typeof Symbol?Symbol:{},c=a.iterator||"@@iterator",l=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function m(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{m({},"")}catch(t){m=function(t,e,n){return t[e]=n}}function p(t,e,n,i){var a=e&&e.prototype instanceof v?e:v,o=Object.create(a.prototype),s=new C(i||[]);return r(o,"_invoke",{value:S(t,n,s)}),o}function f(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=p;var g="suspendedStart",d="suspendedYield",h="executing",y="completed",_={};function v(){}function b(){}function k(){}var w={};m(w,c,(function(){return this}));var L=Object.getPrototypeOf,F=L&&L(L($([])));F&&F!==n&&i.call(F,c)&&(w=F);var x=k.prototype=v.prototype=Object.create(w);function D(t){["next","throw","return"].forEach((function(e){m(t,e,(function(t){return this._invoke(e,t)}))}))}function O(t,e){function n(r,a,s,c){var l=f(t[r],t,a);if("throw"!==l.type){var u=l.arg,m=u.value;return m&&"object"==o(m)&&i.call(m,"__await")?e.resolve(m.__await).then((function(t){n("next",t,s,c)}),(function(t){n("throw",t,s,c)})):e.resolve(m).then((function(t){u.value=t,s(u)}),(function(t){return n("throw",t,s,c)}))}c(l.arg)}var a;r(this,"_invoke",{value:function(t,i){function r(){return new e((function(e,r){n(t,i,e,r)}))}return a=a?a.then(r,r):r()}})}function S(e,n,i){var r=g;return function(a,o){if(r===h)throw Error("Generator is already running");if(r===y){if("throw"===a)throw o;return{value:t,done:!0}}for(i.method=a,i.arg=o;;){var s=i.delegate;if(s){var c=E(s,i);if(c){if(c===_)continue;return c}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if(r===g)throw r=y,i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);r=h;var l=f(e,n,i);if("normal"===l.type){if(r=i.done?y:d,l.arg===_)continue;return{value:l.arg,done:i.done}}"throw"===l.type&&(r=y,i.method="throw",i.arg=l.arg)}}}function E(e,n){var i=n.method,r=e.iterator[i];if(r===t)return n.delegate=null,"throw"===i&&e.iterator.return&&(n.method="return",n.arg=t,E(e,n),"throw"===n.method)||"return"!==i&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+i+"' method")),_;var a=f(r,e.iterator,n.arg);if("throw"===a.type)return n.method="throw",n.arg=a.arg,n.delegate=null,_;var o=a.arg;return o?o.done?(n[e.resultName]=o.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,_):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,_)}function R(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function C(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(R,this),this.reset(!0)}function $(e){if(e||""===e){var n=e[c];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,a=function n(){for(;++r<e.length;)if(i.call(e,r))return n.value=e[r],n.done=!1,n;return n.value=t,n.done=!0,n};return a.next=a}}throw new TypeError(o(e)+" is not iterable")}return b.prototype=k,r(x,"constructor",{value:k,configurable:!0}),r(k,"constructor",{value:b,configurable:!0}),b.displayName=m(k,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,k):(t.__proto__=k,m(t,u,"GeneratorFunction")),t.prototype=Object.create(x),t},e.awrap=function(t){return{__await:t}},D(O.prototype),m(O.prototype,l,(function(){return this})),e.AsyncIterator=O,e.async=function(t,n,i,r,a){void 0===a&&(a=Promise);var o=new O(p(t,n,i,r),a);return e.isGeneratorFunction(n)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},D(x),m(x,u,"Generator"),m(x,c,(function(){return this})),m(x,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var i in e)n.push(i);return n.reverse(),function t(){for(;n.length;){var i=n.pop();if(i in e)return t.value=i,t.done=!1,t}return t.done=!0,t}},e.values=$,C.prototype={constructor:C,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var n in this)"t"===n.charAt(0)&&i.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function r(i,r){return s.type="throw",s.arg=e,n.next=i,r&&(n.method="next",n.arg=t),!!r}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],s=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var c=i.call(o,"catchLoc"),l=i.call(o,"finallyLoc");if(c&&l){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&i.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var a=r;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=t,o.arg=e,a?(this.method="next",this.next=a.finallyLoc,_):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),_},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),j(n),_}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var i=n.completion;if("throw"===i.type){var r=i.arg;j(n)}return r}}throw Error("illegal catch attempt")},delegateYield:function(e,n,i){return this.delegate={iterator:$(e),resultName:n,nextLoc:i},"next"===this.method&&(this.arg=t),_}},e}function c(t,e,n,i,r,a,o){try{var s=t[a](o),c=s.value}catch(t){return void n(t)}s.done?e(c):Promise.resolve(c).then(i,r)}function l(t){return function(){var e=this,n=arguments;return new Promise((function(i,r){var a=t.apply(e,n);function o(t){c(a,i,r,o,s,"next",t)}function s(t){c(a,i,r,o,s,"throw",t)}o(void 0)}))}}var u={name:"AddOrEditAttendanceSetting",components:{},props:{},data:function(){var t=this,e=function(t,e,n){var i=/^[0-9]+$/;if(!e)return n(new Error("请输入时长，单位为分钟"));i.test(e)?e>1e3?n(new Error("不能大于1000分钟")):n():n(new Error("分钟必须为大于零的正整数"))},n=function(e,n,i){var r=e.field.split(".")[1],a=t.settingForm.timeRangeList[r];if(!n)return i(new Error("请选择签到时间"));a.sign_in_time===a.sign_out_time?i(new Error("签到时间与签退时间不能相同")):a.sign_out_time&&a.sign_in_time>a.sign_out_time?i(new Error("签到时间不能晚于签退时间")):i()},i=function(e,n,i){var r=e.field.split(".")[1],a=t.settingForm.timeRangeList[r];if(!n)return i(new Error("请选择签退时间"));a.sign_in_time===a.sign_out_time?i(new Error("签到时间与签退时间不能相同")):a.sign_in_time&&a.sign_in_time>a.sign_out_time?i(new Error("签退时间不能早于签到时间")):i()};return{isLoading:!1,type:"",settingData:{},settingForm:{name:"",attendanceGroup:[],week:[],timeRangeList:[{sign_in_time:"",sign_out_time:"",sign_in_time_ahead:"",sign_out_time_ahead:"",be_late_time_ahead:"",leave_early_time_ahead:""}],isSkipHoliday:!1,skipDaysList:[],skipDays:[]},settingFormRules:{name:[{required:!0,message:"请输入名称",trigger:"blur"}],attendanceGroup:[{required:!0,message:"请选择考勤组",trigger:"change"}],week:[{required:!0,message:"请选择适用日期",trigger:"change"}],sign_in_time:[{required:!0,validator:n,trigger:"change"}],sign_out_time:[{required:!0,validator:i,trigger:"change"}],validateMin:[{required:!0,validator:e,trigger:"blur"}]},attendanceGroupList:[],weekList:a["weekList"]}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){var t=this;this.$route.query.data&&(this.settingData=JSON.parse(decodeURIComponent(this.$route.query.data)),this.settingForm.name=this.settingData.name,this.settingForm.attendanceGroup=this.settingData.attendance_groups_ids,this.settingForm.week=this.settingData.week_day_range,this.settingForm.timeRangeList=[],this.settingData.attendance_time_settings.map((function(e,n){for(var i in t.settingForm.timeRangeList.push({}),e)"id"!==i&&"status"!==i&&t.$set(t.settingForm.timeRangeList[n],i,e[i])})),this.settingForm.isSkipHoliday=this.settingData.is_pass_holiday,this.settingForm.skipDaysList=this.settingData.except_date_range,this.settingForm.skipDays=this.settingData.except_date_range),this.$route.params.type&&(this.type=this.$route.params.type),this.getAttendanceGroupList()},saveSetting:function(){var t=this;this.$refs.settingFormRef.validate((function(e){if(e){var n,i={name:t.settingForm.name,attendance_groups:t.settingForm.attendanceGroup,week_day_range:t.settingForm.week,attendance_time_settings:t.settingForm.timeRangeList,is_pass_holiday:t.settingForm.isSkipHoliday,except_date_range:t.settingForm.skipDaysList};switch(t.type){case"add":n=t.$apis.apiBackgroundAttendanceAttendanceSettingsAddPost(i);break;case"edit":i.id=Number(t.settingData.id),n=t.$apis.apiBackgroundAttendanceAttendanceSettingsModifyPost(i);break}t.confirmOperation(n)}}))},confirmOperation:function(t){var e=this;return l(s().mark((function n(){var i;return s().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!e.isLoading){n.next=2;break}return n.abrupt("return");case 2:return e.isLoading=!0,n.next=5,t;case 5:i=n.sent,e.isLoading=!1,0===i.code?(e.$message.success("成功"),e.$closeCurrentTab(e.$route.path)):e.$message.error(i.msg);case 8:case"end":return n.stop()}}),n)})))()},getAttendanceGroupList:function(){var t=this;return l(s().mark((function e(){var n;return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.$apis.apiBackgroundAttendanceAttendanceGroupListPost({page:1,page_size:99999});case 3:n=e.sent,t.isLoading=!1,0===n.code?t.attendanceGroupList=n.data.results:t.$message.error(n.msg);case 6:case"end":return e.stop()}}),e)})))()},addTimeRange:function(){this.settingForm.timeRangeList.push({sign_in_time:"",sign_out_time:"",sign_in_time_ahead:"",sign_out_time_ahead:"",be_late_time_ahead:"",leave_early_time_ahead:""})},delTimeRange:function(t){this.settingForm.timeRangeList.splice(t,1)},changeSkipDays:function(){this.settingForm.skipDaysList=this.settingForm.skipDays},clearSkipDays:function(){this.settingForm.skipDaysList=[],this.settingForm.skipDays=""},delSkipDay:function(t){this.settingForm.skipDaysList.splice(t,1),this.settingForm.skipDays=this.settingForm.skipDaysList},formatDateText:function(t){var e=t.split("-")[0]+"月"+t.split("-")[1]+"日";return e},changeWeek:function(t,e){-1!==t.indexOf("all")&&(this.settingForm.week=["1","2","3","4","5","6","7"])}}},m=u,p=(n("a5fe"),n("2877")),f=Object(p["a"])(m,i,r,!1,null,"42caaa00",null);e["default"]=f.exports}}]);