(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-health-system-HealthSystem~view-merchant-health-system-components-ArchivesDetail"],{"0919":function(t,e,a){"use strict";a("f7ad")},3373:function(t,e,a){"use strict";a("8c42")},"782d":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"ps-el-drawer"},[e("el-drawer",{attrs:{title:"档案详情",visible:t.visible,"show-close":!1,size:"70%"}},[e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"drawer-content p-20 flex-col"},[e("div",{staticClass:"drawer-content-info m-b-20"},[e("div",{staticClass:"f-w-700 font-size-18 m-b-10"},[t._v("基本信息")]),e("div",{staticClass:"bg-grey ps-flex-align-c flex-justify-c"},[e("el-image",{staticStyle:{width:"100px",height:"100px"},attrs:{src:t.headImg,fit:"contain"}}),e("div",{staticClass:"drawer-content-info-box m-l-20 w-100-p"},t._l(t.infoData,(function(a,i){return e("div",{key:i,class:["line"===a.type?"line":""]},["line"!==a.type?e("div",{staticClass:"ps-flex-align-c flex-align-c"},[e("div",{staticClass:"m-r-20"},[e("span",{staticStyle:{color:"#939597"}},[t._v(t._s(a.label)+"：")]),e("span",[t._v(t._s(a.value)+t._s(a.unit))])]),"bmi"===a.key?e("div",{class:["tips",t.selectClass(a.text)],staticStyle:{width:"60px"}},[t._v(t._s(a.text))]):t._e()]):e("div",["饮食禁忌"===a.label?e("div",[e("span",{staticStyle:{color:"#939597"}},[t._v(t._s(a.label)+"：")]),t._l(a.value,(function(i,r){return e("span",{key:r},[t._v(" "+t._s(i)+" "+t._s(r<a.value.length-1?"、":""))])}))],2):e("div",{staticClass:"ps-flex-align-c flex-align-c"},[e("div",{staticClass:"m-r-20"},[e("span",{staticStyle:{color:"#939597"}},[t._v(t._s(a.label)+"：")]),e("span",[t._v(t._s(a.value)+t._s(a.unit))])]),"job_alias"===a.key?e("div",{class:["tips",t.selectClass(t.computedPAL(a.Job_intensity))],staticStyle:{width:"80px"}},[t._v(t._s(t.computedPAL(a.Job_intensity)))]):t._e()])])])})),0)],1)]),e("div",{staticClass:"drawer-content-physicalData m-b-20"},[e("div",{staticClass:"f-w-700 font-size-18 m-b-10"},[t._v("体检数据")]),e("div",{staticClass:"bg-grey"},[t.physicalData.length?e("div",{staticClass:"drawer-content-physicalData-box"},t._l(t.physicalData,(function(a,i){return e("div",{key:i,staticClass:"ps-flex-align-c flex-align-c"},[e("div",{staticClass:"m-r-10"},[e("span",{staticStyle:{color:"#939597"}},[t._v(t._s(a.name))]),t._v("： "+t._s(a.value||"--")+" "+t._s(a.unit)+" ")])])})),0):e("div",{staticClass:"flex-center"},[e("el-empty",{attrs:{description:"暂无数据","image-size":80}})],1)])]),e("div",{staticClass:"drawer-content-nutrient m-b-20"},[e("div",{staticClass:"f-w-700 font-size-18 m-b-10"},[t._v("营养素摄入推荐")]),e("div",{staticClass:"bg-grey"},[t.nutrientData.length?e("div",{staticClass:"drawer-content-nutrient-box"},t._l(t.nutrientData,(function(a,i){return e("div",{key:i},[e("span",{staticStyle:{color:"#939597"}},[t._v(t._s(a.name))]),t._v("： "+t._s(a.range[0]+"~"+a.range[1])+" "+t._s(a.unit)+" ")])})),0):e("div",{staticClass:"flex-center"},[e("el-empty",{attrs:{description:"暂无数据","image-size":80}})],1)])]),e("div",{staticClass:"drawer-content-diet m-b-20"},[e("div",{staticClass:"f-w-700 font-size-18 m-b-10"},[t._v("饮食结构")]),e("div",{staticClass:"bg-grey"},[t.dietData.length?e("div",{staticClass:"drawer-content-diet-box"},t._l(t.dietData,(function(a,i){return e("div",{key:i,staticClass:"drawer-content-diet-box-item"},[e("span",{staticStyle:{color:"#939597"}},[t._v(t._s(a.name))]),t._v("： "+t._s(a.range[0]+"~"+a.range[1])+" "+t._s(a.unit)+" ")])})),0):e("div",{staticClass:"flex-center"},[e("el-empty",{attrs:{description:"暂无数据","image-size":80}})],1)])]),e("div",{staticClass:"drawer-content-forewarning m-b-20"},[e("div",{staticClass:"f-w-700 font-size-18 m-b-10"},[t._v("摄入预警")]),e("div",{staticClass:"bg-grey"},[e("div",{staticClass:"drawer-content-forewarning-box"},[e("diet",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],attrs:{formInfoData:t.nutrientIntakeData}})],1)])]),e("div",{staticClass:"m-b-20"},[e("div",{staticClass:"f-w-700 font-size-18 m-b-10"},[t._v("运动数据")]),e("div",{staticClass:"bg-grey"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableView",attrs:{data:t.sportTableData,stripe:"","header-row-class-name":"ps-table-header-row"}},t._l(t.sportTableSetting,(function(t){return e("table-column",{key:t.key,attrs:{col:t}})})),1)],1)]),e("div",{staticClass:"m-b-20"},[e("div",{staticClass:"f-w-700 font-size-18 m-b-10"},[t._v("习惯打卡")]),e("div",{staticClass:"bg-grey"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableView",attrs:{data:t.habitTableData,stripe:"","header-row-class-name":"ps-table-header-row"}},t._l(t.habitTableSetting,(function(t){return e("table-column",{key:t.key,attrs:{col:t}})})),1)],1)]),e("div",{staticClass:"ps-el-drawer-footer ps-flex-align-c flex-align-c"},[e("div",{staticClass:"m-r-30"},[e("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:function(e){t.visible=!1}}},[t._v("关闭")])],1)])])])],1)},r=[],n=a("ed08"),s=a("bd1e");function l(t){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},l(t)}function o(t){return f(t)||d(t)||u(t)||c()}function c(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(t,e){if(t){if("string"==typeof t)return y(t,e);var a={}.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?y(t,e):void 0}}function d(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function f(t){if(Array.isArray(t))return y(t)}function y(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,i=Array(e);a<e;a++)i[a]=t[a];return i}function v(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,i)}return a}function b(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?v(Object(a),!0).forEach((function(e){m(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):v(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function m(t,e,a){return(e=_(e))in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function _(t){var e=h(t,"string");return"symbol"==l(e)?e:e+""}function h(t,e){if("object"!=l(t)||!t)return t;var a=t[Symbol.toPrimitive];if(void 0!==a){var i=a.call(t,e||"default");if("object"!=l(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var p={props:{isShow:Boolean,archivesDetailParams:{type:Object,default:function(){return{}}}},components:{Diet:s["default"]},data:function(){return{isLoading:!1,infoData:[{key:"name",label:"姓名",value:"",unit:"",type:""},{key:"person_no",label:"人员编号",value:"",unit:"",type:""},{key:"phone",label:"手机号",value:"",unit:"",type:""},{key:"gender",label:"性别",value:"",unit:"",type:""},{key:"age",label:"年龄",value:"",unit:"岁",type:""},{key:"height",label:"身高",value:"",unit:"cm",type:""},{key:"weight",label:"体重",value:"",unit:"kg",type:""},{key:"bmi",label:"BMI",value:"19",text:"",unit:"",type:""},{key:"job_alias",label:"职业",value:"",pal:"",unit:"",type:"line"},{key:"disease_name",label:"个人特征",value:"",unit:"",type:"line"},{key:"ingredient_taboo",label:"饮食禁忌",value:"",unit:"",type:"line"}],headImg:"",physicalData:[],nutrientData:[],dietData:[],sportTableSetting:[{label:"运动名称",key:"name"},{label:"运动强度",key:"intensity_alias"},{label:"最近一次记录",key:"last_update_time"},{label:"最近一次运动时长",key:"last_length"},{label:"最近一次消耗能量",key:"last_use_energy_kcal"},{label:"总记录次数",key:"count"}],habitTableSetting:[{label:"习惯名称",key:"name"},{label:"最近一次打卡",key:"update_time"},{label:"打卡次数",key:"count"}],nutrientIntakeData:{},sportTableData:[],habitTableData:[]}},computed:{visible:{get:function(){return this.isShow},set:function(t){this.$emit("update:isShow",t)}},tipName:function(){return function(t){var e="";switch(t){case"low":e="偏低";break;case"normal":e="正常";break;case"height":e="偏高";break;default:break}return e}},computedPAL:function(){return function(t){var e="";switch(t){case"lv2":e="PAL：中";break;case"lv3":e="PAL：重";break;default:e="PAL：轻";break}return e}}},watch:{visible:function(t,e){t&&this.setTableData()}},methods:{setTableData:function(){var t=this;this.isLoading=!0,this.$apis.apiBackgroundHealthyHealthyInfoHealthyInfoDetailsPost(b({},this.archivesDetailParams)).then((function(e){if(0===e.code){var a=e.data;t.headImg=a.base_data.head_image||"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png",t.infoData.forEach((function(t){for(var e in a.base_data)t.key===e&&(t.value=a.base_data[e]),"bmi"===t.key&&"bmi_text"===e&&(t.text=a.base_data[e]),"job"===t.key&&"Job_intensity"===e&&(t.pal=a.base_data[e])}));var i=Object(n["f"])(a.physical_data)||{};if(i.return_data&&i.return_data.length)if("custom"!==i.data_source){var r=i.return_data.filter((function(t){return"blood_pressure_l"===t.key})),s=r[0].children.map((function(t){return t.name=t.name+"(左臂)",t})),l=i.return_data.filter((function(t){return"blood_pressure_r"===t.key})),c=l[0].children.map((function(t){return t.name=t.name+"(右臂)",t}));t.physicalData=[].concat(o(i.return_data[0].children),o(i.return_data[1].children),o(s),o(c))}else t.physicalData=[].concat(o(i.return_data[0].children),o(i.return_data[1].children),o(i.return_data[2].children));t.nutrientData=Object(n["f"])(a.nutrition_intake),t.dietData=Object(n["f"])(a.food_category_nutrition),t.nutrientIntakeData=Object(n["f"])(a.nutrient_intake_data),t.sportTableData=Object(n["f"])(a.sport_data.sport_list),t.habitTableData=Object(n["f"])(a.habit_data.habit_list),t.isLoading=!1}else t.$message.error(e.msg)}))},selectClass:function(t){var e="";switch(t){case"low":e="low";break;case"normal":e="normal";break;case"hight":e="height";break;case"PAL：轻":e="low";break;case"PAL：中":e="normal";break;case"PAL：重":e="height";break;default:e="overWeight"}return e}}},g=p,k=(a("3373"),a("2877")),D=Object(k["a"])(g,i,r,!1,null,"73c8e322",null);e["default"]=D.exports},"8c42":function(t,e,a){},bd1e:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"diet-wrapp"},[e("div",{staticClass:"ps-flex flex-wrap m-t-10"},[e("div",[e("div",{staticClass:"diet-title m-r-10 m-b-10"},[t._m(0),e("div",{ref:"intake_record",staticStyle:{height:"240px"},attrs:{id:"circular_chart"}})])]),e("div",[e("div",{staticClass:"diet-title m-r-10 m-b-10"},[t._m(1),e("div",{ref:"source",staticStyle:{height:"240px"},attrs:{id:"circular_chart"}})])]),e("div",[e("div",{staticClass:"diet-title m-r-10 m-b-10"},[t._m(2),e("div",{ref:"intake_exceed",staticStyle:{height:"240px"},attrs:{id:"circular_chart"}})])]),e("div",[e("div",{staticClass:"diet-title m-r-10 m-b-10"},[t._m(3),e("div",{ref:"food_category",staticStyle:{height:"240px"},attrs:{id:"circular_chart"}})])])])])},r=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"ps-flex flex-align-c"},[e("span",{staticClass:"text p-l-10"},[t._v("饮食记录")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"ps-flex flex-align-c"},[e("span",{staticClass:"text p-l-10"},[t._v("记录来源")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"ps-flex flex-align-c"},[e("span",{staticClass:"text p-l-10"},[t._v("摄入超标")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"ps-flex flex-align-c"},[e("span",{staticClass:"text p-l-10"},[t._v("食物种类")])])}],n=a("9c61"),s=a("ed08"),l=a("da92"),o={props:{formInfoData:{type:Object,default:function(){return{}}}},data:function(){return{isLoading:!1,formData:{},circularChartRefList:[{key:"intake_record",data:[{value:0,name:"早餐",key:"breakfast",current:0,unit:"次"},{value:0,name:"午餐",key:"lunch",current:0,unit:"次"},{value:0,name:"晚餐",key:"dinner",current:0,unit:"次"}],color:["#07DED0","#FE985F","#e98397"]},{key:"source",data:[{value:0,name:"系统",key:"system",current:0,unit:"次"},{value:0,name:"手动",key:"user",current:0,unit:"次"}],color:["#FE985F","#f6c80e"]},{key:"intake_exceed",data:[{value:0,name:"早餐",key:"breakfast",current:0,unit:"次"},{value:0,name:"午餐",key:"lunch",current:0,unit:"次"},{value:0,name:"晚餐",key:"dinner",current:0,unit:"次"}],color:["red","#FE985F","#e98397"]},{key:"food_category",data:[{value:0,name:"谷类薯类",key:"cereals_tubers",current:0,unit:"种"},{value:0,name:"鱼禽蛋肉",key:"eggsandmeat",current:0,unit:"种"},{value:0,name:"蔬菜水果",key:"fruit_vegetable",current:0,unit:"种"},{value:0,name:"奶类豆类",key:"dairy",current:0,unit:"种"}],color:["#08d7d7","#4e95fa","#4ad96c","#727aff"]}],pieChart:{intake_record:null,source:null,intake_exceed:null,food_category:null},tabType:"food",tableData:[],dietData:{intake_exceed_total:0,intake_record_total:0,source_total:0,food_category_total:0},currentPage:1,pageSize:6,totalCount:0,foodList:[],ingredientList:[]}},watch:{formInfoData:function(t){var e=this;this.tabType="food",this.formData=t,this.dietData.intake_record_total=l["a"].plus(this.formData.intake_record.breakfast,this.formData.intake_record.lunch,this.formData.intake_record.dinner),this.dietData.source_total=l["a"].plus(this.formData.source.system,this.formData.source.user),this.dietData.intake_exceed_total=l["a"].plus(this.formData.intake_exceed.breakfast,this.formData.intake_exceed.lunch,this.formData.intake_exceed.dinner),this.dietData.food_category_total=l["a"].plus(this.formData.food_category.cereals_tubers,this.formData.food_category.dairy,this.formData.food_category.eggsandmeat,this.formData.food_category.fruit_vegetable),this.tableData=this.formData.food_list,this.foodList=this.formData.food_list,this.ingredientList=this.formData.ingredient_list,this.$nextTick((function(){e.initMealTimeDataPie()}))}},created:function(){},beforeDestroy:function(){window.removeEventListener("resize",this.resizeChartHandle)},mounted:function(){window.addEventListener("resize",this.resizeChartHandle)},methods:{initMealTimeDataPie:function(){var t=this;this.circularChartRefList.forEach((function(e){var a=e.data,i={};a.forEach((function(a){"intake_record"===e.key&&t.formData[e.key][a.key]/t.dietData.intake_record_total&&(a.value=Number((t.formData[e.key][a.key]/t.dietData.intake_record_total*100).toFixed(2))),"source"===e.key&&t.formData[e.key][a.key]/t.dietData.source_total&&(a.value=Number((t.formData[e.key][a.key]/t.dietData.source_total*100).toFixed(2))),"intake_exceed"===e.key&&t.formData[e.key][a.key]/t.dietData.intake_exceed_total&&(a.value=Number((t.formData[e.key][a.key]/t.dietData.intake_exceed_total*100).toFixed(2))),"food_category"===e.key&&t.formData[e.key][a.key]/t.dietData.food_category_total&&(a.value=Number((t.formData[e.key][a.key]/t.dietData.food_category_total*100).toFixed(2))),i[a.name]={value:a.value,current:t.formData[e.key][a.key],unit:a.unit}}));var r=n["MEALTIME_SETTING"];r.legend.formatter=function(t){var e=i[t];return t+"    "+(e.value||0)+"%    "+(e.current||0)+e.unit},r.series[0].data=a,r.title.text="".concat(t.dietData[e.key+"_total"]).concat("food_category"===e.key?"种":"次"),t.pieChart[e.key]||(t.pieChart[e.key]=t.$echarts.init(t.$refs[e.key])),t.pieChart[e.key].setOption(r)}))},tableLoadmore:function(){var t=this;setTimeout((function(){t.pageSize+=10}),100)},tabClick:function(t){this.tabType=t,this.tableData=[],this.pageSize=6,this.tableData="food"===t?this.foodList:this.ingredientList},resizeChartHandle:Object(s["d"])((function(){this.pieChart.intake_record&&this.pieChart.intake_record.resize(),this.pieChart.source&&this.pieChart.source.resize(),this.pieChart.intake_exceed&&this.pieChart.intake_exceed.resize(),this.pieChart.food_category&&this.pieChart.food_category.resize()}),300)}},c=o,u=(a("0919"),a("2877")),d=Object(u["a"])(c,i,r,!1,null,null,null);e["default"]=d.exports},f7ad:function(t,e,a){}}]);