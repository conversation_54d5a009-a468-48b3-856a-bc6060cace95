(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-accessControl-attendance-attendance-components-AttendanceGroupDialog"],{"40f3":function(t,e,r){"use strict";r("f612")},"73f7":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("dialog-message",{attrs:{show:t.visible,title:t.title,loading:t.isLoading,customClass:"ps-dialog",width:t.width},on:{"update:show":function(e){t.visible=e},"update:loading":function(e){t.isLoading=e},close:t.handleClose}},[e("el-form",{ref:"dialogFormRef",staticClass:"attendance-form",attrs:{model:t.dialogForm,"status-icon":"",rules:t.dialogFormRules,"label-width":"70px",inline:""},nativeOn:{submit:function(t){t.preventDefault()}}},["choosePerson"===t.type?e("div",[e("el-form-item",{attrs:{label:"姓名"}},[e("el-input",{staticClass:"ps-input w-180",on:{input:t.searchUserList},model:{value:t.personName,callback:function(e){t.personName=e},expression:"personName"}})],1),e("el-form-item",{attrs:{label:"人员编号","label-width":"80px"}},[e("el-input",{staticClass:"ps-input w-180",on:{input:t.searchUserList},model:{value:t.personNo,callback:function(e){t.personNo=e},expression:"personNo"}})],1),e("el-form-item",{attrs:{label:"分组"}},[e("user-group-select",{staticClass:"search-item-w ps-input w-180",attrs:{multiple:!0,"collapse-tags":!0,placeholder:"请下拉选择"},on:{change:t.searchUserList},model:{value:t.userGroup,callback:function(e){t.userGroup=e},expression:"userGroup"}})],1),e("el-form-item",{attrs:{label:"部门"}},[e("user-department-select",{staticClass:"w-180 ps-input",attrs:{clearable:!0,multiple:!0,"check-strictly":!0,isLazy:!1,placeholder:"请选择部门","append-to-body":!0},on:{change:t.searchUserList},model:{value:t.userDepartment,callback:function(e){t.userDepartment=e},expression:"userDepartment"}})],1),e("div",{staticClass:"person-table"},[e("el-table",{ref:"userListRef",attrs:{data:t.userList,"row-key":t.getRowKey,"header-row-class-name":"ps-table-header-row"},on:{select:t.handleSelection,"select-all":t.handleAllSelection}},[e("el-table-column",{attrs:{type:"selection","reserve-selection":!0,width:"50",align:"center","class-name":"ps-checkbox"}}),e("el-table-column",{attrs:{prop:"name",label:"姓名",align:"center"}}),e("el-table-column",{attrs:{prop:"person_no",label:"人员编号",align:"center"}}),e("el-table-column",{attrs:{prop:"card_department_group_alias",label:"部门",align:"center"}}),e("el-table-column",{attrs:{prop:"phone",label:"手机号",align:"center"}})],1)],1),e("div",{staticClass:"block ps-pagination person-table-bottom"},[e("div",{staticStyle:{width:"100px"}},[t._v("已选人数："+t._s(t.selectList.length))]),e("el-pagination",{staticClass:"ps-text",attrs:{"current-page":t.currentPage,"page-sizes":[10,20,50,100,500],"page-size":t.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:t.totalCount,"pager-count":5,background:"","popper-class":"ps-popper-select"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1):t._e(),"addAttendanceGroup"===t.type||"editAttendanceGroup"===t.type?e("div",[e("el-form-item",{attrs:{label:"名称",prop:"groupName"}},[e("el-input",{staticClass:"ps-input w-250",attrs:{maxlength:"20"},model:{value:t.dialogForm.groupName,callback:function(e){t.$set(t.dialogForm,"groupName",e)},expression:"dialogForm.groupName"}})],1),e("el-form-item",{attrs:{label:"备注",prop:"groupRemark"}},[e("el-input",{staticClass:"ps-input w-250",attrs:{type:"textarea"},model:{value:t.dialogForm.groupRemark,callback:function(e){t.$set(t.dialogForm,"groupRemark",e)},expression:"dialogForm.groupRemark"}})],1)],1):t._e(),"addGroupAdministrators"===t.type||"editGroupAdministrators"===t.type?e("div",[e("el-form-item",{attrs:{label:"名称",prop:"administratorsName"}},[e("el-input",{staticClass:"ps-input w-250",attrs:{maxlength:"20"},model:{value:t.dialogForm.administratorsName,callback:function(e){t.$set(t.dialogForm,"administratorsName",e)},expression:"dialogForm.administratorsName"}})],1),e("el-form-item",{attrs:{label:"手机号",prop:"administratorsPhone"}},[e("el-input",{staticClass:"ps-input w-250",model:{value:t.dialogForm.administratorsPhone,callback:function(e){t.$set(t.dialogForm,"administratorsPhone",e)},expression:"dialogForm.administratorsPhone"}})],1),e("el-form-item",{attrs:{label:"考勤组",prop:"administratorsGroups"}},[e("el-select",{staticClass:"ps-select w-250",attrs:{multiple:!0,placeholder:"请下拉选择","popper-class":"ps-popper-select"},model:{value:t.dialogForm.administratorsGroups,callback:function(e){t.$set(t.dialogForm,"administratorsGroups",e)},expression:"dialogForm.administratorsGroups"}},t._l(t.attendanceGroupList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),e("el-form-item",{attrs:{label:"备注",prop:"administratorsRemark"}},[e("el-input",{staticClass:"ps-input w-250",attrs:{type:"textarea"},model:{value:t.dialogForm.administratorsRemark,callback:function(e){t.$set(t.dialogForm,"administratorsRemark",e)},expression:"dialogForm.administratorsRemark"}})],1)],1):t._e()]),e("template",{slot:"tool"},[e("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:t.isLoading},on:{click:t.clickCancleHandle}},[t._v(" 取消 ")]),e("el-button",{staticClass:"ps-btn",attrs:{disabled:t.isLoading,type:"primary"},on:{click:t.clickConfirmHandle}},[t._v(" 确定 ")])],1)])],2)},o=[],i=r("ed08"),a=r("390a"),s=r("faa6"),c=r("bbd5");function l(t){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},l(t)}function u(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u(Object(r),!0).forEach((function(e){d(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function d(t,e,r){return(e=f(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function f(t){var e=m(t,"string");return"symbol"==l(e)?e:e+""}function m(t,e){if("object"!=l(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=l(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function h(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */h=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function p(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,a=Object.create(i.prototype),s=new C(n||[]);return o(a,"_invoke",{value:_(t,r,s)}),a}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=p;var f="suspendedStart",m="suspendedYield",g="executing",v="completed",y={};function b(){}function w(){}function L(){}var k={};u(k,a,(function(){return this}));var x=Object.getPrototypeOf,F=x&&x(x(j([])));F&&F!==r&&n.call(F,a)&&(k=F);var G=L.prototype=b.prototype=Object.create(k);function P(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function O(t,e){function r(o,i,a,s){var c=d(t[o],t,i);if("throw"!==c.type){var u=c.arg,p=u.value;return p&&"object"==l(p)&&n.call(p,"__await")?e.resolve(p.__await).then((function(t){r("next",t,a,s)}),(function(t){r("throw",t,a,s)})):e.resolve(p).then((function(t){u.value=t,a(u)}),(function(t){return r("throw",t,a,s)}))}s(c.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function _(e,r,n){var o=f;return function(i,a){if(o===g)throw Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var s=n.delegate;if(s){var c=S(s,n);if(c){if(c===y)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===f)throw o=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=g;var l=d(e,r,n);if("normal"===l.type){if(o=n.done?v:m,l.arg===y)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(o=v,n.method="throw",n.arg=l.arg)}}}function S(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,S(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var i=d(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,y;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function N(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function A(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function C(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(N,this),this.reset(!0)}function j(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(l(e)+" is not iterable")}return w.prototype=L,o(G,"constructor",{value:L,configurable:!0}),o(L,"constructor",{value:w,configurable:!0}),w.displayName=u(L,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,L):(t.__proto__=L,u(t,c,"GeneratorFunction")),t.prototype=Object.create(G),t},e.awrap=function(t){return{__await:t}},P(O.prototype),u(O.prototype,s,(function(){return this})),e.AsyncIterator=O,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new O(p(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},P(G),u(G,c,"Generator"),u(G,a,(function(){return this})),u(G,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=j,C.prototype={constructor:C,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(A),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return s.type="throw",s.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),l=n.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),A(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;A(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:j(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),y}},e}function g(t,e,r,n,o,i,a){try{var s=t[i](a),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,o)}function v(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){g(i,n,o,a,s,"next",t)}function s(t){g(i,n,o,a,s,"throw",t)}a(void 0)}))}}var y={name:"AttendanceGroupDialog",components:{UserGroupSelect:a["a"],UserDepartmentSelect:s["a"]},props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:""},width:{type:String,default:"400px"},selectInfo:{type:Object,default:function(){return{}}},personList:{type:Array,default:function(){return[]}},isshow:Boolean},data:function(){var t=function(t,e,r){var n=/^1[3456789]\d{9}$/;e?n.test(e)?r():r(new Error("手机号格式错误")):r(new Error("请输入手机号"))},e=function(t,e,r){var n=/^\S+$/;e?n.test(e)?r():r(new Error("名称不能包含空格")):r(new Error("请输入名称"))};return{isLoading:!1,dialogForm:{groupName:"",groupRemark:"",administratorsName:"",administratorsPhone:"",administratorsGroups:[],administratorsRemark:""},dialogFormRules:{groupName:[{required:!0,validator:e,trigger:"blur"}],administratorsName:[{required:!0,validator:e,trigger:"blur"}],administratorsPhone:[{required:!0,validator:t,trigger:"blur"}],administratorsGroups:[{required:!0,message:"请选择考勤组",trigger:"change"}]},organizationId:"",userGroup:[],userDepartment:[],personName:"",personNo:"",userList:[],pageSize:10,totalCount:0,currentPage:1,selectList:[],attendanceGroupList:[]}},computed:{visible:{get:function(){return this.isshow},set:function(t){this.$emit("update:isshow",t)}}},watch:{visible:function(){var t=this;if(this.visible)if("choosePerson"===this.type){this.$nextTick((function(){t.$refs.userListRef.clearSelection()})),this.currentPage=1,this.userGroup=[],this.userDepartment=[],this.personName="",this.personNo="";var e=JSON.parse(decodeURIComponent(Object(i["x"])("USERINFO"))).orgs;for(var r in e)this.organizationId=r;this.getUserList();var n=Object(c["a"])(this.personList);this.selectList=[],n.map((function(e){t.selectList.push({id:e})}))}else"editAttendanceGroup"===this.type?(this.dialogForm.groupName=this.selectInfo.name,this.dialogForm.groupRemark=this.selectInfo.remark):"addGroupAdministrators"===this.type?this.getAttendanceGroupList():"editGroupAdministrators"===this.type&&(this.getAttendanceGroupList(),this.dialogForm.administratorsName=this.selectInfo.name,this.dialogForm.administratorsPhone=this.selectInfo.phone,this.dialogForm.administratorsGroups=this.selectInfo.attendance_groups_ids,this.dialogForm.administratorsRemark=this.selectInfo.remark);else this.$refs.dialogFormRef.resetFields()}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){},clickConfirmHandle:function(){var t=this;this.$refs.dialogFormRef.validate((function(e){if(e){var r,n;switch(t.type){case"choosePerson":var o=[];t.selectList.map((function(t){o.push(t.id)})),r={id:t.selectInfo.id,card_users:o},n=t.$apis.apiBackgroundAttendanceAttendanceGroupModifyPost(r);break;case"addAttendanceGroup":r={name:t.dialogForm.groupName,remark:t.dialogForm.groupRemark},n=t.$apis.apiBackgroundAttendanceAttendanceGroupAddPost(r);break;case"editAttendanceGroup":r={id:t.selectInfo.id,name:t.dialogForm.groupName,remark:t.dialogForm.groupRemark},n=t.$apis.apiBackgroundAttendanceAttendanceGroupModifyPost(r);break;case"addGroupAdministrators":r={name:t.dialogForm.administratorsName,phone:t.dialogForm.administratorsPhone,attendance_groups:t.dialogForm.administratorsGroups,remark:t.dialogForm.administratorsRemark},n=t.$apis.apiBackgroundAttendanceAttendanceGroupAdminAddPost(r);break;case"editGroupAdministrators":r={id:t.selectInfo.id,name:t.dialogForm.administratorsName,phone:t.dialogForm.administratorsPhone,attendance_groups:t.dialogForm.administratorsGroups,remark:t.dialogForm.administratorsRemark},n=t.$apis.apiBackgroundAttendanceAttendanceGroupAdminModifyPost(r);break}t.confirmOperation(n)}}))},confirmOperation:function(t){var e=this;return v(h().mark((function r(){var n;return h().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!e.isLoading){r.next=2;break}return r.abrupt("return");case 2:return e.isLoading=!0,r.next=5,t;case 5:n=r.sent,e.isLoading=!1,0===n.code?(e.$message.success("成功"),e.$emit("confirm","search")):e.$message.error(n.msg);case 8:case"end":return r.stop()}}),r)})))()},clickCancleHandle:function(){this.visible=!1},handleClose:function(t){this.isLoading=!1,this.visible=!1,this.$refs.dialogFormRef.resetFields()},getAttendanceGroupList:function(){var t=this;return v(h().mark((function e(){var r;return h().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.$apis.apiBackgroundAttendanceAttendanceGroupListPost({page:1,page_size:99999});case 3:r=e.sent,t.isLoading=!1,0===r.code?t.attendanceGroupList=r.data.results:t.$message.error(r.msg);case 6:case"end":return e.stop()}}),e)})))()},searchUserList:Object(i["d"])((function(){this.currentPage=1,this.getUserList()}),300),getUserList:function(){var t=this;return v(h().mark((function e(){var r,n,o,i;return h().wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(o in t.isLoading=!0,r={card_user_group_ids:t.userGroup,card_department_group_ids:t.userDepartment,person_name:t.personName,person_no:t.personNo},n={},r)r[o]&&(n[o]=r[o]);return e.next=6,t.$apis.apiCardServiceCardUserListPost(p(p({},n),{},{page:t.currentPage,page_size:t.pageSize}));case 6:i=e.sent,t.isLoading=!1,0===i.code?(t.userList=i.data.results,t.totalCount=i.data.count,t.userList.map((function(e){t.personList.map((function(r){e.id===r&&t.$refs.userListRef.toggleRowSelection(e,!0)}))}))):t.$message.error(i.msg);case 9:case"end":return e.stop()}}),e)})))()},handleSizeChange:function(t){this.pageSize=t,this.getUserList()},handleCurrentChange:function(t){this.currentPage=t,this.getUserList()},handleSelection:function(t,e){var r=this.selectList.findIndex((function(t){return t.id===e.id}));-1===r?this.selectList.push(e):this.selectList.splice(r,1)},handleAllSelection:function(t){var e=this,r=Object(c["a"])(t),n=!0;this.userList.map((function(t){var e=r.findIndex((function(e){return e.id===t.id}));-1===e&&(n=!1)})),n?this.userList.map((function(t){var r=e.selectList.findIndex((function(e){return e.id===t.id}));-1===r&&e.selectList.push(t)})):this.userList.map((function(t){var r=e.selectList.findIndex((function(e){return e.id===t.id}));-1!==r&&e.selectList.splice(r,1)}))},getRowKey:function(t){return t.id}}},b=y,w=(r("40f3"),r("2877")),L=Object(w["a"])(b,n,o,!1,null,"48ac56a9",null);e["default"]=L.exports},bbd5:function(t,e,r){"use strict";function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}r.d(e,"a",(function(){return o}));var o=function t(e){if(!e&&"object"!==n(e))throw new Error("error arguments","deepClone");var r=e.constructor===Array?[]:{};return Object.keys(e).forEach((function(o){e[o]&&"object"===n(e[o])?r[o]=t(e[o]):r[o]=e[o]})),r}},f612:function(t,e,r){}}]);