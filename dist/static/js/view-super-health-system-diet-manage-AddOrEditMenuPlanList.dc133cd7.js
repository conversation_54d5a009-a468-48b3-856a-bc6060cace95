(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-diet-manage-AddOrEditMenuPlanList"],{"141c":function(t,e,r){},"7ecf":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"AddOrEditDietPlanList container-wrapper"},[e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v(t._s("add"===t.type?"新建":"编辑")+"食谱")])]),e("div",[e("el-form",{ref:"formRef",attrs:{model:t.formData,rules:t.formRuls,"label-width":"180px"}},[e("el-form-item",{staticClass:"endItem",attrs:{label:"食谱标题：",prop:"name"}},[e("el-input",{staticClass:"ps-input",staticStyle:{"max-width":"400px"},attrs:{type:"textarea",maxlength:"20",placeholder:"不超过20个字","show-word-limit":"",autosize:{minRows:4,maxRows:8}},model:{value:t.formData.name,callback:function(e){t.$set(t.formData,"name",e)},expression:"formData.name"}})],1),e("el-form-item",{staticClass:"endItem",attrs:{label:"食谱描述：",prop:"detail"}},[e("el-input",{staticClass:"ps-input",staticStyle:{"max-width":"400px"},attrs:{type:"textarea",maxlength:"200",placeholder:"不超过200个字","show-word-limit":"",autosize:{minRows:4,maxRows:8}},model:{value:t.formData.detail,callback:function(e){t.$set(t.formData,"detail",e)},expression:"formData.detail"}})],1),e("el-form-item",{staticClass:"upload-block-label",attrs:{label:"食谱图片：",prop:"image"}},[e("file-upload",{directives:[{name:"loading",rawName:"v-loading",value:t.isImageLoading,expression:"isImageLoading"}],ref:"uploadImg",staticClass:"uploadImg",attrs:{limit:1,"before-upload":t.beforeUpload,"show-file-list":!1,"on-remove":t.remove,prefix:"dietPlan"},on:{fileLists:t.getSuccessUploadRes}},[t.formData.image?e("el-image",{staticClass:"avatar",attrs:{src:t.formData.image},on:{click:t.clearFileHandle}}):e("div",{staticClass:"upload-t inline-block upload-w"},[e("i",{staticClass:"el-icon-plus avatar-uploader-icon"})]),e("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v("只能上传图片")])],1),e("div",[t._v("建议上传 590 * 280 的格式")])],1),e("el-form-item",{staticClass:"endItem",attrs:{label:"体重目标：",prop:"healthy_target"}},[e("el-select",{staticClass:"ps-select ps-select-diet m-l-5",attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select"},model:{value:t.formData.healthy_target,callback:function(e){t.$set(t.formData,"healthy_target",e)},expression:"formData.healthy_target"}},t._l(t.targetList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),e("el-form-item",{staticClass:"endItem",attrs:{label:"运动强度：",prop:"sport_vol"}},[e("el-select",{staticClass:"ps-select ps-select-diet m-l-5",attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select"},model:{value:t.formData.sport_vol,callback:function(e){t.$set(t.formData,"sport_vol",e)},expression:"formData.sport_vol"}},t._l(t.motionList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),e("el-form-item",{attrs:{label:"食谱："}},t._l(t.menuData,(function(r,n){return e("div",{key:n,staticClass:"m-b-20"},[e("div",{staticClass:"ps-flex"},[e("div",{staticClass:"food-diet-box m-r-20",staticStyle:{"background-color":"#d7d7d7ae"}},[e("div",{staticClass:"p-l-20"},[t._v("第"+t._s(n+1)+"天")]),e("div",{staticClass:"cantent"},[t._v("营养合计")])]),t.menuData.length>1?e("el-button",{staticClass:"ps-red",attrs:{type:"text"},on:{click:function(e){return t.clickDelcookbook(n)}}},[t._v(" 删除 ")]):t._e()],1),t._l(r,(function(r,a,o){return e("div",{key:o,staticClass:"food-diet-box"},[e("div",{staticClass:"text",on:{click:function(e){return t.clickNutrition(r,a,n)}}},[t._v(" "+t._s(t.mealName(a))+" ")]),e("div",{staticClass:"cantent"},[t._v(" 热量"+t._s(r.total_nutrition.energy_kcal?r.total_nutrition.energy_kcal:0)+"kcal，碳水化合物"+t._s(r.total_nutrition.carbohydrate?r.total_nutrition.carbohydrate:0)+"g，蛋白质"+t._s(r.total_nutrition.protein?r.total_nutrition.protein:0)+"g，脂肪"+t._s(r.total_nutrition.axunge?r.total_nutrition.axunge:0)+"g ")])])}))],2)})),0),e("el-form-item",[e("el-button",{staticClass:"ps-origin-btn w-150",attrs:{size:"small",type:"primary",icon:"el-icon-plus",disabled:!(t.menuData.length<30)},on:{click:t.clickDay}},[t._v(" 添加天数 ")])],1),e("el-form-item",[e("el-button",{staticClass:"ps-origin-btn w-150",attrs:{size:"small",type:"primary"},on:{click:t.submitHandler}},[t._v(" 保存 ")])],1)],1)],1)]),t.dialogVisible?e("meal-nutrition-dialog",{attrs:{isshow:t.dialogVisible,title:"早餐","select-info":t.mealNutritionDialogInfo},on:{"update:isshow":function(e){t.dialogVisible=e},confirmHandle:t.clickConfirmHandle}}):t._e()],1)},a=[],o=r("d0a8"),i=r("dfd5"),s=r("ed08");function l(t){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},l(t)}function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function d(t,e,r,n){var o=e&&e.prototype instanceof b?e:b,i=Object.create(o.prototype),s=new I(n||[]);return a(i,"_invoke",{value:P(t,r,s)}),i}function m(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=d;var p="suspendedStart",h="suspendedYield",g="executing",y="completed",v={};function b(){}function _(){}function w(){}var x={};f(x,i,(function(){return this}));var k=Object.getPrototypeOf,D=k&&k(k($([])));D&&D!==r&&n.call(D,i)&&(x=D);var L=w.prototype=b.prototype=Object.create(x);function O(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function j(t,e){function r(a,o,i,s){var c=m(t[a],t,o);if("throw"!==c.type){var u=c.arg,f=u.value;return f&&"object"==l(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,i,s)}),(function(t){r("throw",t,i,s)})):e.resolve(f).then((function(t){u.value=t,i(u)}),(function(t){return r("throw",t,i,s)}))}s(c.arg)}var o;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return o=o?o.then(a,a):a()}})}function P(e,r,n){var a=p;return function(o,i){if(a===g)throw Error("Generator is already running");if(a===y){if("throw"===o)throw i;return{value:t,done:!0}}for(n.method=o,n.arg=i;;){var s=n.delegate;if(s){var l=C(s,n);if(l){if(l===v)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===p)throw a=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=g;var c=m(e,r,n);if("normal"===c.type){if(a=n.done?y:h,c.arg===v)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(a=y,n.method="throw",n.arg=c.arg)}}}function C(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,C(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var o=m(a,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,v;var i=o.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function S(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function $(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,o=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(l(e)+" is not iterable")}return _.prototype=w,a(L,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:_,configurable:!0}),_.displayName=f(w,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===_||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,f(t,u,"GeneratorFunction")),t.prototype=Object.create(L),t},e.awrap=function(t){return{__await:t}},O(j.prototype),f(j.prototype,s,(function(){return this})),e.AsyncIterator=j,e.async=function(t,r,n,a,o){void 0===o&&(o=Promise);var i=new j(d(t,r,n,a),o);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},O(L),f(L,u,"Generator"),f(L,i,(function(){return this})),f(L,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=$,I.prototype={constructor:I,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(S),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return s.type="throw",s.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],s=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),S(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;S(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:$(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function u(t,e){return h(t)||p(t,e)||d(t,e)||f()}function f(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(t,e){if(t){if("string"==typeof t)return m(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?m(t,e):void 0}}function m(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function p(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,o,i,s=[],l=!0,c=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=o.call(r)).done)&&(s.push(n.value),s.length!==e);l=!0);}catch(t){c=!0,a=t}finally{try{if(!l&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(c)throw a}}return s}}function h(t){if(Array.isArray(t))return t}function g(t,e,r,n,a,o,i){try{var s=t[o](i),l=s.value}catch(t){return void r(t)}s.done?e(l):Promise.resolve(l).then(n,a)}function y(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var o=t.apply(e,r);function i(t){g(o,n,a,i,s,"next",t)}function s(t){g(o,n,a,i,s,"throw",t)}i(void 0)}))}}function v(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function b(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?v(Object(r),!0).forEach((function(e){_(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function _(t,e,r){return(e=w(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function w(t){var e=x(t,"string");return"symbol"==l(e)?e:e+""}function x(t,e){if("object"!=l(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=l(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var k={name:"AddOrEditDietPlanList",components:{MealNutritionDialog:o["default"]},props:{},data:function(){return{isImageLoading:!1,isLoading:!1,type:"",formData:{name:"",detail:"",image:"",healthy_target:"",sport_vol:""},formRuls:{name:[{required:!0,message:"请输入食谱标题",trigger:"blur"}],image:[{required:!0,message:"请上传图片",trigger:["change","blur"]}]},targetList:[{name:"增重",id:"gain"},{name:"减重",id:"lose"},{name:"保持体重",id:"keep"}],motionList:[{name:"几乎不运动",id:"lv0"},{name:"稍微运动",id:"lv1"},{name:"中度运动",id:"lv2"},{name:"积极极动",id:"lv3"},{name:"专业运动",id:"lv4"}],menuData:[{breakfast:{total_nutrition:{axunge:0,protein:0,carbohydrate:0,energy_kcal:0},main_food:[],sec_food:[]},lunch:{total_nutrition:{axunge:0,protein:0,carbohydrate:0,energy_kcal:0},main_food:[],sec_food:[]},dinner:{total_nutrition:{axunge:0,protein:0,carbohydrate:0,energy_kcal:0},main_food:[],sec_food:[]}}],dialogVisible:!1,mealNutritionDialogInfo:{}}},created:function(){var t=Object(s["x"])("allFoodList");JSON.parse(t)&&JSON.parse(t).length||this.getFoodlist(),this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.$route.params.type&&(this.type=this.$route.params.type),"modify"===this.type&&this.getPlanDetails()},mealName:function(t){var e="";switch(t){case"breakfast":e="早餐";break;case"lunch":e="午餐";break;case"dinner":e="晚餐";break;default:break}return e},remove:function(){this.formData.image=""},getSuccessUploadRes:function(t){t.length&&(this.formData.image=t[0].url),this.isImageLoading=!1},clearFileHandle:function(){this.$refs.uploadImg.clearHandle()},beforeUpload:function(t){var e=[".jpg",".png"];if(!e.includes(this.getSuffix(t.name)))return this.$message.error("上传图片只能是 jpg/png 格式"),!1;var r=t.size/1024/1024<15;return r?void 0:(this.$message.error("上传图片大小不能超过 15MB!"),!1)},getSuffix:function(t){var e=t.lastIndexOf("."),r="";return-1!==e&&(r=t.substring(e)),r},clickDay:function(){this.menuData.push({breakfast:{total_nutrition:{axunge:0,protein:0,carbohydrate:0,energy_kcal:0},main_food:[],sec_food:[]},lunch:{total_nutrition:{axunge:0,protein:0,carbohydrate:0,energy_kcal:0},main_food:[],sec_food:[]},dinner:{total_nutrition:{axunge:0,protein:0,carbohydrate:0,energy_kcal:0},main_food:[],sec_food:[]}})},clickDelcookbook:function(t){this.menuData.splice(t,1)},clickNutrition:function(t,e,r){this.mealNutritionDialogInfo=b({key:e,menuIndex:r},t),this.dialogVisible=!0},clickConfirmHandle:function(t){this.menuData[t.menuIndex][t.key].total_nutrition=t.total_nutrition,this.menuData[t.menuIndex][t.key].main_food=t.main_food,this.menuData[t.menuIndex][t.key].sec_food=t.sec_food},submitHandler:function(){var t=this;this.$refs.formRef.validate((function(e){if(e){if(t.isLoading)return t.$message.error("请勿重复提交！");var r={name:t.formData.name,detail:t.formData.detail,image:t.formData.image,menu_data:[]};t.formData.sport_vol&&(r.sport_vol=t.formData.sport_vol),t.formData.healthy_target&&(r.healthy_target=t.formData.healthy_target),t.menuData.forEach((function(t,e){var n={days:e+1,breakfast:{main_food:[],sec_food:[]},lunch:{main_food:[],sec_food:[]},dinner:{main_food:[],sec_food:[]}};for(var a in n)t[a]&&(n[a].main_food=t[a].main_food,n[a].sec_food=t[a].sec_food);r.menu_data.push(n)})),t.addModifyMenuPlan(r)}}))},addModifyMenuPlan:function(t){var e=this;return y(c().mark((function r(){var n,a,o,i,l,f,d,m;return c().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(e.isLoading=!0,n="",a=u(n,2),o=a[0],i=a[1],"add"!==e.type){r.next=12;break}return r.next=6,Object(s["Z"])(e.$apis.apiBackgroundHealthyMenuPlanAddPost(t));case 6:l=r.sent,f=u(l,2),o=f[0],i=f[1],r.next=19;break;case 12:return r.next=15,Object(s["Z"])(e.$apis.apiBackgroundHealthyMenuPlanModifyPost(b({id:e.$route.query.id},t)));case 15:d=r.sent,m=u(d,2),o=m[0],i=m[1];case 19:if(e.isLoading=!1,!o){r.next=23;break}return e.$message.error(o.message),r.abrupt("return");case 23:0===i.code?(e.$message.success(i.msg),e.$closeCurrentTab(e.$route.path)):e.$message.error(i.msg);case 24:case"end":return r.stop()}}),r)})))()},getPlanDetails:function(){var t=this;return y(c().mark((function e(){var r,n,a,o,i;return c().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(s["Z"])(t.$apis.apiBackgroundHealthyMenuPlanDetailsPost({id:t.$route.query.id}));case 3:if(r=e.sent,n=u(r,2),a=n[0],o=n[1],t.isLoading=!1,!a){e.next=11;break}return t.$message.error(a.message),e.abrupt("return");case 11:0===o.code?(i=o.data,t.formData.name=i.name,t.formData.detail=i.detail,t.formData.image=i.image,t.formData.healthy_target=i.healthy_target,t.formData.sport_vol=i.sport_vol,i.menu_data.length&&(t.menuData=[],i.menu_data.forEach((function(e){var r={breakfast:{},lunch:{},dinner:{}};for(var n in e)r[n]&&(r[n]=e[n]);t.menuData.push(r)})))):t.$message.error(o.msg);case 12:case"end":return e.stop()}}),e)})))()},getFoodlist:i["getFoodlist"]}},D=k,L=(r("9a84"),r("2877")),O=Object(L["a"])(D,n,a,!1,null,"89a4fd52",null);e["default"]=O.exports},"9a84":function(t,e,r){"use strict";r("141c")}}]);