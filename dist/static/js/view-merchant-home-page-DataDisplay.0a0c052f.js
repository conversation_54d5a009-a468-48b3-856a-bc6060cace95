(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-home-page-DataDisplay","view-merchant-home-page-components-FoodCategory","view-merchant-home-page-constants"],{"00b3":function(t,e,r){},4960:function(t,e,r){},"894e":function(t,e,r){"use strict";r("4960")},"9f0d":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"data-display"},[e("div",{staticClass:"money_type flex"},t._l(t.businessSummery,(function(r){return e("div",{key:r.key,staticClass:"money_type_item",class:r.class},[e("div",{staticClass:"title"},[t._v(t._s(r.label))]),e("div",{staticClass:"money"},[e("span",[t._v(t._s(t._f("formatPriceTo3")(r.value)))])])])})),0),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isBusinessLoading,expression:"isBusinessLoading"}],staticClass:"line-trend"},[t._m(0),e("div",{staticClass:"nav_cen flex"},t._l(t.businessSummery,(function(r){return e("div",{key:r.key,staticClass:"item",class:t.selectTrend.key===r.key?"nav_active":"",on:{click:function(e){return t.changetrendHandle(r)}}},[t._v(" "+t._s(r.label)+" ")])})),0),e("div",{ref:"line_chart",attrs:{id:"line_chart"}})]),e("div",{staticClass:"annular-chart flex"},[e("div",{staticClass:"meal_type meal_item"},[e("div",{staticClass:"meal_type_header"},[e("div",{staticClass:"title"},[t._v("餐段统计")]),e("el-radio-group",{staticClass:"ps-radio-btn",attrs:{size:"mini",prop:"couponType"},on:{change:t.mealStatisticChange},model:{value:t.mealStatisticType,callback:function(e){t.mealStatisticType=e},expression:"mealStatisticType"}},[e("el-radio-button",{attrs:{label:"count"}},[t._v("按笔数")]),e("el-radio-button",{attrs:{label:"user"}},[t._v("按人数")]),e("el-radio-button",{attrs:{label:"passenger"}},[t._v("按客流")])],1)],1),e("div",{ref:"circular_chart",attrs:{id:"circular_chart"}})]),e("div",{staticClass:"eat_type meal_item"},[e("div",{staticClass:"title"},[t._v("取餐方式统计")]),e("div",{ref:"annular_chart",attrs:{id:"annular_chart"}})]),e("div",{staticClass:"meal_consumption meal_item"},[e("div",{staticClass:"title"},[t._v("餐段消费情况")]),e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.mealConsumeData,"cell-style":t.CellStyle,"header-cell-style":t.HeaderCell}},t._l(t.mealConsumeColumns,(function(r){return e("el-table-column",{key:r.key,attrs:{prop:r.key,label:r.label,width:r.width},scopedSlots:t._u([{key:"default",fn:function(n){return["turnover"===r.key?e("span",[t._v(" ￥"+t._s(t._f("formatPriceTo3")(t._f("formatMoney")(n.row[r.key])))+" ")]):"percent"===r.key?e("span",[e("el-progress",{attrs:{"stroke-width":5,percentage:n.row[r.key],color:"#FF9144","show-text":!1}})],1):e("span",[e("span",[t._v(t._s(n.row[r.key]))])])]}}],null,!0)})})),1)],1),e("div",{staticClass:"food_TOP ranking_item"},[e("div",{staticClass:"top flex",staticStyle:{"justify-content":"space-between"}},[e("div",{staticClass:"title"},[t._v("菜品销量排行TOP10")]),e("div",{staticClass:"nav"},[e("el-button",{class:"fee"===t.foodSalesRankingType?"top_nav":"",attrs:{size:"mini"},on:{click:function(e){return t.changeFoodSalesRankingType("fee")}}},[t._v(" 销额 ")]),e("el-button",{class:"count"===t.foodSalesRankingType?"top_nav":"",attrs:{size:"mini"},on:{click:function(e){return t.changeFoodSalesRankingType("count")}}},[t._v(" 销量 ")])],1)]),e("div",{staticClass:"m-t-10",staticStyle:{width:"200px"}},[e("food-category",{on:{input:t.changeCategory}})],1),t._l(t.foodSalesRankingData,(function(r,n){return e("div",{key:n,staticClass:"TOP_item flex"},[e("div",{staticClass:"left flex"},[e("div",{staticClass:"count",class:t.foodTOPThreeClass(n+1)},[t._v(t._s(n+1))]),e("div",{staticClass:"food"},[t._v(t._s(r.name))])]),"fee"===t.foodSalesRankingType?e("div",{staticClass:"right"},[t._v(" ￥"+t._s(t._f("formatPriceTo3")(t._f("formatMoney")(r.total_fee)))+" ")]):e("div",{staticClass:"right"},[t._v(t._s(t._f("formatPriceTo3")(r.food_count))+"笔")])])})),t.foodSalesRankingData.length?t._e():e("div",{staticClass:"flex flex-center empty-text"},[t._v("暂无更多")])],2),e("div",{staticClass:"pay_TOP ranking_item"},[e("div",{staticClass:"title"},[t._v("支付方式排行")]),e("div",{staticClass:"scroll-box"},[t._l(t.payTypeData,(function(r,n){return e("div",{key:n,staticClass:"TOP_item"},[e("div",{staticClass:"top flex"},[e("div",{staticClass:"pay_type"},[t._v(t._s(r.st_payway))]),e("div",{staticClass:"right flex"},[e("div",{staticClass:"count"},[t._v(t._s(t._f("formatPriceTo3")(r.payway_count))+"笔")]),e("div",{staticClass:"pay_fee"},[t._v("￥"+t._s(t._f("formatPriceTo3")(t._f("formatMoney")(r.total_payfee))))])])]),e("el-progress",{attrs:{"stroke-width":9,percentage:r.percent,color:t.payColor(n),"show-text":!1}})],1)})),t.payTypeData.length?t._e():e("div",{staticClass:"flex flex-center empty-text"},[t._v("暂无更多")])],2)]),e("div",{staticClass:"shebei_TOP ranking_item"},[e("div",{staticClass:"title"},[t._v("设备消费统计")]),e("div",{staticClass:"scroll-box"},[t._l(t.devicePayData,(function(r,n){return e("div",{key:n,staticClass:"TOP_item"},[e("div",{staticClass:"left flex"},[e("el-progress",{staticStyle:{"font-weight":"bold"},attrs:{type:"circle",percentage:r.percent,"stroke-width":4,width:50,height:50,color:"#FF9144",format:t.formatDevice(n+1)}}),e("div",{staticClass:"type"},[e("div",{staticClass:"name"},[t._v(t._s(r.device_name))]),e("div",{staticClass:"number flex"},[e("div",{staticClass:"count"},[t._v(t._s(t._f("formatPriceTo3")(r.paydevice_count))+"笔")]),e("div",{staticClass:"fee"},[t._v("￥"+t._s(t._f("formatPriceTo3")(t._f("formatMoney")(r.total_payfee))))])])])],1)])})),t.devicePayData.length?t._e():e("div",{staticClass:"flex flex-center empty-text"},[t._v("暂无更多")])],2)])]),t.showBarChart?e("div",{staticClass:"bar-chart"},[t._m(1),e("div",{ref:"bar_chart",attrs:{id:"bar_chart"}})]):t._e()])},a=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"top flex"},[e("div",{staticClass:"title"},[t._v("经营趋势")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"top flex"},[e("div",{staticClass:"title"},[t._v("营业额统计")])])}],i=r("5a0c"),o=r("ed08"),s=r("ca26"),c=r("da92"),l=r("d4e4");function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function h(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach((function(e){d(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function d(t,e,r){return(e=p(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function p(t){var e=y(t,"string");return"symbol"==u(e)?e:e+""}function y(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function v(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */v=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var i=e&&e.prototype instanceof g?e:g,o=Object.create(i.prototype),s=new F(n||[]);return a(o,"_invoke",{value:L(t,r,s)}),o}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var d="suspendedStart",p="suspendedYield",y="executing",m="completed",_={};function g(){}function b(){}function C(){}var x={};l(x,o,(function(){return this}));var T=Object.getPrototypeOf,w=T&&T(T(j([])));w&&w!==r&&n.call(w,o)&&(x=w);var k=C.prototype=g.prototype=Object.create(x);function S(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function D(t,e){function r(a,i,o,s){var c=h(t[a],t,i);if("throw"!==c.type){var l=c.arg,f=l.value;return f&&"object"==u(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,o,s)}),(function(t){r("throw",t,o,s)})):e.resolve(f).then((function(t){l.value=t,o(l)}),(function(t){return r("throw",t,o,s)}))}s(c.arg)}var i;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return i=i?i.then(a,a):a()}})}function L(e,r,n){var a=d;return function(i,o){if(a===y)throw Error("Generator is already running");if(a===m){if("throw"===i)throw o;return{value:t,done:!0}}for(n.method=i,n.arg=o;;){var s=n.delegate;if(s){var c=O(s,n);if(c){if(c===_)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===d)throw a=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=y;var l=h(e,r,n);if("normal"===l.type){if(a=n.done?m:p,l.arg===_)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(a=m,n.method="throw",n.arg=l.arg)}}}function O(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,O(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),_;var i=h(a,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,_;var o=i.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,_):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,_)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function P(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function F(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function j(e){if(e||""===e){var r=e[o];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,i=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(u(e)+" is not iterable")}return b.prototype=C,a(k,"constructor",{value:C,configurable:!0}),a(C,"constructor",{value:b,configurable:!0}),b.displayName=l(C,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,C):(t.__proto__=C,l(t,c,"GeneratorFunction")),t.prototype=Object.create(k),t},e.awrap=function(t){return{__await:t}},S(D.prototype),l(D.prototype,s,(function(){return this})),e.AsyncIterator=D,e.async=function(t,r,n,a,i){void 0===i&&(i=Promise);var o=new D(f(t,r,n,a),i);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},S(k),l(k,c,"Generator"),l(k,o,(function(){return this})),l(k,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=j,F.prototype={constructor:F,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(P),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return s.type="throw",s.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var c=n.call(o,"catchLoc"),l=n.call(o,"finallyLoc");if(c&&l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,_):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),_},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),P(r),_}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;P(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:j(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),_}},e}function m(t,e){return x(t)||C(t,e)||g(t,e)||_()}function _(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function g(t,e){if(t){if("string"==typeof t)return b(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?b(t,e):void 0}}function b(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function C(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,i,o,s=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(s.push(n.value),s.length!==e);c=!0);}catch(t){l=!0,a=t}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw a}}return s}}function x(t){if(Array.isArray(t))return t}function T(t,e,r,n,a,i,o){try{var s=t[i](o),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,a)}function w(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var i=t.apply(e,r);function o(t){T(i,n,a,o,s,"next",t)}function s(t){T(i,n,a,o,s,"throw",t)}o(void 0)}))}}var k={name:"DataDisplay",components:{FoodCategory:l["default"]},props:{change_time:Array,orgid:[Number,String,Array],chartdata:{type:Object,default:function(){}}},data:function(){return{queryDateList:[],isBusinessLoading:!1,businessSummery:[{label:"营业额",value:0,key:"turnover",class:"turnover_fee",type:"money"},{label:"实收金额",value:0,key:"real_income",class:"origin_fee",type:"money"},{label:"消费订单笔数",value:0,key:"consume_order_count",class:"consumption_count"},{label:"充值金额",value:0,key:"total_charge",class:"recharge_fee",type:"money"},{label:"消费退款金额",value:0,key:"total_consume_refund",class:"refund_fee",type:"money"}],selectTrend:{},mealConsumeData:[],mealConsumeColumns:s["MEALCONSUME_COLUMS"],foodSalesRankingType:"fee",foodSalesRankingData:[],payTypeData:[],devicePayData:[],tableData:[],Top_change:"xiaoE",TopFood_arr:[],TopPay_arr:[],shebei_arr:[],LineChartObj:{},lineChart:null,pieMealTimeChart:null,mealStatisticType:"count",pieMealWayChart:null,barTurnoverChart:null,showBarChart:!1,secondCategories:[]}},computed:{diffDay:function(){return this.getDiffDay(this.change_time[1],this.change_time[0])+1}},watch:{chartdata:{deep:!0,handler:function(t){this.setSummaryData(t.summary),"count"===this.mealStatisticType?this.initMealTimeDataPie(t.meal_time_statistic,"笔","订单总笔数"):"passenger"===this.mealStatisticType?this.initMealTimeDataPie(t.meal_time_statistic_for_flow,"笔","客流总人数"):this.initMealTimeDataPie(t.meal_time_statistic_for_user,"人","下单总人数"),this.initMealPayDataPie(t.take_meal_type_st),this.initMealConsume(t.meal_consume_statistic),this.initFoodSalesRanking(t),this.initPayTypeData(t.payinfo_st),this.initDevicePayData(t.device_payinfo_st),this.initTurnoverData(t.turnover_statistic)}},orgid:function(t){},change_time:function(t){this.queryDateList=this.setDateList(),this.getBusinessTrendList(this.secondCategories)}},beforeDestroy:function(){window.removeEventListener("resize",this.resizeChartHandle)},mounted:function(){this.selectTrend=this.businessSummery[0],window.addEventListener("resize",this.resizeChartHandle)},methods:{getDiffDay:function(t,e){return i(t).diff(e,"day")},setSunmaryHandle:function(t){var e={};t.forEach((function(t){Object.keys(t).forEach((function(r){"st_date"!==r&&(void 0===e[r]?e[r]=t[r]?t[r]:0:e[r]+=t[r])}))}))},setSummaryData:function(t){var e=this;this.businessSummery.forEach((function(r){"consume_order_count"===r.key?e.setDataValue(r,"value",t[r.key]):e.setDataValue(r,"value",Object(o["i"])(t[r.key]))}))},setDataValue:function(t,e,r){this.$set(t,e,r)},setDateList:function(){var t=[],e=this.change_time[0];if(this.diffDay>0)for(var r=0;r<this.diffDay;r++){var n=i(e).add(r,"day").format("YYYY-MM-DD");t.push(n)}else t=[e];return t},getBusinessTrendList:function(t){var e=this;return w(v().mark((function r(){var n,a,i,s;return v().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return e.isBusinessLoading=!0,r.next=3,Object(o["Z"])(e.$apis.apiBackgroundHomepageDataGetBusinessTrendDataPost({start_date:e.change_time[0],end_date:e.change_time[1],query_type:e.selectTrend.key,org_id:e.orgid,second_categories:t}));case 3:if(n=r.sent,a=m(n,2),i=a[0],s=a[1],e.isBusinessLoading=!1,!i){r.next=11;break}return e.$message.error(i.message),r.abrupt("return");case 11:0===s.code?e.initTurnoverTrendLine(s.data):e.$message.error(s.msg);case 12:case"end":return r.stop()}}),r)})))()},initTurnoverTrendLine:function(t){var e=this,r=s["TREND_SETTING"];r.xAxis.data=this.setDateList(),r.tooltip.formatter='<div style="padding:5px;font-size:16px;font-weight: 540;">{b0}<br /><span style=" color: #666;display:inline-block;margin: 5px 20px 0 0;">'.concat(this.selectTrend.label,"</span>").concat("money"===this.selectTrend.type?"￥":"","{c0}</div>"),r.series[0].showSymbol=this.diffDay<10;var n=this.queryDateList.map((function(r){var n=0;return t.forEach((function(t){r===t.st_date&&t[e.selectTrend.key]&&(n="consume_order_count"===e.selectTrend.key?t[e.selectTrend.key]:Object(o["i"])(t[e.selectTrend.key]))})),{value:n,date:r}}));r.series[0].data=n,this.lineChart||(this.lineChart=this.$echarts.init(this.$refs.line_chart)),this.lineChart&&this.lineChart.setOption(r)},updateTurnoverTrend:function(t){this.initTurnoverTrendLine()},changetrendHandle:function(t){this.selectTrend=t,this.getBusinessTrendList(this.secondCategories)},mealStatisticChange:function(){"count"===this.mealStatisticType?this.initMealTimeDataPie(this.chartdata.meal_time_statistic,"笔","订单总笔数"):"passenger"===this.mealStatisticType?this.initMealTimeDataPie(this.chartdata.meal_time_statistic_for_flow,"笔","客流总人数"):this.initMealTimeDataPie(this.chartdata.meal_time_statistic_for_user,"人","下单总人数")},initMealTimeDataPie:function(t,e,r){var n=[{value:0,name:"早餐",key:"breakfast_count"},{value:0,name:"午餐",key:"lunch_count"},{value:0,name:"下午茶",key:"afternoon_count"},{value:0,name:"晚餐",key:"dinner_count"},{value:0,name:"宵夜",key:"supper_count"},{value:0,name:"凌晨餐",key:"morning_count"}],a={};n.forEach((function(e){e.value=t[e.key]?t[e.key]:0,a[e.name]=e.key}));var i=s["MEALTIME_SETTING"];i.legend.formatter=function(r){var n=t[a[r]];return r+"    "+(n||0)+e},i.title.subtext=r,i.series[0].data=n,i.title.text=r&&"客流总人数"===r?t.all_count||0:n.reduce((function(t,e){return t+e.value}),0),this.pieMealTimeChart||(this.pieMealTimeChart=this.$echarts.init(this.$refs.circular_chart)),this.pieMealTimeChart.setOption(i)},initMealPayDataPie:function(t){var e=[{value:0,name:"线下堂食",key:"instore_on_scene_count"},{value:0,name:"预约堂食",key:"online_on_scene_count"},{value:0,name:"外卖配送",key:"waimai_count"},{value:0,name:"食堂自提",key:"bale_count"},{value:0,name:"取餐柜自提",key:"cupboard_count"}],r={};e.forEach((function(e){e.value=t[e.key]?t[e.key]:0,r[e.name]=e.key}));var n=s["MEALWAY_SETTING"];n.legend.formatter=function(e){var n=t[r[e]];return e+"    "+(n||0)+"笔"},n.series[0].data=e,this.pieMealWayChart||(this.pieMealWayChart=this.$echarts.init(this.$refs.annular_chart)),this.pieMealWayChart.setOption(n)},initMealConsume:function(t){var e=this,r=0,n=[],a=[{id:1,meal_type:"早餐",turnover:0,key:"breakfast",percent:0},{id:2,meal_type:"午餐",turnover:0,key:"lunch",percent:0},{id:3,meal_type:"下午茶",turnover:0,key:"afternoon",percent:0},{id:4,meal_type:"晚餐",turnover:0,key:"dinner",percent:0},{id:5,meal_type:"宵夜",turnover:0,key:"supper",percent:0},{id:6,meal_type:"凌晨餐",turnover:0,key:"morning",percent:0}],i=function(e){if(Object.hasOwnProperty.call(t,e)){var i=t[e];a.forEach((function(r){r.key===e&&(r.turnover=t[e],n.push(r))})),i&&(r+=i)}};for(var o in t)i(o);var s=n.length-1,l=0;this.sortData(n,"turnover"),n.length>1?n.forEach((function(t,n){n<s?(t.percent=e.computedPercent(t.turnover,r),l=c["a"].plus(l,t.percent)):t.percent=l?c["a"].minus(100,l):0})):1===n.length&&(n[0].percent=100),this.mealConsumeData=this.sortData(n,"id","up")},initFoodSalesRanking:function(t){var e=[],r=[];e="fee"===this.foodSalesRankingType?t.food_sales_amount_ranking:t.food_sales_number_ranking,e&&(r=Object(o["f"])(e)),this.foodSalesRankingType,this.sortData(r,"total_fee"),this.foodSalesRankingData=r},changeFoodSalesRankingType:function(t){this.foodSalesRankingType=t,this.initFoodSalesRanking(this.chartdata)},initPayTypeData:function(t){var e=this,r=[];t&&(r=Object(o["f"])(t));var n=r.reduce((function(t,e){return t+e.total_payfee}),0),a=r.length-1,i=0;this.sortData(r,"total_payfee");var s=r.map((function(t,r){return a?(r<a?(t.percent=e.computedPercent(t.total_payfee,n),i=c["a"].plus(i,t.percent)):t.percent=i?c["a"].minus(100,i):0,t):(t.percent=100,t)}));this.payTypeData=this.sortData(s,"total_payfee")},initDevicePayData:function(t){var e=this,r=[];t&&(r=Object(o["f"])(t));var n=r.reduce((function(t,e){return t+e.total_payfee}),0),a=r.length-1,i=0;this.sortData(r,"total_payfee");var s=r.map((function(t,r){return a?(r<a?(t.percent=e.computedPercent(t.total_payfee,n),i=c["a"].plus(i,t.percent)):t.percent=i?c["a"].minus(100,i):0,t):(t.percent=100,t)}));this.devicePayData=this.sortData(s,"total_payfee")},initTurnoverData:function(t){var e=this,r=[];t&&(r=Object(o["f"])(t));var n=s["TURNOVER_SETTING"],a=[],i=[];this.showBarChart=t.length&&t.length>0,this.showBarChart&&(r.forEach((function(t){a.push(t.name),i.push(h(h({},t),{},{value:Object(o["i"])(t.total_payfee)}))})),n.xAxis.data=a,n.series[0].data=i,a.length||(n.xAxis.axisLine.show=!0),this.$nextTick((function(){e.barTurnoverChart=e.$echarts.init(e.$refs.bar_chart),e.barTurnoverChart&&e.barTurnoverChart.setOption(n)})))},computedPercent:function(t,e){return e?parseInt(Number(t)/e*100):0},sortData:function(t,e,r){var n=t;return n.sort((function(t,n){return"up"===r?t[e]-n[e]:n[e]-t[e]})),n},CellStyle:function(t){t.rowIndex;var e=window.innerWidth;return e<=1350?"padding: 14px 0 14px 10px; color: #000; fontSize:12px;":"padding: 14px 0 14px 10px; color: #000; fontSize:16px;"},HeaderCell:function(t){var e=t.columnIndex;return 2===e?" text-align: center;":"padding: 14px 0 14px 10px"},moneyTypeClass:function(t){return 1===t?"turnover_fee":2===t?"origin_fee":3===t?"consumption_count":4===t?"recharge_fee":5===t?"refund_fee":void 0},foodTOPThreeClass:function(t){return 1===t?"No1":2===t?"No2":3===t?"No3":void 0},payColor:function(t){return 1===t?"#03CDFE":2===t?"#7E86FF":3===t?"#05DACB":4===t?"#49D96C":5===t?"#569BFE":6===t?"#FFB72F":void 0},formatDevice:function(t){return function(){return t}},resizeChartHandle:Object(o["d"])((function(){this.lineChart&&this.lineChart.resize(),this.pieMealTimeChart&&this.pieMealTimeChart.resize(),this.pieMealWayChart&&this.pieMealWayChart.resize(),this.barTurnoverChart&&this.barTurnoverChart.resize()}),300),changeCategory:function(t){this.secondCategories=t,this.$emit("udpcategories",t)}}},S=k,D=(r("f673"),r("fcc6"),r("2877")),L=Object(D["a"])(S,n,a,!1,null,"3bd974c4",null);e["default"]=L.exports},ca26:function(t,e,r){"use strict";function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}r.r(e),r.d(e,"TREND_SETTING",(function(){return a})),r.d(e,"MEALTIME_SETTING",(function(){return i})),r.d(e,"MEALWAY_SETTING",(function(){return o})),r.d(e,"MEALCONSUME_COLUMS",(function(){return s})),r.d(e,"TURNOVER_SETTING",(function(){return l}));var a={xAxis:{type:"category",data:[],axisLine:{show:!1},axisTick:{show:!1},splitLine:{show:!1},axisLabel:{textStyle:{fontSize:14,color:"#666",lineHeight:50}}},yAxis:{type:"value",axisTick:{show:!1},axisLine:{show:!1},splitLine:{show:!0,lineStyle:{type:"dashed",color:"#dfe5ec"}},splitNumber:4,axisLabel:{textStyle:{fontSize:16,color:"#666"},formatter:function(t,e){return t>=1e3&&(t=t/1e3+"k"),t}}},tooltip:{trigger:"axis",axisPointer:{lineStyle:{type:"dashed"}},transitionDuration:0,borderColor:"#FCA155",textStyle:{color:"#000"},backgroundColor:"#fff",extraCssText:"box-shadow: 0 0 10px rgba(0,0,0,0.2);font-weight: 540;"},grid:{left:"40",top:"20",right:"40"},series:[{data:[],type:"line",smooth:!0,symbolSize:10,showSymbol:!0,lineStyle:{color:"#FCAD6A",width:4},itemStyle:{borderColor:"#FCAD6A",borderWidth:3}}]},i={tooltip:{trigger:"item",borderColor:"#FCA155",textStyle:{color:"#000",fontWeight:500},backgroundColor:"#fff",extraCssText:"box-shadow: 0 0 10px rgba(0,0,0,0.2);font-weight: 540;",formatter:function(t){var e=t.marker,r=t.percent;return e+t.name+"&nbsp;&nbsp;&nbsp;"+r+"%"}},title:{text:0,subtext:"订单总笔数",left:"center",top:"30%",textStyle:{color:"#000",fontSize:40,align:"center"},subtextStyle:{color:"#999",fontSize:16,align:"center"}},legend:{bottom:"5%",left:"center",icon:"circle"},series:[{type:"pie",radius:["70%","60%"],avoidLabelOverlap:!1,top:"-25%",itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:5},hoverAnimation:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!1,fontSize:"20",fontWeight:"bold"}},labelLine:{show:!1},data:[]}],color:["#07DED0","#FE985F","#9E92F7","#F97C95","#58AFFE","#F8C345"]},o={tooltip:{trigger:"item",borderColor:"#FCA155",textStyle:{color:"#000"},backgroundColor:"#fff",extraCssText:"box-shadow: 0 0 10px rgba(0,0,0,0.2);font-weight: 540;",formatter:function(t){var e=t.marker,r=t.percent;return e+t.name+"&nbsp;&nbsp;&nbsp;"+r+"%"}},legend:{bottom:"5%",left:"center",icon:"circle"},series:[{type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,top:"-20%",left:"-5%",label:{show:!1,position:"center"},labelLine:{show:!1},emphasis:{label:{show:!0,fontSize:"20",fontWeight:"bold",minWidth:300}},data:[]}],color:["#4D95FA","#07D7D7","#727AFF","#4AD96D","#ffdc60"]},s=[{label:"餐段",key:"meal_type"},{label:"营业额",key:"turnover"},{label:"占比",key:"percent"}];function c(t){return t?(n("string"!==t)&&(t=t.toString()),t.replace(/(\d)(?=(\d{3})+(?!\d))/g,"$1,")):t}var l={title:{text:"单位：元",right:40,textStyle:{color:"#a0a1a3",fontSize:14}},xAxis:{type:"category",data:[],axisLine:{show:!1},axisTick:{show:!1},splitLine:{show:!1},axisLabel:{textStyle:{fontSize:12,color:"#666"},formatter:function(t,e){var r="";return t.length>4?(r=t.slice(0,4),r+="\n"+t.slice(4,8),t.length>8&&(r+="...")):r=t,r}}},yAxis:{type:"value",axisLine:{show:!1},axisTick:{show:!1},splitNumber:4,axisLabel:{textStyle:{fontSize:16,color:"#666"},formatter:function(t,e){return t>=1e3&&(t=t/1e3+"k"),t}},splitLine:{show:!0,lineStyle:{type:"dashed",color:"#dfe5ec"}}},grid:{left:"40",top:"50",right:"40"},tooltip:{borderColor:"#FCA155",textStyle:{color:"#000"},transitionDuration:0,backgroundColor:"#fff",extraCssText:"box-shadow: 0 0 10px rgba(0,0,0,0.2);font-weight: 540;",formatter:function(t){var e=t.value,r=t.name;return'<div style="padding:5px;font-size:16px;font-weight: 540;">'.concat(r,'<br /><span style=" color: #666;display:inline-block;margin: 5px 20px 0 0;">营业额</span>￥').concat(c(e))}},series:[{barMaxWidth:30,color:"#FE943C",data:[],type:"bar",large:!0}]}},d4e4:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",[e("tree-select",t._g(t._b({attrs:{multiple:!0,options:t.treeList,limit:1,limitText:function(t){return"+"+t},"default-expand-level":1,normalizer:t.normalizer,placeholder:"请选择","no-children-text":"暂无更多",noOptionsText:"暂无分类",noResultsText:"暂无更多","search-nested":"","value-consists-of":"LEAF_PRIORITY",appendToBody:!0},model:{value:t.selectData,callback:function(e){t.selectData=e},expression:"selectData"}},"tree-select",t.$attrs,!1),t.$listeners))],1)},a=[],i=r("ed08"),o={name:"",components:{},props:{onlyFirstList:{type:Boolean,default:!1}},data:function(){return{selectData:[],treeList:[],normalizer:function(t){return{id:t.id,label:t.name,children:t.children}},firstLevelList:[],secondLevelList:[]}},computed:{},watch:{},created:function(){this.getCategory()},mounted:function(){},methods:{getCategory:function(){var t=this,e={page:1,page_size:999999},r=this.$apis.apiBackgroundFoodFoodSortListPost(e),n=this.$apis.apiBackgroundFoodFoodCategoryListPost(e);this.onlyFirstList?r.then((function(e){0===e.code&&(t.firstLevelList=e.data.results),t.treeList=t.arrayToTree(t.firstLevelList,[])})):Promise.all([r,n]).then((function(e){e.forEach((function(e,r){if(0===e.code)switch(r){case 0:t.firstLevelList=e.data.results;break;case 1:t.secondLevelList=e.data.results;break}})),t.treeList=t.arrayToTree(t.firstLevelList,t.secondLevelList)})).catch((function(t){}))},arrayToTree:function(t,e){var r=Object(i["f"])(t);return e.forEach((function(t){for(var e=0;e<r.length;e++){var n=r[e];t.sort==n.id?(n.isDisabled=!1,n.children?n.children.push(t):n.children=[t]):n.children||(n.children=[],n.isDisabled=!0)}})),r},reset:function(){this.selectData=[]}}},s=o,c=(r("894e"),r("2877")),l=Object(c["a"])(s,n,a,!1,null,null,null);e["default"]=l.exports},efff:function(t,e,r){},f673:function(t,e,r){"use strict";r("efff")},fcc6:function(t,e,r){"use strict";r("00b3")}}]);