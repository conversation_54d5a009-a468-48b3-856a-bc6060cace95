(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-food-admin-DietNutrition","view-merchant-meal-management-components-ColumnItem","view-merchant-meal-management-components-dietCrowdNutritionData"],{5392:function(e,t,r){},"71f6":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"diet-nutrition container-wrapper"},[t("refresh-tool",{on:{refreshPage:e.refreshHandle}}),t("search-form",{ref:"searchRef",attrs:{"form-setting":e.searchFormSetting,"label-width":"105px"},on:{search:e.searchHandle}}),t("div",{staticClass:"table-wrapper"},[t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title",staticStyle:{width:"900px"}},[e._v(" 中国居民膳食营养素参考摄入量（孕妇与哺乳期在同龄人群参考值基础上额外增加量） ")]),t("div",{staticClass:"align-r"},[t("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(t){return e.addAndEditCrowd("add")}}},[e._v(" 创建人群 ")])],1)]),t("div",{staticClass:"table-content"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},[t("el-table-column",{attrs:{prop:"id",label:"编号",align:"center"}}),t("el-table-column",{attrs:{prop:"name",label:"名称",align:"center",width:"200px"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",[e._v(e._s(r.row.name))])]}}])}),t("el-table-column",{attrs:{prop:"group",label:"人群",align:"center"}}),t("el-table-column",{attrs:{prop:"",label:"年龄",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",[e._v(e._s(r.row.min_age)+"~"+e._s(r.row.max_age))])]}}])}),t("el-table-column",{attrs:{prop:"",label:"营养信息",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.clickShowDialogNutrition(r.row)}}},[e._v(" 查看 ")])]}}])}),t("el-table-column",{attrs:{prop:"source",label:"来源",align:"center"}}),t("el-table-column",{attrs:{prop:"account_username",label:"创建人",align:"center"}}),t("el-table-column",{attrs:{fixed:"right",label:"操作",width:"180",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.addAndEditCrowd("edit",r.row)}}},[e._v(" 编辑 ")]),t("el-button",{staticClass:"ps-red",attrs:{type:"text",size:"small"},on:{click:function(t){return e.deleteHaldler("del",r.row)}}},[e._v(" 删除 ")])]}}])})],1)],1),t("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[t("el-pagination",{staticClass:"ps-text",attrs:{"current-page":e.currentPage,"page-sizes":[10,20,50,100,500],"page-size":e.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)]),t("el-dialog",{attrs:{title:"膳食营养",visible:e.showDietDialog,"custom-class":"ps-dialog",width:"50%"},on:{"update:visible":function(t){e.showDietDialog=t}}},[e.showDietDialog?t("el-table",{ref:"tableDataNutrition",staticStyle:{width:"100%"},attrs:{data:e.tableDataNutrition,border:""}},[t("el-table-column",{attrs:{prop:"",label:"中国居民常量和微量元素的参考摄入量(RNIS)或推荐摄入量(AI)、可耐最高摄入量(UL)",align:"center"}},[e._l(e.tableDataNutritionData,(function(e){return t("column-item",{key:e.label,attrs:{col:e,showDialogNutritionDisabled:!0}})}))],2)],1):e._e()],1)],1)},a=[],l=r("ed08"),o=r("981e"),i=r("9665"),c=r("f63a");function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},l="function"==typeof Symbol?Symbol:{},o=l.iterator||"@@iterator",i=l.asyncIterator||"@@asyncIterator",c=l.toStringTag||"@@toStringTag";function p(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{p({},"")}catch(e){p=function(e,t,r){return e[t]=r}}function d(e,t,r,n){var l=t&&t.prototype instanceof k?t:k,o=Object.create(l.prototype),i=new j(n||[]);return a(o,"_invoke",{value:_(e,r,i)}),o}function h(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var y="suspendedStart",f="suspendedYield",b="executing",g="completed",m={};function k(){}function v(){}function w(){}var L={};p(L,o,(function(){return this}));var A=Object.getPrototypeOf,I=A&&A(A(C([])));I&&I!==r&&n.call(I,o)&&(L=I);var D=w.prototype=k.prototype=Object.create(L);function U(e){["next","throw","return"].forEach((function(t){p(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,t){function r(a,l,o,i){var c=h(e[a],e,l);if("throw"!==c.type){var s=c.arg,p=s.value;return p&&"object"==u(p)&&n.call(p,"__await")?t.resolve(p.__await).then((function(e){r("next",e,o,i)}),(function(e){r("throw",e,o,i)})):t.resolve(p).then((function(e){s.value=e,o(s)}),(function(e){return r("throw",e,o,i)}))}i(c.arg)}var l;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){r(e,n,t,a)}))}return l=l?l.then(a,a):a()}})}function _(t,r,n){var a=y;return function(l,o){if(a===b)throw Error("Generator is already running");if(a===g){if("throw"===l)throw o;return{value:e,done:!0}}for(n.method=l,n.arg=o;;){var i=n.delegate;if(i){var c=O(i,n);if(c){if(c===m)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===y)throw a=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=b;var u=h(t,r,n);if("normal"===u.type){if(a=n.done?g:f,u.arg===m)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(a=g,n.method="throw",n.arg=u.arg)}}}function O(t,r){var n=r.method,a=t.iterator[n];if(a===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,O(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var l=h(a,t.iterator,r.arg);if("throw"===l.type)return r.method="throw",r.arg=l.arg,r.delegate=null,m;var o=l.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,m):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function N(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function S(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function j(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(N,this),this.reset(!0)}function C(t){if(t||""===t){var r=t[o];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,l=function r(){for(;++a<t.length;)if(n.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return l.next=l}}throw new TypeError(u(t)+" is not iterable")}return v.prototype=w,a(D,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:v,configurable:!0}),v.displayName=p(w,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===v||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,p(e,c,"GeneratorFunction")),e.prototype=Object.create(D),e},t.awrap=function(e){return{__await:e}},U(x.prototype),p(x.prototype,i,(function(){return this})),t.AsyncIterator=x,t.async=function(e,r,n,a,l){void 0===l&&(l=Promise);var o=new x(d(e,r,n,a),l);return t.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},U(D),p(D,c,"Generator"),p(D,o,(function(){return this})),p(D,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=C,j.prototype={constructor:j,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(S),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(n,a){return i.type="throw",i.arg=t,r.next=n,a&&(r.method="next",r.arg=e),!!a}for(var l=this.tryEntries.length-1;l>=0;--l){var o=this.tryEntries[l],i=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var c=n.call(o,"catchLoc"),u=n.call(o,"finallyLoc");if(c&&u){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var l=a;break}}l&&("break"===e||"continue"===e)&&l.tryLoc<=t&&t<=l.finallyLoc&&(l=null);var o=l?l.completion:{};return o.type=e,o.arg=t,l?(this.method="next",this.next=l.finallyLoc,m):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),m},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),S(r),m}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;S(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:C(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),m}},t}function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach((function(t){h(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function h(e,t,r){return(t=y(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function y(e){var t=f(e,"string");return"symbol"==u(t)?t:t+""}function f(e,t){if("object"!=u(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=u(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function b(e,t){return w(e)||v(e,t)||m(e,t)||g()}function g(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function m(e,t){if(e){if("string"==typeof e)return k(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?k(e,t):void 0}}function k(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function v(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,l,o,i=[],c=!0,u=!1;try{if(l=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=l.call(r)).done)&&(i.push(n.value),i.length!==t);c=!0);}catch(e){u=!0,a=e}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(u)throw a}}return i}}function w(e){if(Array.isArray(e))return e}function L(e,t,r,n,a,l,o){try{var i=e[l](o),c=i.value}catch(e){return void r(e)}i.done?t(c):Promise.resolve(c).then(n,a)}function A(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var l=e.apply(t,r);function o(e){L(l,n,a,o,i,"next",e)}function i(e){L(l,n,a,o,i,"throw",e)}o(void 0)}))}}var I={name:"DietNutrition",components:{columnItem:o["default"]},mixins:[c["a"]],data:function(){return{isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[],searchFormSetting:{name:{type:"input",label:"名字",value:"",placeholder:"请输入名字"},group:{type:"input",label:"人群",value:"",placeholder:"请输入人群"},min_age:{type:"input",label:"最小年龄",value:"",placeholder:"请输入最小年龄"},max_age:{type:"input",label:"最大年龄",value:"",placeholder:"请输入最大年龄"},gender:{type:"select",label:"性别",value:"",placeholder:"请选择性别",collapseTags:!0,listNameKey:"name",listValueKey:"value",dataList:[{name:"男",value:"MAN"},{name:"女",value:"WOMEN"},{name:"其他",value:"OTHER"}]}},showDietDialog:!1,showAgeDialog:!1,ageForm:{age1:"",age2:""},tableDataNutrition:[],tableDataNutritionData:i["dietCrowdData"]}},created:function(){this.initLoad(),this.initTableDataNutrition()},mounted:function(){},methods:{initLoad:function(){this.currentPage=1,this.getFoodDietGroupList()},searchHandle:Object(l["d"])((function(){this.currentPage=1,this.initLoad()}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.initLoad()},initTableDataNutrition:function(){var e={};this.calleArr(i["dietCrowdData"],e,1),this.tableDataNutrition.push(e)},calleArr:function(e,t,r,n){for(var a=0;a<e.length;a++){var l=e[a];if(1===r&&(n=l.key),l.children){var o=r+1;this.calleArr(l.children,t,o,n)}else l.prop=n+"_"+l.key,t[l.prop]=""}},getFoodDietGroupList:function(){var e=this;return A(s().mark((function t(){var r,n,a,o;return s().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(l["Z"])(e.$apis.apiBackgroundFoodDietGroupListPost(d(d({},e.formatQueryParams(e.searchFormSetting)),{},{page:e.currentPage,page_size:e.pageSize})));case 3:if(r=t.sent,n=b(r,2),a=n[0],o=n[1],e.isLoading=!1,!a){t.next=11;break}return e.$message.error(a.message),t.abrupt("return");case 11:0===o.code?(e.totalCount=o.data.count,e.tableData=o.data.results):e.$message.error(o.msg);case 12:case"end":return t.stop()}}),t)})))()},formatQueryParams:function(e){var t={};for(var r in e)e[r].value&&("select_date"!==r?t[r]=e[r].value:e[r].value.length>0&&(t.start_date=e[r].value[0],t.end_date=e[r].value[1]));return t},foodDietGroupDelete:function(e){var t=this;return A(s().mark((function r(){var n,a,o,i;return s().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,Object(l["Z"])(t.$apis.apiBackgroundFoodDietGroupDeletePost({ids:[e.id]}));case 2:if(n=r.sent,a=b(n,2),o=a[0],i=a[1],!o){r.next=9;break}return t.$message.error(o.message),r.abrupt("return");case 9:0===i.code?(t.$message.success(i.msg),t.initLoad()):t.$message.error(i.msg);case 10:case"end":return r.stop()}}),r)})))()},deleteHaldler:function(e,t){var r=this;this.$confirm("是否删除该人群？","删除",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-warn",center:!0,beforeClose:function(){var e=A(s().mark((function e(n,a,l){return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:"confirm"===n?(a.confirmButtonLoading=!0,r.foodDietGroupDelete(t),l(),a.confirmButtonLoading=!1):a.confirmButtonLoading||l();case 1:case"end":return e.stop()}}),e)})));function n(t,r,n){return e.apply(this,arguments)}return n}()}).then((function(e){})).catch((function(e){}))},handleSizeChange:function(e){this.pageSize=e,this.getFoodDietGroupList()},handleCurrentChange:function(e){this.currentPage=e,this.getFoodDietGroupList()},clickShowDialogNutrition:function(e){this.showDietDialog=!0,this.editRecurrence(e)},editRecurrence:function(e){var t=JSON.parse(e.element),r=JSON.parse(e.vitamin);for(var n in this.tableDataNutrition[0].energy_mj=e.energy_mj,this.tableDataNutrition[0].energy_kcal=e.energy_kcal,this.tableDataNutrition[0].protein_40=e.protein,this.tableDataNutrition[0].axunge_20=e.axunge,t)this.tableDataNutrition[0][n+"_AI"]=t[n].AI,this.tableDataNutrition[0][n+"_UL"]=t[n].UL;for(var a in r)this.tableDataNutrition[0][a+"_AI"]=r[a].AI,this.tableDataNutrition[0][a+"_UL"]=r[a].UL},gotoExport:function(){this.$message.error("暂无导出")},addAndEditCrowd:function(e,t){this.$router.push({name:"MerchantDietCrowd",query:{type:e,id:"edit"===e?t.id:""}})}}},D=I,U=(r("abda"),r("2877")),x=Object(U["a"])(D,n,a,!1,null,"1bf36ec7",null);t["default"]=x.exports},9665:function(e,t,r){"use strict";r.r(t),r.d(t,"dietCrowdData",(function(){return n}));var n=[{key:"energy",label:"能量(ERR=mj/kg+kcal/kg)",children:[{key:"mj",label:"兆焦耳(mj/kg)",prop:""},{key:"kcal",label:"千卡(kcal/kg)",prop:""}]},{key:"axunge",label:"蛋白质(Protein)",children:[{key:20,label:"克/天(g/d)",prop:""}]},{key:"protein",label:"脂肪(Fat)",children:[{key:40,label:"占能量百分比（EER%）",prop:""}]},{key:"Ca",label:"钙",children:[{key:5,label:"毫克/天(mg/d)",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"P",label:"磷",children:[{key:60,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"K",label:"钾",children:[{key:70,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"Na",label:"钠",children:[{key:80,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"Mg",label:"镁",children:[{key:90,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"Fe",label:"铁",children:[{key:100,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"I",label:"碘",children:[{key:110,label:"微克/天（μg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"Zn",label:"锌",children:[{key:120,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"Se",label:"硒",children:[{key:130,label:"微克/天（μg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"Cu",label:"铜",children:[{key:140,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"F",label:"氟",children:[{key:150,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"Cr",label:"铬",children:[{key:160,label:"微克/天（μg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"Mn",label:"锰",children:[{key:170,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"Mo",label:"钼",children:[{key:180,label:"微克/天（μg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"VA",label:"维生素A",children:[{key:190,label:"微克（μgRAE/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"VD",label:"维生素D",children:[{key:200,label:"微克/天（μg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"VE",label:"维生素E",children:[{key:210,label:"毫克（mga-TE/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"VK",label:"维生素K",children:[{key:220,label:"微克/天（μg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"VB1",label:"维生素B1",children:[{key:230,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"VB2",label:"维生素B2",children:[{key:240,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"VB6",label:"维生素B6",children:[{key:250,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"VB12",label:"维生素B12",children:[{key:260,label:"微克/天（μg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"VC",label:"维生素C",children:[{key:270,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"VB5",label:"泛酸",children:[{key:280,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"VM",label:"叶酸",children:[{key:290,label:"微克（μgDFE/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"VB3",label:"烟酸",children:[{key:300,label:"毫克/天（mg NE/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"Choline",label:"胆碱",children:[{key:310,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"Nicotinamide",label:"烟酰胺",children:[{key:320,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]},{key:"VH",label:"生物素",children:[{key:330,label:"毫克/天（mg/d）",children:[{key:"AI",label:"AI",prop:""},{key:"UL",label:"UL",prop:""}]}]}]},"981e":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("el-table-column",{attrs:{prop:e.col.prop,label:e.col.label,align:e.alignType}},[e._l(e.col.children,(function(r,n){return[r.children?t("column-item",{key:n,attrs:{col:r,showDialogNutritionDisabled:e.showDialogNutritionDisabled}}):t("el-table-column",{key:n,attrs:{label:r.label,prop:r.prop,align:e.alignType},scopedSlots:e._u([{key:"default",fn:function(n){return[t("el-input",{staticClass:"input",attrs:{disabled:e.showDialogNutritionDisabled},model:{value:n.row[r.prop],callback:function(t){e.$set(n.row,r.prop,t)},expression:"scope.row[item.prop]"}})]}}],null,!0)})]}))],2)},a=[],l={name:"ColumnItem",props:{col:{type:Object},alignType:{type:String,default:"center"},showDialogNutritionDisabled:{type:Boolean,default:!1}}},o=l,i=r("2877"),c=Object(i["a"])(o,n,a,!1,null,null,null);t["default"]=c.exports},abda:function(e,t,r){"use strict";r("5392")}}]);