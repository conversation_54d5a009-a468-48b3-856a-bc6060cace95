(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-member-center-constants"],{c8c2:function(e,l,a){"use strict";a.r(l),a.d(l,"getRequestParams",(function(){return y})),a.d(l,"RECENTSEVEN",(function(){return m})),a.d(l,"DIC_OBTAIN_TYPE",(function(){return p})),a.d(l,"DIC_SEND_TYPE",(function(){return b})),a.d(l,"DIC_MEMBER_STATUS",(function(){return c})),a.d(l,"DIC_TRIGGER_TYPE",(function(){return _})),a.d(l,"DIC_MENBER_STATUS",(function(){return d})),a.d(l,"DIC_PERMISSION_TYPE",(function(){return k})),a.d(l,"DIC_MEMBER_CYCLE",(function(){return v})),a.d(l,"SEARCH_FORM_RECORD_DATA",(function(){return f})),a.d(l,"SEARCH_FORM_EXCLUSIVE_DATA",(function(){return A})),a.d(l,"TABLE_HEAD_RECORD_DATA",(function(){return E})),a.d(l,"TABLE_HEAD_EXCLUSIVE_DATA",(function(){return h})),a.d(l,"SEARCH_FORM_SMS_MANAGER_DATA",(function(){return T})),a.d(l,"TABLE_HEAD_SEND_DATA",(function(){return w})),a.d(l,"TABLE_HEAD_CHARGE_INDEX_DATA",(function(){return N})),a.d(l,"TABLE_HEAD_CHARGE_INDEX_CONTENT",(function(){return g})),a.d(l,"SEARCH_FORM_CHARGE_DETAIL_DATA",(function(){return D})),a.d(l,"TABLE_HEAD_CHARGE_DETAIL_DATA",(function(){return R})),a.d(l,"SEARCH_FORM_PERMISSION_MANAGER_DATA",(function(){return O})),a.d(l,"TABLE_HEAD_PERMISSION_MANAGER_DATA",(function(){return S})),a.d(l,"SEARCH_FORM_KEY_MANAGER_DATA",(function(){return M})),a.d(l,"TABLE_HEAD_KEY_MANAGER_DATA",(function(){return C})),a.d(l,"SEARCH_FORM_PROMOTIONAL_PICTURE_DATA",(function(){return I})),a.d(l,"TABLE_HEAD_PROMOTIONAL_PICTURE_DATA",(function(){return L}));var t=a("5a0c");function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function n(e,l){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);l&&(t=t.filter((function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable}))),a.push.apply(a,t)}return a}function i(e){for(var l=1;l<arguments.length;l++){var a=null!=arguments[l]?arguments[l]:{};l%2?n(Object(a),!0).forEach((function(l){o(e,l,a[l])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):n(Object(a)).forEach((function(l){Object.defineProperty(e,l,Object.getOwnPropertyDescriptor(a,l))}))}return e}function o(e,l,a){return(l=s(l))in e?Object.defineProperty(e,l,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[l]=a,e}function s(e){var l=u(e,"string");return"symbol"==r(l)?l:l+""}function u(e,l){if("object"!=r(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var t=a.call(e,l||"default");if("object"!=r(t))return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===l?String:Number)(e)}var y=function(e,l,a){var t,r={};Object.keys(e).forEach((function(l){("select_time"!==l&&""!==e[l].value&&e[l].value&&0!==e[l].value.length||"boolean"===typeof e[l].value)&&(r[l]=e[l].value)}));var n=i({page:l,page_size:a},r);return 2===(null===(t=e.select_time)||void 0===t||null===(t=t.value)||void 0===t?void 0:t.length)&&(n.start_date=e.select_time.value[0],n.end_date=e.select_time.value[1]),n},m=[t().subtract(7,"day").format("YYYY-MM-DD"),t().format("YYYY-MM-DD")],p=[{name:"线上购买",value:"buy",label:"buy"},{name:"手动发放",value:"manual_release",label:"manual_release"}],b=[{name:"消费通知",value:"consume",label:"consume"},{name:"点餐提醒",value:"order",label:"order"},{name:"充值提醒",value:"recharge",label:"recharge"},{name:"余额提醒",value:"balance",label:"balance"},{name:"提现通知",value:"withdraw",label:"withdraw"},{name:"通行通知",value:"is_pass",label:"is_pass"}],c=[{name:"使用中",value:"using",label:"using"},{name:"已过期",value:"expired",label:"expired"},{name:"非会员",value:"non",label:"non"}],_=[{name:"全部标签",value:"all",label:"all"},{name:"任意标签",value:"any",label:"any"}],d=[{name:"开启",value:!0,label:!0},{name:"关闭",value:!1,label:!1}],k=[{name:"关联链接",value:"association"},{name:"固定模块",value:"fixed"}],v=[{name:"月卡",value:"month"},{name:"季卡",value:"season"},{name:"年卡",value:"year"},{name:"永久会员",value:"permanent"},{name:"畅享会员",value:"week"}],f={select_time:{type:"daterange",label:"购买时间",clearable:!0,format:"yyyy-MM-dd",value:m},member_permission_name:{type:"input",label:"权益",value:"",placeholder:"请输入权益"},nickname:{type:"input",label:"用户姓名",value:"",placeholder:"请输入用户姓名"},phone:{type:"input",label:"手机号",value:"",placeholder:"请输入手机号"},receive_type:{type:"select",value:"",label:"获取方式",dataList:p,multiple:!1,collapseTags:!0,listNameKey:"name",listValueKey:"value",clearable:!0}},A={member_permissions:{type:"select",value:[],label:"权益",dataList:[],multiple:!1,collapseTags:!0,listNameKey:"name",listValueKey:"id",clearable:!0}},E=[{label:"订单号",key:"trade_no",width:"200",fixed:"left"},{label:"购买时间",key:"create_time",width:"200"},{label:"权益",key:"member_permission_name",width:"140"},{label:"金额",key:"origin_fee",width:"140",type:"slot",slotName:"price"},{label:"有效期",key:"days",width:"120",type:"slot",slotName:"days"},{label:"用户姓名",key:"nickname",width:"150"},{label:"手机号",key:"phone",width:"150"},{label:"操作员",key:"operator_name"},{label:"获取方式",key:"receive_type_alias"}],h=[{label:"序号",key:"index",width:"100",fixed:"left"},{label:"权益",key:"member_permissions_name",width:"200",type:"slot",slotName:"permission"},{label:"有效期",key:"days",width:"200",type:"slot",slotName:"days"},{label:"价格",key:"origin_fee",width:"200",type:"slot",slotName:"price"},{label:"说明",key:"remark",width:"200"},{label:"操作员",key:"operator_name"},{label:"修改时间",key:"update_time",width:"200"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"200"}],T={select_time:{type:"daterange",label:"推送时间",clearable:!0,format:"yyyy-MM-dd",value:m},name:{type:"input",label:"用户名",value:"",placeholder:"请输用户名"},phone:{type:"input",label:"手机号",value:"",placeholder:"请输入手机号"},organization_id:{type:"select",multiple:!0,filterable:!0,collapseTags:!0,label:"消费点",value:[],placeholder:"请选择消费点",listNameKey:"name",listValueKey:"id",dataList:[]},company_id:{type:"CompanySelect",value:[],label:"所属项目点",dataList:[],multiple:!0,checkStrictly:!0,clearable:!0,labelWidth:"100px",companyOpts:{label:"name",value:"company"},role:"super"},receive_type:{type:"select",value:[],label:"推送类型",dataList:b,multiple:!1,collapseTags:!0,listNameKey:"name",clearable:!0}},w=[{label:"推送时间",key:"create_time",width:"200"},{label:"用户名",key:"user_name",width:"150"},{label:"手机号",key:"phone",width:"150"},{label:"所属项目点",key:"company_name",width:"150"},{label:"消费点",key:"organization_name",width:"150"},{label:"推送类型",key:"push_type_alias",width:"150"},{label:"推送内容",key:"remark"},{label:"短信服务商",key:"sms_type_alias"}],N=[{label:"会员类型",key:"member_cycle_alias",width:"140"},{label:"说明",key:"remark",width:"250",type:"slot",slotName:"remark",align:"center"},{label:"基础价格（元）",key:"origin_fee",type:"slot",slotName:"price"},{label:"操作时间",key:"update_time"},{label:"操作人",key:"operator_name"},{label:"操作",key:"contact_name",type:"slot",slotName:"operation"}],g=[{member_cycle_alias:"年卡",origin_fee:"155622",remark:"说明说明说明说明"},{member_cycle_alias:"季卡",origin_fee:"26",remark:"11111"},{member_cycle_alias:"月卡",origin_fee:"32",remark:"2222"},{member_cycle_alias:"畅享会员",price:"44",remark:"333"},{member_cycle_alias:"永久会员",origin_fee:"522",remark:"66666666"}],D={name:{type:"input",label:"名称",value:"",placeholder:"请输入名称"},member_labels:{type:"select",value:[],label:"会员标签",dataList:[],multiple:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",clearable:!0},trigger_type:{type:"select",value:[],label:"标签触发类型",dataList:_,collapseTags:!0,listNameKey:"name",listValueKey:"value",clearable:!0,labelWidth:"200"},is_enable:{type:"select",value:[],label:"状态",dataList:d,listNameKey:"name",listValueKey:"value",clearable:!0}},R=[{label:"名称",key:"name",width:"140"},{label:"会员标签",key:"member_labels",width:"250",type:"slot",slotName:"label"},{label:"标签触发类型",key:"trigger_type",type:"slot",slotName:"trigger"},{label:"优惠折扣",key:"discount"},{label:"优惠价",key:"origin_fee",type:"money"},{label:"限购次数",key:"buy_count",type:"slot",slotName:"buyCount"},{label:"说明",key:"remark",showTooltip:!0},{label:"操作时间",key:"update_time"},{label:"操作人",key:"operator_name"},{label:"状态",key:"status",type:"slot",slotName:"status"},{label:"操作",key:"contact_name",type:"slot",slotName:"operation"}],O={name:{type:"input",label:"权限名称",value:"",placeholder:"请输入权限名称"},type:{type:"select",value:[],label:"类型",dataList:k,collapseTags:!0,listNameKey:"name",listValueKey:"value",clearable:!0},associate_url:{type:"input",label:"关联链接",value:"",placeholder:"请输入关联链接"}},S=[{label:"权限名称",key:"name",width:"140"},{label:"图标",key:"icon_url",width:"250",type:"slot",slotName:"image"},{label:"类型",key:"type_alias"},{label:"键值",key:"permission_key"},{label:"关联链接",key:"associate_url",width:"250",type:"slot",slotName:"associateUrl"},{label:"权限说明",key:"remark",showTooltip:!0,width:"200"},{label:"操作时间",key:"update_time"},{label:"操作人",key:"operator_name"},{label:"是否需要会员",key:"status",type:"slot",slotName:"status"},{label:"操作",key:"contact_name",type:"slot",slotName:"operation"}],M={name:{type:"input",label:"名称",value:"",placeholder:"请输入名称"}},C=[{label:"名称",key:"name",width:"140"},{label:"键值",key:"permission_key"},{label:"说明",key:"remark"},{label:"操作时间",key:"update_time"},{label:"操作人",key:"operator_name"},{label:"操作",key:"operation",type:"slot",slotName:"operation"}],I={name:{type:"input",label:"权限名称",value:"",placeholder:"请输入权限名称"},type:{type:"select",value:[],label:"类型",dataList:k,collapseTags:!0,listNameKey:"name",listValueKey:"value",clearable:!0},associate_url:{type:"input",label:"关联链接",value:"",placeholder:"请输入关联链接"}},L=[{label:"名称",key:"name",width:"140"},{label:"图片",key:"url",width:"250",type:"slot",slotName:"image"},{label:"优先级",key:"priority"},{label:"状态",key:"is_release",type:"slot",slotName:"status"},{label:"显示界面",key:"ui_type_alias",type:"slot",slotName:"UIType"},{label:"操作时间",key:"update_time"},{label:"操作人",key:"operator_name"},{label:"操作",key:"contact_name",type:"slot",slotName:"operation"}]}}]);