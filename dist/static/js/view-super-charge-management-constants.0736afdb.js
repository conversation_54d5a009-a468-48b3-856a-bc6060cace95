(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-charge-management-constants"],{5006:function(e,t,a){"use strict";a.r(t),a.d(t,"RECENTSEVEN",(function(){return n})),a.d(t,"chargeOrderTableSetting",(function(){return r})),a.d(t,"chargeTrailTableSetting",(function(){return s})),a.d(t,"chargeRulesTableSetting",(function(){return i})),a.d(t,"divide",(function(){return y})),a.d(t,"times",(function(){return u}));var l=a("5a0c"),o=a("da92"),n=[l().subtract(7,"day").format("YYYY-MM-DD"),l().format("YYYY-MM-DD")],r=[{label:"商户",key:"company_name"},{label:"订单号",key:"trade_no"},{label:"创建时间",key:"create_time"},{label:"支付时间",key:"pay_time"},{label:"到账时间",key:"finish_time"},{label:"支付金额",key:"real_fee",type:"slot",slotName:"realFee"},{label:"支付方式",key:"pay_method_alias"},{label:"交易类型",key:"transaction_type_alias"},{label:"交易内容",key:"transaction_type",type:"slot",slotName:"transactionContent"},{label:"订单状态",key:"order_status_alias"},{label:"转账凭证",key:"voucher_url",type:"slot",slotName:"voucherUrl"},{label:"发票申请",key:"invoice_status",type:"slot",slotName:"invoiceStatus"},{label:"操作员",key:"operator"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"100"}],s=[{type:"selection",width:"55",reserveSelection:!0},{label:"商户名称",key:"company_name"},{label:"收费模式",key:"toll_type",type:"slot",slotName:"tollType"},{label:"系统版本",key:"toll_version_name"},{label:"收费规则",key:"toll_rule",type:"slot",slotName:"tollRule"},{label:"用户规模",key:"user_scale",type:"slot",slotName:"userScale"},{label:"规模使用率",key:"use_user_rate",type:"slot",slotName:"useUserRate"},{label:"用户数预警",key:"is_user_scale_warning_alias"},{label:"使用期限",key:"date_range",type:"slot",slotName:"dateRange"},{label:"距离到期",key:"due_day_num",type:"slot",slotName:"dueDayNum"},{label:"到期预警",key:"is_expire_warning_alias"}],i=[{label:"规则名称",key:"name"},{label:"系统版本",key:"toll_version_name"},{label:"收费规则",key:"alias"},{label:"使用商户数",key:"use_count"},{label:"创建人",key:"creater_alias"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"200"}],y=function(e){return"number"===typeof e?o["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:o["a"].divide(e,100).toFixed(2)},u=function(e){return o["a"].times(e,100)}}}]);