(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-charge-management-components-AddOrEditDialog"],{"0a69":function(t,e,i){"use strict";i("bc86")},"165d":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"ps-el-drawer"},[e("el-drawer",{attrs:{title:"add"===t.type?"新建规则":"编辑规则",visible:t.visible,"show-close":!1,size:"35%"}},[e("div",{staticClass:"p-20 flex-col"},[e("el-form",{ref:"formDataRef",staticClass:"addData-form",attrs:{model:t.formData,"status-icon":"",rules:t.formDataRules,"label-width":"90px","label-position":"right"},nativeOn:{submit:function(t){t.preventDefault()}}},[e("div",[e("el-form-item",{attrs:{label:"规则名称:",prop:"name"}},[e("el-input",{staticClass:"ps-input w-250",attrs:{placeholder:"请输入"},model:{value:t.formData.name,callback:function(e){t.$set(t.formData,"name",e)},expression:"formData.name"}})],1),e("el-form-item",{attrs:{label:"系统版本:",prop:"toll_version"}},[e("el-select",{staticClass:"w-250",attrs:{placeholder:"请选择系统收费版本（单选）"},model:{value:t.formData.toll_version,callback:function(e){t.$set(t.formData,"toll_version",e)},expression:"formData.toll_version"}},t._l(t.versionList,(function(t,i){return e("el-option",{key:i,attrs:{label:t.name,value:t.id}})})),1)],1),e("el-form-item",{attrs:{label:"收费规则:",prop:"fee"}},[e("div",{staticClass:"addData-form-content p-t-10 p-l-20 p-r-20 p-b-20"},[e("span",{staticClass:"origin"},[t._v("提示：续费优惠设置的折扣不填，则商户续费时不可见")]),e("el-form-item",{attrs:{label:"用户收费金额","label-width":"100px"}},[e("el-input",{staticClass:"w-80 m-r-10",attrs:{size:"mini",type:"number"},model:{value:t.formData.fee,callback:function(e){t.$set(t.formData,"fee",e)},expression:"formData.fee"}}),e("span",[t._v("元，人/年")])],1),e("el-form-item",{attrs:{label:"续费优惠设置","label-width":"100px"}},[e("el-form-item",{attrs:{prop:"first_discount","inline-message":!0}},[e("span",[t._v("1年折扣")]),e("el-input",{staticClass:"w-80 m-l-10 m-r-10",attrs:{size:"mini",type:"number"},model:{value:t.formData.first_discount,callback:function(e){t.$set(t.formData,"first_discount",e)},expression:"formData.first_discount"}}),e("span",[t._v("%")])],1),e("el-form-item",{attrs:{prop:"second_discount","inline-message":!0}},[e("span",[t._v("2年折扣")]),e("el-input",{staticClass:"w-80 m-l-10 m-r-10",attrs:{size:"mini",type:"number"},model:{value:t.formData.second_discount,callback:function(e){t.$set(t.formData,"second_discount",e)},expression:"formData.second_discount"}}),e("span",[t._v("%")])],1),e("el-form-item",{attrs:{prop:"third_discount","inline-message":!0}},[e("span",[t._v("3年折扣")]),e("el-input",{staticClass:"w-80 m-l-10 m-r-10",attrs:{size:"mini",type:"number"},model:{value:t.formData.third_discount,callback:function(e){t.$set(t.formData,"third_discount",e)},expression:"formData.third_discount"}}),e("span",[t._v("%")])],1),e("el-form-item",{attrs:{prop:"fourth_discount","inline-message":!0}},[e("span",[t._v("4年折扣")]),e("el-input",{staticClass:"w-80 m-l-10 m-r-10",attrs:{size:"mini",type:"number"},model:{value:t.formData.fourth_discount,callback:function(e){t.$set(t.formData,"fourth_discount",e)},expression:"formData.fourth_discount"}}),e("span",[t._v("%")])],1),e("el-form-item",{attrs:{prop:"fifth_discount","inline-message":!0}},[e("span",[t._v("5年折扣")]),e("el-input",{staticClass:"w-80 m-l-10 m-r-10",attrs:{size:"mini",type:"number"},model:{value:t.formData.fifth_discount,callback:function(e){t.$set(t.formData,"fifth_discount",e)},expression:"formData.fifth_discount"}}),e("span",[t._v("%")])],1)],1)],1)])],1)]),e("div",{staticClass:"ps-el-drawer-footer ps-flex-align-c flex-align-c"},[e("div",{staticClass:"m-r-30"},[e("el-button",{staticClass:"w-100",attrs:{size:"small"},on:{click:t.cancel}},[t._v("取消")]),e("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:t.save}},[t._v("保存")])],1)])],1)])],1)},s=[],r=i("ed08"),o={name:"AddOrEditDialog",props:{type:{type:String,default:""},width:{type:String,default:""},rowData:{type:Object,default:function(){return{}}},nameList:{type:Array,default:function(){return[]}},isShow:Boolean},data:function(){var t=this,e=function(e,i,a){i?"add"===t.type?t.nameList.includes(i)?a(new Error("该规则名已被使用，请重新输入")):a():t.newNameList.includes(i)?a(new Error("该规则名已被使用，请重新输入")):a():a(new Error("请输入活动名称"))},i=function(t,e,i){var a=/^(?:\d{1,2}|\d{1,2}\.\d)$/;e?a.test(e)?e<6?i(new Error("最低不能低于6元/人/年")):i():i(new Error("请输入一个两位且至多保留一位小数的数")):i()},a=function(e,i,a){var s=["first_discount","second_discount","third_discount","fourth_discount","fifth_discount"],r=/^(0|[1-9][0-9]?)$/;if(i)if("first_discount"===e.fullField||r.test(i))if("first_discount"===e.fullField&&i>100)a(new Error("最大不能超过100"));else if("first_discount"!==e.fullField)for(var o=s.indexOf(e.fullField),n=1;n<=o;n++){var l=o-n;t.formData["".concat(s[l])]&&(i>t.formData["".concat(s[l])]?a(new Error("第".concat(o+1,"年折扣需小于第").concat(l+1,"年"))):a())}else a();else a(new Error("最多输入两位且不能有小数"));else a()};return{isLoading:!1,formDataRules:{name:[{max:10,message:"最大输入10个字符",trigger:["change","blur"]},{validator:e,trigger:["change","blur"]}],toll_version:[{required:!0,message:"请选择收费版本",trigger:"blur"}],fee:[{required:!0,message:"用户收费金额不能为空",trigger:["change","blur"]},{validator:i,trigger:["change","blur"]}],first_discount:[{validator:a,trigger:["change","blur"]}],second_discount:[{validator:a,trigger:["change","blur"]}],third_discount:[{validator:a,trigger:["change","blur"]}],fourth_discount:[{validator:a,trigger:["change","blur"]}],fifth_discount:[{validator:a,trigger:["change","blur"]}]},formData:{},newNameList:[],versionList:[]}},computed:{visible:{get:function(){return this.isShow},set:function(t){this.$emit("update:isShow",t)}}},watch:{isShow:function(t){var e=this;t&&(this.formData=Object(r["f"])(this.rowData),this.newNameList=Object(r["f"])(this.nameList.filter((function(t){return t!==e.formData.name}))),this.getVersionList())}},methods:{save:function(){var t=this,e={id:"add"===this.type?void 0:this.rowData.id,name:this.formData.name,toll_version:this.formData.toll_version,fee:Object(r["Y"])(this.formData.fee),first_discount:this.formData.first_discount||void 0,second_discount:this.formData.second_discount||void 0,third_discount:this.formData.third_discount||void 0,fourth_discount:this.formData.fourth_discount||void 0,fifth_discount:this.formData.fifth_discount||void 0};this.$refs.formDataRef.validate((function(i){if(!i)return t.$message.error("创建失败，请确认规则填写是否正确");"edit"===t.type?(Object.assign(e,{id:t.rowData.id}),t.$apis.apiBackgroundAdminBackgroundTollRuleModifyPost(e).then((function(e){0===e.code?t.$message.success("编辑成功"):t.$message.error(e.msg)}))):t.$apis.apiBackgroundAdminBackgroundTollRuleAddPost(e).then((function(e){0===e.code?t.$message.success("新增成功"):t.$message.error(e.msg)})),t.visible=!1}))},cancel:function(){this.$refs.formDataRef.resetFields(),this.visible=!1},getVersionList:function(){var t=this;this.$apis.apiBackgroundAdminBackgroundTollVersionListPost({page:1,page_size:9999,is_toll:!0}).then((function(e){0===e.code?t.versionList=Object(r["f"])(e.data.results)||[]:t.$message.error(e.msg)}))}}},n=o,l=(i("0a69"),i("2877")),c=Object(l["a"])(n,a,s,!1,null,"9aea564c",null);e["default"]=c.exports},bc86:function(t,e,i){}}]);