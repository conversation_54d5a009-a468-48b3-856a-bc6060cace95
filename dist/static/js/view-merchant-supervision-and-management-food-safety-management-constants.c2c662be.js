(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-supervision-and-management-food-safety-management-constants","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-meal-report-MealPackageRule"],{c9d9:function(e,a,l){"use strict";l.d(a,"a",(function(){return r})),l.d(a,"d",(function(){return i})),l.d(a,"b",(function(){return u})),l.d(a,"c",(function(){return o})),l.d(a,"e",(function(){return d})),l.d(a,"f",(function(){return m})),l.d(a,"g",(function(){return s}));var t=l("5a0c"),n=l("da92"),r=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],i=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],u={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},o=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],d=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],m=(t().subtract(7,"day").format("YYYY-MM-DD"),t().format("YYYY-MM-DD"),function(e){return e?"number"===typeof e?n["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:n["a"].divide(e,100).toFixed(2):"0.00"}),s=function(e){return n["a"].times(e,100)}},e49c:function(e,a,l){"use strict";l.r(a),l.d(a,"recentSevenDayTime",(function(){return s})),l.d(a,"recentSevenDay",(function(){return y})),l.d(a,"recentThreeDay",(function(){return c})),l.d(a,"recentCurrentDay",(function(){return b})),l.d(a,"SEARCH_SETTING_MORNING_INSPECTION_DETAILS",(function(){return p})),l.d(a,"SEARCH_SETTING_MORNING_INSPECTION_SUMMARY",(function(){return _})),l.d(a,"TABLE_HEAD_DATA_MORNING_INSPECTION_DETAILS",(function(){return f})),l.d(a,"TABLE_HEAD_DATA_MORNING_INSPECTION_SUMMARY",(function(){return k})),l.d(a,"SEARCH_SETTING_MORNING_SAMPLE_RECORD",(function(){return v})),l.d(a,"TABLE_HEAD_DATA_SAMPLE_RECORD",(function(){return g}));var t=l("5a0c"),n=l("c9d9");function r(e){return d(e)||o(e)||u(e)||i()}function i(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(e,a){if(e){if("string"==typeof e)return m(e,a);var l={}.toString.call(e).slice(8,-1);return"Object"===l&&e.constructor&&(l=e.constructor.name),"Map"===l||"Set"===l?Array.from(e):"Arguments"===l||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(l)?m(e,a):void 0}}function o(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function d(e){if(Array.isArray(e))return m(e)}function m(e,a){(null==a||a>e.length)&&(a=e.length);for(var l=0,t=Array(a);l<a;l++)t[l]=e[l];return t}var s=[t().subtract(7,"day").format("YYYY-MM-DD HH:mm:ss"),t().format("YYYY-MM-DD HH:mm:ss")],y=[t().subtract(7,"day").format("YYYY-MM-DD"),t().format("YYYY-MM-DD")],c=[t().subtract(3,"day").format("YYYY-MM-DD"),t().format("YYYY-MM-DD")],b=[t().subtract(0,"day").format("YYYY-MM-DD"),t().format("YYYY-MM-DD")],p={select_time:{type:"daterange",label:"晨检时间",value:y,clearable:!1},org_id:{type:"organizationSelect",value:"",label:"组织名称",listNameKey:"name",listValueKey:"id",checkStrictly:!0,multiple:!1,collapseTags:!0,clearable:!0},name:{type:"input",value:"",label:"晨检人",dataList:[],placeholder:"请输入",maxlength:20,clearable:!0},check_result:{type:"select",value:"全部",label:"晨检结果",dataList:[{label:"全部",value:"全部"},{label:"合格",value:"0"},{label:"不合格",value:"1"}],clearable:!0}},_={select_time:{type:"daterange",label:"晨检时间",value:b,clearable:!1},org_id:{type:"organizationSelect",value:"",label:"组织名称",listNameKey:"name",listValueKey:"id",checkStrictly:!0,multiple:!1,collapseTags:!0,clearable:!0},name:{type:"input",value:"",label:"晨检人",dataList:[],placeholder:"请输入",maxlength:20,clearable:!0},is_check:{type:"select",value:"全部",label:"晨检状态",dataList:[{label:"全部",value:"全部"},{label:"已晨检",value:!0},{label:"未晨检",value:!1}],clearable:!0}},f=[{label:"晨检时间",key:"check_time"},{label:"组织名称",key:"org_name"},{label:"晨检人姓名",key:"name"},{label:"晨检结果",key:"check_result_alias",type:"slot",slotName:"checkResult"},{label:"体温",key:"temperature",type:"slot",slotName:"temperature"},{label:"手部识别结果",key:"hand_result"},{label:"是否有腹泻和咽喉炎症",key:"risk_type_one_alias"},{label:"健康证是否有效",key:"health_certificate_status_alias"},{label:"不合格原因",key:"remark"},{label:"图片",key:"images",type:"slot",slotName:"images"}],k=[{label:"晨检时间",key:"check_time"},{label:"晨检人姓名",key:"name"},{label:"晨检状态",key:"check_status",type:"slot",slotName:"checkStatus"},{label:"晨检次数",key:"count"},{label:"合格数",key:"qualified_count"},{label:"不合格数",key:"unqualified_count"}],v={date_type:{type:"select",value:"reserved_time",label:"",width:"120px",dataList:[{label:"留样时间",value:"reserved_time"},{label:"入柜时间",value:"entry_date"},{label:"离柜时间",value:"exit_time"}],clearable:!1},selecttime:{type:"daterange",filterable:!0,defaultExpandAll:!0,clearable:!1,label:"",value:c,placeholder:"请选择",dataList:[]},org_id:{type:"organizationSelect",value:"",label:"组织名称",listNameKey:"name",listValueKey:"id",checkStrictly:!0,multiple:!1,collapseTags:!0},meal_type:{type:"select",value:[],clearable:!0,label:"留样餐段",multiple:!0,collapseTags:!0,dataList:r(n["a"])},menu_type:{type:"select",value:"",label:"留样菜谱",placeholder:"请选择",dataList:[],clearable:!0},food_name:{type:"input",label:"菜品",clearable:!0,value:"",placeholder:"请输入",maxlength:20},reserved_user:{type:"input",value:"",label:"留样员",placeholder:"请输入"},sample_exit_user:{type:"input",value:"",label:"取样员",placeholder:"请输入"},sample_entry_user:{type:"input",value:"",label:"入柜员",placeholder:"请输入"},reserved_status:{type:"select",value:"all",label:"留样状态",dataList:[{label:"全部",value:"all"},{label:"已留样",value:"reserved"},{label:"未留样",value:"not_reserved"}]},entry_cupboard:{type:"select",value:"all",label:"入柜状态",dataList:[{label:"全部",value:"all"},{label:"是",value:!0},{label:"否",value:!1}]},entry_device_ids:{type:"select",value:[],label:"入柜设备",dataList:[],multiple:!0,placeholder:"请选择",clearable:!0,listNameKey:"device_name",listValueKey:"device_no"}},g=[{label:"留样时间",key:"reserved_time",width:"180px"},{label:"所属组织",key:"org_name"},{label:"所属菜谱",key:"menu_name"},{label:"餐段",key:"meal_type_alias"},{label:"菜品",key:"food_name",showTooltip:!0,width:"130px"},{label:"留样状态",key:"reserved_status_alias"},{label:"留样数量",key:"food_count"},{label:"留样重量",key:"food_weight",type:"slot",slotName:"foodWeight"},{label:"是否入柜",key:"entry_cupboard",type:"slot",slotName:"entryCupboard"},{label:"入柜时间",key:"entry_cupboard_time",width:"180px"},{label:"存放时长",key:"store_time",width:"180px"},{label:"离柜时间",key:"exit_cupboard_time",width:"180px"},{label:"入柜设备",key:"entry_device"},{label:"当前柜内温度",key:"temperature",type:"slot",slotName:"temperature"},{label:"留样员",key:"reserved_user_name",type:"slot",slotName:"reservedUserName",width:"180px",showTooltip:!0},{label:"入柜员",key:"sample_entry_user",type:"slot",slotName:"sampleEntryUser",width:"180px",showTooltip:!0},{label:"取样员",key:"sample_exit_user",type:"slot",slotName:"sampleExitUser",width:"180px",showTooltip:!0},{label:"未入柜原因",key:"not_entry_reason",type:"slot",slotName:"notEntryReason",showTooltip:!0,width:"180px"},{label:"留样照片",key:"operation",type:"slot",slotName:"operation",fixed:"right"}]}}]);