(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-order-ConsumptionAutoSelling","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-meal-report-MealPackageRule","view-merchant-order-RechargeOrder","view-merchant-order-RefundOrder","view-merchant-report-report-management-FoodSaleRanking"],{7613:function(e,t,r){"use strict";r("ca83")},"87ac":function(e,t,r){"use strict";var n=r("ed08"),a=r("2f62");function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function o(e,t){return d(e)||l(e,t)||c(e,t)||s()}function s(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function c(e,t){if(e){if("string"==typeof e)return u(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?u(e,t):void 0}}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function l(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,i,o,s=[],c=!0,u=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(e){u=!0,a=e}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(u)throw a}}return s}}function d(e){if(Array.isArray(e))return e}function f(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */f=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},s=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function l(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,r){return e[t]=r}}function d(e,t,r,n){var i=t&&t.prototype instanceof b?t:b,o=Object.create(i.prototype),s=new D(n||[]);return a(o,"_invoke",{value:E(e,r,s)}),o}function h(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var p="suspendedStart",m="suspendedYield",g="executing",y="completed",v={};function b(){}function _(){}function w(){}var S={};l(S,s,(function(){return this}));var O=Object.getPrototypeOf,x=O&&O(O(M([])));x&&x!==r&&n.call(x,s)&&(S=x);var k=w.prototype=b.prototype=Object.create(S);function L(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function C(e,t){function r(a,o,s,c){var u=h(e[a],e,o);if("throw"!==u.type){var l=u.arg,d=l.value;return d&&"object"==i(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,s,c)}),(function(e){r("throw",e,s,c)})):t.resolve(d).then((function(e){l.value=e,s(l)}),(function(e){return r("throw",e,s,c)}))}c(u.arg)}var o;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){r(e,n,t,a)}))}return o=o?o.then(a,a):a()}})}function E(t,r,n){var a=p;return function(i,o){if(a===g)throw Error("Generator is already running");if(a===y){if("throw"===i)throw o;return{value:e,done:!0}}for(n.method=i,n.arg=o;;){var s=n.delegate;if(s){var c=j(s,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===p)throw a=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=g;var u=h(t,r,n);if("normal"===u.type){if(a=n.done?y:m,u.arg===v)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(a=y,n.method="throw",n.arg=u.arg)}}}function j(t,r){var n=r.method,a=t.iterator[n];if(a===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,j(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=h(a,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var o=i.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,v):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function P(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function F(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function D(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(P,this),this.reset(!0)}function M(t){if(t||""===t){var r=t[s];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,o=function r(){for(;++a<t.length;)if(n.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(i(t)+" is not iterable")}return _.prototype=w,a(k,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:_,configurable:!0}),_.displayName=l(w,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===_||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,l(e,u,"GeneratorFunction")),e.prototype=Object.create(k),e},t.awrap=function(e){return{__await:e}},L(C.prototype),l(C.prototype,c,(function(){return this})),t.AsyncIterator=C,t.async=function(e,r,n,a,i){void 0===i&&(i=Promise);var o=new C(d(e,r,n,a),i);return t.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},L(k),l(k,u,"Generator"),l(k,s,(function(){return this})),l(k,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=M,D.prototype={constructor:D,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(F),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(n,a){return s.type="throw",s.arg=t,r.next=n,a&&(r.method="next",r.arg=e),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var c=n.call(o,"catchLoc"),u=n.call(o,"finallyLoc");if(c&&u){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),F(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;F(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:M(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),v}},t}function h(e,t,r,n,a,i,o){try{var s=e[i](o),c=s.value}catch(e){return void r(e)}s.done?t(c):Promise.resolve(c).then(n,a)}function p(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){h(i,n,a,o,s,"next",e)}function s(e){h(i,n,a,o,s,"throw",e)}o(void 0)}))}}function m(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function g(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?m(Object(r),!0).forEach((function(t){y(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function y(e,t,r){return(t=v(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function v(e){var t=b(e,"string");return"symbol"==i(t)?t:t+""}function b(e,t){if("object"!=i(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}t["a"]={data:function(){return{currentTableSetting:[],dialogPrintVisible:!1}},computed:g({},Object(a["c"])(["userInfo"])),mounted:function(){},methods:{initPrintSetting:function(){var e=this;return p(f().mark((function t(){var r,a;return f().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return r=[],t.next=3,e.getPrintSettingInfo();case 3:r=t.sent;try{r=r?Object(n["E"])(r):Object(n["E"])(e.tableSetting)}catch(i){r=Object(n["E"])(e.tableSetting)}r.length<12?(a=Object(n["m"])(e.tableSetting,r),a=e.deleteWidthKey(a),e.currentTableSetting=a):e.currentTableSetting=Object(n["m"])(e.tableSetting,r);case 6:case"end":return t.stop()}}),t)})))()},getPrintSettingInfo:function(){var e=this;return p(f().mark((function t(){var r,a,i,s,c;return f().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return r=null,t.next=3,Object(n["Z"])(e.$apis.apiBackgroundAdminAccountGetAccountPrintInfoPost({id:e.userInfo.account_id,print_key:e.printType}));case 3:if(a=t.sent,i=o(a,2),s=i[0],c=i[1],!s){t.next=10;break}return e.$message.error(s.message),t.abrupt("return",r);case 10:return 0===c.code?r=c.data:e.$message.error(c.msg),t.abrupt("return",r);case 12:case"end":return t.stop()}}),t)})))()},setPrintSettingInfo:function(e,t){var r=this;return p(f().mark((function a(){var i,s,c,u;return f().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,Object(n["Z"])(r.$apis.apiBackgroundAdminAccountSetAccountPrintInfoPost(g({id:r.userInfo.account_id,print_key:r.printType,print_list:e},t)));case 2:if(i=a.sent,s=o(i,2),c=s[0],u=s[1],!c){a.next=9;break}return r.$message.error(c.message),a.abrupt("return");case 9:0===u.code?r.$message.success("设置成功"):r.$message.error(u.msg);case 10:case"end":return a.stop()}}),a)})))()},openPrintSetting:function(){this.dialogPrintVisible=!0},confirmPrintDialog:function(e,t){var r=this;return p(f().mark((function a(){var i;return f().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(!e){a.next=6;break}return i=Object(n["f"])(e),i.length<12&&(i=r.deleteWidthKey(i)),a.next=5,r.setPrintSettingInfo(i,t);case 5:r.currentTableSetting=i;case 6:case"end":return a.stop()}}),a)})))()},deleteWidthKey:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"children";function r(e){e.map((function(e){e.width&&delete e.width,e.minWidth&&delete e.minWidth,e[t]&&e[t].length>0&&r(e[t])}))}return r(e),e},indexMethod:function(e){return(this.page-1)*this.pageSize+(e+1)},setCollectData:function(e){var t=this;this.collect.forEach((function(r){e.data.collect&&void 0!==e.data.collect[r.key]&&t.$set(r,"value",e.data.collect[r.key])}))},setSummaryData:function(e){var t=e.data.collect;t[this.tableSetting[0].key]="合计",this.tableData.length&&this.tableData.push(t)}},beforeDestroy:function(){}}},"8c85":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"ConsumptionOrder container-wrapper"},[t("refresh-tool",{on:{refreshPage:e.refreshHandle}}),t("search-form",{ref:"searchRef",attrs:{loading:e.isLoading,"form-setting":e.searchForm,"label-width":"105px"},on:{search:e.searchHandle,reset:e.resetHandler},scopedSlots:e._u([{key:"perv",fn:function(){return[t("div",{staticClass:"searchref_top"},[t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_order.vending_machine.order_list"],expression:"['background_order.vending_machine.order_list']"}],class:{active:0===e.current},on:{click:function(t){return e.tabHandler(0)}}},[e._v("消费订单")]),t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_order.vending_machine.refund_order_list"],expression:"['background_order.vending_machine.refund_order_list']"}],class:{active:1===e.current},on:{click:function(t){return e.tabHandler(1)}}},[e._v("退款订单")]),t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_order.vending_machine.food_sale_summary"],expression:"['background_order.vending_machine.food_sale_summary']"}],class:{active:2===e.current},on:{click:function(t){return e.tabHandler(2)}}},[e._v("销售统计")])],1)]},proxy:!0}])}),t("div",{staticClass:"table-wrapper"},[t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[e._v("数据列表")]),t("div",{staticClass:"align-r"},[2===e.current?t("button-icon",{attrs:{color:"plain"},on:{click:e.gotoPrint}},[e._v("打印")]):e._e(),t("button-icon",{attrs:{color:"plain"},on:{click:e.openPrintSetting}},[e._v("报表设置")]),0===e.current?t("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_order.vending_machine.order_list_export"],expression:"['background_order.vending_machine.order_list_export']"}],attrs:{color:"plain",type:"export"},on:{click:e.handleExport}},[e._v("导出报表")]):e._e(),1===e.current?t("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_order.vending_machine.refund_order_list_export"],expression:"['background_order.vending_machine.refund_order_list_export']"}],attrs:{color:"plain",type:"export"},on:{click:e.handleExport}},[e._v("导出报表")]):e._e(),2===e.current?t("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_order.vending_machine.food_sale_summary_export"],expression:"['background_order.vending_machine.food_sale_summary_export']"}],attrs:{color:"plain",type:"export"},on:{click:e.handleExport}},[e._v("导出报表")]):e._e()],1)]),t("div",{staticClass:"table-content"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],attrs:{data:e.tableData,stripe:"","empty-text":e.isFirstSearch?"暂无数据，请查询":"","header-row-class-name":"ps-table-header-row","span-method":e.collectSpanMethod},on:{"selection-change":e.handleOrderSelectionChange,"sort-change":e.sortChange}},e._l(e.currentTableSetting,(function(r,n){return t("table-column",{key:n,attrs:{col:r},scopedSlots:e._u([{key:"index",fn:function(t){var r=t.row,n=t.index;return[e._v(" "+e._s(e.getPageIndex(n,r))+" ")]}},{key:"operation",fn:function(r){var n=r.row;return[t("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.gotoDetail(n)}}},[e._v("详情")]),t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_order.order_payment.instore_refund"],expression:"['background_order.order_payment.instore_refund']"}],staticClass:"ps-text",attrs:{type:"text",size:"small",disabled:!n.can_refund},on:{click:function(t){return e.openRefundDialog(n)}}},[e._v("退款")])]}}],null,!0)})})),1),0===e.current||1===e.current?t("ul",{staticClass:"total"},[t("li",[e._v(" "+e._s(0===e.current?"合计笔数:":"退款笔数")+" "),t("span",[e._v(e._s(e.total_count))])]),t("li",[e._v(" "+e._s(0===e.current?"合计订单金额:￥":"合计退款金额:￥")+" "),t("span",[e._v(e._s(e._f("formatMoney")(e.total_amount)))])]),0===e.current?t("li",[e._v(" 合计实收金额:￥ "),t("span",[e._v(e._s(e._f("formatMoney")(e.total_origin_amount)))])]):e._e(),t("li",[e._v(" 手续费合计:￥ "),t("span",[e._v(e._s(e._f("formatMoney")(e.total_rate_fee)))])])]):e._e(),2===e.current?t("div",{staticClass:"ps-text m-t-20"},[e._v("注：该表基于商品售卖原金额进行计算，仅供参考，不能作为最终营业额的依据。")]):e._e(),t("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[t("el-pagination",{staticClass:"ps-text",attrs:{"current-page":e.page,"page-sizes":[10,20,50,100,500],"page-size":e.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)]),t("div",{staticClass:"refund-confirm"},[t("el-dialog",{attrs:{top:"200px",title:"退款",visible:e.outerVisible,width:"800px",customClass:"ps-dialog"},on:{"update:visible":function(t){e.outerVisible=t}}},[t("el-table",{ref:"refundTable",staticStyle:{width:"100%"},attrs:{"cell-style":{textAlign:"center"},"header-cell-style":{textAlign:"center"},data:e.refundData.food_list,border:"","row-key":"food_id"},on:{"selection-change":e.handleRefundSelectionChange}},["part"===e.refundMethod?t("el-table-column",{attrs:{type:"selection","show-overflow-tooltip":!0,"reserve-selection":!0,width:"55",selectable:e.selectDisabled}}):e._e(),t("el-table-column",{attrs:{prop:"food_url",label:"图片"},scopedSlots:e._u([{key:"default",fn:function(e){return[t("img",{staticStyle:{width:"60px"},attrs:{src:e.row.food_url?e.row.food_url:"https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png",alt:""}})]}}])}),t("el-table-column",{attrs:{prop:"food_name",label:"商品名称"}}),t("el-table-column",{attrs:{prop:"raw_fee",label:"销售价格"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",[e._v("￥"+e._s(e._f("formatMoney")(r.row.raw_fee)))])]}}])}),t("el-table-column",{attrs:{prop:"count",label:"数量"}}),t("el-table-column",{attrs:{prop:"cargo_lane",label:"货道"}}),t("el-table-column",{attrs:{prop:"buy_price",label:"消费金额"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",[e._v("￥"+e._s(e._f("formatMoney")(r.row.all_raw_fee)))])]}}])}),t("el-table-column",{attrs:{prop:"food_status",label:"退款状态"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",[e._v(e._s("ORDER_REFUND_SUCCESS"===r.row.food_status?"退款成功":"未退款"))])]}}])})],1),t("div",{staticClass:"refund-radio"},[t("el-radio",{staticClass:"ps-radio",attrs:{"text-color":"#FF9B45",label:"all"},on:{change:e.changeRefundType},model:{value:e.refundMethod,callback:function(t){e.refundMethod=t},expression:"refundMethod"}},[e._v("全额退款")]),t("el-radio",{staticClass:"ps-radio",attrs:{"text-color":"#FF9B45",label:"part"},on:{change:e.changeRefundType},model:{value:e.refundMethod,callback:function(t){e.refundMethod=t},expression:"refundMethod"}},[e._v("部分退款")])],1),t("div",{staticClass:"refund-info"},[t("el-form",{ref:"dialogFormRef",staticClass:"attendance-form",attrs:{model:e.dialogForm,"status-icon":"",rules:e.dialogFormRules,"label-width":"100px",inline:""},nativeOn:{submit:function(e){e.preventDefault()}}},[t("div",{staticClass:"inline-box m-r-20"},[t("span",[e._v("可退款余额：")]),"all"===e.refundMethod?t("span",[e._v(e._s(e._f("formatMoney")(e.refundData.net_fee)))]):e._e(),"part"===e.refundMethod?t("span",[e._v("<"+e._s(e.refundData.part_net_fee))]):e._e()]),"all"===e.refundMethod?t("div",{staticClass:"inline-box"},[t("span",[e._v("退款金额：")]),t("span",[e._v(e._s(e._f("formatMoney")(e.refundData.pay_fee)))])]):e._e(),"part"===e.refundMethod&&"PushiPay"!==e.refundData.payway?t("div",{staticClass:"inline-box"},[t("el-form-item",{attrs:{label:"退款金额：",prop:"refundMoney"}},[t("el-input",{staticClass:"w-180 ps-input",attrs:{placeholder:"请输入退款金额"},model:{value:e.dialogForm.refundMoney,callback:function(t){e.$set(e.dialogForm,"refundMoney",t)},expression:"dialogForm.refundMoney"}})],1)],1):e._e(),"part"===e.refundMethod&&"PushiPay"===e.refundData.payway?t("div",[t("el-form-item",{attrs:{label:"补贴钱包：",prop:"refundSubsidyMoney"}},[t("el-input",{staticClass:"w-180 ps-input",attrs:{placeholder:"可退金额<"+e.refundData.part_subsidy_fee,disabled:!Number(e.refundData.part_subsidy_fee)},model:{value:e.dialogForm.refundSubsidyMoney,callback:function(t){e.$set(e.dialogForm,"refundSubsidyMoney",t)},expression:"dialogForm.refundSubsidyMoney"}})],1),t("el-form-item",{attrs:{label:"储值钱包：",prop:"refundWalletMoney"}},[t("el-input",{staticClass:"w-180 ps-input",attrs:{placeholder:"可退金额<"+e.refundData.part_wallet_fee,disabled:!Number(e.refundData.part_wallet_fee)},model:{value:e.dialogForm.refundWalletMoney,callback:function(t){e.$set(e.dialogForm,"refundWalletMoney",t)},expression:"dialogForm.refundWalletMoney"}})],1),t("el-form-item",{attrs:{label:"赠送钱包：",prop:"refundComplimentaryMoney"}},[t("el-input",{staticClass:"w-180 ps-input",attrs:{placeholder:"可退金额<"+e.refundData.part_complimentary_fee,disabled:!Number(e.refundData.part_complimentary_fee)},model:{value:e.dialogForm.refundComplimentaryMoney,callback:function(t){e.$set(e.dialogForm,"refundComplimentaryMoney",t)},expression:"dialogForm.refundComplimentaryMoney"}})],1)],1):e._e()])],1),t("el-dialog",{attrs:{width:"30%",title:"温馨提示",customClass:"ps-dialog",visible:e.innerVisible,"append-to-body":"",top:"280px"},on:{"update:visible":function(t){e.innerVisible=t}}},["all"===e.refundMethod?t("p",{staticClass:"twoRefund",staticStyle:{"font-size":"20px"}},[e._v(" 确定要对该订单进行退款吗 ")]):t("p",{staticClass:"twoRefund",staticStyle:{"font-size":"20px"}},[e._v(" 确定要对该订单进行 "),t("span",{staticStyle:{"font-weight":"bold"}},[e._v("部分退款吗?")])]),t("p",{staticClass:"twoRefund",staticStyle:{color:"#E0364C"}},[e._v("温馨提示: 确定后不可撤销")]),t("div",{staticClass:"footer-btn",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-cancel-btn",on:{click:function(t){e.innerVisible=!1}}},[e._v("取消")]),t("el-button",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoadingRefund,expression:"isLoadingRefund"}],staticClass:"ps-btn",attrs:{disabled:e.dialogLoading},on:{click:e.handleConfirm}},[e._v("确定")])],1)]),t("div",{attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-cancel-btn",on:{click:function(t){e.outerVisible=!1}}},[e._v("取 消")]),t("el-button",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoadingRefund,expression:"isLoadingRefund"}],staticClass:"ps-btn",on:{click:e.handleRefund}},[e._v("确 定")])],1)],1)],1),e.dialogPrintVisible?t("print-setting",{attrs:{extraParams:{printType:e.printType},tableSetting:e.tableSetting,defaultCheckedSetting:e.currentTableSetting,show:e.dialogPrintVisible},on:{"update:show":function(t){e.dialogPrintVisible=t},confirm:e.confirmPrintDialog}}):e._e(),t("print-ticket",{ref:"printTicket",attrs:{isshow:e.printTicketVisible,type:"0"==e.current?"onScence":"order",title:"小票打印","select-list-id":e.selectListId,"print-type":e.printTypeList,confirm:e.searchHandle},on:{"update:isshow":function(t){e.printTicketVisible=t}}})],1)},a=[],i=r("d1eb"),o=r("ed08"),s=r("f63a"),c=r("f1bf"),u=r("7bfc"),l=r("87ac");function d(e){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},d(e)}function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function h(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach((function(t){p(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function p(e,t,r){return(t=m(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function m(e){var t=g(e,"string");return"symbol"==d(t)?t:t+""}function g(e,t){if("object"!=d(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=d(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function y(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */y=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function l(e,t,r,n){var i=t&&t.prototype instanceof b?t:b,o=Object.create(i.prototype),s=new D(n||[]);return a(o,"_invoke",{value:E(e,r,s)}),o}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=l;var h="suspendedStart",p="suspendedYield",m="executing",g="completed",v={};function b(){}function _(){}function w(){}var S={};u(S,o,(function(){return this}));var O=Object.getPrototypeOf,x=O&&O(O(M([])));x&&x!==r&&n.call(x,o)&&(S=x);var k=w.prototype=b.prototype=Object.create(S);function L(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function C(e,t){function r(a,i,o,s){var c=f(e[a],e,i);if("throw"!==c.type){var u=c.arg,l=u.value;return l&&"object"==d(l)&&n.call(l,"__await")?t.resolve(l.__await).then((function(e){r("next",e,o,s)}),(function(e){r("throw",e,o,s)})):t.resolve(l).then((function(e){u.value=e,o(u)}),(function(e){return r("throw",e,o,s)}))}s(c.arg)}var i;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){r(e,n,t,a)}))}return i=i?i.then(a,a):a()}})}function E(t,r,n){var a=h;return function(i,o){if(a===m)throw Error("Generator is already running");if(a===g){if("throw"===i)throw o;return{value:e,done:!0}}for(n.method=i,n.arg=o;;){var s=n.delegate;if(s){var c=j(s,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===h)throw a=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=m;var u=f(t,r,n);if("normal"===u.type){if(a=n.done?g:p,u.arg===v)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(a=g,n.method="throw",n.arg=u.arg)}}}function j(t,r){var n=r.method,a=t.iterator[n];if(a===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,j(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=f(a,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var o=i.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,v):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function P(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function F(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function D(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(P,this),this.reset(!0)}function M(t){if(t||""===t){var r=t[o];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,i=function r(){for(;++a<t.length;)if(n.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(d(t)+" is not iterable")}return _.prototype=w,a(k,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:_,configurable:!0}),_.displayName=u(w,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===_||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,u(e,c,"GeneratorFunction")),e.prototype=Object.create(k),e},t.awrap=function(e){return{__await:e}},L(C.prototype),u(C.prototype,s,(function(){return this})),t.AsyncIterator=C,t.async=function(e,r,n,a,i){void 0===i&&(i=Promise);var o=new C(l(e,r,n,a),i);return t.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},L(k),u(k,c,"Generator"),u(k,o,(function(){return this})),u(k,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=M,D.prototype={constructor:D,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(F),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(n,a){return s.type="throw",s.arg=t,r.next=n,a&&(r.method="next",r.arg=e),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var c=n.call(o,"catchLoc"),u=n.call(o,"finallyLoc");if(c&&u){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),F(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;F(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:M(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),v}},t}function v(e,t){return O(e)||S(e,t)||_(e,t)||b()}function b(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _(e,t){if(e){if("string"==typeof e)return w(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?w(e,t):void 0}}function w(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function S(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,i,o,s=[],c=!0,u=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(e){u=!0,a=e}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(u)throw a}}return s}}function O(e){if(Array.isArray(e))return e}function x(e,t,r,n,a,i,o){try{var s=e[i](o),c=s.value}catch(e){return void r(e)}s.done?t(c):Promise.resolve(c).then(n,a)}function k(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){x(i,n,a,o,s,"next",e)}function s(e){x(i,n,a,o,s,"throw",e)}o(void 0)}))}}var L={name:"ConsumptionAutoSelling",components:{PrintTicket:c["a"]},mixins:[i["a"],s["a"],l["a"]],data:function(){var e=this,t=function(t,r,n){var a,i=/^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;"refundWalletMoney"===t.field?a=e.refundData.part_wallet_fee:"refundSubsidyMoney"===t.field?a=e.refundData.part_subsidy_fee:"refundComplimentaryMoney"===t.field?a=e.refundData.part_complimentary_fee:"refundMoney"===t.field&&(a=e.refundData.part_net_fee),r?0===Number(r)?n(new Error("金额不能为0")):r>=Number(a)?n(new Error("金额不能大于等于可退金额")):i.test(r)?n():n(new Error("金额格式有误")):n()};return{current:0,sceneSearchForm:Object(o["f"])(u["CONSUMPTION_SCENE_AUTO_SEARCH"]),reservationSearchForm:Object(o["f"])(u["REFUND_ORDER_AUTO_SEARCH"]),collectSearchForm:Object(o["f"])(u["COLLECT_ORDER_AUTO_SEARCH"]),searchForm:{},tableData:[],isLoading:!1,isLoadingRefund:!1,pageSize:10,totalCount:0,page:1,total_count:0,total_amount:0,total_origin_amount:0,total_rate_fee:0,refundMethod:"all",dialogForm:{refundMoney:"",refundWalletMoney:"",refundSubsidyMoney:"",refundComplimentaryMoney:""},dialogFormRules:{refundMoney:[{required:!0,validator:t,trigger:"change"}],refundWalletMoney:[{required:!0,validator:t,trigger:"change"}],refundSubsidyMoney:[{required:!0,validator:t,trigger:"change"}],refundComplimentaryMoney:[{required:!0,validator:t,trigger:"change"}]},refundFoodId:[],outerVisible:!1,innerVisible:!1,dialogLoading:!1,refundData:[],refundStatus:["ORDER_REFUNDING","ORDER_REFUND_SUCCESS"],tableSetting:[],sceneTableSetting:Object(o["f"])(u["CONSUMPTION_SCENE_AUTO_TABLE"]),reservationSableSetting:Object(o["f"])(u["REFUND_ORDER_AUTO_TABLE"]),collectionTableSetting:Object(o["f"])(u["COLLECT_ORDER_AUTO_TABLE"]),currentTableSetting:[],dialogPrintVisible:!1,printType:"VendingMachineOrder",printTicketVisible:!1,refundOrderIds:[],selectListId:[],totalServeFee:0,printTypeList:[{label:"后厨单",value:"kitchen"},{label:"结账单",value:"bill"}],isFirstSearch:!0,deviceList:[]}},created:function(){this.initLoad()},watch:{"collectSearchForm.consume_organization_ids.value":function(e){this.getDeviceList(e)}},mounted:function(){this.getLevelNameList(),this.getpayList(),this.getDeviceList(),this.getDeviceType()},methods:{initLoad:function(){var e=this;1===this.current?(this.searchForm=this.reservationSearchForm,this.currentTableSetting=Object(o["f"])(this.reservationSableSetting),this.isFirstSearch||this.getConsumptionList()):2===this.current?(this.searchForm=this.collectSearchForm,this.tableSetting=Object(o["f"])(this.collectionTableSetting),this.currentTableSetting=Object(o["f"])(this.collectionTableSetting),this.initPrintSetting(),this.isFirstSearch||this.getCollectList()):(this.searchForm=this.sceneSearchForm,this.currentTableSetting=Object(o["f"])(this.sceneTableSetting),this.isFirstSearch||this.getOnSceneList()),this.$nextTick((function(){Reflect.has(e.$refs,"printTicket")&&e.$refs.printTicket.setPrintType("bill")}))},searchHandle:Object(o["d"])((function(e){e&&"search"===e&&(this.printTicketVisible=!1,this.page=1,this.isFirstSearch=!1,this.initLoad())}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.tableData=[],this.page=1,this.isFirstSearch=!0,this.initLoad()},tabHandler:function(e){var t=this;if(this.isLoading)return this.$message.error("页面更新中请稍后再切换");this.current=e,this.page=1,this.isFirstSearch=!0,this.$nextTick((function(){0===e?(t.printType="VendingMachineOrder",t.searchForm=t.sceneSearchForm,t.tableSetting=t.sceneTableSetting,t.currentTableSetting=Object(o["f"])(t.sceneTableSetting),t.initPrintSetting(),t.getOnSceneList()):1===e?(t.printType="VendingMachineRefundOrder",t.searchForm=t.reservationSearchForm,t.tableSetting=t.reservationSableSetting,t.currentTableSetting=Object(o["f"])(t.reservationSableSetting),t.initPrintSetting(),t.getConsumptionList()):(t.printType="VendingMachineFoodSaleSummary",t.searchForm=t.collectSearchForm,t.tableSetting=t.collectionTableSetting,t.currentTableSetting=Object(o["f"])(t.collectionTableSetting),t.searchForm.device_name_list.dataList=Object(o["f"])(t.deviceList),t.initPrintSetting(),t.getCollectList()),Reflect.has(t.$refs,"printTicket")&&t.$refs.printTicket.setPrintType(0===e?"bill":"")}))},getConsumptionList:function(){var e=this;return k(y().mark((function t(){var r,n,a,i,s;return y().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,r=Object(u["getRequestParams"])(e.searchForm,e.page,e.pageSize),t.next=4,Object(o["Z"])(e.$apis.apiBackgroundOrderVendingMachineRefundOrderListPost(r));case 4:if(n=t.sent,a=v(n,2),i=a[0],s=a[1],e.isLoading=!1,!i){t.next=12;break}return e.$message.error(i.message),t.abrupt("return");case 12:0===s.code?(e.tableData=s.data.results,e.totalCount=s.data.count,e.total_amount=s.data.total_amount,e.total_count=s.data.count,e.total_origin_amount=s.data.total_pay_amount,e.total_rate_fee=s.data.total_rate_fee,e.totalServeFee=s.data.total_fuwu_fee||0):e.$message.error(s.msg);case 13:case"end":return t.stop()}}),t)})))()},getOnSceneList:function(){var e=this;return k(y().mark((function t(){var r,n,a,i,s;return y().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,r=Object(u["getRequestParams"])(e.searchForm,e.page,e.pageSize),t.next=4,Object(o["Z"])(e.$apis.apiBackgroundOrderVendingMachineOrderListPost(r));case 4:if(n=t.sent,a=v(n,2),i=a[0],s=a[1],e.isLoading=!1,!i){t.next=12;break}return e.$message.error(i.message),t.abrupt("return");case 12:0===s.code?(e.tableData=s.data.results,e.totalCount=s.data.count,e.total_amount=s.data.total_amount,e.total_count=s.data.count,e.total_origin_amount=s.data.total_pay_amount,e.total_rate_fee=s.data.total_rate_fee,e.totalServeFee=s.data.total_fuwu_fee||0):e.$message.error(s.msg);case 13:case"end":return t.stop()}}),t)})))()},getCollectList:function(){var e=this;return k(y().mark((function t(){var r,n,a,i,s;return y().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,r=Object(u["getRequestParams"])(e.searchForm,e.page,e.pageSize),t.next=4,Object(o["Z"])(e.$apis.apiBackgroundOrderVendingMachineFoodSaleSummaryPost(r));case 4:if(n=t.sent,a=v(n,2),i=a[0],s=a[1],e.isLoading=!1,!i){t.next=12;break}return e.$message.error(i.message),t.abrupt("return");case 12:0===s.code?(e.tableData=e.setCollectData(s.data.results,s.data.summary_data),e.totalCount=s.data.count):e.$message.error(s.msg);case 13:case"end":return t.stop()}}),t)})))()},handleSizeChange:function(e){this.pageSize=e,this.initLoad()},handleCurrentChange:function(e){this.page=e,this.initLoad()},getLevelNameList:function(){var e=this;return k(y().mark((function t(){return y().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.sceneTableSetting=Object(o["f"])(u["CONSUMPTION_SCENE_AUTO_TABLE"]),e.reservationSableSetting=Object(o["f"])(u["REFUND_ORDER_AUTO_TABLE"]),0===e.current?e.tableSetting=e.sceneTableSetting:1===e.current&&(e.tableSetting=e.reservationSableSetting),e.initPrintSetting();case 4:case"end":return t.stop()}}),t)})))()},getpayList:function(){var e=this;return k(y().mark((function t(){var r,n,a;return y().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundReportCenterDataReportGetPayInfoPost();case 2:r=t.sent,0===r.code?(n=[],a=[],r.data.result.payways.forEach((function(e){Object.keys(e).forEach((function(t){return n.push({label:e[t],value:t})}))})),r.data.result.sub_payways.forEach((function(e){Object.keys(e).forEach((function(t){return a.push({label:e[t],value:t})}))})),e.sceneSearchForm.payway_list.dataList=n,e.sceneSearchForm.sub_payway_list.dataList=a):e.$message.error(r.msg);case 4:case"end":return t.stop()}}),t)})))()},getDeviceList:function(e){var t=this;return k(y().mark((function r(){var n,a;return y().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return t.isLoading=!0,n={device_type:"ZDSHG",page:1,page_size:99999},e&&e.length>0&&(n.organization_id=e[0]),r.next=5,t.$apis.apiBackgroundDeviceDeviceListPost(n);case 5:a=r.sent,t.isLoading=!1,0===a.code?(t.deviceList=Object(o["f"])(a.data.results||[]),2===t.current?t.collectSearchForm.device_name_list.dataList=a.data.results||[]:t.sceneSearchForm.device_name_list.dataList=a.data.results||[]):t.$message.error(a.msg);case 8:case"end":return r.stop()}}),r)})))()},gotoDetail:function(e){this.$router.push({name:"MerchantConsumptionAutoDetail",query:{id:e.id,type:this.current,isAutoCell:!0}})},handleExport:function(){var e;e=1===this.current?"ExportAutoRefundOrder":0===this.current?"ExportAutoOrderConsumption":"ExportAutoOrderCollect";var t=Object(u["getRequestParams"])(this.searchForm,this.page,this.pageSize),r={type:e,params:t};this.exportHandle(r)},handleExportLive:function(){var e=Object(u["getRequestParams"])(this.searchForm,this.page,this.pageSize);1===this.current?e.export_order_types="reservation":e.export_order_types="instore";var t={type:"OrderPaymentLiveExport",url:"apiBackgroundOrderOrderPaymentLiveExportPost",params:e};this.exportHandle(t)},openRefundDialog:function(e){this.dialogForm={refundMoney:"",refundWalletMoney:"",refundSubsidyMoney:"",refundComplimentaryMoney:""},this.refundData=Object(o["f"])(e),this.refundData.part_wallet_fee=Object(o["i"])(Math.abs(this.refundData.part_wallet_fee)),this.refundData.part_subsidy_fee=Object(o["i"])(Math.abs(this.refundData.part_subsidy_fee)),this.refundData.part_complimentary_fee=Object(o["i"])(Math.abs(this.refundData.part_complimentary_fee)),this.refundData.part_net_fee=Object(o["i"])(Math.abs(this.refundData.part_net_fee)),this.outerVisible=!0,this.refundMethod="all"},changeRefundType:function(){"part"===this.refundMethod&&this.$refs.refundTable.clearSelection()},handleRefund:function(){var e=this;if("part"===this.refundMethod&&"PushiPay"===this.refundData.payway&&!this.dialogForm.refundWalletMoney&&!this.dialogForm.refundSubsidyMoney&&!this.dialogForm.refundComplimentaryMoney||"part"===this.refundMethod&&"PushiPay"!==this.refundData.payway&&!this.dialogForm.refundMoney)return this.$message.error("请输入退款金额");this.$refs.dialogFormRef.validate((function(t){t&&(e.innerVisible=!0)}))},handleConfirm:function(){var e={trade_no:this.refundData.trade_no};"all"===this.refundMethod?e.refund_fee=this.refundData.pay_fee:("PushiPay"===this.refundData.payway?(e.refund_wallet_fee=Object(o["Y"])(this.dialogForm.refundWalletMoney),e.refund_subsidy_fee=Object(o["Y"])(this.dialogForm.refundSubsidyMoney),e.refund_complimentary_fee=Object(o["Y"])(this.dialogForm.refundComplimentaryMoney)):e.refund_fee=Object(o["Y"])(this.dialogForm.refundMoney),e.third_food_record_ids=this.refundFoodId),this.orderRefund(e)},orderRefund:function(e){var t=this;return k(y().mark((function r(){var n,a,i,s,c,u,l;return y().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!t.dialogLoading){r.next=2;break}return r.abrupt("return");case 2:if(t.dialogLoading=!0,n=[],a=n[0],i=n[1],t.isLoadingRefund=!0,t.innerVisible=!1,!t.current){r.next=15;break}return r.next=9,Object(o["Z"])(t.$apis.apiBackgroundOrderOrderReservationReservationOrderRefundPost(e));case 9:s=r.sent,c=v(s,2),a=c[0],i=c[1],r.next=21;break;case 15:return r.next=17,Object(o["Z"])(t.$apis.apiBackgroundOrderOrderPaymentInstoreRefundPost(e));case 17:u=r.sent,l=v(u,2),a=l[0],i=l[1];case 21:if(t.isLoadingRefund=!1,t.dialogLoading=!1,!a){r.next=25;break}return r.abrupt("return",t.$message.error(a.message||"失败"));case 25:i&&0===i.code?(t.innerVisible=!1,t.outerVisible=!1,t.initLoad(),t.$message.success(i.msg)):t.$message.error(i.msg);case 26:case"end":return r.stop()}}),r)})))()},handleRefundSelectionChange:function(e){var t=this;this.dialogForm.refundMoney=0,this.refundFoodId=[],e.map((function(e){t.dialogForm.refundMoney+=e.real_fee,t.refundFoodId.push(e.id)})),this.dialogForm.refundMoney=Object(o["i"])(this.dialogForm.refundMoney)},selectDisabled:function(e,t){return!this.refundStatus.includes(e.refund_status)},getDeviceType:function(){var e=this;return k(y().mark((function t(){var r;return y().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundDeviceDeviceDeviceTypePost();case 2:r=t.sent,0===r.code?2!==e.current&&(e.sceneSearchForm.device_name_list.dataList=r.data):e.$message.error(r.msg);case 4:case"end":return t.stop()}}),t)})))()},handleOrderSelectionChange:function(e){var t=this;this.refundOrderIds=[],this.selectListId=e.map((function(e){return e.can_refund&&t.refundOrderIds.push(e.id),e.id}))},mulRefundHandle:function(){var e=this;return k(y().mark((function t(){return y().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.dialogLoading){t.next=2;break}return t.abrupt("return");case 2:if(e.selectListId.length){t.next=4;break}return t.abrupt("return",e.$message.error("请选择要退款的订单！"));case 4:if(e.refundOrderIds.length){t.next=6;break}return t.abrupt("return",e.$message.error("当前所选订单不存在可退款订单！"));case 6:e.dialogLoading=!0,e.$confirm("确定要将这些订单进行退款？","提示",{confirmButtonText:e.$t("dialog.confirm_btn"),cancelButtonText:e.$t("dialog.cancel_btn"),closeOnClickModal:!1,beforeClose:function(){var t=k(y().mark((function t(r,n,a){var i,o;return y().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if("confirm"!==r){t.next=18;break}if(n.confirmButtonLoading=!0,i={order_ids:e.refundOrderIds},!e.current){t.next=9;break}return t.next=6,e.$apis.apiBackgroundOrderOrderReservationReservationOrderRefundPost(i);case 6:o=t.sent,t.next=12;break;case 9:return t.next=11,e.$apis.apiBackgroundOrderOrderPaymentInstoreRefundPost(i);case 11:o=t.sent;case 12:e.dialogLoading=!1,0===o.code?(e.initLoad(),e.$message.success("操作成功，其中不可退款订单数".concat(e.selectListId.length-e.refundOrderIds.length,"笔"))):e.$message.error(o.msg),a(),n.confirmButtonLoading=!1,t.next=19;break;case 18:n.confirmButtonLoading||(e.dialogLoading=!1,a());case 19:case"end":return t.stop()}}),t)})));function r(e,r,n){return t.apply(this,arguments)}return r}()}).then((function(e){})).catch((function(e){}));case 8:case"end":return t.stop()}}),t)})))()},openDialog:function(){if(!this.selectListId.length)return this.$message.error("请先选择数据！");this.printTicketVisible=!0},getPageIndex:function(e,t){var r=t.index;return r&&"string"===typeof r&&-1!==r.indexOf("合计")?t.index:e+1+(this.page-1)*this.pageSize},resetHandler:function(){this.page=1,this.initLoad()},setCollectData:function(e,t){var r=this;if(e&&Array.isArray(e)&&e.length>0){var n=Object(o["f"])(u["COLLECT_ORDER_AUTO_TABLE"]),a=[{},{}];a.forEach((function(a,i){n.forEach((function(n){0===i&&(a[n.key]=r.getSumByKey(e,n.key)),1===i&&(a[n.key]=t["total_"+n.key]?t["total_"+n.key]:0),"index"===n.key&&(a[n.key]=0===i?"当页合计":"总合计")}))})),e=e.concat(a)}return e},getSumByKey:function(e,t){var r=0;return e&&Array.isArray(e)&&e.forEach((function(e){r=e[t]&&"name"!==t?r+e[t]:r})),r},collectSpanMethod:function(e){e.row,e.column;var t=e.rowIndex,r=e.columnIndex,n=this.tableData.length;if(2===this.current&&(t===n-1||t===n-2)){if(0===r)return{rowspan:1,colspan:3};if(1===r||2===r)return{rowspan:0,colspan:0}}},gotoPrint:function(){var e=Object(u["getRequestParams"])(this.searchForm),t=this.$router.resolve({name:"ConsumptionAutoSellingPrint",query:{print_type:this.printType,print_title:"售货柜订单销售统计",result_key:"results",api:"apiBackgroundOrderVendingMachineFoodSaleSummaryPost",show_print_header_and_footer:!0,show_summary:!1,table_setting:JSON.stringify(this.tableSetting),current_table_setting:JSON.stringify(this.currentTableSetting),push_summary:!1,isAutoCollect:!0,params:JSON.stringify(h(h({},e),{},{page:1,page_size:this.total?this.total:10}))}}),r=t.href;window.open(r,"_blank")},sortChange:function(e){e.column;var t=e.prop,r=e.order;switch(this.searchForm.sort_name=t,this.searchForm.sort_type="ascending"===r?"asc":"desc",t){case"sale_count":break;case"sale_money":break;case"real_sale_profit":break;default:break}this.getCollectList()}}},C=L,E=(r("7613"),r("2877")),j=Object(E["a"])(C,n,a,!1,null,"0e1e7986",null);t["default"]=j.exports},c9d9:function(e,t,r){"use strict";r.d(t,"a",(function(){return i})),r.d(t,"d",(function(){return o})),r.d(t,"b",(function(){return s})),r.d(t,"c",(function(){return c})),r.d(t,"e",(function(){return u})),r.d(t,"f",(function(){return l})),r.d(t,"g",(function(){return d}));var n=r("5a0c"),a=r("da92"),i=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],o=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],s={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},c=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],u=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],l=(n().subtract(7,"day").format("YYYY-MM-DD"),n().format("YYYY-MM-DD"),function(e){return e?"number"===typeof e?a["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:a["a"].divide(e,100).toFixed(2):"0.00"}),d=function(e){return a["a"].times(e,100)}},ca83:function(e,t,r){},d1eb:function(e,t,r){"use strict";t["a"]={data:function(){return{isDeactivated:!1}},activated:function(){this.isDeactivated&&this.initLoad&&(this.initLoad(),this.isDeactivated=!1)},deactivated:function(){this.isDeactivated=!0},methods:{},beforeDestroy:function(){}}}}]);