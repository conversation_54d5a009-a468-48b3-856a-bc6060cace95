(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-member-center-components-MemberKeyOrPermissDialog"],{"3bbf":function(e,t,r){"use strict";r("f655")},f3be:function(e,t,r){"use strict";r.r(t);var i=function(){var e=this,t=e._self._c;return t("dialog-message",{attrs:{show:e.visible,title:e.title,loading:e.isLoading,customClass:"ps-dialog",width:e.width},on:{"update:show":function(t){e.visible=t},"update:loading":function(t){e.isLoading=t},close:e.handleClose}},[t("el-form",{ref:"dialogFormRef",staticClass:"member-form",attrs:{model:e.dialogForm,"status-icon":"",rules:e.dialogFormRules,"label-width":"120px"},nativeOn:{submit:function(e){e.preventDefault()}}},["permission"!==e.dialogType||"add"!==e.type&&"edit"!==e.type?e._e():t("div",[t("el-form-item",{attrs:{label:"权限名称：",prop:"name"}},[t("el-input",{staticClass:"ps-input w-250",attrs:{maxlength:"5","show-word-limit":""},model:{value:e.dialogForm.name,callback:function(t){e.$set(e.dialogForm,"name",t)},expression:"dialogForm.name"}})],1),t("el-form-item",{attrs:{label:"图标：",prop:"img"}},[t("div",{staticClass:"ps-flex"},[t("el-upload",{directives:[{name:"loading",rawName:"v-loading",value:e.uploading,expression:"uploading"}],ref:"fileUpload",staticClass:"upload-w",attrs:{"element-loading-text":"上传中",action:e.serverUrl,data:e.uploadParams,"file-list":e.fileLists,"on-success":e.uploadSuccess,"before-upload":e.beforeFoodImgUpload,limit:1,multiple:!1,"show-file-list":!1,headers:e.headersOpts,accept:".jpeg,.jpg,.png,.bmp"}},[e.dialogForm.img?t("img",{staticClass:"avatar",attrs:{src:e.dialogForm.img}}):t("i",{staticClass:"el-icon-plus avatar-uploader-icon"})]),t("div",{staticClass:"inline-block upload-tips m-l-10"},[e._v(" 仅支持jpg,png格式，不超过5MB，长宽比为1:1的图片 ")])],1)]),t("el-form-item",{attrs:{label:"类型：",prop:"type"}},[t("el-select",{staticClass:"w-250",attrs:{placeholder:"请选择类型",disabled:"edit"===e.type},on:{change:e.chooseTypeChange},model:{value:e.dialogForm.type,callback:function(t){e.$set(e.dialogForm,"type",t)},expression:"dialogForm.type"}},e._l(e.typeList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),"association"==e.dialogForm.type?t("el-form-item",{attrs:{label:"关联链接：",prop:"link"}},[t("el-input",{staticClass:"ps-input w-250",model:{value:e.dialogForm.link,callback:function(t){e.$set(e.dialogForm,"link",t)},expression:"dialogForm.link"}})],1):e._e(),"fixed"==e.dialogForm.type?t("el-form-item",{attrs:{label:"键值：",prop:"label"}},[t("el-select",{staticClass:"w-250",attrs:{placeholder:"请选择标签",disabled:"edit"===e.type},model:{value:e.dialogForm.label,callback:function(t){e.$set(e.dialogForm,"label",t)},expression:"dialogForm.label"}},e._l(e.keyList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1):e._e(),t("el-form-item",{attrs:{label:"说明：",prop:"remark"}},[t("el-input",{staticClass:"ps-input w-250",attrs:{type:"textarea",rows:3,maxlength:"50","show-word-limit":""},model:{value:e.dialogForm.remark,callback:function(t){e.$set(e.dialogForm,"remark",t)},expression:"dialogForm.remark"}})],1)],1),"key"===e.dialogType?t("div",[t("el-form-item",{attrs:{label:"名称：",prop:"keyName"}},[t("el-input",{staticClass:"ps-input w-250",attrs:{maxlength:"5","show-word-limit":""},model:{value:e.dialogForm.keyName,callback:function(t){e.$set(e.dialogForm,"keyName",t)},expression:"dialogForm.keyName"}})],1),t("el-form-item",{attrs:{label:"键值：",prop:"keyValue"}},[t("el-input",{staticClass:"ps-input w-250",attrs:{maxlength:"20","show-word-limit":""},model:{value:e.dialogForm.keyValue,callback:function(t){e.$set(e.dialogForm,"keyValue",t)},expression:"dialogForm.keyValue"}})],1),t("el-form-item",{attrs:{label:"说明：",prop:"keyRemark"}},[t("el-input",{staticClass:"ps-input w-250",attrs:{maxlength:"100",type:"textarea",rows:8,"show-word-limit":""},model:{value:e.dialogForm.keyRemark,callback:function(t){e.$set(e.dialogForm,"keyRemark",t)},expression:"dialogForm.keyRemark"}})],1)],1):e._e()]),t("template",{slot:"tool"},[t("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:e.clickCancleHandle}},[e._v(" 取消 ")]),t("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickConfirmHandle}},[e._v(" 确定 ")])],1)])],2)},o=[],a=r("ed08");function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return t};var e,t={},r=Object.prototype,i=r.hasOwnProperty,o=Object.defineProperty||function(e,t,r){e[t]=r.value},a="function"==typeof Symbol?Symbol:{},l=a.iterator||"@@iterator",c=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function m(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{m({},"")}catch(e){m=function(e,t,r){return e[t]=r}}function d(e,t,r,i){var a=t&&t.prototype instanceof v?t:v,n=Object.create(a.prototype),s=new E(i||[]);return o(n,"_invoke",{value:T(e,r,s)}),n}function p(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var f="suspendedStart",g="suspendedYield",h="executing",y="completed",b={};function v(){}function k(){}function w(){}var F={};m(F,l,(function(){return this}));var L=Object.getPrototypeOf,x=L&&L(L(N([])));x&&x!==r&&i.call(x,l)&&(F=x);var _=w.prototype=v.prototype=Object.create(F);function $(e){["next","throw","return"].forEach((function(t){m(e,t,(function(e){return this._invoke(t,e)}))}))}function C(e,t){function r(o,a,s,l){var c=p(e[o],e,a);if("throw"!==c.type){var u=c.arg,m=u.value;return m&&"object"==n(m)&&i.call(m,"__await")?t.resolve(m.__await).then((function(e){r("next",e,s,l)}),(function(e){r("throw",e,s,l)})):t.resolve(m).then((function(e){u.value=e,s(u)}),(function(e){return r("throw",e,s,l)}))}l(c.arg)}var a;o(this,"_invoke",{value:function(e,i){function o(){return new t((function(t,o){r(e,i,t,o)}))}return a=a?a.then(o,o):o()}})}function T(t,r,i){var o=f;return function(a,n){if(o===h)throw Error("Generator is already running");if(o===y){if("throw"===a)throw n;return{value:e,done:!0}}for(i.method=a,i.arg=n;;){var s=i.delegate;if(s){var l=O(s,i);if(l){if(l===b)continue;return l}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if(o===f)throw o=y,i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);o=h;var c=p(t,r,i);if("normal"===c.type){if(o=i.done?y:g,c.arg===b)continue;return{value:c.arg,done:i.done}}"throw"===c.type&&(o=y,i.method="throw",i.arg=c.arg)}}}function O(t,r){var i=r.method,o=t.iterator[i];if(o===e)return r.delegate=null,"throw"===i&&t.iterator.return&&(r.method="return",r.arg=e,O(t,r),"throw"===r.method)||"return"!==i&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+i+"' method")),b;var a=p(o,t.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,b;var n=a.arg;return n?n.done?(r[t.resultName]=n.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,b):n:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,b)}function P(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function j(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function E(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(P,this),this.reset(!0)}function N(t){if(t||""===t){var r=t[l];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,a=function r(){for(;++o<t.length;)if(i.call(t,o))return r.value=t[o],r.done=!1,r;return r.value=e,r.done=!0,r};return a.next=a}}throw new TypeError(n(t)+" is not iterable")}return k.prototype=w,o(_,"constructor",{value:w,configurable:!0}),o(w,"constructor",{value:k,configurable:!0}),k.displayName=m(w,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===k||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,m(e,u,"GeneratorFunction")),e.prototype=Object.create(_),e},t.awrap=function(e){return{__await:e}},$(C.prototype),m(C.prototype,c,(function(){return this})),t.AsyncIterator=C,t.async=function(e,r,i,o,a){void 0===a&&(a=Promise);var n=new C(d(e,r,i,o),a);return t.isGeneratorFunction(r)?n:n.next().then((function(e){return e.done?e.value:n.next()}))},$(_),m(_,u,"Generator"),m(_,l,(function(){return this})),m(_,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var i in t)r.push(i);return r.reverse(),function e(){for(;r.length;){var i=r.pop();if(i in t)return e.value=i,e.done=!1,e}return e.done=!0,e}},t.values=N,E.prototype={constructor:E,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(j),!t)for(var r in this)"t"===r.charAt(0)&&i.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function o(i,o){return s.type="throw",s.arg=t,r.next=i,o&&(r.method="next",r.arg=e),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var n=this.tryEntries[a],s=n.completion;if("root"===n.tryLoc)return o("end");if(n.tryLoc<=this.prev){var l=i.call(n,"catchLoc"),c=i.call(n,"finallyLoc");if(l&&c){if(this.prev<n.catchLoc)return o(n.catchLoc,!0);if(this.prev<n.finallyLoc)return o(n.finallyLoc)}else if(l){if(this.prev<n.catchLoc)return o(n.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<n.finallyLoc)return o(n.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&i.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var n=a?a.completion:{};return n.type=e,n.arg=t,a?(this.method="next",this.next=a.finallyLoc,b):this.complete(n)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),b},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),j(r),b}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var i=r.completion;if("throw"===i.type){var o=i.arg;j(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,r,i){return this.delegate={iterator:N(t),resultName:r,nextLoc:i},"next"===this.method&&(this.arg=e),b}},t}function l(e,t,r,i,o,a,n){try{var s=e[a](n),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(i,o)}function c(e){return function(){var t=this,r=arguments;return new Promise((function(i,o){var a=e.apply(t,r);function n(e){l(a,i,o,n,s,"next",e)}function s(e){l(a,i,o,n,s,"throw",e)}n(void 0)}))}}var u={name:"MemberKeyOrPermissDialog",props:{loading:Boolean,type:{type:String,default:""},dialogType:{type:String,default:"permission"},title:{type:String,default:""},width:{type:String,default:"500px"},selectInfo:{type:Object,default:function(){return{}}},isshow:Boolean,confirm:Function},data:function(){return{isLoading:!1,dialogForm:{keyName:"",keyValue:"",keyRemark:"",name:"",img:"",type:"",imageList:[],link:"",label:[],remark:""},dialogFormClone:{keyName:"",keyValue:"",keyRemark:"",name:"",img:"",type:"",imageList:[],link:"",label:"",remark:""},dialogFormRules:{keyName:[{required:!0,message:"请输入名称",trigger:"blur"}],keyValue:[{required:!0,message:"请输入键值",trigger:"blur"}],name:[{required:!0,message:"请输入名称",trigger:"blur"}],img:[{required:!0,message:"请选择图标",trigger:"change"}],type:[{required:!0,message:"请选择类型",trigger:"change"}],link:[{required:!0,message:"请输入链接",trigger:"blur"}],label:[{required:!0,message:"请选择键值",trigger:"change"}],remark:[{required:!0,message:"请输入说明",trigger:"blur"}]},labelList:[],keyList:[],typeList:[{name:"关联链接",id:"association"},{name:"固定模块",id:"fixed"}],warnTip:"全部标签满足才触发优惠规则",serverUrl:"/api/background/file/upload",headersOpts:{TOKEN:Object(a["B"])()},fileLists:[],uploadParams:{prefix:"super_food_img"},uploading:!1}},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},watch:{visible:function(){this.visible?(this.getMemberLabel(),this.getMemberKey(),"edit"===this.type?(this.dialogForm.keyName="key"===this.dialogType?this.selectInfo.name:"",this.dialogForm.keyValue="key"===this.dialogType?this.selectInfo.permission_key:"",this.dialogForm.keyRemark="key"===this.dialogType?this.selectInfo.remark:"",this.dialogForm.name="permission"===this.dialogType?this.selectInfo.name:"",this.dialogForm.img="permission"===this.dialogType?this.selectInfo.icon_url:"",this.dialogForm.type="permission"===this.dialogType?this.selectInfo.type:"",this.dialogForm.imageList="permission"===this.dialogType?[this.selectInfo.icon_url]:"",this.dialogForm.label="permission"===this.dialogType?this.selectInfo.permission_dict:"",this.dialogForm.link="permission"===this.dialogType?this.selectInfo.associate_url:"",this.dialogForm.remark="permission"===this.dialogType?this.selectInfo.remark:""):(this.$set(this,"dialogForm",Object(a["f"])(this.dialogFormClone)),this.$refs.dialogFormRef&&this.$refs.dialogFormRef.resetFields(),this.$refs.fileUpload&&(this.fileLists=[],this.$refs.fileUpload.clearFiles()))):(this.$set(this,"dialogForm",Object(a["f"])(this.dialogFormClone)),this.$refs.dialogFormRef&&this.$refs.dialogFormRef.resetFields(),this.$refs.fileUpload&&(this.fileLists=[],this.$refs.fileUpload.clearFiles()))}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getMemberLabel()},clickConfirmHandle:function(){var e=this;this.$refs.dialogFormRef.validate((function(t){if(t){var r,i={};switch("key"===e.dialogType&&(i={name:e.dialogForm.keyName,permission_key:e.dialogForm.keyValue,remark:e.dialogForm.keyRemark}),"permission"===e.dialogType&&(i={name:e.dialogForm.name,icon_url:e.dialogForm.img,type:e.dialogForm.type,remark:e.dialogForm.remark}),"association"===e.dialogForm.type&&(i.associate_url=e.dialogForm.link),"fixed"===e.dialogForm.type&&(i.permission_dict=e.dialogForm.label),e.type){case"add":r="key"===e.dialogType?e.$apis.apiBackgroundMemberPermissionDictAddPost(i):e.$apis.apiBackgroundMemberMemberPermissionAddPost(i);break;case"edit":i.id=Number(e.selectInfo.id),r="key"===e.dialogType?e.$apis.apiBackgroundMemberPermissionDictModifyPost(i):e.$apis.apiBackgroundMemberMemberPermissionModifyPost(i);break}e.confirmOperation(r)}}))},confirmOperation:function(e){var t=this;return c(s().mark((function r(){var i;return s().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!t.isLoading){r.next=2;break}return r.abrupt("return");case 2:return t.isLoading=!0,r.next=5,e;case 5:i=r.sent,t.isLoading=!1,0===i.code?(t.$message.success("成功"),t.confirm()):t.$message.error(i.msg);case 8:case"end":return r.stop()}}),r)})))()},clickCancleHandle:function(){this.visible=!1},handleClose:function(e){this.isLoading=!1,this.visible=!1,this.$refs.dialogFormRef.resetFields()},getMemberLabel:function(){var e=this;return c(s().mark((function t(){var r;return s().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundMemberMemberLabelListPost({page:1,page_size:99999});case 2:r=t.sent,0===r.code?e.labelList=r.data.results:e.$message.error(r.msg);case 4:case"end":return t.stop()}}),t)})))()},getMemberKey:function(){var e=this;return c(s().mark((function t(){var r,i,o;return s().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundMemberPermissionDictListPost({page:1,page_size:99999});case 2:r=t.sent,r&&0===r.code?(i=r.data||{},o=i.results||[],e.keyList=o):e.$message.error(r.msg);case 4:case"end":return t.stop()}}),t)})))()},labelTypeChange:function(e){this.warnTip="1"===e?"全部标签满足才触发优惠规则":"任一标签满足即触发规则"},beforeFoodImgUpload:function(e){var t=[".jpeg",".jpg",".png",".bmp"],r=e.size/1024/1024<=5;return t.includes(Object(a["A"])(e.name))?r?void(this.uploading=!0):(this.$message.error("上传图片大小不能超过 5MB!"),!1):(this.$message.error("上传图片只能是 JPG 格式或者PNG格式!"),!1)},uploadSuccess:function(e,t,r){this.uploading=!1,e&&0===e.code?(this.fileLists=r,this.dialogForm.img=e.data.public_url,this.dialogForm.imageList=[e.data.public_url]):(this.dialogForm.img="",this.$message.error(e.msg))},removeFoodImg:function(e){this.dialogForm.imageList.splice(e,1),this.fileLists.splice(e,1)},chooseTypeChange:function(e){this.$refs.dialogFormRef&&this.$refs.dialogFormRef.clearValidate(["link","label"])}}},m=u,d=(r("3bbf"),r("2877")),p=Object(d["a"])(m,i,o,!1,null,"4b5bed5a",null);t["default"]=p.exports},f655:function(e,t,r){}}]);