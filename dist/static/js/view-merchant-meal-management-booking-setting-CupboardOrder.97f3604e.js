(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-booking-setting-CupboardOrder","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-booking-setting-SetMealSummary","view-merchant-meal-management-booking-setting-constantsAndConfig","view-merchant-meal-management-meal-report-MealPackageRule"],{"2a66":function(e,t,a){e.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}},"45f7":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"TableAdmin container-wrapper"},[t("refresh-tool",{on:{refreshPage:e.refreshHandle}}),t("search-form",{ref:"searchRef",attrs:{"form-setting":e.searchFormSetting,"label-width":"105px",autoSearch:!1},on:{search:e.searchHandle}}),t("div",{staticClass:"table-wrapper"},[t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title",staticStyle:{"min-width":"130px"}},[e._v("数据列表")]),t("div",{staticClass:"align-r"},[t("button-icon",{attrs:{color:"origin"},on:{click:e.getSelectCupboardOrderAll}},[e._v("列表全选")]),t("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_order.order_reservation.save_cupboard_order_list"],expression:"['background_order.order_reservation.save_cupboard_order_list']"}],attrs:{color:"origin"},on:{click:function(t){return e.clickStorageFood("batch")}}},[e._v("手动存餐")]),t("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_order.reservation_order.auto_save_cupboard"],expression:"['background_order.reservation_order.auto_save_cupboard']"}],attrs:{color:"origin"},on:{click:function(t){return e.clickAutoCupboard("save")}}},[e._v("自动分柜")]),t("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_order.reservation_order.auto_open_cupboard_cell"],expression:"['background_order.reservation_order.auto_open_cupboard_cell']"}],attrs:{color:"origin"},on:{click:function(t){return e.clickAutoCupboard("open_cell")}}},[e._v("存餐开柜")]),t("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_order.order_reservation.put_meal_number_export"],expression:"['background_order.order_reservation.put_meal_number_export']"}],attrs:{color:"plain",type:"export"},on:{click:function(t){return e.gotoExport("ExportPutMealNumber")}}},[e._v(" 导出存餐码 ")]),t("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_order.reservation_order.cupboard_order_list_export"],expression:"['background_order.reservation_order.cupboard_order_list_export']"}],attrs:{color:"plain",type:"export"},on:{click:function(t){return e.gotoExport("ExportCupboardOrderList")}}},[e._v(" 导出Excel ")])],1)]),t("div",{staticClass:"table-content"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData.slice((e.currentPage-1)*e.pageSize,e.currentPage*e.pageSize),stripe:"","row-key":"id","header-row-class-name":"ps-table-header-row"},on:{"selection-change":e.handleSelectionChange}},[t("el-table-column",{attrs:{type:"selection",width:"50",align:"center","reserve-selection":!0,"class-name":"ps-checkbox",selectable:e.selectable}}),t("el-table-column",{attrs:{prop:"unified_trade_no",label:"总单号",align:"center"}}),t("el-table-column",{attrs:{prop:"trade_no",label:"订单号",align:"center"}}),t("el-table-column",{attrs:{prop:"put_meal_number",label:"存餐号",align:"center"}}),t("el-table-column",{attrs:{prop:"take_meal_number",label:"取餐号",align:"center"}}),t("el-table-column",{attrs:{prop:"create_time",label:"创建时间",align:"center"}}),t("el-table-column",{attrs:{prop:"reservation_date",label:"预约时间",align:"center"}}),t("el-table-column",{attrs:{prop:"consumption_name",label:"预约消费点",align:"center",width:"100"}}),t("el-table-column",{attrs:{prop:"name",label:"姓名",align:"center"}}),t("el-table-column",{attrs:{prop:"phone",label:"手机号码",align:"center"}}),t("el-table-column",{attrs:{prop:"meal_type_alias",label:"预约餐段",align:"center"}}),t("el-table-column",{attrs:{prop:"food_list",label:"预约菜品",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.food_list&&a.row.food_list.length?t("div",{staticClass:"ps-flex-align-c flex-wrap"},e._l(a.row.food_list,(function(r,n){return t("div",{key:n},[t("span",{staticClass:"p-t-10"},[e._v(" "+e._s(n===a.row.food_list.length-1&&r.name||r.name+"、")+" ")])])})),0):e._e()]}}])}),t("el-table-column",{attrs:{prop:"person_no",label:"人员编号",align:"center"}}),t("el-table-column",{attrs:{prop:"payer_group_name",label:"分组",align:"center"}}),t("el-table-column",{attrs:{prop:"is_save",label:"是否已存餐",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.is_save?t("div",[e._v("已存餐")]):t("div",[e._v("未存餐")])]}}])}),t("el-table-column",{attrs:{prop:"address",label:"存餐区域",align:"center"}}),t("el-table-column",{attrs:{prop:"cupboard_name",label:"餐柜名称",align:"center"}}),t("el-table-column",{attrs:{prop:"ceil_no",label:"格子号",align:"center"}}),t("el-table-column",{attrs:{prop:"take_meal_status_alias",label:"取餐状态",align:"center"}}),t("el-table-column",{attrs:{prop:"dining_time",label:"用餐时间",align:"center"}}),t("el-table-column",{attrs:{align:"center",width:"180",label:"操作",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_order.order_reservation.save_cupboard_order_list"],expression:"['background_order.order_reservation.save_cupboard_order_list']"}],staticClass:"ps-text",attrs:{type:"text",size:"small",disabled:!a.row.can_save},on:{click:function(t){return e.clickStorageFood("single",a.row)}}},[e._v(" 存餐 ")])]}}])})],1)],1),t("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[t("el-pagination",{staticClass:"ps-text",attrs:{"current-page":e.currentPage,"page-sizes":[10,20,50,100,500],"page-size":e.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)]),t("dialog-message",{attrs:{show:e.storageFoodDialog,title:"请选择所要存餐的取餐柜",customClass:"ps-dialog",width:"350px"},on:{"update:show":function(t){e.storageFoodDialog=t}}},[t("div",{staticClass:"ps-center"},["batch"===e.batchSavetype?t("el-select",{key:"batch",staticClass:"ps-select w-250",attrs:{multiple:"","collapse-tags":"",placeholder:"请选择取餐柜","popper-class":"ps-popper-select"},model:{value:e.saveIdsDialog,callback:function(t){e.saveIdsDialog=t},expression:"saveIdsDialog"}},e._l(e.saveListDialog,(function(e){return t("el-option",{key:e.device_id,attrs:{label:e.device_name,value:e.device_id}})})),1):e._e(),"single"===e.batchSavetype?t("el-select",{key:"single",staticClass:"ps-select w-250",attrs:{"collapse-tags":"",placeholder:"请选择取餐柜","popper-class":"ps-popper-select"},model:{value:e.singleSaveIdsDialog,callback:function(t){e.singleSaveIdsDialog=t},expression:"singleSaveIdsDialog"}},e._l(e.saveListDialog,(function(e){return t("el-option",{key:e.device_id,attrs:{label:e.device_name,value:e.device_id}})})),1):e._e()],1),t("template",{slot:"tool"},[t("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:function(t){e.storageFoodDialog=!1}}},[e._v(" 取消 ")]),t("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickStorageFoodDialog}},[e._v(" 确定 ")])],1)])],2),t("dialog-message",{attrs:{show:e.storageTipsDialog,title:"提示",customClass:"ps-dialog",width:"350px"},on:{"update:show":function(t){e.storageTipsDialog=t}}},[t("div",[t("div",[e._v("当前可用餐格不足以存完所选的订单，是否继续？")]),t("div",[e._v("如继续，则优先存入先下单的订单")])]),t("template",{slot:"tool"},[t("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:function(t){e.storageTipsDialog=!1}}},[e._v(" 取消 ")]),t("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickTipsStorageFood}},[e._v(" 继续 ")])],1)])],2),t("dialog-message",{attrs:{show:e.saveSuccessDialog,title:"提示",customClass:"ps-dialog",width:"350px"},on:{"update:show":function(t){e.saveSuccessDialog=t},close:e.clickSaveSuccessDialog}},[t("div",{staticClass:"t-a-c"},[t("div",[e._v("存餐成功订单："+e._s(e.saveSuccessDialogData.save_success_num)+"单")]),t("div",[e._v("未成功订单："+e._s(e.saveSuccessDialogData.no_save_num)+"单")]),t("el-button",{staticClass:"ps-blue",attrs:{type:"text"},on:{click:function(t){return e.gotoExport("ExportSaveCupboardOrderList")}}},[e._v(" 下载存餐成功订单详情 ")])],1),t("template",{slot:"tool"},[t("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:e.clickSaveSuccessDialog}},[e._v(" 关闭 ")])],1)])],2)],1)},n=[],l=a("ed08"),i=a("a563"),o=a("f63a");function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function(){return t};var e,t={},a=Object.prototype,r=a.hasOwnProperty,n=Object.defineProperty||function(e,t,a){e[t]=a.value},l="function"==typeof Symbol?Symbol:{},i=l.iterator||"@@iterator",o=l.asyncIterator||"@@asyncIterator",u=l.toStringTag||"@@toStringTag";function d(e,t,a){return Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(e){d=function(e,t,a){return e[t]=a}}function p(e,t,a,r){var l=t&&t.prototype instanceof _?t:_,i=Object.create(l.prototype),o=new j(r||[]);return n(i,"_invoke",{value:O(e,a,o)}),i}function m(e,t,a){try{return{type:"normal",arg:e.call(t,a)}}catch(e){return{type:"throw",arg:e}}}t.wrap=p;var g="suspendedStart",v="suspendedYield",b="executing",f="completed",h={};function _(){}function y(){}function k(){}var w={};d(w,i,(function(){return this}));var x=Object.getPrototypeOf,S=x&&x(x(A([])));S&&S!==a&&r.call(S,i)&&(w=S);var L=k.prototype=_.prototype=Object.create(w);function D(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function C(e,t){function a(n,l,i,o){var c=m(e[n],e,l);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==s(d)&&r.call(d,"__await")?t.resolve(d.__await).then((function(e){a("next",e,i,o)}),(function(e){a("throw",e,i,o)})):t.resolve(d).then((function(e){u.value=e,i(u)}),(function(e){return a("throw",e,i,o)}))}o(c.arg)}var l;n(this,"_invoke",{value:function(e,r){function n(){return new t((function(t,n){a(e,r,t,n)}))}return l=l?l.then(n,n):n()}})}function O(t,a,r){var n=g;return function(l,i){if(n===b)throw Error("Generator is already running");if(n===f){if("throw"===l)throw i;return{value:e,done:!0}}for(r.method=l,r.arg=i;;){var o=r.delegate;if(o){var s=T(o,r);if(s){if(s===h)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(n===g)throw n=f,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n=b;var c=m(t,a,r);if("normal"===c.type){if(n=r.done?f:v,c.arg===h)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(n=f,r.method="throw",r.arg=c.arg)}}}function T(t,a){var r=a.method,n=t.iterator[r];if(n===e)return a.delegate=null,"throw"===r&&t.iterator.return&&(a.method="return",a.arg=e,T(t,a),"throw"===a.method)||"return"!==r&&(a.method="throw",a.arg=new TypeError("The iterator does not provide a '"+r+"' method")),h;var l=m(n,t.iterator,a.arg);if("throw"===l.type)return a.method="throw",a.arg=l.arg,a.delegate=null,h;var i=l.arg;return i?i.done?(a[t.resultName]=i.value,a.next=t.nextLoc,"return"!==a.method&&(a.method="next",a.arg=e),a.delegate=null,h):i:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,h)}function P(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function j(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(P,this),this.reset(!0)}function A(t){if(t||""===t){var a=t[i];if(a)return a.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,l=function a(){for(;++n<t.length;)if(r.call(t,n))return a.value=t[n],a.done=!1,a;return a.value=e,a.done=!0,a};return l.next=l}}throw new TypeError(s(t)+" is not iterable")}return y.prototype=k,n(L,"constructor",{value:k,configurable:!0}),n(k,"constructor",{value:y,configurable:!0}),y.displayName=d(k,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,k):(e.__proto__=k,d(e,u,"GeneratorFunction")),e.prototype=Object.create(L),e},t.awrap=function(e){return{__await:e}},D(C.prototype),d(C.prototype,o,(function(){return this})),t.AsyncIterator=C,t.async=function(e,a,r,n,l){void 0===l&&(l=Promise);var i=new C(p(e,a,r,n),l);return t.isGeneratorFunction(a)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},D(L),d(L,u,"Generator"),d(L,i,(function(){return this})),d(L,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),a=[];for(var r in t)a.push(r);return a.reverse(),function e(){for(;a.length;){var r=a.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=A,j.prototype={constructor:j,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(E),!t)for(var a in this)"t"===a.charAt(0)&&r.call(this,a)&&!isNaN(+a.slice(1))&&(this[a]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var a=this;function n(r,n){return o.type="throw",o.arg=t,a.next=r,n&&(a.method="next",a.arg=e),!!n}for(var l=this.tryEntries.length-1;l>=0;--l){var i=this.tryEntries[l],o=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var s=r.call(i,"catchLoc"),c=r.call(i,"finallyLoc");if(s&&c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(e,t){for(var a=this.tryEntries.length-1;a>=0;--a){var n=this.tryEntries[a];if(n.tryLoc<=this.prev&&r.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var l=n;break}}l&&("break"===e||"continue"===e)&&l.tryLoc<=t&&t<=l.finallyLoc&&(l=null);var i=l?l.completion:{};return i.type=e,i.arg=t,l?(this.method="next",this.next=l.finallyLoc,h):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),h},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.finallyLoc===e)return this.complete(a.completion,a.afterLoc),E(a),h}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.tryLoc===e){var r=a.completion;if("throw"===r.type){var n=r.arg;E(a)}return n}}throw Error("illegal catch attempt")},delegateYield:function(t,a,r){return this.delegate={iterator:A(t),resultName:a,nextLoc:r},"next"===this.method&&(this.arg=e),h}},t}function u(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,r)}return a}function d(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?u(Object(a),!0).forEach((function(t){p(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):u(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function p(e,t,a){return(t=m(t))in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function m(e){var t=g(e,"string");return"symbol"==s(t)?t:t+""}function g(e,t){if("object"!=s(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var r=a.call(e,t||"default");if("object"!=s(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function v(e,t){return y(e)||_(e,t)||f(e,t)||b()}function b(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(e,t){if(e){if("string"==typeof e)return h(e,t);var a={}.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?h(e,t):void 0}}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,r=Array(t);a<t;a++)r[a]=e[a];return r}function _(e,t){var a=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=a){var r,n,l,i,o=[],s=!0,c=!1;try{if(l=(a=a.call(e)).next,0===t){if(Object(a)!==a)return;s=!1}else for(;!(s=(r=l.call(a)).done)&&(o.push(r.value),o.length!==t);s=!0);}catch(e){c=!0,n=e}finally{try{if(!s&&null!=a.return&&(i=a.return(),Object(i)!==i))return}finally{if(c)throw n}}return o}}function y(e){if(Array.isArray(e))return e}function k(e,t,a,r,n,l,i){try{var o=e[l](i),s=o.value}catch(e){return void a(e)}o.done?t(s):Promise.resolve(s).then(r,n)}function w(e){return function(){var t=this,a=arguments;return new Promise((function(r,n){var l=e.apply(t,a);function i(e){k(l,r,n,i,o,"next",e)}function o(e){k(l,r,n,i,o,"throw",e)}i(void 0)}))}}var x={name:"TableAdmin",mixins:[o["a"]],data:function(){return{tableData:[],currentPage:1,pageSize:10,totalCount:0,isLoading:!1,searchFormSetting:Object(l["f"])(i["CUPBOARDORDER"]),selectTableList:[],storageFoodDialog:!1,saveIdsDialog:[],singleSaveIdsDialog:"",saveListDialog:[],storageTipsDialog:!1,batchSavetype:"",saveSuccessDialog:!1,saveCupboardParams:{},tableSingleRow:{},saveSuccessDialogData:{}}},created:function(){this.searchFormSetting.organization_id.value=this.$store.getters.organization,this.getCupboardList({data_model:1}),this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getCupboardOrderList()},refreshHandle:function(){this.currentPage=1,this.tableData=[],this.initLoad()},searchHandle:Object(l["d"])((function(e){e&&"search"===e&&(this.currentPage=1,this.initLoad())}),300),getCupboardOrderList:function(){var e=this;return w(c().mark((function t(){var a,r,n,i;return c().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(l["Z"])(e.$apis.apiBackgroundOrderOrderReservationCupboardOrderListPost(d(d({},e.formatQueryParams(e.searchFormSetting)),{},{page:1,page_size:99999})));case 3:if(a=t.sent,r=v(a,2),n=r[0],i=r[1],e.isLoading=!1,!n){t.next=11;break}return e.$message.error(n.message),t.abrupt("return");case 11:0===i.code?(e.$nextTick((function(){e.$refs.tableData.clearSelection()})),e.tableData=i.data.results,e.totalCount=i.data.results.length):e.$message.error(i.msg);case 12:case"end":return t.stop()}}),t)})))()},getSelectCupboardOrderAll:function(){var e=this;this.tableData.forEach((function(t){t.can_save&&e.$nextTick((function(){e.$refs.tableData.toggleAllSelection(t)}))}))},getCupboardList:function(e){var t=this;return w(c().mark((function a(){var r,n,i,o;return c().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,Object(l["Z"])(t.$apis.apiBackgroundOrderOrderReservationGetCupboardListPost(e));case 2:if(r=a.sent,n=v(r,2),i=n[0],o=n[1],!i){a.next=9;break}return t.$message.error(i.message),a.abrupt("return");case 9:0===o.code?1===e.data_model?t.searchFormSetting.device_ids.dataList=o.data:2===e.data_model&&(t.saveListDialog=o.data,t.storageFoodDialog=!0):t.$message.error(o.msg);case 10:case"end":return a.stop()}}),a)})))()},handleSelectionChange:function(e){this.selectTableList=e},clickStorageFood:function(e,t){"batch"===e?this.saveIdsDialog=[]:"single"===e&&(this.singleSaveIdsDialog=""),this.batchSavetype=e,this.tableSingleRow=t;var a={data_model:2,organization_id:"",reservation_date:"",meal_type:""};if("single"===e&&(a.organization_id=t.organization_id,a.reservation_date=t.reservation_date,a.meal_type=t.meal_type),"batch"===e&&!this.selectTableList.length)return this.$message.error("请勾选数据");"batch"===e&&this.selectTableList.length&&(a.organization_id=this.selectTableList[0].organization_id,a.reservation_date=this.selectTableList[0].reservation_date,a.meal_type=this.selectTableList[0].meal_type),this.getCupboardList(a)},clickAutoCupboard:function(e){var t=this,a="save"===e?"请确认未分柜再发送分柜指令":"请确认已分柜再发送存餐开柜指令";this.$confirm(a,"提示",{confirmButtonText:"发送",cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var a=w(c().mark((function a(r,n,i){var o,s,u,d,p,m,g,b;return c().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if("confirm"!==r){a.next=28;break}if(n.confirmButtonLoading=!0,o="",s=v(o,2),u=s[0],d=s[1],"save"!==e){a.next=13;break}return a.next=7,Object(l["Z"])(t.$apis.apiBackgroundOrderReservationOrderAutoSaveCupboardPost());case 7:p=a.sent,m=v(p,2),u=m[0],d=m[1],a.next=20;break;case 13:return a.next=16,Object(l["Z"])(t.$apis.apiBackgroundOrderReservationOrderAutoOpenCupboardCellPost());case 16:g=a.sent,b=v(g,2),u=b[0],d=b[1];case 20:if(n.confirmButtonLoading=!1,i(),!u){a.next=25;break}return t.$message.error(u.message),a.abrupt("return");case 25:0===d.code?t.$message.success(d.msg):t.$message.error(d.msg),a.next=29;break;case 28:n.confirmButtonLoading||i();case 29:case"end":return a.stop()}}),a)})));function r(e,t,r){return a.apply(this,arguments)}return r}()}).then((function(e){})).catch((function(e){}))},selectable:function(e,t){return!!e.can_save},clickStorageFoodDialog:function(){var e=this;if("batch"===this.batchSavetype&&!this.saveIdsDialog.length)return this.$message.error("请选择取餐柜");if("single"===this.batchSavetype&&!this.singleSaveIdsDialog)return this.$message.error("请选择取餐柜");var t=0;return this.saveListDialog.forEach((function(a){"batch"===e.batchSavetype&&e.saveIdsDialog.includes(a.device_id)&&(t+=a.total_free_cell_num),"single"===e.batchSavetype&&e.singleSaveIdsDialog===a.device_id&&(t=a.total_free_cell_num)})),"single"===this.batchSavetype&&t<=0||"batch"===this.batchSavetype&&t<this.selectTableList.length?this.storageTipsDialog=!0:void this.getSaveCupboardOrder()},clickTipsStorageFood:function(){this.getSaveCupboardOrder()},getSaveCupboardOrder:function(){var e=this;return w(c().mark((function t(){var a,r,n,i,o;return c().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a={order_payment_ids:[],device_ids:"batch"===e.batchSavetype?e.saveIdsDialog:[e.singleSaveIdsDialog],organization_id:"",meal_type:""},"batch"===e.batchSavetype?(a.organization_id=e.selectTableList[0].organization_id,a.meal_type=e.selectTableList[0].meal_type,a.order_payment_ids=e.selectTableList.map((function(e){return e.id}))):(a.order_payment_ids=[e.tableSingleRow.id],a.organization_id=e.tableSingleRow.organization_id,a.meal_type=e.tableSingleRow.meal_type),t.next=4,Object(l["Z"])(e.$apis.apiBackgroundOrderOrderReservationSaveCupboardOrderListPost(a));case 4:if(r=t.sent,n=v(r,2),i=n[0],o=n[1],!i){t.next=11;break}return e.$message.error(i.message),t.abrupt("return");case 11:0===o.code?(e.saveSuccessDialogData=o.data,e.saveSuccessDialog=!0,e.initLoad()):e.$message.error(o.msg);case 12:case"end":return t.stop()}}),t)})))()},clickSaveSuccessDialog:function(){this.saveSuccessDialog=!1,this.storageFoodDialog=!1,this.storageTipsDialog=!1},gotoExport:function(e){var t={type:e,params:{}};"ExportSaveCupboardOrderList"===e?t.params={order_payment_save_ids:this.saveSuccessDialogData.order_payment_save_ids}:"ExportCupboardOrderList"!==e&&"ExportPutMealNumber"!==e||(t.params=d(d({},this.formatQueryParams(this.searchFormSetting)),{},{page:1,page_size:99999})),this.exportHandle(t)},handleSizeChange:function(e){this.pageSize=e},handleCurrentChange:function(e){this.currentPage=e},formatQueryParams:function(e){var t={};for(var a in e){var r;""!==e[a].value&&null!==e[a].value&&0!==(null===(r=e[a].value)||void 0===r?void 0:r.length)&&("select_time"!==a?t[a]=e[a].value:e[a].value.length>0&&(t.start_date=e[a].value[0],t.end_date=e[a].value[1]))}return t}}},S=x,L=(a("b6d6"),a("2877")),D=Object(L["a"])(S,r,n,!1,null,null,null);t["default"]=D.exports},a563:function(e,t,a){"use strict";a.r(t),a.d(t,"recentSevenDay",(function(){return p})),a.d(t,"searchFormSetting",(function(){return m})),a.d(t,"columns",(function(){return g})),a.d(t,"menuManagerFormSetting",(function(){return v})),a.d(t,"deviceList",(function(){return b})),a.d(t,"dateTypes",(function(){return f})),a.d(t,"CUPBOARDORDER",(function(){return h}));var r=a("c9d9"),n=a("5a0c");function l(e){return c(e)||s(e)||o(e)||i()}function i(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function o(e,t){if(e){if("string"==typeof e)return u(e,t);var a={}.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?u(e,t):void 0}}function s(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function c(e){if(Array.isArray(e))return u(e)}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,r=Array(t);a<t;a++)r[a]=e[a];return r}var d=[{label:"全部",value:""},{label:"线上",value:"online"},{label:"线下",value:"offline"}],p=[n().subtract(7,"day").format("YYYY-MM-DD"),n().format("YYYY-MM-DD")],m={user_group_id:{type:"select",label:"分组",value:null,placeholder:"请选择分组",dataList:[],listNameKey:"group_name",listValueKey:"id",multiple:!1,collapseTags:!0},date_type:{type:"select",value:"create_time",label:"时间",dataList:[{label:"创建时间",value:"create_time"},{label:"预约时间",value:"reservation_date"},{label:"用餐时间",value:"dining_time"}]},select_time:{clearable:!1,type:"daterange",value:p},meal_type_list:{type:"select",label:"餐段",value:[],multiple:!0,placeholder:"请选择",collapseTags:!0,dataList:r["a"]},take_meal_status:{type:"select",value:"",label:"取餐状态",dataList:[{value:"take_out",label:"已取餐"},{value:"no_take",label:"未取餐"},{value:"cancel",label:"已取消"},{value:"time_out",label:"已过期"}]},take_meal_type:{type:"select",value:"",label:"取餐方式",dataList:[{value:"on_scene",label:"堂食"},{value:"waimai",label:"外卖"},{value:"bale",label:"堂食自提"},{value:"cupboard",label:"取餐柜"}]},name:{type:"input",value:"",label:"姓名",placeholder:"请输入姓名"},person_no:{type:"input",value:"",label:"人员编号",placeholder:"请输入人员编号"},consume:{type:"organizationSelect",value:[],label:"消费点",listNameKey:"name",listValueKey:"id",dataList:[],multiple:!0,checkStrictly:!0,collapseTags:!0},consume_type:{type:"select",value:null,label:"支付方式",dataList:d},unified_out_trade_no:{type:"input",value:"",label:"总单号",placeholder:"请输入总单号"},out_trade_no:{type:"input",value:"",label:"订单号",placeholder:"请输入订单号"},payer_department_group_ids:{type:"departmentSelect",multiple:!0,flat:!1,label:"部门",value:[],placeholder:"请选择部门",checkStrictly:!0,dataList:[],limit:1,level:1},food_name:{type:"input",value:"",label:"菜品",placeholder:"请输入菜品"}},g=[{label:"总单号",column:"unified_trade_no",width:"160px"},{label:"订单号",column:"trade_no",width:"160px"},{label:"分组",column:"payer_group_name"},{label:"姓名",column:"name"},{label:"人员编号",column:"person_no"},{label:"手机号",column:"phone",width:"110px"},{label:"部门",column:"payer_department_group_name",width:"100px"},{label:"创建时间",column:"create_time",width:"160px"},{label:"预约时间",column:"reservation_date",width:"160px"},{label:"用餐时间",column:"dining_time",width:"160px"},{label:"预约餐段",column:"meal_type_alias"},{label:"取餐方式",column:"take_meal_type_alias"},{label:"扣费方式",column:"consume_type"},{label:"菜品",column:"create_date6"},{label:"份数",column:"count"},{label:"订单金额",column:"origin_fee"},{label:"取餐状态",column:"take_meal_status_alias"},{label:"预约消费点",column:"consumption_name",width:"120px"}],v={name:{type:"input",value:"",label:"菜谱名称",placeholder:"请输入菜谱名称"},account_name:{type:"input",value:"",label:"创建人",placeholder:"请输入"},apply_groups:{type:"select",value:[],label:"适用人群",placeholder:"请选择",listNameKey:"name",listValueKey:"id",multiple:!0,collapseTags:!0,dataList:[]},use_user_groups:{type:"select",label:"可见范围",value:null,placeholder:"请选择",dataList:[],listNameKey:"group_name",listValueKey:"id",multiple:!1,collapseTags:!0},organization:{type:"treeselect",label:"创建组织",multiple:!0,flat:!1,value:null,placeholder:"请选择创建组织",dataList:[],listValueKey:"id",limit:1,level:1,normalizer:function(e){return{id:e.id,label:e.name,children:e.children_list}}},use_organization:{type:"treeselect",label:"适用组织",multiple:!0,flat:!1,value:[],placeholder:"请选择",dataList:[],listValueKey:"id",limit:1,level:1,normalizer:function(e){return{id:e.id,label:e.name,children:e.children_list}}}},b=[{label:"全部",value:""},{label:"H5",value:"H5"},{label:"小程序",value:"MAPP"},{label:"K1点餐机",value:"K1DCJ"},{label:"双屏点餐机",value:"SPDCJ"},{label:"结算台",value:"JST"},{label:"P2手持消费机",value:"P2XFJ"},{label:"D2消费机",value:"D2XFJ"},{label:"智能秤",value:"ZNC"}],f=[{label:"创建时间",value:"create_time"},{label:"预约时间",value:"reservation_date"},{label:"用餐时间",value:"dining_time"}],h={date_type:{type:"select",value:"create_time",label:"",dataList:[{label:"创建时间",value:"create_time"},{label:"预约时间",value:"reservation_date"},{label:"用餐时间",value:"dining_time"}]},select_time:{clearable:!1,type:"daterange",value:p},trade_no:{type:"input",value:"",label:"总订单号/订单号",placeholder:"请输入总订单号或者订单号",labelWidth:"150px"},name:{type:"input",value:"",label:"姓名",placeholder:""},phone:{type:"input",value:"",label:"手机号",placeholder:""},person_no:{type:"input",value:"",label:"人员编号",placeholder:""},payer_group_ids:{type:"groupSelect",label:"分组",value:[],placeholder:"请选择分组",multiple:!0,collapseTags:!0},meal_type:{type:"select",value:"",label:"预约餐段",listNameKey:"label",listValueKey:"value",multiple:!1,collapseTags:!0,filterable:!0,dataList:[{value:"",label:"全部"}].concat(l(r["a"]))},is_save:{type:"select",value:"2",label:"是否已存餐",dataList:[{label:"全部",value:"2"},{label:"未存餐",value:"0"},{label:"已存餐",value:"1"}]},organization_id:{type:"organizationSelect",value:"",label:"消费点",listNameKey:"name",listValueKey:"id",dataList:[],checkStrictly:!0,multiple:!1,collapseTags:!0,clearable:!0},take_meal_status_list:{type:"select",value:[],label:"取餐状态",listNameKey:"label",listValueKey:"value",multiple:!0,collapseTags:!0,filterable:!0,dataList:[{value:"take_out",label:"已取餐"},{value:"no_take",label:"未取餐"},{value:"time_out",label:"已过期"}]},device_ids:{type:"select",value:[],label:"餐柜名称",listNameKey:"device_name",listValueKey:"device_id",multiple:!0,collapseTags:!0,filterable:!0,dataList:[]}}},b6d6:function(e,t,a){"use strict";a("2a66")},c9d9:function(e,t,a){"use strict";a.d(t,"a",(function(){return l})),a.d(t,"d",(function(){return i})),a.d(t,"b",(function(){return o})),a.d(t,"c",(function(){return s})),a.d(t,"e",(function(){return c})),a.d(t,"f",(function(){return u})),a.d(t,"g",(function(){return d}));var r=a("5a0c"),n=a("da92"),l=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],i=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],o={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},s=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],c=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],u=(r().subtract(7,"day").format("YYYY-MM-DD"),r().format("YYYY-MM-DD"),function(e){return e?"number"===typeof e?n["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:n["a"].divide(e,100).toFixed(2):"0.00"}),d=function(e){return n["a"].times(e,100)}}}]);