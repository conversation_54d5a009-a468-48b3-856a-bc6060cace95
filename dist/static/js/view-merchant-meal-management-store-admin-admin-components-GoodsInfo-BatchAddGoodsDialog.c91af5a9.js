(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-store-admin-admin-components-GoodsInfo-BatchAddGoodsDialog"],{"079d":function(t,e,r){},"0e415":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"BatchAddGoodsDialog"},[e("el-dialog",{attrs:{title:"批量新增",visible:t.visible,top:"20vh","custom-class":"ps-dialog","show-close":"","close-on-click-modal":!1,width:"1300px"},on:{"update:visible":function(e){t.visible=e}}},[e("div",{staticClass:"text-right p-b-20"},[e("el-button",{staticClass:"m-r-10",attrs:{type:"primary",size:"mini"},on:{click:t.clickDownloadUrl}},[t._v(" 下载模版 ")]),e("parse-excel",{attrs:{titleName:"选择本地文件"},on:{excel:t.getXlsxData}},[t._v("导入Excel")])],1),e("div",{staticClass:"ps-goods-dialog"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"ps-table",attrs:{data:t.goodsList.slice((t.currentPage-1)*t.pageSize,t.currentPage*t.pageSize),border:"","header-row-class-name":"ps-table-header-row"}},[e("el-table-column",{attrs:{prop:"index",label:"序号",align:"center"}}),e("el-table-column",{attrs:{prop:"spec",label:"*商品名称",align:"center",width:"120"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("el-input",{staticClass:"ps-input",attrs:{size:"mini"},model:{value:r.row.name,callback:function(e){t.$set(r.row,"name",e)},expression:"scope.row.name"}})]}}])}),e("el-table-column",{attrs:{prop:"spec",label:"*分类",align:"center",width:"120"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("el-select",{staticClass:"ps-select",attrs:{size:"mini",clearable:"",filterable:"","popper-class":"ps-popper-select"},model:{value:r.row.goods_category_id,callback:function(e){t.$set(r.row,"goods_category_id",e)},expression:"scope.row.goods_category_id"}},t._l(t.goodsCategoryList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)]}}])}),e("el-table-column",{attrs:{prop:"spec",label:"*规格",align:"center",width:"120"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("el-input",{staticClass:"ps-input",attrs:{size:"mini",maxlength:"10",onkeyup:"value=value.replace(/^\\D*(\\d*(?:\\.\\d{0,2})?).*$/g, '$1')"},model:{value:r.row.spec,callback:function(e){t.$set(r.row,"spec",e)},expression:"scope.row.spec"}})]}}])}),e("el-table-column",{attrs:{prop:"goods_unit",label:"*单位",align:"center",width:"120"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("el-select",{staticClass:"ps-select",attrs:{size:"mini",clearable:"",filterable:"","popper-class":"ps-popper-select"},model:{value:r.row.goods_unit,callback:function(e){t.$set(r.row,"goods_unit",e)},expression:"scope.row.goods_unit"}},t._l(t.unitList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)]}}])}),e("el-table-column",{attrs:{prop:"cost_price",label:"成本价",align:"center"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("el-input",{staticClass:"ps-input",attrs:{size:"mini"},model:{value:r.row.cost_price,callback:function(e){t.$set(r.row,"cost_price",e)},expression:"scope.row.cost_price"}})]}}])}),e("el-table-column",{attrs:{prop:"sales_price",label:"*零售价",align:"center"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("el-input",{staticClass:"ps-input",attrs:{size:"mini",maxlength:"4"},model:{value:r.row.sales_price,callback:function(e){t.$set(r.row,"sales_price",e)},expression:"scope.row.sales_price"}})]}}])}),e("el-table-column",{attrs:{prop:"stock_num",label:"库存",align:"center"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("el-input",{staticClass:"ps-input",attrs:{size:"mini",maxlength:"4",onkeyup:"value=value.replace(/[^\\d]/g,'')"},model:{value:r.row.stock_num,callback:function(e){t.$set(r.row,"stock_num",e)},expression:"scope.row.stock_num"}})]}}])}),e("el-table-column",{attrs:{prop:"supplier_id",label:"供应商",align:"center",width:"120"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("el-select",{staticClass:"ps-select",attrs:{size:"mini",clearable:"",filterable:"","popper-class":"ps-popper-select"},model:{value:r.row.supplier_id,callback:function(e){t.$set(r.row,"supplier_id",e)},expression:"scope.row.supplier_id"}},t._l(t.supplierList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)]}}])}),e("el-table-column",{attrs:{prop:"barcode",label:"*条码",align:"center",width:"210px"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("div",{staticClass:"ps-flex flex-align-c"},[e("el-input",{staticClass:"ps-input",attrs:{size:"mini"},model:{value:r.row.barcode,callback:function(e){t.$set(r.row,"barcode",e)},expression:"scope.row.barcode"}}),e("el-button",{staticClass:"ps-green-btn m-l-10",attrs:{slot:"append",size:"mini",type:"primary"},on:{click:function(e){return t.clickGenerateUniqueID(r.$index)}},slot:"append"},[t._v(" 生成 ")])],1)]}}])}),e("el-table-column",{attrs:{label:"操作",align:"center",fixed:"right",width:"100"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:t.clickAddGoods}},[t._v(" 新增 ")]),t.goodsList.length>1?e("el-button",{staticClass:"ps-warn",attrs:{type:"text",size:"small"},on:{click:function(e){return t.clickDelectGoods(r.row)}}},[t._v(" 删除 ")]):t._e()]}}])})],1),e("div",{staticClass:"pageSizeItem ps-pagination"},[e("el-pagination",{staticClass:"ps-text",attrs:{"current-page":t.currentPage,"page-size":t.pageSize,layout:"prev, pager, next, total, jumper",total:t.goodsList.length,background:"","popper-class":"ps-popper-select"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange,"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e}}})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"ps-cancel-btn",on:{click:t.canceDialogHandle}},[t._v("取 消")]),e("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"ps-btn",attrs:{type:"primary"},on:{click:t.clickDetermineDialog}},[t._v(" 确定 ")])],1)])],1)},o=[],i=r("da92"),a=r("ed08");function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function c(t){return p(t)||u(t)||g(t)||l()}function l(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function p(t){if(Array.isArray(t))return m(t)}function d(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */d=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function p(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,a=Object.create(i.prototype),s=new O(n||[]);return o(a,"_invoke",{value:E(t,r,s)}),a}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=p;var h="suspendedStart",g="suspendedYield",m="executing",y="completed",v={};function b(){}function _(){}function w(){}var k={};u(k,a,(function(){return this}));var x=Object.getPrototypeOf,L=x&&x(x(G([])));L&&L!==r&&n.call(L,a)&&(k=L);var S=w.prototype=b.prototype=Object.create(k);function C(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function $(t,e){function r(o,i,a,c){var l=f(t[o],t,i);if("throw"!==l.type){var u=l.arg,p=u.value;return p&&"object"==s(p)&&n.call(p,"__await")?e.resolve(p.__await).then((function(t){r("next",t,a,c)}),(function(t){r("throw",t,a,c)})):e.resolve(p).then((function(t){u.value=t,a(u)}),(function(t){return r("throw",t,a,c)}))}c(l.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function E(e,r,n){var o=h;return function(i,a){if(o===m)throw Error("Generator is already running");if(o===y){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var s=n.delegate;if(s){var c=z(s,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===h)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=m;var l=f(e,r,n);if("normal"===l.type){if(o=n.done?y:g,l.arg===v)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(o=y,n.method="throw",n.arg=l.arg)}}}function z(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,z(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function j(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function A(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function O(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(j,this),this.reset(!0)}function G(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(s(e)+" is not iterable")}return _.prototype=w,o(S,"constructor",{value:w,configurable:!0}),o(w,"constructor",{value:_,configurable:!0}),_.displayName=u(w,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===_||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,u(t,l,"GeneratorFunction")),t.prototype=Object.create(S),t},e.awrap=function(t){return{__await:t}},C($.prototype),u($.prototype,c,(function(){return this})),e.AsyncIterator=$,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new $(p(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},C(S),u(S,l,"Generator"),u(S,a,(function(){return this})),u(S,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=G,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(A),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return s.type="throw",s.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),l=n.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),A(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;A(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:G(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function f(t,e){return v(t)||y(t,e)||g(t,e)||h()}function h(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function g(t,e){if(t){if("string"==typeof t)return m(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?m(t,e):void 0}}function m(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function y(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,s=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(s.push(n.value),s.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return s}}function v(t){if(Array.isArray(t))return t}function b(t,e,r,n,o,i,a){try{var s=t[i](a),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,o)}function _(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){b(i,n,o,a,s,"next",t)}function s(t){b(i,n,o,a,s,"throw",t)}a(void 0)}))}}var w={props:{isshow:Boolean,type:{type:String,default:""},goodsCategoryList:{type:Array,default:function(){return[]}},unitList:{type:Array,default:function(){return[]}},supplierList:{type:Array,default:function(){return[]}},confirm:Function},data:function(){return{isLoading:!1,currentPage:1,pageSize:10,goodsList:[{index:1,name:"",goods_category_id:"",spec:"",goods_unit:"",cost_price:"",sales_price:"",stock_num:"",supplier_id:null,barcode:""}]}},computed:{visible:{get:function(){return this.isshow},set:function(t){this.$emit("update:isshow",t)}}},mounted:function(){},methods:{clickDownloadUrl:function(){var t=location.origin+"/api/temporary/template_excel/food_stock/导入商品模板.xlsx";window.open(t,"_blank")},canceDialogHandle:function(){this.visible=!1},clickGenerateUniqueID:function(t){this.goodsList[t].barcode=Object(a["q"])()},clickAddGoods:function(){this.goodsList.push({index:this.goodsList.length+1,name:"",spec:"",goods_unit:"",cost_price:"",sales_price:"",stock_num:"",supplier_id:null,barcode:""})},clickDelectGoods:function(t){var e=t.index-1;this.goodsList.splice(e,1),this.goodsList.forEach((function(t,e){t.index=e+1}))},clickDetermineDialog:function(){for(var t=[],e=/^(?:[1-9]\d*|0)(?:\.\d{1,2})?$/,r=/^[a-zA-Z0-9_-]+$/,n=new Set,o=0;o<this.goodsList.length;o++){var a=this.goodsList[o];if(!a.name)return this.$message.error("新增下的第".concat(a.index,"条，请输入商品名称"));if(!a.goods_category_id)return this.$message.error("新增下的第".concat(a.index,"条，请选择分类"));if(!a.spec)return this.$message.error("新增下的第".concat(a.index,"条，请输入规格"));if(!a.goods_unit)return this.$message.error("新增下的第".concat(a.index,"条，请选择单位"));if(a.cost_price&&!e.test(a.cost_price))return this.$message.error("多规格下的第".concat(o+1,"条，请正确输入成本价"));if(Number(a.sales_price)<0||!e.test(a.sales_price))return this.$message.error("多规格下的第".concat(o+1,"条，请正确输入零售价"));if(!a.barcode)return this.$message.error("新增下的第".concat(a.index,"条，请输入条码"));if(!r.test(a.barcode))return this.$message.error("新增下的第".concat(a.index,"条，条码由：数字、字母、'_'、'-'组成"));if(n.has(a.barcode))return this.$message.error("新增下的第".concat(a.index,"条,条码有相同,请修改"));n.add(a.barcode);var s={name:a.name,goods_category_id:a.goods_category_id,spec:a.spec,goods_unit:a.goods_unit,cost_price:a.cost_price?i["a"].times(a.cost_price,100):0,sales_price:a.sales_price?i["a"].times(a.sales_price,100):0,stock_num:a.stock_num?Number(a.stock_num):0,supplier_id:a.supplier_id?a.supplier_id:null,barcode:a.barcode};t.push(s)}this.setGoodsAddOrModify(t)},setGoodsAddOrModify:function(t){var e=this;return _(d().mark((function r(){var n,o,i,s;return d().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return e.isLoading=!0,r.next=3,Object(a["Z"])(e.$apis.apiBackgroundStoreGoodsBatchAddPost({goods_list:t}));case 3:if(n=r.sent,o=f(n,2),i=o[0],s=o[1],e.isLoading=!1,!i){r.next=11;break}return e.$message.error(i.message),r.abrupt("return");case 11:0===s.code?(e.visible=!1,e.$emit("confirm","search")):e.$message.error(s.msg);case 12:case"end":return r.stop()}}),r)})))()},getXlsxData:function(t,e){var r,n=this,o=[{key:"name",label:"商品名称"},{key:"goods_category_id",label:"分类"},{key:"spec",label:"规格"},{key:"goods_unit",label:"单位"},{key:"cost_price",label:"成本价"},{key:"sales_price",label:"零售价"},{key:"stock_num",label:"库存"},{key:"supplier_id",label:"供应商"},{key:"barcode",label:"条码"}],i={};o.forEach((function(t){i[t.label]=t.key}));var a=t.slice(1).map((function(t,e){var r={},o=function(e){if(i[e])if("分类"===e){var o=n.goodsCategoryList.find((function(r){return r.name===String(t[e])}));r[i[e]]=o?o.id:""}else if("单位"===e){var a=n.unitList.find((function(r){return r.name===String(t[e])}));r[i[e]]=a?a.id:""}else if("供应商"===e){var s=n.supplierList.find((function(r){return r.name===String(t[e])}));r[i[e]]=s?s.id:""}else r[i[e]]=t[e]};for(var a in t)o(a);return r.index=n.goodsList.length+(e+1),r}));(r=this.goodsList).push.apply(r,c(a))},handleSizeChange:function(t){this.pageSize=t},handleCurrentChange:function(t){this.currentPage=t}}},k=w,x=(r("7d10"),r("2877")),L=Object(x["a"])(k,n,o,!1,null,"bce84f5c",null);e["default"]=L.exports},"7d10":function(t,e,r){"use strict";r("079d")}}]);