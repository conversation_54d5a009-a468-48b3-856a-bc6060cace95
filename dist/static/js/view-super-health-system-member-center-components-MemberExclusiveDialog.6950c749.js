(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-member-center-components-MemberExclusiveDialog"],{"1cbe":function(e,t,r){},be36:function(e,t,r){"use strict";r.r(t);var i=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{title:e.title,visible:e.isShowDialog,width:e.width,"custom-class":"ps-dialog","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.isShowDialog=t},close:e.handlerClose}},[t("el-form",{ref:"memberFormRef",attrs:{model:e.dialogForm,"label-width":"110px",rules:e.dialogFormRules}},["add"==e.type||"edit"==e.type?t("div",[t("el-form-item",{attrs:{label:"权益",prop:"exclusive"}},[t("el-select",{staticClass:"w-250",attrs:{placeholder:"请选择"},model:{value:e.dialogForm.exclusive,callback:function(t){e.$set(e.dialogForm,"exclusive",t)},expression:"dialogForm.exclusive"}},e._l(e.exclusiveList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),t("el-form-item",{attrs:{label:"有效期",prop:"validityDate"}},[t("el-input",{staticClass:"w-250",attrs:{placeholder:"请输入有效期",autocomplete:"new-password",readOnly:"",onfocus:"this.removeAttribute('readonly')",onblur:"this.setAttribute('readonly',true)",type:"number"},model:{value:e.dialogForm.validityDate,callback:function(t){e.$set(e.dialogForm,"validityDate",t)},expression:"dialogForm.validityDate"}})],1),t("el-form-item",[t("div",{staticClass:"ps-flex"},[t("div",{class:["day-item",0==e.chooseCurrentIndex?"active":"no-active"],on:{click:function(t){return e.handlerChooseDate(0,30)}}},[e._v(" 30天")]),t("div",{class:["day-item",1==e.chooseCurrentIndex?"active":"no-active"],on:{click:function(t){return e.handlerChooseDate(1,90)}}},[e._v(" 90天")]),t("div",{class:["day-item",2==e.chooseCurrentIndex?"active":"no-active"],on:{click:function(t){return e.handlerChooseDate(2,180)}}},[e._v(" 180天")])])]),t("el-form-item",{attrs:{label:"价格",prop:"price"}},[t("el-input",{staticClass:"w-250",attrs:{placeholder:"请输入价格(元)",autocomplete:"new-password",readOnly:"",onfocus:"this.removeAttribute('readonly')",onblur:"this.setAttribute('readonly',true)"},model:{value:e.dialogForm.price,callback:function(t){e.$set(e.dialogForm,"price",t)},expression:"dialogForm.price"}})],1),t("el-form-item",{attrs:{label:"说明",prop:"remark"}},[t("el-input",{staticClass:"w-250",attrs:{placeholder:"最多输入12个字",type:"textarea",maxlength:"12","show-word-limit":""},model:{value:e.dialogForm.remark,callback:function(t){e.$set(e.dialogForm,"remark",t)},expression:"dialogForm.remark"}})],1),t("el-form-item",{attrs:{label:"排序",prop:"sort"}},[t("el-input",{staticClass:"w-250",attrs:{placeholder:"数字越小，位置越靠前",type:"number"},model:{value:e.dialogForm.sort,callback:function(t){e.$set(e.dialogForm,"sort",t)},expression:"dialogForm.sort"}})],1),t("el-form-item",[t("div",[e._v("已有排序："+e._s(e.alreadySort))])])],1):e._e(),"send"==e.type?t("div",[t("el-form-item",{attrs:{label:"用户手机号",prop:"phone"}},[t("el-select",{attrs:{filterable:"",placeholder:"请输入手机号关键字搜索",remote:"","reserve-keyword":"","remote-method":e.getPhoneList,loading:e.phoneLoading},on:{change:e.phoneChooseChange},model:{value:e.dialogForm.phone,callback:function(t){e.$set(e.dialogForm,"phone",t)},expression:"dialogForm.phone"}},e._l(e.phoneList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.phone,value:e.id}})})),1)],1),t("el-form-item",{attrs:{label:"用户姓名",prop:"name"}},[t("div",[e._v(e._s(e.dialogForm.name))])]),t("el-form-item",{attrs:{label:"用户ID",prop:"id"}},[t("div",[e._v(e._s(e.dialogForm.id))])]),t("el-form-item",{attrs:{label:"发放权益",prop:"distribute"}},[t("el-select",{staticClass:"w-250",attrs:{placeholder:"请选择"},model:{value:e.dialogForm.distribute,callback:function(t){e.$set(e.dialogForm,"distribute",t)},expression:"dialogForm.distribute"}},e._l(e.distributeList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1):e._e()]),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-cancel-btn",on:{click:function(t){e.isShowDialog=!1}}},[e._v("取 消")]),t("el-button",{directives:[{name:"loading",rawName:"v-loading",value:e.isConfirmLoading,expression:"isConfirmLoading"}],staticClass:"ps-btn",attrs:{type:"primary"},on:{click:e.handlerBtnConfirm}},[e._v("确 定")])],1)],1)},o=[],n=r("ed08");function a(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */a=function(){return t};var e,t={},r=Object.prototype,i=r.hasOwnProperty,o=Object.defineProperty||function(e,t,r){e[t]=r.value},n="function"==typeof Symbol?Symbol:{},s=n.iterator||"@@iterator",l=n.asyncIterator||"@@asyncIterator",c=n.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function d(e,t,r,i){var n=t&&t.prototype instanceof b?t:b,a=Object.create(n.prototype),s=new j(i||[]);return o(a,"_invoke",{value:D(e,r,s)}),a}function h(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var f="suspendedStart",m="suspendedYield",g="executing",v="completed",y={};function b(){}function w(){}function x(){}var L={};u(L,s,(function(){return this}));var F=Object.getPrototypeOf,k=F&&F(F(A([])));k&&k!==r&&i.call(k,s)&&(L=k);var _=x.prototype=b.prototype=Object.create(L);function S(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function C(e,t){function r(o,n,a,s){var l=h(e[o],e,n);if("throw"!==l.type){var c=l.arg,u=c.value;return u&&"object"==p(u)&&i.call(u,"__await")?t.resolve(u.__await).then((function(e){r("next",e,a,s)}),(function(e){r("throw",e,a,s)})):t.resolve(u).then((function(e){c.value=e,a(c)}),(function(e){return r("throw",e,a,s)}))}s(l.arg)}var n;o(this,"_invoke",{value:function(e,i){function o(){return new t((function(t,o){r(e,i,t,o)}))}return n=n?n.then(o,o):o()}})}function D(t,r,i){var o=f;return function(n,a){if(o===g)throw Error("Generator is already running");if(o===v){if("throw"===n)throw a;return{value:e,done:!0}}for(i.method=n,i.arg=a;;){var s=i.delegate;if(s){var l=$(s,i);if(l){if(l===y)continue;return l}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if(o===f)throw o=v,i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);o=g;var c=h(t,r,i);if("normal"===c.type){if(o=i.done?v:m,c.arg===y)continue;return{value:c.arg,done:i.done}}"throw"===c.type&&(o=v,i.method="throw",i.arg=c.arg)}}}function $(t,r){var i=r.method,o=t.iterator[i];if(o===e)return r.delegate=null,"throw"===i&&t.iterator.return&&(r.method="return",r.arg=e,$(t,r),"throw"===r.method)||"return"!==i&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+i+"' method")),y;var n=h(o,t.iterator,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,y;var a=n.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,y):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function O(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function j(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function A(t){if(t||""===t){var r=t[s];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,n=function r(){for(;++o<t.length;)if(i.call(t,o))return r.value=t[o],r.done=!1,r;return r.value=e,r.done=!0,r};return n.next=n}}throw new TypeError(p(t)+" is not iterable")}return w.prototype=x,o(_,"constructor",{value:x,configurable:!0}),o(x,"constructor",{value:w,configurable:!0}),w.displayName=u(x,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,x):(e.__proto__=x,u(e,c,"GeneratorFunction")),e.prototype=Object.create(_),e},t.awrap=function(e){return{__await:e}},S(C.prototype),u(C.prototype,l,(function(){return this})),t.AsyncIterator=C,t.async=function(e,r,i,o,n){void 0===n&&(n=Promise);var a=new C(d(e,r,i,o),n);return t.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},S(_),u(_,c,"Generator"),u(_,s,(function(){return this})),u(_,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var i in t)r.push(i);return r.reverse(),function e(){for(;r.length;){var i=r.pop();if(i in t)return e.value=i,e.done=!1,e}return e.done=!0,e}},t.values=A,j.prototype={constructor:j,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(O),!t)for(var r in this)"t"===r.charAt(0)&&i.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function o(i,o){return s.type="throw",s.arg=t,r.next=i,o&&(r.method="next",r.arg=e),!!o}for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n],s=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=i.call(a,"catchLoc"),c=i.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&i.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var n=o;break}}n&&("break"===e||"continue"===e)&&n.tryLoc<=t&&t<=n.finallyLoc&&(n=null);var a=n?n.completion:{};return a.type=e,a.arg=t,n?(this.method="next",this.next=n.finallyLoc,y):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),O(r),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var i=r.completion;if("throw"===i.type){var o=i.arg;O(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,r,i){return this.delegate={iterator:A(t),resultName:r,nextLoc:i},"next"===this.method&&(this.arg=e),y}},t}function s(e,t){return h(e)||d(e,t)||c(e,t)||l()}function l(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function c(e,t){if(e){if("string"==typeof e)return u(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?u(e,t):void 0}}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,i=Array(t);r<t;r++)i[r]=e[r];return i}function d(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var i,o,n,a,s=[],l=!0,c=!1;try{if(n=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(i=n.call(r)).done)&&(s.push(i.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}function h(e){if(Array.isArray(e))return e}function f(e,t,r,i,o,n,a){try{var s=e[n](a),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(i,o)}function m(e){return function(){var t=this,r=arguments;return new Promise((function(i,o){var n=e.apply(t,r);function a(e){f(n,i,o,a,s,"next",e)}function s(e){f(n,i,o,a,s,"throw",e)}a(void 0)}))}}function p(e){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},p(e)}var g={name:"MemberExclusiveDialog",props:{type:{type:String,default:""},title:{type:String,default:""},width:{type:String,default:"450px"},seqsList:{type:Array,default:function(){return[]}}},data:function(){var e=function(e,t,r){if(""===t)return r(new Error("不能为空"));var i=/^[1-9][0-9]{0,2}$/;i.test(t)?r():r(new Error("请输入大于零的正整数，上限为999"))},t=function(e,t,r){if(""===t)return r(new Error("不能为空"));var i=/((^[1-9][0-9]{0,2})|(([0]\.\d{1,2}|^[1-9][0-9]{0,2}\.\d{1,2})))$/;i.test(t)&&"0.0"!==t&&"0.00"!==t?r():r(new Error("请输入大于零的数值，上限为999.99"))};return{isLoading:!1,dialogForm:{exclusive:"",validityDate:"",price:"",remark:"",sort:"",phone:"",name:"",distribute:"",id:""},dialogFormRules:{exclusive:[{required:!0,message:"请选择权益",trigger:"change"}],validityDate:[{required:!0,message:"请输入有效期天数",trigger:"blur"},{required:!0,validator:e,trigger:"blur"}],price:[{required:!0,message:"请输入价格",trigger:"blur"},{required:!0,validator:t,trigger:"blur"}],remark:[{required:!0,message:"请输入说明",trigger:"blur"}],sort:[{required:!0,message:"请输入排序",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号搜索选择",trigger:"change"}],id:[{required:!0,message:"用户ID不能为空，请重新选择用户手机号",trigger:"change"}],distribute:[{required:!0,message:"请选择方法权益",trigger:"change"}]},autoLabelNameList:[],autoLabelIdList:[],labelNameList:[],labelList:[],exclusiveList:[],distributeList:[],alreadySort:"",isShowDialog:!1,chooseCurrentIndex:-1,phoneList:[],phoneLoading:""}},computed:{visible:{get:function(){return this.isShowDialog},set:function(e){this.$emit("update:isShowDialog",e)}}},watch:{visible:function(){this.visible?this.initData():this.$refs.memberFormRef.resetFields()},seqsList:function(e){e&&Array.isArray(e)&&(this.alreadySort=e.join(","))}},created:function(){},mounted:function(){},methods:{initData:function(){"send"===this.type&&(this.getRightsSettingList(),this.getPhoneList()),this.getMemberLabel()},showDialog:function(e){this.isShowDialog=e,e?this.initData():this.handleClose()},setDialogData:function(e){if(e&&"object"===p(e)){var t=Object(n["f"])(this.dialogForm),r=e.member_permissions&&e.member_permissions.length>0?e.member_permissions[0]:"";t.id=e.id||"",t.exclusive=r,t.validityDate=e.days||"",t.price=Object(n["i"])(e.origin_fee)||"",t.sort=e.seq||"",t.remark=e.remark||"",this.$set(this,"dialogForm",t)}},handlerBtnConfirm:function(){var e=this;this.$refs.memberFormRef.validate((function(t){if(t){var r,i={},o="";switch(e.type){case"add":i={member_permissions:[e.dialogForm.exclusive],days:e.dialogForm.validityDate,origin_fee:Object(n["Y"])(e.dialogForm.price),remark:e.dialogForm.remark,seq:e.dialogForm.sort},r=e.$apis.apiBackgroundMemberRightsSettingAddPost(i),o="权益添加成功";break;case"edit":i={id:e.dialogForm.id,member_permissions:[e.dialogForm.exclusive],days:e.dialogForm.validityDate,origin_fee:Object(n["Y"])(e.dialogForm.price),remark:e.dialogForm.remark,seq:e.dialogForm.sort},r=e.$apis.apiBackgroundMemberRightsSettingModifyPost(i),o="权益编辑成功";break;case"send":i={receive_type:"manual_release",rights_setting:e.dialogForm.distribute,user_id:e.dialogForm.id},r=e.$apis.apiBackgroundMemberRightsReceiveAddPost(i),o="手动发放权益成功";break}e.confirmOperation(r,o)}}))},confirmOperation:function(e,t){var r=this;return m(a().mark((function i(){var o,l,c,u;return a().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(!r.isLoading){i.next=2;break}return i.abrupt("return");case 2:return r.isLoading=!0,i.next=5,Object(n["Z"])(e);case 5:if(o=i.sent,l=s(o,2),c=l[0],u=l[1],r.isLoading=!1,!c){i.next=12;break}return i.abrupt("return",r.$message.error(c.message));case 12:u&&0===u.code?(r.$message.success(t),r.$emit("confirm",r.type),r.isShowDialog=!1):r.$message.error(u.msg||"失败");case 13:case"end":return i.stop()}}),i)})))()},handleClose:function(e){this.isLoading=!1,this.visible=!1,this.$refs.memberFormRef.resetFields()},getMemberLabel:function(){var e=this;return m(a().mark((function t(){var r;return a().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundMemberMemberPermissionListPost({page:1,page_size:99999});case 2:r=t.sent,0===r.code?e.exclusiveList=r.data.results:e.$message.error(r.msg);case 4:case"end":return t.stop()}}),t)})))()},getRightsSettingList:function(){var e=this;return m(a().mark((function t(){var r,i;return a().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundMemberRightsSettingListPost({page:1,page_size:99999});case 2:r=t.sent,0===r.code?(i=r.data.results||[],i&&(i=i.map((function(e){var t=e.member_permissions_name&&Array.isArray(e.member_permissions_name)?e.member_permissions_name.join(" "):"",r=e.days?"（"+e.days+"天）":"0(天)";return e.name=t+r,e}))),e.distributeList=Object(n["f"])(i)):e.$message.error(r.msg);case 4:case"end":return t.stop()}}),t)})))()},handlerChooseDate:function(e,t){this.chooseCurrentIndex=e,this.$set(this.dialogForm,"validityDate",t)},getPhoneList:function(e){var t=this;return m(a().mark((function r(){var i,o,l,c,u,d;return a().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return t.phoneLoading=!0,r.next=3,Object(n["Z"])(t.$apis.apiBackgroundMemberMemberUserListPost({phone:e,page:1,page_size:99999}));case 3:if(i=r.sent,o=s(i,2),l=o[0],c=o[1],t.phoneLoading=!1,!l){r.next=10;break}return r.abrupt("return");case 10:c&&0===c.code&&(u=c.data||{},d=u.results||[],d&&(t.phoneList=Object(n["f"])(d)));case 11:case"end":return r.stop()}}),r)})))()},phoneChooseChange:function(e){var t=this.phoneList.find((function(t){return t.id===e}));t&&(this.$set(this.dialogForm,"name",t.nickname),this.$set(this.dialogForm,"id",t.id))},handlerClose:function(){this.chooseCurrentIndex=-1,this.$refs.memberFormRef&&this.$refs.memberFormRef.resetFields()}}},v=g,y=(r("f50f7"),r("2877")),b=Object(y["a"])(v,i,o,!1,null,"57e486b2",null);t["default"]=b.exports},f50f7:function(e,t,r){"use strict";r("1cbe")}}]);