(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-public-banner-add"],{"2cfce":function(t,e,r){"use strict";r("5fde")},"5fde":function(t,e,r){},fb71:function(t,e,r){"use strict";r.r(e);var a=function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"add-banner"},[e("el-form",{ref:"formRef",attrs:{model:t.formData,rules:t.formRules,"label-width":"120px"}},[e("el-form-item",{attrs:{label:"名称：",prop:"name"}},[e("el-input",{staticClass:"w-300 ps-input",attrs:{placeholder:"不超过20字",maxlength:"20"},model:{value:t.formData.name,callback:function(e){t.$set(t.formData,"name",e)},expression:"formData.name"}})],1),e("el-form-item",{attrs:{label:"图片：",prop:"fileLists"}},[e("file-upload",{ref:"faceFileRef",staticClass:"avatar-uploader",attrs:{fileList:t.formData.fileLists,type:"enclosure","before-upload":t.beforeUpload,"show-file-list":!1},on:{fileLists:t.getFileLists}},[t.formData.fileLists.length?e("img",{staticClass:"avatar",attrs:{src:t.formData.fileLists[0].url},on:{click:t.clearFileHandle}}):e("i",{staticClass:"el-icon-plus avatar-uploader-icon"})]),e("span",{staticClass:"tips m-l-20"},[t._v("上传格式 jpg/png, 比例:750*360, 图片最大不能超过10M")])],1),e("el-form-item",{attrs:{label:"有效日期：",prop:"effective_time"}},[e("el-date-picker",{attrs:{type:"datetime",placeholder:"选择日期","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:t.formData.effective_time,callback:function(e){t.$set(t.formData,"effective_time",e)},expression:"formData.effective_time"}}),e("el-checkbox",{staticClass:"m-l-40",model:{value:t.isForever,callback:function(e){t.isForever=e},expression:"isForever"}},[t._v("永久")])],1),e("el-form-item",{attrs:{label:"跳转地址：",prop:"jump_type"}},[e("el-radio-group",{staticClass:"ps-radio",model:{value:t.formData.jump_type,callback:function(e){t.$set(t.formData,"jump_type",e)},expression:"formData.jump_type"}},[e("el-radio",{attrs:{label:"inner",value:"inner"}},[t._v("内部界面")]),"super"===t.type?e("el-radio",{attrs:{label:"outsite",value:"outsite"}},[t._v("外部链接")]):t._e()],1),"inner"===t.formData.jump_type?e("el-form-item",{staticClass:"p-b-20",attrs:{label:"",prop:"jump_innerselect_type"}},[e("el-select",{staticClass:"ps-select",attrs:{placeholder:"请选择跳转地址",clearable:""},model:{value:t.formData.jump_innerselect_type,callback:function(e){t.$set(t.formData,"jump_innerselect_type",e)},expression:"formData.jump_innerselect_type"}},t._l(t.innerList,(function(t){return e("el-option",{key:t.value,attrs:{label:t.name,value:t.value}})})),1)],1):"outsite"===t.formData.jump_type&&"super"===t.type?e("el-form-item",{staticClass:"p-b-20",attrs:{"label-width":"80px",label:"地址：",prop:"jump_url"}},[e("el-input",{staticClass:"w-300 ps-input",model:{value:t.formData.jump_url,callback:function(e){t.$set(t.formData,"jump_url",e)},expression:"formData.jump_url"}})],1):t._e()],1),"super"===t.type?e("el-form-item",{attrs:{label:"显示项目点：",prop:"show_orgs"}},[e("company-select",{staticClass:"search-item-w ps-select",attrs:{clearable:"",multiple:"","collapse-tags":"",filterable:"",placeholder:"",disabled:t.formData.is_all_orgs},on:{change:t.changeCompany},model:{value:t.formData.show_orgs,callback:function(e){t.$set(t.formData,"show_orgs",e)},expression:"formData.show_orgs"}}),e("el-checkbox",{staticClass:"m-l-40",model:{value:t.formData.is_all_orgs,callback:function(e){t.$set(t.formData,"is_all_orgs",e)},expression:"formData.is_all_orgs"}},[t._v("默认全选")])],1):t._e(),e("el-form-item",{attrs:{label:"优先级：",prop:"priority"}},[e("el-input",{staticClass:"w-300 ps-input",attrs:{placeholder:""},model:{value:t.formData.priority,callback:function(e){t.$set(t.formData,"priority",e)},expression:"formData.priority"}})],1),e("div",{staticClass:"error-text m-t-20",staticStyle:{"margin-left":"120px"}},[t._v("优先级数值小的优先显示")]),e("el-form-item",[e("div",{staticClass:"m-t-50"},[e("el-button",{attrs:{plain:""},on:{click:t.closeHandler}},[t._v("取消")]),e("el-button",{staticClass:"ps-origin-btn",attrs:{type:"primary"},on:{click:function(e){return t.submitHandler("publish")}}},[t._v("确认发布")]),t.is_first?t._e():e("el-button",{staticClass:"ps-origin-btn",attrs:{type:"primary"},on:{click:function(e){return t.submitHandler("unpublished")}}},[t._v("保存不发布")])],1)])],1)],1)},n=[],i=r("ed08"),o=r("6e71");function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function l(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */l=function(){return e};var t,e={},r=Object.prototype,a=r.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function m(t,e,r,a){var i=e&&e.prototype instanceof _?e:_,o=Object.create(i.prototype),s=new S(a||[]);return n(o,"_invoke",{value:C(t,r,s)}),o}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=m;var h="suspendedStart",d="suspendedYield",v="executing",y="completed",g={};function _(){}function b(){}function w(){}var D={};f(D,o,(function(){return this}));var L=Object.getPrototypeOf,j=L&&L(L(B([])));j&&j!==r&&a.call(j,o)&&(D=j);var x=w.prototype=_.prototype=Object.create(D);function k(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function $(t,e){function r(n,i,o,l){var u=p(t[n],t,i);if("throw"!==u.type){var c=u.arg,f=c.value;return f&&"object"==s(f)&&a.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,o,l)}),(function(t){r("throw",t,o,l)})):e.resolve(f).then((function(t){c.value=t,o(c)}),(function(t){return r("throw",t,o,l)}))}l(u.arg)}var i;n(this,"_invoke",{value:function(t,a){function n(){return new e((function(e,n){r(t,a,e,n)}))}return i=i?i.then(n,n):n()}})}function C(e,r,a){var n=h;return function(i,o){if(n===v)throw Error("Generator is already running");if(n===y){if("throw"===i)throw o;return{value:t,done:!0}}for(a.method=i,a.arg=o;;){var s=a.delegate;if(s){var l=E(s,a);if(l){if(l===g)continue;return l}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(n===h)throw n=y,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);n=v;var u=p(e,r,a);if("normal"===u.type){if(n=a.done?y:d,u.arg===g)continue;return{value:u.arg,done:a.done}}"throw"===u.type&&(n=y,a.method="throw",a.arg=u.arg)}}}function E(e,r){var a=r.method,n=e.iterator[a];if(n===t)return r.delegate=null,"throw"===a&&e.iterator.return&&(r.method="return",r.arg=t,E(e,r),"throw"===r.method)||"return"!==a&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+a+"' method")),g;var i=p(n,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,g;var o=i.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,g):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function O(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function F(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(O,this),this.reset(!0)}function B(e){if(e||""===e){var r=e[o];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,i=function r(){for(;++n<e.length;)if(a.call(e,n))return r.value=e[n],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(s(e)+" is not iterable")}return b.prototype=w,n(x,"constructor",{value:w,configurable:!0}),n(w,"constructor",{value:b,configurable:!0}),b.displayName=f(w,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,f(t,c,"GeneratorFunction")),t.prototype=Object.create(x),t},e.awrap=function(t){return{__await:t}},k($.prototype),f($.prototype,u,(function(){return this})),e.AsyncIterator=$,e.async=function(t,r,a,n,i){void 0===i&&(i=Promise);var o=new $(m(t,r,a,n),i);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},k(x),f(x,c,"Generator"),f(x,o,(function(){return this})),f(x,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var a in e)r.push(a);return r.reverse(),function t(){for(;r.length;){var a=r.pop();if(a in e)return t.value=a,t.done=!1,t}return t.done=!0,t}},e.values=B,S.prototype={constructor:S,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(F),!e)for(var r in this)"t"===r.charAt(0)&&a.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function n(a,n){return s.type="throw",s.arg=e,r.next=a,n&&(r.method="next",r.arg=t),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var l=a.call(o,"catchLoc"),u=a.call(o,"finallyLoc");if(l&&u){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,g):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),F(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var a=r.completion;if("throw"===a.type){var n=a.arg;F(r)}return n}}throw Error("illegal catch attempt")},delegateYield:function(e,r,a){return this.delegate={iterator:B(e),resultName:r,nextLoc:a},"next"===this.method&&(this.arg=t),g}},e}function u(t,e){return h(t)||p(t,e)||f(t,e)||c()}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(t,e){if(t){if("string"==typeof t)return m(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?m(t,e):void 0}}function m(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,a=Array(e);r<e;r++)a[r]=t[r];return a}function p(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var a,n,i,o,s=[],l=!0,u=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(a=i.call(r)).done)&&(s.push(a.value),s.length!==e);l=!0);}catch(t){u=!0,n=t}finally{try{if(!l&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(u)throw n}}return s}}function h(t){if(Array.isArray(t))return t}function d(t,e,r,a,n,i,o){try{var s=t[i](o),l=s.value}catch(t){return void r(t)}s.done?e(l):Promise.resolve(l).then(a,n)}function v(t){return function(){var e=this,r=arguments;return new Promise((function(a,n){var i=t.apply(e,r);function o(t){d(i,a,n,o,s,"next",t)}function s(t){d(i,a,n,o,s,"throw",t)}o(void 0)}))}}var y={name:"WithdrawOrder",components:{CompanySelect:o["a"]},data:function(){var t=this,e=function(t,e,r){var a=/^\d+$/;if(a.test(e)){if(e>100)return r(new Error("请输入小于100的数字"));r()}else r(new Error("请输入小于100的数字"))},r=function(e,r,a){if(t.isForever)a();else{if(!r)return a(new Error("请输入有效日期或勾选永久"));a()}},a=function(e,r,a){if(t.formData.is_all_orgs)a();else{if(!r)return a(new Error("请选择项目点"));a()}};return{type:"",operate:"",is_first:!1,isLoading:!1,apiList:{super:{add:"apiBackgroundAdminMarketingBannerAddPost",modify:"apiBackgroundAdminMarketingBannerModifyPost"},merchant:{add:"apiBackgroundMarketingMarketingBannerAddPost",modify:"apiBackgroundMarketingMarketingBannerModifyPost"}},isForever:!0,formData:{id:"",name:"",fileLists:[],effective_time:"",img_url:"",jump_type:"inner",jump_url:"",jump_innerselect_type:"",show_orgs:"",priority:"",status:"",is_all_orgs:!0},formRules:{name:[{required:!0,message:"名称不能为空",trigger:"blur"}],fileLists:[{required:!0,message:"图片不能为空",trigger:"blur"}],jump_type:[{required:!0,message:"请选择跳转类型",trigger:"blur"}],effective_time:[{validator:r,trigger:"blur"}],show_orgs:[{validator:a,trigger:"blur"}],priority:[{required:!0,validator:e,trigger:"blur"}]},innerList:[{value:"",name:"无"},{value:"app_management",name:"移动端管理"},{value:"charge",name:"充值"},{value:"order",name:"订单"},{value:"reservation",name:"预约点餐/我的预约"},{value:"report",name:"报餐"},{value:"account_info",name:"账户信息"},{value:"tray_bind",name:"托盘绑定"},{value:"jiaofei",name:"缴费中心"},{value:"intent_food",name:"意向菜谱"},{value:"attendance",name:"门禁考勤"},{value:"free_payment_setting",name:"免密代扣"},{value:"shop_feedback",name:"食堂建议"},{value:"order_review",name:"审核查询"}]}},created:function(){this.type=this.$route.query.role,this.operate=this.$route.params.type,this.$route.query.is_first&&(this.is_first=Boolean(this.$route.query.is_first)),this.initLoad()},mounted:function(){},watch:{"formData.effective_time":{handler:function(t){this.isForever=!t}},isForever:function(t){t&&(this.formData.effective_time="")}},computed:{},methods:{changeCompany:function(t){},initLoad:function(){if(this.$route.query.data){var t=this.$decodeQuery(this.$route.query.data);if(this.formData.id=t.id,this.formData.name=t.name,t.img_url){var e=t.img_url.split("/");e=e[e.length-1],this.formData.fileLists=[{name:e,status:"success",uid:(new Date).getTime(),url:t.img_url}]}this.formData.jump_type=t.jump_type,"inner"===t.jump_type?this.formData.jump_innerselect_type=t.jump_url:this.formData.jump_url=t.jump_url,this.formData.show_orgs=t.show_orgs,this.formData.priority=t.priority,this.formData.status=t.status,this.formData.effective_time=t.effective_time||"",this.formData.effective_time||(this.isForever=!0),this.formData.is_all_orgs=t.is_all_orgs}},formatQueryParams:function(t){var e={};for(var r in t){var a=Object(i["b"])(r);""!==t[r].value&&null!==t[r].value&&("select_time"!==a?e[a]=t[r].value:t[r].value&&t[r].value.length>0&&(e.start_time=t[r].value[0],e.end_time=t[r].value[1]))}return e},getFileLists:function(t){this.formData.fileLists=t},beforeUpload:function(t){var e=[".jpg",".png"];if(!e.includes(this.getSuffix(t.name)))return this.$message.error("上传图片只能是 jpg/png 格式"),!1;var r=t.size/1024/1024<10;return r?void 0:(this.$message.error("上传图片大小不能超过 10MB!"),!1)},getSuffix:function(t){var e=t.lastIndexOf("."),r="";return-1!==e&&(r=t.substring(e)),r},clearFileHandle:function(){this.$refs.faceFileRef.clearHandle(),this.formData.fileLists=[]},modifyHandle:function(t){var e=this;return v(l().mark((function r(){var a,n,i,o;return l().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return e.isLoading=!0,r.next=3,e.$to(e.$apis[e.apiList[e.type].modify](t));case 3:if(a=r.sent,n=u(a,2),i=n[0],o=n[1],e.isLoading=!1,!i){r.next=11;break}return e.$message.error(i.message),r.abrupt("return");case 11:0===o.code?(e.$message.success(o.msg),e.closeTab()):e.$message.error(o.msg);case 12:case"end":return r.stop()}}),r)})))()},addHandle:function(t){var e=this;return v(l().mark((function r(){var a,n,i,o;return l().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return e.isLoading=!0,r.next=3,e.$to(e.$apis[e.apiList[e.type].add](t));case 3:if(a=r.sent,n=u(a,2),i=n[0],o=n[1],e.isLoading=!1,!i){r.next=11;break}return e.$message.error(i.message),r.abrupt("return");case 11:0===o.code?(e.$message.success(o.msg),e.closeTab()):e.$message.error(o.msg);case 12:case"end":return r.stop()}}),r)})))()},getParams:function(){var t={name:this.formData.name,img_url:this.formData.fileLists[0].url,jump_type:this.formData.jump_type,priority:this.formData.priority};return"super"===this.type&&"outsite"===this.formData.jump_type&&(t.jump_url=this.formData.jump_url),"inner"===this.formData.jump_type&&(t.jump_url=this.formData.jump_innerselect_type),"super"===this.type&&(this.formData.is_all_orgs?t.is_all_orgs=this.formData.is_all_orgs:t.show_orgs=this.formData.show_orgs,this.isForever?t.effective_time=null:t.effective_time=this.formData.effective_time),"modify"===this.operate&&(t.id=this.formData.id),t},submitHandler:function(t){var e=this;this.$refs.formRef.validate((function(r){if(r){if(e.isLoading)return e.$message.error("请勿重复提交！");var a=e.getParams();a.status=t,"modify"===e.operate?(e.formData.status===t&&delete a.status,e.modifyHandle(a)):e.addHandle(a)}}))},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,r,a){"confirm"===e?(r.confirmButtonLoading=!0,t.closeTab(),r.confirmButtonLoading=!1):r.confirmButtonLoading||a()}}).then((function(t){})).catch((function(t){}))},closeTab:function(){this.$closeCurrentTab(this.$route.path)}}},g=y,_=(r("2cfce"),r("2877")),b=Object(_["a"])(g,a,n,!1,null,null,null);e["default"]=b.exports}}]);