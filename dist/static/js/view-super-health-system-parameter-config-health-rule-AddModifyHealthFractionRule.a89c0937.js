(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-parameter-config-health-rule-AddModifyHealthFractionRule","view-super-health-system-parameter-config-health-rule-components-BmiConfig","view-super-health-system-parameter-config-health-rule-components-EnergyConfig","view-super-health-system-parameter-config-health-rule-components-FoodDiversityConfig","view-super-health-system-parameter-config-health-rule-components-NutritionConfig","view-super-health-system-parameter-config-health-rule-components-SportConfig"],{"0eab":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t._self._c;return e("div",{staticClass:"energy-config"},[e("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formRef",attrs:{rules:t.formRuls,model:t.formData,size:"small"}},[e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("基本信息")])]),e("div",{staticStyle:{"max-width":"50%",padding:"0 20px"}},[e("el-form-item",{staticClass:"block-label",attrs:{label:"维度分值",prop:"score"}},[e("el-input",{staticClass:"ps-input",attrs:{placeholder:"请输入维度分值",disabled:t.disabled},model:{value:t.formData.score,callback:function(e){t.$set(t.formData,"score",e)},expression:"formData.score"}},[e("template",{slot:"append"},[t._v("分")])],2)],1),e("el-form-item",{staticClass:"block-label",attrs:{label:"提示语",prop:"tips"}},[e("el-input",{staticClass:"ps-input",attrs:{type:"textarea",autosize:{minRows:4,maxRows:6},placeholder:"请输入提示语",disabled:t.disabled},model:{value:t.formData.tips,callback:function(e){t.$set(t.formData,"tips",e)},expression:"formData.tips"}})],1),e("div",{staticClass:"form-content-box m-b-20"},[e("div",[t._v("变量值")]),t._l(t.formData.listText,(function(a,o){return e("div",{key:o,staticClass:"p-t-10"},[t._v(" "+t._s(a.text)+" "),e("span",{staticStyle:{color:"red"}},[t._v(t._s(a.tips))])])})),e("div",{staticClass:"p-t-10 p-b-10 flex-between"},[e("div",[t._v("规则配置")]),t.disabled?t._e():e("div",{staticClass:"align-r"},[e("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(e){return t.addRule()}}},[t._v("新增规则")])],1)]),e("div",{staticClass:"ps-flex-align-c"},[e("span",{staticClass:"p-r-10 p-t-5"},[t._v("如果")]),e("div",t._l(t.formData.config,(function(a,o){return e("div",{key:o,staticClass:"ps-flex-align-c flex-wrap"},[e("span",{staticClass:"p-r-10 p-t-5"},[t._v("x完成度在y正负")]),e("el-form-item",{attrs:{label:""}},[e("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:a.comparison_one,callback:function(e){t.$set(a,"comparison_one",e)},expression:"contentItem.comparison_one"}},t._l(t.comparisonList,(function(t,a){return e("el-option",{key:a,attrs:{label:t.label,value:t.value}})})),1)],1),e("div",{staticClass:"p-l-10"},[e("el-form-item",{attrs:{label:"",prop:"config."+o+".comparison_one_score",rules:{required:!0,message:"请输入",trigger:"blur"}}},[e("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"150px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:a.comparison_one_score,callback:function(e){t.$set(a,"comparison_one_score",e)},expression:"contentItem.comparison_one_score"}},[e("template",{slot:"append"},[t._v("%")])],2)],1)],1),e("div",{staticClass:"p-t-5 p-r-10"},[t._v("到")]),e("el-form-item",{attrs:{label:""}},[e("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:a.comparison_two,callback:function(e){t.$set(a,"comparison_two",e)},expression:"contentItem.comparison_two"}},t._l(t.comparisonList,(function(t,a){return e("el-option",{key:a,attrs:{label:t.label,value:t.value}})})),1)],1),e("div",{staticClass:"p-l-10"},[e("el-form-item",{attrs:{label:"",prop:"config."+o+".comparison_two_score",rules:{required:!0,message:"请输入",trigger:"blur"}}},[e("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"150px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:a.comparison_two_score,callback:function(e){t.$set(a,"comparison_two_score",e)},expression:"contentItem.comparison_two_score"}},[e("template",{slot:"append"},[t._v("%")])],2)],1)],1),e("el-form-item",{attrs:{label:"",prop:"config."+o+".operation",rules:{required:!0,message:"请选择",trigger:"blur"}}},[e("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:a.operation,callback:function(e){t.$set(a,"operation",e)},expression:"contentItem.operation"}},t._l(t.operationList,(function(t,a){return e("el-option",{key:a,attrs:{label:t.label,value:t.value}})})),1)],1),e("div",{staticClass:"p-l-10"},[e("el-form-item",{attrs:{label:"",prop:"config."+o+".operation_score",rules:{required:!0,message:"请选择",trigger:"blur"}}},[e("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"100px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:a.operation_score,callback:function(e){t.$set(a,"operation_score",e)},expression:"contentItem.operation_score"}})],1)],1),e("div",{staticClass:"p-t-5 p-r-10"},[t._v("分")]),e("div",{staticClass:"m-b-30"},[t.formData.config.length>1&&!t.disabled?e("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.removeRule(o)}}},[t._v(" 删除 ")]):t._e()],1)],1)})),0)])],2)],1)]),e("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[e("el-button",{staticStyle:{width:"120px"},on:{click:t.closeHandler}},[t._v("取消")]),e("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:t.submitHandler}},[t._v(" "+t._s("see"===t.type?"返回":"编辑")+" ")])],1)])],1)},s=[],i={props:{type:String,data:Object,disabled:Boolean},data:function(){return{isLoading:!1,formData:{score:"",tips:"",listText:[{text:"x千卡",tips:"（用户每餐摄入量）"},{text:"y千卡",tips:"（查表法计算得出每餐建议摄入量）"}],config:[{comparison_one:"",comparison_one_score:"",comparison_two:"",comparison_two_score:"",operation:"",operation_score:""}]},formRuls:{},comparisonList:[{label:"等于",value:"=="},{label:"大于",value:">"},{label:"大于等于",value:">="},{label:"小于",value:"<"},{label:"小于等于",value:"<="},{label:"不限制",value:""}],operationList:[{label:"加",value:"+"},{label:"减",value:"-"}]}},created:function(){this.formData=this.data},mounted:function(){},methods:{addRule:function(){this.formData.config.push({comparison_one:"",comparison_one_score:"",comparison_two:"",comparison_two_score:"",operation:"",operation_score:""})},removeRule:function(t){this.formData.config.splice(t,1)},submitHandler:function(){var t=this;this.$refs.formRef.validate((function(e){if(e){if(t.isLoading)return t.$message.error("请勿重复提交！");var a={key:t.formData.type};a[t.formData.type]={score:t.formData.score,tips:t.formData.tips,config:t.formData.config},t.$emit("submitHandler",a)}}))},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,a,o){"confirm"===e?t.$closeCurrentTab(t.$route.path):a.confirmButtonLoading||o()}}).then((function(t){})).catch((function(t){}))}}},r=i,n=(a("8c93"),a("2877")),l=Object(n["a"])(r,o,s,!1,null,"49e09af8",null);e["default"]=l.exports},1588:function(t,e,a){},"1a4e":function(t,e,a){"use strict";a("f337")},"34bf":function(t,e,a){},"3a5c":function(t,e,a){},"818c":function(t,e,a){},"825d":function(t,e,a){"use strict";a("1588")},"86fa":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t._self._c;return e("div",{staticClass:"bmi-config"},[e("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formRef",attrs:{rules:t.formRuls,model:t.formData,size:"small"}},[e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("基本信息")])]),e("div",{staticStyle:{"max-width":"50%",padding:"0 20px"}},[e("el-form-item",{staticClass:"block-label",attrs:{label:"维度分值",prop:"score"}},[e("el-input",{staticClass:"ps-input",attrs:{disabled:t.disabled,placeholder:"请输入维度分值"},model:{value:t.formData.score,callback:function(e){t.$set(t.formData,"score",e)},expression:"formData.score"}},[e("template",{slot:"append"},[t._v("分")])],2)],1),e("el-form-item",{staticClass:"block-label",attrs:{label:"提示语",prop:"tips"}},[e("el-input",{staticClass:"ps-input",attrs:{disabled:t.disabled,type:"textarea",autosize:{minRows:4,maxRows:6},placeholder:"请输入提示语"},model:{value:t.formData.tips,callback:function(e){t.$set(t.formData,"tips",e)},expression:"formData.tips"}})],1),e("div",{staticClass:"form-content-box m-b-20"},[e("div",[t._v("变量值")]),t._l(t.formData.listText,(function(a,o){return e("div",{key:o,staticClass:"p-t-10"},[t._v(" "+t._s(a.text)+" "),e("span",{staticStyle:{color:"red"}},[t._v(t._s(a.tips))])])})),e("div",{staticClass:"p-t-10 p-b-10 flex-between"},[e("div",[t._v("规则配置")]),t.disabled?t._e():e("div",{staticClass:"align-r"},[e("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(e){return t.addRule()}}},[t._v("新增规则")])],1)]),e("div",{staticClass:"p-b-20",staticStyle:{color:"red"}},[t._v("提示：选择不限制请选填0或其他")]),e("div",{staticClass:"ps-flex-align-c"},[e("span",{staticClass:"p-r-10 p-t-5"},[t._v("如果")]),e("div",t._l(t.formData.config,(function(a,o){return e("div",{key:o,staticClass:"ps-flex-align-c flex-wrap"},[e("span",{staticClass:"p-r-10 p-t-5"},[t._v("x完成度在y正负")]),e("el-form-item",{attrs:{label:""}},[e("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:a.comparison_one,callback:function(e){t.$set(a,"comparison_one",e)},expression:"contentItem.comparison_one"}},t._l(t.comparisonList,(function(t,a){return e("el-option",{key:a,attrs:{label:t.label,value:t.value}})})),1)],1),e("div",{staticClass:"p-l-10"},[e("el-form-item",{attrs:{label:"",prop:"config."+o+".comparison_one_score",rules:{required:!0,message:"请输入",trigger:"blur"}}},[e("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"150px"},attrs:{disabled:t.disabled,"show-word-limit":""},model:{value:a.comparison_one_score,callback:function(e){t.$set(a,"comparison_one_score",e)},expression:"contentItem.comparison_one_score"}},[e("template",{slot:"append"},[t._v("%")])],2)],1)],1),e("div",{staticClass:"p-t-5 p-r-10"},[t._v("到")]),e("el-form-item",{attrs:{label:""}},[e("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:a.comparison_two,callback:function(e){t.$set(a,"comparison_two",e)},expression:"contentItem.comparison_two"}},t._l(t.comparisonList,(function(t,a){return e("el-option",{key:a,attrs:{label:t.label,value:t.value}})})),1)],1),e("div",{staticClass:"p-l-10"},[e("el-form-item",{attrs:{label:"",prop:"config."+o+".comparison_two_score",rules:{required:!0,message:"请输入",trigger:"blur"}}},[e("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"150px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:a.comparison_two_score,callback:function(e){t.$set(a,"comparison_two_score",e)},expression:"contentItem.comparison_two_score"}},[e("template",{slot:"append"},[t._v("%")])],2)],1)],1),e("el-form-item",{attrs:{label:"",prop:"config."+o+".operation",rules:{required:!0,message:"请选择",trigger:"blur"}}},[e("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:a.operation,callback:function(e){t.$set(a,"operation",e)},expression:"contentItem.operation"}},t._l(t.operationList,(function(t,a){return e("el-option",{key:a,attrs:{label:t.label,value:t.value}})})),1)],1),e("div",{staticClass:"p-l-10"},[e("el-form-item",{attrs:{label:"",prop:"config."+o+".operation_score",rules:{required:!0,message:"请选择",trigger:"blur"}}},[e("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"100px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:a.operation_score,callback:function(e){t.$set(a,"operation_score",e)},expression:"contentItem.operation_score"}})],1)],1),e("div",{staticClass:"p-t-5 p-r-10"},[t._v("分")]),e("div",{staticClass:"m-b-30"},[t.formData.config.length>1&&!t.disabled?e("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.removeRule(o)}}},[t._v(" 删除 ")]):t._e()],1)],1)})),0)])],2)],1)]),e("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[e("el-button",{staticStyle:{width:"120px"},on:{click:t.closeHandler}},[t._v("取消")]),e("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:t.submitHandler}},[t._v(" "+t._s("see"===t.type?"返回":"编辑")+" ")])],1)])],1)},s=[],i={props:{type:String,data:Object,disabled:Boolean},data:function(){return{isLoading:!1,formData:{score:"",tips:"",listText:[{text:"x BMI",tips:"（此值代表人体BMI）"}],config:[{comparison_one:"",comparison_one_score:"",comparison_two:"",comparison_two_score:"",operation:"",operation_score:""}]},formRuls:{},comparisonList:[{label:"等于",value:"=="},{label:"大于",value:">"},{label:"大于等于",value:">="},{label:"小于",value:"<"},{label:"小于等于",value:"<="},{label:"不限制",value:""}],operationList:[{label:"加",value:"+"},{label:"减",value:"-"}]}},created:function(){this.formData=this.data},mounted:function(){},methods:{addRule:function(){this.formData.config.push({comparison_one:"",comparison_one_score:"",comparison_two:"",comparison_two_score:"",operation:"",operation_score:""})},removeRule:function(t){this.formData.config.splice(t,1)},submitHandler:function(){var t=this;this.$refs.formRef.validate((function(e){if(e){if(t.isLoading)return t.$message.error("请勿重复提交！");var a={key:t.formData.type};a[t.formData.type]={score:t.formData.score,tips:t.formData.tips,config:t.formData.config},t.$emit("submitHandler",a)}}))},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,a,o){"confirm"===e?t.$closeCurrentTab(t.$route.path):a.confirmButtonLoading||o()}}).then((function(t){})).catch((function(t){}))}}},r=i,n=(a("b611"),a("2877")),l=Object(n["a"])(r,o,s,!1,null,"5a446438",null);e["default"]=l.exports},"8c93":function(t,e,a){"use strict";a("f259")},"9b74":function(t,e,a){"use strict";a("818c")},a947:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t._self._c;return e("div",{staticClass:"sport-config"},[e("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formRef",attrs:{rules:t.formRuls,model:t.formData,size:"small"}},[e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("基本信息")])]),e("div",{staticStyle:{"max-width":"50%",padding:"0 20px"}},[e("el-form-item",{staticClass:"block-label",attrs:{label:"维度分值",prop:"score"}},[e("el-input",{staticClass:"ps-input",attrs:{placeholder:"请输入维度分值",disabled:t.disabled},model:{value:t.formData.score,callback:function(e){t.$set(t.formData,"score",e)},expression:"formData.score"}},[e("template",{slot:"append"},[t._v("分")])],2)],1),e("el-form-item",{staticClass:"block-label",attrs:{label:"提示语",prop:"tips"}},[e("el-input",{staticClass:"ps-input",attrs:{type:"textarea",autosize:{minRows:4,maxRows:6},placeholder:"请输入提示语",disabled:t.disabled},model:{value:t.formData.tips,callback:function(e){t.$set(t.formData,"tips",e)},expression:"formData.tips"}})],1),e("div",{staticClass:"form-content-box m-b-20"},[e("div",[t._v("变量值")]),e("div",{staticClass:"p-t-10"},[e("span",[t._v("x千卡")]),e("span",{staticStyle:{color:"red"}},[t._v("（用户每天运动量）")])]),e("div",{staticClass:"p-t-10 ps-flex-align-c"},[e("span",{staticClass:"p-r-10 p-t-5"},[t._v("y")]),e("el-form-item",{attrs:{label:""}},[e("el-input",{staticStyle:{width:"150px"},attrs:{disabled:t.disabled,"show-word-limit":""},model:{value:t.formData.kcal,callback:function(e){t.$set(t.formData,"kcal",e)},expression:"formData.kcal"}},[e("template",{slot:"append"},[t._v("千卡")])],2)],1),e("span",{staticClass:"p-r-10 p-t-5",staticStyle:{color:"red"}},[t._v("（用户每天运动量）")])],1),e("div",{staticClass:"p-t-10 p-b-10 flex-between"},[e("div",[t._v("规则配置")]),t.disabled?t._e():e("div",{staticClass:"align-r"},[e("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(e){return t.addRule()}}},[t._v("新增规则")])],1)]),e("div",{staticClass:"p-b-20",staticStyle:{color:"red"}},[t._v("提示：选择不限制请选填0或其他")]),e("div",{staticClass:"ps-flex-align-c"},[e("span",{staticClass:"p-r-10 p-t-5"},[t._v("如果")]),e("div",t._l(t.formData.config,(function(a,o){return e("div",{key:o,staticClass:"ps-flex-align-c flex-wrap"},[e("span",{staticClass:"p-r-10 p-t-5"},[t._v("x完成度在y正负")]),e("el-form-item",{attrs:{label:""}},[e("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:a.comparison_one,callback:function(e){t.$set(a,"comparison_one",e)},expression:"contentItem.comparison_one"}},t._l(t.comparisonList,(function(t,a){return e("el-option",{key:a,attrs:{label:t.label,value:t.value}})})),1)],1),e("div",{staticClass:"p-l-10"},[e("el-form-item",{attrs:{label:"",prop:"config."+o+".comparison_one_score",rules:{required:!0,message:"请输入",trigger:"blur"}}},[e("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"150px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:a.comparison_one_score,callback:function(e){t.$set(a,"comparison_one_score",e)},expression:"contentItem.comparison_one_score"}},[e("template",{slot:"append"},[t._v("%")])],2)],1)],1),e("div",{staticClass:"p-t-5 p-r-10"},[t._v("到")]),e("el-form-item",{attrs:{label:""}},[e("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:a.comparison_two,callback:function(e){t.$set(a,"comparison_two",e)},expression:"contentItem.comparison_two"}},t._l(t.comparisonList,(function(t,a){return e("el-option",{key:a,attrs:{label:t.label,value:t.value}})})),1)],1),e("div",{staticClass:"p-l-10"},[e("el-form-item",{attrs:{label:"",prop:"config."+o+".comparison_two_score",rules:{required:!0,message:"请输入",trigger:"blur"}}},[e("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"150px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:a.comparison_two_score,callback:function(e){t.$set(a,"comparison_two_score",e)},expression:"contentItem.comparison_two_score"}},[e("template",{slot:"append"},[t._v("%")])],2)],1)],1),e("el-form-item",{attrs:{label:"",prop:"config."+o+".operation",rules:{required:!0,message:"请选择",trigger:"blur"}}},[e("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:a.operation,callback:function(e){t.$set(a,"operation",e)},expression:"contentItem.operation"}},t._l(t.operationList,(function(t,a){return e("el-option",{key:a,attrs:{label:t.label,value:t.value}})})),1)],1),e("div",{staticClass:"p-l-10"},[e("el-form-item",{attrs:{label:"",prop:"config."+o+".operation_score",rules:{required:!0,message:"请选择",trigger:"blur"}}},[e("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"100px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:a.operation_score,callback:function(e){t.$set(a,"operation_score",e)},expression:"contentItem.operation_score"}})],1)],1),e("div",{staticClass:"p-t-5 p-r-10"},[t._v("分")]),e("div",{staticClass:"m-b-30"},[t.formData.config.length>1&&!t.disabled?e("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.removeRule(o)}}},[t._v(" 删除 ")]):t._e()],1)],1)})),0)])])],1)]),e("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[e("el-button",{staticStyle:{width:"120px"},on:{click:t.closeHandler}},[t._v("取消")]),e("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:t.submitHandler}},[t._v(" "+t._s("see"===t.type?"返回":"编辑")+" ")])],1)])],1)},s=[],i={props:{type:String,data:Object,disabled:Boolean},data:function(){return{isLoading:!1,formData:{score:"",tips:"",kcal:"",config:[{comparison_one:"",comparison_one_score:"",comparison_two:"",comparison_two_score:"",operation:"",operation_score:""}]},formRuls:{},comparisonList:[{label:"等于",value:"=="},{label:"大于",value:">"},{label:"大于等于",value:">="},{label:"小于",value:"<"},{label:"小于等于",value:"<="},{label:"不限制",value:""}],operationList:[{label:"加",value:"+"},{label:"减",value:"-"}]}},created:function(){this.formData=this.data},mounted:function(){},methods:{addRule:function(){this.formData.config.push({comparison_one:"",comparison_one_score:"",comparison_two:"",comparison_two_score:"",operation:"",operation_score:""})},removeRule:function(t){this.formData.config.splice(t,1)},submitHandler:function(){var t=this;this.$refs.formRef.validate((function(e){if(e){if(t.isLoading)return t.$message.error("请勿重复提交！");var a={key:t.formData.type};a[t.formData.type]={score:t.formData.score,tips:t.formData.tips,kcal:t.formData.kcal,config:t.formData.config},t.$emit("submitHandler",a)}}))},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,a,o){"confirm"===e?t.$closeCurrentTab(t.$route.path):a.confirmButtonLoading||o()}}).then((function(t){})).catch((function(t){}))}}},r=i,n=(a("825d"),a("2877")),l=Object(n["a"])(r,o,s,!1,null,"963d4e68",null);e["default"]=l.exports},b611:function(t,e,a){"use strict";a("34bf")},d8bf:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t._self._c;return e("div",{staticClass:"super_add_modify_health_fraction_rule container-wrapper"},["food"===t.dataType?e("food-diversity-config",{attrs:{data:t.data,type:t.type,disabled:t.disabled},on:{submitHandler:t.submitHandler}}):t._e(),"sport"===t.dataType?e("sport-config",{attrs:{disabled:t.disabled,data:t.data,type:t.type},on:{submitHandler:t.submitHandler}}):t._e(),"bmi"===t.dataType?e("bmi-config",{attrs:{disabled:t.disabled,data:t.data,type:t.type},on:{submitHandler:t.submitHandler}}):t._e(),"energy"===t.dataType?e("energy-config",{attrs:{disabled:t.disabled,data:t.data,type:t.type},on:{submitHandler:t.submitHandler}}):t._e(),"nutrition"===t.dataType?e("nutrition-config",{attrs:{data:t.data,type:t.type,disabled:t.disabled},on:{submitHandler:t.submitHandler}}):t._e()],1)},s=[],i=a("86fa"),r=a("e989"),n=a("a947"),l=a("0eab"),c=a("dfaf"),p=a("ed08");function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function d(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */d=function(){return e};var t,e={},a=Object.prototype,o=a.hasOwnProperty,s=Object.defineProperty||function(t,e,a){t[e]=a.value},i="function"==typeof Symbol?Symbol:{},r=i.iterator||"@@iterator",n=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function c(t,e,a){return Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,a){return t[e]=a}}function p(t,e,a,o){var i=e&&e.prototype instanceof g?e:g,r=Object.create(i.prototype),n=new I(o||[]);return s(r,"_invoke",{value:S(t,a,n)}),r}function f(t,e,a){try{return{type:"normal",arg:t.call(e,a)}}catch(t){return{type:"throw",arg:t}}}e.wrap=p;var m="suspendedStart",b="suspendedYield",v="executing",h="completed",_={};function g(){}function y(){}function x(){}var w={};c(w,r,(function(){return this}));var C=Object.getPrototypeOf,k=C&&C(C(B([])));k&&k!==a&&o.call(k,r)&&(w=k);var D=x.prototype=g.prototype=Object.create(w);function $(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function L(t,e){function a(s,i,r,n){var l=f(t[s],t,i);if("throw"!==l.type){var c=l.arg,p=c.value;return p&&"object"==u(p)&&o.call(p,"__await")?e.resolve(p.__await).then((function(t){a("next",t,r,n)}),(function(t){a("throw",t,r,n)})):e.resolve(p).then((function(t){c.value=t,r(c)}),(function(t){return a("throw",t,r,n)}))}n(l.arg)}var i;s(this,"_invoke",{value:function(t,o){function s(){return new e((function(e,s){a(t,o,e,s)}))}return i=i?i.then(s,s):s()}})}function S(e,a,o){var s=m;return function(i,r){if(s===v)throw Error("Generator is already running");if(s===h){if("throw"===i)throw r;return{value:t,done:!0}}for(o.method=i,o.arg=r;;){var n=o.delegate;if(n){var l=T(n,o);if(l){if(l===_)continue;return l}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(s===m)throw s=h,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);s=v;var c=f(e,a,o);if("normal"===c.type){if(s=o.done?h:b,c.arg===_)continue;return{value:c.arg,done:o.done}}"throw"===c.type&&(s=h,o.method="throw",o.arg=c.arg)}}}function T(e,a){var o=a.method,s=e.iterator[o];if(s===t)return a.delegate=null,"throw"===o&&e.iterator.return&&(a.method="return",a.arg=t,T(e,a),"throw"===a.method)||"return"!==o&&(a.method="throw",a.arg=new TypeError("The iterator does not provide a '"+o+"' method")),_;var i=f(s,e.iterator,a.arg);if("throw"===i.type)return a.method="throw",a.arg=i.arg,a.delegate=null,_;var r=i.arg;return r?r.done?(a[e.resultName]=r.value,a.next=e.nextLoc,"return"!==a.method&&(a.method="next",a.arg=t),a.delegate=null,_):r:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,_)}function R(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function H(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(R,this),this.reset(!0)}function B(e){if(e||""===e){var a=e[r];if(a)return a.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var s=-1,i=function a(){for(;++s<e.length;)if(o.call(e,s))return a.value=e[s],a.done=!1,a;return a.value=t,a.done=!0,a};return i.next=i}}throw new TypeError(u(e)+" is not iterable")}return y.prototype=x,s(D,"constructor",{value:x,configurable:!0}),s(x,"constructor",{value:y,configurable:!0}),y.displayName=c(x,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===y||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,x):(t.__proto__=x,c(t,l,"GeneratorFunction")),t.prototype=Object.create(D),t},e.awrap=function(t){return{__await:t}},$(L.prototype),c(L.prototype,n,(function(){return this})),e.AsyncIterator=L,e.async=function(t,a,o,s,i){void 0===i&&(i=Promise);var r=new L(p(t,a,o,s),i);return e.isGeneratorFunction(a)?r:r.next().then((function(t){return t.done?t.value:r.next()}))},$(D),c(D,l,"Generator"),c(D,r,(function(){return this})),c(D,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),a=[];for(var o in e)a.push(o);return a.reverse(),function t(){for(;a.length;){var o=a.pop();if(o in e)return t.value=o,t.done=!1,t}return t.done=!0,t}},e.values=B,I.prototype={constructor:I,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(H),!e)for(var a in this)"t"===a.charAt(0)&&o.call(this,a)&&!isNaN(+a.slice(1))&&(this[a]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var a=this;function s(o,s){return n.type="throw",n.arg=e,a.next=o,s&&(a.method="next",a.arg=t),!!s}for(var i=this.tryEntries.length-1;i>=0;--i){var r=this.tryEntries[i],n=r.completion;if("root"===r.tryLoc)return s("end");if(r.tryLoc<=this.prev){var l=o.call(r,"catchLoc"),c=o.call(r,"finallyLoc");if(l&&c){if(this.prev<r.catchLoc)return s(r.catchLoc,!0);if(this.prev<r.finallyLoc)return s(r.finallyLoc)}else if(l){if(this.prev<r.catchLoc)return s(r.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<r.finallyLoc)return s(r.finallyLoc)}}}},abrupt:function(t,e){for(var a=this.tryEntries.length-1;a>=0;--a){var s=this.tryEntries[a];if(s.tryLoc<=this.prev&&o.call(s,"finallyLoc")&&this.prev<s.finallyLoc){var i=s;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var r=i?i.completion:{};return r.type=t,r.arg=e,i?(this.method="next",this.next=i.finallyLoc,_):this.complete(r)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),_},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.finallyLoc===t)return this.complete(a.completion,a.afterLoc),H(a),_}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.tryLoc===t){var o=a.completion;if("throw"===o.type){var s=o.arg;H(a)}return s}}throw Error("illegal catch attempt")},delegateYield:function(e,a,o){return this.delegate={iterator:B(e),resultName:a,nextLoc:o},"next"===this.method&&(this.arg=t),_}},e}function f(t,e){return _(t)||h(t,e)||b(t,e)||m()}function m(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function b(t,e){if(t){if("string"==typeof t)return v(t,e);var a={}.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?v(t,e):void 0}}function v(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,o=Array(e);a<e;a++)o[a]=t[a];return o}function h(t,e){var a=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=a){var o,s,i,r,n=[],l=!0,c=!1;try{if(i=(a=a.call(t)).next,0===e){if(Object(a)!==a)return;l=!1}else for(;!(l=(o=i.call(a)).done)&&(n.push(o.value),n.length!==e);l=!0);}catch(t){c=!0,s=t}finally{try{if(!l&&null!=a.return&&(r=a.return(),Object(r)!==r))return}finally{if(c)throw s}}return n}}function _(t){if(Array.isArray(t))return t}function g(t,e,a,o,s,i,r){try{var n=t[i](r),l=n.value}catch(t){return void a(t)}n.done?e(l):Promise.resolve(l).then(o,s)}function y(t){return function(){var e=this,a=arguments;return new Promise((function(o,s){var i=t.apply(e,a);function r(t){g(i,o,s,r,n,"next",t)}function n(t){g(i,o,s,r,n,"throw",t)}r(void 0)}))}}var x={components:{foodDiversityConfig:r["default"],nutritionConfig:c["default"],sportConfig:n["default"],bmiConfig:i["default"],energyConfig:l["default"]},data:function(){return{type:"",dataType:"",disabled:!1,data:{}}},created:function(){this.type=this.$route.query.type,this.dataType=this.$route.query.dataType,Number(this.$route.query.disabled)?this.disabled=!0:this.disabled=!1,this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.data=this.$decodeQuery(this.$route.query.data)},setHealthyModify:function(t){var e=this;return y(d().mark((function a(){var o,s,i,r;return d().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,Object(p["Z"])(e.$apis.apiBackgroundAdminHealthyInfoHealthyModifyPost(t));case 2:if(o=a.sent,s=f(o,2),i=s[0],r=s[1],!i){a.next=9;break}return e.$message.error(i.message),a.abrupt("return");case 9:0===r.code?(e.$message.success(r.msg),e.$closeCurrentTab(e.$route.path)):e.$message.error(r.msg);case 10:case"end":return a.stop()}}),a)})))()},submitHandler:function(t){if("see"===this.type)return this.$closeCurrentTab(this.$route.path);this.setHealthyModify(t)}}},w=x,C=(a("ea36"),a("2877")),k=Object(C["a"])(w,o,s,!1,null,"849bb19a",null);e["default"]=k.exports},dfaf:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t._self._c;return e("div",{staticClass:"nutrition"},[e("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formRef",attrs:{rules:t.formRuls,model:t.formData,size:"small"}},[e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("基本信息")])]),e("div",{staticStyle:{"max-width":"50%",padding:"0 20px"}},[e("el-form-item",{staticClass:"block-label",attrs:{label:"维度分值",prop:"score"}},[e("el-input",{staticClass:"ps-input",attrs:{placeholder:"请输入维度分值",disabled:t.disabled},model:{value:t.formData.score,callback:function(e){t.$set(t.formData,"score",e)},expression:"formData.score"}},[e("template",{slot:"append"},[t._v("分")])],2)],1),e("el-form-item",{staticClass:"block-label",attrs:{label:"提示语",prop:"tips"}},[e("el-input",{staticClass:"ps-input",attrs:{type:"textarea",autosize:{minRows:4,maxRows:6},placeholder:"请输入提示语",disabled:t.disabled},model:{value:t.formData.tips,callback:function(e){t.$set(t.formData,"tips",e)},expression:"formData.tips"}})],1),t._l(t.formData.config,(function(a,o,s){return e("div",{key:s,staticClass:"form-content-box m-b-20"},[e("div",[t._v("变量值")]),t._l(a.listText,(function(a,o){return e("div",{key:o,staticClass:"p-t-10"},[t._v(" "+t._s(a.text)+" "),e("span",{staticStyle:{color:"red"}},[t._v(t._s(a.tips))])])})),e("div",{staticClass:"p-t-10 p-b-10 flex-between"},[e("div",[t._v("规则配置")]),t.disabled?t._e():e("div",{staticClass:"align-r"},[e("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(e){return t.addRule(a,o)}}},[t._v(" 新增规则 ")])],1)]),e("div",{staticClass:"p-b-20",staticStyle:{color:"red"}},[t._v("提示：选择不限制请选填0或其他")]),e("div",{staticClass:"ps-flex-align-c"},[e("span",{staticClass:"p-r-10 p-t-5"},[t._v("如果")]),e("div",t._l(a.content,(function(s,i){return e("div",{key:i,staticClass:"ps-flex-align-c flex-wrap"},[e("span",{staticClass:"p-r-10 p-t-5"},[t._v(t._s(a.unitText))]),e("el-form-item",{attrs:{label:""}},[e("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:s.comparison_one,callback:function(e){t.$set(s,"comparison_one",e)},expression:"contentItem.comparison_one"}},t._l(t.comparisonList,(function(t,a){return e("el-option",{key:a,attrs:{label:t.label,value:t.value}})})),1)],1),e("div",{staticClass:"p-l-10"},[e("el-form-item",{attrs:{label:"",prop:"config."+o+".content."+i+".comparison_one_score",rules:{required:!0,message:"请输入",trigger:"blur"}}},[e("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"150px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:s.comparison_one_score,callback:function(e){t.$set(s,"comparison_one_score",e)},expression:"contentItem.comparison_one_score"}},[e("template",{slot:"append"},[t._v("%")])],2)],1)],1),e("div",{staticClass:"p-t-5 p-r-10"},[t._v("到")]),e("el-form-item",{attrs:{label:""}},[e("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:s.comparison_two,callback:function(e){t.$set(s,"comparison_two",e)},expression:"contentItem.comparison_two"}},t._l(t.comparisonList,(function(t,a){return e("el-option",{key:a,attrs:{label:t.label,value:t.value}})})),1)],1),e("div",{staticClass:"p-l-10"},[e("el-form-item",{attrs:{label:"",prop:"config."+o+".content."+i+".comparison_two_score",rules:{required:!0,message:"请输入",trigger:"blur"}}},[e("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"150px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:s.comparison_two_score,callback:function(e){t.$set(s,"comparison_two_score",e)},expression:"contentItem.comparison_two_score"}},[e("template",{slot:"append"},[t._v("%")])],2)],1)],1),e("el-form-item",{attrs:{label:"",prop:"config."+o+".content."+i+".operation",rules:{required:!0,message:"请选择",trigger:"blur"}}},[e("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:s.operation,callback:function(e){t.$set(s,"operation",e)},expression:"contentItem.operation"}},t._l(t.operationList,(function(t,a){return e("el-option",{key:a,attrs:{label:t.label,value:t.value}})})),1)],1),e("div",{staticClass:"p-l-10"},[e("el-form-item",{attrs:{label:"",prop:"config."+o+".content."+i+".operation_score",rules:{required:!0,message:"请选择",trigger:"blur"}}},[e("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"100px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:s.operation_score,callback:function(e){t.$set(s,"operation_score",e)},expression:"contentItem.operation_score"}})],1)],1),e("div",{staticClass:"p-t-5 p-r-10"},[t._v("分")]),e("div",{staticClass:"m-b-30"},[a.content.length>1&&!t.disabled?e("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.removeRule(s,o,i)}}},[t._v(" 删除 ")]):t._e()],1)],1)})),0)])],2)}))],2)]),e("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[e("el-button",{staticStyle:{width:"120px"},on:{click:t.closeHandler}},[t._v("取消")]),e("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:t.submitHandler}},[t._v(" "+t._s("see"===t.type?"返回":"编辑")+" ")])],1)])],1)},s=[],i={props:{type:String,data:Object,disabled:Boolean},data:function(){return{isLoading:!1,formData:{score:"",tips:"",config:{protein:{listText:[{text:"x克",tips:"（用户每餐蛋白质摄入量）"},{text:"y克",tips:"（查表法计算得出用户每餐蛋白质推荐摄入量）"}],unitText:"x完成度在y正负",content:[{comparison_one:"",comparison_one_score:"",comparison_two:"",comparison_two_score:"",operation:"",operation_score:""}]},carbohydrate:{listText:[{text:"x1克",tips:"（用户每餐碳水化合物摄入量）"},{text:"y1克",tips:"（查表法计算得出用户每餐碳水化合物推荐摄入量）"}],unitText:"x1完成度在y1正负",content:[{comparison_one:"",comparison_one_score:"",comparison_two:"",comparison_two_score:"",operation:"",operation_score:""}]},axunge:{listText:[{text:"x2克",tips:"（用户每餐脂肪摄入量）"},{text:"y2克/餐",tips:"（查表法计算得出用户每餐脂肪推荐摄入量）"}],unitText:"x2完成度在y2正负",content:[{comparison_one:"",comparison_one_score:"",comparison_two:"",comparison_two_score:"",operation:"",operation_score:""}]}}},formRuls:{},comparisonList:[{label:"等于",value:"=="},{label:"大于",value:">"},{label:"大于等于",value:">="},{label:"小于",value:"<"},{label:"小于等于",value:"<="},{label:"不限制",value:""}],operationList:[{label:"加",value:"+"},{label:"减",value:"-"}]}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){for(var t in this.formData.score=this.data.score,this.formData.tips=this.data.tips,this.formData.type=this.data.type,this.data.config)this.formData.config[t].content=this.data.config[t]},addRule:function(t,e){this.formData.config[e].content.push({comparison_one:"",comparison_one_score:"",comparison_two:"",comparison_two_score:"",operation:"",operation_score:""})},removeRule:function(t,e,a){this.formData.config[e].content.splice(a,1)},submitHandler:function(){var t=this;this.$refs.formRef.validate((function(e){if(e){if(t.isLoading)return t.$message.error("请勿重复提交！");var a={key:t.formData.type},o={};for(var s in t.formData.config)o[s]=t.formData.config[s].content;a[t.formData.type]={score:t.formData.score,tips:t.formData.tips,config:o},t.$emit("submitHandler",a)}}))},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,a,o){"confirm"===e?t.$closeCurrentTab(t.$route.path):a.confirmButtonLoading||o()}}).then((function(t){})).catch((function(t){}))}}},r=i,n=(a("1a4e"),a("2877")),l=Object(n["a"])(r,o,s,!1,null,"12e29f82",null);e["default"]=l.exports},e989:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t._self._c;return e("div",{staticClass:"food-diversity"},[e("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formRef",attrs:{rules:t.formRuls,model:t.formData,size:"small"}},[e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("基本信息")])]),e("div",{staticStyle:{"max-width":"50%",padding:"0 20px"}},[e("el-form-item",{staticClass:"block-label",attrs:{label:"维度分值",prop:"score"}},[e("el-input",{staticClass:"ps-input",attrs:{disabled:t.disabled,placeholder:"请输入维度分值"},model:{value:t.formData.score,callback:function(e){t.$set(t.formData,"score",e)},expression:"formData.score"}},[e("template",{slot:"append"},[t._v("分")])],2)],1),e("el-form-item",{staticClass:"block-label",attrs:{label:"提示语",prop:"tips"}},[e("el-input",{staticClass:"ps-input",attrs:{type:"textarea",autosize:{minRows:4,maxRows:6},placeholder:"请输入提示语",disabled:t.disabled},model:{value:t.formData.tips,callback:function(e){t.$set(t.formData,"tips",e)},expression:"formData.tips"}})],1),t._l(t.formData.config,(function(a,o,s){return e("div",{key:s,staticClass:"form-content-box m-b-20"},[e("div",[t._v("变量值")]),t._l(a.listText,(function(a,o){return e("div",{key:o,staticClass:"p-t-10"},[t._v(" "+t._s(a.text)+" "),e("span",{staticStyle:{color:"red"}},[t._v(t._s(a.tips))])])})),e("div",{staticClass:"p-t-10 p-b-10 flex-between"},[e("div",[t._v("规则配置")]),t.disabled?t._e():e("div",{staticClass:"align-r"},[e("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(e){return t.addRule(a,o)}}},[t._v(" 新增规则 ")])],1)]),e("div",{staticClass:"ps-flex-align-c"},[e("span",{staticClass:"p-r-10 p-t-5"},[t._v("如果")]),e("div",t._l(a.content,(function(s,i){return e("div",{key:i,staticClass:"ps-flex-align-c flex-wrap"},[e("span",{staticClass:"p-r-10 p-t-5"},[t._v(t._s(a.unitText))]),e("el-form-item",{attrs:{label:"",prop:"config."+o+".content."+i+".comparison",rules:{required:!0,message:"请选择",trigger:"blur"}}},[e("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:s.comparison,callback:function(e){t.$set(s,"comparison",e)},expression:"contentItem.comparison"}},t._l(t.comparisonList,(function(t,a){return e("el-option",{key:a,attrs:{label:t.label,value:t.value}})})),1)],1),e("div",{staticClass:"p-l-10"},[e("el-form-item",{attrs:{label:"",prop:"config."+o+".content."+i+".comparison_score",rules:{required:!0,message:"请输入",trigger:"blur"}}},[e("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"100px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:s.comparison_score,callback:function(e){t.$set(s,"comparison_score",e)},expression:"contentItem.comparison_score"}})],1)],1),e("div",{staticClass:"p-t-5 p-r-10"},[t._v("执行")]),e("el-form-item",{attrs:{label:"",prop:"config."+o+".content."+i+".operation",rules:{required:!0,message:"请选择",trigger:"blur"}}},[e("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:s.operation,callback:function(e){t.$set(s,"operation",e)},expression:"contentItem.operation"}},t._l(t.operationList,(function(t,a){return e("el-option",{key:a,attrs:{label:t.label,value:t.value}})})),1)],1),e("div",{staticClass:"p-l-10"},[e("el-form-item",{attrs:{label:"",prop:"config."+o+".content."+i+".operation_score",rules:{required:!0,message:"请输入",trigger:"blur"}}},[e("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"100px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:s.operation_score,callback:function(e){t.$set(s,"operation_score",e)},expression:"contentItem.operation_score"}})],1)],1),e("div",{staticClass:"p-t-5 p-r-10"},[t._v("分")]),e("div",{staticClass:"m-b-30"},[a.content.length>1&&!t.disabled?e("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.removeRule(s,o,i)}}},[t._v(" 删除 ")]):t._e()],1)],1)})),0)])],2)}))],2)]),e("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[e("el-button",{staticStyle:{width:"120px"},on:{click:t.closeHandler}},[t._v("取消")]),e("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:t.submitHandler}},[t._v(" "+t._s("see"===t.type?"返回":"编辑")+" ")])],1)])],1)},s=[],i={props:{type:String,data:Object,disabled:Boolean},data:function(){return{isLoading:!1,formData:{score:"",tips:"",config:{diversity:{listText:[{text:"x种",tips:"（用户每餐食物多样摄入种类）"}],unitText:"x",content:[{comparison:"",comparison_score:"",operation:"",operation_score:""}]},cereals:{listText:[{text:"x1克/餐",tips:"（用户每餐谷物摄入量）"},{text:"y1克/餐",tips:"（查表法计算得出每餐谷物推荐摄入量）"}],unitText:"x1正负",content:[{comparison:"",comparison_score:"",operation:"",operation_score:""}]},eggsandmeat:{listText:[{text:"x2克/餐",tips:"（用户每餐鱼禽蛋肉摄入量）"},{text:"y2克/餐",tips:"（查表法计算得出用户每餐鱼禽蛋肉摄入量）"}],unitText:"x2正负",content:[{comparison:"",comparison_score:"",operation:"",operation_score:""}]},fruit:{listText:[{text:"x3克/餐",tips:"（用户每餐水果摄入量）"},{text:"y3克/餐",tips:"（查表计算得出用户每餐水果摄入量）"}],unitText:"x3正负",content:[{comparison:"",comparison_score:"",operation:"",operation_score:""},{comparison:"",comparison_score:"",operation:"",operation_score:""}]},vegetable:{listText:[{text:"x4克/餐",tips:"（用户每餐蔬菜摄入量）"},{text:"y4克/餐",tips:"（查表计算得出用户每餐蔬菜摄入量）"}],unitText:"x4正负",content:[{comparison:"",comparison_score:"",operation:"",operation_score:""},{comparison:"",comparison_score:"",operation:"",operation_score:""}]}}},formRuls:{},comparisonList:[{label:"等于",value:"=="},{label:"大于",value:">"},{label:"大于等于",value:">="},{label:"小于",value:"<"},{label:"小于等于",value:"<="}],operationList:[{label:"加",value:"+"},{label:"减",value:"-"}]}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){for(var t in this.formData.score=this.data.score,this.formData.tips=this.data.tips,this.formData.type=this.data.type,this.data.config)this.formData.config[t].content=this.data.config[t]},addRule:function(t,e){this.formData.config[e].content.push({comparison:"",comparison_score:"",operation:"",operation_score:""})},removeRule:function(t,e,a){this.formData.config[e].content.splice(a,1)},submitHandler:function(){var t=this;this.$refs.formRef.validate((function(e){if(e){if(t.isLoading)return t.$message.error("请勿重复提交！");var a={key:t.formData.type},o={};for(var s in t.formData.config)o[s]=t.formData.config[s].content;a[t.formData.type]={score:t.formData.score,tips:t.formData.tips,config:o},t.$emit("submitHandler",a)}}))},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,a,o){"confirm"===e?t.$closeCurrentTab(t.$route.path):a.confirmButtonLoading||o()}}).then((function(t){})).catch((function(t){}))}}},r=i,n=(a("9b74"),a("2877")),l=Object(n["a"])(r,o,s,!1,null,"cfeeba88",null);e["default"]=l.exports},ea36:function(t,e,a){"use strict";a("3a5c")},f259:function(t,e,a){},f337:function(t,e,a){}}]);