(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-user-health-records-constants"],{"2f56":function(e,t,a){"use strict";a.r(t),a.d(t,"recentSevenDay",(function(){return o})),a.d(t,"USERHEALTHRECORDS",(function(){return r})),a.d(t,"RADAROPTION",(function(){return n})),a.d(t,"MEALTIME_SETTING",(function(){return i})),a.d(t,"BODY_DETAIL",(function(){return c}));var l=a("5a0c"),o=[l().subtract(7,"day").format("YYYY-MM-DD"),l().format("YYYY-MM-DD")],r={select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"创建时间",value:[],clearable:!1},name:{type:"input",value:"",label:"姓名",placeholder:"请输入姓名"},phone:{type:"input",value:"",label:"手机号",placeholder:"请输入手机号"},company_ids:{type:"CompanySelect",value:[],label:"组织",dataList:[],multiple:!0,checkStrictly:!0,collapseTags:!0,clearable:!0,companyOpts:{label:"name",value:"company"},companyKey:"all"},status:{type:"select",value:"",label:"档案状态",clearable:!0,dataList:[{label:"全部",value:""},{label:"使用中",value:"enable"},{label:"注销中",value:"logoff"}]}},n={title:{text:0,x:"center",y:"center",textStyle:{color:"#fd953c",fontWeight:"bolder",fontSize:28}},tooltip:{trigger:"axis"},radar:{name:{textStyle:{padding:[-10,-5]},color:"#23282d"},splitLine:{lineStyle:{type:"dashed",width:1}},splitArea:{show:!1,areaStyle:{color:"rgba(255,0,0,0)"}},indicator:[{name:"食物多样性",max:100},{name:"营养均衡",max:100},{name:"能量摄入",max:100},{name:"BMI",max:100},{name:"运动情况",max:100}]},series:[{tooltip:{trigger:"item"},type:"radar",label:{show:!1},areaStyle:{color:"#fad1ae"},data:[{name:"健康分",value:[0,0,0,0,0]}]}],color:["#fca255"]},i={tooltip:{trigger:"item",borderColor:"#FCA155",textStyle:{color:"#000",fontWeight:500},backgroundColor:"#fff",extraCssText:"box-shadow: 0 0 10px rgba(0,0,0,0.2);font-weight: 540;",formatter:function(e){var t=e.marker,a=e.percent;return t+e.name+"&nbsp;&nbsp;&nbsp;"+a+"%"}},title:{text:"0",x:"center",y:"center",top:"25%",textStyle:{color:"#fd953c",fontSize:18}},legend:{top:"62%",orient:"vertical",y:"bottom",padding:[0,0,0,0]},series:[{type:"pie",radius:["45%","60%"],avoidLabelOverlap:!1,top:"-10%",height:200,itemStyle:{borderRadius:10,borderColor:"#f1f2f5",borderWidth:3},hoverAnimation:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!1,fontSize:"20",fontWeight:"bold"}},labelLine:{show:!1},data:[]}],color:["#07DED0","#FE985F","#e98397","#F97C95","#58AFFE","#F8C345"]},c={select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"创建时间",value:[],clearable:!1}}}}]);