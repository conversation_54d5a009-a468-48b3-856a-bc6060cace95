(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-components-mealFoodList-AddAndEditMealFoodDialog~view-merchant-meal-ma~a367f856","view-merchant-meal-management-components-mealFoodList-NutritionTable","view-merchant-meal-management-food-admin-constants"],{"0449":function(e,t,a){"use strict";a.r(t),a.d(t,"DEFAULT_NUTRITION",(function(){return i})),a.d(t,"ELEMENT_NUTRITION",(function(){return o})),a.d(t,"VITAMIN_NUTRITION",(function(){return r})),a.d(t,"NUTRITION_LIST",(function(){return n})),a.d(t,"LIBRARY_SEARCH_SETTING_MERCHANT",(function(){return s})),a.d(t,"LIBRARY_SEARCH_SETTING_CATEORY",(function(){return l})),a.d(t,"COMMODITY_SEARCH_SETTING_MERCHANT",(function(){return d})),a.d(t,"SPEC_LIST",(function(){return c}));var i=[{name:"千卡",key:"energy_kcal",unit:"Kcal",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{name:"胆固醇",key:"cholesterol",unit:"mg",type:"default"},{name:"膳食纤维",key:"dietary_fiber",unit:"g",type:"default"}],o=[{key:"Ca",name:"钙",unit:"mg",type:"element"},{key:"P",name:"磷",unit:"mg",type:"element"},{key:"K",name:"钾",unit:"mg",type:"element"},{key:"Na",name:"钠",unit:"mg",type:"element"},{name:"镁",key:"Mg",unit:"mg",type:"element"},{key:"Fe",name:"铁",unit:"mg",type:"element"},{key:"I",name:"碘",unit:"μg",type:"element"},{key:"Se",name:"硒",unit:"μg",type:"element"},{key:"Zn",name:"锌",unit:"mg",type:"element"},{key:"Cu",name:"铜",unit:"mg",type:"element"},{key:"F",name:"氟",unit:"mg",type:"element"},{key:"Cr",name:"铬",unit:"μg",type:"element"},{key:"Mo",name:"钼",unit:"μg",type:"element"},{key:"Mn",name:"锰",unit:"mg",type:"element"}],r=[{key:"VA",name:"维生素A",unit:"μg",type:"vitamin"},{key:"VD",name:"维生素D",unit:"μg",type:"vitamin"},{key:"VE",name:"维生素E",unit:"mg",type:"vitamin"},{key:"VK",name:"维生素K",unit:"μg",type:"vitamin"},{key:"VB1",name:"维生素B1",unit:"mg",type:"vitamin"},{key:"VB2",name:"维生素B2",unit:"mg",type:"vitamin"},{key:"VB6",name:"维生素B6",unit:"mg",type:"vitamin"},{key:"VB12",name:"维生素B12",unit:"μg",type:"vitamin"},{key:"VC",name:"维生素C",unit:"mg",type:"vitamin"},{key:"VB5",name:"泛酸",unit:"mg",type:"vitamin"},{key:"VM",name:"叶酸",unit:"μg",type:"vitamin"},{key:"VB3",name:"烟酸",unit:"mg",type:"vitamin"},{key:"Choline",name:" 胆碱",unit:"mg",type:"vitamin"},{key:"Nicotinamide",name:"烟酰胺",unit:"mg",type:"vitamin"},{key:"VH",name:"生物素",unit:"mg",type:"vitamin"}],n=[].concat(i,o,r),s={select_time:{type:"datetimerange",label:"创建时间",format:"yyyy-MM-dd HH:mm:ss",value:[]},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(e){return{id:e.level+"_"+e.id,label:e.name,children:e.sort_list}}},create_source:{type:"organizationSelect",label:"创建来源",multiple:!1,checkStrictly:!0,isLazy:!1,value:"",placeholder:"请选择创建来源",dataList:[{name:"全部",id:""},{company:0,create_time:"2022-01-25 09:49:26",has_children:!1,id:1,level:-1,level_name:"",level_tag:-1,name:"系统",parent:null,status:"enable",status_alias:"正常",tree_id:-1}]},is_enable_nutrition:{type:"select",label:"营养录入",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"是",value:"1"},{label:"否",value:"0"}]},ingredient_name:{type:"input",label:"食材名称",value:"",placeholder:"请输入食材名称"},label_filter:{type:"select",label:"",value:"",placeholder:"",dataList:[{label:"请选择",value:"select",disabled:!0},{label:"包含",value:"Include"},{label:"不包含",value:"Exclude"}]},label_list:{type:"treeselect",label:"",value:[],placeholder:"选择标签",multiple:!0,limit:1,level:1,valueConsistsOf:"LEAF_PRIORITY",normalizer:function(e){return{id:e.id,label:e.name,children:e.label_list}},dataList:[]},is_entering:{type:"select",label:"录入图片",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"已录入",value:"1"},{label:"未录入",value:"0"}]},is_entering22:{type:"select",label:"是否应季",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"是",value:"1"},{label:"否",value:"0"}]}},l={select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"修改时间",value:[]},category:{type:"select",label:"分类",value:[],placeholder:"请选择分类",multiple:!0,collapseTags:!0,dataList:[{label:"挂失",value:"LOSS"},{label:"退卡",value:"QUIT"},{label:"使用中",value:"ENABLE"},{label:"未发卡",value:"UNUSED"}]},person_no:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"}},d={select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"创建时间",value:[]},nutrition:{type:"select",label:"营养录入",value:[],placeholder:"请选择",multiple:!0,collapseTags:!0,dataList:[{label:"全部",value:""},{label:"是",value:"1"},{label:"否",value:"0"}]},perso_no:{type:"input",label:"菜品/商品名称",value:"",placeholder:"请输入菜品/商品名称"},hasx:{type:"select",label:"已有菜品/商品",value:"",placeholder:"请选择已有菜品/商品",dataList:[{label:"是",value:!0},{label:"否",value:!1}]},attributes:{type:"select",label:"属性",value:"",placeholder:"请选择属性",dataList:[{label:"是",value:!0},{label:"否",value:!1}]}},c=[{name:"份"},{name:"碗"},{name:"瓶"},{name:"碟"},{name:"盅"},{name:"盆"},{name:"件"},{name:"串"},{name:"例"},{name:"只"},{name:"边"}]},"0e41":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e._self._c;return t("div",[t("el-form",{ref:"formIngredients",attrs:{model:e.formData,rules:e.formRuls,size:"small"}},[e._l(e.currentNutritionList,(function(a){return[t("div",{key:a.key,staticClass:"nutrition-item"},[t("el-form-item",{attrs:{prop:a.key,label:a.name+"：",rules:e.formRuls.nutrition}},[t("el-input",{staticClass:"ps-input",staticStyle:{width:"120px"},attrs:{readonly:e.readonly,disabled:"",placeholder:"请输入"},on:{change:e.changeNutrition},model:{value:e.formData[a.key],callback:function(t){e.$set(e.formData,a.key,t)},expression:"formData[nutrition.key]"}}),t("span",{staticStyle:{"margin-left":"10px"}},[e._v(e._s(a.unit))])],1)],1)]})),t("div",{staticClass:"text-center pointer"},[t("span",{staticStyle:{color:"#027DB4"},on:{click:function(t){e.showAll=!e.showAll}}},[e._v(e._s(e.showAll?"收起":"查看更多营养信息"))])])],2)],1)},o=[],r=a("0449"),n={props:{tableDataNutrition:Object,readonly:{type:Boolean,default:!1}},data:function(){var e=function(e,t,a){if(t){var i=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;i.test(t)?a():a(new Error("营养数据有误，仅支持两位小数"))}else a()};return{formData:{},nutritionList:r["NUTRITION_LIST"],formRuls:{nutrition:[{validator:e,trigger:"change"}]},showAll:!1}},watch:{tableDataNutrition:{handler:function(e){this.initData()},deep:!0}},computed:{currentNutritionList:function(){var e=[];return e=this.showAll?this.nutritionList:this.nutritionList.slice(0,4),e}},mounted:function(){this.initData()},methods:{initData:function(e){var t=this;this.nutritionList.forEach((function(e){var a=t.tableDataNutrition[e.key]?t.tableDataNutrition[e.key]:0;t.$set(t.formData,e.key,a)}))},changeNutrition:function(e){this.$emit("update:tableDataNutrition",this.formData)}}},s=n,l=(a("f50f"),a("2877")),d=Object(l["a"])(s,i,o,!1,null,null,null);t["default"]=d.exports},"3b88":function(e,t,a){},"95d4":function(e,t,a){},dd14:function(e,t,a){"use strict";a("3b88")},f50f:function(e,t,a){"use strict";a("95d4")},f8da:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e._self._c;return t("div",[t("el-dialog",{attrs:{title:e.foodDialogTitle,visible:e.showDialog,width:"1000px",top:"20vh","custom-class":"ps-dialog","close-on-click-modal":!1},on:{"update:visible":function(t){e.showDialog=t}}},[t("span",{staticClass:"msg-tips title-tips"},[e._v("添加菜品后可在菜品库查看，缺失的食材会自动同步")]),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.formFoodLoading,expression:"formFoodLoading"}]},[t("el-form",{ref:"formFoodData",staticClass:"dialog-form add-and-edit-meal-food",staticStyle:{overflow:"auto",height:"450px"},attrs:{rules:e.formFoodDataRuls,model:e.formFoodData,"label-width":"80px",size:"small"}},["batchAdd"!=e.showDialogMealFoodType?t("div",{staticClass:"add-form-wrapper"},[t("el-form-item",{attrs:{label:"菜品名称：",prop:"foodName","label-width":"100px"}},[t("el-select",{ref:"foodNameRef",staticStyle:{width:"190px"},attrs:{clearable:"",filterable:"",remote:"",loading:e.remoteLoading,"remote-method":e.getSystemFoodList,placeholder:""},on:{"visible-change":e.visibleChangeFoodHandle,focus:e.focusFoodHandle,clear:e.clearFoodHandle,change:e.changeFoodHandle},model:{value:e.formFoodData.foodName,callback:function(t){e.$set(e.formFoodData,"foodName",t)},expression:"formFoodData.foodName"}},e._l(e.foodList,(function(a){return t("el-option",{key:a.id,attrs:{label:a.name,value:a.id,"text-light":!0}},[t("div",{domProps:{innerHTML:e._s(a.highlight_text)}})])})),1)],1),t("el-tooltip",{attrs:{effect:"dark",content:"增加菜品别名",placement:"top"}},[t("img",{staticClass:"add-btn-img",attrs:{src:a("a851"),alt:""},on:{click:e.addFoodAliasName}})]),t("el-form-item",{attrs:{label:"属性",prop:"attributes","label-width":"100px"}},[t("el-radio-group",{staticClass:"ps-radio",model:{value:e.formFoodData.attributes,callback:function(t){e.$set(e.formFoodData,"attributes",t)},expression:"formFoodData.attributes"}},[t("el-radio",{attrs:{label:"goods"}},[e._v("商品")]),t("el-radio",{attrs:{label:"foods"}},[e._v("菜品")])],1)],1)],1):e._e(),e.formFoodData.aliasName.length?t("div",[t("el-form-item",{attrs:{label:"菜品别名：","label-width":"100px"}},e._l(e.formFoodData.aliasName,(function(i,o){return t("el-form-item",{key:o,class:[o>0?"m-t-10":"","food-alias-name-form"],attrs:{rules:e.formFoodDataRuls.aliasName,prop:"aliasName[".concat(o,"]")}},[t("el-input",{staticClass:"ps-input",attrs:{maxlength:"20",placeholder:"请输入菜品别名"},model:{value:e.formFoodData.aliasName[o],callback:function(t){e.$set(e.formFoodData.aliasName,o,t)},expression:"formFoodData.aliasName[index]"}}),t("img",{attrs:{src:a("a851"),alt:""},on:{click:e.addFoodAliasName}}),t("img",{attrs:{src:a("1597"),alt:""},on:{click:function(t){return e.delFoodAliasName(o)}}})],1)})),1)],1):e._e(),t("div",{staticClass:"add-form-wrapper"},[t("el-form-item",{attrs:{label:"分类：",prop:"categoryId","label-width":"100px"}},[t("el-select",{staticClass:"ps-select",staticStyle:{width:"190px"},attrs:{placeholder:"请下拉选择分类","popper-class":"ps-popper-select","collapse-tags":"",clearable:"","popper-append-to-body":!1},model:{value:e.formFoodData.categoryId,callback:function(t){e.$set(e.formFoodData,"categoryId",t)},expression:"formFoodData.categoryId"}},e._l(e.foodCategoryList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),t("el-form-item",{attrs:{label:"是否主厨推荐","label-width":"135px"}},[t("el-radio-group",{staticClass:"ps-radio",model:{value:e.formFoodData.is_chef_recommended,callback:function(t){e.$set(e.formFoodData,"is_chef_recommended",t)},expression:"formFoodData.is_chef_recommended"}},[t("el-radio",{attrs:{label:!0}},[e._v("是")]),t("el-radio",{attrs:{label:!1}},[e._v("否")])],1)],1)],1),"goods"===e.formFoodData.attributes?t("el-form-item",{attrs:{label:"条形码：",prop:"barcode","label-width":"100px"}},[t("el-input",{staticClass:"ps-input",staticStyle:{width:"190px"},attrs:{placeholder:"请输入条形码",maxlength:"13"},model:{value:e.formFoodData.barcode,callback:function(t){e.$set(e.formFoodData,"barcode",t)},expression:"formFoodData.barcode"}})],1):e._e(),t("el-form-item",{attrs:{label:"标签",prop:""}},[t("el-button",{staticClass:"ps-origin-btn",attrs:{type:"primary",size:"small"},on:{click:e.labelClick}},[e._v(" 选择标签 ")])],1),e._l(e.formFoodData.labelGroupInfoList,(function(a,i,o){return t("el-form-item",{key:o,attrs:{label:"".concat(i,":"),prop:""}},e._l(a,(function(a,o){return t("el-tag",{key:o,staticClass:"m-r-5 collapse-data",attrs:{size:"medium",effect:"plain",type:"info",color:"#fff",closable:""},on:{close:function(t){return e.closeTag(i,o,a)}}},[e._v(" "+e._s(a.name)+" ")])})),1)})),t("el-form-item",{attrs:{label:"菜品简介","label-width":"100px"}},[t("el-input",{staticClass:"ps-input w-300",attrs:{type:"textarea",autosize:{minRows:4,maxRows:6},maxlength:"100",placeholder:"请输入简介"},model:{value:e.formFoodData.description,callback:function(t){e.$set(e.formFoodData,"description",t)},expression:"formFoodData.description"}})],1),t("div",{staticClass:"add-form-wrapper"}),"batchAdd"!=e.showDialogMealFoodType?t("el-form-item",{attrs:{label:"展示图片：","label-width":"100px"}},[t("div",{staticClass:"msg-tips"},[e._v("用于设备或手机端展示使用，最多上传1张")]),t("el-upload",{ref:"uploadFoodImage",class:{"upload-food":!0,"hide-upload":e.formFoodData.foodImagesList.length>0},attrs:{drag:"",data:e.uploadParams,action:e.actionUrl,multiple:!1,"file-list":e.formFoodData.foodImagesList,"list-type":"picture-card","on-change":e.handelChange,"on-success":e.handleFoodImgSuccess,"before-upload":e.beforeFoodImgUpload,limit:1,headers:e.headersOpts},scopedSlots:e._u([{key:"file",fn:function(a){var i=a.file;return t("div",{directives:[{name:"loading",rawName:"v-loading",value:"uploading"===i.status,expression:"file.status==='uploading'"}],attrs:{"element-loading-text":"上传中"}},[t("div",{staticClass:"upload-food-img"},[t("img",{attrs:{src:i.url,alt:""}})]),t("span",{staticClass:"el-upload-list__item-actions"},[t("span",{staticClass:"el-upload-list__item-preview",on:{click:function(t){return e.handlePictureCardPreview(i)}}},[t("i",{staticClass:"el-icon-zoom-in"})]),t("span",{staticClass:"el-upload-list__item-delete",on:{click:function(t){return e.handleFoodImgRemove(i,"foodImages")}}},[t("i",{staticClass:"el-icon-delete"})])])])}}],null,!1,1992532046)},[e.formFoodData.foodImagesList.length<1?t("i",{staticClass:"el-icon-plus"}):e._e()])],1):e._e(),t("el-form-item",{attrs:{label:"识别图片：","label-width":"100px"}},[t("div",{staticClass:"msg-tips"},[e._v("用于提取菜品特征使用，最多上传25张")]),t("el-upload",{ref:"uploadExtraImage",staticClass:"upload-food",attrs:{drag:"",data:e.uploadParams,action:e.actionUrl,multiple:!0,"file-list":e.formFoodData.extraImagesList,"list-type":"picture-card","on-change":e.handelChange,"on-success":e.handleExtraImgSuccess,"before-upload":e.beforeFoodImgUpload,limit:25,headers:e.headersOpts},scopedSlots:e._u([{key:"file",fn:function(a){var i=a.file;return t("div",{directives:[{name:"loading",rawName:"v-loading",value:"uploading"===i.status,expression:"file.status==='uploading'"}],attrs:{"element-loading-text":"上传中"}},[t("div",{staticClass:"upload-food-img"},[t("img",{attrs:{src:i.url,alt:""}})]),t("span",{staticClass:"el-upload-list__item-actions"},[t("span",{staticClass:"el-upload-list__item-preview",on:{click:function(t){return e.handlePictureCardPreview(i)}}},[t("i",{staticClass:"el-icon-zoom-in"})]),t("span",{staticClass:"el-upload-list__item-delete",on:{click:function(t){return e.handleFoodImgRemove(i,"extraImages")}}},[t("i",{staticClass:"el-icon-delete"})])])])}}])},[e.formFoodData.extraImages.length<25?t("i",{staticClass:"el-icon-plus"}):e._e()])],1),t("el-form-item",{attrs:{label:"价格信息：",prop:"","label-width":"100px"}},[t("el-radio-group",{attrs:{"text-color":"#ff8f44"},on:{change:e.changeCount},model:{value:e.formFoodData.countType,callback:function(t){e.$set(e.formFoodData,"countType",t)},expression:"formFoodData.countType"}},[t("el-radio",{staticClass:"ps-radio",attrs:{label:1}},[e._v("菜品 / 商品价格")]),t("el-radio",{staticClass:"ps-radio",attrs:{label:2}},[e._v("称重价格")]),t("el-radio",{staticClass:"ps-radio",attrs:{label:3}},[e._v("菜品 / 商品价格 + 称重价格")])],1),1==e.formFoodData.countType?t("div",[t("ul",{staticClass:"form-ul"},[t("div",{staticClass:"clearfix form-li-box"},[t("li",{staticClass:"form-li float-l"},[e._v(" 规格 ")]),t("li",{staticClass:"form-li float-l li-center"},[e._v(" 价格（￥） ")]),t("li",{staticClass:"form-li float-l"},[e._v(" 重量 ")])]),e._l(e.formFoodData.foodPriceJson,(function(a,i){return t("div",{key:i,staticClass:"clearfix form-li-box"},[t("li",{staticClass:"form-li float-l"},[t("el-form-item",{staticClass:"no-label",attrs:{label:"","show-message":!1,rules:e.formFoodDataRuls.foodSpec,prop:"foodPriceJson["+i+"].spec"}},[t("el-input",{attrs:{disabled:!i},model:{value:a.spec,callback:function(t){e.$set(a,"spec",t)},expression:"item.spec"}})],1)],1),t("li",{staticClass:"form-li float-l li-center"},[t("el-form-item",{staticClass:"no-label",attrs:{label:"","show-message":!1,rules:e.formFoodDataRuls.foodPriceJson,prop:"foodPriceJson["+i+"].price"}},[t("el-input",{model:{value:a.price,callback:function(t){e.$set(a,"price",t)},expression:"item.price"}})],1)],1),t("li",{staticClass:"form-li float-l"},[t("el-form-item",{staticClass:"no-label",attrs:{label:"","show-message":!1,rules:e.formFoodDataRuls.foodPriceJson,prop:"foodPriceJson["+i+"].weight"}},[t("el-input",{model:{value:a.weight,callback:function(t){e.$set(a,"weight",t)},expression:"item.weight"}})],1)],1),t("span",{staticClass:"fix-icon"},[i?t("i",{staticClass:"li-icon el-icon-remove",on:{click:function(t){return e.deleteFoodPriceJson(i)}}}):e._e(),i===e.formFoodData.foodPriceJson.length-1?t("i",{staticClass:"li-icon el-icon-circle-plus",on:{click:e.addFoodPriceJson}}):e._e()])])}))],2)]):2==e.formFoodData.countType||3==e.formFoodData.countType?t("div",[t("el-radio-group",{staticClass:"m-t-20 m-b-20",attrs:{"text-color":"#ff8f44"},on:{change:e.changeWeightType},model:{value:e.formFoodData.weightType,callback:function(t){e.$set(e.formFoodData,"weightType",t)},expression:"formFoodData.weightType"}},[t("el-radio",{staticClass:"ps-radio",attrs:{label:1}},[e._v("按克")]),t("el-radio",{staticClass:"ps-radio",attrs:{label:2}},[e._v("按份")])],1),1==e.formFoodData.weightType?t("div",[t("el-form-item",{staticClass:"align-left",attrs:{label:"称重价格",prop:"weightPrice","label-width":"80px"}},[t("el-input",{staticClass:"ps-input",staticStyle:{width:"130px","padding-bottom":"10px"},model:{value:e.formFoodData.weightPrice,callback:function(t){e.$set(e.formFoodData,"weightPrice",t)},expression:"formFoodData.weightPrice"}}),t("span",[e._v("  元/ ")]),t("el-input",{staticClass:"ps-input input",model:{value:e.formFoodData.weight,callback:function(t){e.$set(e.formFoodData,"weight",t)},expression:"formFoodData.weight"}}),t("span",[e._v("  克")])],1),3==e.formFoodData.countType?t("el-form-item",{attrs:{label:"菜品 / 商品价格",prop:"foodPrice","label-width":"90px"}},[t("el-input",{staticClass:"ps-input input",model:{value:e.formFoodData.foodPrice,callback:function(t){e.$set(e.formFoodData,"foodPrice",t)},expression:"formFoodData.foodPrice"}}),t("span",[e._v("  元")])],1):e._e()],1):e._e(),2==e.formFoodData.weightType?t("div",{staticClass:"add-form-item-wrapper"},[t("div",{staticClass:"add-form-wrapper"},[t("el-form-item",{staticClass:"align-left",attrs:{label:"称重价格",prop:"weightPrice","label-width":"80px"}},[t("el-input",{staticClass:"ps-input input",model:{value:e.formFoodData.weightPrice,callback:function(t){e.$set(e.formFoodData,"weightPrice",t)},expression:"formFoodData.weightPrice"}}),t("span",[e._v("  元/份")])],1),t("el-form-item",{staticClass:"add-form-item-wrapper",attrs:{label:"单份重量",prop:"weight","label-width":"110px"}},[t("el-input",{staticClass:"ps-input input",model:{value:e.formFoodData.weight,callback:function(t){e.$set(e.formFoodData,"weight",t)},expression:"formFoodData.weight"}}),t("span",[e._v("  克")])],1)],1),t("div",{staticClass:"add-form-wrapper"},[t("el-form-item",{staticClass:"align-left",attrs:{label:"起始计价重量",prop:"startGram","label-width":"110px"}},[t("el-input",{staticClass:"ps-input input",model:{value:e.formFoodData.startGram,callback:function(t){e.$set(e.formFoodData,"startGram",t)},expression:"formFoodData.startGram"}}),t("span",[e._v("  克")])],1),t("el-form-item",{staticClass:"add-form-item-wrapper",attrs:{label:"误差率",prop:"faultRate","label-width":"100px"}},[t("el-input",{staticClass:"ps-input input",model:{value:e.formFoodData.faultRate,callback:function(t){e.$set(e.formFoodData,"faultRate",t)},expression:"formFoodData.faultRate"}}),t("div",{staticClass:"text-tips"},[e._v("误差率指每份菜品的重量在±误差率内都算1份")])],1)],1),t("div",[3==e.formFoodData.countType?t("el-form-item",{staticClass:"align-left",attrs:{label:"菜品 / 商品价格",prop:"foodPrice","label-width":"120px"}},[t("el-input",{staticClass:"ps-input input",model:{value:e.formFoodData.foodPrice,callback:function(t){e.$set(e.formFoodData,"foodPrice",t)},expression:"formFoodData.foodPrice"}}),t("span",[e._v("  元")])],1):e._e()],1)]):e._e()],1):e._e(),t("el-form-item",{staticClass:"align-left",attrs:{label:"成本价格",prop:"originPrice","label-width":"80px"}},[t("el-input",{staticClass:"ps-input input",attrs:{placeholder:"请输入成本价格"},model:{value:e.formFoodData.originPrice,callback:function(t){e.$set(e.formFoodData,"originPrice",t)},expression:"formFoodData.originPrice"}}),t("span",[e._v(" 元")])],1),t("el-form-item",{staticClass:"align-left",attrs:{label:"打包费",prop:"packPrice","label-width":"60px"}},[t("el-input",{staticClass:"ps-input input",attrs:{placeholder:"请输入打包费"},model:{value:e.formFoodData.packPrice,callback:function(t){e.$set(e.formFoodData,"packPrice",t)},expression:"formFoodData.packPrice"}}),t("span",[e._v("  元")])],1)],1),"batchAdd"!=e.showDialogMealFoodType?t("div",[t("div",[e._v("食材占比组成："),t("span",{staticClass:"origin-text"},[e._v("相同的食材会自动覆盖，缺失的食材会自动同步。如无填写食材信息，系统将自动关联食材")])]),t("el-form-item",{attrs:{"label-width":"100px",label:""}},[t("div",{staticClass:"food-proportion-box"},[t("div",{staticClass:"w-150 food wh t-a-c white"},[e._v("食材")]),t("div",{staticClass:"cantent proportion white"},[e._v("占比"),t("span",[e._v("（菜品每100g所含食材占比，相加必须等于100%）")])]),t("div",{staticClass:"w-150 tools t-a-c white"},[e._v("操作")])]),t("div",{class:["food-proportion-wrapper",e.errorMsg.percentageError?"error-border":""]},e._l(e.formFoodData.ingredientList,(function(a,i){return t("div",{key:i,staticClass:"food-proportion-box"},[t("div",{staticClass:"w-150"},[t("virtual-select",{staticClass:"ps-select margin-right",attrs:{width:150,"popover-width":200,placeholder:"请下拉选择",filterable:"","data-list":a.selectFoodIngredient,option:{label:"name",value:"id"}},on:{change:e.changeIngredient},model:{value:a.ingredient_id,callback:function(t){e.$set(a,"ingredient_id",t)},expression:"item.ingredient_id"}})],1),t("div",{staticClass:"cantent ps-flex-align-c"},[t("div",{staticStyle:{width:"100%","margin-left":"10px"}},[t("el-slider",{staticClass:"cantent",attrs:{"show-input":""},on:{change:e.changePercentage},model:{value:a.percentage,callback:function(t){e.$set(a,"percentage",t)},expression:"item.percentage"}})],1),e._v(" % ")]),t("div",{staticClass:"w-150 t-a-c"},[1!=e.formFoodData.ingredientList.length?t("el-button",{staticClass:"ps-warn",attrs:{type:"text",size:"small"},on:{click:function(t){return t.preventDefault(),e.deleteFunList("food",i,a)}}},[e._v(" 删除 ")]):e._e(),i==e.formFoodData.ingredientList.length-1?t("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(t){return t.preventDefault(),e.addFunList("food")}}},[e._v(" 新增 ")]):e._e()],1)])})),0),e.errorMsg.percentageError?t("div",{staticStyle:{color:"red"}},[e._v(e._s(e.errorMsg.percentageError))]):e._e()]),t("div",[e._v("营养信息：")]),t("el-form-item",{attrs:{"label-width":"100px",label:""}},[t("nutrition-data",{ref:"nutritionRef",attrs:{tableDataNutrition:e.tableDataNutrition,readonly:""},on:{"update:tableDataNutrition":function(t){e.tableDataNutrition=t},"update:table-data-nutrition":function(t){e.tableDataNutrition=t}}})],1)],1):e._e()],2)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-cancel-btn",on:{click:e.cancel}},[e._v("取 消")]),t("el-button",{staticClass:"ps-btn",attrs:{type:"primary",loading:e.formFoodLoading},on:{click:e.determineDialogFood}},[e._v(" 保 存 ")])],1)]),t("el-dialog",{attrs:{visible:e.dialogVisible},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("img",{attrs:{width:"100%",src:e.dialogImageUrl,alt:""}})]),e.selectLaberDialogVisible?t("select-laber",{ref:"selectLaber",attrs:{isshow:e.selectLaberDialogVisible,width:"600px",ruleSingleInfo:e.ruleSingleInfo},on:{"update:isshow":function(t){e.selectLaberDialogVisible=t},selectLaberData:e.selectLaberData}},[t("div",{attrs:{slot:"append"},slot:"append"},[t("div",{staticClass:"tab"},[t("div",{class:["tab-item",e.ruleSingleInfo.isAdmin?"active":""],on:{click:e.tabClick}},[e._v(" 平台标签 ")]),t("div",{class:["tab-item",!1===e.ruleSingleInfo.isAdmin?"active":""],on:{click:e.tabClick}},[e._v(" 自有标签 ")])])])]):e._e()],1)},o=[],r=a("ed08"),n=a("da92"),s=a("0e41"),l=a("d0dd"),d=a("0449"),c=a("f6f8"),u=a("8003");function f(e){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},f(e)}function m(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,i)}return a}function p(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?m(Object(a),!0).forEach((function(t){g(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):m(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function g(e,t,a){return(t=h(t))in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function h(e){var t=b(e,"string");return"symbol"==f(t)?t:t+""}function b(e,t){if("object"!=f(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var i=a.call(e,t||"default");if("object"!=f(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function y(e,t){return w(e)||_(e,t)||F(e,t)||v()}function v(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function F(e,t){if(e){if("string"==typeof e)return D(e,t);var a={}.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?D(e,t):void 0}}function D(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,i=Array(t);a<t;a++)i[a]=e[a];return i}function _(e,t){var a=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=a){var i,o,r,n,s=[],l=!0,d=!1;try{if(r=(a=a.call(e)).next,0===t){if(Object(a)!==a)return;l=!1}else for(;!(l=(i=r.call(a)).done)&&(s.push(i.value),s.length!==t);l=!0);}catch(e){d=!0,o=e}finally{try{if(!l&&null!=a.return&&(n=a.return(),Object(n)!==n))return}finally{if(d)throw o}}return s}}function w(e){if(Array.isArray(e))return e}function L(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */L=function(){return t};var e,t={},a=Object.prototype,i=a.hasOwnProperty,o=Object.defineProperty||function(e,t,a){e[t]=a.value},r="function"==typeof Symbol?Symbol:{},n=r.iterator||"@@iterator",s=r.asyncIterator||"@@asyncIterator",l=r.toStringTag||"@@toStringTag";function d(e,t,a){return Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(e){d=function(e,t,a){return e[t]=a}}function c(e,t,a,i){var r=t&&t.prototype instanceof y?t:y,n=Object.create(r.prototype),s=new $(i||[]);return o(n,"_invoke",{value:N(e,a,s)}),n}function u(e,t,a){try{return{type:"normal",arg:e.call(t,a)}}catch(e){return{type:"throw",arg:e}}}t.wrap=c;var m="suspendedStart",p="suspendedYield",g="executing",h="completed",b={};function y(){}function v(){}function F(){}var D={};d(D,n,(function(){return this}));var _=Object.getPrototypeOf,w=_&&_(_(S([])));w&&w!==a&&i.call(w,n)&&(D=w);var I=F.prototype=y.prototype=Object.create(D);function k(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,t){function a(o,r,n,s){var l=u(e[o],e,r);if("throw"!==l.type){var d=l.arg,c=d.value;return c&&"object"==f(c)&&i.call(c,"__await")?t.resolve(c.__await).then((function(e){a("next",e,n,s)}),(function(e){a("throw",e,n,s)})):t.resolve(c).then((function(e){d.value=e,n(d)}),(function(e){return a("throw",e,n,s)}))}s(l.arg)}var r;o(this,"_invoke",{value:function(e,i){function o(){return new t((function(t,o){a(e,i,t,o)}))}return r=r?r.then(o,o):o()}})}function N(t,a,i){var o=m;return function(r,n){if(o===g)throw Error("Generator is already running");if(o===h){if("throw"===r)throw n;return{value:e,done:!0}}for(i.method=r,i.arg=n;;){var s=i.delegate;if(s){var l=P(s,i);if(l){if(l===b)continue;return l}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if(o===m)throw o=h,i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);o=g;var d=u(t,a,i);if("normal"===d.type){if(o=i.done?h:p,d.arg===b)continue;return{value:d.arg,done:i.done}}"throw"===d.type&&(o=h,i.method="throw",i.arg=d.arg)}}}function P(t,a){var i=a.method,o=t.iterator[i];if(o===e)return a.delegate=null,"throw"===i&&t.iterator.return&&(a.method="return",a.arg=e,P(t,a),"throw"===a.method)||"return"!==i&&(a.method="throw",a.arg=new TypeError("The iterator does not provide a '"+i+"' method")),b;var r=u(o,t.iterator,a.arg);if("throw"===r.type)return a.method="throw",a.arg=r.arg,a.delegate=null,b;var n=r.arg;return n?n.done?(a[t.resultName]=n.value,a.next=t.nextLoc,"return"!==a.method&&(a.method="next",a.arg=e),a.delegate=null,b):n:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,b)}function C(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function O(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function $(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(C,this),this.reset(!0)}function S(t){if(t||""===t){var a=t[n];if(a)return a.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,r=function a(){for(;++o<t.length;)if(i.call(t,o))return a.value=t[o],a.done=!1,a;return a.value=e,a.done=!0,a};return r.next=r}}throw new TypeError(f(t)+" is not iterable")}return v.prototype=F,o(I,"constructor",{value:F,configurable:!0}),o(F,"constructor",{value:v,configurable:!0}),v.displayName=d(F,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===v||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,F):(e.__proto__=F,d(e,l,"GeneratorFunction")),e.prototype=Object.create(I),e},t.awrap=function(e){return{__await:e}},k(x.prototype),d(x.prototype,s,(function(){return this})),t.AsyncIterator=x,t.async=function(e,a,i,o,r){void 0===r&&(r=Promise);var n=new x(c(e,a,i,o),r);return t.isGeneratorFunction(a)?n:n.next().then((function(e){return e.done?e.value:n.next()}))},k(I),d(I,l,"Generator"),d(I,n,(function(){return this})),d(I,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),a=[];for(var i in t)a.push(i);return a.reverse(),function e(){for(;a.length;){var i=a.pop();if(i in t)return e.value=i,e.done=!1,e}return e.done=!0,e}},t.values=S,$.prototype={constructor:$,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(O),!t)for(var a in this)"t"===a.charAt(0)&&i.call(this,a)&&!isNaN(+a.slice(1))&&(this[a]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var a=this;function o(i,o){return s.type="throw",s.arg=t,a.next=i,o&&(a.method="next",a.arg=e),!!o}for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r],s=n.completion;if("root"===n.tryLoc)return o("end");if(n.tryLoc<=this.prev){var l=i.call(n,"catchLoc"),d=i.call(n,"finallyLoc");if(l&&d){if(this.prev<n.catchLoc)return o(n.catchLoc,!0);if(this.prev<n.finallyLoc)return o(n.finallyLoc)}else if(l){if(this.prev<n.catchLoc)return o(n.catchLoc,!0)}else{if(!d)throw Error("try statement without catch or finally");if(this.prev<n.finallyLoc)return o(n.finallyLoc)}}}},abrupt:function(e,t){for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a];if(o.tryLoc<=this.prev&&i.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var r=o;break}}r&&("break"===e||"continue"===e)&&r.tryLoc<=t&&t<=r.finallyLoc&&(r=null);var n=r?r.completion:{};return n.type=e,n.arg=t,r?(this.method="next",this.next=r.finallyLoc,b):this.complete(n)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),b},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.finallyLoc===e)return this.complete(a.completion,a.afterLoc),O(a),b}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.tryLoc===e){var i=a.completion;if("throw"===i.type){var o=i.arg;O(a)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,a,i){return this.delegate={iterator:S(t),resultName:a,nextLoc:i},"next"===this.method&&(this.arg=e),b}},t}function I(e,t,a,i,o,r,n){try{var s=e[r](n),l=s.value}catch(e){return void a(e)}s.done?t(l):Promise.resolve(l).then(i,o)}function k(e){return function(){var t=this,a=arguments;return new Promise((function(i,o){var r=e.apply(t,a);function n(e){I(r,i,o,n,s,"next",e)}function s(e){I(r,i,o,n,s,"throw",e)}n(void 0)}))}}var x={props:{showDialogMealFood:Boolean,showDialogMealFoodType:String,foodDialogTitle:String,formFoodDataDialog:{type:Object,default:function(){return{}}},confirm:Function,foodCategoryList:Array,selectListId:Array},components:{nutritionData:s["default"],selectLaber:c["default"],VirtualSelect:u["a"]},data:function(){var e=this,t=function(e,t,a){if(!t)return a(new Error("格式有误"));var i=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;i.test(t)?a():a(new Error("格式有误"))},a=function(e,t,a){if(!t)return a(new Error("格式有误"));var i=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;i.test(t)?a():a(new Error("格式有误"))},i=function(t,a,i){if(!e.formFoodData.foodImages.length)return i(new Error("请上传菜品图片"));i()};return{formFoodData:{foodName:"",aliasName:[],categoryId:"",specList:"",attributes:"foods",barcode:"",isSupportReservation:!1,saleStatus:0,weightType:1,foodImagesList:[],foodImages:[],extraImages:[],extraImagesList:[],countType:1,foodPriceJson:[{spec:"默认",price:"",weight:""}],foodPrice:"",weightPrice:"",weight:"",startGram:"",faultRate:"",originPrice:"",packPrice:"",ingredientList:[{id:"",ingredient_id:"",name:"",nutrition:{},selectFoodIngredient:[],percentage:0}],selectLabelListData:[],selectLabelIdList:[],labelGroupInfoList:{},is_chef_recommended:!1,description:""},formFoodDataRuls:{foodName:[{required:!0,message:"请输入菜品名称",trigger:"blur"}],aliasName:[{required:!0,message:"请输入菜品别名",trigger:"blur"}],categoryId:[{required:!0,message:"请选择分类",trigger:"change"}],attributes:[{required:!0,message:"请选择属性",trigger:"blur"}],foodPrice:[{required:!0,validator:l["a"],message:"请输入菜品菜品 / 商品价格",trigger:"blur"}],weightPrice:[{required:!0,validator:l["a"],message:"请输入称重价格",trigger:"blur"}],weight:[{required:!0,message:"请输入称重重量",trigger:"blur"}],startGram:[{required:!0,message:"请输入起始计价重量",trigger:"blur"}],faultRate:[{required:!0,message:"请输入误差率",trigger:"blur"}],packPrice:[{required:!1,validator:l["b"],message:"请输入打包费",trigger:"blur"}],foodSpec:[{required:!0,message:"请输入",trigger:"blur"}],foodPriceJson:[{required:!0,validator:t,trigger:"blur"}],foodWeight:[{validator:a,trigger:"blur"}],foodImages:[{required:!0,validator:i,trigger:"blur"}]},formFoodLoading:!1,foodIngredientList:[],allSelectIngredient:[],tableDataNutrition:{},actionUrl:"/api/background/file/upload",headersOpts:{TOKEN:Object(r["B"])()},uploadParams:{prefix:"foodImage"},rulsNumder:/^\d+$/,deepFormIngredients:[],specLists:d["SPEC_LIST"],errorMsg:{percentageError:"",nutritionError:""},dialogImageUrl:"",dialogVisible:!1,selectLaberDialogVisible:!1,ruleSingleInfo:{isAdmin:!0,labelType:"food"},foodList:[],filterFoodName:"",isSelectFood:!1,remoteLoading:!1,selectFood:null,systemIngredientsList:[],systemIngredientsIdsObj:{},allIngredientsList:{}}},computed:{showDialog:{get:function(){return this.showDialogMealFood},set:function(e){this.$emit("update:showDialogMealFood",e)}}},created:function(){var e=this;return k(L().mark((function t(){return L().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.initLoad();case 2:e.deepFormIngredients=e.formFoodDataDialog.ingredients;case 3:case"end":return t.stop()}}),t)})))()},mounted:function(){this.$nextTick((function(e){}))},methods:{initLoad:function(){var e=this;return k(L().mark((function t(){var a,i;return L().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a=Object(r["x"])("systemIngredientsList"),i=Object(r["x"])("foodIngredientList"),a&&i){t.next=8;break}return t.next=5,e.getSystemIngredientList();case 5:e.getFoodIngredientList(),t.next=12;break;case 8:a=JSON.parse(a),i=JSON.parse(i),e.setSystemIngrendients(a),e.setMerchantIngrendients(i);case 12:case"end":return t.stop()}}),t)})))()},initFormFoodDataDialog:function(e,t){var a=this,i=Object(r["f"])(t);this.filterFoodName=i.name,this.formFoodData={foodName:i.name,aliasName:i.alias_name?i.alias_name:[],categoryId:i.category,attributes:i.attributes,barcode:i.barcode,specList:i.spec_list&&i.spec_list.length?i.spec_list[0].name:"",countType:1,weightType:1,foodPrice:"",weightPrice:"",weight:"",startGram:"",faultRate:"",originPrice:"",packPrice:"",foodImages:[],foodImagesList:[],extraImages:[],extraImagesList:[],nutrition_info:i.nutrition_info,selectLabelListData:[],selectLabelIdList:[],labelGroupInfoList:{},ingredientList:[{weight:"",id:"",ingredient_id:"",name:"",nutrition:{},selectFoodIngredient:Object(r["f"])(this.foodIngredientList),percentage:0}],foodPriceJson:[{spec:"默认",price:"",weight:""}],is_chef_recommended:i.is_chef_recommended,description:i.description};var o={weight_type:"weightType",food_price:"foodPrice",weight_price:"weightPrice",weight:"weight",start_gram:"startGram",fault_rate:"faultRate",origin_price:"originPrice",pack_price:"packPrice"};i.price_info?(this.setFormData(i.price_info,o,this.formFoodData),this.$set(this.formFoodData,"countType",i.price_info.count_type?i.price_info.count_type:1)):(this.setFormData(i.price_info,o,this.formFoodData),this.$set(this.formFoodData,"countType",1));var n=[];i.spec_list&&i.spec_list.length&&i.spec_list.forEach((function(e){n.push({spec:e.name,price:Object(r["i"])(e.food_price),weight:e.weight})})),n.length>0&&(this.formFoodData.foodPriceJson=n),i.image&&(this.formFoodData.foodImagesList.push({url:i.image,name:i.image,status:"success",uid:i.image}),this.formFoodData.foodImages=[i.image]),i.extra_image&&(i.extra_image.forEach((function(e){a.formFoodData.extraImagesList.push({url:e,name:e,status:"success",uid:e})})),this.formFoodData.extraImages=i.extra_image),i.ingredients_list&&i.ingredients_list.length&&(this.formFoodData.ingredientList=i.ingredients_list.map((function(e){var t={id:e.ingredient,ingredient_id:e.ingredient,name:e.ingredient_name,nutrition:a.foodIngredientNutrition(e),selectFoodIngredient:Object(r["f"])(a.foodIngredientList),percentage:e.ingredient_scale,ingredient_type:a.systemIngredientsIdsObj[e.ingredient]?"super":""};return t})),this.isDisabledOtherMeal()),this.tableDataNutrition={},i.nutrition_info||(i.nutrition_info={});var s=i.nutrition_info.element?JSON.parse(Object(r["R"])(i.nutrition_info.element)):{},l=i.nutrition_info.vitamin?JSON.parse(Object(r["R"])(i.nutrition_info.vitamin)):{};d["NUTRITION_LIST"].forEach((function(e){"default"===e.type&&a.$set(a.tableDataNutrition,e.key,i.nutrition_info[e.key]?i.nutrition_info[e.key]:0),"element"===e.type&&a.$set(a.tableDataNutrition,e.key,s[e.key]?s[e.key]:0),"vitamin"===e.type&&a.$set(a.tableDataNutrition,e.key,l[e.key]?l[e.key]:0)})),i.label&&i.label.length&&(this.initLabelGroup(i.label),this.formFoodData.selectLabelListData=i.label,this.formFoodData.selectLabelIdList=i.label.map((function(e){return e.id})))},setFormData:function(e,t,a){var i=this;e&&Object.keys(t).forEach((function(o){e[o]?i.$set(a,t[o],e[o]):i.$set(a,t[o],"")}))},foodIngredientNutrition:function(e){var t={};return this.allIngredientsList[e.ingredient]&&(t=this.allIngredientsList[e.ingredient].nutrition),t},cancel:function(){this.showDialog=!1},deleteFunList:function(e,t,a){"taste"===e?this.formFoodData.tasteList.splice(t,1):"food"===e?(this.formFoodData.ingredientList.splice(t,1),this.isDisabledOtherMeal(),this.computedNutritionAndPercentage(),this.changePercentage()):"spec"===e&&("edit"===this.showDialogMealFoodType&&a.id?this.setFoodDeleteSpec(a):this.formFoodData.specList.splice(t,1))},addFunList:function(e,t){"taste"===e?this.formFoodData.tasteList.push({name:""}):"food"===e?(this.formFoodData.ingredientList.push({weight:"",id:"",ingredient_id:"",name:"",nutrition:{},selectFoodIngredient:Object(r["f"])(this.foodIngredientList),percentage:0}),this.isDisabledOtherMeal()):"spec"===e&&this.formFoodData.specList.push({name:""})},setFoodAddTaste:function(e){var t=this;return k(L().mark((function a(){var i,o,n,s;return L().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,Object(r["Z"])(t.$apis.apiBackgroundFoodFoodAddTastePost({food_id:t.formFoodDataDialog.id,name:e.name}));case 2:if(i=a.sent,o=y(i,2),n=o[0],s=o[1],!n){a.next=9;break}return t.$message.error(n.message),a.abrupt("return");case 9:0===s.code?(t.showDialog=!1,t.$message.success(s.msg),t.$emit("confirm","search")):t.$message.error(s.msg);case 10:case"end":return a.stop()}}),a)})))()},setFoodDeleteTaste:function(e){var t=this;return k(L().mark((function a(){var i,o,n,s;return L().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,Object(r["Z"])(t.$apis.apiBackgroundFoodFoodDeleteTastePost({ids:[e.id]}));case 2:if(i=a.sent,o=y(i,2),n=o[0],s=o[1],!n){a.next=9;break}return t.$message.error(n.message),a.abrupt("return");case 9:0===s.code?(t.showDialog=!1,t.$message.success(s.msg),t.$emit("confirm","search")):t.$message.error(s.msg);case 10:case"end":return a.stop()}}),a)})))()},setFoodAddSpec:function(e){var t=this;return k(L().mark((function a(){var i,o,n,s;return L().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,Object(r["Z"])(t.$apis.apiBackgroundFoodFoodAddSpecPost({food_id:t.formFoodDataDialog.id,name:e.name}));case 2:if(i=a.sent,o=y(i,2),n=o[0],s=o[1],!n){a.next=9;break}return t.$message.error(n.message),a.abrupt("return");case 9:0===s.code?(t.showDialog=!1,t.$message.success(s.msg),t.$emit("confirm","search")):t.$message.error(s.msg);case 10:case"end":return a.stop()}}),a)})))()},setFoodDeleteSpec:function(e){var t=this;return k(L().mark((function a(){var i,o,n,s;return L().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,Object(r["Z"])(t.$apis.apiBackgroundFoodFoodDeleteSpecPost({ids:[e.id]}));case 2:if(i=a.sent,o=y(i,2),n=o[0],s=o[1],!n){a.next=9;break}return t.$message.error(n.message),a.abrupt("return");case 9:0===s.code?(t.showDialog=!1,t.$message.success(s.msg),t.$emit("confirm","search")):t.$message.error(s.msg);case 10:case"end":return a.stop()}}),a)})))()},labelClick:function(){this.ruleSingleInfo={isAdmin:!0,labelType:"food",selectLabelIdList:this.formFoodData.selectLabelIdList,selectLabelListData:this.formFoodData.selectLabelListData},this.selectLaberDialogVisible=!0},closeTag:function(e,t,a){var i=this.formFoodData.selectLabelIdList.indexOf(a.id),o=this.formFoodData.selectLabelListData.indexOf(a);this.formFoodData.selectLabelIdList.splice(i,1),this.formFoodData.selectLabelListData.splice(o,1),this.formFoodData.labelGroupInfoList={},this.initLabelGroup(this.formFoodData.selectLabelListData)},selectLaberData:function(e){this.formFoodData.selectLabelIdList=e.selectLabelIdList,this.formFoodData.selectLabelListData=e.selectLabelListData,this.formFoodData.labelGroupInfoList={},this.initLabelGroup(this.formFoodData.selectLabelListData)},setFoodAddIngredient:function(e){var t=this;return k(L().mark((function a(){var i,o,n,s;return L().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,Object(r["Z"])(t.$apis.apiBackgroundFoodFoodAddIngredientPost({food_id:t.formFoodDataDialog.id,ingredient_id:e.ingredient_id,weight:e.weight}));case 2:if(i=a.sent,o=y(i,2),n=o[0],s=o[1],!n){a.next=9;break}return t.$message.error(n.message),a.abrupt("return");case 9:0===s.code?(t.showDialog=!1,t.$message.success(s.msg),t.$emit("confirm","search")):t.$message.error(s.msg);case 10:case"end":return a.stop()}}),a)})))()},setFoodDeleteIngredient:function(e){var t=this;return k(L().mark((function a(){var i,o,n,s;return L().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,Object(r["Z"])(t.$apis.apiBackgroundFoodFoodDeleteIngredientPost({ids:[e.id],food_id:t.formFoodDataDialog.id}));case 2:if(i=a.sent,o=y(i,2),n=o[0],s=o[1],!n){a.next=9;break}return t.$message.error(n.message),a.abrupt("return");case 9:0===s.code?(t.showDialog=!1,t.$message.success(s.msg),t.$emit("confirm","search")):t.$message.error(s.msg);case 10:case"end":return a.stop()}}),a)})))()},getSystemIngredientList:function(){var e=this;return k(L().mark((function t(){var a,i,o,n;return L().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.foodIngredientList=[],e.systemIngredientsIdsObj={},e.formFoodLoading=!0,t.next=5,Object(r["Z"])(e.$apis.apiBackgroundFoodIngredientSystemIngredientPost({is_copy:"all"}));case 5:if(a=t.sent,i=y(a,2),o=i[0],n=i[1],e.formFoodLoading=!1,!o){t.next=12;break}return t.abrupt("return");case 12:0===n.code&&(sessionStorage.setItem("systemIngredientsList",n.data?JSON.stringify(n.data):"[]"),e.setSystemIngrendients(n.data));case 13:case"end":return t.stop()}}),t)})))()},setSystemIngrendients:function(e){var t=this;e&&(this.foodIngredientList=[],this.systemIngredientsIdsObj={},this.systemIngredientsList=e.map((function(e){var a={ingredient_type:"system",ingredient_type_alias:"系统",id:e.id,name:e.name};return t.systemIngredientsIdsObj[e.id]=!0,e.ingredient_type="system",e.ingredient_type_alias="系统",t.allIngredientsList[e.id]=e,a})))},getFoodIngredientList:function(){var e=this;return k(L().mark((function t(){var a,i,o,n;return L().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.formFoodLoading=!0,t.next=3,Object(r["Z"])(e.$apis.apiBackgroundFoodIngredientIngredientNamePost({page:1,page_size:99999}));case 3:if(a=t.sent,i=y(a,2),o=i[0],n=i[1],e.formFoodLoading=!1,!o){t.next=11;break}return e.$message.error(o.message),t.abrupt("return");case 11:0===n.code?(sessionStorage.setItem("foodIngredientList",n.data?JSON.stringify(n.data):"[]"),e.setMerchantIngrendients(n.data)):e.$message.error(n.msg);case 12:case"end":return t.stop()}}),t)})))()},setMerchantIngrendients:function(e){var t=this;if(e){var a=e.map((function(e){return t.allIngredientsList[e.id]=e,{id:e.id,name:e.name}}));Object.freeze(this.allIngredientsList),this.foodIngredientList=a.concat(this.systemIngredientsList).map((function(e,t){return p(p({},e),{},{index:t+1})})),this.systemIngredientsList=[],this.formFoodData.ingredientList[0].selectFoodIngredient=Object(r["f"])(this.foodIngredientList),"edit"===this.showDialogMealFoodType&&this.initFormFoodDataDialog(this.showDialogMealFoodType,this.formFoodDataDialog),"edit"===this.type&&this.changePercentage(),this.isDisabledOtherMeal()}},setFoodAdd:function(e){var t=this;return k(L().mark((function a(){var i,o,n,s;return L().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.formFoodLoading=!0,a.next=3,Object(r["Z"])(t.$apis.apiBackgroundFoodFoodAddPost(e));case 3:if(i=a.sent,o=y(i,2),n=o[0],s=o[1],t.formFoodLoading=!1,!n){a.next=11;break}return t.$message.error(n.message),a.abrupt("return");case 11:0===s.code?(t.showDialog=!1,t.$message.success(s.msg),t.$emit("confirm","search")):t.$message.error(s.msg);case 12:case"end":return a.stop()}}),a)})))()},setFoodEdit:function(e){var t=this;return k(L().mark((function a(){var i,o,n,s;return L().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.formFoodLoading=!0,a.next=3,Object(r["Z"])(t.$apis.apiBackgroundFoodFoodModifyPost(e));case 3:if(i=a.sent,o=y(i,2),n=o[0],s=o[1],t.formFoodLoading=!1,!n){a.next=11;break}return t.$message.error(n.message),a.abrupt("return");case 11:0===s.code?(t.showDialog=!1,t.$message.success(s.msg),t.$emit("confirm","search")):t.$message.error(s.msg);case 12:case"end":return a.stop()}}),a)})))()},setFoodBatchEdit:function(e){var t=this;return k(L().mark((function a(){var i,o,n,s;return L().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.formFoodLoading=!0,a.next=3,Object(r["Z"])(t.$apis.apiBackgroundFoodFoodBatchModifyPost(e));case 3:if(i=a.sent,o=y(i,2),n=o[0],s=o[1],t.formFoodLoading=!1,!n){a.next=11;break}return t.$message.error(n.message),a.abrupt("return");case 11:0===s.code?(t.showDialog=!1,t.$emit("confirm","search")):t.$message.error(s.msg);case 12:case"end":return a.stop()}}),a)})))()},initAddAndEditFood:function(){var e=this,t={name:this.formFoodData.foodName,alias_name:this.formFoodData.aliasName,category_id:this.formFoodData.categoryId,taste_list:[],spec_list:[this.formFoodData.specList],attributes:this.formFoodData.attributes,image:this.formFoodData.foodImages[0],extra_image:this.formFoodData.extraImages,count_type:this.formFoodData.countType,weight_type:this.formFoodData.weightType,food_price:this.formFoodData.foodPrice?Object(r["Y"])(this.formFoodData.foodPrice):0,weight_price:this.formFoodData.weightPrice?Object(r["Y"])(this.formFoodData.weightPrice):0,weight:this.formFoodData.weight?this.formFoodData.weight:0,start_gram:this.formFoodData.startGram?this.formFoodData.startGram:0,fault_rate:this.formFoodData.faultRate?this.formFoodData.faultRate:0,origin_price:this.formFoodData.originPrice?Object(r["Y"])(this.formFoodData.originPrice):0,pack_price:this.formFoodData.packPrice?Object(r["Y"])(this.formFoodData.packPrice):0,device_picture_list:[],ingredient_list:[],nutrition_info:{},label_list:this.formFoodData.selectLabelIdList,is_chef_recommended:this.formFoodData.is_chef_recommended,description:this.formFoodData.description};if(1===this.formFoodData.countType){var a=Object(r["f"])(this.formFoodData.foodPriceJson);t.food_price_json=a.map((function(e){return e.price=Object(r["Y"])(e.price),e}))}"goods"===this.formFoodData.attributes&&(t.barcode=this.formFoodData.barcode),this.formFoodData.ingredientList&&this.formFoodData.ingredientList.length&&this.formFoodData.ingredientList.map((function(e){if(e.id){var a={ingredient_id:e.id,ingredient_scale:e.percentage,ingredient_type:e.ingredient_type};t.ingredient_list.push(a)}}));var i={},o={};return d["NUTRITION_LIST"].forEach((function(a){"default"===a.type&&(t.nutrition_info[a.key]=e.tableDataNutrition[a.key]),"element"===a.type&&(i[a.key]=e.tableDataNutrition[a.key]),"vitamin"===a.type&&(o[a.key]=e.tableDataNutrition[a.key])})),t.nutrition_info.element=JSON.stringify(i),t.nutrition_info.vitamin=JSON.stringify(o),t},determineDialogFood:function(){var e=this;this.$refs.formFoodData.validate((function(t){var a=!1;if(e.$refs.nutritionRef.$refs.formIngredients.validate((function(e){a=e})),!t||!a||e.errorMsg.percentageError)return e.$message.error("请认真检查表单数据！"),!1;"add"===e.showDialogMealFoodType?e.setFoodAdd(e.initAddAndEditFood()):"edit"===e.showDialogMealFoodType?e.setFoodEdit(p({id:e.formFoodDataDialog.id,no:e.formFoodDataDialog.no},e.initAddAndEditFood())):"batchAdd"===e.showDialogMealFoodType&&e.setFoodBatchEdit(p({ids:e.selectListId,no:e.formFoodDataDialog.no},e.initAddAndEditFood()))}))},handelChange:function(e,t){this.uploadParams.key=this.uploadParams.prefix+(new Date).getTime()+Math.floor(150*Math.random())+".png"},handleFoodImgSuccess:function(e,t,a){0===e.code?(this.formFoodData.foodImagesList=a,this.formFoodData.foodImages.push(e.data.public_url)):this.$message.error(e.msg)},handleExtraImgSuccess:function(e,t,a){0===e.code?(this.formFoodData.extraImagesList=a,this.formFoodData.extraImages.push(e.data.public_url)):this.$message.error(e.msg)},handlePictureCardPreview:function(e){this.dialogImageUrl=e.url,this.dialogVisible=!0},handleFoodImgRemove:function(e,t){var a=this.formFoodData[t+"List"].findIndex((function(t){return t.url===e.url}));this.formFoodData[t].splice(a,1),this.formFoodData[t+"List"].splice(a,1)},beforeFoodImgUpload:function(e){var t=[".jpeg",".jpg",".png",".bmp"],a=e.size/1024/1024<2;return t.includes(Object(r["A"])(e.name))?a?void 0:(this.$message.error("上传图片大小不能超过 2MB!"),!1):(this.$message.error("上传图片只能是JPG/BMP/PNG格式!"),!1)},changeIngredient:function(e){var t=this.allIngredientsList[e.id];this.formFoodData.ingredientList.forEach((function(e){e.ingredient_id===t.id&&(t.id?(e.name=t.name,e.nutrition=t.nutrition,e.id=t.id,e.ingredient_type=t.ingredient_type):e.name="")})),this.isDisabledOtherMeal(),this.computedNutritionAndPercentage(!0)},computedNutritionAndPercentage:function(){var e=this,t={};d["NUTRITION_LIST"].forEach((function(e){t[e.key]=0}));var a=0;this.formFoodData.ingredientList.map((function(i,o){var s={};if(i.ingredient_id){o<e.formFoodData.ingredientList.length-1?(i.percentage=parseInt(n["a"].divide(100,e.formFoodData.ingredientList.length)),a=n["a"].plus(i.percentage,a)):i.percentage=parseInt(n["a"].minus(100,a));var l=i.percentage/100;if(e.allIngredientsList[i.ingredient_id]&&e.allIngredientsList[i.ingredient_id].nutrition&&(s=e.allIngredientsList[i.ingredient_id].nutrition),t.energy_kcal=+s.energy_kcal?n["a"].plus(t.energy_kcal,s.energy_kcal*l):t.energy_kcal?t.energy_kcal:0,t.protein=+s.protein?n["a"].plus(t.protein,s.protein*l):t.protein?t.protein:0,t.axunge=+s.axunge?n["a"].plus(t.axunge,s.axunge*l):t.axunge?t.axunge:0,t.carbohydrate=+s.carbohydrate?n["a"].plus(t.carbohydrate,s.carbohydrate*l):t.carbohydrate?t.carbohydrate:0,t.cholesterol=+s.cholesterol?n["a"].plus(t.cholesterol,s.cholesterol*l):t.cholesterol?t.cholesterol:0,t.dietary_fiber=+s.dietary_fiber?n["a"].plus(t.dietary_fiber,s.dietary_fiber*l):t.dietary_fiber?t.dietary_fiber:0,s.element&&s.vitamin)try{var d=JSON.parse(Object(r["R"])(s.element)),c=JSON.parse(Object(r["R"])(s.vitamin));for(var u in d)t[u]=n["a"].plus(t[u],+d[u]?d[u]*l:0);for(var f in c)t[f]=n["a"].plus(t[f],+c[f]?c[f]*l:0)}catch(m){}e.deepFormIngredients&&e.deepFormIngredients.length&&e.deepFormIngredients.forEach((function(e){e.id===i.id&&(i.status=!0)}))}})),d["NUTRITION_LIST"].forEach((function(a){var i=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;i.test(t[a.key])?e.$set(e.tableDataNutrition,a.key,t[a.key]):e.$set(e.tableDataNutrition,a.key,t[a.key].toFixed(2))}))},setNutritionAndPercentage:function(){var e=this,t={};d["NUTRITION_LIST"].forEach((function(e){t[e.key]=0})),this.formFoodData.ingredientList.map((function(a,i){if(a.ingredient_id){var o={};e.allIngredientsList[a.ingredient_id]&&e.allIngredientsList[a.ingredient_id].nutrition&&(o=e.allIngredientsList[a.ingredient_id].nutrition);var s=a.percentage/100;if(t.energy_kcal=o.energy_kcal?n["a"].plus(t.energy_kcal,o.energy_kcal*s):t.energy_kcal?t.energy_kcal:0,t.protein=o.protein?n["a"].plus(t.protein,o.protein*s):t.protein?t.protein:0,t.axunge=o.axunge?n["a"].plus(t.axunge,o.axunge*s):t.axunge?t.axunge:0,t.carbohydrate=o.carbohydrate?n["a"].plus(t.carbohydrate,o.carbohydrate*s):t.carbohydrate?t.carbohydrate:0,t.cholesterol=o.cholesterol?n["a"].plus(t.cholesterol,o.cholesterol*s):t.cholesterol?t.cholesterol:0,t.dietary_fiber=o.dietary_fiber?n["a"].plus(t.dietary_fiber,o.dietary_fiber*s):t.dietary_fiber?t.dietary_fiber:0,o.element&&o.vitamin)try{var l=JSON.parse(Object(r["R"])(o.element)),d=JSON.parse(Object(r["R"])(o.vitamin));for(var c in l)t[c]=n["a"].plus(t[c],+l[c]?l[c]*s:0);for(var u in d)t[u]=n["a"].plus(t[u],+d[u]?d[u]*s:0)}catch(f){}}})),d["NUTRITION_LIST"].forEach((function(a){var i=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;i.test(t[a.key])?e.$set(e.tableDataNutrition,a.key,t[a.key]):e.$set(e.tableDataNutrition,a.key,t[a.key].toFixed(2))}))},changePercentage:function(e){this.setNutritionAndPercentage();var t=this.formFoodData.ingredientList.reduce((function(e,t){return n["a"].plus(t.percentage,e)}),0);this.errorMsg.percentageError=t>100||t<100?"菜品每100g所含食材占比，相加必须等于100%":""},isDisabledOtherMeal:function(){var e=this;this.allSelectIngredient=[],this.formFoodData.ingredientList&&this.formFoodData.ingredientList.length&&(this.formFoodData.ingredientList.map((function(t,a){t.ingredient_id&&e.allSelectIngredient.push(t.ingredient_id)})),this.formFoodData.ingredientList.forEach((function(t,a){t.selectFoodIngredient.forEach((function(a){e.allSelectIngredient.includes(a.id)&&t.ingredient_id!==a.id?a.disabled=!0:a.disabled=!1}))})))},initCountData:function(){this.formFoodData.foodPrice="",this.formFoodData.weightPrice="",this.formFoodData.weight="",this.formFoodData.startGram="",this.formFoodData.faultRate="",this.formFoodData.originPrice="",this.formFoodData.packPrice=""},changeCount:function(){this.initCountData()},changeWeightType:function(){this.initCountData()},changeNewProduct:function(){},addFoodPriceJson:function(){this.formFoodData.foodPriceJson.push({spec:"",price:"",weight:""})},deleteFoodPriceJson:function(e){this.formFoodData.foodPriceJson.splice(e,1)},addFoodAliasName:function(){this.formFoodData.aliasName.push("")},delFoodAliasName:function(e){this.formFoodData.aliasName.splice(e,1)},tabClick:function(){this.ruleSingleInfo.isAdmin=!this.ruleSingleInfo.isAdmin,this.$refs.selectLaber.currentPage=1,this.$refs.selectLaber.getLabelGroupList()},initLabelGroup:function(e){var t=this;e.forEach((function(e){t.formFoodData.labelGroupInfoList[e.label_group_name]||(t.formFoodData.labelGroupInfoList[e.label_group_name]=[]),t.formFoodData.labelGroupInfoList[e.label_group_name]&&!t.formFoodData.labelGroupInfoList[e.label_group_name].includes(e)&&t.formFoodData.labelGroupInfoList[e.label_group_name].push(e)}))},changeFoodHandle:function(e){this.isSelectFood=!0,this.selectFood=this.foodList.find((function(t){return t.id===e})),this.filterFoodName=this.selectFood.name,this.initFormFoodDataDialog(this.showDialogMealFoodType,this.selectFood)},clearFoodHandle:function(e){this.foodList=[],this.isSelectFood=!1},visibleChangeFoodHandle:function(e){var t=this;e?this.$nextTick((function(e){t.filterFoodName&&!t.isSelectFood&&(t.$refs.foodNameRef.query=t.filterFoodName,t.$refs.foodNameRef.selectedLabel=t.filterFoodName,t.$refs.foodNameRef.handleQueryChange(t.filterFoodName))})):this.isSelectFood||(this.formFoodData.foodName=this.filterFoodName)},focusFoodHandle:function(e){var t=this;this.$nextTick((function(e){setTimeout((function(){t.$refs.foodNameRef.query=t.filterFoodName,t.$refs.foodNameRef.selectedLabel=t.filterFoodName}),70)}))},getSystemFoodList:function(e){var t=this;return k(L().mark((function a(){var i,o,n,s;return L().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(!e||e!==t.filterFoodName){a.next=2;break}return a.abrupt("return");case 2:if(t.filterFoodName=e,e){a.next=11;break}return t.$refs.foodNameRef.query="",t.$refs.foodNameRef.selectedLabel="",t.isSelectFood||(t.formFoodData.foodName=e),t.foodList=[],a.abrupt("return");case 11:t.formFoodData.foodName=e;case 12:return t.remoteLoading=!0,a.next=15,Object(r["Z"])(t.$apis.apiBackgroundFoodFoodStockListPost({food_name:e}));case 15:if(i=a.sent,o=y(i,2),n=o[0],s=o[1],t.remoteLoading=!1,!n){a.next=24;break}return t.$message.error(n.message),t.foodList=[],a.abrupt("return");case 24:0===s.code?0!==s.data.results.length?(t.foodList=[],s.data.results.map((function(a){a.highlight_text=a.name,t.foodList.push(t.setHightLight(a,e)),a.alias_name&&a.alias_name.length&&a.alias_name.map((function(i,o){var n=Object(r["f"])(a);n.id=n.id+(new Date).getTime()+o,n.original_id=n.id,n.name=i,n.alias_name=[],n.highlight_text=n.name,t.foodList.push(t.setHightLight(n,e))}))}))):t.getSystemFoodListAgain(e):(t.$message.error(s.msg),t.foodList=[]);case 25:case"end":return a.stop()}}),a)})))()},setHightLight:function(e,t){var a=new RegExp(t,"g"),i='<span style="color: #FF9B45;">'.concat(t,"</span>");return e.highlight_text.match(a)?(e.highlight_text=e.highlight_text.replace(a,i),e):e},getSystemFoodListAgain:function(e){var t=this;return k(L().mark((function a(){var i,o,n,s;return L().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,Object(r["Z"])(t.$apis.apiBackgroundFoodFoodStockListPost({food_name:e,is_pair:!0}));case 2:if(i=a.sent,o=y(i,2),n=o[0],s=o[1],!n){a.next=10;break}return t.$message.error(n.message),t.foodList=[],a.abrupt("return");case 10:0===s.code?(t.foodList=[],s.data.results.length&&(s.data.results.map((function(e){t.formFoodData.ingredientList=s.data.results.map((function(e){var a={id:e.ingredient_id,ingredient_id:e.ingredient_id,name:e.ingredient_name,nutrition:t.foodIngredientNutrition(e),selectFoodIngredient:Object(r["f"])(t.foodIngredientList),percentage:e.ingredient_scale,ingredient_type:t.systemIngredientsIdsObj[e.ingredient]?"super":""};return a}))})),t.formFoodData.ingredientList.forEach((function(e){var a=t.allIngredientsList[e.id];t.formFoodData.ingredientList.forEach((function(e){e.ingredient_id===a.id&&(a.id?(e.name=a.name,e.nutrition=a.nutrition,e.id=a.id,e.ingredient_type=a.ingredient_type):e.name="")})),t.computedNutritionAndPercentage(!0)})))):(t.$message.error(s.msg),t.foodList=[]);case 11:case"end":return a.stop()}}),a)})))()}}},N=x,P=(a("dd14"),a("2877")),C=Object(P["a"])(N,i,o,!1,null,null,null);t["default"]=C.exports}}]);