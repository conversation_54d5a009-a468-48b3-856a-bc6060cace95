(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-dashboard-components-management"],{"2a59":function(e,t,o){"use strict";o.r(t);var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"management"},[e._m(0),t("div",{staticClass:"chart"},[t("div",{staticClass:"left"},[t("div",{staticClass:"block h-770"},[t("div",{staticClass:"title"},[e._v("当前餐段菜品余量")]),t("el-button",{staticClass:"ps-bule block-btn",attrs:{type:"text",size:"small"},on:{click:function(t){return e.openDialog("stock")}}},[e._v("编辑")])],1)]),t("div",{staticClass:"center"},[t("div",{staticClass:"block h-770"},[t("div",{staticClass:"title"},[e._v("今日菜单")]),t("el-button",{staticClass:"ps-bule block-btn",attrs:{type:"text",size:"small"},on:{click:function(t){return e.openDialog("menu")}}},[e._v("编辑")])],1)]),t("div",{staticClass:"right"},[t("div",{staticClass:"block h-380"},[t("div",{staticClass:"title"},[e._v("当前餐段客流高峰统计")]),t("el-button",{staticClass:"ps-bule block-btn",attrs:{type:"text",size:"small"},on:{click:function(t){return e.openDialog("peak")}}},[e._v("编辑")])],1),t("div",{staticClass:"block h-380"},[t("div",{staticClass:"title"},[e._v("菜品销量排行")]),t("el-button",{staticClass:"ps-bule block-btn",attrs:{type:"text",size:"small"},on:{click:function(t){return e.openDialog("sales")}}},[e._v("编辑")])],1)])]),t("el-dialog",{attrs:{title:"选择组织",visible:e.showDialog,width:"500px","custom-class":"ps-dialog"},on:{"update:visible":function(t){e.showDialog=t}}},[t("el-form",{ref:"dialogFormRef",staticClass:"dialog-form",attrs:{model:e.dialogForm,"label-width":"120px",rules:e.dialogFormRules}},["peak"===e.dialogType||"sales"===e.dialogType?t("div",[t("el-form-item",{attrs:{label:"显示组织：",prop:"orgId"}},[t("organization-select",{staticClass:"search-item-w ps-input w-250",attrs:{placeholder:"请选择所属组织",isLazy:!1,multiple:!1,"check-strictly":!0,"append-to-body":!0},on:{change:e.changeOrg},model:{value:e.dialogForm.orgId,callback:function(t){e.$set(e.dialogForm,"orgId",t)},expression:"dialogForm.orgId"}})],1)],1):e._e(),"stock"===e.dialogType||"menu"===e.dialogType?t("div",[t("el-form-item",{attrs:{label:"显示组织：",prop:"orgId"}},[t("organization-select",{staticClass:"search-item-w ps-input w-250",attrs:{placeholder:"请选择所属组织",isLazy:!1,multiple:!1,"check-strictly":!0,"append-to-body":!0},model:{value:e.dialogForm.orgId,callback:function(t){e.$set(e.dialogForm,"orgId",t)},expression:"dialogForm.orgId"}})],1),t("el-form-item",{attrs:{label:"菜谱："}},[t("el-radio-group",{staticClass:"ps-radio",on:{change:e.changeMenuType},model:{value:e.dialogForm.menuType,callback:function(t){e.$set(e.dialogForm,"menuType",t)},expression:"dialogForm.menuType"}},[t("el-radio",{attrs:{label:"week"}},[e._v("周菜谱")]),t("el-radio",{attrs:{label:"month"}},[e._v("月菜谱")])],1)],1),t("el-form-item",{attrs:{label:"设备类型：",prop:"deviceType"}},[t("el-select",{staticClass:"ps-select w-250",attrs:{multiple:"",placeholder:"请选择设备类型","popper-class":"ps-popper-select"},on:{change:e.deviceTypeChange},model:{value:e.dialogForm.deviceType,callback:function(t){e.$set(e.dialogForm,"deviceType",t)},expression:"dialogForm.deviceType"}},e._l(e.deviceList,(function(e){return t("el-option",{key:e.key,attrs:{label:e.name,value:e.key}})})),1)],1),t("el-form-item",{attrs:{label:"设备型号：",prop:e.isDisabledModel?"":"deviceModel"}},[t("el-select",{staticClass:"ps-select w-250",attrs:{multiple:"",placeholder:"请选择设备型号","popper-class":"ps-popper-select",disabled:e.isDisabledModel},on:{change:e.deviceModelChange},model:{value:e.dialogForm.deviceModel,callback:function(t){e.$set(e.dialogForm,"deviceModel",t)},expression:"dialogForm.deviceModel"}},e._l(e.deviceModelList,(function(e){return t("el-option",{key:e.key,attrs:{label:e.key,value:e.key}})})),1)],1),t("el-form-item",{attrs:{label:"菜谱：",prop:"menuId"}},[t("el-select",{staticClass:"ps-select w-250",attrs:{multiple:""},model:{value:e.dialogForm.menuId,callback:function(t){e.$set(e.dialogForm,"menuId",t)},expression:"dialogForm.menuId"}},e._l(e.menuList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1):e._e(),"camera"===e.dialogType?t("div",[t("el-form-item",{attrs:{label:"显示摄像头",prop:"cameraId"}},[t("el-select",{staticClass:"ps-select w-250",attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select"},model:{value:e.dialogForm.cameraId,callback:function(t){e.$set(e.dialogForm,"cameraId",t)},expression:"dialogForm.cameraId"}},e._l(e.cameraList,(function(e){return t("el-option",{key:e.camera_id,attrs:{label:e.name,value:e.camera_id}})})),1)],1)],1):e._e()]),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-cancel-btn",on:{click:function(t){e.showDialog=!1}}},[e._v("取 消")]),t("el-button",{staticClass:"ps-btn",attrs:{type:"primary"},on:{click:e.confirmDialog}},[e._v("确 定")])],1)],1)],1)},i=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"header"},[t("div",{staticClass:"title"},[e._v("经营数据大屏")]),t("div",{staticClass:"time"},[e._v("2022-08-30 08:36:50")])])}],a=o("cbfb");function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function s(e){return u(e)||d(e)||c(e)||l()}function l(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function c(e,t){if(e){if("string"==typeof e)return p(e,t);var o={}.toString.call(e).slice(8,-1);return"Object"===o&&e.constructor&&(o=e.constructor.name),"Map"===o||"Set"===o?Array.from(e):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?p(e,t):void 0}}function d(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function u(e){if(Array.isArray(e))return p(e)}function p(e,t){(null==t||t>e.length)&&(t=e.length);for(var o=0,r=Array(t);o<t;o++)r[o]=e[o];return r}function h(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */h=function(){return t};var e,t={},o=Object.prototype,r=o.hasOwnProperty,i=Object.defineProperty||function(e,t,o){e[t]=o.value},a="function"==typeof Symbol?Symbol:{},s=a.iterator||"@@iterator",l=a.asyncIterator||"@@asyncIterator",c=a.toStringTag||"@@toStringTag";function d(e,t,o){return Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(e){d=function(e,t,o){return e[t]=o}}function u(e,t,o,r){var a=t&&t.prototype instanceof b?t:b,n=Object.create(a.prototype),s=new E(r||[]);return i(n,"_invoke",{value:_(e,o,s)}),n}function p(e,t,o){try{return{type:"normal",arg:e.call(t,o)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var m="suspendedStart",f="suspendedYield",g="executing",v="completed",y={};function b(){}function I(){}function k(){}var w={};d(w,s,(function(){return this}));var F=Object.getPrototypeOf,T=F&&F(F(S([])));T&&T!==o&&r.call(T,s)&&(w=T);var M=k.prototype=b.prototype=Object.create(w);function L(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,t){function o(i,a,s,l){var c=p(e[i],e,a);if("throw"!==c.type){var d=c.arg,u=d.value;return u&&"object"==n(u)&&r.call(u,"__await")?t.resolve(u.__await).then((function(e){o("next",e,s,l)}),(function(e){o("throw",e,s,l)})):t.resolve(u).then((function(e){d.value=e,s(d)}),(function(e){return o("throw",e,s,l)}))}l(c.arg)}var a;i(this,"_invoke",{value:function(e,r){function i(){return new t((function(t,i){o(e,r,t,i)}))}return a=a?a.then(i,i):i()}})}function _(t,o,r){var i=m;return function(a,n){if(i===g)throw Error("Generator is already running");if(i===v){if("throw"===a)throw n;return{value:e,done:!0}}for(r.method=a,r.arg=n;;){var s=r.delegate;if(s){var l=D(s,r);if(l){if(l===y)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===m)throw i=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=g;var c=p(t,o,r);if("normal"===c.type){if(i=r.done?v:f,c.arg===y)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(i=v,r.method="throw",r.arg=c.arg)}}}function D(t,o){var r=o.method,i=t.iterator[r];if(i===e)return o.delegate=null,"throw"===r&&t.iterator.return&&(o.method="return",o.arg=e,D(t,o),"throw"===o.method)||"return"!==r&&(o.method="throw",o.arg=new TypeError("The iterator does not provide a '"+r+"' method")),y;var a=p(i,t.iterator,o.arg);if("throw"===a.type)return o.method="throw",o.arg=a.arg,o.delegate=null,y;var n=a.arg;return n?n.done?(o[t.resultName]=n.value,o.next=t.nextLoc,"return"!==o.method&&(o.method="next",o.arg=e),o.delegate=null,y):n:(o.method="throw",o.arg=new TypeError("iterator result is not an object"),o.delegate=null,y)}function C(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function O(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function E(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(C,this),this.reset(!0)}function S(t){if(t||""===t){var o=t[s];if(o)return o.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,a=function o(){for(;++i<t.length;)if(r.call(t,i))return o.value=t[i],o.done=!1,o;return o.value=e,o.done=!0,o};return a.next=a}}throw new TypeError(n(t)+" is not iterable")}return I.prototype=k,i(M,"constructor",{value:k,configurable:!0}),i(k,"constructor",{value:I,configurable:!0}),I.displayName=d(k,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===I||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,k):(e.__proto__=k,d(e,c,"GeneratorFunction")),e.prototype=Object.create(M),e},t.awrap=function(e){return{__await:e}},L(x.prototype),d(x.prototype,l,(function(){return this})),t.AsyncIterator=x,t.async=function(e,o,r,i,a){void 0===a&&(a=Promise);var n=new x(u(e,o,r,i),a);return t.isGeneratorFunction(o)?n:n.next().then((function(e){return e.done?e.value:n.next()}))},L(M),d(M,c,"Generator"),d(M,s,(function(){return this})),d(M,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),o=[];for(var r in t)o.push(r);return o.reverse(),function e(){for(;o.length;){var r=o.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=S,E.prototype={constructor:E,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(O),!t)for(var o in this)"t"===o.charAt(0)&&r.call(this,o)&&!isNaN(+o.slice(1))&&(this[o]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var o=this;function i(r,i){return s.type="throw",s.arg=t,o.next=r,i&&(o.method="next",o.arg=e),!!i}for(var a=this.tryEntries.length-1;a>=0;--a){var n=this.tryEntries[a],s=n.completion;if("root"===n.tryLoc)return i("end");if(n.tryLoc<=this.prev){var l=r.call(n,"catchLoc"),c=r.call(n,"finallyLoc");if(l&&c){if(this.prev<n.catchLoc)return i(n.catchLoc,!0);if(this.prev<n.finallyLoc)return i(n.finallyLoc)}else if(l){if(this.prev<n.catchLoc)return i(n.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<n.finallyLoc)return i(n.finallyLoc)}}}},abrupt:function(e,t){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var n=a?a.completion:{};return n.type=e,n.arg=t,a?(this.method="next",this.next=a.finallyLoc,y):this.complete(n)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var o=this.tryEntries[t];if(o.finallyLoc===e)return this.complete(o.completion,o.afterLoc),O(o),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var o=this.tryEntries[t];if(o.tryLoc===e){var r=o.completion;if("throw"===r.type){var i=r.arg;O(o)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,o,r){return this.delegate={iterator:S(t),resultName:o,nextLoc:r},"next"===this.method&&(this.arg=e),y}},t}function m(e,t,o,r,i,a,n){try{var s=e[a](n),l=s.value}catch(e){return void o(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function f(e){return function(){var t=this,o=arguments;return new Promise((function(r,i){var a=e.apply(t,o);function n(e){m(a,r,i,n,s,"next",e)}function s(e){m(a,r,i,n,s,"throw",e)}n(void 0)}))}}var g={name:"management",components:{OrganizationSelect:a["a"]},props:{type:String,cameraList:Array,templateInfo:Object},data:function(){return{dialogType:"",showDialog:!1,dialogForm:{orgId:"",menuType:"",deviceType:[],deviceModel:[],menuId:[],cameraId:""},dialogFormRules:{orgId:[{required:!0,message:"请选择显示组织",trigger:"blur"}],deviceType:[{required:!0,message:"请选择设备类型",trigger:"blur"}],deviceModel:[{required:!0,message:"请选择设备型号",trigger:"blur"}],menuId:[{required:!0,message:"请选择菜谱",trigger:"blur"}]},deviceList:[],deviceModelList:[],isDisabledModel:!1,menuList:[],dataInfo:{stockOrgId:"",stockMenuType:"week",stockDeviceType:"",stockDeviceModel:"",stockMenuId:[],todayMenuOrgId:"",todayMenuType:"week",todayDeviceType:"",todayDeviceModel:"",todayMenuId:[],salesOrgId:"",cameraId:"",peakOrgId:""}}},created:function(){if("edit"===this.type)for(var e in this.templateInfo)this.dataInfo[e]=this.templateInfo[e];this.getOrgDeviceList()},methods:{openDialog:function(e){this.dialogType=e,this.showDialog=!0,"stock"===this.dialogType?(this.dialogForm.orgId=this.dataInfo.stockOrgId,this.dialogForm.menuType=this.dataInfo.stockMenuType,this.dialogForm.deviceType=this.dataInfo.stockDeviceType,this.dialogForm.deviceModel=this.dataInfo.stockDeviceModel,this.dialogForm.menuId=this.dataInfo.stockMenuId,this.dialogForm.deviceType.length?this.getDeviceModel():this.isDisabledModel=!1,this.checkGetMemuList()):"camera"===this.dialogType?this.dialogForm.cameraId=this.dataInfo.cameraId:"menu"===this.dialogType?(this.dialogForm.orgId=this.dataInfo.todayMenuOrgId,this.dialogForm.menuType=this.dataInfo.todayMenuType,this.dialogForm.deviceType=this.dataInfo.todayDeviceType,this.dialogForm.deviceModel=this.dataInfo.todayDeviceModel,this.dialogForm.menuId=this.dataInfo.todayMenuId,this.dialogForm.deviceType.length?this.getDeviceModel():this.isDisabledModel=!1,this.checkGetMemuList()):"peak"===this.dialogType?this.dialogForm.orgId=this.dataInfo.peakOrgId:"sales"===this.dialogType&&(this.dialogForm.orgId=this.dataInfo.salesOrgId)},confirmDialog:function(){var e=this;this.$refs.dialogFormRef.validate((function(t){t&&("stock"===e.dialogType?(e.dataInfo.stockOrgId=e.dialogForm.orgId,e.dataInfo.stockMenuType=e.dialogForm.menuType,e.dataInfo.stockDeviceType=e.dialogForm.deviceType,e.dataInfo.stockDeviceModel=e.dialogForm.deviceModel,e.dataInfo.stockMenuId=e.dialogForm.menuId):"camera"===e.dialogType?e.dataInfo.cameraId=e.dialogForm.cameraId:"menu"===e.dialogType?(e.dataInfo.todayMenuOrgId=e.dialogForm.orgId,e.dataInfo.todayMenuType=e.dialogForm.menuType,e.dataInfo.todayDeviceType=e.dialogForm.deviceType,e.dataInfo.todayDeviceModel=e.dialogForm.deviceModel,e.dataInfo.todayMenuId=e.dialogForm.menuId):"peak"===e.dialogType?e.dataInfo.peakOrgId=e.dialogForm.orgId:"sales"===e.dialogType&&(e.dataInfo.salesOrgId=e.dialogForm.orgId),e.$refs.dialogFormRef.clearValidate(),e.dialogForm={orgId:"",menuType:"",deviceType:[],deviceModel:[],menuId:[],cameraId:""},e.showDialog=!1,e.$emit("comfirm",e.dataInfo))}))},changeOrg:function(){this.dialogForm.menuId=[],this.menuList=[],this.checkGetMemuList()},changeMenuType:function(){this.dialogForm.menuId=[],this.menuList=[],this.checkGetMemuList()},deviceTypeChange:function(){this.dialogForm.menuId=[],this.menuList=[],this.dialogForm.deviceModel=[],this.deviceModelList=[],this.dialogForm.deviceType.length?this.getDeviceModel():this.isDisabledModel=!1,this.checkGetMemuList()},deviceModelChange:function(){this.dialogForm.menuId=[],this.menuList=[],this.checkGetMemuList()},checkGetMemuList:function(){this.dialogForm.deviceType.length&&this.dialogForm.orgId&&(-1!==this.dialogForm.deviceType.indexOf("H5")||-1!==this.dialogForm.deviceType.indexOf("MAPP")||this.dialogForm.deviceModel.length)&&this.getMenuList()},getMenuList:function(){var e=this;return f(h().mark((function t(){var o;return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundDatascreenKanbanTempGetKanbanSettingPost({organization_id:[e.dialogForm.orgId],device_type:e.dialogForm.deviceType,device_model:e.dialogForm.deviceModel,menu_type:e.dialogForm.menuType});case 2:o=t.sent,0===o.code?e.menuList=o.data:e.$message.error(o.msg);case 4:case"end":return t.stop()}}),t)})))()},getOrgDeviceList:function(){var e=this;return f(h().mark((function t(){var o;return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundDeviceDeviceDeviceTypePost({source:"self"});case 2:o=t.sent,0===o.code?e.deviceList=[{name:"H5",key:"H5"},{name:"小程序",key:"MAPP"}].concat(s(o.data)):e.$message.error(o.msg);case 4:case"end":return t.stop()}}),t)})))()},getDeviceModel:function(){var e=this;return f(h().mark((function t(){var o,r;return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(o=e.dialogForm.deviceType.filter((function(e){return"H5"!==e&&"MAPP"!==e})),o.length){t.next=6;break}return e.isDisabledModel=!0,t.abrupt("return");case 6:e.isDisabledModel=!1;case 7:return t.next=9,e.$apis.apiBackgroundDeviceDeviceDeviceModelPost({device_types:o});case 9:r=t.sent,0===r.code?e.deviceModelList=r.data:e.$message.error(r.msg);case 11:case"end":return t.stop()}}),t)})))()}}},v=g,y=(o("f2f4"),o("2877")),b=Object(y["a"])(v,r,i,!1,null,null,null);t["default"]=b.exports},db87:function(e,t,o){},f2f4:function(e,t,o){"use strict";o("db87")}}]);