(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-address-admin-components-AddressAreaDialog"],{"1c85":function(t,e,r){t.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}},"6e59":function(t,e,r){"use strict";r("1c85")},f262:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("dialog-message",{attrs:{show:t.visible,title:t.title,loading:t.isLoading,customClass:"ps-dialog",width:t.width,"destroy-on-close":!1},on:{"update:show":function(e){t.visible=e},"update:loading":function(e){t.isLoading=e},close:t.handleClose}},[t.visible?e("el-form",{ref:"dialogForm",attrs:{model:t.dialogForm,"status-icon":"",rules:t.dialogFormRules,"label-width":"120px"},nativeOn:{submit:function(t){t.preventDefault()}}},["add"===t.type||"modify"===t.type?e("div",[e("el-form-item",{attrs:{label:"名称：",prop:"name"}},[e("el-input",{staticClass:"ps-input w-250",attrs:{placeholder:"请输入配送点名称"},model:{value:t.dialogForm.name,callback:function(e){t.$set(t.dialogForm,"name",e)},expression:"dialogForm.name"}})],1),e("el-form-item",{attrs:{label:"组织：",prop:"org"}},[e("organization-select",{staticClass:"search-item-w ps-input w-250",attrs:{placeholder:"请选择所属组织",isLazy:!1,multiple:!1,"check-strictly":!0,"append-to-body":!0,disabled:"modify"===t.type},model:{value:t.dialogForm.org,callback:function(e){t.$set(t.dialogForm,"org",e)},expression:"dialogForm.org"}})],1),e("el-form-item",{attrs:{label:"配送点：",prop:"address"}},[e("address-select",{staticClass:"search-item-w ps-input w-250",attrs:{placeholder:"请选择配送点",multiple:!0,"check-strictly":!0,"is-select-child":!0,"default-expand-all":!0,orgId:t.dialogForm.org,"append-to-body":!0},model:{value:t.dialogForm.address,callback:function(e){t.$set(t.dialogForm,"address",e)},expression:"dialogForm.address"}})],1)],1):t._e(),"copy"===t.type?e("div",{staticStyle:{"margin-left":"90px"}},[e("div",{staticStyle:{"margin-bottom":"20px","font-weight":"bold"}},[t._v("请选择需要复制的组织:")]),e("el-form-item",{attrs:{label:"",prop:"orgs","label-width":"0px"}},[e("organization-select",{staticClass:"search-item-w ps-input w-250",attrs:{placeholder:"请选择需要复制的组织",isLazy:!1,multiple:!0,"check-strictly":!0,"append-to-body":!0},model:{value:t.dialogForm.orgs,callback:function(e){t.$set(t.dialogForm,"orgs",e)},expression:"dialogForm.orgs"}})],1),e("div",{staticStyle:{color:"#ff9b45"}},[t._v("非对应组织使用地址会在复制成功后相应剔除")])],1):t._e()]):t._e(),e("template",{slot:"tool"},[e("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:t.isLoading},on:{click:t.clickCancleHandle}},[t._v("取消")]),e("el-button",{staticClass:"ps-btn",attrs:{disabled:t.isLoading,type:"primary"},on:{click:t.clickConfirmHandle}},[t._v("确定")])],1)])],2)},o=[],i=r("cbfb"),a=r("94c6");function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function c(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function l(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?c(Object(r),!0).forEach((function(e){u(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function u(t,e,r){return(e=d(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function d(t){var e=f(t,"string");return"symbol"==s(e)?e:e+""}function f(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function p(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */p=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function d(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,a=Object.create(i.prototype),s=new C(n||[]);return o(a,"_invoke",{value:_(t,r,s)}),a}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=d;var h="suspendedStart",g="suspendedYield",m="executing",y="completed",v={};function b(){}function w(){}function x(){}var L={};u(L,a,(function(){return this}));var O=Object.getPrototypeOf,A=O&&O(O($([])));A&&A!==r&&n.call(A,a)&&(L=A);var F=x.prototype=b.prototype=Object.create(L);function k(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function j(t,e){function r(o,i,a,c){var l=f(t[o],t,i);if("throw"!==l.type){var u=l.arg,d=u.value;return d&&"object"==s(d)&&n.call(d,"__await")?e.resolve(d.__await).then((function(t){r("next",t,a,c)}),(function(t){r("throw",t,a,c)})):e.resolve(d).then((function(t){u.value=t,a(u)}),(function(t){return r("throw",t,a,c)}))}c(l.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function _(e,r,n){var o=h;return function(i,a){if(o===m)throw Error("Generator is already running");if(o===y){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var s=n.delegate;if(s){var c=E(s,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===h)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=m;var l=f(e,r,n);if("normal"===l.type){if(o=n.done?y:g,l.arg===v)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(o=y,n.method="throw",n.arg=l.arg)}}}function E(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,E(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function P(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function S(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function C(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(P,this),this.reset(!0)}function $(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(s(e)+" is not iterable")}return w.prototype=x,o(F,"constructor",{value:x,configurable:!0}),o(x,"constructor",{value:w,configurable:!0}),w.displayName=u(x,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,x):(t.__proto__=x,u(t,l,"GeneratorFunction")),t.prototype=Object.create(F),t},e.awrap=function(t){return{__await:t}},k(j.prototype),u(j.prototype,c,(function(){return this})),e.AsyncIterator=j,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new j(d(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},k(F),u(F,l,"Generator"),u(F,a,(function(){return this})),u(F,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=$,C.prototype={constructor:C,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(S),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return s.type="throw",s.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),l=n.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),S(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;S(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:$(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function h(t,e,r,n,o,i,a){try{var s=t[i](a),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,o)}function g(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){h(i,n,o,a,s,"next",t)}function s(t){h(i,n,o,a,s,"throw",t)}a(void 0)}))}}var m={name:"AddressAreaDialog",components:{AddressSelect:a["a"],OrganizationSelect:i["a"]},props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:""},width:{type:String,default:"500px"},isshow:Boolean,confirm:Function,addressInfo:{type:Object,default:function(){}},selectListId:{type:Array,default:function(){return[]}}},data:function(){return{isLoading:!1,pageSize:10,totalCount:0,currentPage:1,time:(new Date).getTime(),dialogForm:{name:"",org:null,address:[],orgs:[]},allChildId:[],dialogFormRules:{name:[{required:!0,message:"请输入组织",trigger:"change"}],org:[{required:!0,message:"请选择所属组织",trigger:"blur"}],address:[{required:!0,message:"请选择配送点",trigger:"blur"}],orgs:[{required:!0,message:"请选择组织",trigger:"blur"}]}}},computed:{visible:{get:function(){return this.isshow},set:function(t){this.$emit("update:isshow",t)}}},watch:{visible:function(){this.initLoad()}},created:function(){},mounted:function(){},methods:{initLoad:function(){"modify"===this.type&&(this.dialogForm.name=this.addressInfo.name,this.dialogForm.org=this.addressInfo.organization_id,this.dialogForm.address=this.addressInfo.address_ids)},clickConfirmHandle:function(t){var e=this;this.$refs.dialogForm.validate((function(t){t&&("add"===e.type||"modify"===e.type?e.getAllId():"copy"===e.type&&e.cloneAddressArea())}))},getAllId:function(){var t=this;return g(p().mark((function e(){var r;return p().wrap((function(e){while(1)switch(e.prev=e.next){case 0:r={},r.organization=t.dialogForm.org,r.name=t.dialogForm.name,r.use_points=t.dialogForm.address,"add"===t.type?t.confirmAdd(r):t.modifyAddress(r);case 5:case"end":return e.stop()}}),e)})))()},getAllChildren:function(t){var e=this;return g(p().mark((function r(){var n;return p().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,e.$apis.apiAddressAddersCenterGetAllChildrenPost({id:t});case 2:n=r.sent,0===n.code?(e.allChildId=e.allChildId.concat(n.data),e.allChildId.push(t)):e.$message.error(n.msg);case 4:case"end":return r.stop()}}),r)})))()},confirmAdd:function(t){var e=this;return g(p().mark((function r(){var n;return p().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!e.isLoading){r.next=2;break}return r.abrupt("return");case 2:return e.isLoading=!0,r.next=5,e.$apis.apiAddressAddersAreaAddPost(t);case 5:n=r.sent,e.isLoading=!1,0===n.code?(e.$emit("confirm","search"),e.$message.success(n.msg)):e.$message.error(n.msg);case 8:case"end":return r.stop()}}),r)})))()},modifyAddress:function(t){var e=this;return g(p().mark((function r(){var n;return p().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!e.isLoading){r.next=2;break}return r.abrupt("return");case 2:return e.isLoading=!0,r.next=5,e.$apis.apiAddressAddersAreaModifyPost(l({id:e.addressInfo.id},t));case 5:n=r.sent,e.isLoading=!1,0===n.code?(e.$emit("confirm","search"),e.$message.success(n.msg)):e.$message.error(n.msg);case 8:case"end":return r.stop()}}),r)})))()},cloneAddressArea:function(t){var e=this;return g(p().mark((function t(){var r;return p().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.isLoading){t.next=2;break}return t.abrupt("return");case 2:return e.isLoading=!0,t.next=5,e.$apis.apiAddressAddersAreaClonePost({ids:e.selectListId,used_orgs:e.dialogForm.orgs});case 5:r=t.sent,e.isLoading=!1,0===r.code?(e.$emit("confirm","search"),e.$message.success(r.msg)):e.$message.error(r.msg);case 8:case"end":return t.stop()}}),t)})))()},clickCancleHandle:function(){this.visible=!1},handleClose:function(t){this.dialogForm={name:"",org:null,address:[]},this.isLoading=!1,this.visible=!1},getOrganization:function(t){this.dialogForm.org=t}}},y=m,v=(r("6e59"),r("2877")),b=Object(v["a"])(y,n,o,!1,null,null,null);e["default"]=b.exports}}]);