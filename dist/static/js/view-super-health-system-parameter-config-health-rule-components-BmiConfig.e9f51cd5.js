(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-parameter-config-health-rule-components-BmiConfig"],{"34bf":function(t,e,s){},"86fa":function(t,e,s){"use strict";s.r(e);var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"bmi-config"},[e("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formRef",attrs:{rules:t.formRuls,model:t.formData,size:"small"}},[e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("基本信息")])]),e("div",{staticStyle:{"max-width":"50%",padding:"0 20px"}},[e("el-form-item",{staticClass:"block-label",attrs:{label:"维度分值",prop:"score"}},[e("el-input",{staticClass:"ps-input",attrs:{disabled:t.disabled,placeholder:"请输入维度分值"},model:{value:t.formData.score,callback:function(e){t.$set(t.formData,"score",e)},expression:"formData.score"}},[e("template",{slot:"append"},[t._v("分")])],2)],1),e("el-form-item",{staticClass:"block-label",attrs:{label:"提示语",prop:"tips"}},[e("el-input",{staticClass:"ps-input",attrs:{disabled:t.disabled,type:"textarea",autosize:{minRows:4,maxRows:6},placeholder:"请输入提示语"},model:{value:t.formData.tips,callback:function(e){t.$set(t.formData,"tips",e)},expression:"formData.tips"}})],1),e("div",{staticClass:"form-content-box m-b-20"},[e("div",[t._v("变量值")]),t._l(t.formData.listText,(function(s,a){return e("div",{key:a,staticClass:"p-t-10"},[t._v(" "+t._s(s.text)+" "),e("span",{staticStyle:{color:"red"}},[t._v(t._s(s.tips))])])})),e("div",{staticClass:"p-t-10 p-b-10 flex-between"},[e("div",[t._v("规则配置")]),t.disabled?t._e():e("div",{staticClass:"align-r"},[e("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(e){return t.addRule()}}},[t._v("新增规则")])],1)]),e("div",{staticClass:"p-b-20",staticStyle:{color:"red"}},[t._v("提示：选择不限制请选填0或其他")]),e("div",{staticClass:"ps-flex-align-c"},[e("span",{staticClass:"p-r-10 p-t-5"},[t._v("如果")]),e("div",t._l(t.formData.config,(function(s,a){return e("div",{key:a,staticClass:"ps-flex-align-c flex-wrap"},[e("span",{staticClass:"p-r-10 p-t-5"},[t._v("x完成度在y正负")]),e("el-form-item",{attrs:{label:""}},[e("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:s.comparison_one,callback:function(e){t.$set(s,"comparison_one",e)},expression:"contentItem.comparison_one"}},t._l(t.comparisonList,(function(t,s){return e("el-option",{key:s,attrs:{label:t.label,value:t.value}})})),1)],1),e("div",{staticClass:"p-l-10"},[e("el-form-item",{attrs:{label:"",prop:"config."+a+".comparison_one_score",rules:{required:!0,message:"请输入",trigger:"blur"}}},[e("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"150px"},attrs:{disabled:t.disabled,"show-word-limit":""},model:{value:s.comparison_one_score,callback:function(e){t.$set(s,"comparison_one_score",e)},expression:"contentItem.comparison_one_score"}},[e("template",{slot:"append"},[t._v("%")])],2)],1)],1),e("div",{staticClass:"p-t-5 p-r-10"},[t._v("到")]),e("el-form-item",{attrs:{label:""}},[e("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:s.comparison_two,callback:function(e){t.$set(s,"comparison_two",e)},expression:"contentItem.comparison_two"}},t._l(t.comparisonList,(function(t,s){return e("el-option",{key:s,attrs:{label:t.label,value:t.value}})})),1)],1),e("div",{staticClass:"p-l-10"},[e("el-form-item",{attrs:{label:"",prop:"config."+a+".comparison_two_score",rules:{required:!0,message:"请输入",trigger:"blur"}}},[e("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"150px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:s.comparison_two_score,callback:function(e){t.$set(s,"comparison_two_score",e)},expression:"contentItem.comparison_two_score"}},[e("template",{slot:"append"},[t._v("%")])],2)],1)],1),e("el-form-item",{attrs:{label:"",prop:"config."+a+".operation",rules:{required:!0,message:"请选择",trigger:"blur"}}},[e("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:s.operation,callback:function(e){t.$set(s,"operation",e)},expression:"contentItem.operation"}},t._l(t.operationList,(function(t,s){return e("el-option",{key:s,attrs:{label:t.label,value:t.value}})})),1)],1),e("div",{staticClass:"p-l-10"},[e("el-form-item",{attrs:{label:"",prop:"config."+a+".operation_score",rules:{required:!0,message:"请选择",trigger:"blur"}}},[e("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"100px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:s.operation_score,callback:function(e){t.$set(s,"operation_score",e)},expression:"contentItem.operation_score"}})],1)],1),e("div",{staticClass:"p-t-5 p-r-10"},[t._v("分")]),e("div",{staticClass:"m-b-30"},[t.formData.config.length>1&&!t.disabled?e("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.removeRule(a)}}},[t._v(" 删除 ")]):t._e()],1)],1)})),0)])],2)],1)]),e("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[e("el-button",{staticStyle:{width:"120px"},on:{click:t.closeHandler}},[t._v("取消")]),e("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:t.submitHandler}},[t._v(" "+t._s("see"===t.type?"返回":"编辑")+" ")])],1)])],1)},o=[],i={props:{type:String,data:Object,disabled:Boolean},data:function(){return{isLoading:!1,formData:{score:"",tips:"",listText:[{text:"x BMI",tips:"（此值代表人体BMI）"}],config:[{comparison_one:"",comparison_one_score:"",comparison_two:"",comparison_two_score:"",operation:"",operation_score:""}]},formRuls:{},comparisonList:[{label:"等于",value:"=="},{label:"大于",value:">"},{label:"大于等于",value:">="},{label:"小于",value:"<"},{label:"小于等于",value:"<="},{label:"不限制",value:""}],operationList:[{label:"加",value:"+"},{label:"减",value:"-"}]}},created:function(){this.formData=this.data},mounted:function(){},methods:{addRule:function(){this.formData.config.push({comparison_one:"",comparison_one_score:"",comparison_two:"",comparison_two_score:"",operation:"",operation_score:""})},removeRule:function(t){this.formData.config.splice(t,1)},submitHandler:function(){var t=this;this.$refs.formRef.validate((function(e){if(e){if(t.isLoading)return t.$message.error("请勿重复提交！");var s={key:t.formData.type};s[t.formData.type]={score:t.formData.score,tips:t.formData.tips,config:t.formData.config},t.$emit("submitHandler",s)}}))},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,s,a){"confirm"===e?t.$closeCurrentTab(t.$route.path):s.confirmButtonLoading||a()}}).then((function(t){})).catch((function(t){}))}}},l=i,r=(s("b611"),s("2877")),n=Object(r["a"])(l,a,o,!1,null,"5a446438",null);e["default"]=n.exports},b611:function(t,e,s){"use strict";s("34bf")}}]);