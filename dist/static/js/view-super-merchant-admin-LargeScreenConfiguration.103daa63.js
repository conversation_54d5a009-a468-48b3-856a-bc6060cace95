(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-merchant-admin-LargeScreenConfiguration"],{"15de":function(e,t,r){"use strict";r("9d30")},"9cb9":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"container-wrapper screen-config"},[t("refresh-tool",{on:{refreshPage:e.initLoad}}),t("div",{staticClass:"table-wrapper"},[t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[e._v("数据列表")]),t("div",{staticClass:"align-r"},[t("el-button",{staticClass:"ps-origin-btn",attrs:{size:"mini"},on:{click:function(t){return e.showScreenDrawer("add")}}},[e._v(" 新增 ")])],1)]),t("div",{staticClass:"table-content"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},e._l(e.tableSetting,(function(r,n){return t("table-column",{key:n,attrs:{col:r},scopedSlots:e._u([{key:"imgList",fn:function(r){var n=r.row;return[t("div",{staticClass:"ps-origin pointer",on:{click:function(t){return e.viewPicDetail(n.img_list)}}},[e._v("查看")])]}},{key:"defaultType",fn:function(r){var n=r.row;return[t("div",[e._v(e._s(n.default_type?"是":"否"))])]}},{key:"channel",fn:function(r){var n=r.row;return[t("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small",disabled:n.default_type},on:{click:function(t){return e.getScreenChannel(n)}}},[e._v("配置")])]}},{key:"status",fn:function(r){var n=r.row;return[t("el-switch",{on:{change:function(t){return e.changeShowType(n)}},model:{value:n.show_type,callback:function(t){e.$set(n,"show_type",t)},expression:"row.show_type"}})]}},{key:"operation",fn:function(r){var n=r.row;return[t("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.showScreenDrawer("edit",n)}}},[e._v("编辑")]),t("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.deleteHandle(n)}}},[e._v("删除")])]}}],null,!0)})})),1)],1)]),t("div",{staticClass:"ps-el-drawer"},[t("el-drawer",{attrs:{title:"add"===e.drawerType?"新增数据大屏":"编辑数据大屏",visible:e.screenDrawerShow,"show-close":!1,size:"40%"}},[t("div",{staticClass:"p-20"},[t("el-form",{ref:"screenFormRef",attrs:{model:e.screenForm,"label-width":"80px","label-position":"right"}},[t("el-form-item",{attrs:{label:"大屏名称",prop:"name",rules:[{required:!0,message:"请输入大屏名称",trigger:["change","blur"]}]}},[t("el-input",{staticClass:"w-300",attrs:{placeholder:"请输入大屏名称，不超过15个字",maxlength:"30"},model:{value:e.screenForm.name,callback:function(t){e.$set(e.screenForm,"name",t)},expression:"screenForm.name"}})],1),t("el-form-item",{attrs:{label:"链接地址",prop:"address",rules:[{required:!0,message:"请输入链接地址",trigger:["change","blur"]}]}},[t("el-input",{staticClass:"w-300",attrs:{type:"textarea",placeholder:"请输入大屏链接地址，不超过100个字",maxlength:"100",autosize:{minRows:6,maxRows:8},resize:"none"},model:{value:e.screenForm.address,callback:function(t){e.$set(e.screenForm,"address",t)},expression:"screenForm.address"}})],1),t("el-form-item",{attrs:{label:"展示图片",prop:"fileLists",rules:[{required:!0,message:"请上传图片",trigger:["change","blur"]}]}},[t("div",{staticClass:"img-from"},[t("span",{staticClass:"tips ps-red"},[e._v("支持png、jpg格式，图片大小不能超过2MB")]),t("file-upload",{ref:"pictureRef",staticClass:"avatar-uploader",attrs:{fileList:e.screenForm.fileLists,type:"enclosure","before-upload":e.beforeUpload,"show-file-list":!1},on:{fileLists:e.getFileLists}},[e.screenForm.fileLists.length?t("img",{staticClass:"avatar",attrs:{src:e.screenForm.fileLists[0].url},on:{click:e.clearFileHandle}}):t("span",{staticClass:"icon-tag"},[t("img",{attrs:{src:r("1d89")}}),t("div",{},[e._v("上传图片")])])])],1)]),t("el-form-item",{attrs:{label:"是否默认",prop:"isDefault"}},[t("div",{staticClass:"ps-flex-align-c flex-align-c"},[t("el-switch",{model:{value:e.screenForm.isDefault,callback:function(t){e.$set(e.screenForm,"isDefault",t)},expression:"screenForm.isDefault"}}),t("div",{staticClass:"m-l-10 font-size-14",staticStyle:{color:"#909399"}},[e._v("开启为默认状态，所有渠道默认启用该大屏。")])],1)]),t("el-form-item",{attrs:{label:"备注",prop:"remark"}},[t("el-input",{staticClass:"w-300",attrs:{type:"textarea",placeholder:"请填写备注信息，例如：通用大屏/某渠道专用等。不超过100个字",maxlength:"100",autosize:{minRows:6,maxRows:8},resize:"none"},model:{value:e.screenForm.remark,callback:function(t){e.$set(e.screenForm,"remark",t)},expression:"screenForm.remark"}})],1)],1),t("div",{staticClass:"ps-el-drawer-footer"},[t("el-button",{staticClass:"w-100",attrs:{size:"small"},on:{click:function(t){return e.cancelHandle("addAndEdit")}}},[e._v("取消")]),t("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:function(t){return e.saveHandle("addAndEdit")}}},[e._v("保存")])],1)],1)]),t("el-drawer",{attrs:{title:"适用渠道",visible:e.applyDrawerShow,"show-close":!1,size:"40%"}},[t("div",{staticClass:"p-20"},[t("el-form",{ref:"applyFormRef",attrs:{model:e.applyForm,"label-width":"80px","label-position":"right"}},[t("el-form-item",{attrs:{label:"大屏名称",prop:"name"}},[t("div",[e._v(e._s(e.applyForm.name))])]),t("el-form-item",{attrs:{label:"项目地址",prop:"address"}},[t("el-radio-group",{on:{input:e.changIsOpenStatus},model:{value:e.isSelectAll,callback:function(t){e.isSelectAll=t},expression:"isSelectAll"}},[t("el-radio",{attrs:{label:!0}},[e._v("全部适用")]),t("el-radio",{attrs:{label:!1}},[e._v("全部不适用")])],1),t("div",[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],attrs:{"show-header":!1,data:e.applyForm.data,stripe:"","header-row-class-name":"ps-table-header-row","row-key":"id",lazy:!0,load:e.loadTree,"tree-props":e.checkProps}},e._l(e.applyFormTableSetting,(function(r,n){return t("table-column",{key:n,attrs:{col:r},scopedSlots:e._u([{key:"operation",fn:function(r){var n=r.row;return[t("el-radio-group",{on:{input:function(t){return e.changeStatus(n)}},model:{value:n.isOpen,callback:function(t){e.$set(n,"isOpen",t)},expression:"row.isOpen"}},[t("el-radio",{attrs:{label:!0}},[e._v("启用")]),t("el-radio",{attrs:{label:!1}},[e._v("不适用")])],1)]}}],null,!0)})})),1)],1)],1)],1),t("div",{staticClass:"ps-el-drawer-footer"},[t("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:function(t){return e.cancelHandle("apply")}}},[e._v("取消")]),t("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:function(t){return e.saveHandle("apply")}}},[e._v("保存")])],1)],1)])],1),t("image-view-preview",{attrs:{isshow:e.isShowPreViewDialog,title:e.dialogTitle,picList:e.imgUrlList},on:{"update:isshow":function(t){e.isShowPreViewDialog=t},close:e.closePreviewDialog}})],1)},a=[],i=r("ed08"),s=r("74d4");function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function l(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */l=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},s=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function d(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(e){d=function(e,t,r){return e[t]=r}}function p(e,t,r,n){var i=t&&t.prototype instanceof w?t:w,s=Object.create(i.prototype),o=new E(n||[]);return a(s,"_invoke",{value:D(e,r,o)}),s}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=p;var h="suspendedStart",m="suspendedYield",g="executing",y="completed",v={};function w(){}function b(){}function S(){}var _={};d(_,s,(function(){return this}));var k=Object.getPrototypeOf,F=k&&k(k(P([])));F&&F!==r&&n.call(F,s)&&(_=F);var L=S.prototype=w.prototype=Object.create(_);function x(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function C(e,t){function r(a,i,s,l){var c=f(e[a],e,i);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==o(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,s,l)}),(function(e){r("throw",e,s,l)})):t.resolve(d).then((function(e){u.value=e,s(u)}),(function(e){return r("throw",e,s,l)}))}l(c.arg)}var i;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){r(e,n,t,a)}))}return i=i?i.then(a,a):a()}})}function D(t,r,n){var a=h;return function(i,s){if(a===g)throw Error("Generator is already running");if(a===y){if("throw"===i)throw s;return{value:e,done:!0}}for(n.method=i,n.arg=s;;){var o=n.delegate;if(o){var l=$(o,n);if(l){if(l===v)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===h)throw a=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=g;var c=f(t,r,n);if("normal"===c.type){if(a=n.done?y:m,c.arg===v)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(a=y,n.method="throw",n.arg=c.arg)}}}function $(t,r){var n=r.method,a=t.iterator[n];if(a===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,$(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=f(a,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var s=i.arg;return s?s.done?(r[t.resultName]=s.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,v):s:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function O(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function A(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function E(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(O,this),this.reset(!0)}function P(t){if(t||""===t){var r=t[s];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,i=function r(){for(;++a<t.length;)if(n.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(o(t)+" is not iterable")}return b.prototype=S,a(L,"constructor",{value:S,configurable:!0}),a(S,"constructor",{value:b,configurable:!0}),b.displayName=d(S,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,S):(e.__proto__=S,d(e,u,"GeneratorFunction")),e.prototype=Object.create(L),e},t.awrap=function(e){return{__await:e}},x(C.prototype),d(C.prototype,c,(function(){return this})),t.AsyncIterator=C,t.async=function(e,r,n,a,i){void 0===i&&(i=Promise);var s=new C(p(e,r,n,a),i);return t.isGeneratorFunction(r)?s:s.next().then((function(e){return e.done?e.value:s.next()}))},x(L),d(L,u,"Generator"),d(L,s,(function(){return this})),d(L,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=P,E.prototype={constructor:E,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(A),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(n,a){return o.type="throw",o.arg=t,r.next=n,a&&(r.method="next",r.arg=e),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var s=this.tryEntries[i],o=s.completion;if("root"===s.tryLoc)return a("end");if(s.tryLoc<=this.prev){var l=n.call(s,"catchLoc"),c=n.call(s,"finallyLoc");if(l&&c){if(this.prev<s.catchLoc)return a(s.catchLoc,!0);if(this.prev<s.finallyLoc)return a(s.finallyLoc)}else if(l){if(this.prev<s.catchLoc)return a(s.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<s.finallyLoc)return a(s.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var s=i?i.completion:{};return s.type=e,s.arg=t,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(s)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),A(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;A(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:P(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),v}},t}function c(e,t){return p(e)||d(e,t)||y(e,t)||u()}function u(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,i,s,o=[],l=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(o.push(n.value),o.length!==t);l=!0);}catch(e){c=!0,a=e}finally{try{if(!l&&null!=r.return&&(s=r.return(),Object(s)!==s))return}finally{if(c)throw a}}return o}}function p(e){if(Array.isArray(e))return e}function f(e,t,r,n,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void r(e)}o.done?t(l):Promise.resolve(l).then(n,a)}function h(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function s(e){f(i,n,a,s,o,"next",e)}function o(e){f(i,n,a,s,o,"throw",e)}s(void 0)}))}}function m(e){return w(e)||v(e)||y(e)||g()}function g(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function y(e,t){if(e){if("string"==typeof e)return b(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?b(e,t):void 0}}function v(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function w(e){if(Array.isArray(e))return b(e)}function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var S={data:function(){return{isLoading:!1,tableData:[],tableSetting:[{label:"大屏名称",key:"name"},{label:"地址",key:"link_address"},{label:"展示图片",key:"imgList",type:"slot",slotName:"imgList"},{label:"是否默认",key:"default_type",type:"slot",slotName:"defaultType"},{label:"适用渠道",key:"scree_channel",type:"slot",slotName:"channel"},{label:"可见状态（启用/禁用）",key:"show_type",type:"slot",slotName:"status"},{label:"备注",key:"remark"},{label:"操作时间",key:"update_time"},{label:"操作人",key:"operator_name"},{label:"操作",key:"operation",type:"slot",slotName:"operation"}],drawerType:"",selectId:"",screenDrawerShow:!1,screenForm:{name:"",address:"",isDefault:!0,remark:"",fileLists:[]},isSelectAll:"",applyDrawerShow:!1,screenChannel:[],applyForm:{id:"",name:"",data:[]},applyFormTableSetting:[{label:"操作人",key:"name",align:"left"},{label:"操作",key:"operation",type:"slot",slotName:"operation"}],checkProps:{children:"children",hasChildren:"has_children"},imgUrlList:[],isShowPreViewDialog:!1,dialogTitle:""}},components:{ImageViewPreview:s["a"]},created:function(){this.initLoad()},watch:{applyDrawerShow:function(e){var t=this;e&&this.applyForm.data.forEach((function(e){t.screenChannel.includes(e.id)?e.isOpen=!0:e.isOpen=!1}))},applyForm:{handler:function(e){var t=e.data.map((function(e){return e.isOpen})),r=m(new Set(t));2===r.length?this.isSelectAll="":1===r.length&&r[0]?this.isSelectAll=!0:this.isSelectAll=!1},deep:!0}},methods:{initLoad:function(){this.getScreenData(),this.getChannelList()},getScreenData:function(){var e=this;this.$apis.apiBackgroundFundSupervisionMonitoringScreenListPost().then((function(t){0===t.code?e.tableData=Object(i["f"])(t.data.results||[]):e.$message.error(t.msg)}))},getChannelList:function(e,t,r){var n=this,a={status__in:["enable"]};t?a.parent__in=t:a.parent__is_null="1",e&&(a.name__contains=e),this.$apis.apiBackgroundFundSupervisionSupervisionChannelListPost(a).then((function(e){0===e.code?n.applyForm.data=e.data.results.map((function(e){return Object.assign(e,{isOpen:!1}),e})):n.$message.error(e.msg)}))},loadTree:function(e,t,r){var n=this;return h(l().mark((function t(){var a,s,o,u,d,p;return l().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a={status__in:["enable"],page:1,page_size:99999},e&&e.id?a.parent__in=e.id:a.parent__is_null="1",t.next=4,Object(i["Z"])(n.$apis.apiBackgroundFundSupervisionSupervisionChannelListPost(a));case 4:if(s=t.sent,o=c(s,2),u=o[0],d=o[1],!u){t.next=12;break}return r([]),n.$message.error(u.message),t.abrupt("return");case 12:0===d.code?(p=d.data.results.map((function(e){return Object.assign(e,{isOpen:!1}),n.screenChannel.includes(e.id)?e.isOpen=!0:e.isOpen=!1,e})),r(p)):(r([]),n.$message.error(d.msg));case 13:case"end":return t.stop()}}),t)})))()},showScreenDrawer:function(e,t){var r=this;this.drawerType=e,"edit"===e?(this.selectId=t.id,this.screenForm.name=t.name,this.screenForm.address=t.link_address,this.screenForm.isDefault=t.default_type,this.screenForm.remark=t.remark,this.screenForm.fileLists=t.img_list||[]):(this.selectId="",this.screenForm.name="",this.screenForm.address="",this.screenForm.isDefault=!0,this.screenForm.remark="",this.screenForm.fileLists=[]),this.screenDrawerShow=!0,setTimeout((function(){r.$refs.screenFormRef.clearValidate()}),100)},cancelHandle:function(e){"addAndEdit"===e?(this.$refs.screenFormRef.resetFields(),this.screenDrawerShow=!1):this.applyDrawerShow=!1},saveHandle:function(e){"addAndEdit"===e?this.saveScreenHandle():this.saveApplyHandle()},saveApplyHandle:function(){var e=this,t={supervision_channel_list:this.screenChannel,monitoring_screen_id:this.applyForm.id,is_all_bind:"boolean"===typeof this.isSelectAll&&this.isSelectAll};this.$apis.apiBackgroundFundSupervisionMonitoringScreenScreeChannelConfigPost(t).then((function(t){0===t.code?e.$message.success("保存成功"):e.$message.error(t.msg),e.applyDrawerShow=!1,e.getScreenData()}))},saveScreenHandle:function(){var e=this;this.$refs.screenFormRef.validate((function(t){if(t){var r={id:"add"===e.drawerType?void 0:e.selectId,name:e.screenForm.name,link_address:e.screenForm.address,default_type:e.screenForm.isDefault,img_list:e.screenForm.fileLists,remark:e.screenForm.remark};"add"===e.drawerType?e.addScreenHandle(r):e.editScreenHandle(r),e.cancelHandle("addAndEdit")}}))},addScreenHandle:function(e){var t=this;this.$apis.apiBackgroundFundSupervisionMonitoringScreenAddPost(e).then((function(e){0===e.code?(t.$message.success("新增成功"),t.getScreenData()):t.$message.error(e.msg),t.$refs.screenFormRef.resetFields(),t.screenDrawerShow=!1}))},editScreenHandle:function(e){var t=this;this.$apis.apiBackgroundFundSupervisionMonitoringScreenModifyPost(e).then((function(e){0===e.code?(t.$message.success("修改成功"),t.getScreenData()):t.$message.error(e.msg),t.$refs.screenFormRef.resetFields(),t.screenDrawerShow=!1}))},deleteHandle:function(e){var t=this;this.$confirm("您正在删除数据大屏：".concat(e.name,"。删除后不可恢复，请谨慎操作。确定要删除该大屏？"),"提示",{distinguishCancelAndClose:!0,confirmButtonText:"确定",cancelButtonText:"取消"}).then((function(){t.$apis.apiBackgroundFundSupervisionMonitoringScreenDeletePost({ids:[e.id]}).then((function(e){0===e.code?t.$message.success("删除成功"):t.$message.error(e.msg),t.getScreenData()}))})).catch((function(e){t.$message("已取消删除")}))},changeShowType:function(e){var t=this;this.$apis.apiBackgroundFundSupervisionMonitoringScreenModifyPost({id:e.id,name:e.name,show_type:e.show_type}).then((function(e){0===e.code?(t.$message.success("修改成功"),t.getScreenData()):t.$message.error(e.msg)}))},getScreenChannel:function(e){this.applyForm.name=e.name,this.applyForm.id=e.id,this.screenChannel=Object(i["f"])(e.config_channel_screen),this.applyDrawerShow=!0},changeStatus:function(e){if(e.isOpen)this.screenChannel.push(e.id);else{var t=this.screenChannel.indexOf(e.id);this.screenChannel.splice(t,1)}this.screenChannel=m(new Set(this.screenChannel))},changIsOpenStatus:function(){var e=this;this.applyForm.data.forEach((function(t){e.isSelectAll?t.isOpen=!0:t.isOpen=!1}))},getFileLists:function(e){this.screenForm.fileLists=e},beforeUpload:function(e){var t=[".jpg",".png"];if(!t.includes(Object(i["A"])(e.name)))return this.$message.error("上传图片只能是 jpg/png 格式"),!1;var r=e.size/1024/1024<2;return r?void 0:(this.$message.error("上传图片大小不能超过 2MB!"),!1)},clearFileHandle:function(){this.$refs.pictureRef.clearHandle(),this.screenForm.fileLists=[]},viewPicDetail:function(e){var t=e&&Array.isArray(e)&&e.length>0?e[0].url:"";if(this.dialogTitle="查看详情",t&&t.length>0){var r=t;this.$set(this,"imgUrlList",[r]),this.isShowPreViewDialog=!0}else this.$message.error("亲，没有图片喔！")},closePreviewDialog:function(){this.isShowPreViewDialog=!1}}},_=S,k=(r("15de"),r("2877")),F=Object(k["a"])(_,n,a,!1,null,null,null);t["default"]=F.exports},"9d30":function(e,t,r){}}]);