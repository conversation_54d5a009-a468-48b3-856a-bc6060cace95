(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-merchant-admin-components-AddFoodDrawer","view-super-health-system-health-nutrition-AddMerchantCommodityToSuper","view-super-health-system-health-nutrition-IngredientsCategory","view-super-health-system-health-nutrition-constants"],{"015b":function(e,t,n){"use strict";n.r(t),n.d(t,"DEFAULT_NUTRITION",(function(){return i})),n.d(t,"ELEMENT_NUTRITION",(function(){return a})),n.d(t,"VITAMIN_NUTRITION",(function(){return o})),n.d(t,"NUTRITION_LIST",(function(){return l})),n.d(t,"LIBRARY_SEARCH_SETTING_SUPER",(function(){return s})),n.d(t,"LIBRARY_SEARCH_SETTING_MERCHANT",(function(){return u})),n.d(t,"LIBRARY_SEARCH_SETTING_CATEORY",(function(){return c})),n.d(t,"COMMODITY_SEARCH_SETTING_SUPER",(function(){return d})),n.d(t,"COMMODITY_SEARCH_SETTING_MERCHANT",(function(){return p}));var r=n("ed08"),i=[{name:"千卡",key:"energy_kcal",unit:"Kcal",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{name:"胆固醇",key:"cholesterol",unit:"mg",type:"default"},{name:"膳食纤维",key:"dietary_fiber",unit:"g",type:"default"}],a=[{key:"Ca",name:"钙",unit:"mg",type:"element"},{key:"P",name:"磷",unit:"mg",type:"element"},{key:"K",name:"钾",unit:"mg",type:"element"},{key:"Na",name:"钠",unit:"mg",type:"element"},{name:"镁",key:"Mg",unit:"mg",type:"element"},{key:"Fe",name:"铁",unit:"mg",type:"element"},{key:"I",name:"碘",unit:"μg",type:"element"},{key:"Se",name:"硒",unit:"μg",type:"element"},{key:"Zn",name:"锌",unit:"mg",type:"element"},{key:"Cu",name:"铜",unit:"mg",type:"element"},{key:"F",name:"氟",unit:"mg",type:"element"},{key:"Cr",name:"铬",unit:"μg",type:"element"},{key:"Mo",name:"钼",unit:"μg",type:"element"},{key:"Mn",name:"锰",unit:"mg",type:"element"}],o=[{key:"VA",name:"维生素A",unit:"μg",type:"vitamin"},{key:"VD",name:"维生素D",unit:"μg",type:"vitamin"},{key:"VE",name:"维生素E",unit:"mg",type:"vitamin"},{key:"VK",name:"维生素K",unit:"μg",type:"vitamin"},{key:"VB1",name:"维生素B1",unit:"mg",type:"vitamin"},{key:"VB2",name:"维生素B2",unit:"mg",type:"vitamin"},{key:"VB6",name:"维生素B6",unit:"mg",type:"vitamin"},{key:"VB12",name:"维生素B12",unit:"μg",type:"vitamin"},{key:"VC",name:"维生素C",unit:"mg",type:"vitamin"},{key:"VB5",name:"泛酸",unit:"mg",type:"vitamin"},{key:"VM",name:"叶酸",unit:"μg",type:"vitamin"},{key:"VB3",name:"烟酸",unit:"mg",type:"vitamin"},{key:"Choline",name:" 胆碱",unit:"mg",type:"vitamin"},{key:"Nicotinamide",name:"烟酰胺",unit:"mg",type:"vitamin"},{key:"VH",name:"生物素",unit:"mg",type:"vitamin"}],l=[].concat(i,a,o),s={date_type:{type:"select",label:"",value:1,maxWidth:"130px",placeholder:"请选择",dataList:[{label:"创建时间",value:1},{label:"修改时间",value:2}]},select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"",clearable:!0,value:[]},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(e){return{id:e.level+"_"+e.id,label:e.name,children:e.sort_list}}},is_enable_nutrition:{type:"select",label:"营养信息",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"有",value:"1"},{label:"无",value:"0"}]},ingredient_name:{type:"input",label:"食材名称",value:"",placeholder:"请输入食材名称"},operator:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"},label_filter:{type:"select",label:"",value:"",placeholder:"",dataList:[{label:"请选择",value:"select",disabled:!0},{label:"包含",value:"Include"},{label:"不包含",value:"Exclude"}]},label_list:{type:"treeselect",label:"",value:[],placeholder:"选择标签",multiple:!0,limit:1,level:1,valueConsistsOf:"LEAF_PRIORITY",normalizer:function(e){return{id:e.id,label:e.name,children:e.label_list}},dataList:[]},is_entering:{type:"select",label:"录入图片",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"已录入",value:"1"},{label:"未录入",value:"0"}]}},u={select_time:{type:"datetimerange",label:"创建时间",format:"yyyy-MM-dd",value:Object(r["y"])(7)},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(e){return{id:e.level+"_"+e.id,label:e.name,children:e.sort_list}}},is_repeat:{type:"select",label:"已有食材",value:"",placeholder:"请选择是否已有食材",dataList:[{label:"是",value:1},{label:"否",value:0}]},is_enable_nutrition:{type:"select",label:"营养信息",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"有",value:"1"},{label:"无",value:"0"}]},ingredient_name:{type:"input",label:"食材名称",value:"",placeholder:"请输入食材名称"},is_entering:{type:"select",label:"录入图片",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"已录入",value:"1"},{label:"未录入",value:"0"}]}},c={select_time:{type:"datetimerange",label:"修改时间",format:"yyyy-MM-dd HH:mm:ss",value:[]},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(e){return{id:e.level+"_"+e.id,label:e.name,children:e.sort_list}}},user_name:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"}},d={date_type:{type:"select",label:"",value:1,maxWidth:"130px",placeholder:"请选择",dataList:[{label:"创建时间",value:1},{label:"修改时间",value:2}]},select_time:{type:"datetimerange",label:"",format:"yyyy-MM-dd HH:mm:ss",value:[]},food_name:{type:"input",label:"菜品/商品名称",value:"",placeholder:"请输入菜品/商品名称"},user_name:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"},attributes:{type:"select",label:"属性",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"商品",value:"goods"},{label:"菜品",value:"foods"}]},label_filter:{type:"select",label:"",value:"",placeholder:"",dataList:[{label:"请选择",value:"select",disabled:!0},{label:"包含",value:"Include"},{label:"不包含",value:"Exclude"}]},label_list:{type:"treeselect",label:"",value:[],placeholder:"选择标签",multiple:!0,limit:1,level:1,valueConsistsOf:"LEAF_PRIORITY",normalizer:function(e){return{id:e.id,label:e.name,children:e.label_list}},dataList:[]}},p={select_time:{type:"datetimerange",label:"创建时间",format:"yyyy-MM-dd",value:Object(r["y"])(7)},food_name:{type:"input",label:"菜品/商品名称",value:"",placeholder:"请输入菜品/商品名称"},is_repeat:{type:"select",label:"已有菜品/商品",value:"",placeholder:"请选择",dataList:[{label:"是",value:1},{label:"否",value:0}]},attributes:{type:"select",label:"属性",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"商品",value:"goods"},{label:"菜品",value:"foods"}]}}},"3fa5":function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));n("9e1f"),n("450d");var r=n("6ed5"),i=n.n(r);function a(e,t){return new Promise((function(n,r){i.a.confirm(e.content?e.content:"",e.title?e.title:"提示",{dangerouslyUseHTMLString:!0|e.useHTML,distinguishCancelAndClose:!0,closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:e.cancel_class?e.cancel_class:"ps-cancel-btn",confirmButtonClass:e.confirm_class?e.confirm_class:"ps-btn",confirmButtonText:e.confirmButtonText,cancelButtonText:e.cancelButtonText,center:""===e.center||e.center}).then((function(e){t?n(t()):n()})).catch((function(e){r(e)}))}))}},"7aba":function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e._self._c;return t("el-drawer",e._g(e._b({staticClass:"drawer-wrapper",attrs:{title:"新建菜品",visible:e.showDrawer,direction:e.direction,wrapperClosable:e.wrapperClosable},on:{"update:visible":function(t){e.showDrawer=t}}},"el-drawer",e.$attrs,!1),e.$listeners),[t("div",{staticClass:"add-food-drawer-box"},[t("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"foodRef",staticClass:"AddFoodDrawer",attrs:{rules:e.formRuls,model:e.formData,"label-width":"80px",size:"small"}},[t("el-form-item",{staticClass:"form-content-flex",attrs:{label:"菜品名称",prop:"name"}},[t("el-input",{staticClass:"ps-input",staticStyle:{width:"50%"},attrs:{placeholder:"请输入菜品名称"},model:{value:e.formData.name,callback:function(t){e.$set(e.formData,"name",t)},expression:"formData.name"}})],1),t("el-form-item",{attrs:{label:"分类",prop:"categoryId"}},[t("el-select",{staticClass:"ps-select",staticStyle:{width:"50%"},attrs:{placeholder:"请下拉选择分类","popper-class":"ps-popper-select","collapse-tags":"",filterable:"",clearable:"","popper-append-to-body":!1},model:{value:e.formData.categoryId,callback:function(t){e.$set(e.formData,"categoryId",t)},expression:"formData.categoryId"}},e._l(e.foodCategoryList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),t("el-form-item",{staticClass:"upload-block-label upload-hidden",attrs:{label:"图片",prop:"foodImages"}},[t("div",{staticClass:"inline-block upload-w"},[t("el-upload",{ref:"fileUpload",class:{"file-upload":!0,"hide-upload":e.formData.foodImagesList.length>0},attrs:{drag:"",action:e.serverUrl,data:e.uploadParams,"file-list":e.formData.foodImagesList,"on-success":e.handleFoodImgSuccess,"on-change":e.handelChange,"before-upload":e.beforeFoodImgUpload,limit:1,"list-type":"picture-card",multiple:!1,headers:e.headersOpts,accept:".jpeg,.jpg,.png,.bmp"},scopedSlots:e._u([{key:"file",fn:function(n){var r=n.file;return t("div",{directives:[{name:"loading",rawName:"v-loading",value:"uploading"===r.status,expression:"file.status==='uploading'"}],attrs:{"element-loading-text":"上传中"}},[t("div",{staticClass:"upload-food-img"},[t("el-image",{staticClass:"el-upload-dragger",attrs:{src:r.url,fit:"contain"}})],1),t("span",{staticClass:"el-upload-list__item-actions"},[t("span",{staticClass:"el-upload-list__item-preview",on:{click:function(t){return e.handlePictureCardPreview(r)}}},[t("i",{staticClass:"el-icon-zoom-in"})]),t("span",{staticClass:"el-upload-list__item-delete",on:{click:function(t){return e.handleImgRemove(r,"foodImages")}}},[t("i",{staticClass:"el-icon-delete"})])])])}}])},[e.fileLists.length<1?t("div",{staticClass:"upload-t"},[t("i",{staticClass:"el-icon-circle-plus"}),t("div",{staticClass:"el-upload__text"},[e._v(" 上传菜品/商品图片 ")])]):e._e()])],1),t("div",{staticClass:"inline-block upload-tips"},[e._v(" 上传：菜品/商品图片。"),t("br"),e._v(" 建议图片需清晰，图片内容与名称相符。"),t("br"),e._v(" 仅支持jpg、png、bmp格式，大小不超过5M ")])]),t("el-form-item",{attrs:{label:"食材占比"}},[t("div",{staticClass:"table-title m-b-6"},[e._v(" 食材占比 "),t("span",{staticClass:"tip-o-7"},[e._v("（菜品每100g所含食材占比，相加必须等于100%）")]),t("el-button",{staticClass:"ps-btn",staticStyle:{"margin-right":"15px"},attrs:{type:"primary",size:"small",icon:"el-icon-plus"},on:{click:e.addIngredients}},[e._v("添加")])],1),t("div",{class:[e.errorMsg.percentageError?"error-border":""]},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.formData.ingredientList,size:"mini","header-row-class-name":"ps-table-header-row"}},[t("el-table-column",{attrs:{prop:"no",label:"食材",align:"center"},scopedSlots:e._u([{key:"default",fn:function(n){return[t("VirtualListSelect",{attrs:{"collapse-tags":"",clearable:"",filterable:"",selectData:n.row.selectFoodIngredient},on:{change:e.changeIngredient},model:{value:n.row.selectId,callback:function(t){e.$set(n.row,"selectId",t)},expression:"scope.row.selectId"}})]}}])}),t("el-table-column",{attrs:{prop:"id",label:"占比（%）",align:"center","min-width":"120px"},scopedSlots:e._u([{key:"default",fn:function(n){return[t("div",{staticClass:"cantent ps-flex-align-c flex-align-c"},[t("el-slider",{staticClass:"cantent",attrs:{"show-input":"","input-size":"mini"},on:{change:e.changePercentage},model:{value:n.row.percentage,callback:function(t){e.$set(n.row,"percentage",t)},expression:"scope.row.percentage"}}),e._v("% ")],1)]}}])}),t("el-table-column",{attrs:{prop:"",label:"操作",align:"center",width:"100px"},scopedSlots:e._u([{key:"default",fn:function(n){return[t("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.deleteIngredientHandle(n.row.index)}}},[e._v("删除")])]}}])})],1)],1),e.errorMsg.percentageError?t("div",{staticStyle:{color:"red",padding:"20px"}},[e._v(e._s(e.errorMsg.percentageError))]):e._e()]),t("el-form-item",{attrs:{label:"营养信息"}},[e._l(e.currentNutritionList,(function(n){return[t("div",{key:n.key,staticClass:"inline-block nutrition-item"},[t("span",{staticClass:"inline-block nutrition-label m-r-6 ellipsis"},[e._v(e._s(n.name+"："))]),t("span",{staticClass:"inline-block nutrition-value ellipsis"},[e._v(e._s(e.formData[n.key]?e.formData[n.key]:0))]),t("span",{staticClass:"inline-block nutrition-unit"},[e._v(e._s(n.unit))])])]})),t("div",{staticClass:"pointer"},[t("span",{staticStyle:{color:"#027DB4","text-decoration":"underline"},on:{click:function(t){e.showAll=!e.showAll}}},[e._v(e._s(e.showAll?"收起":"点击查看更多营养信息"))])])],2)],1),t("div",{staticClass:"footer m-l-46"},[t("el-button",{staticStyle:{width:"120px"},attrs:{disabled:e.isLoading},on:{click:e.closeHandler}},[e._v("取消")]),t("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary",disabled:e.isLoading},on:{click:e.submitHandler}},[e._v("保存")])],1)],1),t("el-dialog",{attrs:{visible:e.dialogVisible},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("img",{attrs:{width:"100%",src:e.dialogImageUrl,alt:""}})])],1)},i=[],a=n("ed08"),o=n("015b"),l=n("da92"),s=(n("3fa5"),n("1fe1"));function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}function c(e,t){return g(e)||m(e,t)||p(e,t)||d()}function d(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function p(e,t){if(e){if("string"==typeof e)return f(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?f(e,t):void 0}}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function m(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,a,o,l=[],s=!0,u=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=a.call(n)).done)&&(l.push(r.value),l.length!==t);s=!0);}catch(e){u=!0,i=e}finally{try{if(!s&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(u)throw i}}return l}}function g(e){if(Array.isArray(e))return e}function h(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */h=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(e,t,n){e[t]=n.value},a="function"==typeof Symbol?Symbol:{},o=a.iterator||"@@iterator",l=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function d(e,t,n,r){var a=t&&t.prototype instanceof b?t:b,o=Object.create(a.prototype),l=new T(r||[]);return i(o,"_invoke",{value:E(e,n,l)}),o}function p(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var f="suspendedStart",m="suspendedYield",g="executing",y="completed",v={};function b(){}function _(){}function I(){}var w={};c(w,o,(function(){return this}));var L=Object.getPrototypeOf,k=L&&L(L(A([])));k&&k!==n&&r.call(k,o)&&(w=k);var x=I.prototype=b.prototype=Object.create(w);function D(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function C(e,t){function n(i,a,o,l){var s=p(e[i],e,a);if("throw"!==s.type){var c=s.arg,d=c.value;return d&&"object"==u(d)&&r.call(d,"__await")?t.resolve(d.__await).then((function(e){n("next",e,o,l)}),(function(e){n("throw",e,o,l)})):t.resolve(d).then((function(e){c.value=e,o(c)}),(function(e){return n("throw",e,o,l)}))}l(s.arg)}var a;i(this,"_invoke",{value:function(e,r){function i(){return new t((function(t,i){n(e,r,t,i)}))}return a=a?a.then(i,i):i()}})}function E(t,n,r){var i=f;return function(a,o){if(i===g)throw Error("Generator is already running");if(i===y){if("throw"===a)throw o;return{value:e,done:!0}}for(r.method=a,r.arg=o;;){var l=r.delegate;if(l){var s=O(l,r);if(s){if(s===v)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===f)throw i=y,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=g;var u=p(t,n,r);if("normal"===u.type){if(i=r.done?y:m,u.arg===v)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(i=y,r.method="throw",r.arg=u.arg)}}}function O(t,n){var r=n.method,i=t.iterator[r];if(i===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,O(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var a=p(i,t.iterator,n.arg);if("throw"===a.type)return n.method="throw",n.arg=a.arg,n.delegate=null,v;var o=a.arg;return o?o.done?(n[t.resultName]=o.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,v):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function S(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function N(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function T(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(S,this),this.reset(!0)}function A(t){if(t||""===t){var n=t[o];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,a=function n(){for(;++i<t.length;)if(r.call(t,i))return n.value=t[i],n.done=!1,n;return n.value=e,n.done=!0,n};return a.next=a}}throw new TypeError(u(t)+" is not iterable")}return _.prototype=I,i(x,"constructor",{value:I,configurable:!0}),i(I,"constructor",{value:_,configurable:!0}),_.displayName=c(I,s,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===_||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,I):(e.__proto__=I,c(e,s,"GeneratorFunction")),e.prototype=Object.create(x),e},t.awrap=function(e){return{__await:e}},D(C.prototype),c(C.prototype,l,(function(){return this})),t.AsyncIterator=C,t.async=function(e,n,r,i,a){void 0===a&&(a=Promise);var o=new C(d(e,n,r,i),a);return t.isGeneratorFunction(n)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},D(x),c(x,s,"Generator"),c(x,o,(function(){return this})),c(x,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=A,T.prototype={constructor:T,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(N),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function i(r,i){return l.type="throw",l.arg=t,n.next=r,i&&(n.method="next",n.arg=e),!!i}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],l=o.completion;if("root"===o.tryLoc)return i("end");if(o.tryLoc<=this.prev){var s=r.call(o,"catchLoc"),u=r.call(o,"finallyLoc");if(s&&u){if(this.prev<o.catchLoc)return i(o.catchLoc,!0);if(this.prev<o.finallyLoc)return i(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return i(o.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return i(o.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=e,o.arg=t,a?(this.method="next",this.next=a.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),N(n),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;N(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:A(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),v}},t}function y(e,t,n,r,i,a,o){try{var l=e[a](o),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,i)}function v(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var a=e.apply(t,n);function o(e){y(a,r,i,o,l,"next",e)}function l(e){y(a,r,i,o,l,"throw",e)}o(void 0)}))}}var b={inheritAttrs:!1,name:"AddFoodDrawer",components:{VirtualListSelect:s["a"]},props:{show:{required:!0,type:Boolean},wrapperClosable:{type:Boolean,default:!0},direction:{type:String,default:"rtl"},infoData:{type:Object,default:function(){return{}}}},data:function(){var e=function(e,t,n){if(t){var r=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;r.test(t)?n():n(new Error("营养数据有误，仅支持保留两位小数"))}else n()};return{type:"add",isLoading:!1,formData:{name:"",attributes:"foods",tasteList:[],foodImages:[],foodImagesList:[],extraImages:[],ingredientList:[],food_id:"",categoryId:""},fileLists:[],serverUrl:"/api/background/file/upload",headersOpts:{TOKEN:Object(a["B"])()},formRuls:{name:[{required:!0,message:"食材名称不能为空",trigger:"blur"}],attributes:[{required:!0,message:"请选择属性",trigger:"blur"}],nutrition:[{validator:e,trigger:"change"}],foodImages:[{required:!0,message:"请上传菜品图片",trigger:"change"}],categoryId:[{required:!0,message:"请选择分类",trigger:"blur"}]},nutritionList:o["NUTRITION_LIST"],actionUrl:"",uploadParams:{prefix:"super_food_img"},uploadUrl:"",tableData:[{}],ingredientList:[],allSelectIngredient:[],errorMsg:{percentageError:""},ruleSingleInfo:{},dialogImageUrl:"",dialogVisible:!1,showFoodImg:!0,foodCategoryList:[],showAll:!1,uploading:!1,uploadingExtra:!1}},computed:{showDrawer:{get:function(){return this.show},set:function(e){this.$emit("update:show",e)}},currentNutritionList:function(){var e=[];return e=this.showAll?this.nutritionList:this.nutritionList.slice(0,4),e}},created:function(){this.type=this.$route.query.type,this.initLoad()},mounted:function(){},methods:{initLoad:function(){var e=this;return v(h().mark((function t(){return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,e.getIngredientslist();case 3:return t.next=5,e.foodFoodCategoryList();case 5:e.isLoading=!1,e.initIngredient(),e.setNutritonData({});case 8:case"end":return t.stop()}}),t)})))()},searchHandle:Object(a["d"])((function(){this.currentPage=1}),300),setNutritonData:function(e){var t=this;e.nutrition||(e.nutrition={});var n=e.nutrition.element?JSON.parse(Object(a["R"])(e.nutrition.element)):{},r=e.nutrition.vitamin?JSON.parse(Object(a["R"])(e.nutrition.vitamin)):{};o["NUTRITION_LIST"].forEach((function(i){"default"===i.type&&t.$set(t.formData,i.key,e.nutrition[i.key]?e.nutrition[i.key]:0),"element"===i.type&&t.$set(t.formData,i.key,n[i.key]?n[i.key]:0),"vitamin"===i.type&&t.$set(t.formData,i.key,r[i.key]?r[i.key]:0)}))},getIngredientslist:function(){var e=this;return v(h().mark((function t(){var n,r,i,o;return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(a["Z"])(e.$apis.apiBackgroundAdminIngredientIngredientNamePost({page:1,page_size:999999}));case 2:if(n=t.sent,r=c(n,2),i=r[0],o=r[1],e.isLoading=!1,!i){t.next=10;break}return e.$message.error(i.message),t.abrupt("return");case 10:0===o.code?e.ingredientList=o.data:e.$message.error(o.msg);case 11:case"end":return t.stop()}}),t)})))()},initIngredient:function(e){var t=this;this.formData.ingredientList=[],"add"===this.type?this.formData.ingredientList.push({index:this.formData.ingredientList.length,selectId:"",percentage:0,selectFoodIngredient:Object(a["f"])(this.ingredientList)}):e&&(this.formData.ingredientList=e.ingredients_list.map((function(e,n){return e.index=n,e.selectId=Number(e.ingredient_id),e.percentage=e.ingredient_scale,e.selectFoodIngredient=Object(a["f"])(t.ingredientList),t.ingredientList.map((function(t){t.id===e.selectId&&(e.nutrition=t.nutrition_info)})),e})),this.isDisabledOtherIngredients())},formatParams:function(){var e=this,t={name:this.formData.name,attributes:this.formData.attributes,image:this.formData.foodImages[0],extra_image:this.formData.extraImages,ingredient_list:[],nutrition_info:{},category_id:this.formData.categoryId};this.formData.food_id&&(t.food_id=this.formData.food_id),this.formData.ingredientList.map((function(e){if(e.selectId){var n={ingredient_id:e.selectId,ingredient_scale:e.percentage};t.ingredient_list.push(n)}}));var n={},r={};return o["NUTRITION_LIST"].forEach((function(i){"default"===i.type&&(t.nutrition_info[i.key]=e.formData[i.key]),"element"===i.type&&(n[i.key]=e.formData[i.key]),"vitamin"===i.type&&(r[i.key]=e.formData[i.key])})),t.nutrition_info.element=JSON.stringify(n),t.nutrition_info.vitamin=JSON.stringify(r),t},addIngredients:function(){this.formData.ingredientList.push({index:this.formData.ingredientList.length,selectId:"",percentage:0,selectFoodIngredient:Object(a["f"])(this.ingredientList)}),this.isDisabledOtherIngredients()},deleteIngredientHandle:function(e){this.formData.ingredientList.splice(e,1),this.isDisabledOtherIngredients(),this.computedNutritionAndPercentage(),this.changePercentage()},changeIngredient:function(e){var t={};this.ingredientList.map((function(n){n.id===e&&(t=n)})),this.formData.ingredientList.forEach((function(e){e.selectId===t.id&&(e.nutrition=t.nutrition_info)})),this.errorMsg.percentageError="",this.isDisabledOtherIngredients(),this.computedNutritionAndPercentage()},isDisabledOtherIngredients:function(){var e=this;this.allSelectIngredient=[],this.formData.ingredientList.map((function(t,n){t.selectId&&e.allSelectIngredient.push(t.selectId)})),this.formData.ingredientList.forEach((function(t,n){t.selectFoodIngredient.forEach((function(n){e.allSelectIngredient.includes(n.id)&&t.selectId!==n.id?e.$set(n,"disabled",!0):e.$set(n,"disabled",!1)}))}))},computedNutritionAndPercentage:function(){var e=this,t={};o["NUTRITION_LIST"].forEach((function(e){t[e.key]=0}));var n=0;this.formData.ingredientList.map((function(r,i){if(r.selectId){i<e.allSelectIngredient.length-1?(r.percentage=parseInt(l["a"].divide(100,e.allSelectIngredient.length)),n=l["a"].plus(r.percentage,n)):r.percentage=parseInt(l["a"].minus(100,n));var o=r.percentage/100;if(r.nutrition||(r.nutrition={}),t.energy_kcal=+r.nutrition.energy_kcal?l["a"].plus(t.energy_kcal,r.nutrition.energy_kcal*o):t.energy_kcal?t.energy_kcal:0,t.protein=+r.nutrition.protein?l["a"].plus(t.protein,r.nutrition.protein*o):t.protein?t.protein:0,t.axunge=+r.nutrition.axunge?l["a"].plus(t.axunge,r.nutrition.axunge*o):t.axunge?t.axunge:0,t.carbohydrate=+r.nutrition.carbohydrate?l["a"].plus(t.carbohydrate,r.nutrition.carbohydrate*o):t.carbohydrate?t.carbohydrate:0,t.cholesterol=+r.nutrition.cholesterol?l["a"].plus(t.cholesterol,r.nutrition.cholesterol*o):t.cholesterol?t.cholesterol:0,t.dietary_fiber=+r.nutrition.dietary_fiber?l["a"].plus(t.dietary_fiber,r.nutrition.dietary_fiber*o):t.dietary_fiber?t.dietary_fiber:0,r.nutrition.element&&r.nutrition.vitamin)try{var s=JSON.parse(Object(a["R"])(r.nutrition.element)),u=JSON.parse(Object(a["R"])(r.nutrition.vitamin));for(var c in s)t[c]=l["a"].plus(t[c],+s[c]?s[c]*o:0);for(var d in u)t[d]=l["a"].plus(t[d],+u[d]?u[d]*o:0)}catch(p){}e.deepFormIngredients&&e.deepFormIngredients.length&&e.deepFormIngredients.forEach((function(e){e.id===r.id&&(r.status=!0)}))}})),this.nutritionList.forEach((function(n){var r=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;r.test(t[n.key])?e.$set(e.formData,n.key,t[n.key]):e.$set(e.formData,n.key,t[n.key].toFixed(2))}))},setNutritionAndPercentage:function(){var e=this,t={};o["NUTRITION_LIST"].forEach((function(e){t[e.key]=0})),this.formData.ingredientList.map((function(e,n){if(e.selectId){e.nutrition||(e.nutrition={});var r=e.percentage/100;if(t.energy_kcal=+e.nutrition.energy_kcal?l["a"].plus(t.energy_kcal,e.nutrition.energy_kcal*r):t.energy_kcal?t.energy_kcal:0,t.protein=+e.nutrition.protein?l["a"].plus(t.protein,e.nutrition.protein*r):t.protein?t.protein:0,t.axunge=+e.nutrition.axunge?l["a"].plus(t.axunge,e.nutrition.axunge*r):t.axunge?t.axunge:0,t.carbohydrate=+e.nutrition.carbohydrate?l["a"].plus(t.carbohydrate,e.nutrition.carbohydrate*r):t.carbohydrate?t.carbohydrate:0,t.cholesterol=+e.nutrition.cholesterol?l["a"].plus(t.cholesterol,e.nutrition.cholesterol*r):t.cholesterol?t.cholesterol:0,t.dietary_fiber=+e.nutrition.dietary_fiber?l["a"].plus(t.dietary_fiber,e.nutrition.dietary_fiber*r):t.dietary_fiber?t.dietary_fiber:0,e.nutrition.element&&e.nutrition.vitamin)try{var i=JSON.parse(Object(a["R"])(e.nutrition.element)),o=JSON.parse(Object(a["R"])(e.nutrition.vitamin));for(var s in i)t[s]=l["a"].plus(t[s],+i[s]?i[s]*r:0);for(var u in o)t[u]=l["a"].plus(t[u],+o[u]?o[u]*r:0)}catch(c){}}})),this.nutritionList.forEach((function(n){var r=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;r.test(t[n.key])?e.$set(e.formData,n.key,t[n.key]):e.$set(e.formData,n.key,t[n.key].toFixed(2))}))},changePercentage:function(e){this.setNutritionAndPercentage();var t=this.formData.ingredientList.reduce((function(e,t){return l["a"].plus(t.percentage,e)}),0);this.errorMsg.percentageError=t>100||t<100?"菜品每100g所含食材占比，相加必须等于100%":"",this.formData.ingredientList.length||(this.errorMsg.percentageError="")},handelChange:function(e,t){this.uploadParams.key=this.uploadParams.prefix+(new Date).getTime()+Math.floor(150*Math.random())+".png"},perviewFoodImg:function(e){this.dialogImageUrl=e,this.dialogVisible=!0},handleFoodImgSuccess:function(e,t,n){this.uploading=!1,0===e.code?(this.formData.foodImagesList=n,this.formData.foodImages.push(e.data.public_url),this.$refs.foodRef.clearValidate("foodImages")):this.$message.error(e.msg)},handleImgRemove:function(e,t){var n=this.formData[t+"List"].findIndex((function(t){return t.url===e.url}));this.formData[t].splice(n,1),this.formData[t+"List"].splice(n,1)},beforeFoodImgUpload:function(e){return this.beforeImgUpload(e,"uploading")},beforeImgUpload:function(e,t){var n=[".jpeg",".jpg",".png",".bmp"],r=e.size/1024/1024<5;return n.includes(Object(a["A"])(e.name))?r?void(t&&(this[t]=!0)):(this.$message.error("上传图片大小不能超过 5MB!"),!1):(this.$message.error("上传图片只能是 JPG 格式或者PNG格式!"),!1)},handlePictureCardPreview:function(e){this.dialogImageUrl=e.url,this.dialogVisible=!0},submitHandler:function(){var e=this;this.$refs.foodRef.validate((function(t){if(t&&!e.errorMsg.percentageError){if(e.isLoading)return e.$message.error("请勿重复提交！");e.addFoodList()}}))},addFoodList:function(){var e=this;return v(h().mark((function t(){var n,r,i,o;return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(a["Z"])(e.$apis.apiBackgroundAdminFoodAddPost(e.formatParams()));case 3:if(n=t.sent,r=c(n,2),i=r[0],o=r[1],e.isLoading=!1,!i){t.next=11;break}return e.$message.error(i.message),t.abrupt("return");case 11:0===o.code?(e.$message.success(o.msg),e.resetForm(),e.showDrawer=!1,e.$emit("confirmFood")):2===o.code?e.$message.error("已有相同名称菜品！"):e.$message.error(o.msg);case 12:case"end":return t.stop()}}),t)})))()},closeHandler:function(){this.resetForm(),this.showDrawer=!1},resetForm:function(){this.formData={name:"",attributes:"foods",tasteList:[],foodImages:[],foodImagesList:[],extraImages:[],ingredientList:[],food_id:"",categoryId:""},this.fileLists=[],this.errorMsg.percentageError=""},foodFoodCategoryList:function(){var e=this;return v(h().mark((function t(){var n,r,i,o;return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(a["Z"])(e.$apis.apiBackgroundAdminFoodCategoryListPost({page:1,page_size:999999}));case 2:if(n=t.sent,r=c(n,2),i=r[0],o=r[1],!i){t.next=9;break}return e.$message.error(i.message),t.abrupt("return");case 9:0===o.code?e.foodCategoryList=o.data.results:e.$message.error(o.msg);case 10:case"end":return t.stop()}}),t)})))()}}},_=b,I=(n("8736"),n("2877")),w=Object(I["a"])(_,r,i,!1,null,null,null);t["default"]=w.exports},8736:function(e,t,n){"use strict";n("aa46f")},aa46f:function(e,t,n){}}]);