(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-diet-manage-components-MealNutritionDialog","view-super-health-system-threemeallist"],{"3e17":function(t,e,n){},ada0:function(t,e,n){"use strict";n("3e17")},d0a8:function(t,e,n){"use strict";n.r(e);var o=function(){var t=this,e=t._self._c;return e("dialog-message",{attrs:{show:t.visible,title:t.mealName(t.selectInfo.key),loading:t.isLoading,customClass:"ps-dialog",width:"850px"},on:{"update:show":function(e){t.visible=e},"update:loading":function(e){t.isLoading=e},close:t.handleClose}},[e("el-form",{ref:"dialogFormRef",staticClass:"member-form",attrs:{model:t.dialogForm,"status-icon":"","label-width":"120px"},nativeOn:{submit:function(t){t.preventDefault()}}},[e("el-form-item",{attrs:{label:"正餐菜品："}},[e("el-button",{staticClass:"ps-btn float-r",staticStyle:{margin:"0 15px 15px 0"},attrs:{type:"primary",size:"small",icon:"el-icon-plus"},on:{click:function(e){return t.clickAddFood("main")}}},[t._v(" 新增 ")]),e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.mealFoodTableData["main"],stripe:"","header-row-class-name":"ps-table-header-row"}},t._l(t.tableSettings,(function(n){return e("table-column",{key:n.key,attrs:{col:n},scopedSlots:t._u([{key:"food",fn:function(n){var o=n.row,r=n.index;return[e("virtual-select",{staticClass:"ps-select margin-right",attrs:{width:150,"popover-width":200,placeholder:"请下拉选择",filterable:"","data-list":t.selectFoodList,option:{label:"name",value:"id"}},on:{change:function(e){return t.changeFood(e,r,"main")}},model:{value:o.food_id,callback:function(e){t.$set(o,"food_id",e)},expression:"row.food_id"}})]}},{key:"weight",fn:function(n){var o=n.row,r=n.index;return[e("el-input",{staticClass:"ps-input",attrs:{size:"small",placeholder:"请输入重量",disabled:!o.food_id},on:{input:function(e){return t.changeWeight(o,r,"main")}},model:{value:o.weight,callback:function(e){t.$set(o,"weight",e)},expression:"row.weight"}})]}},{key:"operation",fn:function(n){n.row;var o=n.index;return[e("el-button",{staticClass:"ps-red",attrs:{type:"text",size:"small"},on:{click:function(e){return t.clickDelfood(o,"main")}}},[t._v(" 删除 ")])]}}],null,!0)})})),1)],1),e("el-form-item",{attrs:{label:"加餐菜品："}},[e("el-button",{staticClass:"ps-btn float-r",staticStyle:{margin:"0 15px 15px 0"},attrs:{type:"primary",size:"small",icon:"el-icon-plus"},on:{click:function(e){return t.clickAddFood("sec")}}},[t._v(" 新增 ")]),e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.mealFoodTableData["sec"],stripe:"","header-row-class-name":"ps-table-header-row"}},t._l(t.tableSettings,(function(n){return e("table-column",{key:n.key,attrs:{col:n},scopedSlots:t._u([{key:"food",fn:function(n){var o=n.row,r=n.index;return[e("virtual-select",{staticClass:"ps-select margin-right",attrs:{width:150,"popover-width":200,placeholder:"请下拉选择",filterable:"","data-list":t.selectFoodList,option:{label:"name",value:"id"}},on:{change:function(e){return t.changeFood(e,r,"sec")}},model:{value:o.food_id,callback:function(e){t.$set(o,"food_id",e)},expression:"row.food_id"}})]}},{key:"weight",fn:function(n){var o=n.row,r=n.index;return[e("el-input",{staticClass:"ps-input",attrs:{size:"small",placeholder:"请输入重量",disabled:!o.food_id},on:{input:function(e){return t.changeWeight(o,r,"sec")}},model:{value:o.weight,callback:function(e){t.$set(o,"weight",e)},expression:"row.weight"}})]}},{key:"operation",fn:function(n){n.row;var o=n.index;return[e("el-button",{staticClass:"ps-red",attrs:{type:"text",size:"small"},on:{click:function(e){return t.clickDelfood(o,"sec")}}},[t._v(" 删除 ")])]}}],null,!0)})})),1)],1),e("el-form-item",{attrs:{label:"营养合计："}},[e("span",[t._v("热量："+t._s(t.totalNutrition.energyKcal)+"kcal，")]),e("span",[t._v("碳水化合物："+t._s(t.totalNutrition.carbohydrate)+"g，")]),e("span",[t._v("蛋白质："+t._s(t.totalNutrition.protein)+"g，")]),e("span",[t._v("脂肪："+t._s(t.totalNutrition.axunge)+"g")])])],1),e("template",{slot:"tool"},[e("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:t.isLoading},on:{click:t.clickCancleHandle}},[t._v(" 取消 ")]),e("el-button",{staticClass:"ps-btn",attrs:{disabled:t.isLoading,type:"primary"},on:{click:t.clickConfirmHandle}},[t._v(" 确定 ")])],1)])],2)},r=[],i=n("da92"),a=n("ed08"),l=n("dfd5"),s=n("8003");function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function u(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */u=function(){return e};var t,e={},n=Object.prototype,o=n.hasOwnProperty,r=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function d(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{d({},"")}catch(t){d=function(t,e,n){return t[e]=n}}function h(t,e,n,o){var i=e&&e.prototype instanceof b?e:b,a=Object.create(i.prototype),l=new C(o||[]);return r(a,"_invoke",{value:S(t,n,l)}),a}function f(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var p="suspendedStart",g="suspendedYield",y="executing",m="completed",v={};function b(){}function w(){}function _(){}var k={};d(k,a,(function(){return this}));var x=Object.getPrototypeOf,F=x&&x(x(j([])));F&&F!==n&&o.call(F,a)&&(k=F);var L=_.prototype=b.prototype=Object.create(k);function N(t){["next","throw","return"].forEach((function(e){d(t,e,(function(t){return this._invoke(e,t)}))}))}function D(t,e){function n(r,i,a,l){var s=f(t[r],t,i);if("throw"!==s.type){var u=s.arg,d=u.value;return d&&"object"==c(d)&&o.call(d,"__await")?e.resolve(d.__await).then((function(t){n("next",t,a,l)}),(function(t){n("throw",t,a,l)})):e.resolve(d).then((function(t){u.value=t,a(u)}),(function(t){return n("throw",t,a,l)}))}l(s.arg)}var i;r(this,"_invoke",{value:function(t,o){function r(){return new e((function(e,r){n(t,o,e,r)}))}return i=i?i.then(r,r):r()}})}function S(e,n,o){var r=p;return function(i,a){if(r===y)throw Error("Generator is already running");if(r===m){if("throw"===i)throw a;return{value:t,done:!0}}for(o.method=i,o.arg=a;;){var l=o.delegate;if(l){var s=E(l,o);if(s){if(s===v)continue;return s}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(r===p)throw r=m,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);r=y;var c=f(e,n,o);if("normal"===c.type){if(r=o.done?m:g,c.arg===v)continue;return{value:c.arg,done:o.done}}"throw"===c.type&&(r=m,o.method="throw",o.arg=c.arg)}}}function E(e,n){var o=n.method,r=e.iterator[o];if(r===t)return n.delegate=null,"throw"===o&&e.iterator.return&&(n.method="return",n.arg=t,E(e,n),"throw"===n.method)||"return"!==o&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+o+"' method")),v;var i=f(r,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,v;var a=i.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,v):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function O(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function T(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function C(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(O,this),this.reset(!0)}function j(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,i=function n(){for(;++r<e.length;)if(o.call(e,r))return n.value=e[r],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw new TypeError(c(e)+" is not iterable")}return w.prototype=_,r(L,"constructor",{value:_,configurable:!0}),r(_,"constructor",{value:w,configurable:!0}),w.displayName=d(_,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,d(t,s,"GeneratorFunction")),t.prototype=Object.create(L),t},e.awrap=function(t){return{__await:t}},N(D.prototype),d(D.prototype,l,(function(){return this})),e.AsyncIterator=D,e.async=function(t,n,o,r,i){void 0===i&&(i=Promise);var a=new D(h(t,n,o,r),i);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},N(L),d(L,s,"Generator"),d(L,a,(function(){return this})),d(L,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var o in e)n.push(o);return n.reverse(),function t(){for(;n.length;){var o=n.pop();if(o in e)return t.value=o,t.done=!1,t}return t.done=!0,t}},e.values=j,C.prototype={constructor:C,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(T),!e)for(var n in this)"t"===n.charAt(0)&&o.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function r(o,r){return l.type="throw",l.arg=e,n.next=o,r&&(n.method="next",n.arg=t),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],l=a.completion;if("root"===a.tryLoc)return r("end");if(a.tryLoc<=this.prev){var s=o.call(a,"catchLoc"),c=o.call(a,"finallyLoc");if(s&&c){if(this.prev<a.catchLoc)return r(a.catchLoc,!0);if(this.prev<a.finallyLoc)return r(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return r(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return r(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&o.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),T(n),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var o=n.completion;if("throw"===o.type){var r=o.arg;T(n)}return r}}throw Error("illegal catch attempt")},delegateYield:function(e,n,o){return this.delegate={iterator:j(e),resultName:n,nextLoc:o},"next"===this.method&&(this.arg=t),v}},e}function d(t,e,n,o,r,i,a){try{var l=t[i](a),s=l.value}catch(t){return void n(t)}l.done?e(s):Promise.resolve(s).then(o,r)}function h(t){return function(){var e=this,n=arguments;return new Promise((function(o,r){var i=t.apply(e,n);function a(t){d(i,o,r,a,l,"next",t)}function l(t){d(i,o,r,a,l,"throw",t)}a(void 0)}))}}var f={name:"MealNutritionDialog",components:{VirtualSelect:s["a"]},props:{loading:Boolean,title:{type:String,default:""},selectInfo:{type:Object,default:function(){return{}}},isshow:Boolean},data:function(){return{isLoading:!1,dialogForm:{name:""},numReg:/^\+?[1-9][0-9]*$/,selectFoodList:[],mealFoodTableData:{main:[],sec:[]},dinnerFoodtableData:[],allFoodList:[],tableSettings:[{label:"菜品名称",key:"food",type:"slot",slotName:"food",width:"160"},{label:"推荐重量（g）",key:"weight",type:"slot",slotName:"weight",width:"120"},{label:"菜品每100克热量（kcal）",key:"food_energy_kcal",width:"190"},{label:"热量（kcal）",key:"energy_kcal",width:"120"},{label:"碳水化合物（g）",key:"carbohydrate",width:"130"},{label:"蛋白质（g）",key:"protein",width:"120"},{label:"脂肪（g）",key:"axunge",width:"120"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"100"}],nutritionData:{}}},computed:{visible:{get:function(){return this.isshow},set:function(t){this.$emit("update:isshow",t)}},totalNutrition:function(){var t={energyKcal:0,carbohydrate:0,protein:0,axunge:0};for(var e in this.mealFoodTableData)this.mealFoodTableData[e].length&&this.mealFoodTableData[e].forEach((function(e){e.energy_kcal&&(t.energyKcal=i["a"].plus(e.energy_kcal,t.energyKcal)),e.carbohydrate&&(t.carbohydrate=i["a"].plus(e.carbohydrate,t.carbohydrate)),e.protein&&(t.protein=i["a"].plus(e.protein,t.protein)),e.axunge&&(t.axunge=i["a"].plus(e.axunge,t.axunge))}));return t}},watch:{},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){var t=this;return h(u().mark((function e(){var n,o,r,i;return u().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=Object(a["x"])("allFoodList"),JSON.parse(n)&&JSON.parse(n).length){e.next=8;break}return e.next=4,t.getFoodlist();case 4:o=e.sent,t.allFoodList=o.data.results,e.next=9;break;case 8:t.selectFoodList=JSON.parse(n);case 9:r=u().mark((function e(n){return u().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.selectInfo[n+"_food"]&&(t.mealFoodTableData[n]=[],t.selectInfo[n+"_food"].forEach((function(e){var o=t.selectFoodList.filter((function(t){return t.id===e.food_id}));if(o.length){var r={food_id:e.food_id,weight:e.weight,food_energy_kcal:o[0].nutrition.energy_kcal,energy_kcal:t.countNutrition(o[0].nutrition.energy_kcal,e.weight),carbohydrate:t.countNutrition(o[0].nutrition.carbohydrate,e.weight),protein:t.countNutrition(o[0].nutrition.protein,e.weight),axunge:t.countNutrition(o[0].nutrition.axunge,e.weight)};t.nutritionData[o[0].id]=o[0],t.mealFoodTableData[n].push(r)}})));case 1:case"end":return e.stop()}}),e)})),e.t0=u().keys(t.mealFoodTableData);case 11:if((e.t1=e.t0()).done){e.next=16;break}return i=e.t1.value,e.delegateYield(r(i),"t2",14);case 14:e.next=11;break;case 16:case"end":return e.stop()}}),e)})))()},mealName:function(t){var e="";switch(t){case"breakfast":e="早餐";break;case"lunch":e="午餐";break;case"dinner":e="晚餐";break;default:break}return e},clickAddFood:function(t){this.mealFoodTableData[t].push({food_id:"",weight:"",food_energy_kcal:"",energy_kcal:"",carbohydrate:"",protein:"",axunge:""})},changeFood:function(t,e,n){this.nutritionData[t.id]=t,this.changeWeight(t,e,n,"selectFood")},changeWeight:function(t,e,n,o){var r=Object(a["f"])(this.mealFoodTableData[n][e]);if("selectFood"===o&&(r.food_energy_kcal=t.nutrition?t.nutrition.energy_kcal:0),r.food_id&&r.weight&&this.numReg.test(r.weight)){var i=this.nutritionData[r.food_id];r.energy_kcal=this.countNutrition(i.nutrition.energy_kcal,r.weight),r.carbohydrate=this.countNutrition(i.nutrition.carbohydrate,r.weight),r.protein=this.countNutrition(i.nutrition.protein,r.weight),r.axunge=this.countNutrition(i.nutrition.axunge,r.weight)}else r.energy_kcal="",r.carbohydrate="",r.protein="",r.axunge="";this.$set(this.mealFoodTableData[n],e,r)},clickDelfood:function(t,e){this.mealFoodTableData[e].splice(t,1)},countNutrition:function(t,e){var n=i["a"].divide(t,100);return i["a"].times(n,e).toFixed(2)},clickConfirmHandle:function(){var t={menuIndex:this.selectInfo.menuIndex,key:this.selectInfo.key,total_nutrition:{axunge:this.totalNutrition.axunge,protein:this.totalNutrition.protein,carbohydrate:this.totalNutrition.carbohydrate,energy_kcal:this.totalNutrition.energyKcal},main_food:[],sec_food:[]};for(var e in this.mealFoodTableData)if(this.mealFoodTableData[e].length)for(var n=0;n<this.mealFoodTableData[e].length;n++){var o=this.mealFoodTableData[e][n];if("main"===e&&!o.food_id)return this.$message.error("正餐菜品：第".concat(n+1,"条数据,请选择菜品"));if("main"===e&&(!o.weight||!this.numReg.test(Number(o.weight))))return this.$message.error("正餐菜品：第".concat(n+1,"条数据,请输入重量"));if("main"===e&&o.food_id&&o.weight&&t.main_food.push({food_id:o.food_id,weight:o.weight}),"sec"===e&&!o.food_id)return this.$message.error("加餐菜品：第".concat(n+1,"条数据,请选择菜品"));if("sec"===e&&(!o.weight||!this.numReg.test(Number(o.weight))))return this.$message.error("加餐菜品：第".concat(n+1,"条数据,请输入重量"));"sec"===e&&o.food_id&&o.weight&&t.sec_food.push({food_id:o.food_id,weight:o.weight})}this.visible=!1,this.$emit("confirmHandle",t)},clickCancleHandle:function(){this.visible=!1},handleClose:function(t){this.isLoading=!1,this.visible=!1},getFoodlist:l["getFoodlist"]}},p=f,g=(n("ada0"),n("2877")),y=Object(g["a"])(p,o,r,!1,null,"8b18a0e2",null);e["default"]=y.exports},dfd5:function(t,e,n){"use strict";function o(){var t=this;return new Promise((function(e,n){t.$apis.apiBackgroundAdminFoodListPost({page:1,page_size:999999}).then((function(t){sessionStorage.setItem("allFoodList",t.data.results?JSON.stringify(t.data.results):"[]"),e(t)})).catch((function(t){n(t)}))}))}n.r(e),n.d(e,"getFoodlist",(function(){return o}))}}]);