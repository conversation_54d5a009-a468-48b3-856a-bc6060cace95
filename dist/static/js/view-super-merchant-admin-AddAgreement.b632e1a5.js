(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-merchant-admin-AddAgreement"],{"4eb4":function(t,e,r){"use strict";r("b4ce")},"99f0":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{attrs:{id:"AddAgreement"}},[e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"form-wrapper"},[e("el-form",{ref:"noticeInfoForm",attrs:{model:t.formData,"label-width":"80px",rules:t.noticeInfoRules}},[e("el-form-item",{attrs:{label:"协议类型",prop:"agreement_type"}},[e("el-select",{ref:"companyRef",staticClass:"ps-input",staticStyle:{width:"400px"},attrs:{clearable:"",filterable:""},on:{change:t.changeAgreementType},model:{value:t.formData.agreement_type,callback:function(e){t.$set(t.formData,"agreement_type",e)},expression:"formData.agreement_type"}},t._l(t.agreementTypeList,(function(t){return e("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),e("el-form-item",{attrs:{label:"协议名称",prop:"agreement_name"}},[e("el-input",{staticClass:"ps-input",staticStyle:{width:"400px"},attrs:{placeholder:"协议类型+年月日",maxlength:"40"},on:{input:t.searchHandle},model:{value:t.formData.agreement_name,callback:function(e){t.$set(t.formData,"agreement_name",e)},expression:"formData.agreement_name"}})],1),e("el-form-item",{attrs:{label:"协议内容",prop:"content"}},[e("TinymceUeditor",{attrs:{listener:"focus","custom-handle":t.blurSelsectHandle},model:{value:t.formData.content,callback:function(e){t.$set(t.formData,"content",e)},expression:"formData.content"}})],1),e("el-form-item",[e("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[e("el-button",{staticStyle:{width:"120px"},on:{click:t.closeHandler}},[t._v("取消")]),e("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:t.submitForm}},[t._v(" "+t._s("add"===t.type?"保存":"编辑")+" ")])],1)])],1)],1)])},o=[],a=r("ed08"),i=r("56f9"),c=r("2f62");function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",c=a.asyncIterator||"@@asyncIterator",l=a.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function m(t,e,r,n){var a=e&&e.prototype instanceof b?e:b,i=Object.create(a.prototype),c=new P(n||[]);return o(i,"_invoke",{value:$(t,r,c)}),i}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=m;var h="suspendedStart",d="suspendedYield",y="executing",g="completed",v={};function b(){}function w(){}function x(){}var O={};f(O,i,(function(){return this}));var _=Object.getPrototypeOf,L=_&&_(_(T([])));L&&L!==r&&n.call(L,i)&&(O=L);var j=x.prototype=b.prototype=Object.create(O);function A(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function r(o,a,i,c){var s=p(t[o],t,a);if("throw"!==s.type){var l=s.arg,f=l.value;return f&&"object"==u(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,i,c)}),(function(t){r("throw",t,i,c)})):e.resolve(f).then((function(t){l.value=t,i(l)}),(function(t){return r("throw",t,i,c)}))}c(s.arg)}var a;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return a=a?a.then(o,o):o()}})}function $(e,r,n){var o=h;return function(a,i){if(o===y)throw Error("Generator is already running");if(o===g){if("throw"===a)throw i;return{value:t,done:!0}}for(n.method=a,n.arg=i;;){var c=n.delegate;if(c){var u=k(c,n);if(u){if(u===v)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===h)throw o=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=y;var s=p(e,r,n);if("normal"===s.type){if(o=n.done?g:d,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=g,n.method="throw",n.arg=s.arg)}}}function k(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,k(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var a=p(o,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,v;var i=a.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function D(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function P(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(D,this),this.reset(!0)}function T(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(u(e)+" is not iterable")}return w.prototype=x,o(j,"constructor",{value:x,configurable:!0}),o(x,"constructor",{value:w,configurable:!0}),w.displayName=f(x,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,x):(t.__proto__=x,f(t,l,"GeneratorFunction")),t.prototype=Object.create(j),t},e.awrap=function(t){return{__await:t}},A(S.prototype),f(S.prototype,c,(function(){return this})),e.AsyncIterator=S,e.async=function(t,r,n,o,a){void 0===a&&(a=Promise);var i=new S(m(t,r,n,o),a);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},A(j),f(j,l,"Generator"),f(j,i,(function(){return this})),f(j,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=T,P.prototype={constructor:P,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(E),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return c.type="throw",c.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],c=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var u=n.call(i,"catchLoc"),s=n.call(i,"finallyLoc");if(u&&s){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(u){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=t,i.arg=e,a?(this.method="next",this.next=a.finallyLoc,v):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),E(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;E(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:T(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function l(t,e){return d(t)||h(t,e)||m(t,e)||f()}function f(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function m(t,e){if(t){if("string"==typeof t)return p(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?p(t,e):void 0}}function p(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function h(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,c=[],u=!0,s=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=a.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,o=t}finally{try{if(!u&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(s)throw o}}return c}}function d(t){if(Array.isArray(t))return t}function y(t,e,r,n,o,a,i){try{var c=t[a](i),u=c.value}catch(t){return void r(t)}c.done?e(u):Promise.resolve(u).then(n,o)}function g(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var a=t.apply(e,r);function i(t){y(a,n,o,i,c,"next",t)}function c(t){y(a,n,o,i,c,"throw",t)}i(void 0)}))}}function v(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function b(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?v(Object(r),!0).forEach((function(e){w(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function w(t,e,r){return(e=x(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function x(t){var e=O(t,"string");return"symbol"==u(e)?e:e+""}function O(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var _={name:"NoticeAdd",components:{TinymceUeditor:i["a"]},props:{},data:function(){return{formData:{id:"",agreement_type:"",agreement_name:"",content:""},agreementTypeList:[{label:"信息保护协议",value:"IPA"},{label:"刷脸支付服务协议",value:"FPSA"},{label:"用户服务协议",value:"USA"},{label:"隐私政策",value:"PA"},{label:"免责声明",value:"DA"},{label:"会员服务协议",value:"MSA"},{label:"服务条款",value:"TS"},{label:"监管平台服务协议",value:"FS"},{label:"监管平台免责声明",value:"FSD"}],noticeInfoRules:{agreement_type:[{required:!0,message:"请选择协议类型",trigger:"blur"}],agreement_name:[{required:!0,message:"请输入协议名称",trigger:"blur"}],content:[{required:!0,message:"请输入协议内容",trigger:"blur"}]},type:"",isLoading:!1}},created:function(){},mounted:function(){this.initLoad(),this.$route.query.type&&(this.type=this.$route.query.type),this.$route.query.id&&"modify"===this.$route.query.type&&(this.formData.id=this.$route.query.id,this.getAgreementDetails(this.formData.id))},methods:b(b({},Object(c["b"])({})),{},{initLoad:function(){},addAgreemeentHandle:function(t){var e=this;return g(s().mark((function r(){var n,o,i,c;return s().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return e.isLoading=!0,r.next=3,Object(a["Z"])(e.$apis.apiBackgroundAdminAgreementAddPost(t));case 3:if(n=r.sent,o=l(n,2),i=o[0],c=o[1],e.isLoading=!1,!i){r.next=11;break}return e.$message.error(i.message),r.abrupt("return");case 11:if(0!==c.code){r.next=18;break}return e.$message.success(c.msg),r.next=15,e.$sleep(300);case 15:e.$closeCurrentTab(e.$route.path),r.next=19;break;case 18:e.$message.error(c.msg);case 19:case"end":return r.stop()}}),r)})))()},modifyAgreemeentHandle:function(t){var e=this;return g(s().mark((function r(){var n,o,i,c;return s().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return e.isLoading=!0,r.next=3,Object(a["Z"])(e.$apis.apiBackgroundAdminAgreementModifyPost(t));case 3:if(n=r.sent,o=l(n,2),i=o[0],c=o[1],e.isLoading=!1,!i){r.next=11;break}return e.$message.error(i.message),r.abrupt("return");case 11:if(0!==c.code){r.next=18;break}return e.$message.success(c.msg),r.next=15,e.$sleep(300);case 15:e.$closeCurrentTab(e.$route.path),r.next=19;break;case 18:e.$message.error(c.msg);case 19:case"end":return r.stop()}}),r)})))()},getAgreementDetails:function(t){var e=this;return g(s().mark((function r(){var n,o,i,c;return s().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,Object(a["Z"])(e.$apis.apiBackgroundAdminAgreementAgreementDetailPost({id:t}));case 2:if(n=r.sent,o=l(n,2),i=o[0],c=o[1],!i){r.next=9;break}return e.$message.error(i.message),r.abrupt("return");case 9:0===c.code?(e.formData.agreement_name=c.data.agreement_name,e.formData.agreement_type=c.data.agreement_type,e.formData.content=Object(a["bb"])(c.data.content)):e.$message.error(c.msg);case 10:case"end":return r.stop()}}),r)})))()},blurSelsectHandle:function(t){this.$refs.companyRef.blur()},searchHandle:function(){},submitForm:function(){var t=this;this.$refs.noticeInfoForm.validate((function(e){if(!e)return!1;var r={agreement_type:t.formData.agreement_type,agreement_name:t.formData.agreement_name,content:Object(a["l"])(t.formData.content)};"modify"===t.type?(r.id=t.formData.id,t.modifyAgreemeentHandle(r)):t.addAgreemeentHandle(r)}))},changeAgreementType:function(t){var e=this.agreementTypeList.find((function(e){return e.value===t}));this.formData.agreement_name=e.label+Object(a["M"])(new Date,"{y}-{m}-{d}")},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,r,n){"confirm"===e?(r.confirmButtonLoading=!0,t.$closeCurrentTab(t.$route.path),r.confirmButtonLoading=!1):r.confirmButtonLoading||n()}}).then((function(t){})).catch((function(t){}))}})},L=_,j=(r("4eb4"),r("2877")),A=Object(j["a"])(L,n,o,!1,null,"70aa9097",null);e["default"]=A.exports},b4ce:function(t,e,r){}}]);