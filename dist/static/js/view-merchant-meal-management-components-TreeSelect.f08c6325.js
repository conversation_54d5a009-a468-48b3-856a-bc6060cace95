(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-components-TreeSelect"],{"32b8":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("div",[t("tree-select",{attrs:{multiple:!0,options:e.structureTree,"default-expand-level":1,normalizer:e.structureNormalizer,limit:3,limitText:function(e){return"+"+e},placeholder:"请选择","value-consists-of":"ALL",flat:!0,disabled:e.organizationDisabled},on:{input:e.inputTree},model:{value:e.structure,callback:function(t){e.structure=t},expression:"structure"}})],1)},u=[],i={name:"TreeSelectIngredients",props:{structureTree:{type:Array,default:function(){return[]}},structureData:{type:Array,default:function(){return[]}},organizationDisabled:<PERSON>olean},data:function(){return{}},computed:{structure:{get:function(){return this.structureData},set:function(e){this.$emit("update:structureData",e)}}},mounted:function(){},methods:{structureNormalizer:function(e){return{id:e.id,label:e.name,children:e.children_list}},inputTree:function(e){this.$emit("inputTree",e)}}},a=i,l=r("2877"),c=Object(l["a"])(a,n,u,!1,null,null,null);t["default"]=c.exports}}]);