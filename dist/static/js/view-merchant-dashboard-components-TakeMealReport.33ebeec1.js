(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-dashboard-components-TakeMealReport"],{"08b5":function(t,a,s){"use strict";s.r(a);var e=function(){var t=this,a=t._self._c;return a("div",{staticClass:"take-meal-report"},[a("div",{staticClass:"header"},[t._m(0),a("div",{staticClass:"header-title"},[t._v(" 朴食科技智慧食堂 "),a("el-button",{staticClass:"ps-bule block-btn",attrs:{type:"text",size:"small"},on:{click:t.openDialog}},[t._v("编辑")])],1),a("div",{staticClass:"header-account"})]),a("div",{staticClass:"main"},[a("div",{staticClass:"search"},[a("div",{staticClass:"search-left"},[a("div",{staticClass:"search-btn"},[a("div",{class:["search-btn-item","total"===t.tableType?"active-btn":""],on:{click:function(a){return t.changeType("total")}}},[t._v("汇总")]),a("div",{class:["search-btn-item","detail"===t.tableType?"active-btn":""],on:{click:function(a){return t.changeType("detail")}}},[t._v("明细")])]),a("div",{staticClass:"search-select"},[a("div",{staticClass:"search-select-item group"},[a("div",{staticClass:"search-select-label"},[t._v("分组")]),a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:t.groupId,callback:function(a){t.groupId=a},expression:"groupId"}})],1),a("div",{staticClass:"search-select-item isorder"},[a("div",{staticClass:"search-select-label"},[t._v("点餐情况")]),a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:t.mealStatus,callback:function(a){t.mealStatus=a},expression:"mealStatus"}})],1)])]),t._m(1)]),a("div",{staticClass:"progress"},[t._m(2),a("el-progress",{attrs:{"stroke-width":18,color:"#1684ff",percentage:45}})],1),"total"===t.tableType?a("div",{staticClass:"total-count"},[t._m(3),t._m(4),t._m(5),t._m(6)]):a("div",{staticClass:"table"},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:""}},[a("el-table-column",{attrs:{prop:"name",label:"姓名",align:"center"}}),a("el-table-column",{attrs:{prop:"person_no",label:"人员编号",align:"center"}}),a("el-table-column",{attrs:{prop:"payer_group",label:"所属分组",align:"center"}}),a("el-table-column",{attrs:{prop:"primary",label:"消费点",align:"center"}}),a("el-table-column",{attrs:{prop:"meal_status_alias",label:"点餐情况",align:"center"}})],1),a("div",{staticClass:"table-pagination"},[a("el-pagination",{attrs:{"current-page":t.currentPage,"page-size":t.pageSize,layout:"total, prev, pager, next, jumper",total:t.totalCount,background:""}})],1)],1)]),a("el-dialog",{attrs:{title:"选择组织",visible:t.showDialog,width:"500px","custom-class":"ps-dialog"},on:{"update:visible":function(a){t.showDialog=a}}},[a("el-form",{ref:"dialogFormRef",staticClass:"dialog-form",attrs:{model:t.dialogForm,"label-width":"160px"}},[a("div",{staticClass:"flex",staticStyle:{"align-items":"center"}},[a("el-form-item",{attrs:{label:"显示组织："}},[a("organization-select",{staticClass:"search-item-w ps-input w-180",attrs:{placeholder:"请选择所属组织",isLazy:!1,multiple:!1,"check-strictly":!0,"append-to-body":!0},model:{value:t.dialogForm.orgId,callback:function(a){t.$set(t.dialogForm,"orgId",a)},expression:"dialogForm.orgId"}})],1)],1)]),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{staticClass:"ps-cancel-btn",on:{click:function(a){t.showDialog=!1}}},[t._v("取 消")]),a("el-button",{staticClass:"ps-btn",attrs:{type:"primary"},on:{click:t.confirmDialog}},[t._v("确 定")])],1)],1)],1)},l=[function(){var t=this,a=t._self._c;return a("div",{staticClass:"header-logo"},[a("img",{attrs:{src:s("58b6"),alt:""}})])},function(){var t=this,a=t._self._c;return a("div",{staticClass:"search-right"},[a("div",{staticClass:"date-time"},[a("div",{staticClass:"time"},[t._v("09:00:06")]),a("div",{staticClass:"date"},[t._v("2022年12月12号 星期三")])]),a("div",{staticClass:"meal"},[a("div",{staticClass:"meal-label"},[t._v("当前餐段")]),a("div",{staticClass:"now-meal"},[a("img",{attrs:{src:s("718b"),alt:""}}),a("div",[t._v("午餐")])])])])},function(){var t=this,a=t._self._c;return a("div",{staticClass:"progress-top"},[a("div",{staticClass:"progress-label"},[t._v("刷新：45s")]),a("div",{staticClass:"progress-btn"},[a("i",{staticClass:"el-icon-refresh"}),t._v("手动刷新")])])},function(){var t=this,a=t._self._c;return a("div",{staticClass:"total-count-item"},[a("img",{attrs:{src:s("306c"),alt:""}}),a("div",{staticClass:"count-content"},[a("div",{staticClass:"title"},[t._v("总人数")]),a("div",{staticClass:"count"},[t._v("0")])])])},function(){var t=this,a=t._self._c;return a("div",{staticClass:"total-count-item blue"},[a("img",{attrs:{src:s("11fb"),alt:""}}),a("div",{staticClass:"count-content"},[a("div",{staticClass:"title"},[t._v("点餐人数")]),a("div",{staticClass:"count"},[t._v("0")])])])},function(){var t=this,a=t._self._c;return a("div",{staticClass:"total-count-item"},[a("img",{attrs:{src:s("090f"),alt:""}}),a("div",{staticClass:"count-content"},[a("div",{staticClass:"title"},[t._v("点餐人数")]),a("div",{staticClass:"count"},[t._v("0")])]),a("div",{staticClass:"other-stall"},[t._v("其他档口")])])},function(){var t=this,a=t._self._c;return a("div",{staticClass:"total-count-item"},[a("img",{attrs:{src:s("7578"),alt:""}}),a("div",{staticClass:"count-content"},[a("div",{staticClass:"title"},[t._v("未点餐人数")]),a("div",{staticClass:"count"},[t._v("0")])])])}],i=s("cbfb"),c={name:"TakeMealReport",components:{OrganizationSelect:i["a"]},props:{type:String,templateInfo:Object},data:function(){return{tableType:"total",groupId:"",mealStatus:"",dialogForm:{orgId:""},showDialog:!1}},methods:{changeType:function(t){this.tableType=t},openDialog:function(){this.showDialog=!0,this.dialogForm.orgId=this.templateInfo.jianceOrgId},confirmDialog:function(){this.showDialog=!1,this.templateInfo.jianceOrgId=this.dialogForm.orgId,this.$emit("comfirm",this.templateInfo)}}},o=c,r=(s("9434"),s("2877")),n=Object(r["a"])(o,e,l,!1,null,null,null);a["default"]=n.exports},7488:function(t,a,s){},9434:function(t,a,s){"use strict";s("7488")}}]);