(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-device-admin-ClientVersionList","view-super-device-admin-components-ClientVersionDialog"],{"073a":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("dialog-message",{attrs:{show:t.visible,title:t.title,showFooter:t.showFooter,loading:t.isLoading,customClass:"ps-dialog",width:t.width},on:{"update:show":function(e){t.visible=e},"update:loading":function(e){t.isLoading=e},close:t.handleClose}},[e("el-form",{ref:"dialogForm",staticClass:"dialog-form",attrs:{model:t.dialogForm,"status-icon":"",rules:t.dialogFormRules,"label-width":"125px"},nativeOn:{submit:function(t){t.preventDefault()}}},["push"===t.type?e("div",[e("el-form-item",{attrs:{label:"",prop:"","label-width":"43px"}},[e("div",{staticStyle:{"font-size":"16px","font-weight":"600"}},[t._v("更新说明")]),e("div",[t._v(t._s(t.info.directions))]),e("div",{staticStyle:{"font-size":"16px","font-weight":"600"}},[t._v("推送设置")])]),e("el-form-item",{attrs:{label:"升级方式：",prop:"update_type"}},[e("el-radio",{attrs:{label:"recommend"},model:{value:t.dialogForm.update_type,callback:function(e){t.$set(t.dialogForm,"update_type",e)},expression:"dialogForm.update_type"}},[t._v("推荐升级")]),e("el-radio",{attrs:{label:"enforce"},model:{value:t.dialogForm.update_type,callback:function(e){t.$set(t.dialogForm,"update_type",e)},expression:"dialogForm.update_type"}},[t._v("强制升级")])],1),e("el-form-item",{attrs:{label:"推送组织：",prop:"organizationIds"}},[e("organization-select",{staticClass:"search-item-w ps-input w-350",attrs:{placeholder:"请选择组织",isLazy:!1,multiple:!0,"check-strictly":!0,role:"super","append-to-body":!0,filterable:!1},on:{change:t.organizationChange},model:{value:t.dialogForm.organizationIds,callback:function(e){t.$set(t.dialogForm,"organizationIds",e)},expression:"dialogForm.organizationIds"}})],1),e("el-form-item",{attrs:{label:"推送设备：",prop:"deviceIds"}},[e("el-select",{staticClass:"ps-select w-350",attrs:{placeholder:"请选择推送设备","popper-class":"ps-popper-select",multiple:"","collapse-tags":""},model:{value:t.dialogForm.deviceIds,callback:function(e){t.$set(t.dialogForm,"deviceIds",e)},expression:"dialogForm.deviceIds"}},t._l(t.deviceList,(function(t){return e("el-option",{key:t.device_no,attrs:{label:t.device_name,value:t.device_no}})})),1)],1)],1):t._e(),"code"===t.type?e("div",{staticClass:"code-box"},[e("div",{staticClass:"code"},[e("qrcode",{staticClass:"face-img",attrs:{value:t.info.url,options:{width:280},margin:10,alt:""}}),e("div",{staticClass:"code-info-box"},[e("p",[t._v("v"+t._s(t.info.version_name))]),e("p",[e("el-button",{staticClass:"ps-btn",attrs:{disabled:t.isLoading,type:"primary"},on:{click:t.downCode}},[t._v(" 保存二维码 ")])],1)])],1)]):t._e()]),e("template",{slot:"tool"},[t.showFooter?e("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:t.isLoading},on:{click:t.clickCancleHandle}},[t._v(" 取消 ")]),e("el-button",{staticClass:"ps-btn",attrs:{disabled:t.isLoading,type:"primary"},on:{click:t.clickConfirmHandle}},[t._v(" 确定 ")])],1):t._e()]),e("div",{staticClass:"code-wrapper"},[e("div",{ref:"codeRef",staticClass:"weigh-code p-t-20"},[t.info.url?e("qrcode",{staticClass:"face-img",attrs:{value:t.info.url,options:t.codeOption,margin:5,alt:""}}):t._e(),e("div",{staticClass:"m-b-10"},[t._v("v"+t._s(t.info.name))]),e("div",{staticClass:"m-b-30"},[t._v("v"+t._s(t.info.version_name))])],1)])],2)},o=[],i=r("cbfb"),a=r("b2e5"),s=r.n(a),c=r("c0e9"),l=r.n(c),u=r("21a6"),f=r.n(u),d=r("ed08");function p(t){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},p(t)}function h(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */h=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function(t,e,r){return t[e]=r}}function u(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,a=Object.create(i.prototype),s=new T(n||[]);return o(a,"_invoke",{value:j(t,r,s)}),a}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=u;var d="suspendedStart",g="suspendedYield",y="executing",v="completed",m={};function b(){}function w(){}function _(){}var x={};l(x,a,(function(){return this}));var L=Object.getPrototypeOf,k=L&&L(L($([])));k&&k!==r&&n.call(k,a)&&(x=k);var S=_.prototype=b.prototype=Object.create(x);function O(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function C(t,e){function r(o,i,a,s){var c=f(t[o],t,i);if("throw"!==c.type){var l=c.arg,u=l.value;return u&&"object"==p(u)&&n.call(u,"__await")?e.resolve(u.__await).then((function(t){r("next",t,a,s)}),(function(t){r("throw",t,a,s)})):e.resolve(u).then((function(t){l.value=t,a(l)}),(function(t){return r("throw",t,a,s)}))}s(c.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function j(e,r,n){var o=d;return function(i,a){if(o===y)throw Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var s=n.delegate;if(s){var c=E(s,n);if(c){if(c===m)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===d)throw o=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=y;var l=f(e,r,n);if("normal"===l.type){if(o=n.done?v:g,l.arg===m)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(o=v,n.method="throw",n.arg=l.arg)}}}function E(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,E(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,m;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function F(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function P(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(F,this),this.reset(!0)}function $(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(p(e)+" is not iterable")}return w.prototype=_,o(S,"constructor",{value:_,configurable:!0}),o(_,"constructor",{value:w,configurable:!0}),w.displayName=l(_,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,l(t,c,"GeneratorFunction")),t.prototype=Object.create(S),t},e.awrap=function(t){return{__await:t}},O(C.prototype),l(C.prototype,s,(function(){return this})),e.AsyncIterator=C,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new C(u(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},O(S),l(S,c,"Generator"),l(S,a,(function(){return this})),l(S,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=$,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(P),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return s.type="throw",s.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),l=n.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),P(r),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;P(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:$(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),m}},e}function g(t,e){return w(t)||b(t,e)||v(t,e)||y()}function y(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function v(t,e){if(t){if("string"==typeof t)return m(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?m(t,e):void 0}}function m(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function b(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,s=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(s.push(n.value),s.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return s}}function w(t){if(Array.isArray(t))return t}function _(t,e,r,n,o,i,a){try{var s=t[i](a),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,o)}function x(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){_(i,n,o,a,s,"next",t)}function s(t){_(i,n,o,a,s,"throw",t)}a(void 0)}))}}var L={name:"trayDialog",components:{OrganizationSelect:i["a"],qrcode:s.a},props:{loading:Boolean,isshow:Boolean,showFooter:{type:Boolean,default:!0},type:{type:String,default:""},title:{type:String,default:""},width:{type:String,default:"600px"},info:{type:Object,default:function(){return{}}},deviceTypeList:{type:Array,default:function(){return[]}},confirm:Function},data:function(){return{isLoading:!1,dialogForm:{organizationIds:[],deviceIds:[],deviceName:"",deviceType:"",deviceModel:"",SNCode:"",update_type:"recommend"},dialogFormRules:{organizationIds:[{required:!0,message:"请选择推送组织",trigger:"blur"}],deviceIds:[{required:!0,message:"请选择推送设备",trigger:"blur"}]},deviceList:[],codeOption:{errorCorrectionLevel:"H",width:280}}},computed:{visible:{get:function(){return this.isshow},set:function(t){this.$emit("update:isshow",t)}}},watch:{visible:function(t){this.visible&&this.type}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){},clickConfirmHandle:function(){var t=this;this.$refs.dialogForm.validate((function(e){if(e){var r={};switch(t.type){case"push":r={version_id:t.info.id,organization_ids:t.dialogForm.organizationIds,device_ids:t.dialogForm.deviceIds,update_type:t.dialogForm.update_type},t.pushDeviceHandle(r);break}}}))},pushDeviceHandle:function(t){var e=this;return x(h().mark((function r(){var n,o,i,a;return h().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return e.isLoading=!0,r.next=3,Object(d["Z"])(e.$apis.apiBackgroundAdminDeviceVersionSendClientVersionPost(t));case 3:if(n=r.sent,o=g(n,2),i=o[0],a=o[1],e.isLoading=!1,!i){r.next=11;break}return e.$message.error(i.message),r.abrupt("return");case 11:0===a.code?(e.$message.success("成功"),e.confirm()):e.$message.error(a.msg);case 12:case"end":return r.stop()}}),r)})))()},downCode:function(){var t=this,e=this.$refs.codeRef;l()(e).then((function(e){var r=e.toDataURL();f.a.saveAs(r,t.info.name+"-v"+t.info.version_name+".png"),t.confirm()})),this.isLoading=!1},clickCancleHandle:function(){this.visible=!1},handleClose:function(t){this.$refs.dialogForm.resetFields(),this.isLoading=!1,this.visible=!1},normalizer:function(t){return{id:t.id,label:t.name,children:t.children_list}},organizationChange:function(t){this.getDeviceList()},getDeviceList:function(){var t=this;return x(h().mark((function e(){var r;return h().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.apiBackgroundAdminDeviceVersionDeviceListPost({device_type:t.$route.query.model,organization_ids:t.dialogForm.organizationIds,page:1,page_size:999999});case 2:r=e.sent,0===r.code?t.deviceList=r.data.results:t.$message.error(r.msg);case 4:case"end":return e.stop()}}),e)})))()}}},k=L,S=(r("78fc"),r("2877")),O=Object(S["a"])(k,n,o,!1,null,"1ca9a367",null);e["default"]=O.exports},"27d8":function(t,e,r){},"78fc":function(t,e,r){"use strict";r("a8c2")},"9cf4":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"clientVersion-list container-wrapper"},[e("refresh-tool",{attrs:{"show-refresh":!1}}),e("div",{staticClass:"searchref_top m-b-20"},t._l(t.tabsList,(function(r,n){return e("el-button",{key:n,class:{active:r.type===t.currentType},on:{click:function(e){return t.tabHandler(r)}}},[t._v(" "+t._s(r.name)+" ")])})),1),e("search-form",{ref:"searchRef",attrs:{title:t.$route.query.name,"form-setting":t.searchFormSetting},on:{search:t.searchHandle}}),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v(t._s(t.$route.query.name))]),e("div",{staticClass:"align-r"},["version"===t.currentType?e("button-icon",{attrs:{color:"origin"},on:{click:t.addVersionHandle}},[t._v("创建")]):t._e()],1)]),e("div",{staticClass:"table-content"},["version"===t.currentType?e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{border:"",data:t.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},t._l(t.versionTableSetting,(function(r){return e("table-column",{key:r.key,attrs:{col:r},scopedSlots:t._u([{key:"operate",fn:function(r){var n=r.row;return[e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.gotoModifyPage(n)}}},[t._v("编辑")]),e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHandle("single",n)}}},[t._v("删除")])]}}],null,!0)})})),1):e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{border:"",data:t.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},t._l(t.pushTableSetting,(function(r){return e("table-column",{key:r.key,attrs:{col:r},scopedSlots:t._u([{key:"device",fn:function(r){r.row;return[e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"}},[t._v("查看")])]}},{key:"status",fn:function(r){var n=r.row;return[e("el-switch",{attrs:{"active-color":"#ff9b45"},model:{value:n.status,callback:function(e){t.$set(n,"status",e)},expression:"row.status"}})]}},{key:"operate",fn:function(r){var n=r.row;return[e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.openDialog("code",n)}}},[t._v("生成链接")]),e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.openDialog("push",n)}}},[t._v("推送版本")]),e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.downloadHandle(n)}}},[t._v("下载")])]}}],null,!0)})})),1)],1),e("pagination",{attrs:{onPaginationChange:t.onPaginationChange,"current-page":t.currentPage,"page-size":t.pageSize,layout:"total, prev, pager, next, jumper",total:t.totalCount},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"update:pageSize":function(e){t.pageSize=e},"update:page-size":function(e){t.pageSize=e}}})],1),e("client-version-dialog",{attrs:{isshow:t.dialogVisible,title:t.dialogTitle,type:t.dialogType,width:t.dialogWidth,"show-footer":t.showFooter,info:t.dialogInfo,confirm:t.initLoad},on:{"update:isshow":function(e){t.dialogVisible=e}}})],1)},o=[],i=r("ed08"),a=r("073a");function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function d(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,a=Object.create(i.prototype),s=new T(n||[]);return o(a,"_invoke",{value:j(t,r,s)}),a}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=d;var h="suspendedStart",g="suspendedYield",y="executing",v="completed",m={};function b(){}function w(){}function _(){}var x={};f(x,a,(function(){return this}));var L=Object.getPrototypeOf,k=L&&L(L($([])));k&&k!==r&&n.call(k,a)&&(x=k);var S=_.prototype=b.prototype=Object.create(x);function O(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function C(t,e){function r(o,i,a,c){var l=p(t[o],t,i);if("throw"!==l.type){var u=l.arg,f=u.value;return f&&"object"==s(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,a,c)}),(function(t){r("throw",t,a,c)})):e.resolve(f).then((function(t){u.value=t,a(u)}),(function(t){return r("throw",t,a,c)}))}c(l.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function j(e,r,n){var o=h;return function(i,a){if(o===y)throw Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var s=n.delegate;if(s){var c=E(s,n);if(c){if(c===m)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===h)throw o=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=y;var l=p(e,r,n);if("normal"===l.type){if(o=n.done?v:g,l.arg===m)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(o=v,n.method="throw",n.arg=l.arg)}}}function E(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,E(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var i=p(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,m;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function F(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function P(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(F,this),this.reset(!0)}function $(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(s(e)+" is not iterable")}return w.prototype=_,o(S,"constructor",{value:_,configurable:!0}),o(_,"constructor",{value:w,configurable:!0}),w.displayName=f(_,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,f(t,u,"GeneratorFunction")),t.prototype=Object.create(S),t},e.awrap=function(t){return{__await:t}},O(C.prototype),f(C.prototype,l,(function(){return this})),e.AsyncIterator=C,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new C(d(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},O(S),f(S,u,"Generator"),f(S,a,(function(){return this})),f(S,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=$,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(P),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return s.type="throw",s.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),l=n.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),P(r),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;P(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:$(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),m}},e}function l(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function u(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?l(Object(r),!0).forEach((function(e){f(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function f(t,e,r){return(e=d(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function d(t){var e=p(t,"string");return"symbol"==s(e)?e:e+""}function p(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function h(t,e){return b(t)||m(t,e)||y(t,e)||g()}function g(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function y(t,e){if(t){if("string"==typeof t)return v(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?v(t,e):void 0}}function v(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function m(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,s=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(s.push(n.value),s.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return s}}function b(t){if(Array.isArray(t))return t}function w(t,e,r,n,o,i,a){try{var s=t[i](a),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,o)}function _(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){w(i,n,o,a,s,"next",t)}function s(t){w(i,n,o,a,s,"throw",t)}a(void 0)}))}}var x={name:"ClientVersion",components:{ClientVersionDialog:a["default"]},data:function(){return{isLoading:!1,type:"",pageSize:10,totalCount:0,currentPage:1,currentType:"version",tabsList:[{type:"version",name:"版本更新"},{type:"push",name:"版本推送"}],searchFormSetting:{name:{value:"",label:"版本名称",type:"input"},version_name:{value:"",label:"版本号",type:"input"}},versionTableSetting:[{label:"创建时间",key:"create_time"},{label:"版本名称",key:"name"},{label:"版本号",key:"version_name"},{key:"operate",label:"操作",type:"slot",slotName:"operate",fixed:"right"}],pushTableSetting:[{label:"创建时间",key:"create_time"},{label:"版本名称",key:"name"},{label:"版本号",key:"version_name"},{label:"状态",key:"status_alias"},{label:"推送时间",key:"start_time"},{label:"操作",key:"operate",type:"slot",slotName:"operate",fixed:"right"}],tableData:[],dialogVisible:!1,dialogTitle:"",dialogType:"",dialogInfo:{},showFooter:!1,dialogWidth:"600px"}},created:function(){this.type=this.$route.query.model,this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.dialogVisible=!1,this.getTableDataList()},searchHandle:Object(i["d"])((function(){this.currentPage=1,this.getTableDataList()}),300),tabHandler:function(t){this.currentType=t.type},getTableDataList:function(){var t=this;return _(c().mark((function e(){var r,n,o,a,s;return c().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,r=Object(i["w"])(t.searchFormSetting,t.currentPage,t.pageSize),e.next=4,Object(i["Z"])(t.$apis.apiBackgroundAdminDeviceVersionListPost(u(u({},r),{},{device_type:t.type})));case 4:if(n=e.sent,o=h(n,2),a=o[0],s=o[1],t.isLoading=!1,!a){e.next=12;break}return t.$message.error(a.message),e.abrupt("return");case 12:0===s.code?(t.tableData=s.data.results,t.totalCount=s.data.count):(t.tableData=[],t.$message.error(s.msg));case 13:case"end":return e.stop()}}),e)})))()},onPaginationChange:function(t){this.currentPage=t.current,this.pageSize=t.pageSize,this.getTableDataList()},addVersionHandle:function(){this.$router.push({name:"SuperAddClientVersion",query:u({type:"add"},this.$route.query)})},gotoModifyPage:function(t){this.$router.push({name:"SuperAddClientVersion",query:u(u({type:"modify"},this.$route.query),{},{data:this.$encodeQuery(t)})})},deleteHandle:function(t,e){var r=this;this.$confirm("确定删除吗?","提示",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var t=_(c().mark((function t(n,o,i){var a,s,l,u,f,d;return c().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if("confirm"!==n){t.next=20;break}return o.confirmButtonLoading=!0,o.cancelButtonLoading=!0,a=[e.id],s={ids:a},t.next=7,r.$to(r.$apis.apiBackgroundAdminDeviceVersionDeletePost(s));case 7:if(l=t.sent,u=h(l,2),f=u[0],d=u[1],o.confirmButtonLoading=!1,o.cancelButtonLoading=!1,!f){t.next=16;break}return r.$message.error(f.message),t.abrupt("return");case 16:0===d.code?(i(),r.$message.success(d.msg),r.getTableDataList()):r.$message.error(d.msg),o.confirmButtonLoading=!1,t.next=21;break;case 20:o.confirmButtonLoading||i();case 21:case"end":return t.stop()}}),t)})));function n(e,r,n){return t.apply(this,arguments)}return n}()}).then((function(t){})).catch((function(t){}))},downloadHandle:function(t){Object(i["j"])(t.url)},openDialog:function(t,e){this.dialogType=t,this.dialogInfo=e,"code"===t&&(this.showFooter=!1,this.dialogWidth="400px"),"push"===t&&(this.showFooter=!0,this.dialogTitle="推送版本",this.dialogWidth="600px"),this.dialogVisible=!0}}},L=x,k=(r("c015"),r("2877")),S=Object(k["a"])(L,n,o,!1,null,"2fe9d83d",null);e["default"]=S.exports},a8c2:function(t,e,r){},c015:function(t,e,r){"use strict";r("27d8")}}]);