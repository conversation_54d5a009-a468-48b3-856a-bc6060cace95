(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-accessControl-attendance-attendance-components-GroupAdministrators"],{"2fa22":function(t,e,n){"use strict";n("91a1")},"91a1":function(t,e,n){},d633:function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t._self._c;return e("div",{staticClass:"GroupAdministrators container-wrapper"},[e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("数据列表")]),e("div",{staticClass:"align-r"},[e("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_attendance.attendance_group_admin.add"],expression:"['background_attendance.attendance_group_admin.add']"}],attrs:{color:"origin",type:"add"},on:{click:function(e){return t.openDialog("addGroupAdministrators")}}},[t._v("新建")])],1)]),e("div",{staticClass:"table-content"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},[e("el-table-column",{attrs:{prop:"name",label:"名称",align:"center"}}),e("el-table-column",{key:"phone",attrs:{prop:"phone",label:"手机号",align:"center"},scopedSlots:t._u([{key:"default",fn:function(n){return[e("span",[t._v(t._s(t.sensitiveSetting.phone?n.row.phone:"****"))])]}}])}),e("el-table-column",{attrs:{prop:"attendance_groups",label:"管理考勤组",align:"center"}}),e("el-table-column",{attrs:{prop:"remark",label:"备注",align:"center"}}),e("el-table-column",{attrs:{fixed:"right",label:"操作",align:"center"},scopedSlots:t._u([{key:"default",fn:function(n){return[e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_attendance.attendance_group_admin.modify"],expression:"['background_attendance.attendance_group_admin.modify']"}],staticClass:"ps-bule",attrs:{type:"text",size:"small"},on:{click:function(e){return t.openDialog("editGroupAdministrators",n.row)}}},[t._v("编辑")]),e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_attendance.attendance_group_admin.delete"],expression:"['background_attendance.attendance_group_admin.delete']"}],staticClass:"ps-warn",attrs:{type:"text",size:"small"},on:{click:function(e){return t.delPushSetting(n.row.id)}}},[t._v("删除")])]}}])})],1)],1),e("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[e("el-pagination",{staticClass:"ps-text",attrs:{"current-page":t.currentPage,"page-size":t.pageSize,layout:"total, prev, pager, next",total:t.totalCount,background:"","popper-class":"ps-popper-select"},on:{"current-change":t.handleCurrentChange}})],1)]),e("attendance-group-dialog",{attrs:{isshow:t.dialogVisible,title:t.dialogTitle,type:t.dialogType,width:t.dialogWidth,"select-info":t.selectInfo},on:{"update:isshow":function(e){t.dialogVisible=e},confirm:t.searchHandle}})],1)},a=[],o=n("ed08"),i=n("73f7");function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,a=Object.defineProperty||function(t,e,n){t[e]=n.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",u=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function p(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{p({},"")}catch(t){p=function(t,e,n){return t[e]=n}}function d(t,e,n,r){var o=e&&e.prototype instanceof b?e:b,i=Object.create(o.prototype),s=new j(r||[]);return a(i,"_invoke",{value:E(t,n,s)}),i}function f(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=d;var h="suspendedStart",g="suspendedYield",m="executing",v="completed",y={};function b(){}function w(){}function _(){}var x={};p(x,i,(function(){return this}));var L=Object.getPrototypeOf,k=L&&L(L(T([])));k&&k!==n&&r.call(k,i)&&(x=k);var S=_.prototype=b.prototype=Object.create(x);function C(t){["next","throw","return"].forEach((function(e){p(t,e,(function(t){return this._invoke(e,t)}))}))}function G(t,e){function n(a,o,i,c){var u=f(t[a],t,o);if("throw"!==u.type){var l=u.arg,p=l.value;return p&&"object"==s(p)&&r.call(p,"__await")?e.resolve(p.__await).then((function(t){n("next",t,i,c)}),(function(t){n("throw",t,i,c)})):e.resolve(p).then((function(t){l.value=t,i(l)}),(function(t){return n("throw",t,i,c)}))}c(u.arg)}var o;a(this,"_invoke",{value:function(t,r){function a(){return new e((function(e,a){n(t,r,e,a)}))}return o=o?o.then(a,a):a()}})}function E(e,n,r){var a=h;return function(o,i){if(a===m)throw Error("Generator is already running");if(a===v){if("throw"===o)throw i;return{value:t,done:!0}}for(r.method=o,r.arg=i;;){var s=r.delegate;if(s){var c=A(s,r);if(c){if(c===y)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===h)throw a=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=m;var u=f(e,n,r);if("normal"===u.type){if(a=r.done?v:g,u.arg===y)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(a=v,r.method="throw",r.arg=u.arg)}}}function A(e,n){var r=n.method,a=e.iterator[r];if(a===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,A(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),y;var o=f(a,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,y;var i=o.arg;return i?i.done?(n[e.resultName]=i.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,y):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,y)}function P(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function j(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(P,this),this.reset(!0)}function T(e){if(e||""===e){var n=e[i];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,o=function n(){for(;++a<e.length;)if(r.call(e,a))return n.value=e[a],n.done=!1,n;return n.value=t,n.done=!0,n};return o.next=o}}throw new TypeError(s(e)+" is not iterable")}return w.prototype=_,a(S,"constructor",{value:_,configurable:!0}),a(_,"constructor",{value:w,configurable:!0}),w.displayName=p(_,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,p(t,l,"GeneratorFunction")),t.prototype=Object.create(S),t},e.awrap=function(t){return{__await:t}},C(G.prototype),p(G.prototype,u,(function(){return this})),e.AsyncIterator=G,e.async=function(t,n,r,a,o){void 0===o&&(o=Promise);var i=new G(d(t,n,r,a),o);return e.isGeneratorFunction(n)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},C(S),p(S,l,"Generator"),p(S,i,(function(){return this})),p(S,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=T,j.prototype={constructor:j,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function a(r,a){return s.type="throw",s.arg=e,n.next=r,a&&(n.method="next",n.arg=t),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],s=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var c=r.call(i,"catchLoc"),u=r.call(i,"finallyLoc");if(c&&u){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,y):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),O(n),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var a=r.arg;O(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:T(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),y}},e}function u(t,e,n,r,a,o,i){try{var s=t[o](i),c=s.value}catch(t){return void n(t)}s.done?e(c):Promise.resolve(c).then(r,a)}function l(t){return function(){var e=this,n=arguments;return new Promise((function(r,a){var o=t.apply(e,n);function i(t){u(o,r,a,i,s,"next",t)}function s(t){u(o,r,a,i,s,"throw",t)}i(void 0)}))}}var p={name:"GroupAdministrators",components:{AttendanceGroupDialog:i["default"]},props:{},data:function(){return{isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[],dialogVisible:!1,dialogTitle:"",dialogType:"",dialogWidth:"",selectInfo:{},sensitiveSetting:{}}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getSensitiveSetting(),this.getGroupAdministrators()},searchHandle:Object(o["d"])((function(){this.dialogVisible=!1,this.currentPage=1,this.getGroupAdministrators()}),300),refreshHandle:function(){this.currentPage=1,this.tableData=[],this.initLoad()},getGroupAdministrators:function(){var t=this;return l(c().mark((function e(){var n;return c().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.$apis.apiBackgroundAttendanceAttendanceGroupAdminListPost({page:t.currentPage,page_size:t.pageSize});case 3:n=e.sent,t.isLoading=!1,0===n.code?(t.tableData=n.data.results,t.tableData.map((function(t){t.attendance_groups=[],t.attendance_groups_ids=[],t.attendance_groups_list.map((function(e){t.attendance_groups.push(e.name),t.attendance_groups_ids.push(e.id)})),t.attendance_groups=t.attendance_groups.join(",")})),t.totalCount=n.data.count):t.$message.error(n.msg);case 6:case"end":return e.stop()}}),e)})))()},getSensitiveSetting:function(){var t=this;return l(c().mark((function e(){var n;return c().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.$apis.apiBackgroundReportCenterDataReportGetSensitiveSettingPost();case 3:n=e.sent,t.isLoading=!1,0===n.code?t.sensitiveSetting=n.data:t.$message.error(n.msg);case 6:case"end":return e.stop()}}),e)})))()},handleSizeChange:function(t){this.pageSize=t,this.getGroupAdministrators()},handleCurrentChange:function(t){this.currentPage=t,this.getGroupAdministrators()},delPushSetting:function(t){var e=this;return l(c().mark((function n(){return c().wrap((function(n){while(1)switch(n.prev=n.next){case 0:e.$confirm("确定删除该考勤组管理员？","提示",{confirmButtonText:e.$t("dialog.confirm_btn"),cancelButtonText:e.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var n=l(c().mark((function n(r,a,o){var i;return c().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if("confirm"!==r){n.next=9;break}return n.next=3,e.$apis.apiBackgroundAttendanceAttendanceGroupAdminDeletePost({ids:[t]});case 3:i=n.sent,0===i.code?(e.$message.success("删除成功"),e.getGroupAdministrators()):e.$message.error(i.msg),o(),a.confirmButtonLoading=!1,n.next=10;break;case 9:a.confirmButtonLoading||o();case 10:case"end":return n.stop()}}),n)})));function r(t,e,r){return n.apply(this,arguments)}return r}()}).then((function(t){})).catch((function(t){}));case 1:case"end":return n.stop()}}),n)})))()},openDialog:function(t,e){this.dialogType=t,this.selectInfo=e,"addGroupAdministrators"===t?(this.dialogTitle="新增考勤组管理员",this.dialogWidth="400px"):"editGroupAdministrators"===t&&(this.dialogTitle="编辑考勤组管理员",this.dialogWidth="400px"),this.dialogVisible=!0}}},d=p,f=(n("2fa22"),n("2877")),h=Object(f["a"])(d,r,a,!1,null,"e36a7b0c",null);e["default"]=h.exports}}]);