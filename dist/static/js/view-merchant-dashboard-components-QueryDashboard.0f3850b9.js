(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-dashboard-components-QueryDashboard"],{"00bf":function(e,t,r){"use strict";r.r(t);var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"smartCanteenNutrition"},[e._m(0),t("div",{staticClass:"chart"},[t("div",{staticClass:"center"},[t("div",{staticClass:"block h-770"},[t("div",{staticClass:"title"},[e._v("餐段")]),t("div",[t("el-button",{staticClass:"ps-bule block-btn",attrs:{type:"text"},on:{click:function(t){return e.openDialog("menu")}}},[e._v(" 编辑 ")])],1)])])]),t("el-dialog",{attrs:{title:"选择组织",visible:e.showDialog,width:"500px","custom-class":"ps-dialog"},on:{"update:visible":function(t){e.showDialog=t}}},[t("el-form",{ref:"dialogFormRef",staticClass:"dialog-form",attrs:{model:e.dialogForm,"label-width":"120px",rules:e.dialogFormRules}},[t("el-form-item",{attrs:{label:"显示组织：",prop:"orgId"}},[t("organization-select",{staticClass:"search-item-w ps-input w-250",attrs:{placeholder:"请选择所属组织",isLazy:!1,multiple:!1,"check-strictly":!0,"append-to-body":!0},on:{change:e.changeOrg},model:{value:e.dialogForm.orgId,callback:function(t){e.$set(e.dialogForm,"orgId",t)},expression:"dialogForm.orgId"}})],1),t("el-form-item",{attrs:{label:"菜谱："}},[t("el-radio-group",{staticClass:"ps-radio",on:{change:e.changeMenuType},model:{value:e.dialogForm.menuType,callback:function(t){e.$set(e.dialogForm,"menuType",t)},expression:"dialogForm.menuType"}},[t("el-radio",{attrs:{label:"week"}},[e._v("周菜谱")]),t("el-radio",{attrs:{label:"month"}},[e._v("月菜谱")])],1)],1),t("el-form-item",{attrs:{label:"设备类型：",prop:"deviceType"}},[t("el-select",{staticClass:"ps-select w-250",attrs:{multiple:"",placeholder:"请选择设备类型","popper-class":"ps-popper-select"},on:{change:e.deviceTypeChange},model:{value:e.dialogForm.deviceType,callback:function(t){e.$set(e.dialogForm,"deviceType",t)},expression:"dialogForm.deviceType"}},e._l(e.deviceList,(function(e){return t("el-option",{key:e.key,attrs:{label:e.name,value:e.key}})})),1)],1),t("el-form-item",{attrs:{label:"设备型号：",prop:e.isDisabledModel?"":"deviceModel"}},[t("el-select",{staticClass:"ps-select w-250",attrs:{multiple:"",placeholder:"请选择设备型号","popper-class":"ps-popper-select",disabled:e.isDisabledModel},on:{change:e.deviceModelChange},model:{value:e.dialogForm.deviceModel,callback:function(t){e.$set(e.dialogForm,"deviceModel",t)},expression:"dialogForm.deviceModel"}},e._l(e.deviceModelList,(function(e){return t("el-option",{key:e.key,attrs:{label:e.key,value:e.key}})})),1)],1),t("el-form-item",{attrs:{label:"菜谱：",prop:"menuId"}},[t("el-select",{staticClass:"ps-select w-250",model:{value:e.dialogForm.menuId,callback:function(t){e.$set(e.dialogForm,"menuId",t)},expression:"dialogForm.menuId"}},e._l(e.menuList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-cancel-btn",on:{click:function(t){e.showDialog=!1}}},[e._v("取 消")]),t("el-button",{staticClass:"ps-btn",attrs:{type:"primary"},on:{click:e.confirmDialog}},[e._v("确 定")])],1)],1)],1)},n=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"header"},[t("div",{staticClass:"title"},[e._v("查询大屏")]),t("div",{staticClass:"time"},[e._v("2024年01月08日 09:55:11 星期一")])])}],i=r("cbfb");function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function c(e){return d(e)||u(e)||l(e)||s()}function s(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function l(e,t){if(e){if("string"==typeof e)return h(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?h(e,t):void 0}}function u(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function d(e){if(Array.isArray(e))return h(e)}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,o=Array(t);r<t;r++)o[r]=e[r];return o}function f(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */f=function(){return t};var e,t={},r=Object.prototype,o=r.hasOwnProperty,n=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},c=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function d(e,t,r,o){var i=t&&t.prototype instanceof b?t:b,a=Object.create(i.prototype),c=new O(o||[]);return n(a,"_invoke",{value:_(e,r,c)}),a}function h(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var p="suspendedStart",m="suspendedYield",g="executing",v="completed",y={};function b(){}function w(){}function F(){}var L={};u(L,c,(function(){return this}));var I=Object.getPrototypeOf,k=I&&I(I(S([])));k&&k!==r&&o.call(k,c)&&(L=k);var M=F.prototype=b.prototype=Object.create(L);function T(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,t){function r(n,i,c,s){var l=h(e[n],e,i);if("throw"!==l.type){var u=l.arg,d=u.value;return d&&"object"==a(d)&&o.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,c,s)}),(function(e){r("throw",e,c,s)})):t.resolve(d).then((function(e){u.value=e,c(u)}),(function(e){return r("throw",e,c,s)}))}s(l.arg)}var i;n(this,"_invoke",{value:function(e,o){function n(){return new t((function(t,n){r(e,o,t,n)}))}return i=i?i.then(n,n):n()}})}function _(t,r,o){var n=p;return function(i,a){if(n===g)throw Error("Generator is already running");if(n===v){if("throw"===i)throw a;return{value:e,done:!0}}for(o.method=i,o.arg=a;;){var c=o.delegate;if(c){var s=D(c,o);if(s){if(s===y)continue;return s}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(n===p)throw n=v,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);n=g;var l=h(t,r,o);if("normal"===l.type){if(n=o.done?v:m,l.arg===y)continue;return{value:l.arg,done:o.done}}"throw"===l.type&&(n=v,o.method="throw",o.arg=l.arg)}}}function D(t,r){var o=r.method,n=t.iterator[o];if(n===e)return r.delegate=null,"throw"===o&&t.iterator.return&&(r.method="return",r.arg=e,D(t,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+o+"' method")),y;var i=h(n,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,y;var a=i.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,y):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function C(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(C,this),this.reset(!0)}function S(t){if(t||""===t){var r=t[c];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function r(){for(;++n<t.length;)if(o.call(t,n))return r.value=t[n],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(a(t)+" is not iterable")}return w.prototype=F,n(M,"constructor",{value:F,configurable:!0}),n(F,"constructor",{value:w,configurable:!0}),w.displayName=u(F,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,F):(e.__proto__=F,u(e,l,"GeneratorFunction")),e.prototype=Object.create(M),e},t.awrap=function(e){return{__await:e}},T(x.prototype),u(x.prototype,s,(function(){return this})),t.AsyncIterator=x,t.async=function(e,r,o,n,i){void 0===i&&(i=Promise);var a=new x(d(e,r,o,n),i);return t.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},T(M),u(M,l,"Generator"),u(M,c,(function(){return this})),u(M,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var o in t)r.push(o);return r.reverse(),function e(){for(;r.length;){var o=r.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},t.values=S,O.prototype={constructor:O,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(E),!t)for(var r in this)"t"===r.charAt(0)&&o.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(o,n){return c.type="throw",c.arg=t,r.next=o,n&&(r.method="next",r.arg=e),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var s=o.call(a,"catchLoc"),l=o.call(a,"finallyLoc");if(s&&l){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&o.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),E(r),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var o=r.completion;if("throw"===o.type){var n=o.arg;E(r)}return n}}throw Error("illegal catch attempt")},delegateYield:function(t,r,o){return this.delegate={iterator:S(t),resultName:r,nextLoc:o},"next"===this.method&&(this.arg=e),y}},t}function p(e,t,r,o,n,i,a){try{var c=e[i](a),s=c.value}catch(e){return void r(e)}c.done?t(s):Promise.resolve(s).then(o,n)}function m(e){return function(){var t=this,r=arguments;return new Promise((function(o,n){var i=e.apply(t,r);function a(e){p(i,o,n,a,c,"next",e)}function c(e){p(i,o,n,a,c,"throw",e)}a(void 0)}))}}var g={name:"management",components:{OrganizationSelect:i["a"]},props:{type:String,cameraList:Array,templateInfo:[Object,Array]},data:function(){return{dialogType:"",showDialog:!1,dialogForm:{orgId:"",menuType:"",deviceType:[],deviceModel:[],menuId:""},dialogFormRules:{orgId:[{required:!0,message:"请选择显示组织",trigger:"blur"}],deviceType:[{required:!0,message:"请选择设备类型",trigger:"blur"}],deviceModel:[{required:!0,message:"请选择设备型号",trigger:"blur"}],menuId:[{required:!0,message:"请选择菜谱",trigger:"blur"}]},deviceList:[],deviceModelList:[],isDisabledModel:!1,menuList:[],dataInfo:{menuType:"week",deviceType:"",deviceModel:"",menuId:"",orgId:""}}},created:function(){"edit"===this.type&&(this.dataInfo=this.templateInfo),this.getOrgDeviceList()},methods:{openDialog:function(e){this.dialogType=e,this.showDialog=!0,this.dialogForm.orgId=this.dataInfo.orgId,this.dialogForm.menuType=this.dataInfo.menuType,this.dialogForm.deviceType=this.dataInfo.deviceType,this.dialogForm.deviceModel=this.dataInfo.deviceModel,this.dialogForm.menuId=this.dataInfo.menuId,this.dialogForm.deviceType.length?this.getDeviceModel():this.isDisabledModel=!1,this.checkGetMemuList()},confirmDialog:function(){var e=this;this.$refs.dialogFormRef.validate((function(t){t&&(e.dataInfo.orgId=e.dialogForm.orgId,e.dataInfo.menuType=e.dialogForm.menuType,e.dataInfo.deviceType=e.dialogForm.deviceType,e.dataInfo.deviceModel=e.dialogForm.deviceModel,e.dataInfo.menuId=e.dialogForm.menuId,e.$refs.dialogFormRef.clearValidate(),e.dialogForm={orgId:"",menuType:"",deviceType:[],deviceModel:[],menuId:""},e.showDialog=!1,e.$emit("comfirm",e.dataInfo))}))},changeOrg:function(){this.dialogForm.menuId="",this.menuList=[],this.checkGetMemuList()},changeMenuType:function(){this.dialogForm.menuId="",this.menuList=[],this.checkGetMemuList()},deviceTypeChange:function(){this.dialogForm.menuId="",this.menuList=[],this.dialogForm.deviceModel=[],this.deviceModelList=[],this.dialogForm.deviceType.length?this.getDeviceModel():this.isDisabledModel=!1,this.checkGetMemuList()},deviceModelChange:function(){this.dialogForm.menuId="",this.menuList=[],this.checkGetMemuList()},checkGetMemuList:function(){this.dialogForm.deviceType.length&&this.dialogForm.orgId&&(-1!==this.dialogForm.deviceType.indexOf("H5")||-1!==this.dialogForm.deviceType.indexOf("MAPP")||this.dialogForm.deviceModel.length)&&this.getMenuList()},getMenuList:function(){var e=this;return m(f().mark((function t(){var r;return f().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundDatascreenKanbanTempGetKanbanSettingPost({organization_id:[e.dialogForm.orgId],device_type:e.dialogForm.deviceType,device_model:e.dialogForm.deviceModel,menu_type:e.dialogForm.menuType});case 2:r=t.sent,0===r.code?e.menuList=r.data:e.$message.error(r.msg);case 4:case"end":return t.stop()}}),t)})))()},getOrgDeviceList:function(){var e=this;return m(f().mark((function t(){var r;return f().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundAdminDeviceDeviceTypePost({source:"self"});case 2:r=t.sent,0===r.code?e.deviceList=[{name:"H5",key:"H5"},{name:"小程序",key:"MAPP"}].concat(c(r.data)):e.$message.error(r.msg);case 4:case"end":return t.stop()}}),t)})))()},getDeviceModel:function(){var e=this;return m(f().mark((function t(){var r,o;return f().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(r=e.dialogForm.deviceType.filter((function(e){return"H5"!==e&&"MAPP"!==e})),r.length){t.next=6;break}return e.isDisabledModel=!0,t.abrupt("return");case 6:e.isDisabledModel=!1;case 7:return t.next=9,e.$apis.apiBackgroundAdminDeviceDeviceModelPost({device_types:r});case 9:o=t.sent,0===o.code?e.deviceModelList=o.data:e.$message.error(o.msg);case 11:case"end":return t.stop()}}),t)})))()}}},v=g,y=(r("4cb6"),r("2877")),b=Object(y["a"])(v,o,n,!1,null,null,null);t["default"]=b.exports},"4cb6":function(e,t,r){"use strict";r("e9cfe")},e9cfe:function(e,t,r){}}]);