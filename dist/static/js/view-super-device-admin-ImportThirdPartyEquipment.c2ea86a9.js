(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-device-admin-ImportThirdPartyEquipment"],{"5ad1":function(e,t,i){},"81b3":function(e,t,i){"use strict";i("5ad1")},d22a:function(e,t,i){"use strict";i.r(t);var r=function(){var e=this,t=e._self._c;return t("div",[t("refresh-tool",{attrs:{title:e.title,"show-refresh":!1}}),t("import-page",{staticClass:"importPage",attrs:{initial:e.initial,url:e.url,"header-len":e.headerLen,"template-url":e.templateUrl}})],1)},a=[],n={name:"ImportIngredients",data:function(){return{type:"import",title:"批量第三方设备列表",headerLen:1,initial:!0,url:"apiBackgroundDeviceAdminThirdDeviceThirdDeviceBatchAddPost",templateUrl:"/api/temporary/template_excel/third_device_import.xlsx"}},computed:{},watch:{},created:function(){},mounted:function(){},methods:{}},l=n,d=(i("81b3"),i("2877")),o=Object(d["a"])(l,r,a,!1,null,null,null);t["default"]=o.exports}}]);