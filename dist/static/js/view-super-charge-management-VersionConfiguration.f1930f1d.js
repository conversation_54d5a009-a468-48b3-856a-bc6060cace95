(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-charge-management-VersionConfiguration"],{"2f99":function(e,t,s){},3658:function(e,t,s){"use strict";s.r(t);var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"container-wrapper",staticStyle:{height:"100%"}},[t("refresh-tool",{on:{refreshPage:e.refreshHandle}}),t("div",{staticClass:"version-configuration"},[t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:6}},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],staticClass:"version-configuration-sidebar bg-style"},[t("div",{staticClass:"p-t-20 p-b-20 version-configuration-sidebar-btn w-100-p"},[t("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:function(t){return e.selectThisVersion("add")}}},[e._v("添加版本")])],1),t("transition-group",{staticClass:"version-configuration-sidebar-content",attrs:{name:"el-zoom-in-center",tag:"div"}},e._l(e.versionList,(function(s,i){return t("div",{key:i,staticClass:"version-configuration-sidebar-item flex-b-c",on:{click:function(t){return e.getVersionInfo(s)}}},[t("div",{staticStyle:{width:"60%"}},[t("span",{on:{click:function(t){return e.selectThisVersion("edit",s)}}},[e._v(" "+e._s(s.name)+" ")])]),t("div",{staticClass:"flex-a-c",staticStyle:{width:"40%"}},[t("div",{staticClass:"flex-a-c",staticStyle:{width:"60%"}},[t("div",{staticClass:"button-area flex-center",on:{click:function(t){return e.swapElements("up",i,s)}}},[t("svg-icon",{class:[i>0&&i<=e.versionList.length-1?"enable-up":"disable"],staticStyle:{width:"16px",height:"16px"},attrs:{"icon-class":"button_up"}})],1),t("div",{staticClass:"button-area flex-center",on:{click:function(t){return e.swapElements("down",i,s)}}},[t("svg-icon",{class:[i>=0&&i<e.versionList.length-1?"enable-down":"disable"],staticStyle:{width:"16px",height:"16px"},attrs:{"icon-class":"button_down"}})],1)]),t("div",{staticClass:"button-area flex-center",on:{click:function(t){return e.delItem(s)}}},[t("i",{staticClass:"el-icon-delete",style:0===s.org_count?{color:"red"}:{color:"#D7D7D7"}})])])])})),0)],1)]),t("el-col",{attrs:{span:18}},[t("div",{staticClass:"version-configuration-content p-20 bg-style"},[e.isSelectVersion?t("div",[t("div",{staticClass:"version-configuration-content-head"},[t("el-button",{staticClass:"w-100 m-b-20",attrs:{size:"small",type:"Merchant"===e.selectType?"primary":""},on:{click:function(t){e.selectType="Merchant"}}},[e._v("商户后台")]),t("el-button",{staticClass:"w-100 m-b-20",attrs:{size:"small",type:"UserPhone"===e.selectType?"primary":""},on:{click:function(t){e.selectType="UserPhone"}}},[e._v("用户移动端")]),t("el-button",{staticClass:"w-100 m-b-20",attrs:{size:"small",type:"MerchantPhone"===e.selectType?"primary":""},on:{click:function(t){e.selectType="MerchantPhone"}}},[e._v("商户移动端")])],1),"UserPhone"!==e.selectType?t("el-tabs",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],staticClass:"version-configuration-content-box",attrs:{type:"card","tab-position":"left"}},e._l("Merchant"===e.selectType?e.merchantFeatureList:e.merchantMobileFeatureList,(function(s,i){return t("el-tab-pane",{key:i,attrs:{label:s.verbose_name}},[t("div",{staticClass:"p-20"},[t("div",{staticClass:"m-b-20"},[t("el-checkbox",{staticClass:"ps-flex-align-c flex-align-c",attrs:{indeterminate:s.isIndeterminate,disabled:e.isSave},on:{change:function(t){return e.selectHandle(s.isSelect,s,e.selectType,i,!0)}},model:{value:s.isSelect,callback:function(t){e.$set(s,"isSelect",t)},expression:"item.isSelect"}},[t("span",{staticClass:"font-size-18 f-w-500"},[e._v("全选当前页")])])],1),e._l(s.children,(function(s,n){return t("div",{key:n,staticClass:"m-b-20"},[t("el-checkbox",{staticClass:"ps-flex-align-c flex-align-c m-b-10",attrs:{indeterminate:s.isIndeterminate,disabled:e.isSave},on:{change:function(t){return e.selectHandle(s.isSelect,s,e.selectType,i,!1)}},model:{value:s.isSelect,callback:function(t){e.$set(s,"isSelect",t)},expression:"item1.isSelect"}},[t("span",{staticClass:"font-size-18 f-w-700"},[e._v(e._s(s.verbose_name))])]),s.children.length?t("div",{staticClass:"box-item p-t-20 p-l-20 p-r-20"},[t("el-row",{attrs:{gutter:20}},e._l(s.children,(function(s,n){return t("el-col",{key:n,attrs:{span:6}},[t("el-checkbox",{staticClass:"m-b-20",attrs:{indeterminate:s.isIndeterminate,disabled:e.isSave},on:{change:function(t){return e.selectHandle(s.isSelect,s,e.selectType,i,!1)}},model:{value:s.isSelect,callback:function(t){e.$set(s,"isSelect",t)},expression:"item2.isSelect"}},[t("span",{staticClass:"ellipsis w-180"},[e._v(e._s(s.verbose_name))])])],1)})),1)],1):e._e()],1)}))],2)])})),1):t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],staticClass:"version-configuration-content-box2"},[t("div",{staticClass:"p-20"},[t("div",{staticClass:"box-item p-t-20 p-l-20 p-r-20"},[t("el-row",{attrs:{gutter:20}},e._l(e.userPhoneList,(function(s,i){return t("el-col",{key:i,attrs:{span:6}},[t("el-checkbox",{staticClass:"m-b-20",attrs:{disabled:e.isSave},on:{change:function(t){return e.selectHandle(s.isSelect,s,e.selectType,i,!1)}},model:{value:s.isSelect,callback:function(t){e.$set(s,"isSelect",t)},expression:"item.isSelect"}},[t("span",{staticClass:"ellipsis w-180"},[e._v(e._s(s.verbose_name))])])],1)})),1)],1)])]),t("div",{staticClass:"version-configuration-content-footer m-t-20 m-b-20"},[t("div",{staticClass:"button-area m-r-40"},[t("el-button",{staticClass:"w-100",attrs:{size:"small"},on:{click:function(t){return e.editOrCancelHandle(e.isSave)}}},[e._v(e._s(e.isSave?"编辑":"取消"))]),e.isSave?e._e():t("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:e.save}},[e._v("保存")])],1),t("div",{staticClass:"checkbox-area m-r-40"},[t("el-checkbox",{attrs:{disabled:e.isSave},on:{change:function(t){return e.isSelectAll(e.selectType,"selectAll",!0)}},model:{value:e.selectAll,callback:function(t){e.selectAll=t},expression:"selectAll"}},[t("span",{staticClass:"font-size-16"},[e._v("全选")])]),t("el-checkbox",{attrs:{disabled:e.isSave},on:{change:function(t){return e.isSelectAll(e.selectType,"selectNone",!1)}},model:{value:e.selectNone,callback:function(t){e.selectNone=t},expression:"selectNone"}},[t("span",{staticClass:"font-size-16"},[e._v("全不选")])])],1),t("div",[e._v("定制数量："+e._s(e.computedSelectCount(e.selectType))+"/"+e._s(e.computedTotalCount(e.selectType)))])])],1):t("div",{staticClass:"flex-center",staticStyle:{height:"100%"}},[t("el-empty",{attrs:{description:"请先选择版本"}})],1)])])],1)],1),t("div",{staticClass:"ps-el-drawer"},[t("el-drawer",{attrs:{title:"add"===e.drawerType?"新增版本":"编辑版本",visible:e.drawerShow,"show-close":!1,size:"40%"}},[t("div",{staticClass:"p-20"},[t("el-form",{ref:"drawerForm",attrs:{model:e.drawerForm,rules:e.rules,"label-width":"80px"}},[t("el-form-item",{attrs:{label:"版本名称",prop:"name"}},[t("el-input",{staticClass:"w-250",attrs:{placeholder:"不超过5个字",maxlength:"5"},model:{value:e.drawerForm.name,callback:function(t){e.$set(e.drawerForm,"name",t)},expression:"drawerForm.name"}})],1),t("el-form-item",{attrs:{label:"收费配置",prop:"is_toll"}},[t("el-switch",{attrs:{"active-text":"收费"},model:{value:e.drawerForm.is_toll,callback:function(t){e.$set(e.drawerForm,"is_toll",t)},expression:"drawerForm.is_toll"}})],1)],1),t("div",{staticClass:"ps-el-drawer-footer"},[t("el-button",{staticClass:"w-100",attrs:{size:"small"},on:{click:function(t){e.drawerShow=!1}}},[e._v("关闭")]),t("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:e.saveHandle}},[e._v("保存")])],1)],1)])],1)],1)},n=[],r=s("ed08"),a={data:function(){return{isLoading:!1,versionList:[],merchantFeatureList:[],merchantMobileFeatureList:[],userPhoneList:[],tsMerchantFeatureList:[],tsMerchantMobileFeatureList:[],tsUserPhoneList:[],merchantPermissionList:[],merchantPhonePermissionList:[],userPhonePermissionList:[],drawerShow:!1,drawerType:"",selectType:"Merchant",selectAll:!1,selectNone:!1,isSave:!0,drawerForm:{name:"",is_toll:!1},rules:{name:[{required:!0,message:"版本名称不能为空",trigger:["blur","change"]}]},isSelectVersion:!1,selectRow:{},isIndeterminate:!1}},watch:{selectAll:function(e,t){e&&this.selectNone&&(this.selectNone=!1)},selectNone:function(e,t){e&&this.selectAll&&(this.selectAll=!1)},selectType:{handler:function(e,t){if(this.isSave)this.showSelect(e);else switch(t){case"Merchant":this.tsMerchantFeatureList=this.getNewPermissionOut(this.merchantFeatureList),this.resetSelectList(this.merchantFeatureList,this.tsMerchantFeatureList);break;case"UserPhone":this.tsUserPhoneList=this.getNewPermissionOut(this.userPhoneList),this.resetSelectList(this.userPhoneList,this.tsUserPhoneList);break;case"MerchantPhone":this.tsMerchantMobileFeatureList=this.getNewPermissionOut(this.merchantMobileFeatureList),this.resetSelectList(this.merchantMobileFeatureList,this.tsMerchantMobileFeatureList);break}this.selectAll=!1,this.selectNone=!1},immediate:!0}},computed:{computedSelectCount:function(){var e=this;return function(t){var s=0;switch(t){case"Merchant":e.merchantFeatureList.forEach((function(e){s+=e.tabSelectCount}));break;case"UserPhone":e.userPhoneList.forEach((function(e){s+=e.tabSelectCount}));break;case"MerchantPhone":e.merchantMobileFeatureList.forEach((function(e){s+=e.tabSelectCount}));break}return s}},computedTotalCount:function(){var e=this;return function(t){var s=0;switch(t){case"Merchant":e.merchantFeatureList.forEach((function(e){s+=e.tabTotalCount}));break;case"UserPhone":e.userPhoneList.forEach((function(e){s+=e.tabTotalCount}));break;case"MerchantPhone":e.merchantMobileFeatureList.forEach((function(e){s+=e.tabTotalCount}));break}return s}}},created:function(){this.initLoad()},methods:{initLoad:function(){this.getPermission(),this.getVersionList()},getPermission:function(){this.getMerchantPermissions(),this.getAppPermissions(),this.getMerchantMobilePermissions()},getMerchantPermissions:function(){var e=this;this.$apis.apiBackgroundAdminOrganizationGetMerchantPermissionsPost().then((function(t){if(0===t.code){e.merchantFeatureList=t.data.map((function(t){return e.pushIsSelect(t),Object.assign(t,{tabSelectCount:0,tabTotalCount:0}),t}));var s=e.merchantFeatureList.map((function(t,s){return e.setCount(e.merchantFeatureList,t,s),t}));e.merchantFeatureList=Object(r["f"])(s)}else e.$message.error(t.msg)}))},getMerchantMobilePermissions:function(){var e=this;this.$apis.apiBackgroundAdminOrganizationGetMerchantMobilePermissions().then((function(t){if(0===t.code){e.merchantMobileFeatureList=t.data.map((function(t){return e.pushIsSelect(t),Object.assign(t,{tabSelectCount:0,tabTotalCount:0}),t}));var s=e.merchantMobileFeatureList.map((function(t,s){return e.setCount(e.merchantMobileFeatureList,t,s),t}));e.merchantMobileFeatureList=Object(r["f"])(s)}else e.$message.error(t.msg)}))},getAppPermissions:function(){var e=this;this.$apis.apiBackgroundAdminOrganizationGetAppPermissionsPost().then((function(t){if(0===t.code){e.userPhoneList=t.data[0].children.map((function(t){return e.pushIsSelect(t),Object.assign(t,{tabSelectCount:0,tabTotalCount:0}),t}));var s=e.userPhoneList.map((function(t,s){return e.setCount(e.userPhoneList,t,s),t}));e.userPhoneList=Object(r["f"])(s)}else e.$message.error(t.msg)}))},pushIsSelect:function(e){var t=this;Object.assign(e,{isSelect:!1,isIndeterminate:!1}),e.children&&e.children.length&&e.children.forEach((function(e){t.pushIsSelect(e)}))},setCount:function(e,t,s){var i=this;Object.keys(t).includes("isSelect")&&e[s].tabTotalCount++,t.isSelect&&e[s].tabSelectCount++,t.children&&t.children.length&&t.children.forEach((function(t){i.setCount(e,t,s)}))},getVersionList:function(){var e=this;this.$apis.apiBackgroundAdminBackgroundTollVersionListPost({page:1,page_size:9999}).then((function(t){0===t.code?(e.versionList=Object(r["f"])(t.data.results)||[],e.isLoading=!1):e.$message.error(t.msg)}))},refreshHandle:function(){this.getPermission(),this.initLoad(),this.isSelectVersion=!1},swapElements:function(e,t,s){if("up"===e&&t>0){var i=this.versionList[t-1],n={id:s.id,order:i.order},r={id:i.id,order:s.order};this.modifyVersion(n),this.modifyVersion(r)}else if("down"===e&&t<this.versionList.length-1){var a=this.versionList[t+1],c={id:s.id,order:a.order},o={id:a.id,order:s.order};this.modifyVersion(c),this.modifyVersion(o)}},modifyVersion:function(e){var t=this;this.isLoading=!0,this.$apis.apiBackgroundAdminBackgroundTollVersionModifyPost(e).then((function(e){0===e.code?(t.$message.success("修改成功"),t.drawerShow=!1,t.getPermission(),t.getVersionList()):t.$message.error(e.msg)}))},delItem:function(e){var t=this;if(0!==e.org_count)return this.$message.error("项目点使用中，不可删除");this.isSelectVersion=!1,this.$apis.apiBackgroundAdminBackgroundTollVersionDeletePost({ids:[e.id]}).then((function(e){0===e.code?(t.$message.success("删除成功"),t.getVersionList()):t.$message.error(e.msg)}))},addNewVersion:function(e){var t=this;this.$apis.apiBackgroundAdminBackgroundTollVersionAddPost(e).then((function(e){0===e.code?(t.$message.success("新增成功"),t.getVersionList()):t.$message.error(e.msg),t.drawerShow=!1}))},saveHandle:function(){var e=this;this.$refs.drawerForm.validate((function(t){if(t){var s={id:"add"===e.drawerType?void 0:e.selectRow.id,name:e.drawerForm.name,permission:"add"===e.drawerType?[]:void 0,app_permission:"add"===e.drawerType?[]:void 0,merchant_app_permission:"add"===e.drawerType?[]:void 0,order:"add"===e.drawerType?e.versionList[e.versionList.length-1].order+1:e.selectRow.order,is_toll:e.drawerForm.is_toll};"add"===e.drawerType?e.addNewVersion(s):e.modifyVersion(s)}}))},selectThisVersion:function(e,t){this.drawerType=e,"edit"===e?(this.drawerForm.name=t.name,this.drawerForm.is_toll=t.is_toll):(this.drawerForm.name="",this.drawerForm.is_toll=!1),this.drawerShow=!0},getVersionInfo:function(e){var t=this;this.isLoading=!0,this.isSave=!0,this.selectRow=Object(r["f"])(e),this.selectType="Merchant",this.merchantPermissionList=Object(r["f"])(e.permission),this.userPhonePermissionList=Object(r["f"])(e.app_permission),this.merchantPhonePermissionList=Object(r["f"])(e.merchant_app_permission),this.showSelect("Merchant"),this.showSelect("UserPhone"),this.showSelect("MerchantPhone"),this.tsMerchantFeatureList=this.getNewPermissionOut(this.merchantFeatureList),this.tsUserPhoneList=this.getNewPermissionOut(this.userPhoneList),this.tsMerchantMobileFeatureList=this.getNewPermissionOut(this.merchantMobileFeatureList),this.isSelectVersion=!0,setTimeout((function(){t.isLoading=!1}),500)},showSelect:function(e){switch(e){case"Merchant":this.resetSelectList(this.merchantFeatureList,this.merchantPermissionList);break;case"UserPhone":this.resetSelectList(this.userPhoneList,this.userPhonePermissionList);break;case"MerchantPhone":this.resetSelectList(this.merchantMobileFeatureList,this.merchantPhonePermissionList);break}},resetSelectList:function(e,t){var s=this;e=e.map((function(e){return e.tabSelectCount=0,e})),e.forEach((function(e,i){s.isSelectKey(e,t)})),e.forEach((function(t,i){t.isSelect&&e[i].tabSelectCount++,t.children&&t.children.length&&s.resetTabSelectCount(t.children,i,e)}))},resetTabSelectCount:function(e,t,s){var i=this;e.length&&e.forEach((function(e){e.isSelect&&s[t].tabSelectCount++,e.children&&e.children.length&&i.resetTabSelectCount(e.children,t,s)}))},isSelectKey:function(e,t){var s=this;t.includes(e.key)?e.isSelect=!0:e.isSelect=!1,e.children&&e.children.length&&e.children.forEach((function(e){s.isSelectKey(e,t)}))},selectHandle:function(e,t,s,i,n){switch(e?this.selectNone=!1:this.selectAll=!1,s){case"Merchant":this.changeSelectStatus(e,t,this.merchantFeatureList,i,n),this.tsMerchantFeatureList=this.getNewPermissionOut(this.merchantFeatureList);break;case"UserPhone":this.changeSelectStatus(e,t,this.userPhoneList,i,n),this.tsUserPhoneList=this.getNewPermissionOut(this.userPhoneList);break;case"MerchantPhone":this.changeSelectStatus(e,t,this.merchantMobileFeatureList,i,n),this.tsMerchantMobileFeatureList=this.getNewPermissionOut(this.merchantMobileFeatureList);break}},changeSelectStatus:function(e,t,s,i,n){var r=this;this.selectAllItem(e,t),this.changeSelfIndeterminate(e,t),n||s.forEach((function(i){r.changeParentIndeterminate(e,i,t,s)})),this.tabSelectCountHandle(s,i)},getNewPermissionOut:function(e){var t=this,s=[];return e.forEach((function(e){e.isSelect&&s.push(e.key),e.children&&e.children.length&&t.getNewPermissionIn(e.children,s)})),Object(r["f"])(s)},getNewPermissionIn:function(e,t){var s=this;e.forEach((function(e){e.isSelect&&t.push(e.key),e.children&&e.children.length&&s.getNewPermissionIn(e.children,t)}))},tabSelectCountHandle:function(e,t){e[t].tabSelectCount=0,e[t].isSelect&&e[t].tabSelectCount++,e[t].children&&e[t].children.length&&this.resetTabSelectCount(e[t].children,t,e)},changeSelfIndeterminate:function(e,t){var s=this;e?t.children.some((function(e){return e.isSelect}))&&t.children.some((function(e){return!e.isSelect}))?t.isIndeterminate=!0:t.isIndeterminate=!1:t.children.some((function(e){return e.isSelect}))?t.isIndeterminate=!0:(t.children.some((function(e){return e.isSelect})),t.isIndeterminate=!1),t.children&&t.children.length&&t.children.forEach((function(t){s.changeSelfIndeterminate(e,t)}))},changeParentIndeterminate:function(e,t,s,i){var n=this;t.index===s.parent?(e?t.children.some((function(e){return e.isSelect}))?t.children.some((function(e){return!e.isSelect}))?(t.isIndeterminate=!0,t.isSelect=!0):(t.isIndeterminate=!1,t.isSelect=!0):(t.isIndeterminate=!1,t.isSelect=!1):t.children.some((function(e){return e.isSelect}))?(t.isIndeterminate=!0,t.isSelect=!0):t.children.some((function(e){return e.isSelect}))?(t.isIndeterminate=!1,t.isSelect=!0):(t.isIndeterminate=!1,t.isSelect=!1),0!==t.level&&i.forEach((function(s){n.changeParentIndeterminate(e,s,t,i)}))):t.children&&t.children.length&&t.children.forEach((function(t){n.changeParentIndeterminate(e,t,s,i)}))},selectAllItem:function(e,t){var s=this;t.isSelect=e,t.children&&t.children.length&&t.children.forEach((function(t){t.isSelect=e,s.selectAllItem(e,t)}))},isSelectAll:function(e,t,s){var i=this;if("selectAll"===t&&s||"selectNone"===t&&!s)switch(e){case"Merchant":this.merchantFeatureList.forEach((function(e,t){e.isSelect=s,i.selectAllItem(s,e),i.changeSelfIndeterminate(s,e),i.tabSelectCountHandle(i.merchantFeatureList,t)})),this.tsMerchantFeatureList=this.getNewPermissionOut(this.merchantFeatureList);break;case"UserPhone":this.userPhoneList.forEach((function(e,t){e.isSelect=s,i.selectAllItem(s,e),i.changeSelfIndeterminate(s,e),i.tabSelectCountHandle(i.userPhoneList,t)})),this.tsUserPhoneList=this.getNewPermissionOut(this.userPhoneList);break;case"MerchantPhone":this.merchantMobileFeatureList.forEach((function(e,t){e.isSelect=s,i.selectAllItem(s,e),i.changeSelfIndeterminate(s,e),i.tabSelectCountHandle(i.merchantMobileFeatureList,t)})),this.tsMerchantMobileFeatureList=this.getNewPermissionOut(this.merchantMobileFeatureList);break}},editOrCancelHandle:function(e){e||(this.merchantPermissionList=Object(r["f"])(this.selectRow.permission),this.userPhonePermissionList=Object(r["f"])(this.selectRow.app_permission),this.merchantPhonePermissionList=Object(r["f"])(this.selectRow.merchant_app_permission),this.showSelect()),this.isSave=!e},save:function(){this.isLoading=!0,this.merchantPermissionList=Object(r["f"])(this.traverseGroups(this.merchantFeatureList)),this.userPhonePermissionList=Object(r["f"])(this.traverseGroups(this.userPhoneList)),this.merchantPhonePermissionList=Object(r["f"])(this.traverseGroups(this.merchantMobileFeatureList));var e={id:this.selectRow.id,permission:this.merchantPermissionList,app_permission:this.userPhonePermissionList,merchant_app_permission:this.merchantPhonePermissionList};this.modifyVersion(e),this.isSave=!0,this.isSelectVersion=!1,this.selectAll=!1,this.selectNone=!1},traverseGroups:function(e){var t=[];return this.traverseGroupsDetail(e,t),t},traverseGroupsDetail:function(e,t){var s=this;e.forEach((function(e){e.isSelect&&t.push(e.key),e.children&&e.children.length&&s.traverseGroupsDetail(e.children,t)}))}}},c=a,o=(s("41ed"),s("2877")),l=Object(o["a"])(c,i,n,!1,null,"5620d8ba",null);t["default"]=l.exports},"41ed":function(e,t,s){"use strict";s("2f99")}}]);