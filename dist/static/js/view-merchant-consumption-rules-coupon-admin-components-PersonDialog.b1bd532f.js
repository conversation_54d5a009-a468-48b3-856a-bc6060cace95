(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-consumption-rules-coupon-admin-components-PersonDialog"],{"07a7":function(t,e,r){},"68e3":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("dialog-message",{attrs:{show:t.visible,title:t.title,loading:t.isLoading,customClass:"ps-dialog",width:t.width},on:{"update:show":function(e){t.visible=e},"update:loading":function(e){t.isLoading=e},close:t.handleClose}},[e("el-form",{ref:"couponDialogForm",staticClass:"jiaofei-form",attrs:{model:t.couponDialogForm,"status-icon":"","label-width":"80px",inline:""},nativeOn:{submit:function(t){t.preventDefault()}}},[e("el-form-item",{attrs:{label:"部门"}},[e("user-department-select",{staticClass:"w-180 ps-input",attrs:{clearable:!0,multiple:!0,"check-strictly":!0,isLazy:!1,placeholder:"请选择部门","append-to-body":!0},on:{change:t.searchHandle},model:{value:t.couponDialogForm.department,callback:function(e){t.$set(t.couponDialogForm,"department",e)},expression:"couponDialogForm.department"}})],1),e("el-form-item",{attrs:{label:"分组"}},[e("user-group-select",{staticClass:"search-item-w ps-input w-180",attrs:{multiple:!0,"collapse-tags":!0,placeholder:"请下拉选择"},on:{change:t.searchHandle},model:{value:t.couponDialogForm.groupIds,callback:function(e){t.$set(t.couponDialogForm,"groupIds",e)},expression:"couponDialogForm.groupIds"}})],1),e("el-form-item",{attrs:{label:"姓名"}},[e("el-input",{staticClass:"ps-input w-180",attrs:{placeholder:"请输入"},on:{input:t.searchHandle},model:{value:t.couponDialogForm.name,callback:function(e){t.$set(t.couponDialogForm,"name",e)},expression:"couponDialogForm.name"}})],1),e("el-form-item",{attrs:{label:"人员编号"}},[e("el-input",{staticClass:"ps-input w-180",attrs:{placeholder:"请输入"},on:{input:t.searchHandle},model:{value:t.couponDialogForm.personNo,callback:function(e){t.$set(t.couponDialogForm,"personNo",e)},expression:"couponDialogForm.personNo"}})],1),e("el-form-item",{attrs:{label:"性别",prop:"gender"}},[e("el-select",{staticClass:"ps-select w-180",attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select"},on:{change:t.searchHandle},model:{value:t.couponDialogForm.gender,callback:function(e){t.$set(t.couponDialogForm,"gender",e)},expression:"couponDialogForm.gender"}},t._l(t.genderList,(function(t){return e("el-option",{key:t.value,attrs:{label:t.gender,value:t.value}})})),1)],1),e("div",{staticClass:"person-table"},[e("el-table",{ref:"userListRef",attrs:{data:t.userList,"max-height":"350","row-key":t.getRowKey,"header-row-class-name":"ps-table-header-row"},on:{select:t.handleSelection,"select-all":t.handleAllSelection}},[e("el-table-column",{attrs:{type:"selection","reserve-selection":!0,width:"50",align:"center","class-name":"ps-checkbox"}}),e("el-table-column",{attrs:{prop:"card_department_group_alias",label:"部门",align:"center"}}),e("el-table-column",{attrs:{prop:"card_user_group_alias",label:"分组",align:"center"}}),e("el-table-column",{attrs:{prop:"name",label:"姓名",align:"center"}}),e("el-table-column",{attrs:{prop:"gender_alias",label:"性别",align:"center"}}),e("el-table-column",{attrs:{prop:"person_no",label:"人员编号",align:"center"}}),e("el-table-column",{attrs:{prop:"card_no",label:"卡号",align:"center"}})],1)],1),e("div",{staticClass:"block ps-pagination person-table-bottom"},[e("div",{staticStyle:{width:"100px"}},[t._v("已选人数："+t._s(t.selectList.length))]),e("el-pagination",{staticClass:"ps-text",attrs:{"current-page":t.currentPage,"page-sizes":[5,10,20,30,40],"page-size":t.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:t.totalCount,"pager-count":5,background:"","popper-class":"ps-popper-select"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1),e("template",{slot:"tool"},[e("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:t.isLoading},on:{click:t.clickCancleHandle}},[t._v(" 取消 ")]),e("el-button",{staticClass:"ps-btn",attrs:{disabled:t.isLoading,type:"primary"},on:{click:t.clickConfirmHandle}},[t._v(" 确定 ")])],1)])],2)},o=[],i=r("faa6"),a=r("390a"),s=r("ed08"),c=r("bbd5");function l(t){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},l(t)}function u(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */u=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function p(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{p({},"")}catch(t){p=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,a=Object.create(i.prototype),s=new P(n||[]);return o(a,"_invoke",{value:F(t,r,s)}),a}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var d="suspendedStart",g="suspendedYield",m="executing",y="completed",v={};function b(){}function w(){}function L(){}var x={};p(x,a,(function(){return this}));var _=Object.getPrototypeOf,O=_&&_(_(N([])));O&&O!==r&&n.call(O,a)&&(x=O);var S=L.prototype=b.prototype=Object.create(x);function j(t){["next","throw","return"].forEach((function(e){p(t,e,(function(t){return this._invoke(e,t)}))}))}function D(t,e){function r(o,i,a,s){var c=h(t[o],t,i);if("throw"!==c.type){var u=c.arg,p=u.value;return p&&"object"==l(p)&&n.call(p,"__await")?e.resolve(p.__await).then((function(t){r("next",t,a,s)}),(function(t){r("throw",t,a,s)})):e.resolve(p).then((function(t){u.value=t,a(u)}),(function(t){return r("throw",t,a,s)}))}s(c.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function F(e,r,n){var o=d;return function(i,a){if(o===m)throw Error("Generator is already running");if(o===y){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var s=n.delegate;if(s){var c=C(s,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===d)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=m;var l=h(e,r,n);if("normal"===l.type){if(o=n.done?y:g,l.arg===v)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(o=y,n.method="throw",n.arg=l.arg)}}}function C(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,C(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=h(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function P(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function N(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(l(e)+" is not iterable")}return w.prototype=L,o(S,"constructor",{value:L,configurable:!0}),o(L,"constructor",{value:w,configurable:!0}),w.displayName=p(L,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,L):(t.__proto__=L,p(t,c,"GeneratorFunction")),t.prototype=Object.create(S),t},e.awrap=function(t){return{__await:t}},j(D.prototype),p(D.prototype,s,(function(){return this})),e.AsyncIterator=D,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new D(f(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},j(S),p(S,c,"Generator"),p(S,a,(function(){return this})),p(S,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=N,P.prototype={constructor:P,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(E),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return s.type="throw",s.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),l=n.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),E(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;E(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:N(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function p(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function f(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p(Object(r),!0).forEach((function(e){h(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function h(t,e,r){return(e=d(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function d(t){var e=g(t,"string");return"symbol"==l(e)?e:e+""}function g(t,e){if("object"!=l(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=l(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function m(t,e,r,n,o,i,a){try{var s=t[i](a),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,o)}function y(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){m(i,n,o,a,s,"next",t)}function s(t){m(i,n,o,a,s,"throw",t)}a(void 0)}))}}var v={name:"JiaoFeiDialog",components:{UserDepartmentSelect:i["a"],UserGroupSelect:a["a"]},props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:"选择人员"},width:{type:String,default:"900px"},personList:{type:Array,default:function(){return[]}},selectInfo:{type:Object,default:function(){return{}}},isshow:Boolean,confirm:Function},data:function(){return{isLoading:!1,couponDialogForm:{department:[],groupIds:[],name:"",personNo:"",gender:""},genderList:[{gender:"男",value:"MAN"},{gender:"女",value:"WOMEN"},{gender:"其他",value:"OTHER"}],userList:[],pageSize:10,totalCount:0,currentPage:1,selectList:[],organizationId:this.$store.getters.organization,categoryList:[],foodList:[]}},computed:{visible:{get:function(){return this.isshow},set:function(t){this.$emit("update:isshow",t)}}},watch:{visible:function(){var t=this;this.visible&&(this.currentPage=1,this.totalCount=0,this.isshow&&(this.$nextTick((function(){t.$refs.userListRef.clearSelection()})),this.couponDialogForm.department=[],this.couponDialogForm.groupIds=[],this.couponDialogForm.name="",this.couponDialogForm.personNo="",this.couponDialogForm.gender="",this.selectList=Object(c["a"])(this.personList),this.getUserList()))}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){},searchHandle:Object(s["d"])((function(){this.currentPage=1,this.getUserList()}),300),clickConfirmHandle:function(){var t=this;this.$refs.couponDialogForm.validate((function(e){e&&(t.$emit("confirmPerson",t.selectList),t.visible=!1)}))},clickCancleHandle:function(){this.visible=!1},handleClose:function(t){this.isLoading=!1,this.visible=!1,this.$refs.couponDialogForm.resetFields()},getUserList:function(){var t=this;return y(u().mark((function e(){var r,n,o,i;return u().wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(o in t.isLoading=!0,r={card_department_group_ids:t.couponDialogForm.department,card_user_group_ids:t.couponDialogForm.groupIds,person_name:t.couponDialogForm.name,person_no:t.couponDialogForm.personNo,gender:t.couponDialogForm.gender},n={},r)r[o]&&(n[o]=r[o]);return e.next=6,t.$apis.apiCardServiceCardUserListPost(f(f({},n),{},{org_ids:[t.organizationId],page:t.currentPage,page_size:t.pageSize}));case 6:i=e.sent,t.isLoading=!1,0===i.code?(t.userList=i.data.results,t.totalCount=i.data.count,t.userList.map((function(e){e.card_user_group_alias=e.card_user_group_alias.join("，"),t.personList.map((function(r){e.id===r.id&&t.$refs.userListRef.toggleRowSelection(e,!0)}))}))):t.$message.error(i.msg);case 9:case"end":return e.stop()}}),e)})))()},handleSizeChange:function(t){this.pageSize=t,this.getUserList()},handleCurrentChange:function(t){this.currentPage=t,this.getUserList()},handleSelection:function(t,e){var r=this.selectList.findIndex((function(t){return t.id===e.id}));-1===r?this.selectList.push(e):this.selectList.splice(r,1)},handleAllSelection:function(t){var e=this,r=Object(c["a"])(t),n=!0;this.userList.map((function(t){var e=r.findIndex((function(e){return e.id===t.id}));-1===e&&(n=!1)})),n?this.userList.map((function(t){var r=e.selectList.findIndex((function(e){return e.id===t.id}));-1===r&&e.selectList.push(t)})):this.userList.map((function(t){var r=e.selectList.findIndex((function(e){return e.id===t.id}));-1!==r&&e.selectList.splice(r,1)}))},getRowKey:function(t){return t.id}}},b=v,w=(r("9e41"),r("2877")),L=Object(w["a"])(b,n,o,!1,null,"47bc3d17",null);e["default"]=L.exports},"9e41":function(t,e,r){"use strict";r("07a7")},bbd5:function(t,e,r){"use strict";function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}r.d(e,"a",(function(){return o}));var o=function t(e){if(!e&&"object"!==n(e))throw new Error("error arguments","deepClone");var r=e.constructor===Array?[]:{};return Object.keys(e).forEach((function(o){e[o]&&"object"===n(e[o])?r[o]=t(e[o]):r[o]=e[o]})),r}}}]);