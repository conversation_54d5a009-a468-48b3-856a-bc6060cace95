(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-device-management-device-cloud-print-admin-components-PrintDialog","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-meal-report-MealPackageRule"],{"676a":function(e,t,r){},7164:function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("dialog-message",{attrs:{show:e.visible,title:e.title,loading:e.isLoading,customClass:"ps-dialog",width:e.width,top:"200px"},on:{"update:show":function(t){e.visible=t},"update:loading":function(t){e.isLoading=t},close:e.handleClose}},[t("el-form",{ref:"printForm",staticClass:"dialog-form",attrs:{model:e.printForm,"status-icon":"",rules:e.printFormRules,"label-width":"120px"},nativeOn:{submit:function(e){e.preventDefault()}}},["edit"===e.type||"add"===e.type?t("div",[t("el-form-item",{attrs:{label:"打印机品牌",prop:"brand"}},[t("el-select",{staticClass:"w-250 ps-select",attrs:{placeholder:"请选择"},model:{value:e.printForm.brand,callback:function(t){e.$set(e.printForm,"brand",t)},expression:"printForm.brand"}},[t("el-option",{attrs:{label:"芯烨云",value:"xprinter"}}),t("el-option",{attrs:{label:"映美云",value:"YMPrinter"}})],1)],1),"YMPrinter"===e.printForm.brand?t("el-form-item",{attrs:{label:"校验码",prop:"checkCode"}},[t("el-input",{staticClass:"ps-input w-250",attrs:{maxlength:"32",placeholder:"请输入校验码"},model:{value:e.printForm.checkCode,callback:function(t){e.$set(e.printForm,"checkCode",t)},expression:"printForm.checkCode"}})],1):e._e(),t("el-form-item",{attrs:{label:"序列号/编号",prop:"serialNumber"}},[t("el-input",{staticClass:"ps-input w-250",attrs:{disabled:"edit"===e.type,placeholder:"请输入序列号/编号"},model:{value:e.printForm.serialNumber,callback:function(t){e.$set(e.printForm,"serialNumber",t)},expression:"printForm.serialNumber"}})],1),t("el-form-item",{attrs:{label:"名称",prop:"printDeviceName"}},[t("el-input",{staticClass:"ps-input w-250",attrs:{placeholder:"请输入名称"},model:{value:e.printForm.printDeviceName,callback:function(t){e.$set(e.printForm,"printDeviceName",t)},expression:"printForm.printDeviceName"}})],1),t("el-form-item",{attrs:{label:"类型",prop:"printDeviceType"}},[t("el-select",{staticClass:"w-250 ps-select",attrs:{placeholder:"请选择打印机类型"},model:{value:e.printForm.printDeviceType,callback:function(t){e.$set(e.printForm,"printDeviceType",t)},expression:"printForm.printDeviceType"}},[t("el-option",{attrs:{label:"热敏纸打印机",value:"thermal_paper"}}),t("el-option",{attrs:{label:"标签打印机",value:"label"}})],1)],1),t("el-form-item",{attrs:{label:"适用组织",prop:"organizationIds"}},[t("organization-select",{staticClass:"search-item-w ps-input w-250",attrs:{placeholder:"请选择适用组织",isLazy:!1,multiple:!0,"check-strictly":!0,"append-to-body":!0},model:{value:e.printForm.organizationIds,callback:function(t){e.$set(e.printForm,"organizationIds",t)},expression:"printForm.organizationIds"}})],1)],1):e._e(),"adminHistory"===e.type||"listHistory"===e.type?t("div",[t("el-table",{key:e.tableKey,ref:"historyData",staticStyle:{width:"100%"},attrs:{data:e.historyData,stripe:"","header-row-class-name":"ps-table-header-row"}},[t("el-table-column",{attrs:{prop:"create_time",label:"操作时间",align:"center"}}),t("el-table-column",{attrs:{prop:"operate_type",label:"操作类型",align:"center"}}),t("el-table-column",{attrs:{prop:"account_name",label:"操作人",align:"center"}}),"adminHistory"===e.type?t("el-table-column",{attrs:{prop:"detail",label:"详情内容",align:"center"}}):e._e()],1)],1):e._e(),"detail"===e.type?t("div",[t("div",{staticClass:"print-detail"},[t("el-select",{staticClass:"w-180 ps-select",attrs:{placeholder:"请选择"},on:{change:e.getPrintTaskList},model:{value:e.printDeviceStatus,callback:function(t){e.printDeviceStatus=t},expression:"printDeviceStatus"}},[t("el-option",{attrs:{label:"全部",value:""}}),t("el-option",{attrs:{label:"未开始",value:"not_started"}}),t("el-option",{attrs:{label:"已打印",value:"success"}})],1),t("button-icon",{attrs:{color:"green"},on:{click:e.openDialog}},[e._v("再次打印")])],1),t("el-table",{key:e.tableKey,ref:"historyData",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","header-row-class-name":"ps-table-header-row"},on:{"selection-change":e.handleSelectionChange}},[t("el-table-column",{attrs:{type:"selection",align:"center","class-name":"ps-checkbox"}}),t("el-table-column",{attrs:{prop:"trade_no",label:"订单号",align:"center"}}),t("el-table-column",{attrs:{prop:"third_party_order_no",label:"打印单号",align:"center"}}),t("el-table-column",{attrs:{prop:"task_status_alias",label:"打印状态",align:"center"}})],1),t("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[t("el-pagination",{staticClass:"ps-text",attrs:{"current-page":e.currentPage,"page-sizes":[10,20,50,100,500],"page-size":e.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1):e._e()]),t("template",{slot:"tool"},[t("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},["adminHistory"===e.type||"listHistory"===e.type||"detail"===e.type?t("div",[t("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickCancleHandle}},[e._v(" 关闭 ")])],1):t("div",[t("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:e.clickCancleHandle}},[e._v(" 取消 ")]),t("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickConfirmHandle}},[e._v(" 确定 ")])],1)])]),t("print-ticket",{attrs:{isshow:e.printTicketVisible,type:"printAgainTask",title:"小票打印","select-list-id":e.selectListId,confirm:e.closePrintTicket},on:{"update:isshow":function(t){e.printTicketVisible=t}}})],2)},i=[],a=r("cbfb"),o=r("f1bf");function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function l(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */l=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,i=Object.defineProperty||function(e,t,r){e[t]=r.value},a="function"==typeof Symbol?Symbol:{},o=a.iterator||"@@iterator",c=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function p(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{p({},"")}catch(e){p=function(e,t,r){return e[t]=r}}function d(e,t,r,n){var a=t&&t.prototype instanceof b?t:b,o=Object.create(a.prototype),s=new N(n||[]);return i(o,"_invoke",{value:D(e,r,s)}),o}function h(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var f="suspendedStart",m="suspendedYield",g="executing",y="completed",v={};function b(){}function k(){}function w(){}var _={};p(_,o,(function(){return this}));var x=Object.getPrototypeOf,L=x&&x(x(I([])));L&&L!==r&&n.call(L,o)&&(_=L);var F=w.prototype=b.prototype=Object.create(_);function C(e){["next","throw","return"].forEach((function(t){p(e,t,(function(e){return this._invoke(t,e)}))}))}function P(e,t){function r(i,a,o,l){var c=h(e[i],e,a);if("throw"!==c.type){var u=c.arg,p=u.value;return p&&"object"==s(p)&&n.call(p,"__await")?t.resolve(p.__await).then((function(e){r("next",e,o,l)}),(function(e){r("throw",e,o,l)})):t.resolve(p).then((function(e){u.value=e,o(u)}),(function(e){return r("throw",e,o,l)}))}l(c.arg)}var a;i(this,"_invoke",{value:function(e,n){function i(){return new t((function(t,i){r(e,n,t,i)}))}return a=a?a.then(i,i):i()}})}function D(t,r,n){var i=f;return function(a,o){if(i===g)throw Error("Generator is already running");if(i===y){if("throw"===a)throw o;return{value:e,done:!0}}for(n.method=a,n.arg=o;;){var s=n.delegate;if(s){var l=S(s,n);if(l){if(l===v)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===f)throw i=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=g;var c=h(t,r,n);if("normal"===c.type){if(i=n.done?y:m,c.arg===v)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(i=y,n.method="throw",n.arg=c.arg)}}}function S(t,r){var n=r.method,i=t.iterator[n];if(i===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,S(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var a=h(i,t.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,v;var o=a.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,v):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function z(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function N(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function I(t){if(t||""===t){var r=t[o];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,a=function r(){for(;++i<t.length;)if(n.call(t,i))return r.value=t[i],r.done=!1,r;return r.value=e,r.done=!0,r};return a.next=a}}throw new TypeError(s(t)+" is not iterable")}return k.prototype=w,i(F,"constructor",{value:w,configurable:!0}),i(w,"constructor",{value:k,configurable:!0}),k.displayName=p(w,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===k||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,p(e,u,"GeneratorFunction")),e.prototype=Object.create(F),e},t.awrap=function(e){return{__await:e}},C(P.prototype),p(P.prototype,c,(function(){return this})),t.AsyncIterator=P,t.async=function(e,r,n,i,a){void 0===a&&(a=Promise);var o=new P(d(e,r,n,i),a);return t.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},C(F),p(F,u,"Generator"),p(F,o,(function(){return this})),p(F,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=I,N.prototype={constructor:N,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(z),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function i(n,i){return s.type="throw",s.arg=t,r.next=n,i&&(r.method="next",r.arg=e),!!i}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],s=o.completion;if("root"===o.tryLoc)return i("end");if(o.tryLoc<=this.prev){var l=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return i(o.catchLoc,!0);if(this.prev<o.finallyLoc)return i(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return i(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return i(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=e,o.arg=t,a?(this.method="next",this.next=a.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),z(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;z(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:I(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),v}},t}function c(e,t,r,n,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,i)}function u(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var a=e.apply(t,r);function o(e){c(a,n,i,o,s,"next",e)}function s(e){c(a,n,i,o,s,"throw",e)}o(void 0)}))}}var p={name:"PrintDialog",components:{OrganizationSelect:a["a"],PrintTicket:o["a"]},props:{loading:Boolean,type:{type:String,default:"add"},title:{type:String,default:"新增打印机"},width:{type:String,default:"500px"},isshow:Boolean,printInfo:{type:Object,default:function(){return{}}},confirm:Function},data:function(){var e=function(e,t,r){var n=/^[a-z0-9]+$/i;n.test(t)&&t?r():r(new Error("请输入校验码"))};return{isLoading:!1,printForm:{brand:"",checkCode:"",serialNumber:"",printDeviceName:"",printDeviceType:"",organizationIds:[]},printFormRules:{brand:[{required:!0,message:"请选择打印机品牌",trigger:["change","blur"]}],checkCode:[{validator:e,required:!0,message:"请输入校验码",trigger:["change","blur"]}],serialNumber:[{required:!0,message:"请输入序列号/编号",trigger:"blur"}],printDeviceName:[{required:!0,message:"请输入名称",trigger:"blur"}],printDeviceType:[{required:!0,message:"请选择打印机类型",trigger:"change"}],organizationIds:[{required:!0,message:"请选择适用组织",trigger:"change"}]},tableKey:0,historyData:[],printDeviceStatus:"",printDeviceList:[],pageSize:10,totalCount:0,currentPage:1,tableData:[],selectListId:[],printTicketVisible:!1}},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},watch:{visible:function(){this.visible&&(this.tableKey=Math.random(),"edit"===this.type?(this.printForm.brand=this.printInfo.brand,this.printForm.checkCode=this.printInfo.check_code,this.printForm.serialNumber=this.printInfo.sn,this.printForm.printDeviceName=this.printInfo.name,this.printForm.printDeviceType=this.printInfo.type,this.printForm.organizationIds=this.printInfo.organization):"detail"===this.type?this.getPrintTaskList():"adminHistory"===this.type?this.getPrinterLog():"listHistory"===this.type&&this.getPrinterTaskLog())}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){},clickConfirmHandle:function(){var e=this;this.$refs.printForm.validate((function(t){var r,n={};t&&("add"===e.type?(n={brand:e.printForm.brand,check_code:e.printForm.checkCode,sn:e.printForm.serialNumber,name:e.printForm.printDeviceName,type:e.printForm.printDeviceType,organization:e.printForm.organizationIds},r=e.$apis.apiBackgroundPrinterPrinterAddPost(n)):"edit"===e.type&&(n={brand:e.printForm.brand,check_code:e.printForm.checkCode,sn:e.printForm.serialNumber,name:e.printForm.printDeviceName,type:e.printForm.printDeviceType,organization:e.printForm.organizationIds,id:e.printInfo.id},r=e.$apis.apiBackgroundPrinterPrinterModifyPost(n)),e.operationPrint(r))}))},operationPrint:function(e){var t=this;return u(l().mark((function r(){var n;return l().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!t.isLoading){r.next=2;break}return r.abrupt("return");case 2:return t.isLoading=!0,r.next=5,e;case 5:n=r.sent,t.isLoading=!1,0===n.code?(t.$message.success("成功"),t.$emit("confirm","search")):t.$message.error(n.msg);case 8:case"end":return r.stop()}}),r)})))()},getPrintTaskList:function(){var e=this;return u(l().mark((function t(){var r,n;return l().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,r={task_manager:e.printInfo.id,page:e.currentPage,page_size:e.pageSize},e.printDeviceStatus&&(r.task_status=e.printDeviceStatus),t.next=5,e.$apis.apiBackgroundPrinterPrintTaskListPost(r);case 5:n=t.sent,e.isLoading=!1,0===n.code?(e.tableData=n.data.results,e.totalCount=n.data.count):e.$message.error(n.msg);case 8:case"end":return t.stop()}}),t)})))()},handleSizeChange:function(e){this.pageSize=e,this.getPrintTaskList()},handleCurrentChange:function(e){this.currentPage=e,this.getPrintTaskList()},handleSelectionChange:function(e){var t=this;this.selectListId=[];var r=Object.freeze(e);r.map((function(e){t.selectListId.push(e.id)}))},openDialog:function(){if(!this.selectListId.length)return this.$message.error("请先选择数据！");this.printTicketVisible=!0},getPrinterLog:function(){var e=this;return u(l().mark((function t(){var r,n;return l().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,r={printer_ids:[e.printInfo.id],page:1,page_size:9999},t.next=4,e.$apis.apiBackgroundPrinterPrinterLogListPost(r);case 4:n=t.sent,e.isLoading=!1,0===n.code?e.historyData=n.data.results:e.$message.error(n.msg);case 7:case"end":return t.stop()}}),t)})))()},getPrinterTaskLog:function(){var e=this;return u(l().mark((function t(){var r,n;return l().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,r={print_task_manager_ids:[e.printInfo.id],page:1,page_size:9999},t.next=4,e.$apis.apiBackgroundPrinterPrintTaskManagerLogListPost(r);case 4:n=t.sent,e.isLoading=!1,0===n.code?e.historyData=n.data.results:e.$message.error(n.msg);case 7:case"end":return t.stop()}}),t)})))()},closePrintTicket:function(){this.printTicketVisible=!1,this.$emit("confirm","search")},clickCancleHandle:function(){this.visible=!1},handleClose:function(e){this.isLoading=!1,this.visible=!1,this.$refs.printForm.resetFields()}}},d=p,h=(r("7cec"),r("2877")),f=Object(h["a"])(d,n,i,!1,null,"26066f25",null);t["default"]=f.exports},"7cec":function(e,t,r){"use strict";r("676a")},c9d9:function(e,t,r){"use strict";r.d(t,"a",(function(){return a})),r.d(t,"d",(function(){return o})),r.d(t,"b",(function(){return s})),r.d(t,"c",(function(){return l})),r.d(t,"e",(function(){return c})),r.d(t,"f",(function(){return u})),r.d(t,"g",(function(){return p}));var n=r("5a0c"),i=r("da92"),a=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],o=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],s={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},l=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],c=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],u=(n().subtract(7,"day").format("YYYY-MM-DD"),n().format("YYYY-MM-DD"),function(e){return e?"number"===typeof e?i["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:i["a"].divide(e,100).toFixed(2):"0.00"}),p=function(e){return i["a"].times(e,100)}}}]);