(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-report-financial-statements-DetailTotalList","view-merchant-meal-management-booking-setting-CommonPagination","view-merchant-order-RechargeOrder","view-merchant-order-RefundOrder"],{"4baf":function(t,e,r){"use strict";r("aa91")},"85e9":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[e("el-pagination",{staticClass:"ps-text",attrs:{"current-page":t.currentPage,"page-sizes":[10,20,50,100,500],"page-size":t.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:t.total,background:"","popper-class":"ps-popper-select"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange,"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e}}})],1)},a=[],i={data:function(){return{pageSize:10,currentPage:1}},props:{total:{type:Number},onPaginationChange:{type:Function}},methods:{handleSizeChange:function(t,e){this.pageSize=t,e||this.onPaginationChange({current:this.currentPage,pageSize:this.pageSize})},handleCurrentChange:function(t,e){this.currentPage=t,e||this.onPaginationChange({current:this.currentPage,pageSize:this.pageSize})}}},o=i,c=r("2877"),s=Object(c["a"])(o,n,a,!1,null,null,null);e["default"]=s.exports},"87ac":function(t,e,r){"use strict";var n=r("ed08"),a=r("2f62");function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function o(t,e){return f(t)||u(t,e)||s(t,e)||c()}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function s(t,e){if(t){if("string"==typeof t)return l(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?l(t,e):void 0}}function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function u(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,i,o,c=[],s=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);s=!0);}catch(t){l=!0,a=t}finally{try{if(!s&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw a}}return c}}function f(t){if(Array.isArray(t))return t}function p(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */p=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},c=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,o=Object.create(i.prototype),c=new D(n||[]);return a(o,"_invoke",{value:j(t,r,c)}),o}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var y="suspendedStart",d="suspendedYield",g="executing",m="completed",v={};function b(){}function w(){}function _(){}var x={};u(x,c,(function(){return this}));var S=Object.getPrototypeOf,k=S&&S(S($([])));k&&k!==r&&n.call(k,c)&&(x=k);var O=_.prototype=b.prototype=Object.create(x);function L(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function P(t,e){function r(a,o,c,s){var l=h(t[a],t,o);if("throw"!==l.type){var u=l.arg,f=u.value;return f&&"object"==i(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,c,s)}),(function(t){r("throw",t,c,s)})):e.resolve(f).then((function(t){u.value=t,c(u)}),(function(t){return r("throw",t,c,s)}))}s(l.arg)}var o;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return o=o?o.then(a,a):a()}})}function j(e,r,n){var a=y;return function(i,o){if(a===g)throw Error("Generator is already running");if(a===m){if("throw"===i)throw o;return{value:t,done:!0}}for(n.method=i,n.arg=o;;){var c=n.delegate;if(c){var s=E(c,n);if(s){if(s===v)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===y)throw a=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=g;var l=h(e,r,n);if("normal"===l.type){if(a=n.done?m:d,l.arg===v)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(a=m,n.method="throw",n.arg=l.arg)}}}function E(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,E(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=h(a,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var o=i.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function C(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function T(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function D(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(C,this),this.reset(!0)}function $(e){if(e||""===e){var r=e[c];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,o=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(i(e)+" is not iterable")}return w.prototype=_,a(O,"constructor",{value:_,configurable:!0}),a(_,"constructor",{value:w,configurable:!0}),w.displayName=u(_,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,u(t,l,"GeneratorFunction")),t.prototype=Object.create(O),t},e.awrap=function(t){return{__await:t}},L(P.prototype),u(P.prototype,s,(function(){return this})),e.AsyncIterator=P,e.async=function(t,r,n,a,i){void 0===i&&(i=Promise);var o=new P(f(t,r,n,a),i);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},L(O),u(O,l,"Generator"),u(O,c,(function(){return this})),u(O,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=$,D.prototype={constructor:D,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(T),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return c.type="throw",c.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],c=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var s=n.call(o,"catchLoc"),l=n.call(o,"finallyLoc");if(s&&l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),T(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;T(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:$(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function h(t,e,r,n,a,i,o){try{var c=t[i](o),s=c.value}catch(t){return void r(t)}c.done?e(s):Promise.resolve(s).then(n,a)}function y(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var i=t.apply(e,r);function o(t){h(i,n,a,o,c,"next",t)}function c(t){h(i,n,a,o,c,"throw",t)}o(void 0)}))}}function d(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function g(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d(Object(r),!0).forEach((function(e){m(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function m(t,e,r){return(e=v(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function v(t){var e=b(t,"string");return"symbol"==i(e)?e:e+""}function b(t,e){if("object"!=i(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}e["a"]={data:function(){return{currentTableSetting:[],dialogPrintVisible:!1}},computed:g({},Object(a["c"])(["userInfo"])),mounted:function(){},methods:{initPrintSetting:function(){var t=this;return y(p().mark((function e(){var r,a;return p().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=[],e.next=3,t.getPrintSettingInfo();case 3:r=e.sent;try{r=r?Object(n["E"])(r):Object(n["E"])(t.tableSetting)}catch(i){r=Object(n["E"])(t.tableSetting)}r.length<12?(a=Object(n["m"])(t.tableSetting,r),a=t.deleteWidthKey(a),t.currentTableSetting=a):t.currentTableSetting=Object(n["m"])(t.tableSetting,r);case 6:case"end":return e.stop()}}),e)})))()},getPrintSettingInfo:function(){var t=this;return y(p().mark((function e(){var r,a,i,c,s;return p().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=null,e.next=3,Object(n["Z"])(t.$apis.apiBackgroundAdminAccountGetAccountPrintInfoPost({id:t.userInfo.account_id,print_key:t.printType}));case 3:if(a=e.sent,i=o(a,2),c=i[0],s=i[1],!c){e.next=10;break}return t.$message.error(c.message),e.abrupt("return",r);case 10:return 0===s.code?r=s.data:t.$message.error(s.msg),e.abrupt("return",r);case 12:case"end":return e.stop()}}),e)})))()},setPrintSettingInfo:function(t,e){var r=this;return y(p().mark((function a(){var i,c,s,l;return p().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,Object(n["Z"])(r.$apis.apiBackgroundAdminAccountSetAccountPrintInfoPost(g({id:r.userInfo.account_id,print_key:r.printType,print_list:t},e)));case 2:if(i=a.sent,c=o(i,2),s=c[0],l=c[1],!s){a.next=9;break}return r.$message.error(s.message),a.abrupt("return");case 9:0===l.code?r.$message.success("设置成功"):r.$message.error(l.msg);case 10:case"end":return a.stop()}}),a)})))()},openPrintSetting:function(){this.dialogPrintVisible=!0},confirmPrintDialog:function(t,e){var r=this;return y(p().mark((function a(){var i;return p().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(!t){a.next=6;break}return i=Object(n["f"])(t),i.length<12&&(i=r.deleteWidthKey(i)),a.next=5,r.setPrintSettingInfo(i,e);case 5:r.currentTableSetting=i;case 6:case"end":return a.stop()}}),a)})))()},deleteWidthKey:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"children";function r(t){t.map((function(t){t.width&&delete t.width,t.minWidth&&delete t.minWidth,t[e]&&t[e].length>0&&r(t[e])}))}return r(t),t},indexMethod:function(t){return(this.page-1)*this.pageSize+(t+1)},setCollectData:function(t){var e=this;this.collect.forEach((function(r){t.data.collect&&void 0!==t.data.collect[r.key]&&e.$set(r,"value",t.data.collect[r.key])}))},setSummaryData:function(t){var e=t.data.collect;e[this.tableSetting[0].key]="合计",this.tableData.length&&this.tableData.push(e)}},beforeDestroy:function(){}}},aa91:function(t,e,r){},fbc5:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",[e("div",{staticClass:"booking-meal-wrapper container-wrapper"},[e("refresh-tool",{on:{refreshPage:t.refreshHandle}}),e("search-form",{ref:"searchRef",attrs:{loading:t.isLoading,"form-setting":t.searchFormSetting,autoSearch:!1},on:{search:t.searchHandle}}),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("数据列表")]),e("div",{staticClass:"align-r"},[e("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_order.finance_report.unified_order_list_export"],expression:"['background_order.finance_report.unified_order_list_export']"}],attrs:{color:"plain",type:"export"},on:{click:t.gotoExport}},[t._v("导出Excel")]),e("button-icon",{attrs:{color:"plain"},on:{click:t.gotoPrint}},[t._v("打印")]),e("button-icon",{attrs:{color:"plain"},on:{click:t.openPrintSetting}},[t._v("报表设置")])],1)]),e("div",{staticClass:"table-content"},[e("custom-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{border:"","table-data":t.tableData,"table-setting":t.currentTableSetting,stripe:"",index:t.indexMethod,isFirst:t.isFirstSearch,"header-row-class-name":"ps-table-header-row"}})],1),e("table-statistics",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoadingCollect,expression:"isLoadingCollect"}],attrs:{"element-loading-custom-class":"el-loading-wrapp","element-loading-spinner":"loading","element-loading-text":t.elementLoadingText,statistics:t.collect}}),e("common-pagination",{ref:"pagination",attrs:{total:t.total,onPaginationChange:t.onPaginationChange}})],1)],1),t.dialogPrintVisible?e("print-setting",{attrs:{extraParams:{printType:t.printType},tableSetting:t.tableSetting,defaultCheckedSetting:t.currentTableSetting,show:t.dialogPrintVisible},on:{"update:show":function(e){t.dialogPrintVisible=e},confirm:t.confirmPrintDialog}}):t._e()],1)},a=[],i=r("85e9"),o=r("3eb4"),c=r("ed08"),s=r("f63a"),l=r("87ac");function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach((function(e){h(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function h(t,e,r){return(e=y(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function y(t){var e=d(t,"string");return"symbol"==u(e)?e:e+""}function d(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function g(t){return w(t)||b(t)||v(t)||m()}function m(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function v(t,e){if(t){if("string"==typeof t)return _(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_(t,e):void 0}}function b(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function w(t){if(Array.isArray(t))return _(t)}function _(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function x(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */x=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var i=e&&e.prototype instanceof v?e:v,o=Object.create(i.prototype),c=new D(n||[]);return a(o,"_invoke",{value:j(t,r,c)}),o}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var h="suspendedStart",y="suspendedYield",d="executing",g="completed",m={};function v(){}function b(){}function w(){}var _={};l(_,o,(function(){return this}));var S=Object.getPrototypeOf,k=S&&S(S($([])));k&&k!==r&&n.call(k,o)&&(_=k);var O=w.prototype=v.prototype=Object.create(_);function L(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function P(t,e){function r(a,i,o,c){var s=p(t[a],t,i);if("throw"!==s.type){var l=s.arg,f=l.value;return f&&"object"==u(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,o,c)}),(function(t){r("throw",t,o,c)})):e.resolve(f).then((function(t){l.value=t,o(l)}),(function(t){return r("throw",t,o,c)}))}c(s.arg)}var i;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return i=i?i.then(a,a):a()}})}function j(e,r,n){var a=h;return function(i,o){if(a===d)throw Error("Generator is already running");if(a===g){if("throw"===i)throw o;return{value:t,done:!0}}for(n.method=i,n.arg=o;;){var c=n.delegate;if(c){var s=E(c,n);if(s){if(s===m)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===h)throw a=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=d;var l=p(e,r,n);if("normal"===l.type){if(a=n.done?g:y,l.arg===m)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(a=g,n.method="throw",n.arg=l.arg)}}}function E(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,E(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var i=p(a,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,m;var o=i.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function C(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function T(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function D(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(C,this),this.reset(!0)}function $(e){if(e||""===e){var r=e[o];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,i=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(u(e)+" is not iterable")}return b.prototype=w,a(O,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:b,configurable:!0}),b.displayName=l(w,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,l(t,s,"GeneratorFunction")),t.prototype=Object.create(O),t},e.awrap=function(t){return{__await:t}},L(P.prototype),l(P.prototype,c,(function(){return this})),e.AsyncIterator=P,e.async=function(t,r,n,a,i){void 0===i&&(i=Promise);var o=new P(f(t,r,n,a),i);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},L(O),l(O,s,"Generator"),l(O,o,(function(){return this})),l(O,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=$,D.prototype={constructor:D,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(T),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return c.type="throw",c.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],c=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var s=n.call(o,"catchLoc"),l=n.call(o,"finallyLoc");if(s&&l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),T(r),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;T(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:$(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),m}},e}function S(t,e,r,n,a,i,o){try{var c=t[i](o),s=c.value}catch(t){return void r(t)}c.done?e(s):Promise.resolve(s).then(n,a)}function k(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var i=t.apply(e,r);function o(t){S(i,n,a,o,c,"next",t)}function c(t){S(i,n,a,o,c,"throw",t)}o(void 0)}))}}var O={name:"DetailTotalList",components:{CommonPagination:i["default"]},mixins:[s["a"],l["a"]],data:function(){return{isLoading:!1,isLoadingCollect:!1,elementLoadingText:"数据正在加载，请耐心等待...",tableSetting:[{label:"序号",key:"index",type:"index",width:"50px"},{label:"订单号",key:"trade_no",width:"150px"},{label:"第三方订单号",key:"out_trade_no",width:"150px"},{label:"创建时间",key:"create_time",width:"160px"},{label:"支付时间",key:"pay_time",width:"160px"},{label:"扣款时间",key:"deduction_time",width:"160px"},{label:"动账钱包",key:"wallet",width:"120px"},{label:"支付类型",key:"payway_alias"},{label:"支付方式",key:"sub_payway_alias"},{label:"访客餐支付方式",key:"pay_method_alias"},{label:"订单金额",key:"total_fee",type:"money"},{label:"优惠",key:"discount",type:"percent"},{label:"优惠金额",key:"discount_fee",type:"money"},{label:"优惠类型",key:"discount_type_alias"},{label:"优惠名称",key:"discount_name"},{label:"券类型",key:"coupon_type_alias"},{label:"抵扣金额",key:"deduction_fee",type:"money"},{label:"餐补",key:"food_subsidy_fee",type:"money"},{label:"手续费",key:"rate_fee",type:"money"},{label:"服务费",key:"fuwu_fee",type:"money"},{label:"动账金额",key:"settlement_fee",type:"money"},{label:"动账组织",key:"wallet_org"},{label:"补贴动账",key:"subsidy_fee",type:"money"},{label:"储值动账",key:"wallet_fee",type:"money"},{label:"赠送动账",key:"complimentary_fee",type:"money"},{label:"补贴钱包余额",key:"subsidy_balance",type:"money"},{label:"储值钱包余额",key:"wallet_balance",type:"money"},{label:"赠送钱包余额",key:"complimentary_balance",type:"money"},{label:"操作类型",key:"operate_type_alias"},{label:"餐段",key:"meal_type"},{label:"姓名",key:"payer_name"},{label:"手机号",key:"payer_phone",width:"110px "},{label:"人员编号",key:"payer_person_no"},{label:"卡号",key:"payer_card_no"},{label:"分组",key:"payer_group"},{label:"部门",key:"payer_department_group",width:"120px"},{label:"交易设备",key:"device_number"},{label:"操作员",key:"controller",width:"110px"}],defaultProps:{children:"children",label:"label"},tableData:[],page:1,pageSize:10,total:0,searchFormSetting:Object(c["f"])(o["DetailTotalSearchForm2"]),collect:[{key:"consume",value:0,label:"消费订单金额:￥",type:"money"},{key:"discounts",value:0,label:"累计优惠金额:￥",type:"money"},{key:"balance_consume",value:0,label:"累计动账金额:￥",type:"money"},{key:"recharge",value:0,label:"累计充值:￥",type:"money"},{key:"draw",value:0,label:"累计提现:￥",type:"money"},{key:"refund",value:0,label:"累计退款:￥",type:"money"},{key:"subsidy_grant",value:0,label:"补贴发放:￥",type:"money"},{key:"subsidy_reset",value:0,label:"补贴清零:￥",type:"money"},{key:"total_rate_fee",value:0,label:"手续费合计：¥",type:"money"},{key:"fuwu_fee",value:0,label:"服务费（实收）合计：¥",type:"money"}],currentTableSetting:[],dialogPrintVisible:!1,printType:"DetailTotalList",isFirstSearch:!0}},created:function(){this.initLoad(!0)},mounted:function(){this.getpayList(),this.getLevelNameList()},methods:{initLoad:function(t){t||(this.requestPaymentOrderTotalList(),this.requestPaymentOrderTotalCollect())},refreshHandle:function(){this.$refs.searchRef.resetForm(),this.$refs.pagination.handleCurrentChange(1,!0),this.$refs.pagination.handleSizeChange(10,!0),this.tableData=[],this.isFirstSearch=!0},searchHandle:function(t){var e=this;return k(x().mark((function r(){return x().wrap((function(r){while(1)switch(r.prev=r.next){case 0:t&&"search"===t&&(e.$refs.pagination.handleCurrentChange(1,!0),e.$refs.pagination.handleSizeChange(10,!0),e.onPaginationChange({current:1,pageSize:10}),e.requestPaymentOrderTotalCollect(),e.isFirstSearch=!1);case 1:case"end":return r.stop()}}),r)})))()},deleteEmptyGroup:function(t){var e=this;function r(t){t.map((function(t){t.children_list&&t.children_list.length>0?r(t.children_list):e.$delete(t,"children_list")}))}return r(t),t},userGroupList:function(){var t=this;return k(x().mark((function e(){var r;return x().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.$apis.apiCardServiceCardUserGroupListPost({status:"enable",page:1,page_size:9999999});case 3:r=e.sent,t.isLoading=!1,0===r.code?(t.groupList=r.data.results,t.searchFormSetting.payer_group_ids.dataList=r.data.results):t.$message.error(r.msg);case 6:case"end":return e.stop()}}),e)})))()},handleExport:function(){var t=this;this.$confirm("确定导出？","提示",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0}).then((function(e){var r=Object(c["w"])(t.searchFormSetting,1,9999999);t.$router.push({name:"Excel",query:{type:"PaymentOrderTotal",params:JSON.stringify(r)}})})).catch((function(t){}))},requestOrganizationConsumeList:function(){var t=this;return k(x().mark((function e(){var r;return x().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.$apis.apiBackgroundOrganizationOrganizationConsumeListPost({page:1,pageSize:9999999});case 3:r=e.sent,t.isLoading=!1,0===r.code?t.searchFormSetting.org_ids.dataList=r.data.results:t.$message.error(r.msg);case 6:case"end":return e.stop()}}),e)})))()},requestDeviceType:function(){var t=this;return k(x().mark((function e(){var r;return x().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.apiBackgroundDeviceDeviceDeviceTypePost({});case 2:r=e.sent,0===r.code||t.$message.error(r.msg);case 4:case"end":return e.stop()}}),e)})))()},requestPaymentOrderTotalList:function(){var t=this;return k(x().mark((function e(){var r,n;return x().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=Object(c["w"])(t.searchFormSetting,t.page,t.pageSize),t.isLoading=!0,e.next=4,t.$apis.apiBackgroundReportCenterDataReportUnifiedOrderListPost(r);case 4:n=e.sent,t.isLoading=!1,0===n.code?(t.total=n.data.count,t.tableData=n.data.result):t.$message.error(n.msg);case 7:case"end":return e.stop()}}),e)})))()},requestPaymentOrderTotalCollect:function(){var t=this;return k(x().mark((function e(){var r,n;return x().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=Object(c["w"])(t.searchFormSetting),t.isLoadingCollect=!0,e.next=4,t.$apis.apiBackgroundReportCenterDataReportUnifiedOrderListCollectPost(r);case 4:n=e.sent,0===n.code?(t.elementLoadingText="数据正在加载，请耐心等待...",t.isLoadingCollect=!1,t.setCollectData(n)):(t.elementLoadingText="汇总数据加载失败，请重试。",t.$message.error("汇总数据加载失败，请重试。"));case 6:case"end":return e.stop()}}),e)})))()},getWalletList:function(){var t=this;return k(x().mark((function e(){var r,n;return x().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.apiBackgroundReportCenterDataReportWalletListPost();case 2:r=e.sent,0===r.code?(n=[],r.data.result.forEach((function(t){Object.keys(t).forEach((function(e){return n.push({label:t[e],value:e})}))})),t.searchFormSetting.wallet_org.dataList=n):t.$message.error(r.msg);case 4:case"end":return e.stop()}}),e)})))()},getLevelNameList:function(){var t=this;return k(x().mark((function e(){var r,n,a,i;return x().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.apiBackgroundReportCenterDataReportGetLevelNamePost();case 2:n=e.sent,a=JSON.parse(JSON.stringify(n.data).replace(/name/g,"label")),i=JSON.parse(JSON.stringify(a).replace(/level/g,"key")),(r=t.tableSetting).splice.apply(r,[4,0].concat(g(i))),t.initPrintSetting();case 7:case"end":return e.stop()}}),e)})))()},getpayList:function(){var t=this;return k(x().mark((function e(){var r,n,a;return x().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.apiBackgroundReportCenterDataReportGetPayInfoPost({type:"all"});case 2:r=e.sent,0===r.code?(n=[],a=[],r.data.result.payways.forEach((function(t){Object.keys(t).forEach((function(e){return n.push({label:t[e],value:e})}))})),r.data.result.sub_payways.forEach((function(t){Object.keys(t).forEach((function(e){return a.push({label:t[e],value:e})}))})),t.searchFormSetting.payway_list.dataList=[].concat(n),t.searchFormSetting.sub_payway_list.dataList=[].concat(a)):t.$message.error(r.msg);case 4:case"end":return e.stop()}}),e)})))()},flatten:function(t){var e=this,r=[];return t.map((function(t){Array.isArray(t)?r=r.concat(e.flatten(t)):r.push(t)})),r},onPaginationChange:function(t){this.page=t.current,this.pageSize=t.pageSize,this.requestPaymentOrderTotalList()},gotoExport:function(){var t={type:"DetailTotalList",params:Object(c["w"])(this.searchFormSetting,this.page,this.pageSize)};this.exportHandle(t)},gotoPrint:function(){var t=Object(c["w"])(this.searchFormSetting),e=this.$router.resolve({name:"Print",query:{print_date_state:!0,print_type:this.printType,print_title:"明细总表",result_key:"result",api:"apiBackgroundReportCenterDataReportUnifiedOrderListPost",show_print_header_and_footer:!0,table_setting:JSON.stringify(this.tableSetting),current_table_setting:JSON.stringify(this.currentTableSetting),collect:JSON.stringify(this.collect),push_summary:!1,params:JSON.stringify(p(p({},t),{},{page:1,page_size:this.total?this.total:10}))}}),r=e.href;window.open(r,"_blank")}}},L=O,P=(r("4baf"),r("2877")),j=Object(P["a"])(L,n,a,!1,null,null,null);e["default"]=j.exports}}]);