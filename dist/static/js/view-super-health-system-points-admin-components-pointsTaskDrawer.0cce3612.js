(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-points-admin-components-pointsTaskDrawer"],{"6f76":function(t,e,r){},7252:function(t,e,r){"use strict";r("6f76")},"830e":function(t,e,r){"use strict";r.r(e);var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"drawer-box"},[e("customDrawer",{attrs:{show:t.visible,loading:t.isLoading,title:t.drawerTitle,confirmShow:"details"!==t.drawerType,cancelClass:"details"===t.drawerType?"ps-btn":"ps-cancel-btn",cancelText:"details"===t.drawerType?"关 闭":"取 消",size:800},on:{"update:show":function(e){t.visible=e},confirm:t.saveSetting}},[e("div",{staticClass:"drawer-container"},[e("div",{staticClass:"drawer-content"},[e("el-form",{ref:"drawerFormDataRef",attrs:{model:t.drawerFormData,rules:t.drawerFormDataRuls,"label-width":"90px"}},[e("el-form-item",{attrs:{label:"任务名称",prop:"name"}},[e("el-input",{staticClass:"w-350 ps-input",attrs:{maxlength:"20",placeholder:"请输入任务名称",disabled:"details"===t.drawerType},model:{value:t.drawerFormData.name,callback:function(e){t.$set(t.drawerFormData,"name",e)},expression:"drawerFormData.name"}})],1),e("el-form-item",{attrs:{label:"完成条件",prop:"conditionType"}},[e("el-radio-group",{staticClass:"ps-radio",attrs:{disabled:"details"===t.drawerType},model:{value:t.drawerFormData.conditionType,callback:function(e){t.$set(t.drawerFormData,"conditionType",e)},expression:"drawerFormData.conditionType"}},[e("el-radio",{attrs:{label:"specify_page"}},[t._v("跳转指定界面")]),e("el-radio",{attrs:{label:"specify_action"}},[t._v("完成指定动作")]),e("el-radio",{attrs:{label:"specify_url"}},[t._v("跳转指定链接")])],1),"specify_page"===t.drawerFormData.conditionType?e("el-form-item",{key:"specifyPageType",attrs:{prop:"specifyPageType"}},[e("el-select",{staticClass:"ps-select w-350",attrs:{placeholder:"请选择指定界面",disabled:"details"===t.drawerType},model:{value:t.drawerFormData.specifyPageType,callback:function(e){t.$set(t.drawerFormData,"specifyPageType",e)},expression:"drawerFormData.specifyPageType"}},t._l(t.specifyPageTypeList,(function(t){return e("el-option",{key:t.type,attrs:{label:t.name,value:t.type}})})),1)],1):t._e(),"specify_action"===t.drawerFormData.conditionType?e("el-form-item",{key:"specifyActionType",attrs:{prop:"specifyActionType"}},[e("el-select",{staticClass:"ps-select w-350",attrs:{placeholder:"请选择指定动作",disabled:"details"===t.drawerType},model:{value:t.drawerFormData.specifyActionType,callback:function(e){t.$set(t.drawerFormData,"specifyActionType",e)},expression:"drawerFormData.specifyActionType"}},t._l(t.specifyActionTypeList,(function(t){return e("el-option",{key:t.type,attrs:{label:t.name,value:t.type}})})),1)],1):t._e(),"specify_url"===t.drawerFormData.conditionType?e("el-form-item",{key:"specifyUrl",attrs:{prop:"specifyUrl"}},[e("el-input",{staticClass:"w-350 ps-input",attrs:{maxlength:"20",placeholder:"请输入链接",disabled:"details"===t.drawerType},model:{value:t.drawerFormData.specifyUrl,callback:function(e){t.$set(t.drawerFormData,"specifyUrl",e)},expression:"drawerFormData.specifyUrl"}})],1):t._e()],1),e("el-form-item",{attrs:{label:"完成次数",prop:"completionsNumType"}},[e("el-radio-group",{staticClass:"ps-radio",attrs:{disabled:"details"===t.drawerType},model:{value:t.drawerFormData.completionsNumType,callback:function(e){t.$set(t.drawerFormData,"completionsNumType",e)},expression:"drawerFormData.completionsNumType"}},[e("el-radio",{attrs:{label:"day"}},[t._v("每天")]),e("el-radio",{attrs:{label:"week"}},[t._v("每周")]),e("el-radio",{attrs:{label:"month"}},[t._v("每月")]),e("el-radio",{attrs:{label:"one"}},[t._v("一次性")])],1),"week"===t.drawerFormData.completionsNumType||"month"===t.drawerFormData.completionsNumType?e("el-form-item",[e("span",{staticStyle:{"margin-right":"10px"}},[t._v("按天执行")]),e("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2",disabled:"details"===t.drawerType},model:{value:t.drawerFormData.isDay,callback:function(e){t.$set(t.drawerFormData,"isDay",e)},expression:"drawerFormData.isDay"}})],1):t._e()],1),e("el-form-item",{attrs:{label:"获得积分"}},[t._l(t.drawerFormData.pointsList,(function(a,i){return e("el-form-item",{key:"pointsList"+i,staticClass:"points-content",attrs:{label:"第"+(i+1)+"次获得",prop:"pointsList[".concat(i,"]")+".points","label-width":"100px",rules:t.drawerFormDataRuls.points,"show-message":!1}},[e("div",{staticStyle:{display:"flex","align-items":"center"}},[e("el-input",{staticClass:"w-150 ps-input",attrs:{maxlength:"6",placeholder:"请输入积分",disabled:"details"===t.drawerType},model:{value:a.points,callback:function(e){t.$set(a,"points",e)},expression:"item.points"}}),e("span",{staticStyle:{"padding-left":"10px"}},[t._v("积分")]),t.drawerFormData.pointsList.length<20&&"details"!==t.drawerType?e("img",{attrs:{src:r("a851"),alt:""},on:{click:function(e){return t.clickPointsList()}}}):t._e(),i>0&&"details"!==t.drawerType?e("img",{attrs:{src:r("1597"),alt:""},on:{click:function(e){return t.delPointsList(i)}}}):t._e()],1)])})),e("el-form-item",{staticClass:"points-content",attrs:{label:"往后每次获得","label-width":"100px"}},[e("el-input",{staticClass:"w-150 ps-input",attrs:{maxlength:"6",placeholder:"请输入积分",disabled:"details"===t.drawerType},model:{value:t.drawerFormData.afterCount,callback:function(e){t.$set(t.drawerFormData,"afterCount",e)},expression:"drawerFormData.afterCount"}}),e("span",{staticStyle:{"padding-left":"10px"}},[t._v("积分")])],1)],2),e("el-form-item",{attrs:{label:"优先级",prop:"priority"}},[e("el-input",{staticClass:"w-350 ps-input",attrs:{maxlength:"6",placeholder:"请输入优先级",disabled:"details"===t.drawerType},model:{value:t.drawerFormData.priority,callback:function(e){t.$set(t.drawerFormData,"priority",e)},expression:"drawerFormData.priority"}}),e("div",[t._v("现有优先级："+t._s(t.priorityText()))])],1),e("el-form-item",{attrs:{label:"按钮文案",prop:"canRemark"}},[e("el-input",{staticClass:"w-350 ps-input",attrs:{maxlength:"6",placeholder:"请输入按钮文案",disabled:"details"===t.drawerType},model:{value:t.drawerFormData.canRemark,callback:function(e){t.$set(t.drawerFormData,"canRemark",e)},expression:"drawerFormData.canRemark"}})],1),e("el-form-item",{attrs:{label:"提示文案",prop:"taskRemark"}},[e("el-input",{staticClass:"w-350 ps-input",attrs:{placeholder:"请输入提示文案",maxlength:"20",disabled:"details"===t.drawerType},model:{value:t.drawerFormData.taskRemark,callback:function(e){t.$set(t.drawerFormData,"taskRemark",e)},expression:"drawerFormData.taskRemark"}})],1),e("div",{staticStyle:{display:"flex","align-items":"center","margin-left":"33px"}},[e("div",{staticStyle:{"padding-right":"13px",color:"#606266","font-weight":"700","font-size":"14px"}},[t._v(" h5样式 ")]),e("div",{staticClass:"case-btn"},[e("div",{staticClass:"case-title"},[e("div",[t._v(t._s(t.drawerFormData.name?t.drawerFormData.name:"任务名称"))]),e("div",{staticClass:"case-tips"},[t._v(" "+t._s(t.drawerFormData.taskRemark?t.drawerFormData.taskRemark:"提示文案")+" ")])]),e("div",[e("el-button",{staticClass:"ps-btn",attrs:{type:"primary"}},[t._v(" "+t._s(t.drawerFormData.canRemark?t.drawerFormData.canRemark:"按钮文案")+" ")])],1)])])],1)],1)])])],1)},i=[],n=r("ed08");function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,a)}return r}function c(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach((function(e){l(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function l(t,e,r){return(e=p(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function p(t){var e=u(t,"string");return"symbol"==o(e)?e:e+""}function u(t,e){if("object"!=o(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var a=r.call(t,e||"default");if("object"!=o(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function d(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */d=function(){return e};var t,e={},r=Object.prototype,a=r.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},n="function"==typeof Symbol?Symbol:{},s=n.iterator||"@@iterator",c=n.asyncIterator||"@@asyncIterator",l=n.toStringTag||"@@toStringTag";function p(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{p({},"")}catch(t){p=function(t,e,r){return t[e]=r}}function u(t,e,r,a){var n=e&&e.prototype instanceof v?e:v,o=Object.create(n.prototype),s=new E(a||[]);return i(o,"_invoke",{value:P(t,r,s)}),o}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=u;var m="suspendedStart",y="suspendedYield",h="executing",w="completed",g={};function v(){}function b(){}function D(){}var _={};p(_,s,(function(){return this}));var F=Object.getPrototypeOf,T=F&&F(F(C([])));T&&T!==r&&a.call(T,s)&&(_=T);var k=D.prototype=v.prototype=Object.create(_);function x(t){["next","throw","return"].forEach((function(e){p(t,e,(function(t){return this._invoke(e,t)}))}))}function L(t,e){function r(i,n,s,c){var l=f(t[i],t,n);if("throw"!==l.type){var p=l.arg,u=p.value;return u&&"object"==o(u)&&a.call(u,"__await")?e.resolve(u.__await).then((function(t){r("next",t,s,c)}),(function(t){r("throw",t,s,c)})):e.resolve(u).then((function(t){p.value=t,s(p)}),(function(t){return r("throw",t,s,c)}))}c(l.arg)}var n;i(this,"_invoke",{value:function(t,a){function i(){return new e((function(e,i){r(t,a,e,i)}))}return n=n?n.then(i,i):i()}})}function P(e,r,a){var i=m;return function(n,o){if(i===h)throw Error("Generator is already running");if(i===w){if("throw"===n)throw o;return{value:t,done:!0}}for(a.method=n,a.arg=o;;){var s=a.delegate;if(s){var c=O(s,a);if(c){if(c===g)continue;return c}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(i===m)throw i=w,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);i=h;var l=f(e,r,a);if("normal"===l.type){if(i=a.done?w:y,l.arg===g)continue;return{value:l.arg,done:a.done}}"throw"===l.type&&(i=w,a.method="throw",a.arg=l.arg)}}}function O(e,r){var a=r.method,i=e.iterator[a];if(i===t)return r.delegate=null,"throw"===a&&e.iterator.return&&(r.method="return",r.arg=t,O(e,r),"throw"===r.method)||"return"!==a&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+a+"' method")),g;var n=f(i,e.iterator,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,g;var o=n.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,g):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function j(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function S(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(j,this),this.reset(!0)}function C(e){if(e||""===e){var r=e[s];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,n=function r(){for(;++i<e.length;)if(a.call(e,i))return r.value=e[i],r.done=!1,r;return r.value=t,r.done=!0,r};return n.next=n}}throw new TypeError(o(e)+" is not iterable")}return b.prototype=D,i(k,"constructor",{value:D,configurable:!0}),i(D,"constructor",{value:b,configurable:!0}),b.displayName=p(D,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,D):(t.__proto__=D,p(t,l,"GeneratorFunction")),t.prototype=Object.create(k),t},e.awrap=function(t){return{__await:t}},x(L.prototype),p(L.prototype,c,(function(){return this})),e.AsyncIterator=L,e.async=function(t,r,a,i,n){void 0===n&&(n=Promise);var o=new L(u(t,r,a,i),n);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},x(k),p(k,l,"Generator"),p(k,s,(function(){return this})),p(k,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var a in e)r.push(a);return r.reverse(),function t(){for(;r.length;){var a=r.pop();if(a in e)return t.value=a,t.done=!1,t}return t.done=!0,t}},e.values=C,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(S),!e)for(var r in this)"t"===r.charAt(0)&&a.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function i(a,i){return s.type="throw",s.arg=e,r.next=a,i&&(r.method="next",r.arg=t),!!i}for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n],s=o.completion;if("root"===o.tryLoc)return i("end");if(o.tryLoc<=this.prev){var c=a.call(o,"catchLoc"),l=a.call(o,"finallyLoc");if(c&&l){if(this.prev<o.catchLoc)return i(o.catchLoc,!0);if(this.prev<o.finallyLoc)return i(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return i(o.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return i(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&a.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var n=i;break}}n&&("break"===t||"continue"===t)&&n.tryLoc<=e&&e<=n.finallyLoc&&(n=null);var o=n?n.completion:{};return o.type=t,o.arg=e,n?(this.method="next",this.next=n.finallyLoc,g):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),S(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var a=r.completion;if("throw"===a.type){var i=a.arg;S(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,r,a){return this.delegate={iterator:C(e),resultName:r,nextLoc:a},"next"===this.method&&(this.arg=t),g}},e}function f(t,e){return g(t)||w(t,e)||y(t,e)||m()}function m(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function y(t,e){if(t){if("string"==typeof t)return h(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?h(t,e):void 0}}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,a=Array(e);r<e;r++)a[r]=t[r];return a}function w(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var a,i,n,o,s=[],c=!0,l=!1;try{if(n=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(a=n.call(r)).done)&&(s.push(a.value),s.length!==e);c=!0);}catch(t){l=!0,i=t}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return s}}function g(t){if(Array.isArray(t))return t}function v(t,e,r,a,i,n,o){try{var s=t[n](o),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(a,i)}function b(t){return function(){var e=this,r=arguments;return new Promise((function(a,i){var n=t.apply(e,r);function o(t){v(n,a,i,o,s,"next",t)}function s(t){v(n,a,i,o,s,"throw",t)}o(void 0)}))}}var D={props:{drawerType:{type:String,default:function(){return"add"}},isshow:Boolean,drawerModifyData:{type:Object,default:function(){return{}}},collectData:{type:Object,default:function(){return{}}}},data:function(){var t=function(t,e,r){var a=/^\d+$/;if(!e)return r(new Error("格式错误"));a.test(e)?r():r(new Error("格式错误"))};return{drawerTitle:"新建任务",isLoading:!1,drawerFormData:{name:"",conditionType:"specify_page",specifyPageType:"",specifyActionType:"",specifyUrl:"",completionsNumType:"day",isDay:!1,pointsList:[{num:1,points:""}],afterCount:"",priority:"",canRemark:"",taskRemark:""},drawerFormDataRuls:{name:[{required:!0,message:"请输入任务名称",trigger:"blur"}],conditionType:[{required:!0,trigger:["change","blur"]}],specifyPageType:[{required:!0,message:"请选择指定界面",trigger:"blur"}],specifyActionType:[{required:!0,message:"请选择指定动作",trigger:["change","blur"]}],specifyUrl:[{required:!0,message:"请输入链接",trigger:["change","blur"]}],completionsNumType:[{required:!0,trigger:["change","blur"]}],points:[{required:!0,validator:t,trigger:["change","blur"]}],priority:[{required:!0,validator:t,trigger:["change","blur"]}],canRemark:[{required:!0,message:"请输入按钮文案",trigger:"blur"}],taskRemark:[{required:!0,message:"请输入提示文案",trigger:"blur"}]},specifyPageTypeList:[{name:"首页",type:"front_page"},{name:"营养分析",type:"nutritional_analysis"},{name:"营养周报",type:"nutrition_weekly"}],specifyActionTypeList:[{name:"运动打卡",type:"sports_check_in"},{name:"记录体重",type:"record_weight"},{name:"记录饮食",type:"record_diet"},{name:"习惯打卡",type:"habit_punching"},{name:"分享",type:"share"}]}},computed:{visible:{get:function(){return this.isshow},set:function(t){this.$emit("update:isshow",t)}}},mounted:function(){this.initDrawerForm()},methods:{initDrawerForm:function(){var t=this;"modify"!==this.drawerType&&"details"!==this.drawerType||("modify"===this.drawerType?this.drawerTitle="修改任务":"details"===this.drawerType&&(this.drawerTitle="任务详情"),this.drawerFormData.name=this.drawerModifyData.name,this.drawerFormData.conditionType=this.drawerModifyData.condition_type,this.drawerFormData.completionsNumType=this.drawerModifyData.completions_num_type,this.drawerFormData.priority=this.drawerModifyData.priority,this.drawerFormData.canRemark=this.drawerModifyData.can_remark,this.drawerFormData.taskRemark=this.drawerModifyData.task_remark,"specify_page"===this.drawerModifyData.condition_type?this.drawerFormData.specifyPageType=this.drawerModifyData.specify_page_type:"specify_action"===this.drawerModifyData.condition_type?this.drawerFormData.specifyActionType=this.drawerModifyData.specify_action_type:"specify_url"===this.drawerModifyData.condition_type&&(this.drawerFormData.specifyUrl=this.drawerModifyData.specify_url),"week"!==this.drawerModifyData.completions_num_type&&"month"!==this.drawerModifyData.completions_num_type||(this.drawerFormData.isDay=this.drawerModifyData.is_day),this.drawerFormData.pointsList=[],this.drawerModifyData.points_list.forEach((function(e){-1===e.num?t.drawerFormData.afterCount=e.points:t.drawerFormData.pointsList.push({num:e.num,points:e.points})})))},priorityText:function(){return this.collectData.priority_list&&this.collectData.priority_list.length?this.collectData.priority_list.join(","):""},clickPointsList:function(){this.drawerFormData.pointsList.push({num:this.drawerFormData.pointsList.length+1,points:""})},delPointsList:function(t){this.drawerFormData.pointsList.splice(t,1)},initParams:function(){var t={name:this.drawerFormData.name,condition_type:this.drawerFormData.conditionType,completions_num_type:this.drawerFormData.completionsNumType,priority:this.drawerFormData.priority,can_remark:this.drawerFormData.canRemark,task_remark:this.drawerFormData.taskRemark};return"specify_page"===this.drawerFormData.conditionType?t.specify_page_type=this.drawerFormData.specifyPageType:"specify_action"===this.drawerFormData.conditionType?t.specify_action_type=this.drawerFormData.specifyActionType:"specify_url"===this.drawerFormData.conditionType&&(t.specify_url=this.drawerFormData.specifyUrl),"week"!==this.drawerFormData.completionsNumType&&"month"!==this.drawerFormData.completionsNumType||(t.is_day=this.drawerFormData.isDay),t.points_list=Object(n["f"])(this.drawerFormData.pointsList),this.drawerFormData.afterCount&&t.points_list.push({num:-1,points:this.drawerFormData.afterCount}),t},saveSetting:function(){var t=this;this.$refs.drawerFormDataRef.validate((function(e){e&&("modify"===t.drawerType?t.modifyDrawerForm():t.addDrawerForm())}))},addDrawerForm:function(){var t=this;return b(d().mark((function e(){var r,a,i,n;return d().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.$to(t.$apis.apiBackgroundMemberPointsPointsTaskAddPost(t.initParams()));case 3:if(r=e.sent,a=f(r,2),i=a[0],n=a[1],t.isLoading=!1,!i){e.next=11;break}return t.$message.error(i.message),e.abrupt("return");case 11:0===n.code?(t.visible=!1,t.$message.success("修改成功"),t.$emit("clickSaveDrawer")):t.$message.error(n.msg);case 12:case"end":return e.stop()}}),e)})))()},modifyDrawerForm:function(){var t=this;return b(d().mark((function e(){var r,a,i,n;return d().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.$to(t.$apis.apiBackgroundMemberPointsPointsTaskModifyPost(c({id:t.drawerModifyData.id},t.initParams())));case 3:if(r=e.sent,a=f(r,2),i=a[0],n=a[1],t.isLoading=!1,!i){e.next=11;break}return t.$message.error(i.message),e.abrupt("return");case 11:0===n.code?(t.visible=!1,t.$message.success("修改成功"),t.$emit("clickSaveDrawer")):t.$message.error(n.msg);case 12:case"end":return e.stop()}}),e)})))()}}},_=D,F=(r("7252"),r("2877")),T=Object(F["a"])(_,a,i,!1,null,"4d44eb73",null);e["default"]=T.exports}}]);