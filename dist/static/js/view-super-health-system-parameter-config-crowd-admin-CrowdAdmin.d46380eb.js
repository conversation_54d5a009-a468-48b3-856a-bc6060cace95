(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-parameter-config-crowd-admin-CrowdAdmin"],{7745:function(t,e,r){"use strict";r("fc73")},"800b":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"crowd-admin container-wrapper"},[e("refresh-tool",{on:{refreshPage:t.refreshHandle}}),e("search-form",{ref:"searchRef",attrs:{"form-setting":t.searchFormSetting,"label-width":"105px"},on:{search:t.searchHandle}}),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title",staticStyle:{width:"900px"}},[t._v(" 中国居民膳食营养素参考摄入量（孕妇与哺乳期在同龄人群参考值基础上额外增加量） ")]),e("div",{staticClass:"align-r"},[e("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(e){return t.addAndEditCrowd("add")}}},[t._v(" 创建人群 ")])],1)]),e("div",{staticClass:"table-content"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},[e("el-table-column",{attrs:{prop:"id",label:"编号",align:"center"}}),e("el-table-column",{attrs:{prop:"name",label:"名称",align:"center",width:"200px"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("span",[t._v(t._s(r.row.name))])]}}])}),e("el-table-column",{attrs:{prop:"group",label:"人群",align:"center"}}),e("el-table-column",{attrs:{prop:"",label:"年龄(岁)",align:"center"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("span",[t._v(t._s(r.row.min_age)+"~"+t._s(r.row.max_age))])]}}])}),e("el-table-column",{attrs:{prop:"",label:"标签规则",align:"center"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.seeRule(r.row)}}},[t._v(" 查看 ")])]}}])}),e("el-table-column",{attrs:{prop:"operator_name",label:"创建人",align:"center"}}),e("el-table-column",{attrs:{prop:"create_time",label:"创建时间",align:"center"}}),e("el-table-column",{attrs:{fixed:"right",label:"操作",width:"180",align:"center"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.addAndEditCrowd("modify",r.row)}}},[t._v(" 编辑 ")]),e("el-button",{staticClass:"ps-red",attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHaldler("del",r.row)}}},[t._v(" 删除 ")])]}}])})],1)],1),e("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[e("el-pagination",{staticClass:"ps-text",attrs:{"current-page":t.currentPage,"page-sizes":[10,20,50,100,500],"page-size":t.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:t.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)]),e("el-dialog",{attrs:{title:"查看标签规则",visible:t.ruleDialog,"custom-class":"ps-dialog",width:"600"},on:{"update:visible":function(e){t.ruleDialog=e},close:t.handleClose}},[e("div",[e("span",[t._v("人群名称：")]),e("span",[t._v(t._s(t.labelRuleInfo.group))])]),e("div",{staticClass:"p-t-10 p-b-10"},[e("div",[t._v("推荐菜品标签：")]),t.labelRuleInfo.recommend_label&&t.labelRuleInfo.recommend_label.length?e("div",{staticClass:"p-t-10 p-b-10"},t._l(t.labelRuleInfo.recommend_label,(function(r,n){return e("el-tag",{key:n,staticClass:"m-r-10 m-b-5",attrs:{size:"small",effect:"plain",type:"info",color:"#fff"}},[e("i",{staticClass:"el-icon-success ps-green-text"}),t._v(" "+t._s(r.name)+" ")])})),1):t._e(),t.labelRuleInfo.recommend_tips?e("div",[e("span",[t._v("说明文案：")]),e("span",[t._v(t._s(t.labelRuleInfo.recommend_tips))])]):t._e()]),e("div",{staticClass:"p-t-10 p-b-10"},[e("div",[t._v("不建议菜品标签：")]),t.labelRuleInfo.not_recommend_label&&t.labelRuleInfo.not_recommend_label.length?e("div",{staticClass:"p-t-10 p-b-10"},t._l(t.labelRuleInfo.not_recommend_label,(function(r,n){return e("el-tag",{key:n,staticClass:"m-r-10 m-b-5",attrs:{size:"small",effect:"plain",type:"info",color:"#fff"}},[e("i",{staticClass:"el-icon-warning ps-i"}),t._v(" "+t._s(r.name)+" ")])})),1):t._e(),t.labelRuleInfo.not_recommend_tips?e("div",[e("span",[t._v("说明文案：")]),e("span",[t._v(t._s(t.labelRuleInfo.not_recommend_tips))])]):t._e()])])],1)},a=[],o=r("ed08");function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function l(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */l=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},c=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function p(t,e,r,n){var o=e&&e.prototype instanceof b?e:b,i=Object.create(o.prototype),l=new I(n||[]);return a(i,"_invoke",{value:P(t,r,l)}),i}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=p;var d="suspendedStart",m="suspendedYield",g="executing",y="completed",v={};function b(){}function w(){}function _(){}var x={};f(x,c,(function(){return this}));var C=Object.getPrototypeOf,L=C&&C(C(R([])));L&&L!==r&&n.call(L,c)&&(x=L);var O=_.prototype=b.prototype=Object.create(x);function S(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function j(t,e){function r(a,o,l,c){var s=h(t[a],t,o);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==i(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,l,c)}),(function(t){r("throw",t,l,c)})):e.resolve(f).then((function(t){u.value=t,l(u)}),(function(t){return r("throw",t,l,c)}))}c(s.arg)}var o;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return o=o?o.then(a,a):a()}})}function P(e,r,n){var a=d;return function(o,i){if(a===g)throw Error("Generator is already running");if(a===y){if("throw"===o)throw i;return{value:t,done:!0}}for(n.method=o,n.arg=i;;){var l=n.delegate;if(l){var c=k(l,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===d)throw a=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=g;var s=h(e,r,n);if("normal"===s.type){if(a=n.done?y:m,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(a=y,n.method="throw",n.arg=s.arg)}}}function k(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,k(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var o=h(a,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,v;var i=o.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function A(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function R(e){if(e||""===e){var r=e[c];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,o=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(i(e)+" is not iterable")}return w.prototype=_,a(O,"constructor",{value:_,configurable:!0}),a(_,"constructor",{value:w,configurable:!0}),w.displayName=f(_,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,f(t,u,"GeneratorFunction")),t.prototype=Object.create(O),t},e.awrap=function(t){return{__await:t}},S(j.prototype),f(j.prototype,s,(function(){return this})),e.AsyncIterator=j,e.async=function(t,r,n,a,o){void 0===o&&(o=Promise);var i=new j(p(t,r,n,a),o);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},S(O),f(O,u,"Generator"),f(O,c,(function(){return this})),f(O,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=R,I.prototype={constructor:I,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(A),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return l.type="throw",l.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],l=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var c=n.call(i,"catchLoc"),s=n.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),A(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;A(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:R(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function c(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function s(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?c(Object(r),!0).forEach((function(e){u(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function u(t,e,r){return(e=f(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function f(t){var e=p(t,"string");return"symbol"==i(e)?e:e+""}function p(t,e){if("object"!=i(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function h(t,e){return v(t)||y(t,e)||m(t,e)||d()}function d(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function m(t,e){if(t){if("string"==typeof t)return g(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?g(t,e):void 0}}function g(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function y(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,o,i,l=[],c=!0,s=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(l.push(n.value),l.length!==e);c=!0);}catch(t){s=!0,a=t}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}function v(t){if(Array.isArray(t))return t}function b(t,e,r,n,a,o,i){try{var l=t[o](i),c=l.value}catch(t){return void r(t)}l.done?e(c):Promise.resolve(c).then(n,a)}function w(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var o=t.apply(e,r);function i(t){b(o,n,a,i,l,"next",t)}function l(t){b(o,n,a,i,l,"throw",t)}i(void 0)}))}}var _={name:"DietNutrition",data:function(){return{isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[],searchFormSetting:{name:{type:"input",label:"名字",value:"",maxlength:20,placeholder:"请输入名字"},group:{type:"input",label:"人群",value:"",maxlength:20,placeholder:"请输入人群"},min_age:{type:"input",label:"最小年龄",value:"",maxlength:20,placeholder:"请输入最小年龄"},max_age:{type:"input",label:"最大年龄",value:"",maxlength:20,placeholder:"请输入最大年龄"}},ruleDialog:!1,labelRuleInfo:{}}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.currentPage=1,this.getAdminCrowdList()},searchHandle:Object(o["d"])((function(){this.currentPage=1,this.initLoad()}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.currentPage=1,this.initLoad()},getAdminCrowdList:function(){var t=this;return w(l().mark((function e(){var r,n,a,i,c,u;return l().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!Object.keys(t.formatQueryParams(t.searchFormSetting))||!Object.keys(t.formatQueryParams(t.searchFormSetting)).length){e.next=5;break}if(r=/^\d+$/,n=t.formatQueryParams(t.searchFormSetting),!(n.min_age&&!r.test(n.min_age)||n.max_age&&!r.test(n.max_age))){e.next=5;break}return e.abrupt("return",t.$message.error("年龄需要输入整数"));case 5:return t.isLoading=!0,e.next=8,Object(o["Z"])(t.$apis.apiBackgroundHealthyAdminCrowdListPost(s(s({},t.formatQueryParams(t.searchFormSetting)),{},{page:t.currentPage,page_size:t.pageSize})));case 8:if(a=e.sent,i=h(a,2),c=i[0],u=i[1],t.isLoading=!1,!c){e.next=16;break}return t.$message.error(c.message),e.abrupt("return");case 16:0===u.code?(t.totalCount=u.data.count,t.tableData=u.data.results):t.$message.error(u.msg);case 17:case"end":return e.stop()}}),e)})))()},formatQueryParams:function(t){var e={};for(var r in t)t[r].value&&("select_date"!==r?e[r]=t[r].value:t[r].value.length>0&&(e.start_date=t[r].value[0],e.end_date=t[r].value[1]));return e},deleteHaldler:function(t,e){var r=this;this.$confirm("是否删除该人群？","删除",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-warn",center:!0,beforeClose:function(){var t=w(l().mark((function t(n,a,o){var i;return l().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if("confirm"!==n){t.next=10;break}return a.confirmButtonLoading=!0,t.next=4,r.$apis.apiBackgroundHealthyAdminCrowdDeletePost({ids:[e.id]});case 4:i=t.sent,0===i.code?(r.$message.success("删除成功"),r.currentPage>1&&1===r.tableData.length&&r.currentPage--,r.getAdminCrowdList()):r.$message.error(i.msg),o(),a.confirmButtonLoading=!1,t.next=11;break;case 10:a.confirmButtonLoading||o();case 11:case"end":return t.stop()}}),t)})));function n(e,r,n){return t.apply(this,arguments)}return n}()}).then((function(t){})).catch((function(t){}))},seeRule:function(t){this.ruleDialog=!0,this.labelRuleInfo=t},handleClose:function(){},handleSizeChange:function(t){this.pageSize=t,this.getAdminCrowdList()},handleCurrentChange:function(t){this.currentPage=t,this.getAdminCrowdList()},gotoExport:function(){this.$message.error("暂无导出")},addAndEditCrowd:function(t,e){this.$router.push({name:"SuperAddOrModifyCrowd",params:{type:t},query:{type:t,data:"modify"===t?this.$encodeQuery(e):""}})}}},x=_,C=(r("7745"),r("2877")),L=Object(C["a"])(x,n,a,!1,null,"6a5dbd56",null);e["default"]=L.exports},fc73:function(t,e,r){}}]);