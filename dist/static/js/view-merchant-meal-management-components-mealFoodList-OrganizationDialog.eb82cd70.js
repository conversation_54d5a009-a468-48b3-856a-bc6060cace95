(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-components-mealFoodList-OrganizationDialog"],{e03f:function(t,r,e){"use strict";e.r(r);var n=function(){var t=this,r=t._self._c;return r("div",[r("el-dialog",{attrs:{title:"see"===t.structureType?"查看":"应用组织",visible:t.showDialog,width:"450px","custom-class":"ps-dialog"},on:{"update:visible":function(r){t.showDialog=r}}},[r("el-form",{ref:"formData",staticClass:"dialog-form",attrs:{rules:t.formDataRuls,model:t.formData}},[r("el-form-item",{attrs:{label:"see"===t.structureType?"":"请选择下发的组织架构",prop:""}},[r("organization-select",{attrs:{"only-child":!0,isLazy:!1,multiple:!0,checkStrictly:!0},model:{value:t.formData.useOrganizations,callback:function(r){t.$set(t.formData,"useOrganizations",r)},expression:"formData.useOrganizations"}})],1),"see"!==t.structureType?r("el-form-item",{attrs:{label:"see"===t.structureType?"允许上级下发":"下发后是否可以进行修改菜品 / 商品信息",prop:""}},[r("el-switch",{attrs:{disabled:"see"===t.structureType,"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:t.formData.isAllowModify,callback:function(r){t.$set(t.formData,"isAllowModify",r)},expression:"formData.isAllowModify"}})],1):t._e()],1),r("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{staticClass:"ps-cancel-btn",on:{click:t.cancel}},[t._v("取 消")]),"see"!==t.structureType?r("el-button",{staticClass:"ps-btn",attrs:{type:"primary",loading:t.foodFoodDistributeDetermine},on:{click:t.determineOrganization}},[t._v(" 确 定 ")]):t._e()],1)],1)],1)},o=[],i=e("cbfb"),a=e("ed08");function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function u(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */u=function(){return r};var t,r={},e=Object.prototype,n=e.hasOwnProperty,o=Object.defineProperty||function(t,r,e){t[r]=e.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function f(t,r,e){return Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}),t[r]}try{f({},"")}catch(t){f=function(t,r,e){return t[r]=e}}function h(t,r,e,n){var i=r&&r.prototype instanceof w?r:w,a=Object.create(i.prototype),c=new A(n||[]);return o(a,"_invoke",{value:j(t,e,c)}),a}function p(t,r,e){try{return{type:"normal",arg:t.call(r,e)}}catch(t){return{type:"throw",arg:t}}}r.wrap=h;var y="suspendedStart",d="suspendedYield",m="executing",g="completed",v={};function w(){}function b(){}function D(){}var x={};f(x,a,(function(){return this}));var O=Object.getPrototypeOf,L=O&&O(O(F([])));L&&L!==e&&n.call(L,a)&&(x=L);var _=D.prototype=w.prototype=Object.create(x);function E(t){["next","throw","return"].forEach((function(r){f(t,r,(function(t){return this._invoke(r,t)}))}))}function S(t,r){function e(o,i,a,u){var s=p(t[o],t,i);if("throw"!==s.type){var l=s.arg,f=l.value;return f&&"object"==c(f)&&n.call(f,"__await")?r.resolve(f.__await).then((function(t){e("next",t,a,u)}),(function(t){e("throw",t,a,u)})):r.resolve(f).then((function(t){l.value=t,a(l)}),(function(t){return e("throw",t,a,u)}))}u(s.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new r((function(r,o){e(t,n,r,o)}))}return i=i?i.then(o,o):o()}})}function j(r,e,n){var o=y;return function(i,a){if(o===m)throw Error("Generator is already running");if(o===g){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var c=n.delegate;if(c){var u=k(c,n);if(u){if(u===v)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===y)throw o=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=m;var s=p(r,e,n);if("normal"===s.type){if(o=n.done?g:d,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=g,n.method="throw",n.arg=s.arg)}}}function k(r,e){var n=e.method,o=r.iterator[n];if(o===t)return e.delegate=null,"throw"===n&&r.iterator.return&&(e.method="return",e.arg=t,k(r,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=p(o,r.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,v;var a=i.arg;return a?a.done?(e[r.resultName]=a.value,e.next=r.nextLoc,"return"!==e.method&&(e.method="next",e.arg=t),e.delegate=null,v):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,v)}function z(t){var r={tryLoc:t[0]};1 in t&&(r.catchLoc=t[1]),2 in t&&(r.finallyLoc=t[2],r.afterLoc=t[3]),this.tryEntries.push(r)}function T(t){var r=t.completion||{};r.type="normal",delete r.arg,t.completion=r}function A(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(z,this),this.reset(!0)}function F(r){if(r||""===r){var e=r[a];if(e)return e.call(r);if("function"==typeof r.next)return r;if(!isNaN(r.length)){var o=-1,i=function e(){for(;++o<r.length;)if(n.call(r,o))return e.value=r[o],e.done=!1,e;return e.value=t,e.done=!0,e};return i.next=i}}throw new TypeError(c(r)+" is not iterable")}return b.prototype=D,o(_,"constructor",{value:D,configurable:!0}),o(D,"constructor",{value:b,configurable:!0}),b.displayName=f(D,l,"GeneratorFunction"),r.isGeneratorFunction=function(t){var r="function"==typeof t&&t.constructor;return!!r&&(r===b||"GeneratorFunction"===(r.displayName||r.name))},r.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,D):(t.__proto__=D,f(t,l,"GeneratorFunction")),t.prototype=Object.create(_),t},r.awrap=function(t){return{__await:t}},E(S.prototype),f(S.prototype,s,(function(){return this})),r.AsyncIterator=S,r.async=function(t,e,n,o,i){void 0===i&&(i=Promise);var a=new S(h(t,e,n,o),i);return r.isGeneratorFunction(e)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},E(_),f(_,l,"Generator"),f(_,a,(function(){return this})),f(_,"toString",(function(){return"[object Generator]"})),r.keys=function(t){var r=Object(t),e=[];for(var n in r)e.push(n);return e.reverse(),function t(){for(;e.length;){var n=e.pop();if(n in r)return t.value=n,t.done=!1,t}return t.done=!0,t}},r.values=F,A.prototype={constructor:A,reset:function(r){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(T),!r)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(r){if(this.done)throw r;var e=this;function o(n,o){return c.type="throw",c.arg=r,e.next=n,o&&(e.method="next",e.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(u&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,r){for(var e=this.tryEntries.length-1;e>=0;--e){var o=this.tryEntries[e];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=r&&r<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=r,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,r){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&r&&(this.next=r),v},finish:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.finallyLoc===t)return this.complete(e.completion,e.afterLoc),T(e),v}},catch:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.tryLoc===t){var n=e.completion;if("throw"===n.type){var o=n.arg;T(e)}return o}}throw Error("illegal catch attempt")},delegateYield:function(r,e,n){return this.delegate={iterator:F(r),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=t),v}},r}function s(t,r){return y(t)||p(t,r)||f(t,r)||l()}function l(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(t,r){if(t){if("string"==typeof t)return h(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?h(t,r):void 0}}function h(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}function p(t,r){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var n,o,i,a,c=[],u=!0,s=!1;try{if(i=(e=e.call(t)).next,0===r){if(Object(e)!==e)return;u=!1}else for(;!(u=(n=i.call(e)).done)&&(c.push(n.value),c.length!==r);u=!0);}catch(t){s=!0,o=t}finally{try{if(!u&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}function y(t){if(Array.isArray(t))return t}function d(t,r,e,n,o,i,a){try{var c=t[i](a),u=c.value}catch(t){return void e(t)}c.done?r(u):Promise.resolve(u).then(n,o)}function m(t){return function(){var r=this,e=arguments;return new Promise((function(n,o){var i=t.apply(r,e);function a(t){d(i,n,o,a,c,"next",t)}function c(t){d(i,n,o,a,c,"throw",t)}a(void 0)}))}}var g={props:{showDialogStructure:Boolean,organizationDisabled:Boolean,structureTree:{type:Array,default:function(){return[]}},dialogDataRow:Object,structureType:String,confirm:Function},data:function(){return{formData:{useOrganizations:[],isAllowModify:!1},formDataRuls:{},foodFoodDistributeDetermine:!1}},components:{organizationSelect:i["a"]},computed:{showDialog:{get:function(){return this.showDialogStructure},set:function(t){this.$emit("update:showDialogStructure",t)}}},mounted:function(){},created:function(){var t=this;this.formData.useOrganizations=[],this.dialogDataRow.use_organizations.map((function(r){t.formData.useOrganizations.push(r.id)}))},methods:{determineOrganization:Object(a["d"])((function(){this.foodIngredientSync()}),300),inputTree:function(t){this.formData.useOrganizations=t},cancel:function(){this.showDialog=!1},foodIngredientSync:function(){var t=this;return m(u().mark((function r(){var e,n,o,i;return u().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(t.formData.useOrganizations.length){r.next=2;break}return r.abrupt("return",t.$message.error("请选择应用的组织！"));case 2:return t.foodFoodDistributeDetermine=!0,r.next=5,Object(a["Z"])(t.$apis.apiBackgroundFoodFoodDistributePost({food_id:t.dialogDataRow.id,organizations:t.formData.useOrganizations,is_allow_modify:t.formData.isAllowModify}));case 5:if(e=r.sent,n=s(e,2),o=n[0],i=n[1],t.foodFoodDistributeDetermine=!1,!o){r.next=13;break}return t.$message.error(o.message),r.abrupt("return");case 13:0===i.code?(t.showDialog=!1,t.$message.success(i.msg),t.$emit("confirm","search")):t.$message.error(i.msg);case 14:case"end":return r.stop()}}),r)})))()}}},v=g,w=e("2877"),b=Object(w["a"])(v,n,o,!1,null,"67a9b2d8",null);r["default"]=b.exports}}]);