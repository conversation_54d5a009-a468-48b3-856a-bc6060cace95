(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-consumption-rules-operations-management-survey-SurveyAdmin"],{"1d00":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"SurveyAdmin container-wrapper"},[e("refresh-tool",{on:{refreshPage:t.refreshHandle}}),e("search-form",{ref:"searchRef",attrs:{"form-setting":t.searchFormSetting},on:{search:t.searchHandle}}),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("数据列表")]),e("div",{staticClass:"align-r"},[e("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_marketing.survey_info.add"],expression:"['background_marketing.survey_info.add']"}],attrs:{color:"origin",type:"add"},on:{click:function(e){return t.gotoAddOrEdit("add")}}},[t._v("新增问卷")])],1)]),e("div",{staticClass:"table-content"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},[e("el-table-column",{attrs:{prop:"create_time",label:"创建时间",align:"center"}}),e("el-table-column",{attrs:{prop:"name",label:"问卷名称",align:"center"}}),e("el-table-column",{attrs:{prop:"end_date",label:"截止时间",align:"center"}}),e("el-table-column",{attrs:{prop:"status_alias",label:"状态",align:"center",width:"90"}}),e("el-table-column",{attrs:{fixed:"right",label:"操作",align:"center",width:"150"},scopedSlots:t._u([{key:"default",fn:function(r){return["enable"===r.row.status&&"expire"!==r.row.status?e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_marketing.survey_info.modify_status"],expression:"['background_marketing.survey_info.modify_status']"}],staticClass:"ps-red",attrs:{type:"text",size:"small"},on:{click:function(e){return t.mulOperation("status",r.row)}}},[t._v("禁用")]):t._e(),"enable"!==r.row.status&&"expire"!==r.row.status?e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_marketing.survey_info.modify_status"],expression:"['background_marketing.survey_info.modify_status']"}],staticClass:"ps-red",attrs:{type:"text",size:"small"},on:{click:function(e){return t.mulOperation("status",r.row)}}},[t._v("启用")]):t._e(),e("el-button",{staticClass:"ps-bule",attrs:{type:"text",size:"small"},on:{click:function(e){return t.gotoDetail(r.row)}}},[t._v("查看")]),"enable"!==r.row.status&&"expire"!==r.row.status?e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_marketing.survey_info.modify"],expression:"['background_marketing.survey_info.modify']"}],staticClass:"ps-bule",attrs:{type:"text",size:"small"},on:{click:function(e){return t.gotoAddOrEdit("edit",r.row)}}},[t._v("编辑")]):t._e(),"enable"!==r.row.status?e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_marketing.survey_info.delete"],expression:"['background_marketing.survey_info.delete']"}],staticClass:"ps-red",attrs:{type:"text",size:"small"},on:{click:function(e){return t.mulOperation("del",r.row)}}},[t._v("删除")]):t._e()]}}])})],1)],1),e("pagination",{attrs:{onPaginationChange:t.onPaginationChange,"current-page":t.currentPage,"page-size":t.pageSize,layout:"total, prev, pager, next, jumper",total:t.totalCount},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"update:pageSize":function(e){t.pageSize=e},"update:page-size":function(e){t.pageSize=e}}})],1)],1)},a=[],o=r("ed08"),i=r("f63a");function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function u(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function c(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u(Object(r),!0).forEach((function(e){l(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function l(t,e,r){return(e=f(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function f(t){var e=p(t,"string");return"symbol"==s(e)?e:e+""}function p(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function h(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */h=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",u=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var o=e&&e.prototype instanceof b?e:b,i=Object.create(o.prototype),s=new N(n||[]);return a(i,"_invoke",{value:E(t,r,s)}),i}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var d="suspendedStart",v="suspendedYield",g="executing",m="completed",y={};function b(){}function w(){}function _(){}var x={};l(x,i,(function(){return this}));var k=Object.getPrototypeOf,S=k&&k(k($([])));S&&S!==r&&n.call(S,i)&&(x=S);var L=_.prototype=b.prototype=Object.create(x);function O(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function P(t,e){function r(a,o,i,u){var c=p(t[a],t,o);if("throw"!==c.type){var l=c.arg,f=l.value;return f&&"object"==s(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,i,u)}),(function(t){r("throw",t,i,u)})):e.resolve(f).then((function(t){l.value=t,i(l)}),(function(t){return r("throw",t,i,u)}))}u(c.arg)}var o;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return o=o?o.then(a,a):a()}})}function E(e,r,n){var a=d;return function(o,i){if(a===g)throw Error("Generator is already running");if(a===m){if("throw"===o)throw i;return{value:t,done:!0}}for(n.method=o,n.arg=i;;){var s=n.delegate;if(s){var u=j(s,n);if(u){if(u===y)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===d)throw a=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=g;var c=p(e,r,n);if("normal"===c.type){if(a=n.done?m:v,c.arg===y)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(a=m,n.method="throw",n.arg=c.arg)}}}function j(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,j(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var o=p(a,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,y;var i=o.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function C(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function z(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function N(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(C,this),this.reset(!0)}function $(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,o=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(s(e)+" is not iterable")}return w.prototype=_,a(L,"constructor",{value:_,configurable:!0}),a(_,"constructor",{value:w,configurable:!0}),w.displayName=l(_,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,l(t,c,"GeneratorFunction")),t.prototype=Object.create(L),t},e.awrap=function(t){return{__await:t}},O(P.prototype),l(P.prototype,u,(function(){return this})),e.AsyncIterator=P,e.async=function(t,r,n,a,o){void 0===o&&(o=Promise);var i=new P(f(t,r,n,a),o);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},O(L),l(L,c,"Generator"),l(L,i,(function(){return this})),l(L,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=$,N.prototype={constructor:N,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(z),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return s.type="throw",s.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],s=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var u=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(u&&c){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(u){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,y):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),z(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;z(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:$(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),y}},e}function d(t,e,r,n,a,o,i){try{var s=t[o](i),u=s.value}catch(t){return void r(t)}s.done?e(u):Promise.resolve(u).then(n,a)}function v(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var o=t.apply(e,r);function i(t){d(o,n,a,i,s,"next",t)}function s(t){d(o,n,a,i,s,"throw",t)}i(void 0)}))}}var g={name:"SurveyAdmin",mixins:[i["a"]],data:function(){return{isLoading:!1,tableData:[],currentPage:1,pageSize:10,totalCount:0,searchFormSetting:{name:{type:"input",label:"问卷名称",value:"",maxlength:20,placeholder:"请输入问卷名称"},status:{type:"select",label:"状态",value:"",clearable:!0,placeholder:"请选择状态",dataList:[{label:"全部",value:""},{label:"启用",value:"enable"},{label:"未启用",value:"disable"},{label:"已结束",value:"expire"}]}}}},mounted:function(){this.initLoad()},methods:{initLoad:function(){var t=this;return v(h().mark((function e(){return h().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.getSurveyList();case 1:case"end":return e.stop()}}),e)})))()},refreshHandle:function(){this.$refs.searchRef.resetForm(),this.currentPage=1,this.tableData=[],this.initLoad()},searchHandle:Object(o["d"])((function(){this.currentPage=1,this.getSurveyList()}),300),formatQueryParams:function(t){var e={};for(var r in t)""!==t[r].value&&null!==t[r].value&&0!==t[r].value.length&&("select_date"!==r?e[r]=t[r].value:t[r].value.length>0&&(e.start_date=t[r].value[0],e.end_date=t[r].value[1]));return e},getSurveyList:function(){var t=this;return v(h().mark((function e(){var r;return h().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.$apis.apiBackgroundMarketingSurveyInfoListPost(c(c({},t.formatQueryParams(t.searchFormSetting)),{},{page:t.currentPage,page_size:t.pageSize}));case 3:r=e.sent,t.isLoading=!1,0===r.code?(t.tableData=r.data.results.map((function(t){return t.rules_status=!("disable"===t.status),t})),t.totalCount=r.data.count):t.$message.error(r.msg);case 6:case"end":return e.stop()}}),e)})))()},onPaginationChange:function(t){this.currentPage=t.current,this.pageSize=t.pageSize,this.getSurveyList()},mulOperation:function(t,e){var r,n=this,a="提示",o="";switch(t){case"del":o="确定删除该调查问卷吗？";break;case"status":"disable"===e.status?(o="确定启用该调查问卷吗？",r="enable"):(o="确定停用该调查问卷吗？",r="disable");break}this.$confirm("".concat(o),"".concat(a),{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var a=v(h().mark((function a(o,i,s){var u;return h().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if("confirm"!==o){a.next=16;break}i.confirmButtonLoading=!0,u={id:e.id},a.t0=t,a.next="del"===a.t0?6:"status"===a.t0?9:12;break;case 6:return u.ids=[e.id],n.delSurveyInfo(u),a.abrupt("break",12);case 9:return u.status=r,n.confirmStatus(u),a.abrupt("break",12);case 12:s(),i.confirmButtonLoading=!1,a.next=18;break;case 16:n.getSurveyList(),i.confirmButtonLoading||s();case 18:case"end":return a.stop()}}),a)})));function o(t,e,r){return a.apply(this,arguments)}return o}()}).then((function(t){})).catch((function(t){}))},confirmStatus:function(t){var e=this;return v(h().mark((function r(){var n;return h().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,e.$apis.apiBackgroundMarketingSurveyInfoModifyStatusPost(t);case 2:n=r.sent,0===n.code?e.$message.success("成功"):e.$message.error(n.msg),e.getSurveyList();case 5:case"end":return r.stop()}}),r)})))()},delSurveyInfo:function(t){var e=this;return v(h().mark((function r(){var n;return h().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,e.$apis.apiBackgroundMarketingSurveyInfoDeletePost(t);case 2:n=r.sent,0===n.code?e.$message.success("成功"):e.$message.error(n.msg),e.getSurveyList();case 5:case"end":return r.stop()}}),r)})))()},gotoAddOrEdit:function(t,e){var r={};"edit"!==t&&"detail"!==t||(r={data:JSON.stringify(e)}),this.$router.push({name:"AddSurvey",params:{type:t},query:c(c({},r),{},{type:t})})},gotoDetail:function(t){this.$router.push({name:"MerchantSurveyDetail",query:{id:t.id}})},handleExport:function(){var t="apiBackgroundApproveOrderApproveVisitorJfListExportPost",e=this.formatQueryParams(this.searchFormSetting,this.page,this.pageSize),r={type:"MealVisitorOrder",url:t,params:e};this.exportHandle(r)}}},m=g,y=(r("8857"),r("2877")),b=Object(y["a"])(m,n,a,!1,null,null,null);e["default"]=b.exports},8857:function(t,e,r){"use strict";r("d043")},d043:function(t,e,r){}}]);