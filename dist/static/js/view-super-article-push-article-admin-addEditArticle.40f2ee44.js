(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-article-push-article-admin-addEditArticle"],{"34e3":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"super-add-article container-wrapper"},[e("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formIngredients",attrs:{rules:t.formRuls,model:t.formData,size:"small","label-width":"80px"}},[e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("基本信息")])]),e("div",{staticStyle:{padding:"0 20px"}},[e("el-form-item",{attrs:{label:"标题",prop:"title"}},[e("el-input",{staticClass:"ps-input w-250",attrs:{type:"textarea",rows:4,maxlength:"40","show-word-limit":""},model:{value:t.formData.title,callback:function(e){t.$set(t.formData,"title",e)},expression:"formData.title"}})],1),e("el-form-item",{attrs:{label:"来源",prop:"source"}},[e("el-select",{staticClass:"ps-select w-250",attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select"},model:{value:t.formData.source,callback:function(e){t.$set(t.formData,"source",e)},expression:"formData.source"}},[e("el-option",{attrs:{label:"原创",value:1}}),e("el-option",{attrs:{label:"转载",value:2}})],1)],1),e("el-form-item",{attrs:{label:"是否推荐",prop:"is_recommend"}},[e("el-select",{staticClass:"ps-select w-250",attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select"},model:{value:t.formData.is_recommend,callback:function(e){t.$set(t.formData,"is_recommend",e)},expression:"formData.is_recommend"}},[e("el-option",{attrs:{label:"是",value:!0}}),e("el-option",{attrs:{label:"否",value:!1}})],1)],1),e("el-form-item",{attrs:{label:"作者",prop:"author"}},[e("el-input",{staticClass:"ps-input p-r-50 w-300",attrs:{placeholder:"请输入标题",maxlength:"10","show-word-limit":""},model:{value:t.formData.author,callback:function(e){t.$set(t.formData,"author",e)},expression:"formData.author"}})],1),e("el-form-item",{attrs:{label:"分类",prop:"categorys"}},[e("el-select",{staticClass:"ps-select w-250",attrs:{placeholder:"请下拉选择",multiple:"","popper-class":"ps-popper-select"},model:{value:t.formData.categorys,callback:function(e){t.$set(t.formData,"categorys",e)},expression:"formData.categorys"}},t._l(t.categorysList,(function(t,r){return e("el-option",{key:r,attrs:{label:t.name,value:t.id,disabled:"disable"==t.status}})})),1)],1),e("el-form-item",{attrs:{label:"封面",prop:"image"}},[e("el-upload",{ref:"uploadFood",staticClass:"avatar-uploader",attrs:{data:t.uploadParams,action:t.actionUrl,"show-file-list":!1,"on-success":t.handleAvatarSuccess,"before-upload":t.beforeAvatarUpload}},[t.formData.image?e("img",{staticClass:"avatar",attrs:{src:t.formData.image}}):e("i",{staticClass:"el-icon-plus avatar-uploader-icon"})])],1),e("el-form-item",{attrs:{label:"正文",prop:"content"}},[e("TinymceUeditor",{attrs:{content:t.formData.content},on:{message:t.messageTinymceUeditor},model:{value:t.formData.content,callback:function(e){t.$set(t.formData,"content",e)},expression:"formData.content"}})],1)],1)]),e("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[e("el-button",{staticStyle:{width:"120px"},on:{click:t.closeHandler}},[t._v("取消")]),e("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:t.submitHandler}},[t._v(" "+t._s("add"===t.type?"添加":"保存")+" ")])],1)])],1)},a=[],o=r("ed08"),i=r("56f9");function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function c(t,e){return d(t)||p(t,e)||u(t,e)||l()}function l(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(t,e){if(t){if("string"==typeof t)return f(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?f(t,e):void 0}}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function p(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,o,i,s=[],c=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(s.push(n.value),s.length!==e);c=!0);}catch(t){l=!0,a=t}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(l)throw a}}return s}}function d(t){if(Array.isArray(t))return t}function h(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */h=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var o=e&&e.prototype instanceof b?e:b,i=Object.create(o.prototype),s=new O(n||[]);return a(i,"_invoke",{value:A(t,r,s)}),i}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var d="suspendedStart",m="suspendedYield",g="executing",y="completed",v={};function b(){}function w(){}function x(){}var L={};u(L,i,(function(){return this}));var k=Object.getPrototypeOf,_=k&&k(k(T([])));_&&_!==r&&n.call(_,i)&&(L=_);var $=x.prototype=b.prototype=Object.create(L);function D(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function E(t,e){function r(a,o,i,c){var l=p(t[a],t,o);if("throw"!==l.type){var u=l.arg,f=u.value;return f&&"object"==s(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,i,c)}),(function(t){r("throw",t,i,c)})):e.resolve(f).then((function(t){u.value=t,i(u)}),(function(t){return r("throw",t,i,c)}))}c(l.arg)}var o;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return o=o?o.then(a,a):a()}})}function A(e,r,n){var a=d;return function(o,i){if(a===g)throw Error("Generator is already running");if(a===y){if("throw"===o)throw i;return{value:t,done:!0}}for(n.method=o,n.arg=i;;){var s=n.delegate;if(s){var c=C(s,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===d)throw a=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=g;var l=p(e,r,n);if("normal"===l.type){if(a=n.done?y:m,l.arg===v)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(a=y,n.method="throw",n.arg=l.arg)}}}function C(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,C(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var o=p(a,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,v;var i=o.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function S(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function O(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(S,this),this.reset(!0)}function T(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,o=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(s(e)+" is not iterable")}return w.prototype=x,a($,"constructor",{value:x,configurable:!0}),a(x,"constructor",{value:w,configurable:!0}),w.displayName=u(x,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,x):(t.__proto__=x,u(t,l,"GeneratorFunction")),t.prototype=Object.create($),t},e.awrap=function(t){return{__await:t}},D(E.prototype),u(E.prototype,c,(function(){return this})),e.AsyncIterator=E,e.async=function(t,r,n,a,o){void 0===o&&(o=Promise);var i=new E(f(t,r,n,a),o);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},D($),u($,l,"Generator"),u($,i,(function(){return this})),u($,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=T,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return s.type="throw",s.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],s=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var c=n.call(i,"catchLoc"),l=n.call(i,"finallyLoc");if(c&&l){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),j(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;j(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:T(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function m(t,e,r,n,a,o,i){try{var s=t[o](i),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,a)}function g(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var o=t.apply(e,r);function i(t){m(o,n,a,i,s,"next",t)}function s(t){m(o,n,a,i,s,"throw",t)}i(void 0)}))}}var y={name:"SuperAddEditArticle",components:{TinymceUeditor:i["a"]},data:function(){var t=function(t,e,r){e?e.length>40?r(new Error("标题不能超过40个字")):r():r(new Error("请输入文章标题"))};return{isLoading:!1,type:"add",formData:{title:"",tags:[],categorys:[],source:"",is_recommend:null,author:"",image:"",content:""},formRuls:{title:[{required:!0,validator:t,trigger:"blur"}],categorys:[{required:!0,message:"请选择分类",trigger:"blur"}],source:[{required:!0,message:"请选择来源",trigger:"blur"}],is_recommend:[{required:!0,message:"请选择是否推荐",trigger:"blur"}],content:[{required:!0,message:"请输入文章内容",trigger:"blur"}]},tagsList:[],categorysList:[],actionUrl:"",uploadParams:{}}},created:function(){this.getUploadToken(),this.getArticleCategoryList(),this.type=this.$route.query.type},mounted:function(){},methods:{initLoad:function(){if("modify"===this.type){var t=this.$decodeQuery(this.$route.query.data);this.formData={id:t.id,title:t.title,categorys:t.categorys.map((function(t){return t.id})),source:t.source,is_recommend:t.is_recommend,author:t.author,image:t.image,content:t.content?t.content:""}}},searchHandle:Object(o["d"])((function(){this.currentPage=1}),300),getUploadToken:function(){var t=this;return g(h().mark((function e(){var r;return h().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.getUploadToken({prefix:"jpeg/png"});case 2:r=e.sent,0===r.code?(t.actionUrl=r.data.host,t.uploadParams={key:r.data.prefix+(new Date).getTime()+Math.floor(150*Math.random()),prefix:r.data.prefix,policy:r.data.policy,OSSAccessKeyId:r.data.accessid,signature:r.data.signature,callback:r.data.callback,success_action_status:"200"}):t.$message.error(r.msg);case 4:case"end":return e.stop()}}),e)})))()},getArticleCategoryList:function(){var t=this;return g(h().mark((function e(){var r,n,a,i;return h().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(o["Z"])(t.$apis.apiBackgroundAdminArticleCategoryListPost({page:1,page_size:1e4}));case 2:if(r=e.sent,n=c(r,2),a=n[0],i=n[1],!a){e.next=9;break}return t.$message.error(a.message),e.abrupt("return");case 9:0===i.code?(t.categorysList=i.data.results,t.initLoad()):t.$message.error(i.msg);case 10:case"end":return e.stop()}}),e)})))()},handleAvatarSuccess:function(t,e){0===t.code?(this.$refs.uploadFood.clearFiles(),this.formData.image=t.data.public_url,this.getUploadToken()):this.$message.error(t.msg)},beforeAvatarUpload:function(t){var e="image/jpeg"===t.type||"image/png"===t.type,r=t.size/1024/1024<2;return e||this.$message.error("上传头像图片只能是 JPG 格式!"),r||this.$message.error("上传头像图片大小不能超过 2MB!"),e&&r},messageTinymceUeditor:function(t){this.formData.content=t},addModifyArticle:function(t){var e=this;return g(h().mark((function r(){var n,a,i,s,l,u,f,p;return h().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(e.isLoading=!0,n="",a=c(n,2),i=a[0],s=a[1],"add"!==e.type){r.next=12;break}return r.next=6,Object(o["Z"])(e.$apis.apiBackgroundAdminArticleAddPost(t));case 6:l=r.sent,u=c(l,2),i=u[0],s=u[1],r.next=19;break;case 12:return r.next=15,Object(o["Z"])(e.$apis.apiBackgroundAdminArticleModifyPost(t));case 15:f=r.sent,p=c(f,2),i=p[0],s=p[1];case 19:if(e.isLoading=!1,!i){r.next=23;break}return e.$message.error(i.message),r.abrupt("return");case 23:0===s.code?(e.$message.success(s.msg),e.$closeCurrentTab(e.$route.path)):e.$message.error(s.msg);case 24:case"end":return r.stop()}}),r)})))()},submitHandler:function(){var t=this;this.$refs.formIngredients.validate((function(e){if(e){if(t.isLoading)return t.$message.error("请勿重复提交！");t.addModifyArticle(t.formData)}}))},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,r,n){"confirm"===e?t.$closeCurrentTab(t.$route.path):r.confirmButtonLoading||n()}}).then((function(t){})).catch((function(t){}))}}},v=y,b=(r("46d4"),r("2877")),w=Object(b["a"])(v,n,a,!1,null,null,null);e["default"]=w.exports},"39ca":function(t,e,r){},"46d4":function(t,e,r){"use strict";r("39ca")}}]);