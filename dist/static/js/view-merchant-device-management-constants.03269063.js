(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-device-management-constants"],{"4dff":function(e,t,n){"use strict";n.r(t),n.d(t,"recentSevenDay",(function(){return b})),n.d(t,"TableSetting",(function(){return P})),n.d(t,"TabTypeList",(function(){return s})),n.d(t,"TableButtonList",(function(){return S})),n.d(t,"DishPatternTableButton",(function(){return y})),n.d(t,"WeekList",(function(){return g})),n.d(t,"SwitchDate",(function(){return m})),n.d(t,"DEVICE_IMG",(function(){return f})),n.d(t,"PRINT_ADMIN_SEARCH",(function(){return d})),n.d(t,"PRINT_LIST_SEARCH",(function(){return k})),n.d(t,"PRINT_ADMIN_TABLE",(function(){return v})),n.d(t,"PRINT_LIST_TABLE",(function(){return _})),n.d(t,"getRequestParams",(function(){return h}));var a=n("ed08"),l=n("5a0c");function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(Object(n),!0).forEach((function(t){p(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function p(e,t,n){return(t=u(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function u(e){var t=c(e,"string");return"symbol"==r(t)?t:t+""}function c(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!=r(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var b=[l().subtract(7,"day").format("YYYY-MM-DD"),l().format("YYYY-MM-DD")],P=[{label:"设备名称",key:"device_name"},{label:"图片1",key:"1",type:"slot",slotName:"default"},{label:"图片2",key:"2",type:"slot",slotName:"default"},{label:"图片3",key:"3",type:"slot",slotName:"default"},{label:"操作",key:"operation",type:"slot",slotName:"operation"}],s=[{name:"显示设置",key:"showSetting"},{name:"图片设置",key:"imgSetting"}],S=[{name:"一键清空",key:"cleanImg"},{name:"导入图片",key:"importImg"}],y=[{name:"一键清空",key:"cleanDish"},{name:"复制",key:"copy"}],g=[{label:"全部",key:0},{label:"周一",key:1},{label:"周二",key:2},{label:"周三",key:3},{label:"周四",key:4},{label:"周五",key:5},{label:"周六",key:6},{label:"周日",key:7}],m=function(e){var t="";switch(e){case 0:t="周一";break;case 1:t="周二";break;case 2:t="周三";break;case 3:t="周四";break;case 4:t="周五";break;case 5:t="周六";break;case 6:t="周日";break}return t},f={"PS-502":"PS-502.png","PS-503":"PS-503.png","PS-1050":"PS-1050.png","PS-1314":"PS-1314.png","PS-1500":"PS-1500.png","PS-1516":"PS-1516.png","PS-BOX32":"PS-BOX32.png","PS-BOX-BX10":"PS-BOX-BX10.png","PS-BOX-BX20":"PS-BOX-BX20.png","PS-C1050":"PS-C1050.png","PS-C1050-1":"PS-C1050-1.png","PS-C1050-2":"PS-C1050-2.png","PS-C1051":"PS-C1051.png","PS-D2":"PS-D2.png","PS-HY11S":"PS-HY11S.png","PS-HY11W":"PS-HY11W.png","PS-K1 02":"PS-K1 02.png","PS-k1":"PS-k1.png","PS-KW001":"PS-KW001.png","PS-M2":"PS-M2.png","PS-P2":"PS-P2.png","PS-TP":"PS-TP.png","PS-ZJ001":"PS-ZJ001.png","PS-ZY001":"PS-ZY001.png","PS-ZY002右":"PS-ZY002右.png","PS-ZY002左":"PS-ZY002左.png","PS-ZZKWJ":"PS-ZZKWJ.png","PS-H8":"PS-H8.png","PS-LY01":"PS-LYY.png","PS-D2D":"PS-D2D.png","PS-K1-TP":"PS-K1-TP.png","PS-TPJ":"PS-TPJ.png",CXP:"CXP.png","PS-KB01":"PS-KB01.png","PS-KC001":"PS-KC001.png","PS-D2mini":"PS-D2.png",CJY:"CJY.png",LYG:"LYG.png"},d={org_ids:{type:"organizationSelect",value:[],label:"适用组织",checkStrictly:!0,isLazy:!1,collapseTags:!0,multiple:!0},type:{type:"select",label:"类型",value:"",placeholder:"请选择类型",dataList:[{label:"全部",value:""},{label:"热敏纸打印机",value:"thermal_paper"},{label:"标签打印机",value:"label"}]},sn:{type:"input",label:"序列号",value:"",placeholder:"请输入序列号"},name:{type:"input",label:"名称",value:"",placeholder:"请输入名称"}},k={create_time:{timeRange:!0,type:"daterange",label:"创建时间",clearable:!0,value:Object(a["t"])(0,{format:"{y}-{m}-{d}"})},print_time:{timeRange:!0,type:"daterange",label:"打印时间",clearable:!0,value:[]},print_no:{type:"input",label:"打印单号",value:"",placeholder:"请输入打印单号"},task_status:{type:"select",label:"打印状态",value:"",multiple:!0,clearable:!0,collapseTags:!0,placeholder:"请选择打印状态",dataList:[{label:"未开始",value:"not_started"},{label:"打印中",value:"printing"},{label:"打印成功",value:"success"},{label:"打印异常",value:"terminated"},{label:"打印终止",value:"abnormal"}]}},v=[{label:"打印机品牌",key:"brand_alias"},{label:"序列号/编号",key:"sn",width:160},{label:"名称",key:"name"},{label:"类型",key:"type_alias"},{label:"设备状态",key:"printer_status_alias"},{label:"适用组织",key:"org"},{label:"运行状态",key:"running_status_alias"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"220"}],_=[{label:"打印单号",key:"print_no"},{label:"创建时间",key:"create_time"},{label:"打印时间",key:"print_time"},{label:"打印进度",key:"progress",type:"slot",slotName:"progress"},{label:"打印份数",key:"print_num"},{label:"打印状态",key:"task_status_alias"},{label:"打印设备",key:"printer_name"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"80"}],h=function(e,t,n){var a={};Object.keys(e).forEach((function(t){var n,l;e[t].timeRange&&(null===(n=e[t].value)||void 0===n?void 0:n.length)>0?(a["start_"+t]=e[t].value[0],a["end_"+t]=e[t].value[1]):""!==e[t].value&&e[t].value&&(null===(l=e[t].value)||void 0===l?void 0:l.length)>0&&(a[t]=e[t].value)}));var l=i({page:t,page_size:n},a);return l}}}]);