(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-order-component-evaluateSettingDialog"],{"5a346":function(t,e,r){"use strict";r.r(e);var o=function(){var t=this,e=t._self._c;return e("div",{staticClass:"evaluate-setting-dialog"},[e("CustomDrawer",t._g(t._b({attrs:{show:t.showDrawer,size:t.size,title:t.title,cancelShow:!1,confirmShow:!1},on:{"update:show":function(e){t.showDrawer=e}}},"CustomDrawer",t.$attrs,!1),t.$listeners),[e("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"dialogForm",staticClass:"dialog-evaluate-form",attrs:{model:t.dialogForm,rules:t.dialogFormRules,"label-width":"100px",size:"mini"},nativeOn:{submit:function(t){t.preventDefault()}}},[e("el-form-item",{staticClass:"m-t-6",attrs:{label:"菜品评价"}},[e("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:t.dialogForm.food_evaluation_status,callback:function(e){t.$set(t.dialogForm,"food_evaluation_status",e)},expression:"dialogForm.food_evaluation_status"}}),e("span",{staticClass:"switch-text origin-color m-l-20"},[t._v("关闭后用户只可对整笔订单进行评价")]),t.dialogForm.food_evaluation_status?e("div",{staticClass:"rate-setting"},[e("div",{staticClass:"origin-color"},[t._v("设置菜品星级评价内容，最多设置3个条件")]),e("div",{},t._l(t.dialogForm.food_evaluation_field,(function(r,o){return e("el-form-item",{key:o,staticClass:"m-t-6",attrs:{prop:"food_evaluation_field."+o+".field_name",rules:t.dialogFormRules.food_evaluation_field,"show-message":!1,label:""}},[e("el-input",{staticClass:"ps-input max-w-rate",attrs:{maxLength:5},model:{value:r.field_name,callback:function(e){t.$set(r,"field_name",e)},expression:"item.field_name"}}),e("el-rate",{staticClass:"rate inline-block m-l-10",attrs:{"disabled-void-color":"#ff9b45",disabled:""},model:{value:r.score,callback:function(e){t.$set(r,"score",e)},expression:"item.score"}}),e("span",{staticClass:"square-icon-box vertical-middle m-l-20"},[t.dialogForm.food_evaluation_field.length<3?e("svg-icon",{staticClass:"pointer m-r-10",attrs:{"icon-class":"square_plus"},nativeOn:{click:function(e){return t.addScoreHandle("food_evaluation_field",o)}}}):t._e(),1!==t.dialogForm.food_evaluation_field.length?e("svg-icon",{staticClass:"pointer",attrs:{"icon-class":"square_minus"},nativeOn:{click:function(e){return t.deleteHandle("food_evaluation_field",o)}}}):t._e()],1)],1)})),1)]):t._e()],1),e("el-form-item",{staticClass:"m-t-6",attrs:{label:"星级设置"}},t._l(t.dialogForm.start_score_field,(function(r,o){return e("el-form-item",{staticClass:"rate-text-item",attrs:{prop:"start_score_field."+o+".content",rules:t.dialogFormRules.start_score_field,"show-message":!1,label:"","label-width":"0"}},[e("span",{staticClass:"rate-text-albel vertical-middle m-r-20"},[t._v(t._s(r.score)+"星")]),e("el-input",{staticClass:"ps-input max-w-text",attrs:{maxLength:5},model:{value:r.content,callback:function(e){t.$set(r,"content",e)},expression:"item.content"}})],1)})),1),e("el-form-item",{staticClass:"m-t-6",attrs:{label:"评价时间"}},[e("el-form-item",{staticClass:"rate-text-item",attrs:{label:"","label-width":"0",prop:"on_scene","show-message":!1}},[e("span",{staticClass:"rate-text-albel vertical-middle"},[t._v("堂食订单：")]),e("span",{staticClass:"inline-block vertical-middle max-s-w m-r-10"},[t._v("订单完成后")]),e("el-input",{staticClass:"ps-input max-s-w",model:{value:t.dialogForm.on_scene,callback:function(e){t.$set(t.dialogForm,"on_scene",e)},expression:"dialogForm.on_scene"}}),e("span",{staticClass:"vertical-middle m-l-10"},[t._v("天内可评价")])],1),e("el-form-item",{staticClass:"rate-text-item",attrs:{label:"","label-width":"0"}},[e("span",{staticClass:"rate-text-albel vertical-middle"},[t._v("预约订单：")]),e("el-form-item",{staticClass:"rate-text-item inline-block",attrs:{label:"","label-width":"0",prop:"reservation_order_evaluate_type","show-message":!1}},[e("el-select",{staticClass:"max-s-w ps-select m-r-10",model:{value:t.dialogForm.reservation_order_evaluate_type,callback:function(e){t.$set(t.dialogForm,"reservation_order_evaluate_type",e)},expression:"dialogForm.reservation_order_evaluate_type"}},[e("el-option",{attrs:{label:"核销后",value:"release_end"}}),e("el-option",{attrs:{label:"支付后",value:"pay_end"}}),e("el-option",{attrs:{label:"餐段结束后",value:"meal_end"}})],1)],1),e("el-form-item",{staticClass:"rate-text-item inline-block",attrs:{label:"","label-width":"0",prop:"reservation_order","show-message":!1}},[e("el-input",{staticClass:"ps-input max-s-w",model:{value:t.dialogForm.reservation_order,callback:function(e){t.$set(t.dialogForm,"reservation_order",e)},expression:"dialogForm.reservation_order"}})],1),e("span",{staticClass:"vertical-middle m-l-10"},[t._v("天内可评价")])],1)],1),e("el-form-item",{staticClass:"m-t-6",attrs:{label:"匿名设置"}},[e("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:t.dialogForm.anonymous,callback:function(e){t.$set(t.dialogForm,"anonymous",e)},expression:"dialogForm.anonymous"}}),e("span",{staticClass:"switch-text origin-color m-l-20"},[t._v("开启后支持用户进行匿名评价")])],1),e("el-form-item",{staticClass:"m-t-6",attrs:{label:"快捷回复"}},[e("el-input",{staticClass:"ps-input w-input",attrs:{type:"textarea",rows:4,maxlength:150,"show-word-limit":"",placeholder:"输入您常用的回复语，不超过150字"},model:{value:t.dialogForm.shortcut_reply_field,callback:function(e){t.$set(t.dialogForm,"shortcut_reply_field",e)},expression:"dialogForm.shortcut_reply_field"}})],1),e("el-form-item",{attrs:{"label-width":"0"}},[e("div",{staticClass:"ps-drawer-footer"},[e("el-button",{attrs:{size:"medium"},on:{click:t.closeHandle}},[t._v("取消")]),e("el-button",{staticClass:"ps-origin-btn",attrs:{size:"medium",type:"primary"},on:{click:t.submitHandle}},[t._v("保存")])],1)])],1)],1)],1)},a=[];r("786b");function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}function i(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */i=function(){return e};var t,e={},r=Object.prototype,o=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},l="function"==typeof Symbol?Symbol:{},s=l.iterator||"@@iterator",c=l.asyncIterator||"@@asyncIterator",u=l.toStringTag||"@@toStringTag";function d(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{d({},"")}catch(t){d=function(t,e,r){return t[e]=r}}function f(t,e,r,o){var n=e&&e.prototype instanceof y?e:y,i=Object.create(n.prototype),l=new P(o||[]);return a(i,"_invoke",{value:O(t,r,l)}),i}function m(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="suspendedYield",v="executing",g="completed",_={};function y(){}function b(){}function w(){}var x={};d(x,s,(function(){return this}));var F=Object.getPrototypeOf,L=F&&F(F(D([])));L&&L!==r&&o.call(L,s)&&(x=L);var C=w.prototype=y.prototype=Object.create(x);function k(t){["next","throw","return"].forEach((function(e){d(t,e,(function(t){return this._invoke(e,t)}))}))}function E(t,e){function r(a,i,l,s){var c=m(t[a],t,i);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==n(d)&&o.call(d,"__await")?e.resolve(d.__await).then((function(t){r("next",t,l,s)}),(function(t){r("throw",t,l,s)})):e.resolve(d).then((function(t){u.value=t,l(u)}),(function(t){return r("throw",t,l,s)}))}s(c.arg)}var i;a(this,"_invoke",{value:function(t,o){function a(){return new e((function(e,a){r(t,o,e,a)}))}return i=i?i.then(a,a):a()}})}function O(e,r,o){var a=p;return function(n,i){if(a===v)throw Error("Generator is already running");if(a===g){if("throw"===n)throw i;return{value:t,done:!0}}for(o.method=n,o.arg=i;;){var l=o.delegate;if(l){var s=S(l,o);if(s){if(s===_)continue;return s}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(a===p)throw a=g,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);a=v;var c=m(e,r,o);if("normal"===c.type){if(a=o.done?g:h,c.arg===_)continue;return{value:c.arg,done:o.done}}"throw"===c.type&&(a=g,o.method="throw",o.arg=c.arg)}}}function S(e,r){var o=r.method,a=e.iterator[o];if(a===t)return r.delegate=null,"throw"===o&&e.iterator.return&&(r.method="return",r.arg=t,S(e,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+o+"' method")),_;var n=m(a,e.iterator,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,_;var i=n.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,_):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,_)}function $(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function P(t){this.tryEntries=[{tryLoc:"root"}],t.forEach($,this),this.reset(!0)}function D(e){if(e||""===e){var r=e[s];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,i=function r(){for(;++a<e.length;)if(o.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(n(e)+" is not iterable")}return b.prototype=w,a(C,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:b,configurable:!0}),b.displayName=d(w,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,d(t,u,"GeneratorFunction")),t.prototype=Object.create(C),t},e.awrap=function(t){return{__await:t}},k(E.prototype),d(E.prototype,c,(function(){return this})),e.AsyncIterator=E,e.async=function(t,r,o,a,n){void 0===n&&(n=Promise);var i=new E(f(t,r,o,a),n);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},k(C),d(C,u,"Generator"),d(C,s,(function(){return this})),d(C,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var o in e)r.push(o);return r.reverse(),function t(){for(;r.length;){var o=r.pop();if(o in e)return t.value=o,t.done=!1,t}return t.done=!0,t}},e.values=D,P.prototype={constructor:P,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var r in this)"t"===r.charAt(0)&&o.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(o,a){return l.type="throw",l.arg=e,r.next=o,a&&(r.method="next",r.arg=t),!!a}for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n],l=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var s=o.call(i,"catchLoc"),c=o.call(i,"finallyLoc");if(s&&c){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&o.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var n=a;break}}n&&("break"===t||"continue"===t)&&n.tryLoc<=e&&e<=n.finallyLoc&&(n=null);var i=n?n.completion:{};return i.type=t,i.arg=e,n?(this.method="next",this.next=n.finallyLoc,_):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),_},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),j(r),_}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var o=r.completion;if("throw"===o.type){var a=o.arg;j(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,o){return this.delegate={iterator:D(e),resultName:r,nextLoc:o},"next"===this.method&&(this.arg=t),_}},e}function l(t,e){return f(t)||d(t,e)||c(t,e)||s()}function s(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function c(t,e){if(t){if("string"==typeof t)return u(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?u(t,e):void 0}}function u(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,o=Array(e);r<e;r++)o[r]=t[r];return o}function d(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var o,a,n,i,l=[],s=!0,c=!1;try{if(n=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(o=n.call(r)).done)&&(l.push(o.value),l.length!==e);s=!0);}catch(t){c=!0,a=t}finally{try{if(!s&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(c)throw a}}return l}}function f(t){if(Array.isArray(t))return t}function m(t,e,r,o,a,n,i){try{var l=t[n](i),s=l.value}catch(t){return void r(t)}l.done?e(s):Promise.resolve(s).then(o,a)}function p(t){return function(){var e=this,r=arguments;return new Promise((function(o,a){var n=t.apply(e,r);function i(t){m(n,o,a,i,l,"next",t)}function l(t){m(n,o,a,i,l,"throw",t)}i(void 0)}))}}var h={name:"EvaluateSettingDialog",props:{isshow:Boolean,loading:Boolean,type:{type:String,default:"add"},title:{type:String,default:"基础设置"},size:{type:String,default:"700px"},dialogInfo:{type:Object,default:function(){return{}}}},data:function(){var t=function(t,e,r){var o=/^[0-9]$/;o.test(e)?r():r(new Error("格式不正确"))};return{isLoading:!1,score:0,dialogForm:{food_evaluation_status:!0,shortcut_reply_field:"",on_scene:0,reservation_order:"",reservation_order_evaluate_type:"",anonymous:!1,start_score_field:[{score:5,content:"非常好"},{score:4,content:"好"},{score:3,content:"较好"},{score:2,content:"一般"},{score:1,content:"差"}],food_evaluation_field:[{score:5,field_name:""}],evaluation_score_field:[]},dialogFormRules:{food_evaluation_field:[{required:!0,message:"请输入",trigger:"blur"}],start_score_field:[{required:!0,message:"请输入",trigger:"blur"}],reservation_order_evaluate_type:[{required:!0,message:"请选择",trigger:"blur"}],on_scene:[{validator:t,trigger:"blur"}],reservation_order:[{validator:t,trigger:"blur"}]}}},computed:{showDrawer:{get:function(){return this.isshow&&this.getOperationData(),this.isshow},set:function(t){this.$emit("update:isshow",t)}}},watch:{},created:function(){},mounted:function(){},methods:{getOperationData:function(){var t=this;return p(i().mark((function e(){var r,o,a,n,s;return i().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.$to(t.$apis.apiBackgroundOperationManagementEvaluationSettingListPost());case 3:if(r=e.sent,o=l(r,2),a=o[0],n=o[1],t.isLoading=!1,!a){e.next=11;break}return t.$message.error(a.message),e.abrupt("return");case 11:0===n.code?(s=Object.keys(n.data.start_score_field).sort((function(t,e){return e-t})),t.dialogForm.start_score_field=s.map((function(t,e){return{score:Number(t),content:n.data.start_score_field[t]}})),t.dialogForm.food_evaluation_field=n.data.food_evaluation_field?n.data.food_evaluation_field:[],t.dialogForm.evaluation_score_field=n.data.evaluation_score_field?n.data.evaluation_score_field:[],t.dialogForm.evaluation_content_field=n.data.evaluation_content_field?n.data.evaluation_content_field:[],t.dialogForm.reservation_order_evaluate_type=n.data.reservation_order_evaluate_type,t.dialogForm.reservation_order=n.data.reservation_order,t.dialogForm.on_scene=n.data.on_scene,t.dialogForm.anonymous=!!n.data.anonymous,n.data.shortcut_reply_field&&n.data.shortcut_reply_field.length>0&&(t.dialogForm.shortcut_reply_field=n.data.shortcut_reply_field[0])):t.$message.error(n.msg);case 12:case"end":return e.stop()}}),e)})))()},closeHandle:function(){this.showDrawer=!1},addScoreHandle:function(t,e){this.dialogForm[t].splice(e+1,0,{score:5,field_name:""})},deleteHandle:function(t,e){this.dialogForm[t].length>1&&this.dialogForm[t].splice(e,1)},submitHandle:function(){var t=this;this.$refs.dialogForm.validate((function(e){if(e){if(t.isLoading)return;t.getAppealPendList()}}))},formatQueryParams:function(t){var e={};for(var r in t)""!==t[r]&&(t[r]instanceof Array?t[r].length>0&&(e[r]=t[r]):e[r]="anonymous"===r?t[r]?1:0:"shortcut_reply_field"===r?[t[r]]:t[r]);return e},getAppealPendList:function(){var t=this;return p(i().mark((function e(){var r,o,a,n;return i().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.$to(t.$apis.apiBackgroundOperationManagementEvaluationSettingModifyPost(t.formatQueryParams(t.dialogForm)));case 3:if(r=e.sent,o=l(r,2),a=o[0],n=o[1],t.isLoading=!1,!a){e.next=11;break}return t.$message.error(a.message),e.abrupt("return");case 11:0===n.code?(t.$message.success("成功"),t.showDrawer=!1,t.$emit("confirm")):t.$message.error(n.msg);case 12:case"end":return e.stop()}}),e)})))()}}},v=h,g=(r("907a0"),r("2877")),_=Object(g["a"])(v,o,a,!1,null,"35eb2aae",null);e["default"]=_.exports},86995:function(t,e,r){},"907a0":function(t,e,r){"use strict";r("86995")}}]);