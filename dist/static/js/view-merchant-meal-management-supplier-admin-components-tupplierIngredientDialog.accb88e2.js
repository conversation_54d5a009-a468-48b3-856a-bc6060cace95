(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-supplier-admin-components-tupplierIngredientDialog"],{"16f7":function(t,e,r){"use strict";r("c1db")},"228c":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("dialog-message",{attrs:{show:t.visible,title:t.title,showFooter:t.showFooter,loading:t.isLoading,customClass:"ps-dialog",width:t.width,top:"200px"},on:{"update:show":function(e){t.visible=e},"update:loading":function(e){t.isLoading=e},close:t.handleClose}},[e("el-form",{ref:"formData",staticClass:"dialog-form",attrs:{model:t.formData,"status-icon":"",rules:t.formDataRules,"label-width":"120px"},nativeOn:{submit:function(t){t.preventDefault()}}},[e("el-form-item",{attrs:{label:"食材名称",prop:"ingredient_id"}},[e("el-select",{staticClass:"ps-select",attrs:{placeholder:"请选择",size:"small"},model:{value:t.formData.ingredient_id,callback:function(e){t.$set(t.formData,"ingredient_id",e)},expression:"formData.ingredient_id"}},t._l(t.ingredientNameList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),e("el-form-item",{attrs:{label:"食材溯源码",prop:"sourced_code"}},[e("el-upload",{ref:"uploadRegistration",staticClass:"avatar-uploader",attrs:{data:t.uploadParams,action:t.actionUrl,"show-file-list":!1,"on-success":t.handleAvatarSuccess,"before-upload":t.beforeAvatarUpload}},[t.formData.sourced_code?e("img",{staticClass:"avatar",attrs:{src:t.formData.sourced_code}}):e("i",{staticClass:"el-icon-plus avatar-uploader-icon"}),e("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v(" 上传的图片不可超过2M ")])])],1)],1),e("template",{slot:"tool"},[t.showFooter?e("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:t.isLoading},on:{click:t.clickCancleHandle}},[t._v(" 取消 ")]),e("el-button",{staticClass:"ps-btn",attrs:{disabled:t.isLoading,type:"primary"},on:{click:t.clickConfirmHandle}},[t._v(" 确定 ")])],1):t._e()])],2)},o=[];function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function a(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */a=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},s="function"==typeof Symbol?Symbol:{},c=s.iterator||"@@iterator",u=s.asyncIterator||"@@asyncIterator",l=s.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function d(t,e,r,n){var i=e&&e.prototype instanceof w?e:w,a=Object.create(i.prototype),s=new P(n||[]);return o(a,"_invoke",{value:D(t,r,s)}),a}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=d;var h="suspendedStart",g="suspendedYield",m="executing",v="completed",y={};function w(){}function b(){}function L(){}var x={};f(x,c,(function(){return this}));var _=Object.getPrototypeOf,k=_&&_(_(C([])));k&&k!==r&&n.call(k,c)&&(x=k);var E=L.prototype=w.prototype=Object.create(x);function S(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function j(t,e){function r(o,a,s,c){var u=p(t[o],t,a);if("throw"!==u.type){var l=u.arg,f=l.value;return f&&"object"==i(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,s,c)}),(function(t){r("throw",t,s,c)})):e.resolve(f).then((function(t){l.value=t,s(l)}),(function(t){return r("throw",t,s,c)}))}c(u.arg)}var a;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return a=a?a.then(o,o):o()}})}function D(e,r,n){var o=h;return function(i,a){if(o===m)throw Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var s=n.delegate;if(s){var c=O(s,n);if(c){if(c===y)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===h)throw o=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=m;var u=p(e,r,n);if("normal"===u.type){if(o=n.done?v:g,u.arg===y)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(o=v,n.method="throw",n.arg=u.arg)}}}function O(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,O(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var i=p(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,y;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function I(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function $(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function P(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(I,this),this.reset(!0)}function C(e){if(e||""===e){var r=e[c];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(i(e)+" is not iterable")}return b.prototype=L,o(E,"constructor",{value:L,configurable:!0}),o(L,"constructor",{value:b,configurable:!0}),b.displayName=f(L,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,L):(t.__proto__=L,f(t,l,"GeneratorFunction")),t.prototype=Object.create(E),t},e.awrap=function(t){return{__await:t}},S(j.prototype),f(j.prototype,u,(function(){return this})),e.AsyncIterator=j,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new j(d(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},S(E),f(E,l,"Generator"),f(E,c,(function(){return this})),f(E,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=C,P.prototype={constructor:P,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach($),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return s.type="throw",s.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),u=n.call(a,"finallyLoc");if(c&&u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),$(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;$(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:C(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),y}},e}function s(t,e,r,n,o,i,a){try{var s=t[i](a),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,o)}function c(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){s(i,n,o,a,c,"next",t)}function c(t){s(i,n,o,a,c,"throw",t)}a(void 0)}))}}var u={name:"trayDialog",props:{loading:Boolean,type:{type:String,default:"add"},title:{type:String,default:"关联食材"},width:{type:String,default:"500px"},showFooter:{type:Boolean,default:!0},isshow:Boolean,ingredientInfo:{type:Object,default:function(){return{}}},confirm:Function},data:function(){return{isLoading:!1,formData:{ingredient_id:"",sourced_code:""},ingredientNameList:[],formDataRules:{ingredient_id:[{required:!0,message:"请选择食材名称",trigger:"change"}],sourced_code:[{required:!0,message:"请上传食材溯源码",trigger:"change"}]},actionUrl:"",uploadParams:{}}},computed:{visible:{get:function(){return this.isshow},set:function(t){this.$emit("update:isshow",t)}}},created:function(){this.getUploadToken(),this.getIngredientNameList()},mounted:function(){},methods:{getUploadToken:function(){var t=this;return c(a().mark((function e(){var r;return a().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.getUploadToken({prefix:"jpeg/png"});case 2:r=e.sent,0===r.code?(t.actionUrl=r.data.host,t.uploadParams={key:r.data.prefix+(new Date).getTime()+Math.floor(150*Math.random()),prefix:r.data.prefix,policy:r.data.policy,OSSAccessKeyId:r.data.accessid,signature:r.data.signature,callback:r.data.callback,success_action_status:"200"}):t.$message.error(r.msg);case 4:case"end":return e.stop()}}),e)})))()},getIngredientNameList:function(){var t=this;return c(a().mark((function e(){var r;return a().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.$apis.apiBackgroundFoodIngredientIngredientNamePost();case 3:r=e.sent,t.isLoading=!1,0===r.code?(t.ingredientNameList=r.data,t.initLoad()):t.$message.error(r.msg);case 6:case"end":return e.stop()}}),e)})))()},initLoad:function(){"modify"===this.type&&(this.formData=this.ingredientInfo)},clickConfirmHandle:function(){var t,e=this,r={ingredient_id:this.formData.ingredient_id,sourced_code:this.formData.sourced_code};this.$refs.formData.validate((function(n){n&&("add"===e.type?(r.supplier_id=e.ingredientInfo.supplier_id,t=e.$apis.apiBackgroundFoodIngredientSupplierAddSupplierIngredientPost(r)):(r.id=e.ingredientInfo.id,t=e.$apis.apiBackgroundFoodIngredientSupplierModifySupplierIngredientPost(r)),e.addEditTray(t))}))},addEditTray:function(t){var e=this;return c(a().mark((function r(){var n;return a().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!e.isLoading){r.next=2;break}return r.abrupt("return");case 2:return e.isLoading=!0,r.next=5,t;case 5:n=r.sent,e.isLoading=!1,e.visible=!1,0===n.code?(e.$message.success("成功"),e.$emit("confirm","search")):e.$message.error(n.msg);case 9:case"end":return r.stop()}}),r)})))()},clickCancleHandle:function(){this.visible=!1},handleAvatarSuccess:function(t,e){0===t.code?(this.$refs.uploadRegistration.clearFiles(),this.formData.sourced_code=t.data.public_url,this.getUploadToken()):this.$message.error(t.msg)},beforeAvatarUpload:function(t){var e=this;return c(a().mark((function r(){var n,o,i,s,c;return a().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(n="image/jpeg"===t.type||"image/png"===t.type,o=t.size/1024/1024<2,n){r.next=7;break}return e.$message.error("上传图片只能为jpg或png格式"),r.abrupt("return",!1);case 7:if(o){r.next=12;break}return e.$message.error("上传图片大小不能超过2MB"),r.abrupt("return",!1);case 12:return i=250,s=250,r.next=16,new Promise((function(e,r){var n=window.URL||window.webkitURL,o=new Image;o.onload=function(){var t=o.width===i&&o.height===s;t?e():r()},o.src=n.createObjectURL(t)})).then((function(){return!0}),(function(){return!1}));case 16:if(c=r.sent,c){r.next=20;break}return e.$message.error("上传图片尺寸必须为"+i+"*"+s+"!"),r.abrupt("return",Promise.reject());case 20:case"end":return r.stop()}}),r)})))()},handleClose:function(t){this.isLoading=!1,this.visible=!1,this.$refs.formData.resetFields()}}},l=u,f=(r("16f7"),r("2877")),d=Object(f["a"])(l,n,o,!1,null,null,null);e["default"]=d.exports},c1db:function(t,e,r){}}]);