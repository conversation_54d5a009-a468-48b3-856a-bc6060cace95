(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-components-WeekHeader"],{"0cc3":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t._self._c;return e("div",[e("div",{staticClass:"week-wrapper"},[e("i",{staticClass:"el-icon-arrow-left arrow-direction",on:{click:function(e){"week"===t.headerType?t.prevFourWeek():t.prevFourMonth()}}}),"week"===t.headerType?e("div",{staticClass:"data-wrapper"},t._l(t.weekList,(function(i){return e("el-card",{key:i.id,staticClass:"item",nativeOn:{click:function(e){return t.switchActiveWeek(i.id)}}},[e("div",{class:t.activeWeek===i.id?"active-week":""},[e("span",{staticStyle:{margin:"0 10px"}},[t._v(t._s(i.start)+" 至 "+t._s(i.end))])])])})),1):t._e(),"month"===t.headerType?e("div",{staticClass:"data-wrapper"},t._l(t.monthList,(function(i){return e("el-card",{key:i,staticClass:"item",nativeOn:{click:function(e){return t.switchActiveMonth(i)}}},[e("div",{class:t.activeMonth===i?"active-week":""},[e("span",{staticStyle:{margin:"0 10px"}},[t._v(t._s(i))])])])})),1):t._e(),e("i",{staticClass:"el-icon-arrow-right arrow-direction",on:{click:function(e){"week"===t.headerType?t.nextFourWeek():t.nextFourMonth()}}}),e("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){"week"===t.headerType?t.backToCurrentWeek():t.backToCurrentMonth()}}},[t._v(" 回到当"+t._s("week"===t.headerType?"周":"月")+" ")])],1)])},s=[],n=i("5a0c"),r={name:"WeekHeader",props:{headerType:{type:String},isFirstSearch:{type:Boolean,default:!0}},mounted:function(){this.initLoad()},data:function(){return{activeWeek:"",activeMonth:"",weekList:[],monthList:[]}},methods:{initLoad:function(){this.weekList=this.getWeekList(n(),0),this.monthList=this.getMonthList(n(),0),this.activeWeek=this.weekList[0].id,this.activeMonth=this.monthList[0]},switchActiveWeek:function(t){this.activeWeek=t,this.$parent.$refs.pagination.handleCurrentChange(1,!0),this.$parent.$refs.pagination.handleSizeChange(10,!0),this.$parent.onPaginationChange({current:1,pageSize:10})},switchActiveMonth:function(t){this.activeMonth=t,this.$parent.$refs.pagination.handleCurrentChange(1,!0),this.$parent.$refs.pagination.handleSizeChange(10,!0),this.$parent.onPaginationChange({current:1,pageSize:10})},nextFourWeek:function(){var t=n(this.weekList[3].end).add(1,"day");this.weekList=this.getWeekList(t,0)},nextFourMonth:function(){var t=this.monthList[3],e=t.replace("年","-").replace("月",""),i=n(e).add(1,"month");this.monthList=this.getMonthList(i,0)},prevFourWeek:function(){var t=this.weekList[0].start,e=n(t).subtract(2,"day");this.weekList=this.getWeekList(e,1)},prevFourMonth:function(){var t=this.monthList[0],e=t.replace("年","-").replace("月","");this.monthList=this.getMonthList(n(e),1)},backToCurrentWeek:function(){this.weekList=this.getWeekList(n(),0),this.activeWeek=this.weekList[0].id,this.$parent.requestMenuWeeklyList(),this.$message.success("已切换至当周")},backToMenuWeek:function(t){var e=n(t).startOf("week").add(1,"day").format("YYYY-MM-DD");this.weekList[0].start!==e&&(this.weekList=this.getWeekList(e,0)),this.activeWeek=this.weekList[0].id,this.$parent.requestMenuWeeklyList(),this.$message.success("已切换至沿用菜谱所在周")},backToCurrentMonth:function(){var t=n().format("YYYY年MM月");if(this.monthList[0]!==t){var e=n(t.replace("年","-").replace("月",""));this.monthList=this.getMonthList(e,0)}this.activeMonth=this.monthList[0],this.$parent.requestMenuWeeklyList(),this.$message.success("已切换至当月")},backToMenuMonth:function(t){if(this.monthList[0]!==t){var e=n(t.replace("年","-").replace("月",""));this.monthList=this.getMonthList(e,0)}this.activeMonth=this.monthList[0],this.$parent.requestMenuWeeklyList(),this.$message.success("已切换至沿用菜谱所在月")},getWeekList:function(t,e){var i=[],a=n(t).subtract(1,"day").startOf("week").add(1,"day"),s={id:a.format("YYYY-MM-DD"),start:a.format("YYYY-MM-DD"),end:n(t).subtract(1,"day").endOf("week").add(1,"day").format("YYYY-MM-DD")};if(0===e){i.push(s);for(var r=1;r<4;r++)i.push({id:a.add(r,"week").startOf("week").add(1,"day").format("YYYY-MM-DD"),start:a.add(r,"week").startOf("week").add(1,"day").format("YYYY-MM-DD"),end:a.add(r,"week").endOf("week").add(1,"day").format("YYYY-MM-DD")})}else for(var o=3;o>-1;o--)i.push({id:a.subtract(o,"week").startOf("week").add(1,"day").format("YYYY-MM-DD"),start:a.subtract(o,"week").startOf("week").add(1,"day").format("YYYY-MM-DD"),end:a.subtract(o,"week").endOf("week").add(1,"day").format("YYYY-MM-DD")});return i},getMonthList:function(t,e){var i=[],a=n(t);if(0===e){i.push(a.format("YYYY年MM月"));for(var s=1;s<4;s++)i.push(n(a).add(s,"month").format("YYYY年MM月"))}else for(var r=4;r>0;r--)i.push(n(a).subtract(r,"month").format("YYYY年MM月"));return i}}},o=r,h=(i("c6d5"),i("2877")),c=Object(h["a"])(o,a,s,!1,null,"2cc3717b",null);e["default"]=c.exports},c6d5:function(t,e,i){"use strict";i("f13e")},f13e:function(t,e,i){}}]);