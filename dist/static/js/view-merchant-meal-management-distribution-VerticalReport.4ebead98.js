(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-distribution-VerticalReport","view-merchant-meal-management-distribution-constantsConfig"],{8804:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",[e("div",{staticClass:"booking-meal-wrapper container-wrapper"},[e("refresh-tool",{on:{refreshPage:t.refreshHandle}}),e("search-form",{ref:"searchRef",attrs:{"form-setting":t.searchFormSetting},on:{search:t.searchHandle}}),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("数据列表")]),e("div",{staticClass:"align-r"},[e("button-icon",{attrs:{color:"origin",type:"export"},on:{click:t.handleExport}},[t._v("导出Excel")]),e("button-icon",{attrs:{color:"plain"},on:{click:t.handleExport}},[t._v("打印")]),e("button-icon",{attrs:{color:"plain"},on:{click:t.handleExport}},[t._v("报表设置")])],1)]),e("div",{staticClass:"table-content"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{border:"",data:t.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},t._l(t.columns,(function(t){return e("el-table-column",{key:t.column,attrs:{align:"center",prop:t.column,label:t.label}})})),1)],1),e("pagination",{attrs:{total:t.total,onPaginationChange:t.onPaginationChange}})],1)],1)])},o=[],a=r("8fb5");function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function l(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */l=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},c=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function h(t,e,r,n){var a=e&&e.prototype instanceof b?e:b,i=Object.create(a.prototype),l=new C(n||[]);return o(i,"_invoke",{value:S(t,r,l)}),i}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var d="suspendedStart",v="suspendedYield",y="executing",m="completed",g={};function b(){}function w(){}function L(){}var x={};f(x,c,(function(){return this}));var O=Object.getPrototypeOf,E=O&&O(O(F([])));E&&E!==r&&n.call(E,c)&&(x=E);var _=L.prototype=b.prototype=Object.create(x);function j(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function P(t,e){function r(o,a,l,c){var u=p(t[o],t,a);if("throw"!==u.type){var s=u.arg,f=s.value;return f&&"object"==i(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,l,c)}),(function(t){r("throw",t,l,c)})):e.resolve(f).then((function(t){s.value=t,l(s)}),(function(t){return r("throw",t,l,c)}))}c(u.arg)}var a;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return a=a?a.then(o,o):o()}})}function S(e,r,n){var o=d;return function(a,i){if(o===y)throw Error("Generator is already running");if(o===m){if("throw"===a)throw i;return{value:t,done:!0}}for(n.method=a,n.arg=i;;){var l=n.delegate;if(l){var c=T(l,n);if(c){if(c===g)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===d)throw o=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=y;var u=p(e,r,n);if("normal"===u.type){if(o=n.done?m:v,u.arg===g)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(o=m,n.method="throw",n.arg=u.arg)}}}function T(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,T(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var a=p(o,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,g;var i=a.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,g):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function D(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function C(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function F(e){if(e||""===e){var r=e[c];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(i(e)+" is not iterable")}return w.prototype=L,o(_,"constructor",{value:L,configurable:!0}),o(L,"constructor",{value:w,configurable:!0}),w.displayName=f(L,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,L):(t.__proto__=L,f(t,s,"GeneratorFunction")),t.prototype=Object.create(_),t},e.awrap=function(t){return{__await:t}},j(P.prototype),f(P.prototype,u,(function(){return this})),e.AsyncIterator=P,e.async=function(t,r,n,o,a){void 0===a&&(a=Promise);var i=new P(h(t,r,n,o),a);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},j(_),f(_,s,"Generator"),f(_,c,(function(){return this})),f(_,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=F,C.prototype={constructor:C,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(D),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return l.type="throw",l.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],l=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var c=n.call(i,"catchLoc"),u=n.call(i,"finallyLoc");if(c&&u){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=t,i.arg=e,a?(this.method="next",this.next=a.finallyLoc,g):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),D(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;D(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:F(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),g}},e}function c(t,e,r,n,o,a,i){try{var l=t[a](i),c=l.value}catch(t){return void r(t)}l.done?e(c):Promise.resolve(c).then(n,o)}function u(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var a=t.apply(e,r);function i(t){c(a,n,o,i,l,"next",t)}function l(t){c(a,n,o,i,l,"throw",t)}i(void 0)}))}}function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function f(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach((function(e){h(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function h(t,e,r){return(e=p(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function p(t){var e=d(t,"string");return"symbol"==i(e)?e:e+""}function d(t,e){if("object"!=i(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var v={name:"VerticalReport",data:function(){return{isLoading:!1,columns:[{label:"配送区域",column:"column1"},{label:"一级",column:"column2"},{label:"二级",column:"column3"},{label:"三级",column:"column4"},{label:"四级",column:"column5"},{label:"五级",column:"column6"},{label:"地址",column:"column7"},{label:"订单数量",column:"column8"},{label:"合计",column:"column9"}],tableData:[],page:1,pageSize:10,total:0,searchFormSetting:f({},a["TransverseSearchForm"])}},mounted:function(){this.initLoad()},methods:{initLoad:function(){this.requestPaymentOrderTotalList()},refreshHandle:function(){},searchHandle:function(){return u(l().mark((function t(){return l().wrap((function(t){while(1)switch(t.prev=t.next){case 0:case"end":return t.stop()}}),t)})))()},handleExport:function(){},requestPaymentOrderTotalList:function(){var t=this;return u(l().mark((function e(){var r;return l().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r={page:t.page,page_size:t.pageSize},e.next=3,t.$apis.apiBackgroundReportCenterDataReportPaymentOrderTotalListPost(r);case 3:e.sent;case 4:case"end":return e.stop()}}),e)})))()},onPaginationChange:function(){}}},y=v,m=r("2877"),g=Object(m["a"])(y,n,o,!1,null,null,null);e["default"]=g.exports},"8fb5":function(t,e,r){"use strict";r.r(e),r.d(e,"recentSevenDay",(function(){return o})),r.d(e,"GetMealSearchForm",(function(){return a})),r.d(e,"TransverseSearchForm",(function(){return i}));var n=r("5a0c"),o=[n().subtract(7,"day").format("YYYY-MM-DD"),n().format("YYYY-MM-DD")],a={select_time:{type:"daterange",label:"用餐时间",value:o},org_ids:{type:"organizationSelect",multiple:!1,isLazy:!0,label:"来源",value:"",placeholder:"请选择来源"},card_status:{type:"select",label:"配送区域",value:"",placeholder:"请选择配送区域",collapseTags:!0,dataList:[]}},i={select_time:{type:"daterange",label:"用餐时间",value:o},org_ids:{type:"organizationSelect",multiple:!1,isLazy:!0,label:"来源",value:"",placeholder:"请选择来源"},card_status:{type:"select",label:"配送区域",value:"",placeholder:"请选择配送区域",collapseTags:!0,dataList:[]},meal:{type:"select",label:"餐段",value:"",placeholder:"请选择餐段",collapseTags:!0,dataList:[]},one:{type:"select",label:"一级",value:"",placeholder:"请选择一级",collapseTags:!0,dataList:[]},two:{type:"select",label:"二级",value:"",placeholder:"请选择二级",collapseTags:!0,dataList:[]},three:{type:"select",label:"三级",value:"",placeholder:"请选择三级",collapseTags:!0,dataList:[]},four:{type:"select",label:"四级",value:"",placeholder:"请选择四级",collapseTags:!0,dataList:[]},five:{type:"select",label:"五级",value:"",placeholder:"请选择五级",collapseTags:!0,dataList:[]}}}}]);