(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-inventory-constants"],{8309:function(e,l,a){"use strict";a.r(l),a.d(l,"INVENTORY_TYPE",(function(){return n})),a.d(l,"ENTRY_TYPE",(function(){return r})),a.d(l,"OUT_TYPE",(function(){return u})),a.d(l,"APPROVAL_DAIBAN_SEARCHFORMSETTINGS",(function(){return _})),a.d(l,"APPROVAL_DAIBAN_TABLESETTINGS",(function(){return b})),a.d(l,"APPROVAL_YIBAN_SEARCHFORMSETTINGS",(function(){return y})),a.d(l,"APPROVAL_YIBAN_TABLESETTINGS",(function(){return i})),a.d(l,"APPROVAL_DETAIL_TABLESETTINGS",(function(){return o})),a.d(l,"CYCLE_TYPE_LIST",(function(){return p})),a.d(l,"QUARTER_LIST",(function(){return E})),a.d(l,"APTITUDE_LIST",(function(){return c})),a.d(l,"DELIVERY_STATUS",(function(){return v}));var t=a("ed08"),n=[{label:"采购入库",value:"PURCHASE_ENTRY"},{label:"借调入库",value:"BORROW_ENTRY"},{label:"导入入库",value:"IMPORT_ENTRY"},{label:"消耗出库",value:"EXPEND_EXIT"},{label:"借调出库",value:"BORROW_EXIT"},{label:"过期出库",value:"OVERDUE_EXIT"},{label:"退货出库",value:"REFUND_EXIT"}],r=[{label:"采购入库",value:"PURCHASE_ENTRY"},{label:"借调入库",value:"BORROW_ENTRY"},{label:"导入入库",value:"IMPORT_ENTRY"},{label:"赠送物资",value:"BESTOW_ENTRY"}],u=[{label:"过期出库",value:"OVERDUE_EXIT"},{label:"损耗出库",value:"EXPEND_EXIT"},{label:"借调出库",value:"BORROW_EXIT"},{label:"退货出库",value:"REFUND_EXIT"}],_={select_time:{type:"daterange",format:"yyyy-MM-dd",label:"申请时间",clearable:!1,value:Object(t["y"])(7)},name:{type:"input",label:"申请人",value:"",placeholder:"请输入"},trade_no:{type:"input",label:"单据编号",value:"",placeholder:"请输入"}},b=[{label:"申请时间",key:"create_time"},{label:"申请人",key:"name"},{label:"单据编号",key:"trade_no"},{label:"单据类型",key:"approve_type_alias"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right"}],y={date_type:{type:"select",label:"",value:"create_time",maxWidth:"100px",placeholder:"请选择",dataList:[{label:"申请时间",value:"create_time"},{label:"审批时间",value:"approve_time"}]},select_time:{type:"daterange",format:"yyyy-MM-dd",label:"",clearable:!1,value:Object(t["y"])(7)},name:{type:"input",label:"申请人",value:"",placeholder:"请输入"},trade_no:{type:"input",label:"单据编号",value:"",placeholder:"请输入"}},i=[{label:"申请时间",key:"create_time"},{label:"申请人",key:"name"},{label:"单据编号",key:"trade_no"},{label:"审批结果",key:"approve_status_alias"},{label:"审批意见",key:"reject_reason"},{label:"审批时间",key:"approve_time"},{label:"审批详情",key:"record_list",type:"slot",slotName:"record"},{label:"审批项进程",key:"deal_status_alias"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right"}],o={purchase_info:[{label:"物资名称",key:"name"},{label:"数量",key:"count"},{label:"单位",key:"unit_name"},{label:"参考单价",key:"ref_unit_price",type:"money"},{label:"合计",key:"total",type:"money"},{label:"供应商",key:"supplier_manage_name"}],entry_info:[{label:"物资名称",key:"materials_name"},{label:"入库数量",key:"expected_entry_count"},{label:"单位",key:"unit_name"},{label:"参考单价",key:"ref_unit_price",type:"money"},{label:"入库价",key:"entry_price",type:"money"},{label:"供应商",key:"supplier_manage_name"}],exit_info:[{label:"物资名称",key:"materials_name"},{label:"出库数量",key:"count"},{label:"单位",key:"unit_name"},{label:"供应商",key:"supplier_manage_name"}],return_info:[{label:"物资名称",key:"materials_name"},{label:"出库数量",key:"refund_count"},{label:"单位",key:"unit_name"},{label:"入库价",key:"ref_unit_price",type:"money"},{label:"退货金额",key:"refund_fee",type:"money"}],subscribe_info:[{label:"物资名称",key:"materials_name"},{label:"申购数量",key:"count"},{label:"单位",key:"unit_name"}]},p=[{label:"按天",value:"DAY"},{label:"按周",value:"WEEK"},{label:"按月",value:"MONTH"}],E=[{label:"第1季度",value:"1"},{label:"第2季度",value:"2"},{label:"第3季度",value:"3"},{label:"第4季度",value:"4"}],c=[{label:"营业执照",value:"1"},{label:"食品经营许可证",value:"2"},{label:"食品生产许可证",value:"3"}],v=[{label:"待配送",value:"wait_delivery"},{label:"配送中",value:"delivering"},{label:"货物送达待确认",value:"arrive"},{label:"货物送达已确认",value:"confirmed"}]}}]);