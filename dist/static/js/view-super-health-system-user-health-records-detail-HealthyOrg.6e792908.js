(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-user-health-records-detail-HealthyOrg"],{6361:function(t,a,e){"use strict";e.r(a);var n=function(){var t=this,a=t._self._c;return a("div",{staticClass:"healthy-org records-wrapp-bg m-b-20"},[t._m(0),a("div",{staticClass:"text"},[t._v(" "+t._s(t.formData.org_name)+" ")])])},s=[function(){var t=this,a=t._self._c;return a("div",{staticClass:"p-b-10"},[a("span",{staticStyle:{"font-weight":"bold"}},[t._v("所属组织")])])}],r={props:{formInfoData:{type:Object,default:function(){return{}}}},data:function(){return{formData:{}}},watch:{formInfoData:function(t){this.formData=t}},mounted:function(){},methods:{}},o=r,i=(e("f504"),e("2877")),f=Object(i["a"])(o,n,s,!1,null,"812fef84",null);a["default"]=f.exports},b877:function(t,a,e){},f504:function(t,a,e){"use strict";e("b877")}}]);