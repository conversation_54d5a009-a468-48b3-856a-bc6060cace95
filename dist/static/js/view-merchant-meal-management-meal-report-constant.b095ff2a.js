(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-meal-report-constant"],{"0604":function(e,l,a){"use strict";a.r(l),a.d(l,"TABLE_HEAD_DATA_IMPORT_MEAL",(function(){return t})),a.d(l,"MEAL_PACKAGE_SEARCH",(function(){return p})),a.d(l,"MEAL_PACKAGE_TABLE",(function(){return n}));a("ed08");var t=[{label:"姓名",key:"name"},{label:"人员编号",key:"person_no"},{label:"分组",key:"payer_group"},{label:"部门",key:"department_group"},{label:"消费点",key:"organization"},{label:"报餐时间",key:"report_date"},{label:"报餐餐段",key:"meal_type"},{label:"份数",key:"count"},{label:"取餐方式",key:"take_meal_type"}],p={report_meal_pack_settings_id:{type:"select",label:"餐包名称",value:"",placeholder:"请选择",listNameKey:"name",listValueKey:"id",dataList:[]},payer_group_ids:{type:"groupSelect",label:"分组",value:[],multiple:!0,collapseTags:!0,placeholder:"请选择分组"},payer_department_group_ids:{type:"departmentSelect",multiple:!0,isLazy:!1,checkStrictly:!0,collapseTags:!0,label:"部门",value:[],placeholder:"请选择部门"},name:{type:"input",label:"姓名",value:"",placeholder:"请输入姓名"},person_no:{type:"input",label:"人员编号",value:"",placeholder:"请输入人员编号"},phone:{type:"input",label:"手机号",value:"",maxlength:11,placeholder:"请输入手机号"},buy_status:{type:"select",value:"buy",label:"购买状态",dataList:[{label:"已购买",value:"buy"},{label:"未购买",value:"not_buy"},{label:"已退款",value:"refund"}]}},n=[{label:"餐包名称",key:"report_meal_pack_settingsname"},{label:"姓名",key:"name"},{label:"人员编号",key:"person_no"},{label:"手机号",key:"phone"},{label:"分组",key:"card_user_group_alias",showTooltip:!0},{label:"部门",key:"department_group_name"},{label:"购买状态",key:"order_status_alias"},{label:"购买日期",key:"pay_time",isComponents:!0,type:"date",format:"YYYY-MM-DD"},{label:"购买价格",key:"real_fee",type:"money"},{label:"退款日期",key:"refund_time",isComponents:!0,type:"date",format:"YYYY-MM-DD"},{label:"使用情况",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"80"}]}}]);