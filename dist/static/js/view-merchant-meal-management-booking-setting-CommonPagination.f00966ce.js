(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-booking-setting-CommonPagination"],{"85e9":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[t("el-pagination",{staticClass:"ps-text",attrs:{"current-page":e.currentPage,"page-sizes":[10,20,50,100,500],"page-size":e.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:e.total,background:"","popper-class":"ps-popper-select"},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange,"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t}}})],1)},i=[],r={data:function(){return{pageSize:10,currentPage:1}},props:{total:{type:Number},onPaginationChange:{type:Function}},methods:{handleSizeChange:function(e,t){this.pageSize=e,t||this.onPaginationChange({current:this.currentPage,pageSize:this.pageSize})},handleCurrentChange:function(e,t){this.currentPage=e,t||this.onPaginationChange({current:this.currentPage,pageSize:this.pageSize})}}},g=r,p=n("2877"),o=Object(p["a"])(g,a,i,!1,null,null,null);t["default"]=o.exports}}]);