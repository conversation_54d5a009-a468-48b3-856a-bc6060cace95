(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-merchant-admin-components-seniorSetting"],{"64df":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"deductsetting-wrapper"},[t._m(0),e("div",{staticClass:"form-wrapper",staticStyle:{"max-width":"700px"}},[e("el-form",{ref:"seniorFormRef",attrs:{model:t.seniorFormData,rules:t.seniorFormRuls,"label-width":"120px"}},[e("el-form-item",{attrs:{prop:"money",label:"可选充值金额"}},[e("el-input",{staticClass:"ps-input",staticStyle:{width:"300px"},attrs:{maxlength:9},model:{value:t.seniorFormData.money,callback:function(e){t.$set(t.seniorFormData,"money",e)},expression:"seniorFormData.money"}}),t.seniorFormData.rechargeAmountList.length<6?e("el-button",{staticClass:"add-btn",attrs:{disabled:!t.seniorFormData.money,icon:"el-icon-circle-plus",type:"text",circle:""},on:{click:t.addMoneyList}}):t._e(),e("div",{staticClass:"money-tag m-t-10"},t._l(t.seniorFormData.rechargeAmountList,(function(r,n){return e("el-tag",{key:r+n,attrs:{closable:""},on:{close:function(e){return t.closeMoneyTag(r,n)}}},[t._v(" "+t._s(r+"元")+" ")])})),1)],1),e("el-form-item",{attrs:{prop:"abcPayTime",label:"可充值任意金额"}},[e("el-switch",{staticStyle:{"margin-right":"25px"},attrs:{"active-color":"#ff9b45"},model:{value:t.seniorFormData.allowCustomAmount,callback:function(e){t.$set(t.seniorFormData,"allowCustomAmount",e)},expression:"seniorFormData.allowCustomAmount"}}),e("el-checkbox",{staticClass:"ps-checkbox",attrs:{disabled:!t.seniorFormData.allowCustomAmount},model:{value:t.seniorFormData.openMinimumRechargeAmount,callback:function(e){t.$set(t.seniorFormData,"openMinimumRechargeAmount",e)},expression:"seniorFormData.openMinimumRechargeAmount"}},[t._v("最低需要充值")]),e("el-input",{staticClass:"ps-input",staticStyle:{width:"80px",margin:"0 10px"},attrs:{maxlength:9,disabled:!(t.seniorFormData.openMinimumRechargeAmount&&t.seniorFormData.allowCustomAmount)},model:{value:t.seniorFormData.minimumRechargeAmount,callback:function(e){t.$set(t.seniorFormData,"minimumRechargeAmount",e)},expression:"seniorFormData.minimumRechargeAmount"}}),t._v(" 元 ")],1),e("div",{staticClass:"m-b-10",staticStyle:{color:"red","font-size":"12px"}},[t._v(" 注：最低充值对应当前组织的充值限制 ")]),e("el-form-item",{attrs:{prop:"rechargeDateType",label:"指定日期可充值"}},t._l(t.rechargePaySceneType,(function(r,n){return e("div",{key:r},[e("label",{staticStyle:{"margin-right":"10px",float:"left"}},[t._v(t._s(r)+" ")]),e("div",{staticClass:"inline-block"},[e("el-checkbox-group",{staticClass:"ps-checkbox",on:{change:t.changeRechargeDate},model:{value:t.seniorFormData.rechargeDateType[n],callback:function(e){t.$set(t.seniorFormData.rechargeDateType,n,e)},expression:"seniorFormData.rechargeDateType[payScene]"}},[e("div",{staticClass:"money-tag"},[e("el-checkbox",{attrs:{label:"month"}},[t._v("每月")]),t._l(t.seniorFormData.allowRechargeDateList[n],(function(r,a){return e("el-tag",{key:r+a,staticClass:"m-l-10 m-r-10 m-b-10",attrs:{closable:""},on:{close:function(e){return t.closeDateHandle(r,a,n)}}},[t._v(" "+t._s(r+" 号")+" ")])})),t.seniorFormData.allowRechargeDateList[n].length<6?e("span",[t.inputVisible[n]?e("el-form-item",{staticClass:"inline-label",attrs:{prop:"dateValue",label:""}},[e("el-input",{ref:n+"saveTagInput",refInFor:!0,staticClass:"input-new-tag ps-input m-l-10",attrs:{size:"small",disabled:t.isDisabledDate(n)},on:{blur:function(e){return t.handleInputConfirm(n)}},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleInputConfirm(n)}},model:{value:t.seniorFormData.dateValue,callback:function(e){t.$set(t.seniorFormData,"dateValue",e)},expression:"seniorFormData.dateValue"}})],1):e("el-button",{staticClass:"button-new-tag",attrs:{disabled:t.isDisabledDate(n),size:"small"},on:{click:function(e){return t.showInput(n)}}},[t._v("+")])],1):t._e()],2),e("div",{},[e("el-checkbox",{attrs:{label:"lastDay"}},[t._v("每月最后一天")])],1)])],1)])})),0),e("div",{staticClass:"form-line ps-line"}),e("div",{staticClass:"l-title"},[e("span",[t._v("其它设置")])]),e("div",{staticClass:"inline"},[e("el-form-item",{attrs:{prop:"limitTodayRechargeAmount",label:"单日累计充值上限"}},[e("el-input",{staticClass:"ps-input",attrs:{maxlength:9},model:{value:t.seniorFormData.limitTodayRechargeAmount,callback:function(e){t.$set(t.seniorFormData,"limitTodayRechargeAmount",e)},expression:"seniorFormData.limitTodayRechargeAmount"}})],1),e("el-form-item",{attrs:{prop:"limitTodayConsumeAmount",label:"单日累计消费上限"}},[e("el-input",{staticClass:"ps-input",attrs:{maxlength:9},model:{value:t.seniorFormData.limitTodayConsumeAmount,callback:function(e){t.$set(t.seniorFormData,"limitTodayConsumeAmount",e)},expression:"seniorFormData.limitTodayConsumeAmount"}})],1),e("el-form-item",{attrs:{prop:"limitBalanceAmount",label:"钱包累计余额上限"}},[e("el-input",{staticClass:"ps-input",attrs:{maxlength:9},model:{value:t.seniorFormData.limitBalanceAmount,callback:function(e){t.$set(t.seniorFormData,"limitBalanceAmount",e)},expression:"seniorFormData.limitBalanceAmount"}})],1)],1),e("div",{staticStyle:{color:"red","font-size":"12px"}},[t._v(" 注：该设置只针对当前组织的储值钱包进行设置 ")]),e("div",{staticClass:"form-line ps-line"}),e("div",{staticClass:"l-title"},[e("span",[t._v("隐私设置")])]),e("div",{staticClass:"inline"},[e("el-form-item",{staticClass:"form-item-box",attrs:{label:"",prop:""}},[e("span",{staticStyle:{"margin-right":"25px"}},[t._v(" 人员编号 "),e("el-switch",{staticStyle:{"margin-right":"25px"},attrs:{"active-color":"#ff9b45","active-value":1,"inactive-value":0},model:{value:t.seniorFormData.sensitive_json.person_no,callback:function(e){t.$set(t.seniorFormData.sensitive_json,"person_no",e)},expression:"seniorFormData.sensitive_json.person_no"}})],1),e("span",{staticStyle:{"margin-right":"25px"}},[t._v(" 手机号码 "),e("el-switch",{staticStyle:{"margin-right":"25px"},attrs:{"active-color":"#ff9b45","active-value":1,"inactive-value":0},model:{value:t.seniorFormData.sensitive_json.phone,callback:function(e){t.$set(t.seniorFormData.sensitive_json,"phone",e)},expression:"seniorFormData.sensitive_json.phone"}})],1),e("span",{staticStyle:{"margin-right":"25px"}},[t._v(" 卡号 "),e("el-switch",{staticStyle:{"margin-right":"25px"},attrs:{"active-color":"#ff9b45","active-value":1,"inactive-value":0},model:{value:t.seniorFormData.sensitive_json.card_no,callback:function(e){t.$set(t.seniorFormData.sensitive_json,"card_no",e)},expression:"seniorFormData.sensitive_json.card_no"}})],1),e("span",{staticStyle:{"margin-right":"25px"}},[t._v(" 身份证号 "),e("el-switch",{staticStyle:{"margin-right":"25px"},attrs:{"active-color":"#ff9b45","active-value":1,"inactive-value":0},model:{value:t.seniorFormData.sensitive_json.id_number,callback:function(e){t.$set(t.seniorFormData.sensitive_json,"id_number",e)},expression:"seniorFormData.sensitive_json.id_number"}})],1)])],1),e("div",{staticStyle:{color:"red","font-size":"12px"}},[t._v(" 注：关闭后商户后台则隐藏对应字段 ")]),e("div",{staticClass:"add-wrapper"},[e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.organization.modify_settings"],expression:"['background.admin.organization.modify_settings']"}],staticClass:"ps-origin-btn",attrs:{type:"primary"},on:{click:t.saveWalletHandle}},[t._v("保存")])],1)],1)],1)])},a=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"l-title"},[e("span",[t._v("充值设置")])])}],i=r("ed08");function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},l=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function m(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{m({},"")}catch(t){m=function(t,e,r){return t[e]=r}}function h(t,e,r,n){var i=e&&e.prototype instanceof _?e:_,o=Object.create(i.prototype),s=new O(n||[]);return a(o,"_invoke",{value:k(t,r,s)}),o}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var p="suspendedStart",g="suspendedYield",d="executing",v="completed",y={};function _(){}function b(){}function D(){}var w={};m(w,l,(function(){return this}));var F=Object.getPrototypeOf,x=F&&F(F(R([])));x&&x!==r&&n.call(x,l)&&(w=x);var A=D.prototype=_.prototype=Object.create(w);function L(t){["next","throw","return"].forEach((function(e){m(t,e,(function(t){return this._invoke(e,t)}))}))}function j(t,e){function r(a,i,s,l){var c=f(t[a],t,i);if("throw"!==c.type){var u=c.arg,m=u.value;return m&&"object"==o(m)&&n.call(m,"__await")?e.resolve(m.__await).then((function(t){r("next",t,s,l)}),(function(t){r("throw",t,s,l)})):e.resolve(m).then((function(t){u.value=t,s(u)}),(function(t){return r("throw",t,s,l)}))}l(c.arg)}var i;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return i=i?i.then(a,a):a()}})}function k(e,r,n){var a=p;return function(i,o){if(a===d)throw Error("Generator is already running");if(a===v){if("throw"===i)throw o;return{value:t,done:!0}}for(n.method=i,n.arg=o;;){var s=n.delegate;if(s){var l=S(s,n);if(l){if(l===y)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===p)throw a=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=d;var c=f(e,r,n);if("normal"===c.type){if(a=n.done?v:g,c.arg===y)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(a=v,n.method="throw",n.arg=c.arg)}}}function S(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,S(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var i=f(a,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,y;var o=i.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function T(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function C(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function O(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(T,this),this.reset(!0)}function R(e){if(e||""===e){var r=e[l];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,i=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(o(e)+" is not iterable")}return b.prototype=D,a(A,"constructor",{value:D,configurable:!0}),a(D,"constructor",{value:b,configurable:!0}),b.displayName=m(D,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,D):(t.__proto__=D,m(t,u,"GeneratorFunction")),t.prototype=Object.create(A),t},e.awrap=function(t){return{__await:t}},L(j.prototype),m(j.prototype,c,(function(){return this})),e.AsyncIterator=j,e.async=function(t,r,n,a,i){void 0===i&&(i=Promise);var o=new j(h(t,r,n,a),i);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},L(A),m(A,u,"Generator"),m(A,l,(function(){return this})),m(A,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=R,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(C),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return s.type="throw",s.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var l=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),C(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;C(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:R(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),y}},e}function l(t,e){return f(t)||h(t,e)||u(t,e)||c()}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(t,e){if(t){if("string"==typeof t)return m(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?m(t,e):void 0}}function m(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function h(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,i,o,s=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(s.push(n.value),s.length!==e);l=!0);}catch(t){c=!0,a=t}finally{try{if(!l&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw a}}return s}}function f(t){if(Array.isArray(t))return t}function p(t,e,r,n,a,i,o){try{var s=t[i](o),l=s.value}catch(t){return void r(t)}s.done?e(l):Promise.resolve(l).then(n,a)}function g(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var i=t.apply(e,r);function o(t){p(i,n,a,o,s,"next",t)}function s(t){p(i,n,a,o,s,"throw",t)}o(void 0)}))}}var d={name:"SuperSeniorSetting",props:{type:String,infoData:{type:Object,default:function(){return{}}},organizationData:Object,restoreHandle:Function},data:function(){var t=function(t,e,r){var n=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;if(""!==e||"money"!==t.field&&"limitBalanceAmount"!==t.field)return"0"===e?r(new Error("金额格式有误")):void(n.test(e)?r():r(new Error("金额格式有误")));r()},e=function(t,e,r){var n=/^\+?[1-9][0-9]*$/;if("0"===e)return r(new Error("日期不能为0"));n.test(e)?(e>28&&r(new Error("不能超过28")),r()):r(new Error("日期格式有误"))};return{isLoading:!1,settingInfo:null,formOperate:"detail",rechargePaySceneType:{charge:"线上",charge_offline:"线下"},seniorFormData:{money:"",rechargeAmountList:[],allowCustomAmount:!1,openMinimumRechargeAmount:!1,minimumRechargeAmount:"",rechargeDateType:{charge:[],charge_offline:[]},allowRechargeDateList:{charge:[],charge_offline:[]},limitTodayRechargeAmount:"",limitTodayConsumeAmount:"",limitBalanceAmount:"",dateValue:"",sensitive_json:{card_no:1,phone:1,person_no:1,id_number:0}},seniorFormRuls:{limitTodayRechargeAmount:[{required:!0,validator:t,trigger:"blur"}],limitTodayConsumeAmount:[{required:!0,validator:t,trigger:"blur"}],limitBalanceAmount:[{validator:t,trigger:"blur"}],money:[{validator:t,trigger:"change"}],dateValue:[{validator:e,trigger:"change"}]},inputVisible:{charge:!1,charge_offline:!1},inputValue:{charge:"",charge_offline:""}}},computed:{isDisabledDate:function(){return function(t){return!this.seniorFormData.rechargeDateType[t].includes("month")}}},watch:{type:function(t){},organizationData:function(t){var e=this;setTimeout((function(){e.searchHandle()}),50)}},created:function(){},mounted:function(){this.initLoad()},methods:{initLoad:function(){this.getSettingInfo()},refreshHandle:function(){this.currentPage=1,this.initLoad()},searchHandle:Object(i["d"])((function(){this.initLoad()}),300),getSettingInfo:function(){var t=this;return g(s().mark((function e(){var r,n,a,o;return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(i["Z"])(t.$apis.apiBackgroundAdminOrganizationGetSettingsPost({id:t.organizationData.id,company:t.organizationData.company}));case 3:if(r=e.sent,n=l(r,2),a=n[0],o=n[1],t.isLoading=!1,!a){e.next=11;break}return t.$message.error(a.message),e.abrupt("return");case 11:0===o.code?(t.settingInfo=o.data,t.initSettingInfo(o.data)):t.$message.error(o.msg);case 12:case"end":return e.stop()}}),e)})))()},initSettingInfo:function(t){for(var e in this.seniorFormData.rechargeAmountList=t.recharge_amount_list.map((function(t){return Object(i["i"])(t)})),this.seniorFormData.limitTodayRechargeAmount=Object(i["i"])(t.limit_today_recharge_amount),this.seniorFormData.limitTodayConsumeAmount=Object(i["i"])(t.limit_today_consume_amount),this.seniorFormData.limitBalanceAmount=Object(i["i"])(t.limit_balance_amount),this.seniorFormData.allowCustomAmount=t.allow_custom_amount,Object.keys(t.sensitive_json)&&Object.keys(t.sensitive_json).length&&(this.seniorFormData.sensitive_json=t.sensitive_json),t.minimum_recharge_amount&&(this.seniorFormData.openMinimumRechargeAmount=!0,this.seniorFormData.minimumRechargeAmount=Object(i["i"])(t.minimum_recharge_amount)),t.allow_recharge_date_list instanceof Array&&(t.allow_recharge_date_list={charge:Object(i["f"])(t.allow_recharge_date_list),charge_offline:[]}),this.rechargePaySceneType)if(this.seniorFormData.rechargeDateType[e]=[],t.allow_recharge_date_list[e]&&t.allow_recharge_date_list[e].length){var r=t.allow_recharge_date_list[e].indexOf(-1);r>-1?(t.allow_recharge_date_list[e].length>1&&this.seniorFormData.rechargeDateType[e].push("month"),t.allow_recharge_date_list[e].splice(r,1),this.seniorFormData.rechargeDateType[e].push("lastDay"),this.seniorFormData.allowRechargeDateList[e]=t.allow_recharge_date_list[e]):(this.seniorFormData.rechargeDateType[e].push("month"),this.seniorFormData.allowRechargeDateList[e]=t.allow_recharge_date_list[e])}else this.seniorFormData.rechargeDateType[e]=[]},addMoneyList:function(){var t=this,e=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;e.test(this.seniorFormData.money)?(this.seniorFormData.rechargeAmountList.push(this.seniorFormData.money),this.seniorFormData.money="",this.$nextTick((function(){t.$refs.seniorFormRef.clearValidate("money")}))):this.$message.error("金额格式有误，请重新输入！")},closeMoneyTag:function(t,e){this.seniorFormData.rechargeAmountList.splice(e,1)},changeRechargeDate:function(t){},saveWalletHandle:function(){var t=this;this.$refs.seniorFormRef.validate((function(e){if(e){if(t.isLoading)return t.$message.error("请勿重复提交!");t.setSeniorSettingHandle()}}))},setSeniorSettingHandle:function(){var t=this;return g(s().mark((function e(){var r,n,a,o,c,u;return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(n in t.isLoading=!0,r={id:t.organizationData.id,allow_custom_amount:t.seniorFormData.allowCustomAmount,limit_today_recharge_amount:Object(i["Y"])(t.seniorFormData.limitTodayRechargeAmount),limit_today_consume_amount:Object(i["Y"])(t.seniorFormData.limitTodayConsumeAmount),company:t.organizationData.company,sensitive_json:t.seniorFormData.sensitive_json,allow_recharge_date_list:{charge:[],charge_offline:[]}},t.seniorFormData.rechargeAmountList.length>0&&(r.recharge_amount_list=t.seniorFormData.rechargeAmountList.map((function(t){return Object(i["Y"])(t)}))),t.seniorFormData.limitBalanceAmount&&(r.limit_balance_amount=Object(i["Y"])(t.seniorFormData.limitBalanceAmount)),t.seniorFormData.openMinimumRechargeAmount?r.minimum_recharge_amount=Object(i["Y"])(t.seniorFormData.minimumRechargeAmount):r.minimum_recharge_amount=0,t.rechargePaySceneType)t.seniorFormData.rechargeDateType[n].length>0?(t.seniorFormData.rechargeDateType[n].includes("month")&&(r.allow_recharge_date_list[n]=Object(i["f"])(t.seniorFormData.allowRechargeDateList[n])),t.seniorFormData.rechargeDateType[n].includes("lastDay")&&(r.allow_recharge_date_list[n]&&r.allow_recharge_date_list[n].length?r.allow_recharge_date_list[n].push(-1):r.allow_recharge_date_list[n]=[-1])):r.allow_recharge_date_list[n]=[];return e.next=8,Object(i["Z"])(t.$apis.apiBackgroundAdminOrganizationModifySettingsPost(r));case 8:if(a=e.sent,o=l(a,2),c=o[0],u=o[1],t.isLoading=!1,!c){e.next=16;break}return t.$message.error(c.message),e.abrupt("return");case 16:0===u.code?(t.$message.success(u.msg),t.getSettingInfo()):t.$message.error(u.msg);case 17:case"end":return e.stop()}}),e)})))()},closeDateHandle:function(t,e,r){this.seniorFormData.allowRechargeDateList[r].splice(e,1)},showInput:function(t){var e=this;this.inputVisible[t]=!0,this.$nextTick((function(r){e.$refs[t+"saveTagInput"][0].$refs.input.focus()}))},handleInputConfirm:function(t){var e=this.seniorFormData.dateValue,r=this.seniorFormData.allowRechargeDateList[t].indexOf(Number(e)),n=/^\+?[1-9][0-9]*$/,a=!0;"0"===e&&(a=!1),(!n.test(e)||Number(e)>28||Number(e)<1)&&(a=!1),e&&a&&Number(e)&&(r<0?(this.seniorFormData.allowRechargeDateList[t].push(Number(e)),this.sortList(this.seniorFormData.allowRechargeDateList[t])):this.$message.warning("请不要添加相同的日期")),(a||""===e)&&(this.inputVisible[t]=!1,this.seniorFormData.dateValue="")},sortList:function(t){t=t.sort((function(t,e){return t-e}))}}},v=d,y=(r("b122"),r("2877")),_=Object(y["a"])(v,n,a,!1,null,null,null);e["default"]=_.exports},b122:function(t,e,r){"use strict";r("e72c")},e72c:function(t,e,r){t.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}}}]);