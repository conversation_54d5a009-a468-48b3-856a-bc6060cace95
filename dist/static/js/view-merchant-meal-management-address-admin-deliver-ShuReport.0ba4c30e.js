(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-address-admin-deliver-ShuReport","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-AreaFood","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-components-menu-menuPreviewDialog","view-merchant-meal-management-meal-report-MealPackageRule"],{"1f59":function(e,t,r){e.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}},"3dc1":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"ShuReport container-wrapper"},[t("search-form",{ref:"searchRef",attrs:{"form-setting":e.searchFormSetting,"label-width":"105px",autoSearch:!1},on:{search:e.searchHandle,reset:e.resetHandle}}),t("div",{staticClass:"table-wrapper"},[t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[e._v("数据列表")]),t("div",{staticClass:"align-r"},[t("el-checkbox",{attrs:{disabled:e.tableData.length<=0},on:{change:e.selectAllChange},model:{value:e.isSelectAll,callback:function(t){e.isSelectAll=t},expression:"isSelectAll"}},[e._v("全部选择")]),t("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_order.order_reservation.get_delivery_collect_by_y_export"],expression:"['background_order.order_reservation.get_delivery_collect_by_y_export']"}],attrs:{color:"plain",type:"export"},on:{click:e.handleExport}},[e._v("导出报表")]),t("button-icon",{attrs:{color:"plain"},on:{click:e.openDialog}},[e._v("小票打印")])],1)]),t("div",{staticClass:"table-content"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"",border:"","header-row-class-name":"ps-table-header-row","span-method":e.objectSpanMethod},on:{"selection-change":e.handleSelectionChange,select:e.selectSelection,"select-all":e.selectSelectionAll}},[t("el-table-column",{attrs:{type:"selection",prop:"selection",align:"center","class-name":"ps-checkbox"}}),t("el-table-column",{attrs:{prop:"area_name",label:"区域",align:"center"}}),t("el-table-column",{attrs:{prop:"l1_addr",label:"一级",align:"center"}}),t("el-table-column",{attrs:{prop:"l2_addr",label:"二级",align:"center"}}),t("el-table-column",{attrs:{prop:"l3_addr",label:"三级",align:"center"}}),t("el-table-column",{attrs:{prop:"l4_addr",label:"四级",align:"center"}}),t("el-table-column",{attrs:{prop:"l5_addr",label:"五级",align:"center"}}),t("el-table-column",{attrs:{prop:"user_name",label:"取餐人",align:"center"}}),t("el-table-column",{attrs:{prop:"phone",label:"手机号",align:"center"}}),t("el-table-column",{attrs:{prop:"remark",label:"备注",align:"center"}}),t("el-table-column",{attrs:{prop:"food_name",label:"菜品",align:"center"}}),t("el-table-column",{attrs:{prop:"food_count",label:"数量",align:"center"}})],1)],1),t("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[t("el-pagination",{staticClass:"ps-text",attrs:{"current-page":e.currentPage,"page-sizes":[10,20,50,100,500],"page-size":e.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)]),t("print-ticket",{attrs:{isshow:e.dialogPrintVisible,type:"order","select-list-id":e.selectOrderId},on:{"update:isshow":function(t){e.dialogPrintVisible=t},confirm:e.searchHandle}})],1)},n=[],i=r("ed08"),l=r("c9d9"),o=r("cc06"),s=r("f63a"),c=r("f1bf");function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}function d(e){return m(e)||p(e)||f(e)||h()}function h(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(e,t){if(e){if("string"==typeof e)return g(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?g(e,t):void 0}}function p(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function m(e){if(Array.isArray(e))return g(e)}function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,a=Array(t);r<t;r++)a[r]=e[r];return a}function y(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */y=function(){return t};var e,t={},r=Object.prototype,a=r.hasOwnProperty,n=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},l=i.iterator||"@@iterator",o=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function c(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,r){return e[t]=r}}function d(e,t,r,a){var i=t&&t.prototype instanceof b?t:b,l=Object.create(i.prototype),o=new C(a||[]);return n(l,"_invoke",{value:F(e,r,o)}),l}function h(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var f="suspendedStart",p="suspendedYield",m="executing",g="completed",v={};function b(){}function _(){}function w(){}var S={};c(S,l,(function(){return this}));var k=Object.getPrototypeOf,x=k&&k(k(D([])));x&&x!==r&&a.call(x,l)&&(S=x);var L=w.prototype=b.prototype=Object.create(S);function O(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function A(e,t){function r(n,i,l,o){var s=h(e[n],e,i);if("throw"!==s.type){var c=s.arg,d=c.value;return d&&"object"==u(d)&&a.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,l,o)}),(function(e){r("throw",e,l,o)})):t.resolve(d).then((function(e){c.value=e,l(c)}),(function(e){return r("throw",e,l,o)}))}o(s.arg)}var i;n(this,"_invoke",{value:function(e,a){function n(){return new t((function(t,n){r(e,a,t,n)}))}return i=i?i.then(n,n):n()}})}function F(t,r,a){var n=f;return function(i,l){if(n===m)throw Error("Generator is already running");if(n===g){if("throw"===i)throw l;return{value:e,done:!0}}for(a.method=i,a.arg=l;;){var o=a.delegate;if(o){var s=P(o,a);if(s){if(s===v)continue;return s}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(n===f)throw n=g,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);n=m;var c=h(t,r,a);if("normal"===c.type){if(n=a.done?g:p,c.arg===v)continue;return{value:c.arg,done:a.done}}"throw"===c.type&&(n=g,a.method="throw",a.arg=c.arg)}}}function P(t,r){var a=r.method,n=t.iterator[a];if(n===e)return r.delegate=null,"throw"===a&&t.iterator.return&&(r.method="return",r.arg=e,P(t,r),"throw"===r.method)||"return"!==a&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+a+"' method")),v;var i=h(n,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var l=i.arg;return l?l.done?(r[t.resultName]=l.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,v):l:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function j(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function C(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(j,this),this.reset(!0)}function D(t){if(t||""===t){var r=t[l];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function r(){for(;++n<t.length;)if(a.call(t,n))return r.value=t[n],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(u(t)+" is not iterable")}return _.prototype=w,n(L,"constructor",{value:w,configurable:!0}),n(w,"constructor",{value:_,configurable:!0}),_.displayName=c(w,s,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===_||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,c(e,s,"GeneratorFunction")),e.prototype=Object.create(L),e},t.awrap=function(e){return{__await:e}},O(A.prototype),c(A.prototype,o,(function(){return this})),t.AsyncIterator=A,t.async=function(e,r,a,n,i){void 0===i&&(i=Promise);var l=new A(d(e,r,a,n),i);return t.isGeneratorFunction(r)?l:l.next().then((function(e){return e.done?e.value:l.next()}))},O(L),c(L,s,"Generator"),c(L,l,(function(){return this})),c(L,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var a in t)r.push(a);return r.reverse(),function e(){for(;r.length;){var a=r.pop();if(a in t)return e.value=a,e.done=!1,e}return e.done=!0,e}},t.values=D,C.prototype={constructor:C,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(E),!t)for(var r in this)"t"===r.charAt(0)&&a.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(a,n){return o.type="throw",o.arg=t,r.next=a,n&&(r.method="next",r.arg=e),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var l=this.tryEntries[i],o=l.completion;if("root"===l.tryLoc)return n("end");if(l.tryLoc<=this.prev){var s=a.call(l,"catchLoc"),c=a.call(l,"finallyLoc");if(s&&c){if(this.prev<l.catchLoc)return n(l.catchLoc,!0);if(this.prev<l.finallyLoc)return n(l.finallyLoc)}else if(s){if(this.prev<l.catchLoc)return n(l.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<l.finallyLoc)return n(l.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var l=i?i.completion:{};return l.type=e,l.arg=t,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(l)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),E(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var a=r.completion;if("throw"===a.type){var n=a.arg;E(r)}return n}}throw Error("illegal catch attempt")},delegateYield:function(t,r,a){return this.delegate={iterator:D(t),resultName:r,nextLoc:a},"next"===this.method&&(this.arg=e),v}},t}function v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function b(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v(Object(r),!0).forEach((function(t){_(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function _(e,t,r){return(t=w(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function w(e){var t=S(e,"string");return"symbol"==u(t)?t:t+""}function S(e,t){if("object"!=u(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=u(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function k(e,t,r,a,n,i,l){try{var o=e[i](l),s=o.value}catch(e){return void r(e)}o.done?t(s):Promise.resolve(s).then(a,n)}function x(e){return function(){var t=this,r=arguments;return new Promise((function(a,n){var i=e.apply(t,r);function l(e){k(i,a,n,l,o,"next",e)}function o(e){k(i,a,n,l,o,"throw",e)}l(void 0)}))}}var L={name:"ShuReport",components:{PrintTicket:c["a"]},mixins:[s["a"]],data:function(){return{tableData:[],currentPage:1,pageSize:10,totalCount:0,isLoading:!1,searchFormSetting:{select_date:{clearable:!1,label:"取餐时间",type:"daterange",value:Object(i["y"])(1)},area_ids:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"配送区域",dataList:[]},l1_addr:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"一级地址",dataList:[]},l2_addr:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"二级地址",dataList:[]},l3_addr:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"三级地址",dataList:[]},l4_addr:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"四级地址",dataList:[]},l5_addr:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"五级地址",dataList:[]},take_meal_time:{type:"select",value:"",clearable:!0,label:"取餐餐段",dataList:l["a"]},name:{type:"input",value:"",label:"取餐人",placeholder:"请输入"},phone:{type:"input",value:"",label:"手机号码",placeholder:"请输入手机号码"},org_ids:{type:"organizationSelect",value:[this.$store.getters.organization],label:"消费点",clearable:!0,checkStrictly:!0,isLazy:!1,multiple:!0},is_visitor:{type:"checkbox",label:"",checkboxLabel:"只看游客",value:!1}},mergeOpts:{useKeyList:{id:["selection","l1_addr","l2_addr","l3_addr","l4_addr","l5_addr","user_name","phone","remark"]},mergeKeyList:["area_name"]},dialogPrintVisible:!1,selectOrderId:[],isSelectAll:!1,selectListIdCount:0}},created:function(){this.initLoad()},watch:{"searchFormSetting.org_ids.value":function(){this.getAddressAreaList()},"searchFormSetting.area_ids.value":function(){this.searchFormSetting.l1_addr.dataList=[],this.searchFormSetting.l1_addr.value=[],this.loadAddress(1,this.searchFormSetting.area_ids.value)},"searchFormSetting.l1_addr.value":function(){this.searchFormSetting.l2_addr.dataList=[],this.searchFormSetting.l2_addr.value="",this.searchFormSetting.l1_addr.value.length&&this.loadAddress(2)},"searchFormSetting.l2_addr.value":function(){this.searchFormSetting.l3_addr.dataList=[],this.searchFormSetting.l3_addr.value="",this.searchFormSetting.l2_addr.value.length&&this.loadAddress(3)},"searchFormSetting.l3_addr.value":function(){this.searchFormSetting.l4_addr.dataList=[],this.searchFormSetting.l4_addr.value="",this.searchFormSetting.l3_addr.value.length&&this.loadAddress(4)},"searchFormSetting.l4_addr.value":function(){this.searchFormSetting.l5_addr.dataList=[],this.searchFormSetting.l5_addr.value="",this.searchFormSetting.l4_addr.value.length&&this.loadAddress(5)}},mounted:function(){},methods:{initLoad:function(){this.getShuList(),this.getAddressAreaList(),this.loadAddress(1)},searchHandle:Object(i["d"])((function(e){e&&"search"===e&&(this.dialogPrintVisible=!1,this.currentPage=1,this.resetSelectAll(),this.getShuList())}),300),resetSearchHandle:function(){this.$refs.searchRef.resetForm(),this.searchHandle()},resetHandle:function(){this.resetSelectAll(),this.searchFormSetting.org_ids.value=[this.$store.getters.organization]},formatQueryParams:function(e){var t={};for(var r in e)""===e[r].value&&0===e[r].value.length||("select_date"!==r?t[r]=e[r].value:e[r].value.length>0&&(t.reservation_date_start=e[r].value[0],t.reservation_date_end=e[r].value[1]));return t},getShuList:function(){var e=this;return x(y().mark((function t(){var r;return y().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,e.$apis.apiBackgroundOrderOrderReservationGetDeliveryCollectByXPost(b(b({},e.formatQueryParams(e.searchFormSetting)),{},{page:e.currentPage,page_size:e.pageSize}));case 3:r=t.sent,e.isLoading=!1,0===r.code?(e.tableData=[],r.data.results.map((function(t,a){t.foods_st.map((function(n){e.tableData.push(b(b(b({id:r.data.other_info[a].order_payment_id},t),n),{},{index:a}))}))})),e.rowMergeArrs=Object(o["a"])(e.tableData,e.mergeOpts),e.totalCount=r.data.count):e.$message.error(r.msg);case 6:case"end":return t.stop()}}),t)})))()},objectSpanMethod:function(e){e.row;var t=e.column,r=e.rowIndex,a=e.columnIndex,n=Object.keys(this.mergeOpts.useKeyList),i=this.mergeOpts.useKeyList&&n.length;if(i)for(var l in this.mergeOpts.useKeyList)if(this.mergeOpts.useKeyList[l].includes(t.property))return Object(o["b"])(this.rowMergeArrs,t.property,r,a);if(this.mergeOpts.mergeKeyList&&this.mergeOpts.mergeKeyList.length&&this.mergeOpts.mergeKeyList.includes(t.property))return Object(o["b"])(this.rowMergeArrs,t.property,r,a)},handleSizeChange:function(e){var t=this;return x(y().mark((function r(){return y().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return t.pageSize=e,r.next=3,t.getShuList();case 3:t.changeTableSelection();case 4:case"end":return r.stop()}}),r)})))()},handleCurrentChange:function(e){var t=this;return x(y().mark((function r(){return y().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return t.currentPage=e,r.next=3,t.getShuList();case 3:t.changeTableSelection();case 4:case"end":return r.stop()}}),r)})))()},handleSelectionChange:function(e){},selectSelection:function(e){this.changeSelectSelection(e)},selectSelectionAll:function(e){this.changeSelectSelection(e)},changeSelectSelection:function(e){var t=this,r=e.map((function(e){return e.id}));this.tableData.forEach((function(e,a){if(r.includes(e.id)){var n;(n=t.selectOrderId).push.apply(n,d(r)),t.selectOrderId=d(new Set(t.selectOrderId))}else{var i=t.selectOrderId.indexOf(e.id);-1!==i&&t.selectOrderId.splice(i,1)}})),this.selectOrderId.length&&(this.isSelectAll=this.selectListIdCount===this.selectOrderId.length||0)},changeTableSelection:function(){var e=this;this.tableData.length&&this.tableData.forEach((function(t,r){e.selectOrderId.includes(t.id)&&e.$nextTick((function(){e.$refs.tableData.toggleRowSelection(t)}))}))},getAddressAreaList:function(){var e=this;return x(y().mark((function t(){var r;return y().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiAddressAddersAreaAllPost({used_org_ids:e.searchFormSetting.org_ids.value});case 2:r=t.sent,0===r.code?e.searchFormSetting.area_ids.dataList=[{name:"未命名区域",id:0}].concat(d(r.data)):e.$message.error(r.msg);case 4:case"end":return t.stop()}}),t)})))()},loadAddress:function(e,t){var r=this;return x(y().mark((function a(){var n,i;return y().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return n={page:1,page_size:99999,level:e-1,used_for_web:!0},2===e?n.parent_id=r.searchFormSetting.l1_addr.value:3===e?n.parent_id=r.searchFormSetting.l2_addr.value:4===e?n.parent_id=r.searchFormSetting.l3_addr.value:5===e&&(n.parent_id=r.searchFormSetting.l4_addr.value),t&&(n.area_id=t),a.next=5,r.$apis.apiAddressAddersCenterListPost(n);case 5:i=a.sent,0===i.code?1===e?r.searchFormSetting.l1_addr.dataList=i.data.results:2===e?r.searchFormSetting.l2_addr.dataList=i.data.results:3===e?r.searchFormSetting.l3_addr.dataList=i.data.results:4===e?r.searchFormSetting.l4_addr.dataList=i.data.results:5===e&&(r.searchFormSetting.l5_addr.dataList=i.data.results):r.$message.error(i.msg);case 7:case"end":return a.stop()}}),a)})))()},handleExport:function(){var e={type:"ShuReport",url:"apiBackgroundOrderOrderReservationGetDeliveryCollectByXPost",params:b(b({},this.formatQueryParams(this.searchFormSetting)),{},{is_export:!0})};this.exportHandle(e)},openDialog:function(){if(!this.selectOrderId.length)return this.$message.error("请先选择数据！");this.dialogPrintVisible=!0},selectAllChange:function(e){var t=this;return x(y().mark((function r(){var a;return y().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(t.selectOrderId=[],t.$refs.tableData.clearSelection(),!e){r.next=12;break}if(!Reflect.has(t.$parent,"getDeliveryCollectByXyIds")){r.next=10;break}return t.isLoading=!0,r.next=7,t.$parent.getDeliveryCollectByXyIds(b(b({},t.formatQueryParams(t.searchFormSetting)),{},{page:t.currentPage,page_size:t.pageSize}));case 7:a=r.sent,t.isLoading=!1,a&&a.length>0?(t.selectOrderId=Object(i["f"])(a),t.selectListIdCount=a.length,t.changeTableSelection()):t.isSelectAll=!1;case 10:r.next=14;break;case 12:t.$refs.tableData.clearSelection(),t.selectOrderId=[];case 14:case"end":return r.stop()}}),r)})))()},resetSelectAll:function(){this.isSelectAll=!1,this.selectOrderId=[],this.$refs.tableData.clearSelection()}}},O=L,A=(r("dddd"),r("2877")),F=Object(A["a"])(O,a,n,!1,null,null,null);t["default"]=F.exports},c9d9:function(e,t,r){"use strict";r.d(t,"a",(function(){return i})),r.d(t,"d",(function(){return l})),r.d(t,"b",(function(){return o})),r.d(t,"c",(function(){return s})),r.d(t,"e",(function(){return c})),r.d(t,"f",(function(){return u})),r.d(t,"g",(function(){return d}));var a=r("5a0c"),n=r("da92"),i=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],l=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],o={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},s=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],c=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],u=(a().subtract(7,"day").format("YYYY-MM-DD"),a().format("YYYY-MM-DD"),function(e){return e?"number"===typeof e?n["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:n["a"].divide(e,100).toFixed(2):"0.00"}),d=function(e){return n["a"].times(e,100)}},cc06:function(e,t,r){"use strict";function a(e,t){var r={},a=t.useKeyList&&Object.keys(t.useKeyList).length;return a&&Object.keys(t.useKeyList).forEach((function(a,i){t.useKeyList[a].forEach((function(t,i){r[t]={row:[],mergeNum:0,key:a},r=n(r,e,t,a)}))})),t.mergeKeyList&&t.mergeKeyList.forEach((function(t,a){r[t]={row:[],mergeNum:0},r=n(r,e,t)})),r}function n(e,t,r,a){return t.forEach((function(n,l){if(0===l)e[r].row.push(1),e[r].mergeNum=l;else{var o=a?n[a]===t[l-1][a]:!a,s=n[r]===t[l-1][r]&&o;if(s){var c=i(e[r].row);e[r].row[c]+=1,e[r].row.push(0),e[r].mergeNum=l}else e[r].row.push(1),e[r].mergeNum=l}})),e}function i(e){var t=e.length-1;while(t>0){if(e[t])break;t--}return t}function l(e,t,r,a){var n=e[t].row[r],i=n>0?1:0;return[n,i]}r.d(t,"a",(function(){return a})),r.d(t,"b",(function(){return l}))},dddd:function(e,t,r){"use strict";r("1f59")}}]);