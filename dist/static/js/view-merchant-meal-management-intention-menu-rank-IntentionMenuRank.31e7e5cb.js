(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-intention-menu-rank-IntentionMenuRank","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-meal-report-MealPackageRule","intention_menu_rank"],{"043f":function(e,t,n){"use strict";n("ce8f")},"58d2":function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"PrintSetting container-wrapper"},[t("refresh-tool",{on:{refreshPage:e.refreshHandle}}),t("search-form",{ref:"searchRef",attrs:{"form-setting":e.searchFormSetting,autoSearch:!1},on:{search:e.searchHandle}}),t("div",{staticClass:"table-wrapper"},[e._m(0),t("div",{staticClass:"table-content"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","header-row-class-name":"ps-table-header-row","default-sort":{prop:"count",order:"descending"}},on:{"sort-change":e.sortChange}},[t("el-table-column",{attrs:{prop:"count",label:"点赞数",sortable:"custom",align:"center"}}),t("el-table-column",{attrs:{prop:"food_name",label:"菜品名称",align:"center"}}),t("el-table-column",{attrs:{prop:"food_category",label:"菜品分类",align:"center"}})],1)],1),t("pagination",{attrs:{onPaginationChange:e.onPaginationChange,"current-page":e.currentPage,"page-size":e.pageSize,layout:"total, prev, pager, next,sizes, jumper",total:e.totalCount},on:{"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t},"update:pageSize":function(t){e.pageSize=t},"update:page-size":function(t){e.pageSize=t}}})],1)],1)},a=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[e._v("数据列表")])])}],i=n("ed08"),o=n("c9d9");function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,a=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function f(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{f({},"")}catch(e){f=function(e,t,n){return e[t]=n}}function d(e,t,n,r){var i=t&&t.prototype instanceof b?t:b,o=Object.create(i.prototype),u=new z(r||[]);return a(o,"_invoke",{value:j(e,n,u)}),o}function h(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var m="suspendedStart",p="suspendedYield",g="executing",y="completed",v={};function b(){}function w(){}function _(){}var k={};f(k,o,(function(){return this}));var x=Object.getPrototypeOf,L=x&&x(x(D([])));L&&L!==n&&r.call(L,o)&&(k=L);var S=_.prototype=b.prototype=Object.create(k);function P(e){["next","throw","return"].forEach((function(t){f(e,t,(function(e){return this._invoke(t,e)}))}))}function O(e,t){function n(a,i,o,c){var l=h(e[a],e,i);if("throw"!==l.type){var s=l.arg,f=s.value;return f&&"object"==u(f)&&r.call(f,"__await")?t.resolve(f.__await).then((function(e){n("next",e,o,c)}),(function(e){n("throw",e,o,c)})):t.resolve(f).then((function(e){s.value=e,o(s)}),(function(e){return n("throw",e,o,c)}))}c(l.arg)}var i;a(this,"_invoke",{value:function(e,r){function a(){return new t((function(t,a){n(e,r,t,a)}))}return i=i?i.then(a,a):a()}})}function j(t,n,r){var a=m;return function(i,o){if(a===g)throw Error("Generator is already running");if(a===y){if("throw"===i)throw o;return{value:e,done:!0}}for(r.method=i,r.arg=o;;){var u=r.delegate;if(u){var c=E(u,r);if(c){if(c===v)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===m)throw a=y,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=g;var l=h(t,n,r);if("normal"===l.type){if(a=r.done?y:p,l.arg===v)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(a=y,r.method="throw",r.arg=l.arg)}}}function E(t,n){var r=n.method,a=t.iterator[r];if(a===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,E(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var i=h(a,t.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,v;var o=i.arg;return o?o.done?(n[t.resultName]=o.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,v):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function F(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function z(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(F,this),this.reset(!0)}function D(t){if(t||""===t){var n=t[o];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,i=function n(){for(;++a<t.length;)if(r.call(t,a))return n.value=t[a],n.done=!1,n;return n.value=e,n.done=!0,n};return i.next=i}}throw new TypeError(u(t)+" is not iterable")}return w.prototype=_,a(S,"constructor",{value:_,configurable:!0}),a(_,"constructor",{value:w,configurable:!0}),w.displayName=f(_,s,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,f(e,s,"GeneratorFunction")),e.prototype=Object.create(S),e},t.awrap=function(e){return{__await:e}},P(O.prototype),f(O.prototype,l,(function(){return this})),t.AsyncIterator=O,t.async=function(e,n,r,a,i){void 0===i&&(i=Promise);var o=new O(d(e,n,r,a),i);return t.isGeneratorFunction(n)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},P(S),f(S,s,"Generator"),f(S,o,(function(){return this})),f(S,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=D,z.prototype={constructor:z,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(C),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function a(r,a){return u.type="throw",u.arg=t,n.next=r,a&&(n.method="next",n.arg=e),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],u=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var c=r.call(o,"catchLoc"),l=r.call(o,"finallyLoc");if(c&&l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),C(n),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var a=r.arg;C(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:D(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),v}},t}function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){f(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function f(e,t,n){return(t=d(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function d(e){var t=h(e,"string");return"symbol"==u(t)?t:t+""}function h(e,t){if("object"!=u(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=u(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function m(e,t,n,r,a,i,o){try{var u=e[i](o),c=u.value}catch(e){return void n(e)}u.done?t(c):Promise.resolve(c).then(r,a)}function p(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function o(e){m(i,r,a,o,u,"next",e)}function u(e){m(i,r,a,o,u,"throw",e)}o(void 0)}))}}function g(e){return w(e)||b(e)||v(e)||y()}function y(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function v(e,t){if(e){if("string"==typeof e)return _(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_(e,t):void 0}}function b(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function w(e){if(Array.isArray(e))return _(e)}function _(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var k={name:"IntentionMenuRank",components:{},props:{},data:function(){return{isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[],searchFormSetting:{organization_id:{type:"organizationSelect",value:"",label:"组织",listNameKey:"name",listValueKey:"id",dataList:[],multiple:!1,checkStrictly:!0,collapseTags:!1},meal_type:{type:"select",value:"all",label:"餐段",dataList:[{label:"全部",value:"all"}].concat(g(o["a"]))},use_date:{type:"date",value:Object(i["M"])(new Date,"{y}-{m}-{d}"),label:"日期"}},orderBy:"desc"}},created:function(){this.initLoad()},watch:{},mounted:function(){},methods:{initLoad:function(){this.getFoodIntentFoodList()},searchHandle:Object(i["d"])((function(e){e&&"search"===e&&(this.currentPage=1,this.getFoodIntentFoodList())}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.initLoad()},formatQueryParams:function(e){var t={};for(var n in e)""!==e[n].value&&("select_time"!==n?t[n]=e[n].value:e[n].value&&e[n].value.length>0&&(t.start_time=e[n].value[0],t.end_time=e[n].value[1]));return t},sortChange:function(e){this.orderBy="descending"===e.order?"desc":"asc",this.page=1,this.pageSize=10,this.getFoodIntentFoodList()},getFoodIntentFoodList:function(){var e=this;return p(c().mark((function t(){var n;return c().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.searchFormSetting.organization_id.value=e.$store.state.user.organization,e.searchFormSetting.organization_id.value){t.next=6;break}return e.tableData=[],e.totalCount=0,e.$message.error("请先选择组织"),t.abrupt("return");case 6:return e.isLoading=!0,t.next=9,e.$apis.apiBackgroundFoodIntentFoodListPost(s(s({},e.formatQueryParams(e.searchFormSetting)),{},{order_by:e.orderBy,page:e.currentPage,page_size:e.pageSize}));case 9:n=t.sent,e.isLoading=!1,0===n.code?(e.tableData=n.data.results,e.totalCount=n.data.count):e.$message.error(n.msg);case 12:case"end":return t.stop()}}),t)})))()},onPaginationChange:function(e){this.currentPage=e.current,this.pageSize=e.pageSize,this.getFoodIntentFoodList()}}},x=k,L=(n("043f"),n("2877")),S=Object(L["a"])(x,r,a,!1,null,"5bd189d0",null);t["default"]=S.exports},c9d9:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"d",(function(){return o})),n.d(t,"b",(function(){return u})),n.d(t,"c",(function(){return c})),n.d(t,"e",(function(){return l})),n.d(t,"f",(function(){return s})),n.d(t,"g",(function(){return f}));var r=n("5a0c"),a=n("da92"),i=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],o=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],u={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},c=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],l=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],s=(r().subtract(7,"day").format("YYYY-MM-DD"),r().format("YYYY-MM-DD"),function(e){return e?"number"===typeof e?a["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:a["a"].divide(e,100).toFixed(2):"0.00"}),f=function(e){return a["a"].times(e,100)}},ce8f:function(e,t,n){}}]);