(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-consumption-rules-coupon-AddDiscountCoupon"],{"0050":function(t,e,i){},1181:function(t,e,i){"use strict";i.r(e);var r=function(){var t=this,e=t._self._c;return e("div",{staticClass:"container-wrapper circular-bead add_active_recharge"},[e("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"activityForm",staticClass:"activity-form ps-small-box",attrs:{size:"small",model:t.activityForm,rules:t.rules,"label-width":"120px"}},[e("el-form-item",{attrs:{label:"活动名称",prop:"name"}},[e("el-input",{staticClass:"common-input ps-input",attrs:{placeholder:"请输入活动名称",disabled:t.isDisabled},model:{value:t.activityForm.name,callback:function(e){t.$set(t.activityForm,"name",e)},expression:"activityForm.name"}})],1),e("el-form-item",{attrs:{label:"适用分组",prop:"groups"}},[e("user-group-select",{staticClass:"search-item-w ps-input",attrs:{multiple:!0,disabled:t.isDisabled,placeholder:"请选择分组"},model:{value:t.activityForm.groups,callback:function(e){t.$set(t.activityForm,"groups",e)},expression:"activityForm.groups"}})],1),e("el-form-item",{staticClass:"is-required",attrs:{label:"活动时间"}},[e("div",[e("el-radio-group",{staticClass:"ps-radio",attrs:{disabled:t.isDisabled},model:{value:t.activityForm.dateRange,callback:function(e){t.$set(t.activityForm,"dateRange",e)},expression:"activityForm.dateRange"}},[e("el-radio",{attrs:{label:0}},[t._v("永久")]),e("el-radio",{attrs:{label:1}},[t._v("时间段")])],1)],1),0==t.activityForm.dateRange?e("div",{staticClass:"time-item"},[e("el-form-item",{key:0,attrs:{label:"",prop:"startTime"}},[e("span",[t._v("开始时间：")]),e("el-date-picker",{attrs:{type:"datetime",disabled:t.isDisabled,format:"yyyy-MM-dd HH:mm:ss","value-format":"yyyy-MM-dd HH:mm:ss","picker-options":t.pickerOptions,placeholder:"开始日期"},model:{value:t.activityForm.startTime,callback:function(e){t.$set(t.activityForm,"startTime",e)},expression:"activityForm.startTime"}})],1)],1):t._e(),1==t.activityForm.dateRange?e("div",{staticClass:"time-item"},[e("el-form-item",{key:1,attrs:{label:"",prop:"selectTime"}},[e("el-date-picker",{staticClass:"common-input",attrs:{disabled:t.isDisabled,type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"left",format:"yyyy-MM-dd HH:mm:ss","value-format":"yyyy-MM-dd HH:mm:ss","picker-options":t.pickerOptions,"default-time":t.defaultTime},model:{value:t.activityForm.selectTime,callback:function(e){t.$set(t.activityForm,"selectTime",e)},expression:"activityForm.selectTime"}})],1)],1):t._e()]),e("el-form-item",{staticClass:"is-required",attrs:{label:"活动规则"}},[e("el-form-item",{attrs:{label:""}},[e("el-radio-group",{staticClass:"ps-radio",attrs:{disabled:t.isDisabled,prop:"couponType"},on:{change:t.changeCouponHandle},model:{value:t.activityForm.couponType,callback:function(e){t.$set(t.activityForm,"couponType",e)},expression:"activityForm.couponType"}},[e("el-radio",{attrs:{label:"RG"}},[t._v("充值赠送")])],1)],1),t.showRandom?e("el-form-item",{attrs:{label:""}}):t._e(),e("div",{staticClass:"coupon-type-box clearfix"},[0==t.activityForm.subtractType?e("div",{staticClass:"coupon-type-item"},[e("transition-group",{attrs:{name:"el-zoom-in-top",tag:"div"}},t._l(t.activityForm.gradList,(function(i,r){return e("div",{key:"k"+r,staticClass:"grad"},[e("span",{staticClass:"label-l max-w"},[t._v("单笔充值满")]),e("el-form-item",{staticClass:"inline-item",attrs:{rules:t.rules.maxRange,label:"",prop:"gradList."+r+".full"}},[e("el-input",{staticClass:"coupon-type-input ps-input",attrs:{disabled:t.isDisabled},model:{value:i.full,callback:function(e){t.$set(i,"full",e)},expression:"item.full"}})],1),e("span",{staticClass:"label-l label-r"},[t._v(t._s("RG"==t.activityForm.couponType?"送":"减"))]),e("el-form-item",{staticClass:"inline-item",attrs:{label:"",rules:t.rules.minRange,prop:"gradList."+r+".reduce"}},[e("el-input",{staticClass:"coupon-type-input ps-input",attrs:{disabled:t.isDisabled},model:{value:i.reduce,callback:function(e){t.$set(i,"reduce",e)},expression:"item.reduce"}})],1),e("span",{staticClass:"label-r"},[t._v("元")]),t.activityForm.gradList.length>1?e("el-button",{staticClass:"grad-danger",attrs:{disabled:t.isDisabled,type:"text"},on:{click:function(e){return t.delGradHandle(r)}}},[e("i",{staticClass:"el-icon-delete grad-danger"})]):t._e()],1)})),0),e("div",{staticStyle:{"margin-top":"5px","padding-left":"75px"}},[e("el-button",{attrs:{type:"text"},on:{click:t.addGradHandle}},[t._v("添加》")])],1)],1):t._e(),1==t.activityForm.subtractType?e("div",{staticClass:"coupon-type-item"},[e("span",{staticClass:"label-l max-r-w"},[t._v("随机"+t._s("RG"==t.activityForm.couponType?"赠送":"立减")+"范围")]),e("el-form-item",{staticClass:"inline-item",attrs:{label:"",prop:"minRange"}},[e("el-input",{staticClass:"coupon-type-input ps-input",attrs:{disabled:t.isDisabled},model:{value:t.activityForm.minRange,callback:function(e){t.$set(t.activityForm,"minRange",e)},expression:"activityForm.minRange"}})],1),e("span",{staticClass:"label-l label-r"},[t._v("至")]),e("el-form-item",{staticClass:"inline-item",attrs:{label:"",prop:"maxRange"}},[e("el-input",{staticClass:"coupon-type-input ps-input",attrs:{disabled:t.isDisabled},model:{value:t.activityForm.maxRange,callback:function(e){t.$set(t.activityForm,"maxRange",e)},expression:"activityForm.maxRange"}})],1),e("span",{staticClass:"label-r"},[t._v("元")]),e("div",{staticClass:"limit-box"},[e("span",{staticClass:"label-l max-r-w"},[t._v("生效额度")]),e("el-form-item",{staticClass:"inline-item",staticStyle:{width:"280px"},attrs:{label:"",prop:"checkPrice"}},[e("el-input",{staticClass:"ps-input",attrs:{disabled:t.isDisabled},model:{value:t.activityForm.checkPrice,callback:function(e){t.$set(t.activityForm,"checkPrice",e)},expression:"activityForm.checkPrice"}})],1),e("span",{staticClass:"label-r"},[t._v("元")])],1)],1):t._e(),e("div",{staticClass:"coupon-type-box-tips"},["RG"==t.activityForm.couponType?e("div",[e("p",{staticClass:"tips-title"},[t._v("提示：")]),e("p",[t._v("1、后一条规则需要比前一个规则金额大，如第一个规则为“满10增送200”，则第二个规则需满（>10）赠送（>200）。")])]):t._e(),"RS"==t.activityForm.couponType?e("div",[e("p",{staticClass:"tips-title"},[t._v("提示：")]),e("p",[t._v("1、后一条规则需比前一条规则金额大，如第一个规则为“满20减10元”，则下一条规则需满（>20）减（>10）。")]),e("p",[t._v("2、立减金额需小于支付金额，如满10减9；不允许满10减11。")])]):t._e()])])],1),e("el-form-item",{staticClass:"is-required",attrs:{label:"活动上限",prop:"amountlimit"}},[e("div",[e("el-radio",{staticClass:"ps-radio",attrs:{disabled:"modify"==t.type&&"enable"==t.modifyData.status,label:0},model:{value:t.activityForm.isAmountlimit,callback:function(e){t.$set(t.activityForm,"isAmountlimit",e)},expression:"activityForm.isAmountlimit"}},[t._v("无上限")])],1),e("div",[e("el-radio",{staticClass:"ps-radio",attrs:{disabled:"modify"==t.type&&"enable"==t.modifyData.status,label:1},model:{value:t.activityForm.isAmountlimit,callback:function(e){t.$set(t.activityForm,"isAmountlimit",e)},expression:"activityForm.isAmountlimit"}},[t._v("合计"+t._s("RG"==t.activityForm.couponType?"赠送":"立减")+"金额超过")]),e("el-input",{staticClass:"ps-input",staticStyle:{width:"180px"},attrs:{disabled:0==t.activityForm.isAmountlimit||"enable"==t.modifyData.status},model:{value:t.activityForm.amountlimit,callback:function(e){t.$set(t.activityForm,"amountlimit",e)},expression:"activityForm.amountlimit"}}),e("span",{staticClass:"label-r"},[t._v("元停止活动")])],1)]),e("el-form-item",{attrs:{label:"规则说明"}},[e("el-input",{staticClass:"common-input ps-input",attrs:{type:"textarea",rows:"5",disabled:"modify"==t.type&&"enable"==t.modifyData.status},model:{value:t.activityForm.couponTip,callback:function(e){t.$set(t.activityForm,"couponTip",e)},expression:"activityForm.couponTip"}})],1),e("el-form-item",{attrs:{label:"活动状态",prop:"status"}},[e("el-radio-group",{staticClass:"ps-radio",model:{value:t.activityForm.status,callback:function(e){t.$set(t.activityForm,"status",e)},expression:"activityForm.status"}},[e("el-radio",{attrs:{disabled:t.disabledStatus,label:"enable"}},[t._v("启用")]),e("el-radio",{attrs:{disabled:t.disabledStatus,label:"disable"}},[t._v("停用")])],1)],1),e("el-form-item",[e("el-button",{staticClass:"activity-input ps-origin-btn",attrs:{type:"primary",disabled:t.disabledStatus},on:{click:t.submitActivityHandle}},[t._v(t._s("add"==t.type?"创建活动":"修改活动"))]),e("el-button",{staticClass:"activity-input",on:{click:function(e){return t.$router.go(-1)}}},[t._v("取消")])],1)],1)],1)},a=[],n=i("2f62"),o=i("ed08"),s=i("390a");function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function l(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */l=function(){return e};var t,e={},i=Object.prototype,r=i.hasOwnProperty,a=Object.defineProperty||function(t,e,i){t[e]=i.value},n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",s=n.asyncIterator||"@@asyncIterator",u=n.toStringTag||"@@toStringTag";function m(t,e,i){return Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{m({},"")}catch(t){m=function(t,e,i){return t[e]=i}}function p(t,e,i,r){var n=e&&e.prototype instanceof h?e:h,o=Object.create(n.prototype),s=new E(r||[]);return a(o,"_invoke",{value:L(t,i,s)}),o}function d(t,e,i){try{return{type:"normal",arg:t.call(e,i)}}catch(t){return{type:"throw",arg:t}}}e.wrap=p;var y="suspendedStart",v="suspendedYield",f="executing",b="completed",g={};function h(){}function F(){}function w(){}var _={};m(_,o,(function(){return this}));var x=Object.getPrototypeOf,k=x&&x(x(A([])));k&&k!==i&&r.call(k,o)&&(_=k);var T=w.prototype=h.prototype=Object.create(_);function R(t){["next","throw","return"].forEach((function(e){m(t,e,(function(t){return this._invoke(e,t)}))}))}function O(t,e){function i(a,n,o,s){var l=d(t[a],t,n);if("throw"!==l.type){var u=l.arg,m=u.value;return m&&"object"==c(m)&&r.call(m,"__await")?e.resolve(m.__await).then((function(t){i("next",t,o,s)}),(function(t){i("throw",t,o,s)})):e.resolve(m).then((function(t){u.value=t,o(u)}),(function(t){return i("throw",t,o,s)}))}s(l.arg)}var n;a(this,"_invoke",{value:function(t,r){function a(){return new e((function(e,a){i(t,r,e,a)}))}return n=n?n.then(a,a):a()}})}function L(e,i,r){var a=y;return function(n,o){if(a===f)throw Error("Generator is already running");if(a===b){if("throw"===n)throw o;return{value:t,done:!0}}for(r.method=n,r.arg=o;;){var s=r.delegate;if(s){var c=C(s,r);if(c){if(c===g)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===y)throw a=b,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=f;var l=d(e,i,r);if("normal"===l.type){if(a=r.done?b:v,l.arg===g)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(a=b,r.method="throw",r.arg=l.arg)}}}function C(e,i){var r=i.method,a=e.iterator[r];if(a===t)return i.delegate=null,"throw"===r&&e.iterator.return&&(i.method="return",i.arg=t,C(e,i),"throw"===i.method)||"return"!==r&&(i.method="throw",i.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var n=d(a,e.iterator,i.arg);if("throw"===n.type)return i.method="throw",i.arg=n.arg,i.delegate=null,g;var o=n.arg;return o?o.done?(i[e.resultName]=o.value,i.next=e.nextLoc,"return"!==i.method&&(i.method="next",i.arg=t),i.delegate=null,g):o:(i.method="throw",i.arg=new TypeError("iterator result is not an object"),i.delegate=null,g)}function j(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function $(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(j,this),this.reset(!0)}function A(e){if(e||""===e){var i=e[o];if(i)return i.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,n=function i(){for(;++a<e.length;)if(r.call(e,a))return i.value=e[a],i.done=!1,i;return i.value=t,i.done=!0,i};return n.next=n}}throw new TypeError(c(e)+" is not iterable")}return F.prototype=w,a(T,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:F,configurable:!0}),F.displayName=m(w,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===F||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,m(t,u,"GeneratorFunction")),t.prototype=Object.create(T),t},e.awrap=function(t){return{__await:t}},R(O.prototype),m(O.prototype,s,(function(){return this})),e.AsyncIterator=O,e.async=function(t,i,r,a,n){void 0===n&&(n=Promise);var o=new O(p(t,i,r,a),n);return e.isGeneratorFunction(i)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},R(T),m(T,u,"Generator"),m(T,o,(function(){return this})),m(T,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),i=[];for(var r in e)i.push(r);return i.reverse(),function t(){for(;i.length;){var r=i.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=A,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach($),!e)for(var i in this)"t"===i.charAt(0)&&r.call(this,i)&&!isNaN(+i.slice(1))&&(this[i]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var i=this;function a(r,a){return s.type="throw",s.arg=e,i.next=r,a&&(i.method="next",i.arg=t),!!a}for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n],s=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var c=r.call(o,"catchLoc"),l=r.call(o,"finallyLoc");if(c&&l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var n=a;break}}n&&("break"===t||"continue"===t)&&n.tryLoc<=e&&e<=n.finallyLoc&&(n=null);var o=n?n.completion:{};return o.type=t,o.arg=e,n?(this.method="next",this.next=n.finallyLoc,g):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var i=this.tryEntries[e];if(i.finallyLoc===t)return this.complete(i.completion,i.afterLoc),$(i),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var i=this.tryEntries[e];if(i.tryLoc===t){var r=i.completion;if("throw"===r.type){var a=r.arg;$(i)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,i,r){return this.delegate={iterator:A(e),resultName:i,nextLoc:r},"next"===this.method&&(this.arg=t),g}},e}function u(t,e){return v(t)||y(t,e)||p(t,e)||m()}function m(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function p(t,e){if(t){if("string"==typeof t)return d(t,e);var i={}.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?d(t,e):void 0}}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,r=Array(e);i<e;i++)r[i]=t[i];return r}function y(t,e){var i=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=i){var r,a,n,o,s=[],c=!0,l=!1;try{if(n=(i=i.call(t)).next,0===e){if(Object(i)!==i)return;c=!1}else for(;!(c=(r=n.call(i)).done)&&(s.push(r.value),s.length!==e);c=!0);}catch(t){l=!0,a=t}finally{try{if(!c&&null!=i.return&&(o=i.return(),Object(o)!==o))return}finally{if(l)throw a}}return s}}function v(t){if(Array.isArray(t))return t}function f(t,e,i,r,a,n,o){try{var s=t[n](o),c=s.value}catch(t){return void i(t)}s.done?e(c):Promise.resolve(c).then(r,a)}function b(t){return function(){var e=this,i=arguments;return new Promise((function(r,a){var n=t.apply(e,i);function o(t){f(n,r,a,o,s,"next",t)}function s(t){f(n,r,a,o,s,"throw",t)}o(void 0)}))}}function g(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,r)}return i}function h(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?g(Object(i),!0).forEach((function(e){F(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):g(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function F(t,e,i){return(e=w(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function w(t){var e=_(t,"string");return"symbol"==c(e)?e:e+""}function _(t,e){if("object"!=c(t)||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var r=i.call(t,e||"default");if("object"!=c(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var x={components:{UserGroupSelect:s["a"]},data:function(){var t=this,e=function(e,i,r){if(1===t.activityForm.dateRange)if(i&&i.length){var a=i[0].replace(new RegExp(/-/gm),"/"),n=i[1].replace(new RegExp(/-/gm),"/");new Date(a).getTime()>=new Date(n).getTime()?r(new Error("活动开始时间要小于结束时间！")):r()}else r(new Error("请选择活动时间！"));else r()},i=function(t,e,i){var r=/^([0-9]+[\d]*(.[0-9]{1,2})?)$/;r.test(e)?i():i(new Error("金额格式错误！"))},r=function(e,i,r){var a=/^([0-9]+[\d]*(.[0-9]{1,2})?)$/,n=e.field.split(".");if("full"===n[2]&&"0"===i)return r(new Error("活动金额不能为0！"));if(a.test(i)){var o=Number(t.activityForm.gradList[Number(n[1])].full),s=Number(t.activityForm.gradList[Number(n[1])].reduce),c={full:"",reduce:""},l={full:"",reduce:""};if(n[1]>0&&(c.full=t.activityForm.gradList[Number(n[1])-1].full,c.reduce=t.activityForm.gradList[Number(n[1])-1].reduce),Number(n[1])<t.activityForm.gradList.length-1&&(l.full=t.activityForm.gradList[Number(n[1])+1].full,l.reduce=t.activityForm.gradList[Number(n[1])+1].reduce),"RS"===t.activityForm.couponType)if(o&&s){if(s>=o)return"full"===n[2]?r(new Error("金额要大于优惠金额！")):r(new Error("金额要小于活动金额！"));switch(n[2]){case"full":t.$refs.activityForm.clearValidate("".concat(n[0],".").concat(n[1],".reduce"));break;case"reduce":t.$refs.activityForm.clearValidate("".concat(n[0],".").concat(n[1],".full"));break}r()}else r();else{var u=!0,m="";switch(n[2]){case"full":a.test(c.full)&&Number(i)<=Number(c.full)&&(u=!1,m="当前规则金额必须大于上一条规则金额");break;case"reduce":a.test(c.reduce)&&Number(i)<=Number(c.reduce)&&(u=!1,m="当前优惠金额必须大于上一条规则的优惠金额");break}if(!u)return r(new Error(m));r()}}else r(new Error("金额格式错误！"))},a=function(t,e,i){var r=/^([0-9]+[\d]*(.[0-9]{1,2})?)$/;r.test(e)?i():i(new Error("金额格式错误！"))},n=function(e,i,r){var a=/^([0-9]+[\d]*(.[0-9]{1,2})?)$/;a.test(i)||1!==t.activityForm.isAmountlimit?r():r(new Error("金额格式错误！"))};return{type:"add",isLoading:!1,activityForm:{id:"",name:"",groups:[],couponType:"RG",subtractType:0,couponTip:"",dateRange:0,selectTime:[],startTime:"",endTime:"",checkPrice:"",fullMoney:"",reduceMoney:"",maxRange:"",minRange:"",fee:"",gradList:[{full:"",reduce:""}],isAmountlimit:0,amountlimit:"",status:"enable"},disabledStatus:!1,showRandom:!0,rules:{name:[{required:!0,message:"请填写活动名称",trigger:"blur"}],groups:[{required:!0,message:"请选择适用分组",trigger:"blur"}],status:[{required:!0,message:"请设置活动状态",trigger:"change"}],startTime:[{required:!0,message:"请选择活动开始时间",trigger:"change"}],selectTime:[{validator:e,trigger:"change"}],checkPrice:[{validator:i,trigger:"blur"}],reduceMoney:[{validator:i,trigger:"blur"}],amountlimit:[{validator:n,trigger:"blur"}],maxRange:[{validator:r,trigger:"blur"}],minRange:[{validator:r,trigger:"blur"}],gradRange:[{validator:a,trigger:"blur"}]},pickerOptions:{disabledDate:function(t){var e=new Date,i=new Date("".concat(e.getFullYear(),"/").concat(e.getMonth()+1,"/").concat(e.getDate()));return t.getTime()<i.getTime()}},defaultTime:["00:00:00","23:59:59"],modifyData:{}}},computed:{isDisabled:function(){var t=!0;return"add"===this.type&&(t=!1),"expire"===this.activityForm.status&&(t=!0),t}},created:function(){this.$route.params.type&&(this.type=this.$route.params.type,"modify"===this.type&&(this.activityForm.id=this.$route.query.id,this.getActivityRechargeDetail()))},methods:h(h({},Object(n["b"])({_createActivityRecharge:"createActivityRecharge",_modifyActivityRecharge:"modifyActivityRecharge"})),{},{changeSubtractType:function(t){},getActivityRechargeDetail:function(t){var e=this;return b(l().mark((function t(){var i,r,a,n,s;return l().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,i={coupon_no:e.activityForm.id},t.next=4,Object(o["Z"])(e.$apis.apiBackgroundMarketingRechargeDetailsPost(i));case 4:if(r=t.sent,a=u(r,2),n=a[0],s=a[1],e.isLoading=!1,!n){t.next=12;break}return e.$message.error(n.message),t.abrupt("return");case 12:0===s.code?e.initEditData(s.data):e.$message.error(s.msg);case 13:case"end":return t.stop()}}),t)})))()},initEditData:function(t){this.modifyData=t,this.activityForm.id=t.coupon_no,this.activityForm.name=t.name,this.activityForm.groups=Object.values(t.user_groups_name),t.start_time&&t.end_time?(this.activityForm.dateRange=1,this.activityForm.selectTime=[t.start_time.replace("/T/ig"," "),t.end_time.replace("/T/ig"," ")]):(this.activityForm.dateRange=0,this.activityForm.startTime=t.start_time.replace("/T/ig"," ")),this.activityForm.couponTip=t.tip,this.activityForm.isAmountlimit=t.limit_type,this.activityForm.amountlimit=Object(o["i"])(t.upper_limit);var e=[];switch(t.discount&&t.discount.length&&t.discount.map((function(t){e.push({full:Object(o["i"])(t.limit_fee),reduce:Object(o["i"])(t.coupon_fee)})})),this.activityForm.gradList=e,this.activityForm.status="enable"===t.status?"enable":"disable",t.status){case"enable":this.activityForm.status="enable";break;case"disable":this.activityForm.status="disable";break;case"expire":this.activityForm.status="disable",this.disabledStatus=!0;break;default:break}},changeCouponHandle:function(){},submitActivityHandle:function(){var t=this;this.$refs.activityForm.validate((function(e){if(e){if(1===t.activityForm.subtractType&&Number(t.activityForm.minRange)>Number(t.activityForm.maxRange))return void t.$message.error("随机".concat("RG"===t.activityForm.couponType?"赠送":"立减","范围应该从小到大！"));"add"===t.type?t.addActivityRechargeHandle():t.modifyActivityRechargeHandle()}}))},checkActivityRule:function(){var t=this,e=!0,i="";return this.activityForm.gradList.forEach((function(r,a){a>0&&e&&(Number(r.full)<=Number(t.activityForm.gradList[a-1].full)&&(i="后一条活动的金额必须大于上一条活动的金额！",e=!1),e&&Number(r.reduce)<=Number(t.activityForm.gradList[a-1].reduce)&&(i="后一条活动规则的优惠金额必须大于上一条活动的优惠金额！",e=!1))})),e||this.$message.error(i),e},addActivityRechargeHandle:function(){var t=this;return b(l().mark((function e(){var i,r,a,n,s;return l().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.checkActivityRule()){e.next=2;break}return e.abrupt("return");case 2:return i={user_group_nos:t.activityForm.groups,name:t.activityForm.name,coupon_type:t.activityForm.couponType,date_range:t.activityForm.dateRange,start_time:1===t.activityForm.dateRange?t.activityForm.selectTime[0]:t.activityForm.startTime,limit_type:t.activityForm.isAmountlimit,upper_limit:Object(o["Y"])(t.activityForm.amountlimit),status:t.activityForm.status},1===t.activityForm.dateRange&&(i.end_time=t.activityForm.selectTime[1]),t.activityForm.couponTip&&(i.tip=t.activityForm.couponTip),1===t.activityForm.subtractType?(i.fee=JSON.stringify({type:t.activityForm.subtractType,rules:[Number(Object(o["Y"])(t.activityForm.minRange)),Number(Object(o["Y"])(t.activityForm.maxRange))]}),i.check_price=Number(Object(o["Y"])(t.activityForm.checkPrice))):i.discount=t.activityForm.gradList.map((function(t){return{limit_fee:Object(o["Y"])(t.full),coupon_fee:Object(o["Y"])(t.reduce)}})),t.isLoading=!0,e.next=9,Object(o["Z"])(t.$apis.apiBackgroundMarketingRechargeAddPost(i));case 9:if(r=e.sent,a=u(r,2),n=a[0],s=a[1],t.isLoading=!1,!n){e.next=17;break}return t.$message.error(n.message),e.abrupt("return");case 17:0===s.code?(t.$message.success("创建活动成功！"),t.$closeCurrentTab(t.$route.path)):t.$message.error(s.msg);case 18:case"end":return e.stop()}}),e)})))()},modifyActivityRechargeHandle:function(){var t=this;return b(l().mark((function e(){var i,r,a,n,s;return l().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.checkActivityRule()){e.next=2;break}return e.abrupt("return");case 2:return i={coupon_no:t.activityForm.id,user_group_nos:t.activityForm.groups,name:t.activityForm.name,coupon_type:t.activityForm.couponType,date_range:t.activityForm.dateRange,start_time:1===t.activityForm.dateRange?t.activityForm.selectTime[0]:t.activityForm.startTime,limit_type:t.activityForm.isAmountlimit,upper_limit:Object(o["Y"])(t.activityForm.amountlimit),status:t.activityForm.status},1===t.activityForm.dateRange&&(i.end_time=t.activityForm.selectTime[1]),t.activityForm.couponTip&&(i.tip=t.activityForm.couponTip),1===t.activityForm.subtractType?(i.fee=JSON.stringify({type:t.activityForm.subtractType,rules:[Number(Object(o["Y"])(t.activityForm.minRange)),Number(Object(o["Y"])(t.activityForm.maxRange))]}),i.check_price=Number(Object(o["Y"])(t.activityForm.checkPrice))):i.discount=t.activityForm.gradList.map((function(t){return{limit_fee:Object(o["Y"])(t.full),coupon_fee:Object(o["Y"])(t.reduce)}})),t.isLoading=!0,e.next=9,Object(o["Z"])(t.$apis.apiBackgroundMarketingRechargeModifyPost(i));case 9:if(r=e.sent,a=u(r,2),n=a[0],s=a[1],t.isLoading=!1,!n){e.next=17;break}return t.$message.error(n.message),e.abrupt("return");case 17:0===s.code?(t.$message.success("修改活动成功！"),t.$closeCurrentTab(t.$route.path)):t.$message.error(s.msg);case 18:case"end":return e.stop()}}),e)})))()},addGradHandle:function(){this.activityForm.gradList.push({full:"",reduce:""})},delGradHandle:function(t){this.activityForm.gradList.splice(t,1)}})},k=x,T=(i("1fd7"),i("2877")),R=Object(T["a"])(k,r,a,!1,null,null,null);e["default"]=R.exports},"1fd7":function(t,e,i){"use strict";i("0050")}}]);