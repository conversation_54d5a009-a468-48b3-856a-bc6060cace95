(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-parameter-config-auto-label-AutoLabelDialog"],{8507:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("dialog-message",{attrs:{show:t.visible,title:t.title,loading:t.isLoading,customClass:"ps-auto-dialog",width:t.width},on:{"update:show":function(e){t.visible=e},"update:loading":function(e){t.isLoading=e},close:t.handleClose}},[e("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formData",staticClass:"dialog-form",attrs:{model:t.formData,"status-icon":"",rules:t.formRules,"label-width":"100px"},nativeOn:{submit:function(t){t.preventDefault()}}},[e("el-form-item",{attrs:{label:"标签名称",prop:"label_name"}},[e("div",{staticClass:"w-auto"},[e("el-input",{attrs:{maxlength:10},model:{value:t.formData.label_name,callback:function(e){t.$set(t.formData,"label_name",e)},expression:"formData.label_name"}})],1)]),e("el-form-item",{attrs:{label:"选择元素",prop:"nutrition_key"}},[e("el-select",{staticClass:"ps-select w-auto",attrs:{placeholder:"请选择",filterable:"","popper-class":"ps-popper-select",disabled:"modify"===t.type},model:{value:t.formData.nutrition_key,callback:function(e){t.$set(t.formData,"nutrition_key",e)},expression:"formData.nutrition_key"}},t._l(t.nutrition_key,(function(t,r){return e("el-option",{key:r,attrs:{label:t.name,value:t.key}})})),1)],1),e("div",{staticClass:"ps-flex no-item-bottom"},[e("el-form-item",{attrs:{label:"标签规则",prop:"comparison"}},[e("el-select",{staticClass:"ps-select",staticStyle:{width:"140px"},attrs:{placeholder:"请选择",filterable:"","popper-class":"ps-popper-select"},model:{value:t.formData.comparison,callback:function(e){t.$set(t.formData,"comparison",e)},expression:"formData.comparison"}},t._l(t.comparisonList,(function(t,r){return e("el-option",{key:r,attrs:{label:t.label,value:t.value}})})),1)],1),t.formData.comparison?e("div",{staticClass:"ps-flex flex-align-c"},[e("el-form-item",{staticClass:"p-l-10 p-r-10",attrs:{label:"",prop:"operator_number_one","label-width":"0"}},[e("el-input",{staticClass:"ps-input",staticStyle:{width:"95px"},model:{value:t.formData.operator_number_one,callback:function(e){t.$set(t.formData,"operator_number_one",e)},expression:"formData.operator_number_one"}})],1),"between"===t.formData.comparison?e("div",{staticClass:"ps-flex flex-align-c"},[e("div",[t._v("~")]),e("el-form-item",{attrs:{label:"",prop:"operator_number_two","label-width":"0"}},[e("el-input",{staticClass:"ps-input m-l-10",staticStyle:{width:"95px"},model:{value:t.formData.operator_number_two,callback:function(e){t.$set(t.formData,"operator_number_two",e)},expression:"formData.operator_number_two"}})],1)],1):t._e()],1):t._e()],1),e("el-form-item",{attrs:{label:"",prop:""}},[t._v(" 每100g含量 ")])],1),e("template",{slot:"tool"},[e("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"60px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:t.isLoading},on:{click:t.clickCancleHandle}},[t._v(" 取消 ")]),e("el-button",{staticClass:"ps-btn",attrs:{disabled:t.isLoading,type:"primary"},on:{click:t.clickConfirmHandle}},[t._v(" 保存 ")])],1)])],2)},o=[],a=r("5a50"),i=r("ed08");function l(t){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},l(t)}function s(t,e){return m(t)||p(t,e)||c(t,e)||u()}function u(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function c(t,e){if(t){if("string"==typeof t)return f(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?f(t,e):void 0}}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function p(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,l=[],s=!0,u=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(l.push(n.value),l.length!==e);s=!0);}catch(t){u=!0,o=t}finally{try{if(!s&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw o}}return l}}function m(t){if(Array.isArray(t))return t}function h(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */h=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var a=e&&e.prototype instanceof v?e:v,i=Object.create(a.prototype),l=new N(n||[]);return o(i,"_invoke",{value:S(t,r,l)}),i}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var m="suspendedStart",d="suspendedYield",y="executing",b="completed",g={};function v(){}function w(){}function _(){}var x={};c(x,i,(function(){return this}));var L=Object.getPrototypeOf,k=L&&L(L(A([])));k&&k!==r&&n.call(k,i)&&(x=k);var D=_.prototype=v.prototype=Object.create(x);function E(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function O(t,e){function r(o,a,i,s){var u=p(t[o],t,a);if("throw"!==u.type){var c=u.arg,f=c.value;return f&&"object"==l(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,i,s)}),(function(t){r("throw",t,i,s)})):e.resolve(f).then((function(t){c.value=t,i(c)}),(function(t){return r("throw",t,i,s)}))}s(u.arg)}var a;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return a=a?a.then(o,o):o()}})}function S(e,r,n){var o=m;return function(a,i){if(o===y)throw Error("Generator is already running");if(o===b){if("throw"===a)throw i;return{value:t,done:!0}}for(n.method=a,n.arg=i;;){var l=n.delegate;if(l){var s=j(l,n);if(s){if(s===g)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===m)throw o=b,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=y;var u=p(e,r,n);if("normal"===u.type){if(o=n.done?b:d,u.arg===g)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(o=b,n.method="throw",n.arg=u.arg)}}}function j(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,j(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var a=p(o,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,g;var i=a.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,g):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function C(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function $(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function N(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(C,this),this.reset(!0)}function A(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(l(e)+" is not iterable")}return w.prototype=_,o(D,"constructor",{value:_,configurable:!0}),o(_,"constructor",{value:w,configurable:!0}),w.displayName=c(_,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,c(t,u,"GeneratorFunction")),t.prototype=Object.create(D),t},e.awrap=function(t){return{__await:t}},E(O.prototype),c(O.prototype,s,(function(){return this})),e.AsyncIterator=O,e.async=function(t,r,n,o,a){void 0===a&&(a=Promise);var i=new O(f(t,r,n,o),a);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},E(D),c(D,u,"Generator"),c(D,i,(function(){return this})),c(D,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=A,N.prototype={constructor:N,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach($),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return l.type="throw",l.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],l=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var s=n.call(i,"catchLoc"),u=n.call(i,"finallyLoc");if(s&&u){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=t,i.arg=e,a?(this.method="next",this.next=a.finallyLoc,g):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),$(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;$(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:A(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),g}},e}function d(t,e,r,n,o,a,i){try{var l=t[a](i),s=l.value}catch(t){return void r(t)}l.done?e(s):Promise.resolve(s).then(n,o)}function y(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var a=t.apply(e,r);function i(t){d(a,n,o,i,l,"next",t)}function l(t){d(a,n,o,i,l,"throw",t)}i(void 0)}))}}var b={name:"trayDialog",components:{},props:{loading:Boolean,isshow:Boolean,type:{type:String,default:""},title:{type:String,default:""},width:{type:String,default:"600px"},infoData:{type:Object,default:function(){return{}}},confirm:Function},data:function(){var t=this,e=function(e,r,n){!t.reg.test(r)||Number(r)>1e4?n(new Error(" ")):n()},r=function(e,r,n){!t.reg.test(r)||Number(r)>1e4||Number(r)<Number(t.formData.operator_number_one)?n(new Error(" ")):n()};return{reg:/^(?!0+(?:\.0+)?$)(?:[1-9]\d*|0)(?:\.\d{1,2})?$/,isLoading:!1,formData:{label_name:"",comparison:"",nutrition_key:"",operator_number_one:"",operator_number_two:""},formRules:{label_name:[{required:!0,message:"请选择输入名称",trigger:["blur","change"]}],nutrition_key:[{required:!0,message:" ",trigger:["blur","change"]}],comparison:[{required:!0,message:" ",trigger:["blur","change"]}],operator_number_one:[{required:!0,validator:e,message:" ",trigger:["blur","change"]}],operator_number_two:[{required:!0,validator:r,message:" ",trigger:["blur","change"]}]},foodlabelList:[],nutrition_key:Object(i["f"])(a["a"]),comparisonList:[{label:"大于",value:">"},{label:"大于等于",value:">="},{label:"小于",value:"<"},{label:"小于等于",value:"<="},{label:"区间",value:"between"}]}},computed:{visible:{get:function(){return this.isshow},set:function(t){this.$emit("update:isshow",t)}}},watch:{},created:function(){var t=this;return y(h().mark((function e(){return h().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.initLoad();case 1:case"end":return e.stop()}}),e)})))()},mounted:function(){},methods:{paySettingNormalizer:function(t){if(t)return{id:t.id,label:t.name,children:t.label_list}},getLabelGroupList:function(){var t=this;return y(h().mark((function e(){var r,n,o,a;return h().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(i["Z"])(t.$apis.apiBackgroundHealthyLabelGroupListPost({type:"food",page:1,page_size:999999}));case 3:if(r=e.sent,n=s(r,2),o=n[0],a=n[1],t.isLoading=!1,!o){e.next=11;break}return t.$message.error(o.message),e.abrupt("return");case 11:0===a.code?t.foodlabelList=a.data.results:t.$message.error(a.msg);case 12:case"end":return e.stop()}}),e)})))()},initLoad:function(){this.formData={label_name:"modify"===this.type?this.infoData.label_name:"",comparison:"modify"===this.type?this.infoData.comparison:"",nutrition_key:"modify"===this.type?this.infoData.nutrition_key:"",operator_number_one:"modify"===this.type?this.infoData.operator_number_one:"",operator_number_two:"modify"===this.type?this.infoData.operator_number_two:""}},clickConfirmHandle:function(){var t=this;this.$refs.formData.validate(function(){var e=y(h().mark((function e(r){var n,o,a,l,u,c,f,p,m;return h().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!r){e.next=28;break}if(n={label_name:t.formData.label_name,comparison:t.formData.comparison,nutrition_key:t.formData.nutrition_key,operator_number_one:Number(t.formData.operator_number_one),operator_number_two:Number(t.formData.operator_number_two)},t.isLoading=!0,o="",a=s(o,2),l=a[0],u=a[1],"add"!==t.type){e.next=14;break}return e.next=8,Object(i["Z"])(t.$apis.apiBackgroundHealthyAdminAutoLabelAddPost(n));case 8:c=e.sent,f=s(c,2),l=f[0],u=f[1],e.next=21;break;case 14:return n.id=t.infoData.id,e.next=17,Object(i["Z"])(t.$apis.apiBackgroundHealthyAdminAutoLabelModifyPost(n));case 17:p=e.sent,m=s(p,2),l=m[0],u=m[1];case 21:if(t.isLoading=!1,!l){e.next=25;break}return t.$message.error(l.message),e.abrupt("return");case 25:0===u.code?(t.visible=!1,t.$emit("confirm","search"),t.$message.success(u.msg)):t.$message.error(u.msg),e.next=29;break;case 28:t.$message.error("参数格式错误，请认真检查！");case 29:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},clickCancleHandle:function(){this.visible=!1},handleClose:function(t){this.isLoading=!1,this.visible=!1}}},g=b,v=(r("d507"),r("2877")),w=Object(v["a"])(g,n,o,!1,null,"c5d86592",null);e["default"]=w.exports},d507:function(t,e,r){"use strict";r("e698")},e698:function(t,e,r){}}]);