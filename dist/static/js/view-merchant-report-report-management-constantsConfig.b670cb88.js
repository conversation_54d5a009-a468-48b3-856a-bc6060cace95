(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-report-report-management-constantsConfig","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-meal-report-MealPackageRule"],{"20e4":function(e,a,l){"use strict";l.r(a),l.d(a,"recentSevenDay",(function(){return i})),l.d(a,"FoodSaleRanking",(function(){return u})),l.d(a,"SummaryOfSales",(function(){return d})),l.d(a,"TABLE_HEAD_DATA_DISHES_TAKEN",(function(){return r})),l.d(a,"SEARCH_FORM_SET_DATA_DISHES_TAKEN",(function(){return m}));var t=l("5a0c"),n=l("c9d9"),i=[t().subtract(7,"day").format("YYYY-MM-DD"),t().format("YYYY-MM-DD")],u={select_time:{type:"daterange",label:"就餐时间",value:i,format:"yyyy-MM-dd",clearable:!1},meal_type_list:{type:"select",label:"餐段",value:[],multiple:!0,placeholder:"请选择",collapseTags:!0,dataList:n["a"]},org_ids:{type:"organizationSelect",value:[],label:"消费点",listNameKey:"name",listValueKey:"id",dataList:[],checkStrictly:!0,multiple:!0,collapseTags:!0,clearable:!0},food_name:{type:"input",value:"",label:"菜品名称",maxlength:20,placeholder:"请输入菜品名称"}},d={select_time:{type:"daterange",label:"就餐时间",value:i,format:"yyyy-MM-dd",clearable:!1},meal_type_list:{type:"select",label:"餐段",value:[],multiple:!0,placeholder:"请选择",collapseTags:!0,dataList:n["a"]},org_ids:{type:"organizationSelect",value:[],label:"消费点",listNameKey:"name",listValueKey:"id",dataList:[],checkStrictly:!0,multiple:!0,collapseTags:!0,clearable:!0}},r=[{label:"所属组织",key:"org_name"},{label:"菜品一级分类",key:"sort"},{label:"菜品二级分类",key:"category"},{label:"菜品名称",key:"name"},{label:"取用量",key:"food_weight",type:"slot",slotName:"foodWeight"},{label:"取菜人数",key:"use_count"},{label:"平均取用量",key:"average_weight",type:"slot",slotName:"averageWeight"}],m={select_time:{type:"daterange",label:"就餐时间",value:[],format:"yyyy-MM-dd",clearable:!1},food_name:{type:"input",value:"",label:"菜品名称",maxlength:20,placeholder:"请输入菜品名称"},org_ids:{type:"organizationSelect",value:[],label:"所属组织",listNameKey:"name",listValueKey:"id",dataList:[],checkStrictly:!0,multiple:!0,collapseTags:!0,clearable:!0},meal_type:{type:"select",label:"餐段",value:[],multiple:!0,placeholder:"请选择",collapseTags:!0,dataList:n["a"]}}},c9d9:function(e,a,l){"use strict";l.d(a,"a",(function(){return i})),l.d(a,"d",(function(){return u})),l.d(a,"b",(function(){return d})),l.d(a,"c",(function(){return r})),l.d(a,"e",(function(){return m})),l.d(a,"f",(function(){return o})),l.d(a,"g",(function(){return y}));var t=l("5a0c"),n=l("da92"),i=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],u=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],d={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},r=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],m=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],o=(t().subtract(7,"day").format("YYYY-MM-DD"),t().format("YYYY-MM-DD"),function(e){return e?"number"===typeof e?n["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:n["a"].divide(e,100).toFixed(2):"0.00"}),y=function(e){return n["a"].times(e,100)}}}]);