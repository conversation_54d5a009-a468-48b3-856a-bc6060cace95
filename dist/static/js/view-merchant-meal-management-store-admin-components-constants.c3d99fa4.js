(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-store-admin-components-constants","view-merchant-meal-management-store-admin-goodsStockDetails"],{a94d:function(e,l,a){"use strict";a.r(l),a.d(l,"STORE_GOODS_ADMIN_INFO",(function(){return s})),a.d(l,"STORE_STOCK",(function(){return p})),a.d(l,"CATEGORY_STOCK",(function(){return i})),a.d(l,"CATEGORY_STATISTICS",(function(){return c})),a.d(l,"GOODS_STATISTICS",(function(){return o})),a.d(l,"ADD_STOCK_DETAILS",(function(){return u})),a.d(l,"DEDUCT_STOCK_DETAILS",(function(){return y}));var t=a("ed08"),s={goods_category_ids:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"分类",dataList:[]},barcode:{type:"input",label:"条码",value:"",placeholder:"请输入条码"},name:{type:"input",label:"商品名称",value:"",placeholder:"请输入商品名称"},sale_status:{type:"select",label:"上下架",value:"",placeholder:"请选择类型",dataList:[{label:"全部",value:"2"},{label:"上架",value:"1"},{label:"下架",value:"0"}]},goods_unit:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"单位",dataList:[]},other:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"type",label:"其他",dataList:[{name:"一品多码商品",type:"is_multi_barcode"},{name:"多规格商品",type:"is_multi_spec"}]}},p={goods_category_ids:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"分类",dataList:[]},barcode:{type:"input",label:"条码",value:"",placeholder:"请输入条码"},name:{type:"input",label:"商品名称",value:"",placeholder:"请输入商品名称"},goods_unit:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"单位",dataList:[]}},i={goods_category_ids:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"分类",dataList:[]}},c={select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"创建时间",value:Object(t["t"])(-7),clearable:!1},goods_category_ids:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"分类",dataList:[]}},o={select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"创建时间",value:Object(t["t"])(-7),clearable:!1},goods_category_ids:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"分类",dataList:[]},barcode:{type:"input",label:"条码",value:"",placeholder:"请输入条码"},name:{type:"input",label:"商品名称",value:"",placeholder:"请输入商品名称"}},u={select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"入库时间",value:Object(t["t"])(-7),clearable:!1},goods_category_ids:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"分类",dataList:[]},barcode:{type:"input",label:"条码",value:"",placeholder:"请输入条码"},name:{type:"input",label:"商品名称",value:"",placeholder:"请输入商品名称"},operator_name:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"},operate_type:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"type",label:"入库类型",dataList:[{name:"操作入库",type:"operate"},{name:"退款入库",type:"refund"},{name:"失败订单入库",type:"order_fail"}]},trade_no:{type:"input",label:"订单号",value:"",placeholder:"请输入订单号"}},y={select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"出库时间",value:Object(t["t"])(-7),clearable:!1},goods_category_ids:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",label:"分类",dataList:[]},barcode:{type:"input",label:"条码",value:"",placeholder:"请输入条码"},name:{type:"input",label:"商品名称",value:"",placeholder:"请输入商品名称"},operator_name:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"},operate_type:{type:"select",value:[],multiple:!0,clearable:!0,collapseTags:!0,listNameKey:"name",listValueKey:"type",label:"出库原因",dataList:[{name:"销售出库",type:"sale"},{name:"盘点出库",type:"check"},{name:"保质期出库",type:"allot"},{name:"破损出库",type:"breakage"},{name:"其他",type:"other"}]},trade_no:{type:"input",label:"订单号",value:"",placeholder:"请输入订单号"}}}}]);