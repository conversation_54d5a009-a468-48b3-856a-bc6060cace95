(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-merchant-admin-components-msgSetting"],{"2db0":function(t,e,r){"use strict";r("ea4a")},9497:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"container-wrapper msgSetting"},[e("el-radio-group",{staticClass:"ps-radio-btn m-t-20",attrs:{size:"mini",prop:"couponType"},model:{value:t.senderType,callback:function(e){t.senderType=e},expression:"senderType"}},t._l(t.templateList,(function(r){return e("el-radio-button",{key:r.sender_type,attrs:{label:r.sender_type}},[t._v(t._s(r.sender_type_alias))])})),1),t.dataLoading?e("div",{staticClass:"m-b-20 m-t-20"},[e("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:t.formData[t.senderType].common.enable,callback:function(e){t.$set(t.formData[t.senderType].common,"enable",e)},expression:"formData[senderType].common.enable"}})],1):t._e(),t.dataLoading?e("el-form",{ref:"msgSetting",attrs:{rules:t.formDataRuls,model:t.formData,size:"small"}},[e("el-form-item",{attrs:{label:"适用组织",prop:"orgIds","label-width":"70px"}},[e("organization-select",{staticClass:"ps-input w-250",attrs:{placeholder:"请选择适用组织",isLazy:!1,multiple:!0,"check-strictly":!0,"append-to-body":!0,company:t.organizationData.company,role:"super"},model:{value:t.formData[t.senderType].organizations,callback:function(e){t.$set(t.formData[t.senderType],"organizations",e)},expression:"formData[senderType].organizations"}})],1),t._l(t.formSettingList,(function(r){return["common"===r.setting_type?e("div",{key:r.setting_type,staticClass:"l-title"},[t._v(t._s(r.setting_type_alias))]):e("div",{key:r.setting_type,staticClass:"checkbox-title"},[e("el-checkbox",{staticClass:"ps-checkbox m-r-10",model:{value:t.formData[t.senderType][r.setting_type].enable,callback:function(e){t.$set(t.formData[t.senderType][r.setting_type],"enable",e)},expression:"formData[senderType][tempItem.setting_type].enable"}}),t._v(" "+t._s(r.setting_type_alias)+" ")],1),t._l(r.template,(function(n){return[e("el-form-item",{key:n.key+r.setting_type,staticClass:"m-l-25",attrs:{prop:n.key,label:n.name}},[n.type&&"input"!==n.type?t._e():e("el-input",{staticClass:"ps-input w-250",attrs:{size:"small",type:n.type},model:{value:t.formData[t.senderType][r.setting_type][n.key],callback:function(e){t.$set(t.formData[t.senderType][r.setting_type],n.key,e)},expression:"formData[senderType][tempItem.setting_type][item.key]"}}),"textarea"===n.type?e("el-input",{staticClass:"ps-input w-250",attrs:{size:"small",type:"textarea",rows:3},model:{value:t.formData[t.senderType][r.setting_type][n.key],callback:function(e){t.$set(t.formData[t.senderType][r.setting_type],n.key,e)},expression:"formData[senderType][tempItem.setting_type][item.key]"}}):t._e(),"select"===n.type?e("el-select",{ref:"forRef",refInFor:!0,staticClass:"search-item-w ps-input w-250",attrs:{size:"small",placeholder:""},model:{value:t.formData[t.senderType][r.setting_type][n.key],callback:function(e){t.$set(t.formData[t.senderType][r.setting_type],n.key,e)},expression:"formData[senderType][tempItem.setting_type][item.key]"}},t._l(n.value,(function(t){return e("el-option",{key:t.value,attrs:{label:t.name,value:t.value}})})),1):t._e(),"radio"===n.type?e("el-radio-group",{staticClass:"ps-radio",model:{value:t.formData[t.senderType][r.setting_type][n.key],callback:function(e){t.$set(t.formData[t.senderType][r.setting_type],n.key,e)},expression:"formData[senderType][tempItem.setting_type][item.key]"}},t._l(n.value,(function(r){return e("el-radio",{key:r.value,attrs:{label:r.value}},[t._v(t._s(r.name))])})),1):t._e(),"checkbox"===n.type?e("el-checkbox-group",{model:{value:t.formData[t.senderType][r.setting_type][n.key],callback:function(e){t.$set(t.formData[t.senderType][r.setting_type],n.key,e)},expression:"formData[senderType][tempItem.setting_type][item.key]"}},t._l(n.value,(function(r){return e("el-checkbox",{key:r.value,staticClass:"ps-checkbox",attrs:{label:r.value}},[t._v(t._s(r.name))])})),1):t._e(),"number"===n.type?e("el-input-number",{staticClass:"ps-input-number",attrs:{"step-strictly":"",label:""},model:{value:t.formData[t.senderType][r.setting_type][n.key],callback:function(e){t.$set(t.formData[t.senderType][r.setting_type],n.key,e)},expression:"formData[senderType][tempItem.setting_type][item.key]"}}):t._e(),n.help_text?e("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:n.help_text,placement:"top-start"}},[e("i",{staticClass:"el-icon-info"})]):t._e()],1)]}))]}))],2):t._e(),e("div",{staticClass:"add-wrapper"},[e("el-button",{staticClass:"ps-origin-btn",attrs:{type:"primary",size:"small"},on:{click:t.saveSettingHandle}},[t._v("保存")])],1)],1)},a=[],o=r("ed08"),i=r("cbfb"),s=r("bbd5");function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function l(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */l=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function p(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{p({},"")}catch(t){p=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var o=e&&e.prototype instanceof b?e:b,i=Object.create(o.prototype),s=new z(n||[]);return a(i,"_invoke",{value:O(t,r,s)}),i}function y(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var m="suspendedStart",d="suspendedYield",g="executing",h="completed",v={};function b(){}function _(){}function w(){}var k={};p(k,i,(function(){return this}));var x=Object.getPrototypeOf,L=x&&x(x(C([])));L&&L!==r&&n.call(L,i)&&(k=L);var T=w.prototype=b.prototype=Object.create(k);function D(t){["next","throw","return"].forEach((function(e){p(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function r(a,o,i,s){var l=y(t[a],t,o);if("throw"!==l.type){var u=l.arg,p=u.value;return p&&"object"==c(p)&&n.call(p,"__await")?e.resolve(p.__await).then((function(t){r("next",t,i,s)}),(function(t){r("throw",t,i,s)})):e.resolve(p).then((function(t){u.value=t,i(u)}),(function(t){return r("throw",t,i,s)}))}s(l.arg)}var o;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return o=o?o.then(a,a):a()}})}function O(e,r,n){var a=m;return function(o,i){if(a===g)throw Error("Generator is already running");if(a===h){if("throw"===o)throw i;return{value:t,done:!0}}for(n.method=o,n.arg=i;;){var s=n.delegate;if(s){var c=$(s,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===m)throw a=h,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=g;var l=y(e,r,n);if("normal"===l.type){if(a=n.done?h:d,l.arg===v)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(a=h,n.method="throw",n.arg=l.arg)}}}function $(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,$(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var o=y(a,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,v;var i=o.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function z(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function C(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,o=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(c(e)+" is not iterable")}return _.prototype=w,a(T,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:_,configurable:!0}),_.displayName=p(w,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===_||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,p(t,u,"GeneratorFunction")),t.prototype=Object.create(T),t},e.awrap=function(t){return{__await:t}},D(S.prototype),p(S.prototype,s,(function(){return this})),e.AsyncIterator=S,e.async=function(t,r,n,a,o){void 0===o&&(o=Promise);var i=new S(f(t,r,n,a),o);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},D(T),p(T,u,"Generator"),p(T,i,(function(){return this})),p(T,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=C,z.prototype={constructor:z,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return s.type="throw",s.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],s=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var c=n.call(i,"catchLoc"),l=n.call(i,"finallyLoc");if(c&&l){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),j(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;j(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:C(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function u(t,e){return d(t)||m(t,e)||f(t,e)||p()}function p(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(t,e){if(t){if("string"==typeof t)return y(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?y(t,e):void 0}}function y(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function m(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,o,i,s=[],c=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(s.push(n.value),s.length!==e);c=!0);}catch(t){l=!0,a=t}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(l)throw a}}return s}}function d(t){if(Array.isArray(t))return t}function g(t,e,r,n,a,o,i){try{var s=t[o](i),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,a)}function h(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var o=t.apply(e,r);function i(t){g(o,n,a,i,s,"next",t)}function s(t){g(o,n,a,i,s,"throw",t)}i(void 0)}))}}var v={name:"SuperMsgSetting",components:{OrganizationSelect:i["a"]},props:{type:String,infoData:{type:Object,default:function(){return{}}},organizationData:Object,restoreHandle:Function},data:function(){return{isLoading:!1,dataLoading:!1,templateList:[],settingList:[],senderType:"abc",formData:{},formDataRuls:{}}},created:function(){},mounted:function(){this.initLoad()},computed:{formSettingList:function(){var t=this,e=this.templateList.filter((function(e){return e.sender_type===t.senderType}));return e.length?e[0].settings_template:[]}},methods:{initLoad:function(){this.getMessagestemplateList()},refreshHandle:function(){this.initLoad()},getMessagestemplateList:function(){var t=this;return h(l().mark((function e(){var r,n,a,o;return l().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.$to(t.$apis.apiBackgroundAdminThirdMessagesSettingsTemplateListPost());case 3:if(r=e.sent,n=u(r,2),a=n[0],o=n[1],t.isLoading=!1,!a){e.next=11;break}return t.$message.error(a.message),e.abrupt("return");case 11:0===o.code&&(t.templateList=o.data,t.templateList.length&&(t.senderType=t.templateList[0].sender_type),t.getMessagesSettingsList());case 12:case"end":return e.stop()}}),e)})))()},getMessagesSettingsList:function(){var t=this;return h(l().mark((function e(){var r,n,a,o,i;return l().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,r={company:t.organizationData.company},e.next=4,t.$to(t.$apis.apiBackgroundAdminThirdMessagesSettingsListPost(r));case 4:if(n=e.sent,a=u(n,2),o=a[0],i=a[1],t.isLoading=!1,!o){e.next=12;break}return t.$message.error(o.message),e.abrupt("return");case 12:0===i.code&&(t.settingList=i.data,t.loadFormData());case 13:case"end":return e.stop()}}),e)})))()},loadFormData:function(){var t=this;this.templateList.length&&(this.templateList.map((function(e){var r=t.settingList.filter((function(t){return null!==t.id&&t.sender_type===e.sender_type}));t.$set(t.formData,e.sender_type,{}),t.$set(t.formData[e.sender_type],"organizations",r.length?r[0].organizations:[]),e.settings_template.map((function(n){var a;if(t.$set(t.formData[e.sender_type],n.setting_type,{}),n.template){var o=null!==(a=r[0])&&void 0!==a&&null!==(a=a.event_msg_config)&&void 0!==a&&a[n.setting_type]?r[0].event_msg_config[n.setting_type]:{};n.template.map((function(r){if("checkbox"===r.type){var a=JSON.parse(r.default).map((function(t){return String(t)}));t.$set(t.formData[e.sender_type][n.setting_type],r.key,o[r.key]?o[r.key]:a)}else t.$set(t.formData[e.sender_type][n.setting_type],r.key,o[r.key]?o[r.key]:r.default)})),t.$set(t.formData[e.sender_type][n.setting_type],"enable",!!o.enable&&o.enable)}}))})),this.dataLoading=!0)},saveSettingHandle:function(){var t=this;return h(l().mark((function e(){return l().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.isLoading){e.next=2;break}return e.abrupt("return");case 2:t.$refs.msgSetting.validate((function(e){e&&t.modifySetting()}));case 3:case"end":return e.stop()}}),e)})))()},modifySetting:function(){var t=this;return h(l().mark((function e(){var r,n,a,i,c,p;return l().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=Object(s["a"])(t.formData[t.senderType]),delete r.organizations,n={sender_type:t.senderType,organizations:t.formData[t.senderType].organizations,event_msg_config:r,company_id:t.organizationData.company},t.isLoading=!0,e.next=6,Object(o["Z"])(t.$apis.apiBackgroundAdminThirdMessagesSettingsModifyPost(n));case 6:if(a=e.sent,i=u(a,2),c=i[0],p=i[1],t.isLoading=!1,!c){e.next=14;break}return t.$message.error(c.message),e.abrupt("return");case 14:0===p.code?(t.$message.success("修改成功"),t.formOperate="detail",t.restoreHandle(t.type,t.formOperate)):t.$message.error(p.msg);case 15:case"end":return e.stop()}}),e)})))()}}},b=v,_=(r("2db0"),r("2877")),w=Object(_["a"])(b,n,a,!1,null,null,null);e["default"]=w.exports},bbd5:function(t,e,r){"use strict";function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}r.d(e,"a",(function(){return a}));var a=function t(e){if(!e&&"object"!==n(e))throw new Error("error arguments","deepClone");var r=e.constructor===Array?[]:{};return Object.keys(e).forEach((function(a){e[a]&&"object"===n(e[a])?r[a]=t(e[a]):r[a]=e[a]})),r}},ea4a:function(t,e,r){t.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}}}]);