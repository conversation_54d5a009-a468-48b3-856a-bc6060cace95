(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-booking-setting-SetMealSummary","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-booking-setting-constantsAndConfig","view-merchant-meal-management-meal-report-MealPackageRule"],{a563:function(e,l,a){"use strict";a.r(l),a.d(l,"recentSevenDay",(function(){return p})),a.d(l,"searchFormSetting",(function(){return s})),a.d(l,"columns",(function(){return b})),a.d(l,"menuManagerFormSetting",(function(){return y})),a.d(l,"deviceList",(function(){return v})),a.d(l,"dateTypes",(function(){return _})),a.d(l,"CUPBOARDORDER",(function(){return f}));var t=a("c9d9"),n=a("5a0c");function i(e){return d(e)||o(e)||r(e)||u()}function u(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function r(e,l){if(e){if("string"==typeof e)return m(e,l);var a={}.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?m(e,l):void 0}}function o(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function d(e){if(Array.isArray(e))return m(e)}function m(e,l){(null==l||l>e.length)&&(l=e.length);for(var a=0,t=Array(l);a<l;a++)t[a]=e[a];return t}var c=[{label:"全部",value:""},{label:"线上",value:"online"},{label:"线下",value:"offline"}],p=[n().subtract(7,"day").format("YYYY-MM-DD"),n().format("YYYY-MM-DD")],s={user_group_id:{type:"select",label:"分组",value:null,placeholder:"请选择分组",dataList:[],listNameKey:"group_name",listValueKey:"id",multiple:!1,collapseTags:!0},date_type:{type:"select",value:"create_time",label:"时间",dataList:[{label:"创建时间",value:"create_time"},{label:"预约时间",value:"reservation_date"},{label:"用餐时间",value:"dining_time"}]},select_time:{clearable:!1,type:"daterange",value:p},meal_type_list:{type:"select",label:"餐段",value:[],multiple:!0,placeholder:"请选择",collapseTags:!0,dataList:t["a"]},take_meal_status:{type:"select",value:"",label:"取餐状态",dataList:[{value:"take_out",label:"已取餐"},{value:"no_take",label:"未取餐"},{value:"cancel",label:"已取消"},{value:"time_out",label:"已过期"}]},take_meal_type:{type:"select",value:"",label:"取餐方式",dataList:[{value:"on_scene",label:"堂食"},{value:"waimai",label:"外卖"},{value:"bale",label:"堂食自提"},{value:"cupboard",label:"取餐柜"}]},name:{type:"input",value:"",label:"姓名",placeholder:"请输入姓名"},person_no:{type:"input",value:"",label:"人员编号",placeholder:"请输入人员编号"},consume:{type:"organizationSelect",value:[],label:"消费点",listNameKey:"name",listValueKey:"id",dataList:[],multiple:!0,checkStrictly:!0,collapseTags:!0},consume_type:{type:"select",value:null,label:"支付方式",dataList:c},unified_out_trade_no:{type:"input",value:"",label:"总单号",placeholder:"请输入总单号"},out_trade_no:{type:"input",value:"",label:"订单号",placeholder:"请输入订单号"},payer_department_group_ids:{type:"departmentSelect",multiple:!0,flat:!1,label:"部门",value:[],placeholder:"请选择部门",checkStrictly:!0,dataList:[],limit:1,level:1},food_name:{type:"input",value:"",label:"菜品",placeholder:"请输入菜品"}},b=[{label:"总单号",column:"unified_trade_no",width:"160px"},{label:"订单号",column:"trade_no",width:"160px"},{label:"分组",column:"payer_group_name"},{label:"姓名",column:"name"},{label:"人员编号",column:"person_no"},{label:"手机号",column:"phone",width:"110px"},{label:"部门",column:"payer_department_group_name",width:"100px"},{label:"创建时间",column:"create_time",width:"160px"},{label:"预约时间",column:"reservation_date",width:"160px"},{label:"用餐时间",column:"dining_time",width:"160px"},{label:"预约餐段",column:"meal_type_alias"},{label:"取餐方式",column:"take_meal_type_alias"},{label:"扣费方式",column:"consume_type"},{label:"菜品",column:"create_date6"},{label:"份数",column:"count"},{label:"订单金额",column:"origin_fee"},{label:"取餐状态",column:"take_meal_status_alias"},{label:"预约消费点",column:"consumption_name",width:"120px"}],y={name:{type:"input",value:"",label:"菜谱名称",placeholder:"请输入菜谱名称"},account_name:{type:"input",value:"",label:"创建人",placeholder:"请输入"},apply_groups:{type:"select",value:[],label:"适用人群",placeholder:"请选择",listNameKey:"name",listValueKey:"id",multiple:!0,collapseTags:!0,dataList:[]},use_user_groups:{type:"select",label:"可见范围",value:null,placeholder:"请选择",dataList:[],listNameKey:"group_name",listValueKey:"id",multiple:!1,collapseTags:!0},organization:{type:"treeselect",label:"创建组织",multiple:!0,flat:!1,value:null,placeholder:"请选择创建组织",dataList:[],listValueKey:"id",limit:1,level:1,normalizer:function(e){return{id:e.id,label:e.name,children:e.children_list}}},use_organization:{type:"treeselect",label:"适用组织",multiple:!0,flat:!1,value:[],placeholder:"请选择",dataList:[],listValueKey:"id",limit:1,level:1,normalizer:function(e){return{id:e.id,label:e.name,children:e.children_list}}}},v=[{label:"全部",value:""},{label:"H5",value:"H5"},{label:"小程序",value:"MAPP"},{label:"K1点餐机",value:"K1DCJ"},{label:"双屏点餐机",value:"SPDCJ"},{label:"结算台",value:"JST"},{label:"P2手持消费机",value:"P2XFJ"},{label:"D2消费机",value:"D2XFJ"},{label:"智能秤",value:"ZNC"}],_=[{label:"创建时间",value:"create_time"},{label:"预约时间",value:"reservation_date"},{label:"用餐时间",value:"dining_time"}],f={date_type:{type:"select",value:"create_time",label:"",dataList:[{label:"创建时间",value:"create_time"},{label:"预约时间",value:"reservation_date"},{label:"用餐时间",value:"dining_time"}]},select_time:{clearable:!1,type:"daterange",value:p},trade_no:{type:"input",value:"",label:"总订单号/订单号",placeholder:"请输入总订单号或者订单号",labelWidth:"150px"},name:{type:"input",value:"",label:"姓名",placeholder:""},phone:{type:"input",value:"",label:"手机号",placeholder:""},person_no:{type:"input",value:"",label:"人员编号",placeholder:""},payer_group_ids:{type:"groupSelect",label:"分组",value:[],placeholder:"请选择分组",multiple:!0,collapseTags:!0},meal_type:{type:"select",value:"",label:"预约餐段",listNameKey:"label",listValueKey:"value",multiple:!1,collapseTags:!0,filterable:!0,dataList:[{value:"",label:"全部"}].concat(i(t["a"]))},is_save:{type:"select",value:"2",label:"是否已存餐",dataList:[{label:"全部",value:"2"},{label:"未存餐",value:"0"},{label:"已存餐",value:"1"}]},organization_id:{type:"organizationSelect",value:"",label:"消费点",listNameKey:"name",listValueKey:"id",dataList:[],checkStrictly:!0,multiple:!1,collapseTags:!0,clearable:!0},take_meal_status_list:{type:"select",value:[],label:"取餐状态",listNameKey:"label",listValueKey:"value",multiple:!0,collapseTags:!0,filterable:!0,dataList:[{value:"take_out",label:"已取餐"},{value:"no_take",label:"未取餐"},{value:"time_out",label:"已过期"}]},device_ids:{type:"select",value:[],label:"餐柜名称",listNameKey:"device_name",listValueKey:"device_id",multiple:!0,collapseTags:!0,filterable:!0,dataList:[]}}},c9d9:function(e,l,a){"use strict";a.d(l,"a",(function(){return i})),a.d(l,"d",(function(){return u})),a.d(l,"b",(function(){return r})),a.d(l,"c",(function(){return o})),a.d(l,"e",(function(){return d})),a.d(l,"f",(function(){return m})),a.d(l,"g",(function(){return c}));var t=a("5a0c"),n=a("da92"),i=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],u=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],r={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},o=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],d=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],m=(t().subtract(7,"day").format("YYYY-MM-DD"),t().format("YYYY-MM-DD"),function(e){return e?"number"===typeof e?n["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:n["a"].divide(e,100).toFixed(2):"0.00"}),c=function(e){return n["a"].times(e,100)}}}]);