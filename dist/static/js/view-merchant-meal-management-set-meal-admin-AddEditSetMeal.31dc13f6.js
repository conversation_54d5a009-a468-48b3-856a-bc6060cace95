(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-set-meal-admin-AddEditSetMeal","view-merchant-consumption-rules-service-admin-AddChargeServiceRule","view-merchant-meal-management-components-mealFoodList-FoodDiscountDialog","view-merchant-meal-management-set-meal-admin-components-SetMealDialog"],{"4b35":function(t,e,r){"use strict";r.r(e);var o=function(){var t=this,e=t._self._c;return e("div",[e("div",[t._v(" 分类： "),e("el-select",{attrs:{size:"small"},model:{value:t.category_id,callback:function(e){t.category_id=e},expression:"category_id"}},t._l(t.foodCategoryList,(function(t){return e("el-option",{key:t.id,attrs:{value:t.id,label:t.name}})})),1),e("el-input",{staticStyle:{width:"200px",margin:"0 20px"},attrs:{size:"small",placeholder:"请输入"},on:{input:t.searchHandler},model:{value:t.keyword,callback:function(e){t.keyword=e},expression:"keyword"}}),e("el-button",{attrs:{size:"small",type:"primary"},on:{click:t.searchHandler}},[t._v("搜索")])],1),e("div",{staticStyle:{margin:"20px 0"}},[e("el-checkbox",{on:{change:t.handleCheckChange},model:{value:t.selectAll,callback:function(e){t.selectAll=e},expression:"selectAll"}},[t._v("全选")])],1),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"content-body"},t._l(t.foodsListData,(function(r){return e("div",{key:r.id,staticClass:"item"},[e("el-checkbox",{attrs:{value:r.selected,disabled:r.disabled},on:{change:function(e){return t.handleCheckboxChange(r)}}},[t._v(" "+t._s(r.name)+" ")])],1)})),0)])},a=[],n=r("ed08");function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return e};var t,e={},r=Object.prototype,o=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},n="function"==typeof Symbol?Symbol:{},l=n.iterator||"@@iterator",c=n.asyncIterator||"@@asyncIterator",u=n.toStringTag||"@@toStringTag";function d(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{d({},"")}catch(t){d=function(t,e,r){return t[e]=r}}function f(t,e,r,o){var n=e&&e.prototype instanceof _?e:_,i=Object.create(n.prototype),s=new j(o||[]);return a(i,"_invoke",{value:C(t,r,s)}),i}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var h="suspendedStart",m="suspendedYield",y="executing",v="completed",g={};function _(){}function b(){}function w(){}var x={};d(x,l,(function(){return this}));var k=Object.getPrototypeOf,L=k&&k(k(M([])));L&&L!==r&&o.call(L,l)&&(x=L);var S=w.prototype=_.prototype=Object.create(x);function D(t){["next","throw","return"].forEach((function(e){d(t,e,(function(t){return this._invoke(e,t)}))}))}function F(t,e){function r(a,n,s,l){var c=p(t[a],t,n);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==i(d)&&o.call(d,"__await")?e.resolve(d.__await).then((function(t){r("next",t,s,l)}),(function(t){r("throw",t,s,l)})):e.resolve(d).then((function(t){u.value=t,s(u)}),(function(t){return r("throw",t,s,l)}))}l(c.arg)}var n;a(this,"_invoke",{value:function(t,o){function a(){return new e((function(e,a){r(t,o,e,a)}))}return n=n?n.then(a,a):a()}})}function C(e,r,o){var a=h;return function(n,i){if(a===y)throw Error("Generator is already running");if(a===v){if("throw"===n)throw i;return{value:t,done:!0}}for(o.method=n,o.arg=i;;){var s=o.delegate;if(s){var l=E(s,o);if(l){if(l===g)continue;return l}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(a===h)throw a=v,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);a=y;var c=p(e,r,o);if("normal"===c.type){if(a=o.done?v:m,c.arg===g)continue;return{value:c.arg,done:o.done}}"throw"===c.type&&(a=v,o.method="throw",o.arg=c.arg)}}}function E(e,r){var o=r.method,a=e.iterator[o];if(a===t)return r.delegate=null,"throw"===o&&e.iterator.return&&(r.method="return",r.arg=t,E(e,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+o+"' method")),g;var n=p(a,e.iterator,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,g;var i=n.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,g):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function O(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function $(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function j(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(O,this),this.reset(!0)}function M(e){if(e||""===e){var r=e[l];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,n=function r(){for(;++a<e.length;)if(o.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return n.next=n}}throw new TypeError(i(e)+" is not iterable")}return b.prototype=w,a(S,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:b,configurable:!0}),b.displayName=d(w,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,d(t,u,"GeneratorFunction")),t.prototype=Object.create(S),t},e.awrap=function(t){return{__await:t}},D(F.prototype),d(F.prototype,c,(function(){return this})),e.AsyncIterator=F,e.async=function(t,r,o,a,n){void 0===n&&(n=Promise);var i=new F(f(t,r,o,a),n);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},D(S),d(S,u,"Generator"),d(S,l,(function(){return this})),d(S,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var o in e)r.push(o);return r.reverse(),function t(){for(;r.length;){var o=r.pop();if(o in e)return t.value=o,t.done=!1,t}return t.done=!0,t}},e.values=M,j.prototype={constructor:j,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach($),!e)for(var r in this)"t"===r.charAt(0)&&o.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(o,a){return s.type="throw",s.arg=e,r.next=o,a&&(r.method="next",r.arg=t),!!a}for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n],s=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var l=o.call(i,"catchLoc"),c=o.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&o.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var n=a;break}}n&&("break"===t||"continue"===t)&&n.tryLoc<=e&&e<=n.finallyLoc&&(n=null);var i=n?n.completion:{};return i.type=t,i.arg=e,n?(this.method="next",this.next=n.finallyLoc,g):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),$(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var o=r.completion;if("throw"===o.type){var a=o.arg;$(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,o){return this.delegate={iterator:M(e),resultName:r,nextLoc:o},"next"===this.method&&(this.arg=t),g}},e}function l(t,e,r,o,a,n,i){try{var s=t[n](i),l=s.value}catch(t){return void r(t)}s.done?e(l):Promise.resolve(l).then(o,a)}function c(t){return function(){var e=this,r=arguments;return new Promise((function(o,a){var n=t.apply(e,r);function i(t){l(n,o,a,i,s,"next",t)}function s(t){l(n,o,a,i,s,"throw",t)}i(void 0)}))}}var u={name:"EditMealFoods",props:{mealType:{type:String},alreadyFoodData:{type:Array}},data:function(){return{isLoading:!1,category_id:"",keyword:"",selectAll:!1,allValue:0,foodCategoryList:[],foodsListData:[],selectFoodIds:[],selectFoodList:[],disabledFoodIds:[]}},mounted:function(){this.initSelectData(),this.requestFoodCate(),this.requestMenuWeeklyFoodList()},methods:{initSelectData:function(){var t=this;this.selectFoodIds=[],this.selectFoodList=[],this.alreadyFoodData.length&&this.alreadyFoodData.map((function(e){t.selectFoodIds.push(e.food_id),t.disabledFoodIds.push(e.food_id)}))},searchHandler:Object(n["d"])((function(){this.requestMenuWeeklyFoodList()}),300),getPostData:function(){return this.selectFoodList},handleCheckChange:function(t){var e=this;this.foodsListData.forEach((function(r){r.disabled||(r.selected=t,t?e.selectFoodIds.includes(r.id)||(e.selectFoodIds.push(r.id),e.selectFoodList.push(r)):(e.selectFoodIds.splice(e.selectFoodIds.indexOf(r.id),1),e.selectFoodList.splice(e.selectFoodList.indexOf(r.id),1)))}))},handleCheckboxChange:function(t){var e=this.foodsListData.find((function(e){return e.id===t.id}));e.selected=!e.selected,e.selected&&!this.selectFoodIds.includes(t.id)?(this.selectFoodIds.push(t.id),this.selectFoodList.push(t)):(this.selectFoodIds.splice(this.selectFoodIds.indexOf(t.id),1),this.selectFoodList.splice(this.selectFoodList.indexOf(t.id),1))},requestMenuWeeklyFoodList:function(){var t=this;return c(s().mark((function e(){var r,o;return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,r={},t.category_id&&(r.category_id=t.category_id),t.keyword&&(r.name=t.keyword),e.next=6,t.$apis.apiBackgroundFoodSetMealSetMealFoodListPost(r);case 6:o=e.sent,t.isLoading=!1,0===o.code?(t.selectAll=!1,t.foodsListData=o.data.map((function(e){return e.selected=t.selectFoodIds.includes(e.id),e.disabled=t.disabledFoodIds.includes(e.id),e.status=1,e.spec_id=e.spec_list[0].id,e.food_price=e.spec_list[0].food_price,e}))):t.$message.error(o.msg);case 9:case"end":return e.stop()}}),e)})))()},requestFoodCate:function(){var t=this;return c(s().mark((function e(){var r,o;return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.apiBackgroundFoodMenuWeeklyCategoryListPost();case 2:r=e.sent,0===r.code?(o=r.data.map((function(t){return{name:t.name,id:t.id}})),o.unshift({name:"全部",id:""}),t.foodCategoryList=o):t.$message.error(r.msg);case 4:case"end":return e.stop()}}),e)})))()}}},d=u,f=(r("6d2d"),r("2877")),p=Object(f["a"])(d,o,a,!1,null,null,null);e["default"]=p.exports},"6d2d":function(t,e,r){"use strict";r("82bd")},"82bd":function(t,e,r){},"9c4e":function(t,e,r){},b1be:function(t,e,r){"use strict";r.r(e);var o=function(){var t=this,e=t._self._c;return e("div",{staticClass:"add-edit-set-meal container-wrapper"},[e("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formIngredients",attrs:{rules:t.formRuls,model:t.formData,size:"small","label-width":"80px"}},[e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("套餐基本信息")])]),e("div",{staticStyle:{padding:"0 20px"}},[e("el-form-item",{attrs:{label:"套餐名称",prop:"name"}},[e("el-input",{staticClass:"ps-input p-r-48",staticStyle:{width:"240px"},attrs:{placeholder:"请输入标题",maxlength:"15","show-word-limit":""},model:{value:t.formData.name,callback:function(e){t.$set(t.formData,"name",e)},expression:"formData.name"}})],1),e("el-form-item",{attrs:{label:"套餐分类",prop:"category_id"}},[e("el-select",{staticClass:"ps-select",attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select"},model:{value:t.formData.category_id,callback:function(e){t.$set(t.formData,"category_id",e)},expression:"formData.category_id"}},t._l(t.categoryList,(function(t,r){return e("el-option",{key:r,attrs:{label:t.name,value:t.id}})})),1)],1),e("el-form-item",{attrs:{label:"套餐图片",prop:"image"}},[e("el-upload",{ref:"uploadFood",staticClass:"avatar-uploader",attrs:{headers:t.headersOpts,data:t.uploadParams,action:t.actionUrl,"show-file-list":!1,"on-success":t.handleAvatarSuccess,"before-upload":t.beforeAvatarUpload}},[t.formData.image?e("img",{staticClass:"avatar",attrs:{src:t.formData.image}}):e("i",{staticClass:"el-icon-plus avatar-uploader-icon"}),e("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v("图片仅支持jpg，png格式，且不超过5M")])])],1),e("el-form-item",{attrs:{label:"套餐简介","label-width":"100px"}},[e("el-input",{staticClass:"ps-input w-300",attrs:{type:"textarea",autosize:{minRows:4,maxRows:6},maxlength:"100",placeholder:"请输入简介"},model:{value:t.formData.description,callback:function(e){t.$set(t.formData,"description",e)},expression:"formData.description"}})],1),e("el-form-item",{attrs:{label:"套餐价格","label-width":"100px"}},[e("el-radio",{staticClass:"ps-radio",attrs:{label:"total"},model:{value:t.formData.price_type,callback:function(e){t.$set(t.formData,"price_type",e)},expression:"formData.price_type"}},[t._v(" 菜品价格合计 ")]),e("el-radio",{staticClass:"ps-radio",attrs:{label:"fixed"},model:{value:t.formData.price_type,callback:function(e){t.$set(t.formData,"price_type",e)},expression:"formData.price_type"}},[t._v(" 固定套餐价格 ")]),e("el-form-item",{key:t.formData.price_type,staticClass:"form-item--inline no-label w-300",attrs:{prop:"fixed_price",label:"",rules:t.formRuls.fixed_price}},[e("el-input",{staticClass:"ps-input",staticStyle:{width:"100px"},attrs:{disabled:"fixed"!==t.formData.price_type},model:{value:t.formData.fixed_price,callback:function(e){t.$set(t.formData,"fixed_price",e)},expression:"formData.fixed_price"}})],1)],1),e("el-form-item",{attrs:{label:"套餐打包费","label-width":"100px",prop:"pack_price"}},[e("el-input",{staticClass:"ps-input p-r-48",staticStyle:{width:"240px"},attrs:{placeholder:"请输入套餐打包费","show-word-limit":""},model:{value:t.formData.pack_price,callback:function(e){t.$set(t.formData,"pack_price",e)},expression:"formData.pack_price"}},[e("template",{slot:"append"},[t._v("元")])],2)],1)],1),e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("套餐样式")])]),e("div",{staticStyle:{padding:"0 20px"}},[e("el-form-item",{attrs:{label:"菜品是否可重复选择","label-width":"170px"}},[e("el-radio-group",{staticClass:"ps-radio",model:{value:t.formData.food_repeat,callback:function(e){t.$set(t.formData,"food_repeat",e)},expression:"formData.food_repeat"}},[e("el-radio",{attrs:{label:1}},[t._v("是")]),e("el-radio",{attrs:{label:0}},[t._v("否")])],1)],1),e("el-form-item",{attrs:{label:""}},[e("div",{staticClass:"ps-red"},[t._v(" 勾选“是”的情况下，不同样式内同个菜品用户可重复选择；勾选“否”则选过菜品，在其他样式内用户不可再选 ")])]),e("div",{staticClass:"p-b-10",staticStyle:{color:"#229bff",cursor:"pointer"},on:{click:t.addStyle}},[t._v(" 添加样式 ")]),t._l(t.formData.set_meal_style_data,(function(r,o){return e("div",{key:o,staticClass:"set-meal-table"},[e("div",{staticClass:"p-t-10"},[e("el-form-item",{key:o,attrs:{label:"名称",rules:t.formRuls.name,prop:"set_meal_style_data["+o+"].set_meal_style_name"}},[e("div",{staticClass:"ps-flex-bw"},[e("el-input",{staticClass:"ps-input p-r-48",staticStyle:{width:"240px"},attrs:{placeholder:"请输入名称",maxlength:"15","show-word-limit":""},model:{value:r.set_meal_style_name,callback:function(e){t.$set(r,"set_meal_style_name",e)},expression:"item.set_meal_style_name"}}),e("div",{staticClass:"ps-red p-r-10",staticStyle:{cursor:"pointer"},on:{click:function(e){return t.delStyle(o)}}},[t._v(" 删除样式 ")])],1)])],1),e("el-form-item",{attrs:{label:"样式内菜品是否必选",prop:"","label-width":"170px"}},[e("el-checkbox",{staticClass:"ps-checkbox",attrs:{"true-label":1,"false-label":0},model:{value:r.food_required,callback:function(e){t.$set(r,"food_required",e)},expression:"item.food_required"}},[t._v(" 必选 ")])],1),e("el-form-item",{attrs:{label:"",prop:""}},[e("div",{staticClass:"m-r-10"},[e("el-table",{staticStyle:{width:"100%"},attrs:{data:r.foods,border:"","header-row-class-name":"ps-table-header-row"},scopedSlots:t._u([{key:"append",fn:function(){return[e("div",{staticClass:"t-a-c"},[e("el-button",{attrs:{type:"text",size:"medium"},on:{click:function(e){return t.addSetMealFood(o)}}},[t._v(" 添加菜品 "),e("i",{staticClass:"el-icon-plus el-icon--right"})])],1)]},proxy:!0}],null,!0)},[e("el-table-column",{attrs:{prop:"food_name",label:"菜品名称",align:"center"}}),e("el-table-column",{attrs:{prop:"food_category",label:"菜品分类",align:"center"}}),e("el-table-column",{attrs:{prop:"",label:"规格",align:"center"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select"},on:{change:function(e){return t.specsChange(r.row)}},model:{value:r.row.spec_id,callback:function(e){t.$set(r.row,"spec_id",e)},expression:"scope.row.spec_id"}},t._l(r.row.spec_list,(function(t,r){return e("el-option",{key:r,attrs:{label:t.name,value:t.id}})})),1)]}}],null,!0)}),e("el-table-column",{attrs:{prop:"food_price",label:"菜品价格",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t._f("formatMoney")(e.row.food_price))+" ")]}}],null,!0)}),e("el-table-column",{attrs:{fixed:"right",label:"操作",width:"180",align:"center"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("el-button",{staticClass:"ps-warn",attrs:{type:"text",size:"small"},on:{click:function(e){return t.delSetMealFood(o,r.$index)}}},[t._v(" 删除 ")])]}}],null,!0)})],1)],1)])],1)}))],2)]),e("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[e("el-button",{staticStyle:{width:"120px"},on:{click:t.closeHandler}},[t._v("取消")]),e("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:t.submitHandler}},[t._v(" "+t._s("add"===t.type?"添加":"保存")+" ")])],1)]),t.showFoodDialog?e("div",[e("el-dialog",{attrs:{title:"添加菜品",visible:t.showFoodDialog,width:"900px",top:"20vh","custom-class":"ps-dialog","close-on-click-modal":!1},on:{"update:visible":function(e){t.showFoodDialog=e}}},[e("div",[e("set-meal-dialog",{ref:"setMealDialogRef",attrs:{alreadyFoodData:t.alreadyFoodData}})],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{attrs:{size:"small"},on:{click:function(e){t.showFoodDialog=!1}}},[t._v("取 消")]),e("el-button",{attrs:{size:"small",type:"primary"},on:{click:t.confirmSetMealDialog}},[t._v("确 定")])],1)])],1):t._e()],1)},a=[],n=r("ed08"),i=r("d0dd"),s=r("4b35");function l(t){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},l(t)}function c(t,e){return h(t)||p(t,e)||d(t,e)||u()}function u(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(t,e){if(t){if("string"==typeof t)return f(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?f(t,e):void 0}}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,o=Array(e);r<e;r++)o[r]=t[r];return o}function p(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var o,a,n,i,s=[],l=!0,c=!1;try{if(n=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(o=n.call(r)).done)&&(s.push(o.value),s.length!==e);l=!0);}catch(t){c=!0,a=t}finally{try{if(!l&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(c)throw a}}return s}}function h(t){if(Array.isArray(t))return t}function m(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */m=function(){return e};var t,e={},r=Object.prototype,o=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},n="function"==typeof Symbol?Symbol:{},i=n.iterator||"@@iterator",s=n.asyncIterator||"@@asyncIterator",c=n.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function d(t,e,r,o){var n=e&&e.prototype instanceof _?e:_,i=Object.create(n.prototype),s=new j(o||[]);return a(i,"_invoke",{value:C(t,r,s)}),i}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=d;var p="suspendedStart",h="suspendedYield",y="executing",v="completed",g={};function _(){}function b(){}function w(){}var x={};u(x,i,(function(){return this}));var k=Object.getPrototypeOf,L=k&&k(k(M([])));L&&L!==r&&o.call(L,i)&&(x=L);var S=w.prototype=_.prototype=Object.create(x);function D(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function F(t,e){function r(a,n,i,s){var c=f(t[a],t,n);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==l(d)&&o.call(d,"__await")?e.resolve(d.__await).then((function(t){r("next",t,i,s)}),(function(t){r("throw",t,i,s)})):e.resolve(d).then((function(t){u.value=t,i(u)}),(function(t){return r("throw",t,i,s)}))}s(c.arg)}var n;a(this,"_invoke",{value:function(t,o){function a(){return new e((function(e,a){r(t,o,e,a)}))}return n=n?n.then(a,a):a()}})}function C(e,r,o){var a=p;return function(n,i){if(a===y)throw Error("Generator is already running");if(a===v){if("throw"===n)throw i;return{value:t,done:!0}}for(o.method=n,o.arg=i;;){var s=o.delegate;if(s){var l=E(s,o);if(l){if(l===g)continue;return l}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(a===p)throw a=v,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);a=y;var c=f(e,r,o);if("normal"===c.type){if(a=o.done?v:h,c.arg===g)continue;return{value:c.arg,done:o.done}}"throw"===c.type&&(a=v,o.method="throw",o.arg=c.arg)}}}function E(e,r){var o=r.method,a=e.iterator[o];if(a===t)return r.delegate=null,"throw"===o&&e.iterator.return&&(r.method="return",r.arg=t,E(e,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+o+"' method")),g;var n=f(a,e.iterator,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,g;var i=n.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,g):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function O(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function $(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function j(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(O,this),this.reset(!0)}function M(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,n=function r(){for(;++a<e.length;)if(o.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return n.next=n}}throw new TypeError(l(e)+" is not iterable")}return b.prototype=w,a(S,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:b,configurable:!0}),b.displayName=u(w,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,u(t,c,"GeneratorFunction")),t.prototype=Object.create(S),t},e.awrap=function(t){return{__await:t}},D(F.prototype),u(F.prototype,s,(function(){return this})),e.AsyncIterator=F,e.async=function(t,r,o,a,n){void 0===n&&(n=Promise);var i=new F(d(t,r,o,a),n);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},D(S),u(S,c,"Generator"),u(S,i,(function(){return this})),u(S,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var o in e)r.push(o);return r.reverse(),function t(){for(;r.length;){var o=r.pop();if(o in e)return t.value=o,t.done=!1,t}return t.done=!0,t}},e.values=M,j.prototype={constructor:j,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach($),!e)for(var r in this)"t"===r.charAt(0)&&o.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(o,a){return s.type="throw",s.arg=e,r.next=o,a&&(r.method="next",r.arg=t),!!a}for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n],s=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var l=o.call(i,"catchLoc"),c=o.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&o.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var n=a;break}}n&&("break"===t||"continue"===t)&&n.tryLoc<=e&&e<=n.finallyLoc&&(n=null);var i=n?n.completion:{};return i.type=t,i.arg=e,n?(this.method="next",this.next=n.finallyLoc,g):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),$(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var o=r.completion;if("throw"===o.type){var a=o.arg;$(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,o){return this.delegate={iterator:M(e),resultName:r,nextLoc:o},"next"===this.method&&(this.arg=t),g}},e}function y(t,e,r,o,a,n,i){try{var s=t[n](i),l=s.value}catch(t){return void r(t)}s.done?e(l):Promise.resolve(l).then(o,a)}function v(t){return function(){var e=this,r=arguments;return new Promise((function(o,a){var n=t.apply(e,r);function i(t){y(n,o,a,i,s,"next",t)}function s(t){y(n,o,a,i,s,"throw",t)}i(void 0)}))}}var g={name:"AddEditSetMeal",data:function(){return{isLoading:!1,type:"add",formData:{name:"",category_id:"",image:"",price_type:"total",fixed_price:"",pack_price:"",food_repeat:0,set_meal_style_data:[],description:""},alreadyFoodData:[],categoryList:[],formRuls:{name:[{required:!0,message:"请输入名称",trigger:"blur"}],category_id:[{required:!0,message:"请选择分类",trigger:"blur"}],fixed_price:[{required:!1,validator:i["b"],message:"请输入正确固定套餐价格",trigger:"blur"}],pack_price:[{required:!1,validator:i["b"],message:"请正确套餐打包费",trigger:"blur"}]},actionUrl:"/api/background/file/upload",uploadParams:{prefix:"setMeal"},headersOpts:{TOKEN:Object(n["B"])()},showFoodDialog:!1,setMealFoodIndex:0}},components:{SetMealDialog:s["default"]},created:function(){this.type=this.$route.query.type,this.getSetMealCategoryList(),this.initLoad()},mounted:function(){},methods:{initLoad:function(){if("modify"===this.type){var t=this.$decodeQuery(this.$route.query.data);this.formData={id:t.id,name:t.name,category_id:t.category,image:t.image,price_type:t.price_type,fixed_price:Object(n["i"])(t.fixed_price),pack_price:Object(n["i"])(t.pack_price),food_repeat:t.food_repeat,set_meal_style_data:t.set_meal_style,description:t.description}}},searchHandle:Object(n["d"])((function(){this.currentPage=1}),300),getUploadToken:function(){var t=this;return v(m().mark((function e(){var r;return m().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.getUploadToken({prefix:"setMeal"});case 2:r=e.sent,0===r.code?(t.actionUrl=r.data.host,t.uploadParams={key:r.data.prefix+(new Date).getTime()+Math.floor(150*Math.random()),prefix:r.data.prefix,policy:r.data.policy,OSSAccessKeyId:r.data.accessid,signature:r.data.signature,callback:r.data.callback,success_action_status:"200"}):t.$message.error(r.msg);case 4:case"end":return e.stop()}}),e)})))()},getSetMealCategoryList:function(){var t=this;return v(m().mark((function e(){var r,o,a,i;return m().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(n["Z"])(t.$apis.apiBackgroundFoodSetMealCategoryListPost({page:1,page_size:9999}));case 3:if(r=e.sent,o=c(r,2),a=o[0],i=o[1],t.isLoading=!1,!a){e.next=11;break}return t.$message.error(a.message),e.abrupt("return");case 11:0===i.code?t.categoryList=i.data.results:t.$message.error(i.msg);case 12:case"end":return e.stop()}}),e)})))()},handleAvatarSuccess:function(t,e){0===t.code?(this.$refs.uploadFood.clearFiles(),this.formData.image=t.data.public_url):this.$message.error(t.msg)},beforeAvatarUpload:function(t){var e="image/jpeg"===t.type||"image/png"===t.type,r=t.size/1024/1024<5;return this.uploadParams.key=(new Date).getTime()+Math.floor(150*Math.random()),e||this.$message.error("上传头像图片只能是 JPG PNG格式!"),r||this.$message.error("上传头像图片大小不能超过 5MB!"),e&&r},addStyle:function(){this.formData.set_meal_style_data.push({id:0,set_meal_style_name:"",food_required:0,foods:[]})},delStyle:function(t){this.formData.set_meal_style_data.splice(t,1)},addSetMealFood:function(t){this.showFoodDialog=!0,this.setMealFoodIndex=t,this.alreadyFoodData=this.formData.set_meal_style_data[t].foods},delSetMealFood:function(t,e){this.formData.set_meal_style_data[t].foods.splice(e,1)},specsChange:function(t,e,r){var o=t.spec_list.filter((function(e){return e.id===t.spec_id}))[0];this.$set(t,"food_price",o.food_price)},confirmSetMealDialog:function(){var t=this,e=this.$refs.setMealDialogRef.getPostData();e.length?(this.showFoodDialog=!1,e.map((function(e){t.formData.set_meal_style_data[t.setMealFoodIndex].foods.push({food_id:e.id,food_name:e.name,food_category:e.food_category,spec_id:e.spec_id,food_price:e.food_price,spec_list:e.spec_list})}))):this.$message.error("请选择菜品")},foodModifySetMealAdd:function(t){var e=this;return v(m().mark((function r(){var o,a,i,s,l,u,d,f;return m().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(e.isLoading=!0,o="",a=c(o,2),i=a[0],s=a[1],"add"!==e.type){r.next=12;break}return r.next=6,Object(n["Z"])(e.$apis.apiBackgroundFoodSetMealAddPost(t));case 6:l=r.sent,u=c(l,2),i=u[0],s=u[1],r.next=19;break;case 12:return r.next=15,Object(n["Z"])(e.$apis.apiBackgroundFoodSetMealModifyPost(t));case 15:d=r.sent,f=c(d,2),i=f[0],s=f[1];case 19:if(e.isLoading=!1,!i){r.next=23;break}return e.$message.error(i.message),r.abrupt("return");case 23:0===s.code?(e.$message.success(s.msg),e.$closeCurrentTab(e.$route.path)):e.$message.error(s.msg);case 24:case"end":return r.stop()}}),r)})))()},submitHandler:function(){var t=this;this.$refs.formIngredients.validate((function(e){var r=Object(n["f"])(t.formData);if(r.fixed_price=Object(n["Y"])(t.formData.fixed_price),r.pack_price=Object(n["Y"])(t.formData.pack_price),r.set_meal_style_data=r.set_meal_style_data.map((function(t){return t.food_data=t.foods.map((function(t){return{food_id:t.food_id,spec_id:t.spec_id}})),{food_required:t.food_required,id:t.id,set_meal_style_name:t.set_meal_style_name,food_data:t.food_data}})),e){if(t.isLoading)return t.$message.error("请勿重复提交！");t.foodModifySetMealAdd(r)}}))},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,r,o){"confirm"===e?t.$closeCurrentTab(t.$route.path):r.confirmButtonLoading||o()}}).then((function(t){})).catch((function(t){}))}}},_=g,b=(r("f224"),r("2877")),w=Object(b["a"])(_,o,a,!1,null,null,null);e["default"]=w.exports},d0dd:function(t,e,r){"use strict";r.d(e,"a",(function(){return o})),r.d(e,"b",(function(){return a})),r.d(e,"g",(function(){return n})),r.d(e,"c",(function(){return i})),r.d(e,"f",(function(){return s})),r.d(e,"d",(function(){return l})),r.d(e,"e",(function(){return c}));var o=function(t,e,r){if(e){var o=/^-?(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;o.test(e)?r():r(new Error("金额格式有误"))}else r(new Error("请输入金额"))},a=function(t,e,r){if(e){var o=/^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;o.test(e)?r():r(new Error("金额格式有误"))}else r()},n=function(t,e,r){if(!e)return r(new Error("手机号不能为空"));var o=/^1[3456789]\d{9}$/;o.test(e)?r():r(new Error("请输入正确手机号"))},i=function(t,e,r){if(!e)return r(new Error("金额有误"));var o=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;o.test(e)?r():r(new Error("金额格式有误"))},s=function(t,e,r){if(""===e)return r(new Error("不能为空"));var o=/^\d+$/;o.test(e)?r():r(new Error("请输入正确数字"))},l=function(t,e,r){if(""!==e){var o=/^(\+|-)?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;o.test(e)?r():r(new Error("金额格式有误"))}else r(new Error("请输入金额"))},c=function(t,e,r){var o=/^[\u4E00-\u9FA5\w-]+$/;o.test(e)?r():r(new Error("格式不正确，不能包含特殊字符"))}},f224:function(t,e,r){"use strict";r("9c4e")}}]);