(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-components-TemplateDialog","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-meal-report-MealPackageRule"],{"0cda":function(t,e,n){},"322d":function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t._self._c;return e("dialog-message",{staticClass:"template-dialog",attrs:{show:t.visible,loading:t.dialogLoading,title:t.dialogTitle,top:"10vh",width:t.dialogWidth,showFooter:!1},on:{"update:show":function(e){t.visible=e},close:t.closeDialog}},[e("div",{staticClass:"p-b-30 m-l-30 max-h-600"},t._l(t.formSetting,(function(n,r){return e("div",{key:r},[e("h3",[t._v(t._s(n.label))]),e("div",{staticClass:"m-l-60 m-t-20"},t._l(n.children,(function(r){return e("span",{key:r.key,staticClass:"inline-block m-r-60 m-b-20 vertical-top"},[e("span",{staticClass:"inline-block max-w-100 vertical-top"},[t._v(t._s(r.label+"："))]),e("span",{staticClass:"vertical-top m-l-10 m-r-6"},[t._v(t._s(t.formData[n.key][r.key])+"道")])])})),0)])})),0)])},a=[],i=n("ed08"),o=n("c9d9");function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function l(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */l=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,a=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function f(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,n){return t[e]=n}}function h(t,e,n,r){var i=e&&e.prototype instanceof b?e:b,o=Object.create(i.prototype),u=new C(r||[]);return a(o,"_invoke",{value:j(t,n,u)}),o}function d(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var m="suspendedStart",y="suspendedYield",p="executing",g="completed",v={};function b(){}function w(){}function k(){}var _={};f(_,o,(function(){return this}));var x=Object.getPrototypeOf,L=x&&x(x(V([])));L&&L!==n&&r.call(L,o)&&(_=L);var D=k.prototype=b.prototype=Object.create(_);function E(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function n(a,i,o,l){var c=d(t[a],t,i);if("throw"!==c.type){var s=c.arg,f=s.value;return f&&"object"==u(f)&&r.call(f,"__await")?e.resolve(f.__await).then((function(t){n("next",t,o,l)}),(function(t){n("throw",t,o,l)})):e.resolve(f).then((function(t){s.value=t,o(s)}),(function(t){return n("throw",t,o,l)}))}l(c.arg)}var i;a(this,"_invoke",{value:function(t,r){function a(){return new e((function(e,a){n(t,r,e,a)}))}return i=i?i.then(a,a):a()}})}function j(e,n,r){var a=m;return function(i,o){if(a===p)throw Error("Generator is already running");if(a===g){if("throw"===i)throw o;return{value:t,done:!0}}for(r.method=i,r.arg=o;;){var u=r.delegate;if(u){var l=A(u,r);if(l){if(l===v)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===m)throw a=g,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=p;var c=d(e,n,r);if("normal"===c.type){if(a=r.done?g:y,c.arg===v)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(a=g,r.method="throw",r.arg=c.arg)}}}function A(e,n){var r=n.method,a=e.iterator[r];if(a===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,A(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var i=d(a,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,v;var o=i.arg;return o?o.done?(n[e.resultName]=o.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,v):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function F(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function C(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(F,this),this.reset(!0)}function V(e){if(e||""===e){var n=e[o];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,i=function n(){for(;++a<e.length;)if(r.call(e,a))return n.value=e[a],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw new TypeError(u(e)+" is not iterable")}return w.prototype=k,a(D,"constructor",{value:k,configurable:!0}),a(k,"constructor",{value:w,configurable:!0}),w.displayName=f(k,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,k):(t.__proto__=k,f(t,s,"GeneratorFunction")),t.prototype=Object.create(D),t},e.awrap=function(t){return{__await:t}},E(S.prototype),f(S.prototype,c,(function(){return this})),e.AsyncIterator=S,e.async=function(t,n,r,a,i){void 0===i&&(i=Promise);var o=new S(h(t,n,r,a),i);return e.isGeneratorFunction(n)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},E(D),f(D,s,"Generator"),f(D,o,(function(){return this})),f(D,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=V,C.prototype={constructor:C,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function a(r,a){return u.type="throw",u.arg=e,n.next=r,a&&(n.method="next",n.arg=t),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],u=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var l=r.call(o,"catchLoc"),c=r.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),O(n),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var a=r.arg;O(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:V(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),v}},e}function c(t,e){return m(t)||d(t,e)||f(t,e)||s()}function s(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(t,e){if(t){if("string"==typeof t)return h(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?h(t,e):void 0}}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function d(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,a,i,o,u=[],l=!0,c=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=i.call(n)).done)&&(u.push(r.value),u.length!==e);l=!0);}catch(t){c=!0,a=t}finally{try{if(!l&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw a}}return u}}function m(t){if(Array.isArray(t))return t}function y(t,e,n,r,a,i,o){try{var u=t[i](o),l=u.value}catch(t){return void n(t)}u.done?e(l):Promise.resolve(l).then(r,a)}function p(t){return function(){var e=this,n=arguments;return new Promise((function(r,a){var i=t.apply(e,n);function o(t){y(i,r,a,o,u,"next",t)}function u(t){y(i,r,a,o,u,"throw",t)}o(void 0)}))}}var g={name:"TemplateDialog",model:{prop:"showDialog",event:"changeShow"},props:{showDialog:{required:!0},dialogTitle:{type:String,default:"预览"},type:{type:String,default:"add"},infoData:{type:Array,default:function(){return[]}},dialogWidth:{type:String,default:"760px"},closehandle:Function,confirmhandle:Function},data:function(){return{dialogLoading:!1,formSetting:{},formData:{},mealList:o["a"],templateAttributeList:[]}},computed:{visible:{get:function(){return this.showDialog},set:function(t){this.$emit("changeShow",t)}}},watch:{showDialog:function(t){t&&this.init()}},created:function(){},mounted:function(){this.getFoodAttributeList()},methods:{init:function(){var t=this;this.infoData.forEach((function(e){var n=e.category_json_data;for(var r in t.formData[e.meal_type])""!==n[r]&&t.$set(t.formData[e.meal_type],r,n[r])}))},getFoodAttributeList:function(){var t=this;return p(l().mark((function e(){var n,r,a,i;return l().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$to(t.$apis.apiBackgroundFoodFoodCategoryListPost({page:1,page_size:99999}));case 2:if(n=e.sent,r=c(n,2),a=r[0],i=r[1],!a){e.next=9;break}return t.$message.error(a.message),e.abrupt("return");case 9:0===i.code?t.setDefaultAttrData(i.data.results):t.$message.error(i.msg);case 10:case"end":return e.stop()}}),e)})))()},setDefaultAttrData:function(t){var e=this,n={};t?(this.templateAttributeList=t.map((function(t){var e={label:t.name,key:t.id};return n[t.id]="",e})),this.formSetting=this.mealList.map((function(t){return e.$set(e.formData,t.value,Object(i["f"])(n)),{label:t.label,key:t.value,children:Object(i["f"])(e.templateAttributeList)}}))):(this.templateAttributeList.map((function(t){n[t.key]=""})),this.mealList.map((function(t){e.$set(e.formData,t.value,Object(i["f"])(n))})))},closeDialog:function(){this.resetForm(),this.closehandle&&this.closehandle()},cancleDialog:function(){this.resetForm(),this.closehandle&&this.closehandle(),this.visible=!1},confirmDialog:function(){this.resetForm(),this.confirmhandle&&this.confirmhandle()},resetForm:function(){this.dialogForm={},this.setDefaultAttrData()}}},v=g,b=(n("a770"),n("2877")),w=Object(b["a"])(v,r,a,!1,null,"d52fd8b6",null);e["default"]=w.exports},a770:function(t,e,n){"use strict";n("0cda")},c9d9:function(t,e,n){"use strict";n.d(e,"a",(function(){return i})),n.d(e,"d",(function(){return o})),n.d(e,"b",(function(){return u})),n.d(e,"c",(function(){return l})),n.d(e,"e",(function(){return c})),n.d(e,"f",(function(){return s})),n.d(e,"g",(function(){return f}));var r=n("5a0c"),a=n("da92"),i=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],o=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],u={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},l=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],c=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],s=(r().subtract(7,"day").format("YYYY-MM-DD"),r().format("YYYY-MM-DD"),function(t){return t?"number"===typeof t?a["a"].divide(t,100).toFixed(2):"string"!==typeof t||isNaN(Number(t))?t:a["a"].divide(t,100).toFixed(2):"0.00"}),f=function(t){return a["a"].times(t,100)}}}]);