(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-health-nutrition-AddIngredients","view-super-health-system-components-selectLaber","view-super-health-system-health-nutrition-CommodityLibrary","view-super-health-system-health-nutrition-IngredientsCategory","view-super-health-system-health-nutrition-constants"],{"015b":function(t,e,a){"use strict";a.r(e),a.d(e,"DEFAULT_NUTRITION",(function(){return r})),a.d(e,"ELEMENT_NUTRITION",(function(){return i})),a.d(e,"VITAMIN_NUTRITION",(function(){return o})),a.d(e,"NUTRITION_LIST",(function(){return l})),a.d(e,"LIBRARY_SEARCH_SETTING_SUPER",(function(){return s})),a.d(e,"LIBRARY_SEARCH_SETTING_MERCHANT",(function(){return c})),a.d(e,"LIBRARY_SEARCH_SETTING_CATEORY",(function(){return u})),a.d(e,"COMMODITY_SEARCH_SETTING_SUPER",(function(){return f})),a.d(e,"COMMODITY_SEARCH_SETTING_MERCHANT",(function(){return p}));var n=a("ed08"),r=[{name:"千卡",key:"energy_kcal",unit:"Kcal",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{name:"胆固醇",key:"cholesterol",unit:"mg",type:"default"},{name:"膳食纤维",key:"dietary_fiber",unit:"g",type:"default"}],i=[{key:"Ca",name:"钙",unit:"mg",type:"element"},{key:"P",name:"磷",unit:"mg",type:"element"},{key:"K",name:"钾",unit:"mg",type:"element"},{key:"Na",name:"钠",unit:"mg",type:"element"},{name:"镁",key:"Mg",unit:"mg",type:"element"},{key:"Fe",name:"铁",unit:"mg",type:"element"},{key:"I",name:"碘",unit:"μg",type:"element"},{key:"Se",name:"硒",unit:"μg",type:"element"},{key:"Zn",name:"锌",unit:"mg",type:"element"},{key:"Cu",name:"铜",unit:"mg",type:"element"},{key:"F",name:"氟",unit:"mg",type:"element"},{key:"Cr",name:"铬",unit:"μg",type:"element"},{key:"Mo",name:"钼",unit:"μg",type:"element"},{key:"Mn",name:"锰",unit:"mg",type:"element"}],o=[{key:"VA",name:"维生素A",unit:"μg",type:"vitamin"},{key:"VD",name:"维生素D",unit:"μg",type:"vitamin"},{key:"VE",name:"维生素E",unit:"mg",type:"vitamin"},{key:"VK",name:"维生素K",unit:"μg",type:"vitamin"},{key:"VB1",name:"维生素B1",unit:"mg",type:"vitamin"},{key:"VB2",name:"维生素B2",unit:"mg",type:"vitamin"},{key:"VB6",name:"维生素B6",unit:"mg",type:"vitamin"},{key:"VB12",name:"维生素B12",unit:"μg",type:"vitamin"},{key:"VC",name:"维生素C",unit:"mg",type:"vitamin"},{key:"VB5",name:"泛酸",unit:"mg",type:"vitamin"},{key:"VM",name:"叶酸",unit:"μg",type:"vitamin"},{key:"VB3",name:"烟酸",unit:"mg",type:"vitamin"},{key:"Choline",name:" 胆碱",unit:"mg",type:"vitamin"},{key:"Nicotinamide",name:"烟酰胺",unit:"mg",type:"vitamin"},{key:"VH",name:"生物素",unit:"mg",type:"vitamin"}],l=[].concat(r,i,o),s={date_type:{type:"select",label:"",value:1,maxWidth:"130px",placeholder:"请选择",dataList:[{label:"创建时间",value:1},{label:"修改时间",value:2}]},select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"",clearable:!0,value:[]},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(t){return{id:t.level+"_"+t.id,label:t.name,children:t.sort_list}}},is_enable_nutrition:{type:"select",label:"营养信息",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"有",value:"1"},{label:"无",value:"0"}]},ingredient_name:{type:"input",label:"食材名称",value:"",placeholder:"请输入食材名称"},operator:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"},label_filter:{type:"select",label:"",value:"",placeholder:"",dataList:[{label:"请选择",value:"select",disabled:!0},{label:"包含",value:"Include"},{label:"不包含",value:"Exclude"}]},label_list:{type:"treeselect",label:"",value:[],placeholder:"选择标签",multiple:!0,limit:1,level:1,valueConsistsOf:"LEAF_PRIORITY",normalizer:function(t){return{id:t.id,label:t.name,children:t.label_list}},dataList:[]},is_entering:{type:"select",label:"录入图片",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"已录入",value:"1"},{label:"未录入",value:"0"}]}},c={select_time:{type:"datetimerange",label:"创建时间",format:"yyyy-MM-dd",value:Object(n["y"])(7)},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(t){return{id:t.level+"_"+t.id,label:t.name,children:t.sort_list}}},is_repeat:{type:"select",label:"已有食材",value:"",placeholder:"请选择是否已有食材",dataList:[{label:"是",value:1},{label:"否",value:0}]},is_enable_nutrition:{type:"select",label:"营养信息",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"有",value:"1"},{label:"无",value:"0"}]},ingredient_name:{type:"input",label:"食材名称",value:"",placeholder:"请输入食材名称"},is_entering:{type:"select",label:"录入图片",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"已录入",value:"1"},{label:"未录入",value:"0"}]}},u={select_time:{type:"datetimerange",label:"修改时间",format:"yyyy-MM-dd HH:mm:ss",value:[]},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(t){return{id:t.level+"_"+t.id,label:t.name,children:t.sort_list}}},user_name:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"}},f={date_type:{type:"select",label:"",value:1,maxWidth:"130px",placeholder:"请选择",dataList:[{label:"创建时间",value:1},{label:"修改时间",value:2}]},select_time:{type:"datetimerange",label:"",format:"yyyy-MM-dd HH:mm:ss",value:[]},food_name:{type:"input",label:"菜品/商品名称",value:"",placeholder:"请输入菜品/商品名称"},user_name:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"},attributes:{type:"select",label:"属性",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"商品",value:"goods"},{label:"菜品",value:"foods"}]},label_filter:{type:"select",label:"",value:"",placeholder:"",dataList:[{label:"请选择",value:"select",disabled:!0},{label:"包含",value:"Include"},{label:"不包含",value:"Exclude"}]},label_list:{type:"treeselect",label:"",value:[],placeholder:"选择标签",multiple:!0,limit:1,level:1,valueConsistsOf:"LEAF_PRIORITY",normalizer:function(t){return{id:t.id,label:t.name,children:t.label_list}},dataList:[]}},p={select_time:{type:"datetimerange",label:"创建时间",format:"yyyy-MM-dd",value:Object(n["y"])(7)},food_name:{type:"input",label:"菜品/商品名称",value:"",placeholder:"请输入菜品/商品名称"},is_repeat:{type:"select",label:"已有菜品/商品",value:"",placeholder:"请选择",dataList:[{label:"是",value:1},{label:"否",value:0}]},attributes:{type:"select",label:"属性",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"商品",value:"goods"},{label:"菜品",value:"foods"}]}}},"1a24":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("dialog-message",{attrs:{show:t.visible,title:t.title,loading:t.isLoading,customClass:"ps-dialog",width:t.width},on:{"update:show":function(e){t.visible=e},"update:loading":function(e){t.isLoading=e},close:t.handleClose}},[e("div",{staticClass:"healthTagDialog"},[e("el-input",{staticClass:"ps-input",attrs:{placeholder:"请输入要查询的标签组/标签名称名称"},on:{input:t.searchHandle},model:{value:t.name,callback:function(e){t.name=e},expression:"name"}}),e("el-card",{staticClass:"m-t-20",attrs:{shadow:"never"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v(" 已选 "),e("span",[t._v(t._s(t.selectLabelIdList.length))]),t._v(" 个标签 ")])]),t._l(t.tableData,(function(a,n){return e("div",{key:n},[e("el-collapse",{model:{value:t.activeLaberList,callback:function(e){t.activeLaberList=e},expression:"activeLaberList"}},[e("el-collapse-item",{attrs:{name:a.id}},[e("template",{slot:"title"},[e("span",[t._v(" "+t._s(a.name)+" "),e("span",[t._v("（"+t._s(a.label_list.length)+"）")])]),e("span",{staticClass:"tips-r"},[e("span",{staticClass:"open"},[t._v("展开")]),e("span",{staticClass:"close"},[t._v("收起")])])]),e("div",{staticClass:"ps-flex-align-c flex-align-c flex-wrap"},[a.inputVisible?e("el-input",{ref:"saveTagInput"+a.id,refInFor:!0,staticClass:"ps-input w-100 p-r-10 p-t-5",attrs:{size:"mini",autofocus:"",maxlength:"15"},on:{blur:function(e){return t.handleInputConfirm(a)}},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleInputConfirm(a)}},model:{value:a.inputValue,callback:function(e){t.$set(a,"inputValue",e)},expression:"item.inputValue"}}):e("button-icon",{staticClass:"p-t-5",attrs:{color:"origin",type:"add"},on:{click:function(e){return t.showInput(a)}}},[t._v(" 添加标签 ")]),e("div",{staticStyle:{flex:"1"}},[e("el-checkbox-group",{attrs:{size:"mini"},model:{value:t.selectLabelIdList,callback:function(e){t.selectLabelIdList=e},expression:"selectLabelIdList"}},t._l(a.label_list,(function(n,r){return e("el-checkbox-button",{key:r,attrs:{label:n.id,disabled:n.disabled},on:{change:function(e){return t.checkboxChangge(n,a)}}},[t._v(" "+t._s(n.name)+" ")])})),1)],1)],1)],2)],1)],1)}))],2)],1),e("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[e("el-pagination",{staticClass:"ps-text",attrs:{"page-size":t.pageSize,"current-page":t.currentPage,layout:"total, prev, pager, next",total:t.totalCount,background:"","popper-class":"ps-popper-select"},on:{"current-change":t.handleCurrentChange,"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e}}})],1),e("template",{slot:"tool"},[e("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:t.isLoading},on:{click:t.clickCancleHandle}},[t._v(" 取消 ")]),e("el-button",{staticClass:"ps-btn",attrs:{disabled:t.isLoading,type:"primary"},on:{click:t.clickConfirmHandle}},[t._v(" 保存 ")])],1)])],2)},r=[],i=a("ed08");function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function l(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */l=function(){return e};var t,e={},a=Object.prototype,n=a.hasOwnProperty,r=Object.defineProperty||function(t,e,a){t[e]=a.value},i="function"==typeof Symbol?Symbol:{},s=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function f(t,e,a){return Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,a){return t[e]=a}}function p(t,e,a,n){var i=e&&e.prototype instanceof v?e:v,o=Object.create(i.prototype),l=new T(n||[]);return r(o,"_invoke",{value:S(t,a,l)}),o}function d(t,e,a){try{return{type:"normal",arg:t.call(e,a)}}catch(t){return{type:"throw",arg:t}}}e.wrap=p;var m="suspendedStart",h="suspendedYield",y="executing",g="completed",b={};function v(){}function L(){}function _(){}var w={};f(w,s,(function(){return this}));var k=Object.getPrototypeOf,x=k&&k(k($([])));x&&x!==a&&n.call(x,s)&&(w=x);var I=_.prototype=v.prototype=Object.create(w);function C(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function D(t,e){function a(r,i,l,s){var c=d(t[r],t,i);if("throw"!==c.type){var u=c.arg,f=u.value;return f&&"object"==o(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){a("next",t,l,s)}),(function(t){a("throw",t,l,s)})):e.resolve(f).then((function(t){u.value=t,l(u)}),(function(t){return a("throw",t,l,s)}))}s(c.arg)}var i;r(this,"_invoke",{value:function(t,n){function r(){return new e((function(e,r){a(t,n,e,r)}))}return i=i?i.then(r,r):r()}})}function S(e,a,n){var r=m;return function(i,o){if(r===y)throw Error("Generator is already running");if(r===g){if("throw"===i)throw o;return{value:t,done:!0}}for(n.method=i,n.arg=o;;){var l=n.delegate;if(l){var s=O(l,n);if(s){if(s===b)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(r===m)throw r=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r=y;var c=d(e,a,n);if("normal"===c.type){if(r=n.done?g:h,c.arg===b)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r=g,n.method="throw",n.arg=c.arg)}}}function O(e,a){var n=a.method,r=e.iterator[n];if(r===t)return a.delegate=null,"throw"===n&&e.iterator.return&&(a.method="return",a.arg=t,O(e,a),"throw"===a.method)||"return"!==n&&(a.method="throw",a.arg=new TypeError("The iterator does not provide a '"+n+"' method")),b;var i=d(r,e.iterator,a.arg);if("throw"===i.type)return a.method="throw",a.arg=i.arg,a.delegate=null,b;var o=i.arg;return o?o.done?(a[e.resultName]=o.value,a.next=e.nextLoc,"return"!==a.method&&(a.method="next",a.arg=t),a.delegate=null,b):o:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,b)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function N(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function $(e){if(e||""===e){var a=e[s];if(a)return a.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,i=function a(){for(;++r<e.length;)if(n.call(e,r))return a.value=e[r],a.done=!1,a;return a.value=t,a.done=!0,a};return i.next=i}}throw new TypeError(o(e)+" is not iterable")}return L.prototype=_,r(I,"constructor",{value:_,configurable:!0}),r(_,"constructor",{value:L,configurable:!0}),L.displayName=f(_,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===L||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,f(t,u,"GeneratorFunction")),t.prototype=Object.create(I),t},e.awrap=function(t){return{__await:t}},C(D.prototype),f(D.prototype,c,(function(){return this})),e.AsyncIterator=D,e.async=function(t,a,n,r,i){void 0===i&&(i=Promise);var o=new D(p(t,a,n,r),i);return e.isGeneratorFunction(a)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},C(I),f(I,u,"Generator"),f(I,s,(function(){return this})),f(I,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),a=[];for(var n in e)a.push(n);return a.reverse(),function t(){for(;a.length;){var n=a.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=$,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(N),!e)for(var a in this)"t"===a.charAt(0)&&n.call(this,a)&&!isNaN(+a.slice(1))&&(this[a]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var a=this;function r(n,r){return l.type="throw",l.arg=e,a.next=n,r&&(a.method="next",a.arg=t),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],l=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var s=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(s&&c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(t,e){for(var a=this.tryEntries.length-1;a>=0;--a){var r=this.tryEntries[a];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,b):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),b},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.finallyLoc===t)return this.complete(a.completion,a.afterLoc),N(a),b}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.tryLoc===t){var n=a.completion;if("throw"===n.type){var r=n.arg;N(a)}return r}}throw Error("illegal catch attempt")},delegateYield:function(e,a,n){return this.delegate={iterator:$(e),resultName:a,nextLoc:n},"next"===this.method&&(this.arg=t),b}},e}function s(t,e){return d(t)||p(t,e)||u(t,e)||c()}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(t,e){if(t){if("string"==typeof t)return f(t,e);var a={}.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?f(t,e):void 0}}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,n=Array(e);a<e;a++)n[a]=t[a];return n}function p(t,e){var a=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=a){var n,r,i,o,l=[],s=!0,c=!1;try{if(i=(a=a.call(t)).next,0===e){if(Object(a)!==a)return;s=!1}else for(;!(s=(n=i.call(a)).done)&&(l.push(n.value),l.length!==e);s=!0);}catch(t){c=!0,r=t}finally{try{if(!s&&null!=a.return&&(o=a.return(),Object(o)!==o))return}finally{if(c)throw r}}return l}}function d(t){if(Array.isArray(t))return t}function m(t,e,a,n,r,i,o){try{var l=t[i](o),s=l.value}catch(t){return void a(t)}l.done?e(s):Promise.resolve(s).then(n,r)}function h(t){return function(){var e=this,a=arguments;return new Promise((function(n,r){var i=t.apply(e,a);function o(t){m(i,n,r,o,l,"next",t)}function l(t){m(i,n,r,o,l,"throw",t)}o(void 0)}))}}var y={name:"selectLaber",props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:"选择标签"},width:{type:String,default:"600px"},isshow:Boolean,ruleSingleInfo:{type:Object,default:function(){return{}}}},data:function(){return{isLoading:!1,name:"",activeLaberList:[],selectLabelIdList:[],selectLabelListData:[],tableData:[],labelType:"",pageSize:3,totalCount:0,currentPage:1}},computed:{visible:{get:function(){return this.isshow},set:function(t){this.$emit("update:isshow",t)}}},created:function(){this.ruleSingleInfo.selectLabelIdList&&this.ruleSingleInfo.selectLabelIdList.length&&(this.selectLabelIdList=this.ruleSingleInfo.selectLabelIdList),this.ruleSingleInfo.selectLabelListData&&this.ruleSingleInfo.selectLabelListData.length&&(this.selectLabelListData=this.ruleSingleInfo.selectLabelListData),this.labelType=this.ruleSingleInfo.labelType,this.getLabelGroupList()},mounted:function(){},methods:{searchHandle:Object(i["d"])((function(){this.currentPage=1,this.getLabelGroupList()}),300),getLabelGroupList:function(){var t=this;return h(l().mark((function e(){var a,n,r,o,c;return l().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,a={type:t.labelType,page:t.currentPage,page_size:t.pageSize},t.name&&(a.name=t.name),e.next=5,Object(i["Z"])(t.$apis.apiBackgroundHealthyAdminLabelGroupListPost(a));case 5:if(n=e.sent,r=s(n,2),o=r[0],c=r[1],t.isLoading=!1,!o){e.next=13;break}return t.$message.error(o.message),e.abrupt("return");case 13:0===c.code?(t.totalCount=c.data.count,t.totalPageSize=t.$computedTotalPageSize(t.totalCount,t.pageSize),t.tableData=c.data.results.map((function(e){return e.inputVisible=!1,e.inputValue="",e.label_list.forEach((function(a){a.label_group_name=e.name,t.ruleSingleInfo.selectLabelAllIds&&t.ruleSingleInfo.selectLabelAllIds.length&&t.ruleSingleInfo.selectLabelAllIds.includes(a.id)&&!t.selectLabelIdList.includes(a.id)?a.disabled=!0:a.disabled=!1})),t.activeLaberList.push(e.id),e}))):t.$message.error(c.msg);case 14:case"end":return e.stop()}}),e)})))()},handleChange:function(){},checkboxChangge:function(t,e){var a=this,n=this.selectLabelIdList.indexOf(t.id);-1!==n?this.selectLabelListData.push(t):this.selectLabelListData.map((function(e,n){t.id===e.id&&a.selectLabelListData.splice(n,1)}))},handleInputConfirm:function(t){t.inputValue?this.getLabelGroupAddLabel(t):t.inputVisible=!1},showInput:function(t){var e=this;t.inputVisible=!0,this.$nextTick((function(a){e.$refs["saveTagInput"+t.id][0].$refs.input.focus()}))},getLabelGroupAddLabel:function(t){var e=this;return h(l().mark((function a(){var n,r,o,c;return l().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return e.isLoading=!0,a.next=3,Object(i["Z"])(e.$apis.apiBackgroundHealthyAdminLabelGroupAddLabelPost({name:t.inputValue,label_group_id:t.id}));case 3:if(n=a.sent,r=s(n,2),o=r[0],c=r[1],e.isLoading=!1,!o){a.next=11;break}return e.$message.error(o.message),a.abrupt("return");case 11:0===c.code?(t.inputValue="",t.inputVisible=!1,e.getLabelGroupList()):e.$message.error(c.msg);case 12:case"end":return a.stop()}}),a)})))()},clickConfirmHandle:function(){var t={selectLabelIdList:this.selectLabelIdList,selectLabelListData:this.selectLabelListData};this.$emit("selectLaberData",t),this.visible=!1},clickCancleHandle:function(){this.visible=!1},handleClose:function(t){this.visible=!1},handleCurrentChange:function(t){this.currentPage=t,this.getLabelGroupList()}}},g=y,b=(a("27c8"),a("2877")),v=Object(b["a"])(g,n,r,!1,null,null,null);e["default"]=v.exports},"27c8":function(t,e,a){"use strict";a("c4a9")},"5c85":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"super-add-ingredients container-wrapper"},[e("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formIngredients",attrs:{rules:t.formRuls,model:t.formData,size:"small"}},[e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("基本信息")])]),e("div",{staticStyle:{padding:"0 20px"}},[e("el-form-item",{staticClass:"block-label form-content-flex",attrs:{label:"食材图片",prop:""}},[e("div",{},[e("div",{staticClass:"inline-block upload-w"},[e("el-upload",{directives:[{name:"loading",rawName:"v-loading",value:t.uploading,expression:"uploading"}],ref:"fileUpload",staticClass:"file-upload",attrs:{"element-loading-text":"上传中",drag:"",action:t.serverUrl,data:t.uploadParams,"file-list":t.fileLists,"on-success":t.uploadSuccess,"before-upload":t.beforeFoodImgUpload,limit:1,multiple:!1,"show-file-list":!1,headers:t.headersOpts,accept:".jpeg,.jpg,.png,.bmp"}},[t._t("default",(function(){return[t.formData.imageList.length?t._e():e("div",{staticClass:"upload-t"},[e("i",{staticClass:"el-icon-circle-plus"}),e("div",{staticClass:"el-upload__text"},[e("span",{},[t._v("上传食材图片")])])]),t.formData.imageList.length?e("el-image",{staticClass:"el-upload-dragger",attrs:{src:t.formData.imageList[0],fit:"contain"},on:{click:t.removeFoodImg}}):t._e()]}))],2)],1),e("div",{staticClass:"inline-block upload-tips"},[e("span",{staticStyle:{"padding-left":"2px"}},[t._v("上传：食材图片。")]),e("br"),t._v(" 建议图片需清晰，图片内容与名称相符。"),e("br"),t._v(" 仅支持jpg、png、bmp格式，大小不超过5M ")])])])],1),e("div",{},[e("div",{staticStyle:{width:"48%",padding:"0 20px"}},[e("el-form-item",{staticClass:"block-label form-content-flex",attrs:{label:"食材名称",prop:"name"}},[e("el-input",{staticClass:"ps-input",staticStyle:{width:"80%"},attrs:{placeholder:"请输入食材名称",maxlength:"30"},model:{value:t.formData.name,callback:function(e){t.$set(t.formData,"name",e)},expression:"formData.name"}}),e("el-tooltip",{attrs:{effect:"dark",content:"增加食材别名",placement:"top"}},[e("img",{staticClass:"add-btn-img",attrs:{src:a("a851"),alt:""},on:{click:t.addAliasName}})])],1),t.formData.aliasName.length?e("div",[e("el-form-item",{staticClass:"block-label",attrs:{label:"食材别名"}},t._l(t.formData.aliasName,(function(n,r){return e("el-form-item",{key:r,class:[r>0?"m-t-15":"","alias-name-form"],attrs:{rules:t.formRuls.aliasName,prop:"aliasName[".concat(r,"]")}},[e("el-input",{staticClass:"ps-input",staticStyle:{width:"80%"},attrs:{maxlength:"20",placeholder:"请输入食材别名"},model:{value:t.formData.aliasName[r],callback:function(e){t.$set(t.formData.aliasName,r,e)},expression:"formData.aliasName[index]"}}),e("img",{attrs:{src:a("a851"),alt:""},on:{click:t.addAliasName}}),e("img",{attrs:{src:a("1597"),alt:""},on:{click:function(e){return t.delAliasName(r)}}})],1)})),1)],1):t._e(),e("el-form-item",{staticClass:"block-label",attrs:{label:"食材类别",prop:"sort_id"}},[e("el-cascader",{staticClass:"ps-select",staticStyle:{width:"80%"},attrs:{placeholder:"请选择或输入食材类别",options:t.categoryList,"show-all-levels":!1,props:t.cascaderProps},model:{value:t.formData.sort_id,callback:function(e){t.$set(t.formData,"sort_id",e)},expression:"formData.sort_id"}}),e("el-button",{staticClass:"m-l-10",attrs:{type:"text"},on:{click:t.gotoCategory}},[t._v("添加分类")])],1),e("el-form-item",{attrs:{label:"标签",prop:""}},[e("el-button",{staticClass:"ps-origin-btn",attrs:{type:"primary",size:"small"},on:{click:t.labelClick}},[t._v(" 选择标签 ")])],1),t._l(t.formData.labelGroupInfoList,(function(a,n,r){return e("el-form-item",{key:r,attrs:{label:"".concat(n,":"),prop:""}},t._l(a,(function(a,r){return e("el-tag",{key:r,staticClass:"m-r-5 collapse-data",attrs:{size:"medium",effect:"plain",type:"info",color:"#fff",closable:""},on:{close:function(e){return t.closeTag(n,r,a)}}},[t._v(" "+t._s(a.name)+" ")])})),1)}))],2)])]),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("营养信息 "),e("span",{staticClass:"tip-o-7"},[t._v("（每100克所含的营养信息）")]),e("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:t.formData.is_enable_nutrition,callback:function(e){t.$set(t.formData,"is_enable_nutrition",e)},expression:"formData.is_enable_nutrition"}})],1)]),t.formData.is_enable_nutrition?e("div",{staticClass:"table-content"},[t._l(t.currentNutritionList,(function(a){return[e("div",{key:a.key,staticClass:"nutrition-item"},[e("div",{staticClass:"nutrition-label"},[t._v(t._s(a.name+"："))]),e("el-form-item",{attrs:{prop:a.key,rules:t.formRuls.nutrition}},[e("el-input",{staticClass:"ps-input",staticStyle:{width:"120px"},model:{value:t.formData[a.key],callback:function(e){t.$set(t.formData,a.key,e)},expression:"formData[nutrition.key]"}}),e("span",{staticStyle:{"margin-left":"10px"}},[t._v(t._s(a.unit))])],1)],1)]})),e("div",{staticClass:"text-center pointer"},[e("span",{staticStyle:{color:"#027DB4"},on:{click:function(e){t.showAll=!t.showAll}}},[t._v(t._s(t.showAll?"收起":"查看更多营养信息"))])])],2):t._e()]),e("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[e("el-button",{staticStyle:{width:"120px"},on:{click:t.closeHandler}},[t._v("取消")]),e("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:t.submitHandler}},[t._v(" "+t._s("add"===t.type?"添加":"保存")+" ")])],1)]),t.selectLaberDialogVisible?e("select-laber",{attrs:{isshow:t.selectLaberDialogVisible,width:"600px",ruleSingleInfo:t.ruleSingleInfo},on:{"update:isshow":function(e){t.selectLaberDialogVisible=e},selectLaberData:t.selectLaberData}}):t._e()],1)},r=[],i=a("ed08"),o=a("015b"),l=a("1a24");function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function c(t,e){return m(t)||d(t,e)||f(t,e)||u()}function u(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(t,e){if(t){if("string"==typeof t)return p(t,e);var a={}.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?p(t,e):void 0}}function p(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,n=Array(e);a<e;a++)n[a]=t[a];return n}function d(t,e){var a=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=a){var n,r,i,o,l=[],s=!0,c=!1;try{if(i=(a=a.call(t)).next,0===e){if(Object(a)!==a)return;s=!1}else for(;!(s=(n=i.call(a)).done)&&(l.push(n.value),l.length!==e);s=!0);}catch(t){c=!0,r=t}finally{try{if(!s&&null!=a.return&&(o=a.return(),Object(o)!==o))return}finally{if(c)throw r}}return l}}function m(t){if(Array.isArray(t))return t}function h(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */h=function(){return e};var t,e={},a=Object.prototype,n=a.hasOwnProperty,r=Object.defineProperty||function(t,e,a){t[e]=a.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function u(t,e,a){return Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,a){return t[e]=a}}function f(t,e,a,n){var i=e&&e.prototype instanceof v?e:v,o=Object.create(i.prototype),l=new T(n||[]);return r(o,"_invoke",{value:S(t,a,l)}),o}function p(t,e,a){try{return{type:"normal",arg:t.call(e,a)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var d="suspendedStart",m="suspendedYield",y="executing",g="completed",b={};function v(){}function L(){}function _(){}var w={};u(w,o,(function(){return this}));var k=Object.getPrototypeOf,x=k&&k(k($([])));x&&x!==a&&n.call(x,o)&&(w=x);var I=_.prototype=v.prototype=Object.create(w);function C(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function D(t,e){function a(r,i,o,l){var c=p(t[r],t,i);if("throw"!==c.type){var u=c.arg,f=u.value;return f&&"object"==s(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){a("next",t,o,l)}),(function(t){a("throw",t,o,l)})):e.resolve(f).then((function(t){u.value=t,o(u)}),(function(t){return a("throw",t,o,l)}))}l(c.arg)}var i;r(this,"_invoke",{value:function(t,n){function r(){return new e((function(e,r){a(t,n,e,r)}))}return i=i?i.then(r,r):r()}})}function S(e,a,n){var r=d;return function(i,o){if(r===y)throw Error("Generator is already running");if(r===g){if("throw"===i)throw o;return{value:t,done:!0}}for(n.method=i,n.arg=o;;){var l=n.delegate;if(l){var s=O(l,n);if(s){if(s===b)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(r===d)throw r=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r=y;var c=p(e,a,n);if("normal"===c.type){if(r=n.done?g:m,c.arg===b)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r=g,n.method="throw",n.arg=c.arg)}}}function O(e,a){var n=a.method,r=e.iterator[n];if(r===t)return a.delegate=null,"throw"===n&&e.iterator.return&&(a.method="return",a.arg=t,O(e,a),"throw"===a.method)||"return"!==n&&(a.method="throw",a.arg=new TypeError("The iterator does not provide a '"+n+"' method")),b;var i=p(r,e.iterator,a.arg);if("throw"===i.type)return a.method="throw",a.arg=i.arg,a.delegate=null,b;var o=i.arg;return o?o.done?(a[e.resultName]=o.value,a.next=e.nextLoc,"return"!==a.method&&(a.method="next",a.arg=t),a.delegate=null,b):o:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,b)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function N(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function $(e){if(e||""===e){var a=e[o];if(a)return a.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,i=function a(){for(;++r<e.length;)if(n.call(e,r))return a.value=e[r],a.done=!1,a;return a.value=t,a.done=!0,a};return i.next=i}}throw new TypeError(s(e)+" is not iterable")}return L.prototype=_,r(I,"constructor",{value:_,configurable:!0}),r(_,"constructor",{value:L,configurable:!0}),L.displayName=u(_,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===L||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,u(t,c,"GeneratorFunction")),t.prototype=Object.create(I),t},e.awrap=function(t){return{__await:t}},C(D.prototype),u(D.prototype,l,(function(){return this})),e.AsyncIterator=D,e.async=function(t,a,n,r,i){void 0===i&&(i=Promise);var o=new D(f(t,a,n,r),i);return e.isGeneratorFunction(a)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},C(I),u(I,c,"Generator"),u(I,o,(function(){return this})),u(I,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),a=[];for(var n in e)a.push(n);return a.reverse(),function t(){for(;a.length;){var n=a.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=$,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(N),!e)for(var a in this)"t"===a.charAt(0)&&n.call(this,a)&&!isNaN(+a.slice(1))&&(this[a]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var a=this;function r(n,r){return l.type="throw",l.arg=e,a.next=n,r&&(a.method="next",a.arg=t),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],l=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var s=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(s&&c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(t,e){for(var a=this.tryEntries.length-1;a>=0;--a){var r=this.tryEntries[a];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,b):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),b},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.finallyLoc===t)return this.complete(a.completion,a.afterLoc),N(a),b}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.tryLoc===t){var n=a.completion;if("throw"===n.type){var r=n.arg;N(a)}return r}}throw Error("illegal catch attempt")},delegateYield:function(e,a,n){return this.delegate={iterator:$(e),resultName:a,nextLoc:n},"next"===this.method&&(this.arg=t),b}},e}function y(t,e,a,n,r,i,o){try{var l=t[i](o),s=l.value}catch(t){return void a(t)}l.done?e(s):Promise.resolve(s).then(n,r)}function g(t){return function(){var e=this,a=arguments;return new Promise((function(n,r){var i=t.apply(e,a);function o(t){y(i,n,r,o,l,"next",t)}function l(t){y(i,n,r,o,l,"throw",t)}o(void 0)}))}}var b={name:"SuperAddIngredients",data:function(){var t=function(t,e,a){if(e){var n=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;n.test(e)?a():a(new Error("营养数据有误，仅支持两位小数"))}else a()};return{isLoading:!1,type:"add",formData:{id:"",name:"",aliasName:[],sort_id:"",ingredient_id:"",is_enable_nutrition:!1,selectLabelListData:[],selectLabelIdList:[],labelGroupInfoList:{},imageList:[]},formRuls:{name:[{required:!0,message:"食材名称不能为空",trigger:"blur"}],aliasName:[{required:!0,message:"请输入食材别名",trigger:"blur"}],category:[{required:!0,message:"请选择食材类别",trigger:"blur"}],nutrition:[{validator:t,trigger:"change"}],sort_id:[{required:!0,message:"请选择食材分类",trigger:"blur"}]},nutritionList:o["NUTRITION_LIST"],categoryList:[],cascaderProps:{label:"name",value:"id",children:"sort_list",emitPath:!1},selectLaberDialogVisible:!1,ruleSingleInfo:{},serverUrl:"/api/background/file/upload",headersOpts:{TOKEN:Object(i["B"])()},fileLists:[],uploadParams:{prefix:"super_food_img"},showAll:!1,showDialog:!1,uploading:!1}},computed:{currentNutritionList:function(){var t=[];return t=this.showAll?this.nutritionList:this.nutritionList.slice(0,4),t}},components:{selectLaber:l["default"]},created:function(){this.type=this.$route.query.type,this.initLoad()},mounted:function(){},methods:{initLoad:function(){var t=this;return g(h().mark((function e(){var a;return h().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.getCategoryCategoryNameList();case 2:"modify"===t.type?(a=t.$decodeQuery(t.$route.query.data),t.formData.name=a.name,t.formData.aliasName=a.alias_name,t.formData.sort_id=a.sort,a.image&&(t.formData.imageList=[a.image],t.fileLists=[{url:a.image,name:a.image,status:"success",uid:a.image}]),t.categoryList.map((function(e){e.sort_list.length&&e.sort_list.map((function(e){e.id&&Number(e.id.split("-")[1])===a.sort&&(t.formData.sort_id=e.id)}))})),a.label.length&&t.initLabelGroup(a.label),t.formData.selectLabelListData=a.label,t.formData.selectLabelIdList=a.label.map((function(t){return t.id})),t.formData.id=a.id,t.formData.is_enable_nutrition=!!a.is_enable_nutrition,t.setNutritonData(a)):t.setNutritonData({});case 3:case"end":return e.stop()}}),e)})))()},searchHandle:Object(i["d"])((function(){this.currentPage=1}),300),setNutritonData:function(t){var e=this;t.nutrition_info||(t.nutrition_info={});var a=t.nutrition_info.element?JSON.parse(Object(i["R"])(t.nutrition_info.element)):{},n=t.nutrition_info.vitamin?JSON.parse(Object(i["R"])(t.nutrition_info.vitamin)):{};o["NUTRITION_LIST"].forEach((function(r){"default"===r.type&&e.$set(e.formData,r.key,t.nutrition_info[r.key]?t.nutrition_info[r.key]:0),"element"===r.type&&e.$set(e.formData,r.key,a[r.key]?a[r.key]:0),"vitamin"===r.type&&e.$set(e.formData,r.key,n[r.key]?n[r.key]:0)}))},getCategoryCategoryNameList:function(){var t=this;return g(h().mark((function e(){var a,n,r,o;return h().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(i["Z"])(t.$apis.apiBackgroundAdminIngredientCategoryCategoryNameListPost());case 2:if(a=e.sent,n=c(a,2),r=n[0],o=n[1],!r){e.next=9;break}return t.$message.error(r.message),e.abrupt("return");case 9:0===o.code?t.categoryList=o.data.map((function(t){return t.sort_list.length&&(t.sort_list=t.sort_list.map((function(e){return e.id=t.id+"-"+e.id,e}))),t})):t.$message.error(o.msg);case 10:case"end":return e.stop()}}),e)})))()},formatParams:function(){var t=this,e={name:this.formData.name,label_list:this.formData.selectLabelIdList,alias_name:this.formData.aliasName,is_enable_nutrition:this.formData.is_enable_nutrition?1:0};if(this.formData.ingredient_id&&"add"===this.type&&(e.ingredient_id=this.formData.ingredient_id),this.formData.sort_id)if(this.formData.sort_id.indexOf("-")>-1){var a=this.formData.sort_id.split("-");e.sort_id=a[1]}else e.sort_id=this.formData.sort_id;if("modify"===this.type&&(e.id=this.formData.id),this.formData.is_enable_nutrition){var n={},r={};o["NUTRITION_LIST"].forEach((function(a){var i=t.formData[a.key]?t.formData[a.key]:0;"default"===a.type&&(e[a.key]=i),"element"===a.type&&(n[a.key]=i),"vitamin"===a.type&&(r[a.key]=i)})),e.element=JSON.stringify(n),e.vitamin=JSON.stringify(r)}return this.formData.imageList.length&&(e.image=this.formData.imageList[0]),e},submitHandler:function(){var t=this;this.$refs.formIngredients.validate((function(e){if(e){if(t.isLoading)return t.$message.error("请勿重复提交！");"modify"===t.type?t.modifyIngredients():t.addIngredients()}}))},addIngredients:function(){var t=this;return g(h().mark((function e(){var a,n,r,o;return h().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(i["Z"])(t.$apis.apiBackgroundAdminIngredientAddPost(t.formatParams()));case 3:if(a=e.sent,n=c(a,2),r=n[0],o=n[1],t.isLoading=!1,!r){e.next=11;break}return t.$message.error(r.message),e.abrupt("return");case 11:0===o.code?(t.$message.success(o.msg),t.$closeCurrentTab(t.$route.path)):2===o.code?(t.formData.ingredient_id=o.data.ingredient_id,t.$confirm(o.msg,"提示",{confirmButtonText:"覆 盖",cancelButtonText:t.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var e=g(h().mark((function e(a,n,r){return h().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("confirm"!==a){e.next=7;break}return n.confirmButtonLoading=!0,e.next=4,t.addIngredients();case 4:n.confirmButtonLoading=!1,e.next=8;break;case 7:n.confirmButtonLoading||r();case 8:case"end":return e.stop()}}),e)})));function a(t,a,n){return e.apply(this,arguments)}return a}()}).then((function(t){})).catch((function(e){t.formData.ingredient_id=""}))):t.$message.error(o.msg);case 12:case"end":return e.stop()}}),e)})))()},labelClick:function(){this.ruleSingleInfo={labelType:"ingredient",selectLabelIdList:this.formData.selectLabelIdList,selectLabelListData:this.formData.selectLabelListData},this.selectLaberDialogVisible=!0},closeTag:function(t,e,a){var n=this.formData.selectLabelIdList.indexOf(a.id),r=this.formData.selectLabelListData.indexOf(a);this.formData.selectLabelIdList.splice(n,1),this.formData.selectLabelListData.splice(r,1),this.formData.labelGroupInfoList={},this.initLabelGroup(this.formData.selectLabelListData)},selectLaberData:function(t){this.formData.selectLabelIdList=t.selectLabelIdList,this.formData.selectLabelListData=t.selectLabelListData,this.formData.labelGroupInfoList={},this.initLabelGroup(this.formData.selectLabelListData)},initLabelGroup:function(t){var e=this;t.forEach((function(t){e.formData.labelGroupInfoList[t.label_group_name]||(e.formData.labelGroupInfoList[t.label_group_name]=[]),e.formData.labelGroupInfoList[t.label_group_name]&&!e.formData.labelGroupInfoList[t.label_group_name].includes(t)&&e.formData.labelGroupInfoList[t.label_group_name].push(t)}))},modifyIngredients:function(){var t=this;return g(h().mark((function e(){var a,n,r,o;return h().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(i["Z"])(t.$apis.apiBackgroundAdminIngredientModifyPost(t.formatParams()));case 3:if(a=e.sent,n=c(a,2),r=n[0],o=n[1],t.isLoading=!1,!r){e.next=11;break}return t.$message.error(r.message),e.abrupt("return");case 11:0===o.code?(t.$message.success(o.msg),t.$closeCurrentTab(t.$route.path)):t.$message.error(o.msg);case 12:case"end":return e.stop()}}),e)})))()},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,a,n){"confirm"===e?(a.confirmButtonLoading=!0,t.$closeCurrentTab(t.$route.path),a.confirmButtonLoading=!1):a.confirmButtonLoading||n()}}).then((function(t){})).catch((function(t){}))},gotoCategory:function(){this.$router.push({name:"SuperImportIngredientsCategory"})},addAliasName:function(){this.formData.aliasName.push("")},delAliasName:function(t){this.formData.aliasName.splice(t,1)},removeFoodImg:function(t){this.formData.imageList.splice(t,1),this.fileLists.splice(t,1)},uploadSuccess:function(t,e,a){this.uploading=!1,0===t.code?(this.fileLists=a,this.formData.imageList=[t.data.public_url]):this.$message.error(t.msg)},beforeFoodImgUpload:function(t){var e=[".jpeg",".jpg",".png",".bmp"],a=t.size/1024/1024<=2;return e.includes(Object(i["A"])(t.name))?a?void(this.uploading=!0):(this.$message.error("上传图片大小不能超过 2MB!"),!1):(this.$message.error("上传图片只能是 JPG 格式或者PNG格式!"),!1)},clickConfirmHandle:function(t){var e=this;return g(h().mark((function t(){return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.clickCancleHandle(),t.next=3,e.$sleep(100);case 3:e.$router.push({name:"MerchantCopyFoods"});case 4:case"end":return t.stop()}}),t)})))()},clickCancleHandle:function(t){this.$closeCurrentTab(this.$route.path),this.showDialog=!1}}},v=b,L=(a("d38d"),a("2877")),_=Object(L["a"])(v,n,r,!1,null,null,null);e["default"]=_.exports},c4a9:function(t,e,a){},d38d:function(t,e,a){"use strict";a("ea1f")},ea1f:function(t,e,a){}}]);