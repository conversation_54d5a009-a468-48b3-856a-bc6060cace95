(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-member-center-PromotionalPictureSetting"],{"323ec":function(e,t,r){"use strict";r("6880")},6880:function(e,t,r){},"7ea2":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"picture-setting container-wrapper"},[t("refresh-tool",{on:{refreshPage:e.refreshHandle}}),t("div",{staticClass:"table-wrapper p-l-20 p-t-20"},[t("el-form",{ref:"memberFormRef",attrs:{model:e.memberForm,rules:e.memberFormRules,"label-width":"160px"}},[t("el-form-item",{attrs:{label:"名称：",prop:"name"}},[t("el-input",{staticClass:"ps-input w-333",attrs:{maxlength:"15","show-word-limit":""},model:{value:e.memberForm.name,callback:function(t){e.$set(e.memberForm,"name",t)},expression:"memberForm.name"}})],1),t("el-form-item",{attrs:{label:"图片：",prop:"img"}},[t("div",{staticClass:"ps-flex"},[t("el-upload",{directives:[{name:"loading",rawName:"v-loading",value:e.uploading,expression:"uploading"}],ref:"fileUpload",staticClass:"upload-w",attrs:{"element-loading-text":"上传中",action:e.serverUrl,data:e.uploadParams,"file-list":e.fileLists,"on-success":e.uploadSuccess,"before-upload":e.beforeFoodImgUpload,limit:1,multiple:!1,"show-file-list":!1,headers:e.headersOpts,accept:".jpeg,.jpg,.png,.bmp"}},[e.memberForm.img?t("img",{staticClass:"avatar",attrs:{src:e.memberForm.img}}):t("i",{staticClass:"el-icon-plus avatar-uploader-icon"})]),t("div",{staticClass:"inline-block upload-tips m-l-10"},[e._v(" 仅支持jpg,png格式，比例：333px*183px,图片最大不超过10MB ")])],1)]),t("el-form-item",{attrs:{label:"显示界面：",prop:"type"}},[t("el-select",{staticClass:"w-333",attrs:{placeholder:"请选择显示界面",multiple:""},model:{value:e.memberForm.type,callback:function(t){e.$set(e.memberForm,"type",t)},expression:"memberForm.type"}},e._l(e.typeList,(function(e){return t("el-option",{key:e.value,attrs:{label:e.name,value:e.value}})})),1)],1),t("el-form-item",{attrs:{label:"以下项目点不显示：",prop:"orgIds"}},[t("el-select",{staticClass:"ps-select w-333",attrs:{placeholder:"请选择项目点",multiple:"",filterable:"",clearable:"","collapse-tags":""},model:{value:e.memberForm.orgIds,callback:function(t){e.$set(e.memberForm,"orgIds",t)},expression:"memberForm.orgIds"}},e._l(e.orgsList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.company}})})),1)],1),"model"==e.memberForm.type?t("el-form-item",{attrs:{label:"键值：",prop:"label"}},[t("el-select",{staticClass:"ps-input w-333",attrs:{placeholder:"请选择标签"},model:{value:e.memberForm.label,callback:function(t){e.$set(e.memberForm,"label",t)},expression:"memberForm.label"}},e._l(e.keyList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1):e._e(),t("el-form-item",{attrs:{label:"优先级：",prop:"priority"}},[t("el-input",{staticClass:"ps-input w-333",attrs:{placeholder:"请输入优先级",maxlength:"10",min:0,max:100,type:"number"},model:{value:e.memberForm.priority,callback:function(t){e.$set(e.memberForm,"priority",t)},expression:"memberForm.priority"}}),t("div",{staticClass:"ps-text"},[e._v("优先级数值小的优先显示")])],1),t("el-form-item",[t("el-button",{staticClass:"ps-origin-plain-btn w-100",attrs:{size:"small"},on:{click:e.handlerCancel}},[e._v("取消")]),t("el-button",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],staticClass:"ps-origin-btn w-100 m-l-40",attrs:{size:"small",type:"primary"},on:{click:function(t){return e.saveSetting("release")}}},[e._v("确认发布")]),t("el-button",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],staticClass:"ps-origin-btn w-100 m-l-40",attrs:{size:"small",type:"primary"},on:{click:function(t){return e.saveSetting("noRelease")}}},[e._v("保存不发布")])],1)],1)],1)],1)},i=[],o=r("ed08");function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,i=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},l=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function m(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{m({},"")}catch(e){m=function(e,t,r){return e[t]=r}}function p(e,t,r,n){var o=t&&t.prototype instanceof b?t:b,a=Object.create(o.prototype),s=new C(n||[]);return i(a,"_invoke",{value:E(e,r,s)}),a}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=p;var h="suspendedStart",d="suspendedYield",g="executing",v="completed",y={};function b(){}function w(){}function L(){}var x={};m(x,l,(function(){return this}));var F=Object.getPrototypeOf,_=F&&F(F(I([])));_&&_!==r&&n.call(_,l)&&(x=_);var k=L.prototype=b.prototype=Object.create(x);function O(e){["next","throw","return"].forEach((function(t){m(e,t,(function(e){return this._invoke(t,e)}))}))}function $(e,t){function r(i,o,s,l){var c=f(e[i],e,o);if("throw"!==c.type){var u=c.arg,m=u.value;return m&&"object"==a(m)&&n.call(m,"__await")?t.resolve(m.__await).then((function(e){r("next",e,s,l)}),(function(e){r("throw",e,s,l)})):t.resolve(m).then((function(e){u.value=e,s(u)}),(function(e){return r("throw",e,s,l)}))}l(c.arg)}var o;i(this,"_invoke",{value:function(e,n){function i(){return new t((function(t,i){r(e,n,t,i)}))}return o=o?o.then(i,i):i()}})}function E(t,r,n){var i=h;return function(o,a){if(i===g)throw Error("Generator is already running");if(i===v){if("throw"===o)throw a;return{value:e,done:!0}}for(n.method=o,n.arg=a;;){var s=n.delegate;if(s){var l=j(s,n);if(l){if(l===y)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===h)throw i=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=g;var c=f(t,r,n);if("normal"===c.type){if(i=n.done?v:d,c.arg===y)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(i=v,n.method="throw",n.arg=c.arg)}}}function j(t,r){var n=r.method,i=t.iterator[n];if(i===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,j(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var o=f(i,t.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,y;var a=o.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,y):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function P(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function S(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function C(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(P,this),this.reset(!0)}function I(t){if(t||""===t){var r=t[l];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,o=function r(){for(;++i<t.length;)if(n.call(t,i))return r.value=t[i],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(a(t)+" is not iterable")}return w.prototype=L,i(k,"constructor",{value:L,configurable:!0}),i(L,"constructor",{value:w,configurable:!0}),w.displayName=m(L,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,L):(e.__proto__=L,m(e,u,"GeneratorFunction")),e.prototype=Object.create(k),e},t.awrap=function(e){return{__await:e}},O($.prototype),m($.prototype,c,(function(){return this})),t.AsyncIterator=$,t.async=function(e,r,n,i,o){void 0===o&&(o=Promise);var a=new $(p(e,r,n,i),o);return t.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},O(k),m(k,u,"Generator"),m(k,l,(function(){return this})),m(k,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=I,C.prototype={constructor:C,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(S),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function i(n,i){return s.type="throw",s.arg=t,r.next=n,i&&(r.method="next",r.arg=e),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var l=n.call(a,"catchLoc"),c=n.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,y):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),S(r),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;S(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:I(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),y}},t}function l(e,t){return f(e)||p(e,t)||u(e,t)||c()}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(e,t){if(e){if("string"==typeof e)return m(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?m(e,t):void 0}}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function p(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,o,a,s=[],l=!0,c=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=o.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){c=!0,i=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw i}}return s}}function f(e){if(Array.isArray(e))return e}function h(e,t,r,n,i,o,a){try{var s=e[o](a),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,i)}function d(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var o=e.apply(t,r);function a(e){h(o,n,i,a,s,"next",e)}function s(e){h(o,n,i,a,s,"throw",e)}a(void 0)}))}}var g={name:"PromotionalPictureSetting",props:{},data:function(){var e=function(e,t,r){var n=/^\d+$/;if(n.test(t)){if(t>100)return r(new Error("请输入小于100的数字"));r()}else r(new Error("请输入小于100的数字"))},t=function(e,t,r){if(""===t)return r(new Error("不能为空"));var n=/^[1-9][0-9]*$/;n.test(t)?r():r(new Error("请输入正整数"))};return{isLoading:!1,typeList:[{name:"首页",value:"front_page"},{name:"预约点餐支付成功界面",value:"reservation"},{name:"报餐支付成功界面",value:"report_meal"},{name:"充值成功界面",value:"recharge"},{name:"客服与帮助界面",value:"service_agent"}],memberForm:{id:"",name:"",img:"",imageList:[],type:"",orgIds:[],priority:""},memberFormRules:{balanceNum:[{validator:t,trigger:"blur"}],priority:[{required:!1,validator:e,trigger:"blur"}]},serverUrl:"/api/background/file/upload",headersOpts:{TOKEN:Object(o["B"])()},fileLists:[],uploadParams:{prefix:"super_food_img"},uploading:!1,type:"",orgsList:[]}},created:function(){this.initData()},mounted:function(){},methods:{initData:function(){if(this.$route.query&&(this.type=this.$route.query.type||"","edit"===this.type)){var e=JSON.parse(Object(o["x"])("PictureSettingInfo")||"{}");if(e){var t=Object(o["f"])(this.memberForm);t.name=e.name||"",t.img=e.url||"",t.imageList=e.url?[e.url]:[],t.type=e.ui_type||"",t.orgIds=e.company||"",t.priority=e.priority||"","edit"===this.type&&(t.id=e.id),this.$set(this,"memberForm",t)}}this.getOrgList()},refreshHandle:function(){this.currentPage=1,this.$refs.searchRef.resetForm(),this.tableData=[],this.initLoad()},saveSetting:function(e){var t=this;this.$refs.memberFormRef.validate((function(r){if(r){var n={name:t.memberForm.name,url:t.memberForm.img?t.memberForm.img:"",ui_type:t.memberForm.type,company:t.memberForm.orgIds,priority:parseInt(t.memberForm.priority),is_release:"release"===e};"edit"===t.type&&(n.id=t.memberForm.id);var i="edit"===t.type?t.$apis.apiBackgroundMemberPromotionalPictureModifyPost(n):t.$apis.apiBackgroundMemberPromotionalPictureAddPost(n);t.confirmOperation(i)}}))},confirmOperation:function(e){var t=this;return d(s().mark((function r(){var n,i,a,c;return s().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!t.isLoading){r.next=2;break}return r.abrupt("return");case 2:return t.isLoading=!0,r.next=5,Object(o["Z"])(e);case 5:if(n=r.sent,i=l(n,2),a=i[0],c=i[1],t.isLoading=!1,!a){r.next=12;break}return r.abrupt("return",t.$message.error(a.message||"保存失败"));case 12:0===c.code?(t.$message.success("保存成功"),t.$closeCurrentTab(t.$route.path)):t.$message.error(c.msg);case 13:case"end":return r.stop()}}),r)})))()},getMemberLevel:function(){var e=this;return d(s().mark((function t(){var r;return s().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,e.$apis.apiBackgroundMemberMemberGradeListPost({page:1,page_size:99999});case 3:r=t.sent,e.isLoading=!1,0===r.code?e.levelList=r.data.results:e.$message.error(r.msg);case 6:case"end":return t.stop()}}),t)})))()},tabClick:function(){},hanlderSwitchChange:function(e,t){"isReservation"===t&&this.$set(this.memberForm,"deadlineTimes",e?"1":""),"isBalance"===t&&this.$set(this.memberForm,"balanceNum",e?"20":""),"isMealReporting"===t&&this.$set(this.memberForm,"mealReportingHour",e?"1":"")},handlerCancel:function(){this.$router.back()},beforeFoodImgUpload:function(e){var t=[".jpeg",".jpg",".png",".bmp"],r=e.size/1024/1024<=10;return t.includes(Object(o["A"])(e.name))?r?void(this.uploading=!0):(this.$message.error("上传图片大小不能超过 10MB!"),!1):(this.$message.error("上传图片只能是 JPG 格式或者PNG格式!"),!1)},uploadSuccess:function(e,t,r){this.uploading=!1,e&&0===e.code?(this.fileLists=r,this.memberForm.img=e.data.public_url,this.memberForm.imageList=[e.data.public_url]):(this.memberForm.img="",this.$message.error(e.msg))},removeFoodImg:function(e){this.memberForm.imageList.splice(e,1),this.fileLists.splice(e,1)},getOrgList:function(){var e=this;return d(s().mark((function t(){return s().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$apis.apiBackgroundAdminOrganizationListPost({parent__is_null:"1",status:"enable",page:1,page_size:999999}).then((function(t){0===t.code?e.orgsList=t.data.results||[]:e.$message.error(t.msg)}));case 1:case"end":return t.stop()}}),t)})))()}}},v=g,y=(r("323ec"),r("2877")),b=Object(y["a"])(v,n,i,!1,null,"20a575bc",null);t["default"]=b.exports}}]);