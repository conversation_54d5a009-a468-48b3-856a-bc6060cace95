(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-components-EditRecipes","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-components-TemplateDialog","view-merchant-meal-management-meal-report-MealPackageRule"],{"0cda":function(e,t,r){},"227a":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"EditRecipes-form"},[t("el-form",{ref:"form",attrs:{model:e.form,"label-width":"120px",rules:e.rules}},[t("el-form-item",{attrs:{label:"日期"}},[t("span",[e._v(e._s(e.dateString))])]),t("el-form-item",{attrs:{label:"菜谱名称",prop:"name"}},[t("el-input",{model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),t("el-form-item",{attrs:{label:"设备类型：",prop:"device_types"}},[t("el-select",{staticClass:"ps-select w-350",attrs:{multiple:"",placeholder:"请选择设备类型","popper-class":"ps-popper-select"},on:{change:e.deviceTypeChange},model:{value:e.form.device_types,callback:function(t){e.$set(e.form,"device_types",t)},expression:"form.device_types"}},e._l(e.deviceArr,(function(e){return t("el-option",{key:e.key,attrs:{label:e.name,value:e.key}})})),1)],1),t("el-form-item",{attrs:{label:"设备型号：",prop:e.isDisabledModel?"":"device_model"}},[t("el-select",{staticClass:"ps-select w-350",attrs:{multiple:"",placeholder:"请选择设备型号","popper-class":"ps-popper-select",disabled:e.isDisabledModel},model:{value:e.form.device_model,callback:function(t){e.$set(e.form,"device_model",t)},expression:"form.device_model"}},e._l(e.deviceModelList,(function(e){return t("el-option",{key:e.key,attrs:{label:e.key,value:e.key}})})),1)],1),t("el-form-item",{attrs:{label:"可见范围",prop:"use_user_groups"}},[t("el-select",{staticClass:"ps-select w-350",attrs:{disabled:e.isDisabledGroup,multiple:"","collapse-tags":""},model:{value:e.form.use_user_groups,callback:function(t){e.$set(e.form,"use_user_groups",t)},expression:"form.use_user_groups"}},e._l(e.groupList,(function(e){return t("el-option",{key:e.id,attrs:{value:e.id,label:e.group_name}})})),1),t("el-checkbox",{staticClass:"ps-checkbox m-l-20",attrs:{disabled:e.isDisabledGroup},model:{value:e.form.applied_to_visitor,callback:function(t){e.$set(e.form,"applied_to_visitor",t)},expression:"form.applied_to_visitor"}},[e._v("游客")])],1),t("el-form-item",{attrs:{label:"",prop:"","label-width":"50px"}},[t("el-radio-group",{on:{change:e.changeCreateType},model:{value:e.form.create_type,callback:function(t){e.$set(e.form,"create_type",t)},expression:"form.create_type"}},[t("el-radio",{staticClass:"ps-checkbox",attrs:{label:"CUSTOM",name:"CUSTOM"}},[e._v("自定义创建")]),t("el-radio",{staticClass:"ps-checkbox",attrs:{label:"INTELLECT",name:"INTELLECT"}},[e._v("智能生成")]),t("el-radio",{staticClass:"ps-checkbox",attrs:{label:"TEMPLATE",name:"TEMPLATE"}},[e._v("选择模板")])],1),"INTELLECT"===e.form.create_type||"TEMPLATE"===e.form.create_type?t("div",[t("el-checkbox",{staticClass:"ps-checkbox m-l-20 m-r-6",attrs:{disabled:""},model:{value:e.form.checked_number,callback:function(t){e.$set(e.form,"checked_number",t)},expression:"form.checked_number"}},[e._v("就餐人数")]),t("el-form-item",{staticClass:"inline-block",attrs:{label:"",prop:"diners_number",rules:e.form.is_nutrition_guidance?[]:e.rules.diners_number,"label-width":"10px"}},[t("el-input",{staticClass:"w-100 m-r-6",attrs:{disabled:e.form.is_nutrition_guidance},model:{value:e.form.diners_number,callback:function(t){e.$set(e.form,"diners_number",t)},expression:"form.diners_number"}}),e._v("人 ")],1),t("el-checkbox",{staticClass:"ps-checkbox m-l-30 m-r-6",model:{value:e.form.use_price,callback:function(t){e.$set(e.form,"use_price",t)},expression:"form.use_price"}},[e._v("餐标/物价")]),t("el-form-item",{staticClass:"inline-block",attrs:{label:"",prop:"food_price",rules:e.form.use_price?e.rules.food_price:[],"label-width":"10px"}},[t("el-input",{staticClass:"w-100 m-r-6",attrs:{disabled:!e.form.use_price},model:{value:e.form.food_price,callback:function(t){e.$set(e.form,"food_price",t)},expression:"form.food_price"}}),e._v("元/人 ")],1),"INTELLECT"===e.form.create_type?t("el-checkbox",{staticClass:"ps-checkbox m-l-20",model:{value:e.form.seasonal_priority,callback:function(t){e.$set(e.form,"seasonal_priority",t)},expression:"form.seasonal_priority"}},[e._v("应季")]):e._e(),"TEMPLATE"===e.form.create_type?t("div",{staticStyle:{width:"90%"}},[t("el-form-item",{directives:[{name:"loading",rawName:"v-loading",value:e.loadingTemplate,expression:"loadingTemplate"}],attrs:{label:"",prop:"menu_template_id","label-width":"0px"}},[t("el-table",{ref:"templateList",staticStyle:{"margin-top":"20px"},attrs:{data:e.templateList,"max-height":"400px",stripe:"",border:"","header-row-class-name":"ps-table-header-row"}},e._l(e.templateSettings,(function(r){return t("table-column",{key:r.key,attrs:{col:r},scopedSlots:e._u([{key:"radio",fn:function(r){var n=r.row;return[t("el-radio",{staticClass:"recipes-radio",attrs:{label:n.id},model:{value:e.form.menu_template_id,callback:function(t){e.$set(e.form,"menu_template_id",t)},expression:"form.menu_template_id"}})]}},{key:"operation",fn:function(r){var n=r.row;return[t("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.showTemplateHandle(n)}}},[e._v("预览")])]}}],null,!0)})})),1)],1),t("div",{staticClass:"block",staticStyle:{"text-align":"right"}},[t("pagination",{attrs:{onPaginationChange:e.onPaginationChange,"current-page":e.templateCurrentPage,"page-size":e.templatePageSize,layout:"total, prev, pager, next, jumper",total:e.templateCount},on:{"update:currentPage":function(t){e.templateCurrentPage=t},"update:current-page":function(t){e.templateCurrentPage=t},"update:pageSize":function(t){e.templatePageSize=t},"update:page-size":function(t){e.templatePageSize=t}}})],1)],1):e._e()],1):e._e()],1),t("el-form-item",{attrs:{label:"是否沿用菜谱",prop:"is_continue_to_used"}},[t("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:e.form.is_continue_to_used,callback:function(t){e.$set(e.form,"is_continue_to_used",t)},expression:"form.is_continue_to_used"}})],1),t("el-form-item",{attrs:{label:"是否作为意向菜谱展示",prop:"is_intent_menu","label-width":"160px"}},[t("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:e.form.is_intent_menu,callback:function(t){e.$set(e.form,"is_intent_menu",t)},expression:"form.is_intent_menu"}})],1)],1),t("template-dialog",{ref:"dialogRef",attrs:{"info-data":e.dialogData},model:{value:e.showDialog,callback:function(t){e.showDialog=t},expression:"showDialog"}}),e.selectModelDialogVisible?t("select-model-dialog",{ref:"selectModelDialog",attrs:{isshow:e.selectModelDialogVisible,formDataDialog:e.selectModelDialogInfo,isModify:!1,width:"900px"},on:{"update:isshow":function(t){e.selectModelDialogVisible=t},clickSelect:e.clickSelect}}):e._e()],1)},i=[],a=r("5a0c"),o=r("e925"),s=r("322d"),l=r("b9c1");function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function u(e,t){return p(e)||d(e,t)||g(e,t)||f()}function f(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,o,s=[],l=!0,c=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){c=!0,i=e}finally{try{if(!l&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return s}}function p(e){if(Array.isArray(e))return e}function m(e){return v(e)||y(e)||g(e)||h()}function h(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function g(e,t){if(e){if("string"==typeof e)return b(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?b(e,t):void 0}}function y(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function v(e){if(Array.isArray(e))return b(e)}function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function _(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */_=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,i=Object.defineProperty||function(e,t,r){e[t]=r.value},a="function"==typeof Symbol?Symbol:{},o=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",l=a.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function f(e,t,r,n){var a=t&&t.prototype instanceof v?t:v,o=Object.create(a.prototype),s=new O(n||[]);return i(o,"_invoke",{value:T(e,r,s)}),o}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var p="suspendedStart",m="suspendedYield",h="executing",g="completed",y={};function v(){}function b(){}function w(){}var k={};u(k,o,(function(){return this}));var x=Object.getPrototypeOf,L=x&&x(x(A([])));L&&L!==r&&n.call(L,o)&&(k=L);var D=w.prototype=v.prototype=Object.create(k);function E(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function S(e,t){function r(i,a,o,s){var l=d(e[i],e,a);if("throw"!==l.type){var u=l.arg,f=u.value;return f&&"object"==c(f)&&n.call(f,"__await")?t.resolve(f.__await).then((function(e){r("next",e,o,s)}),(function(e){r("throw",e,o,s)})):t.resolve(f).then((function(e){u.value=e,o(u)}),(function(e){return r("throw",e,o,s)}))}s(l.arg)}var a;i(this,"_invoke",{value:function(e,n){function i(){return new t((function(t,i){r(e,n,t,i)}))}return a=a?a.then(i,i):i()}})}function T(t,r,n){var i=p;return function(a,o){if(i===h)throw Error("Generator is already running");if(i===g){if("throw"===a)throw o;return{value:e,done:!0}}for(n.method=a,n.arg=o;;){var s=n.delegate;if(s){var l=C(s,n);if(l){if(l===y)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===p)throw i=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=h;var c=d(t,r,n);if("normal"===c.type){if(i=n.done?g:m,c.arg===y)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(i=g,n.method="throw",n.arg=c.arg)}}}function C(t,r){var n=r.method,i=t.iterator[n];if(i===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,C(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var a=d(i,t.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,y;var o=a.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,y):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function $(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function P(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach($,this),this.reset(!0)}function A(t){if(t||""===t){var r=t[o];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,a=function r(){for(;++i<t.length;)if(n.call(t,i))return r.value=t[i],r.done=!1,r;return r.value=e,r.done=!0,r};return a.next=a}}throw new TypeError(c(t)+" is not iterable")}return b.prototype=w,i(D,"constructor",{value:w,configurable:!0}),i(w,"constructor",{value:b,configurable:!0}),b.displayName=u(w,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,u(e,l,"GeneratorFunction")),e.prototype=Object.create(D),e},t.awrap=function(e){return{__await:e}},E(S.prototype),u(S.prototype,s,(function(){return this})),t.AsyncIterator=S,t.async=function(e,r,n,i,a){void 0===a&&(a=Promise);var o=new S(f(e,r,n,i),a);return t.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},E(D),u(D,l,"Generator"),u(D,o,(function(){return this})),u(D,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=A,O.prototype={constructor:O,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(P),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function i(n,i){return s.type="throw",s.arg=t,r.next=n,i&&(r.method="next",r.arg=e),!!i}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],s=o.completion;if("root"===o.tryLoc)return i("end");if(o.tryLoc<=this.prev){var l=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return i(o.catchLoc,!0);if(this.prev<o.finallyLoc)return i(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return i(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return i(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=e,o.arg=t,a?(this.method="next",this.next=a.finallyLoc,y):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),P(r),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;P(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:A(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),y}},t}function w(e,t,r,n,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,i)}function k(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var a=e.apply(t,r);function o(e){w(a,n,i,o,s,"next",e)}function s(e){w(a,n,i,o,s,"throw",e)}o(void 0)}))}}var x={name:"EditRecipes",components:{TemplateDialog:s["default"],selectModelDialog:l["default"]},props:{editData:{type:Object},dataType:{type:String,default:""}},data:function(){var e=function(e,t,r){Object(o["b"])(t)?r():r(new Error("仅支持输入整数"))},t=function(e,t,r){Object(o["m"])(t)?r():r(new Error("格式错误"))};return{rules:{name:[{required:!0,message:"请输入名称",trigger:"blur"}],device_types:[{required:!0,message:"请选择设备类型",trigger:"change"}],device_model:[{required:!0,message:"请选择设备型号",trigger:"change"}],diners_number:[{required:!0,message:"请输入就餐人数",trigger:"change"},{validator:e,trigger:"change"}],food_price:[{required:!0,message:"请输入餐标/物价",trigger:"change"},{validator:t,trigger:"change"}],menu_template_id:[{required:!0,message:"请选择模板",trigger:"change"}]},dateString:"",deviceArr:[],apply_groups:[],deviceModelList:[],groupList:[],organizations:[],form:{id:-1,name:"",device_types:[],device_model:[],use_organizations:"",use_user_groups:"",applied_to_visitor:!1,is_continue_to_used:!1,is_intent_menu:!1,create_type:"CUSTOM",checked_number:!0,diners_number:"",is_nutrition_guidance:!1,use_price:!0,food_price:"",seasonal_priority:!1,menu_template_id:""},isDisabledModel:!1,isDisabledGroup:!0,templateList:[],templateSettings:[{label:"",key:"radio",type:"slot",slotName:"radio",width:"50px"},{label:"模板名称",key:"name"},{label:"创建时间",key:"create_time"},{label:"备注",key:"remark",showTooltip:!0},{label:"操作",key:"operation",type:"slot",slotName:"operation"}],loadingTemplate:!1,templateCount:0,templateCurrentPage:1,templatePageSize:10,dialogData:[],showDialog:!1,selectModelDialogVisible:!1,selectModelDialogInfo:{},nutritionGuidanceInfo:null}},mounted:function(){this.initLoad()},methods:{initLoad:function(){var e=this;return k(_().mark((function t(){return _().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.handleDateString(),t.next=3,e.userGroupList();case 3:return t.next=5,e.getOrganizationList();case 5:return t.next=7,e.getOrgDeviceList();case 7:e.setRawData();case 8:case"end":return t.stop()}}),t)})))()},deviceTypeChange:function(){this.form.device_model=[],this.$refs.form.clearValidate(),this.getDeviceModel()},getDeviceModel:function(){var e=this;return k(_().mark((function t(){var r,n,i;return _().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(r=["H5","MAPP","ZNC"],e.isDisabledGroup=!0,r.map((function(t){e.form.device_types.includes(t)&&(e.isDisabledGroup=!1)})),n=e.form.device_types.filter((function(e){return"H5"!==e&&"MAPP"!==e})),n.length){t.next=9;break}return e.isDisabledModel=!0,t.abrupt("return");case 9:e.isDisabledModel=!1;case 10:return t.next=12,e.$apis.apiBackgroundDeviceDeviceDeviceModelPost({device_types:n});case 12:i=t.sent,0===i.code?e.deviceModelList=i.data:e.$message.error(i.msg);case 14:case"end":return t.stop()}}),t)})))()},getOrgDeviceList:function(){var e=this;return k(_().mark((function t(){var r;return _().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundDeviceDeviceDeviceTypePost({source:"self"});case 2:r=t.sent,0===r.code?e.deviceArr=[{name:"H5",key:"H5"},{name:"小程序",key:"MAPP"}].concat(m(r.data)):e.$message.error(r.msg);case 4:case"end":return t.stop()}}),t)})))()},handleDateString:function(){var e=this.$parent.$parent;if("week"===e.headerType){var t=e.$refs.weekHeaderRef.activeWeek,r=a(t).endOf("week").add(1,"d").format("YYYY-MM-DD");this.dateString="".concat(t," 至 ").concat(r)}else this.dateString=e.$refs.weekHeaderRef.activeMonth},setRawData:function(){var e=this;if(this.editData.id){var t=this.deviceArr.filter((function(t){return e.editData.device_types.includes(t.name)})).map((function(e){return e.key}));this.form={id:this.editData.id,name:this.editData.name,device_types:t||[],device_model:this.editData.device_model,use_user_groups:this.editData.use_user_groups,applied_to_visitor:this.editData.applied_to_visitor,is_continue_to_used:this.editData.is_continue_to_used,is_intent_menu:this.editData.is_intent_menu,create_type:this.editData.create_type,checked_number:!0,diners_number:this.editData.diners_number||"",is_nutrition_guidance:!!this.editData.is_nutrition_guidance,use_price:this.editData.use_price,food_price:this.editData.food_price||"",seasonal_priority:!!this.editData.seasonal_priority,menu_template_id:this.editData.menu_template_id||""},this.getDeviceModel(),"TEMPLATE"===this.editData.create_type&&this.getRecipeTemplateList()}},requestDietGroupList:function(){var e=this;return k(_().mark((function t(){var r,n;return _().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return r={page:1,page_size:9999},t.next=3,e.$apis.apiBackgroundFoodDietGroupListPost(r);case 3:n=t.sent,0===n.code?e.apply_groups=n.data.results:e.$message.error(n.msg);case 5:case"end":return t.stop()}}),t)})))()},getOrganizationList:function(){var e=this;return k(_().mark((function t(){var r;return _().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,e.$apis.apiBackgroundOrganizationOrganizationListPost({status:"enable",page:1,page_size:9999});case 3:r=t.sent,e.isLoading=!1,0===r.code?e.organizations=r.data.results:e.$message.error(r.msg);case 6:case"end":return t.stop()}}),t)})))()},userGroupList:function(){var e=this;return k(_().mark((function t(){var r;return _().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,e.$apis.apiCardServiceCardUserGroupListPost({status:"enable",page:1,page_size:9999});case 3:r=t.sent,e.isLoading=!1,0===r.code?e.groupList=r.data.results:e.$message.error(r.msg);case 6:case"end":return t.stop()}}),t)})))()},changeCreateType:function(e){"TEMPLATE"===e&&0===this.templateList.length&&this.getRecipeTemplateList()},getRecipeTemplateList:function(){var e=this;return k(_().mark((function t(){var r,n,i,a,o;return _().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.isLoading){t.next=2;break}return t.abrupt("return");case 2:return e.loadingTemplate=!0,r={page:e.templateCurrentPage,page_size:e.templatePageSize},t.next=6,e.$to(e.$apis.apiBackgroundFoodMenuTemplateListPost(r));case 6:if(n=t.sent,i=u(n,2),a=i[0],o=i[1],e.loadingTemplate=!1,!a){t.next=14;break}return e.$message.error(a.message),t.abrupt("return");case 14:0===o.code?(e.templateCount=o.data.count,e.templateList=o.data.results):e.$message.error(o.msg);case 15:case"end":return t.stop()}}),t)})))()},onPaginationChange:function(e){this.templateCurrentPage=e.current,this.templatePageSize=e.pageSize,this.getRecipeTemplateList()},showTemplateHandle:function(e){this.dialogData=e.category_data,this.showDialog=!0},changeNutritionGuidance:function(e){e&&(this.selectModelDialogVisible=!0),this.form.is_nutrition_guidance=!1},clickSelect:function(e){this.nutritionGuidanceInfo=e,this.form.is_nutrition_guidance=!0,this.selectModelDialogVisible=!1}}},L=x,D=(r("b782"),r("2877")),E=Object(D["a"])(L,n,i,!1,null,"48bf50fe",null);t["default"]=E.exports},"322d":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("dialog-message",{staticClass:"template-dialog",attrs:{show:e.visible,loading:e.dialogLoading,title:e.dialogTitle,top:"10vh",width:e.dialogWidth,showFooter:!1},on:{"update:show":function(t){e.visible=t},close:e.closeDialog}},[t("div",{staticClass:"p-b-30 m-l-30 max-h-600"},e._l(e.formSetting,(function(r,n){return t("div",{key:n},[t("h3",[e._v(e._s(r.label))]),t("div",{staticClass:"m-l-60 m-t-20"},e._l(r.children,(function(n){return t("span",{key:n.key,staticClass:"inline-block m-r-60 m-b-20 vertical-top"},[t("span",{staticClass:"inline-block max-w-100 vertical-top"},[e._v(e._s(n.label+"："))]),t("span",{staticClass:"vertical-top m-l-10 m-r-6"},[e._v(e._s(e.formData[r.key][n.key])+"道")])])})),0)])})),0)])},i=[],a=r("ed08"),o=r("c9d9");function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function l(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */l=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,i=Object.defineProperty||function(e,t,r){e[t]=r.value},a="function"==typeof Symbol?Symbol:{},o=a.iterator||"@@iterator",c=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function f(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{f({},"")}catch(e){f=function(e,t,r){return e[t]=r}}function d(e,t,r,n){var a=t&&t.prototype instanceof b?t:b,o=Object.create(a.prototype),s=new O(n||[]);return i(o,"_invoke",{value:T(e,r,s)}),o}function p(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var m="suspendedStart",h="suspendedYield",g="executing",y="completed",v={};function b(){}function _(){}function w(){}var k={};f(k,o,(function(){return this}));var x=Object.getPrototypeOf,L=x&&x(x(A([])));L&&L!==r&&n.call(L,o)&&(k=L);var D=w.prototype=b.prototype=Object.create(k);function E(e){["next","throw","return"].forEach((function(t){f(e,t,(function(e){return this._invoke(t,e)}))}))}function S(e,t){function r(i,a,o,l){var c=p(e[i],e,a);if("throw"!==c.type){var u=c.arg,f=u.value;return f&&"object"==s(f)&&n.call(f,"__await")?t.resolve(f.__await).then((function(e){r("next",e,o,l)}),(function(e){r("throw",e,o,l)})):t.resolve(f).then((function(e){u.value=e,o(u)}),(function(e){return r("throw",e,o,l)}))}l(c.arg)}var a;i(this,"_invoke",{value:function(e,n){function i(){return new t((function(t,i){r(e,n,t,i)}))}return a=a?a.then(i,i):i()}})}function T(t,r,n){var i=m;return function(a,o){if(i===g)throw Error("Generator is already running");if(i===y){if("throw"===a)throw o;return{value:e,done:!0}}for(n.method=a,n.arg=o;;){var s=n.delegate;if(s){var l=C(s,n);if(l){if(l===v)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===m)throw i=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=g;var c=p(t,r,n);if("normal"===c.type){if(i=n.done?y:h,c.arg===v)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(i=y,n.method="throw",n.arg=c.arg)}}}function C(t,r){var n=r.method,i=t.iterator[n];if(i===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,C(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var a=p(i,t.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,v;var o=a.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,v):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function $(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function P(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach($,this),this.reset(!0)}function A(t){if(t||""===t){var r=t[o];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,a=function r(){for(;++i<t.length;)if(n.call(t,i))return r.value=t[i],r.done=!1,r;return r.value=e,r.done=!0,r};return a.next=a}}throw new TypeError(s(t)+" is not iterable")}return _.prototype=w,i(D,"constructor",{value:w,configurable:!0}),i(w,"constructor",{value:_,configurable:!0}),_.displayName=f(w,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===_||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,f(e,u,"GeneratorFunction")),e.prototype=Object.create(D),e},t.awrap=function(e){return{__await:e}},E(S.prototype),f(S.prototype,c,(function(){return this})),t.AsyncIterator=S,t.async=function(e,r,n,i,a){void 0===a&&(a=Promise);var o=new S(d(e,r,n,i),a);return t.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},E(D),f(D,u,"Generator"),f(D,o,(function(){return this})),f(D,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=A,O.prototype={constructor:O,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(P),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function i(n,i){return s.type="throw",s.arg=t,r.next=n,i&&(r.method="next",r.arg=e),!!i}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],s=o.completion;if("root"===o.tryLoc)return i("end");if(o.tryLoc<=this.prev){var l=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return i(o.catchLoc,!0);if(this.prev<o.finallyLoc)return i(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return i(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return i(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=e,o.arg=t,a?(this.method="next",this.next=a.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),P(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;P(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:A(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),v}},t}function c(e,t){return m(e)||p(e,t)||f(e,t)||u()}function u(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(e,t){if(e){if("string"==typeof e)return d(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?d(e,t):void 0}}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function p(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,o,s=[],l=!0,c=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){c=!0,i=e}finally{try{if(!l&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return s}}function m(e){if(Array.isArray(e))return e}function h(e,t,r,n,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,i)}function g(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var a=e.apply(t,r);function o(e){h(a,n,i,o,s,"next",e)}function s(e){h(a,n,i,o,s,"throw",e)}o(void 0)}))}}var y={name:"TemplateDialog",model:{prop:"showDialog",event:"changeShow"},props:{showDialog:{required:!0},dialogTitle:{type:String,default:"预览"},type:{type:String,default:"add"},infoData:{type:Array,default:function(){return[]}},dialogWidth:{type:String,default:"760px"},closehandle:Function,confirmhandle:Function},data:function(){return{dialogLoading:!1,formSetting:{},formData:{},mealList:o["a"],templateAttributeList:[]}},computed:{visible:{get:function(){return this.showDialog},set:function(e){this.$emit("changeShow",e)}}},watch:{showDialog:function(e){e&&this.init()}},created:function(){},mounted:function(){this.getFoodAttributeList()},methods:{init:function(){var e=this;this.infoData.forEach((function(t){var r=t.category_json_data;for(var n in e.formData[t.meal_type])""!==r[n]&&e.$set(e.formData[t.meal_type],n,r[n])}))},getFoodAttributeList:function(){var e=this;return g(l().mark((function t(){var r,n,i,a;return l().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$to(e.$apis.apiBackgroundFoodFoodCategoryListPost({page:1,page_size:99999}));case 2:if(r=t.sent,n=c(r,2),i=n[0],a=n[1],!i){t.next=9;break}return e.$message.error(i.message),t.abrupt("return");case 9:0===a.code?e.setDefaultAttrData(a.data.results):e.$message.error(a.msg);case 10:case"end":return t.stop()}}),t)})))()},setDefaultAttrData:function(e){var t=this,r={};e?(this.templateAttributeList=e.map((function(e){var t={label:e.name,key:e.id};return r[e.id]="",t})),this.formSetting=this.mealList.map((function(e){return t.$set(t.formData,e.value,Object(a["f"])(r)),{label:e.label,key:e.value,children:Object(a["f"])(t.templateAttributeList)}}))):(this.templateAttributeList.map((function(e){r[e.key]=""})),this.mealList.map((function(e){t.$set(t.formData,e.value,Object(a["f"])(r))})))},closeDialog:function(){this.resetForm(),this.closehandle&&this.closehandle()},cancleDialog:function(){this.resetForm(),this.closehandle&&this.closehandle(),this.visible=!1},confirmDialog:function(){this.resetForm(),this.confirmhandle&&this.confirmhandle()},resetForm:function(){this.dialogForm={},this.setDefaultAttrData()}}},v=y,b=(r("a770"),r("2877")),_=Object(b["a"])(v,n,i,!1,null,"d52fd8b6",null);t["default"]=_.exports},"5c13":function(e,t,r){},a770:function(e,t,r){"use strict";r("0cda")},b782:function(e,t,r){"use strict";r("5c13")},c9d9:function(e,t,r){"use strict";r.d(t,"a",(function(){return a})),r.d(t,"d",(function(){return o})),r.d(t,"b",(function(){return s})),r.d(t,"c",(function(){return l})),r.d(t,"e",(function(){return c})),r.d(t,"f",(function(){return u})),r.d(t,"g",(function(){return f}));var n=r("5a0c"),i=r("da92"),a=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],o=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],s={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},l=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],c=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],u=(n().subtract(7,"day").format("YYYY-MM-DD"),n().format("YYYY-MM-DD"),function(e){return e?"number"===typeof e?i["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:i["a"].divide(e,100).toFixed(2):"0.00"}),f=function(e){return i["a"].times(e,100)}},e925:function(e,t,r){"use strict";r.d(t,"c",(function(){return n})),r.d(t,"g",(function(){return i})),r.d(t,"i",(function(){return a})),r.d(t,"e",(function(){return o})),r.d(t,"h",(function(){return s})),r.d(t,"f",(function(){return l})),r.d(t,"d",(function(){return c})),r.d(t,"m",(function(){return u})),r.d(t,"l",(function(){return f})),r.d(t,"n",(function(){return d})),r.d(t,"j",(function(){return p})),r.d(t,"b",(function(){return m})),r.d(t,"k",(function(){return h})),r.d(t,"a",(function(){return g}));var n=function(e){return/^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(e)},i=function(e){return/^([0-9]{3,4}-)?[0-9]{7,8}$/.test(e.toString())},a=function(e){return/^\w{5,20}$/.test(e)},o=function(e){return/^(?=.*[0-9])(?=.*[a-zA-Z])(.{8,20})$/.test(e)},s=function(e){return/(^([0-9]{3,4}-)?[0-9]{7,8}$)|(^1[3,4,5,6,7,8,9][0-9]{9}$)/.test(e.toString())},l=function(e){return/^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(e.toString())},c=function(e){return/\d/.test(e)},u=function(e){return/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(e)},f=function(e){return/^(([1-9]?[0-9])|([1-9]?[0-9]\.[1-9])|([1-9]?[1-9]\.[0]))$/.test(e)},d=function(e){return/^(([0-9])|([0-9]\.\d{1,3})|([1-9]\.[0]))$/.test(e)},p=function(e){return/^-?(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(e)},m=function(e){return/^[0-9]+$/.test(e)},h=function(e){return/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1}|[1-9][0-9]*\.\d{1})))$/.test(e)},g=function(e){return/^[a-zA-Z0-9]+$/.test(e)}}}]);