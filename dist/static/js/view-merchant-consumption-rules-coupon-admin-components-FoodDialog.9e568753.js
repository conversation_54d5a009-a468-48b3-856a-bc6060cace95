(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-consumption-rules-coupon-admin-components-FoodDialog"],{1884:function(t,e,o){"use strict";o("aa9c")},4382:function(t,e,o){"use strict";o.r(e);var r=function(){var t=this,e=t._self._c;return e("dialog-message",{attrs:{show:t.visible,title:t.title,loading:t.isLoading,customClass:"ps-dialog",width:t.width},on:{"update:show":function(e){t.visible=e},"update:loading":function(e){t.isLoading=e},close:t.handleClose}},[e("el-form",{ref:"couponDialogForm",staticClass:"jiaofei-form",attrs:{model:t.couponDialogForm,"status-icon":"","label-width":"80px",inline:""},nativeOn:{submit:function(t){t.preventDefault()}}},[e("el-form-item",[e("el-radio-group",{staticClass:"ps-radio",on:{change:t.changeFoodType},model:{value:t.couponDialogForm.foodType,callback:function(e){t.$set(t.couponDialogForm,"foodType",e)},expression:"couponDialogForm.foodType"}},[e("el-radio",{attrs:{label:"food"}},[t._v("菜品")]),e("el-radio",{attrs:{label:"taocan"}},[t._v("套餐")])],1)],1),e("br"),e("el-form-item",{attrs:{label:"food"===t.couponDialogForm.foodType?"菜品分类":"套餐分类"}},[e("el-select",{staticClass:"ps-select w-180",attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select"},on:{change:t.searchHandle},model:{value:t.couponDialogForm.categoryId,callback:function(e){t.$set(t.couponDialogForm,"categoryId",e)},expression:"couponDialogForm.categoryId"}},t._l(t.categoryList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),e("el-form-item",{attrs:{label:"food"===t.couponDialogForm.foodType?"菜品名称":"套餐名称"}},[e("el-input",{staticClass:"ps-input w-180",attrs:{placeholder:"请输入"},on:{input:t.searchHandle},model:{value:t.couponDialogForm.foodName,callback:function(e){t.$set(t.couponDialogForm,"foodName",e)},expression:"couponDialogForm.foodName"}})],1),e("div",{staticClass:"person-table"},[e("el-table",{ref:"foodListRef",attrs:{data:t.foodList,"max-height":"350","row-key":t.getRowKey,"header-row-class-name":"ps-table-header-row"},on:{select:t.handleSelection,"select-all":t.handleAllSelection}},[e("el-table-column",{attrs:{type:"selection","reserve-selection":!0,width:"50",align:"center","class-name":"ps-checkbox"}}),e("el-table-column",{attrs:{prop:"name",label:"food"===t.couponDialogForm.foodType?"菜品名称":"套餐名称",align:"center"}}),e("el-table-column",{attrs:{prop:"category_name",label:"food"===t.couponDialogForm.foodType?"菜品分类":"套餐分类",align:"center"}})],1)],1),e("div",{staticClass:"block ps-pagination person-table-bottom"},[e("div",{staticStyle:{width:"100px"}},[t._v("已选人数："+t._s(t.selectList.length))]),e("el-pagination",{staticClass:"ps-text",attrs:{"current-page":t.currentPage,"page-sizes":[5,10,20,30,40],"page-size":t.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:t.totalCount,"pager-count":5,background:"","popper-class":"ps-popper-select"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1),e("template",{slot:"tool"},[e("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:t.isLoading},on:{click:t.clickCancleHandle}},[t._v(" 取消 ")]),e("el-button",{staticClass:"ps-btn",attrs:{disabled:t.isLoading,type:"primary"},on:{click:t.clickConfirmHandle}},[t._v(" 确定 ")])],1)])],2)},n=[],i=o("ed08"),a=o("bbd5");function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return e};var t,e={},o=Object.prototype,r=o.hasOwnProperty,n=Object.defineProperty||function(t,e,o){t[e]=o.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function f(t,e,o){return Object.defineProperty(t,e,{value:o,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,o){return t[e]=o}}function p(t,e,o,r){var i=e&&e.prototype instanceof b?e:b,a=Object.create(i.prototype),c=new _(r||[]);return n(a,"_invoke",{value:k(t,o,c)}),a}function d(t,e,o){try{return{type:"normal",arg:t.call(e,o)}}catch(t){return{type:"throw",arg:t}}}e.wrap=p;var h="suspendedStart",g="suspendedYield",y="executing",m="completed",v={};function b(){}function w(){}function L(){}var F={};f(F,a,(function(){return this}));var x=Object.getPrototypeOf,O=x&&x(x(I([])));O&&O!==o&&r.call(O,a)&&(F=O);var j=L.prototype=b.prototype=Object.create(F);function S(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function D(t,e){function o(n,i,a,s){var l=d(t[n],t,i);if("throw"!==l.type){var u=l.arg,f=u.value;return f&&"object"==c(f)&&r.call(f,"__await")?e.resolve(f.__await).then((function(t){o("next",t,a,s)}),(function(t){o("throw",t,a,s)})):e.resolve(f).then((function(t){u.value=t,a(u)}),(function(t){return o("throw",t,a,s)}))}s(l.arg)}var i;n(this,"_invoke",{value:function(t,r){function n(){return new e((function(e,n){o(t,r,e,n)}))}return i=i?i.then(n,n):n()}})}function k(e,o,r){var n=h;return function(i,a){if(n===y)throw Error("Generator is already running");if(n===m){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var c=r.delegate;if(c){var s=C(c,r);if(s){if(s===v)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(n===h)throw n=m,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n=y;var l=d(e,o,r);if("normal"===l.type){if(n=r.done?m:g,l.arg===v)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n=m,r.method="throw",r.arg=l.arg)}}}function C(e,o){var r=o.method,n=e.iterator[r];if(n===t)return o.delegate=null,"throw"===r&&e.iterator.return&&(o.method="return",o.arg=t,C(e,o),"throw"===o.method)||"return"!==r&&(o.method="throw",o.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var i=d(n,e.iterator,o.arg);if("throw"===i.type)return o.method="throw",o.arg=i.arg,o.delegate=null,v;var a=i.arg;return a?a.done?(o[e.resultName]=a.value,o.next=e.nextLoc,"return"!==o.method&&(o.method="next",o.arg=t),o.delegate=null,v):a:(o.method="throw",o.arg=new TypeError("iterator result is not an object"),o.delegate=null,v)}function P(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function _(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(P,this),this.reset(!0)}function I(e){if(e||""===e){var o=e[a];if(o)return o.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,i=function o(){for(;++n<e.length;)if(r.call(e,n))return o.value=e[n],o.done=!1,o;return o.value=t,o.done=!0,o};return i.next=i}}throw new TypeError(c(e)+" is not iterable")}return w.prototype=L,n(j,"constructor",{value:L,configurable:!0}),n(L,"constructor",{value:w,configurable:!0}),w.displayName=f(L,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,L):(t.__proto__=L,f(t,u,"GeneratorFunction")),t.prototype=Object.create(j),t},e.awrap=function(t){return{__await:t}},S(D.prototype),f(D.prototype,l,(function(){return this})),e.AsyncIterator=D,e.async=function(t,o,r,n,i){void 0===i&&(i=Promise);var a=new D(p(t,o,r,n),i);return e.isGeneratorFunction(o)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},S(j),f(j,u,"Generator"),f(j,a,(function(){return this})),f(j,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),o=[];for(var r in e)o.push(r);return o.reverse(),function t(){for(;o.length;){var r=o.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=I,_.prototype={constructor:_,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(E),!e)for(var o in this)"t"===o.charAt(0)&&r.call(this,o)&&!isNaN(+o.slice(1))&&(this[o]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var o=this;function n(r,n){return c.type="throw",c.arg=e,o.next=r,n&&(o.method="next",o.arg=t),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),l=r.call(a,"finallyLoc");if(s&&l){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(t,e){for(var o=this.tryEntries.length-1;o>=0;--o){var n=this.tryEntries[o];if(n.tryLoc<=this.prev&&r.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var o=this.tryEntries[e];if(o.finallyLoc===t)return this.complete(o.completion,o.afterLoc),E(o),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var o=this.tryEntries[e];if(o.tryLoc===t){var r=o.completion;if("throw"===r.type){var n=r.arg;E(o)}return n}}throw Error("illegal catch attempt")},delegateYield:function(e,o,r){return this.delegate={iterator:I(e),resultName:o,nextLoc:r},"next"===this.method&&(this.arg=t),v}},e}function l(t,e){var o=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),o.push.apply(o,r)}return o}function u(t){for(var e=1;e<arguments.length;e++){var o=null!=arguments[e]?arguments[e]:{};e%2?l(Object(o),!0).forEach((function(e){b(t,e,o[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):l(Object(o)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(o,e))}))}return t}function f(t,e){return y(t)||g(t,e)||d(t,e)||p()}function p(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(t,e){if(t){if("string"==typeof t)return h(t,e);var o={}.toString.call(t).slice(8,-1);return"Object"===o&&t.constructor&&(o=t.constructor.name),"Map"===o||"Set"===o?Array.from(t):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?h(t,e):void 0}}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var o=0,r=Array(e);o<e;o++)r[o]=t[o];return r}function g(t,e){var o=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=o){var r,n,i,a,c=[],s=!0,l=!1;try{if(i=(o=o.call(t)).next,0===e){if(Object(o)!==o)return;s=!1}else for(;!(s=(r=i.call(o)).done)&&(c.push(r.value),c.length!==e);s=!0);}catch(t){l=!0,n=t}finally{try{if(!s&&null!=o.return&&(a=o.return(),Object(a)!==a))return}finally{if(l)throw n}}return c}}function y(t){if(Array.isArray(t))return t}function m(t,e,o,r,n,i,a){try{var c=t[i](a),s=c.value}catch(t){return void o(t)}c.done?e(s):Promise.resolve(s).then(r,n)}function v(t){return function(){var e=this,o=arguments;return new Promise((function(r,n){var i=t.apply(e,o);function a(t){m(i,r,n,a,c,"next",t)}function c(t){m(i,r,n,a,c,"throw",t)}a(void 0)}))}}function b(t,e,o){return(e=w(e))in t?Object.defineProperty(t,e,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[e]=o,t}function w(t){var e=L(t,"string");return"symbol"==c(e)?e:e+""}function L(t,e){if("object"!=c(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var r=o.call(t,e||"default");if("object"!=c(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var F={name:"FoodDialog",props:{loading:Boolean,title:{type:String,default:"选择菜品/套餐"},width:{type:String,default:"700px"},chooseList:{type:Array,default:function(){return[]}},selectInfo:{type:Object,default:function(){return{}}},isshow:Boolean,confirm:Function},data:function(){return b({isLoading:!1,couponDialogForm:{foodType:"food",categoryId:"",foodName:""},foodList:[],pageSize:10,totalCount:0,currentPage:1,selectList:[],organizationId:this.$store.getters.organization,categoryList:[]},"foodList",[])},computed:{visible:{get:function(){return this.isshow},set:function(t){this.$emit("update:isshow",t)}}},watch:{visible:function(){var t=this;this.visible&&(this.currentPage=1,this.totalCount=0,this.isshow&&(this.$nextTick((function(){t.$refs.foodListRef.clearSelection()})),this.couponDialogForm.foodType="food",this.couponDialogForm.categoryId="",this.couponDialogForm.foodName="",this.selectList=Object(a["a"])(this.chooseList)))}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.foodFoodCategoryList()},searchHandle:Object(i["d"])((function(){this.currentPage=1,this.foodFoodList()}),300),clickConfirmHandle:function(){var t=this;this.$refs.couponDialogForm.validate((function(e){e&&(t.$emit("confirmPerson",t.selectList),t.visible=!1)}))},clickCancleHandle:function(){this.visible=!1},handleClose:function(t){this.isLoading=!1,this.visible=!1,this.$refs.couponDialogForm.resetFields()},foodFoodList:function(){var t=this;return v(s().mark((function e(){var o,r,n,a,c,l;return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r={},"food"===t.couponDialogForm.foodType?(o=t.$apis.apiBackgroundFoodFoodListPost,t.couponDialogForm.categoryId&&(r.category_id=t.couponDialogForm.categoryId),t.couponDialogForm.foodName&&(r.food_name=t.couponDialogForm.foodName)):(o=t.$apis.apiBackgroundFoodSetMealListPost,t.couponDialogForm.categoryId&&(r.category=[t.couponDialogForm.categoryId]),t.couponDialogForm.foodName&&(r.name=t.couponDialogForm.foodName)),t.isLoading=!0,e.next=5,Object(i["Z"])(o(u({page:1,page_size:9999},r)));case 5:if(n=e.sent,a=f(n,2),c=a[0],l=a[1],t.isLoading=!1,!c){e.next=13;break}return t.$message.error(c.message),e.abrupt("return");case 13:0===l.code?(t.foodList=l.data.results,t.totalCount=l.data.count,t.foodList.map((function(e){t.chooseList.map((function(o){e.id===o.id&&t.$refs.foodListRef.toggleRowSelection(e,!0)}))}))):t.$message.error(l.msg);case 14:case"end":return e.stop()}}),e)})))()},handleSizeChange:function(t){this.pageSize=t,"choosePerson"===this.type?this.getfoodList():"food"===this.type&&this.foodFoodList()},handleCurrentChange:function(t){if(this.currentPage=t,"choosePerson"===this.type)this.getfoodList();else if("food"===this.type){if(!this.couponDialogForm.categoryId&&!this.couponDialogForm.foodName)return;this.foodFoodList()}},handleSelection:function(t,e){var o=this.selectList.findIndex((function(t){return t.id===e.id}));-1===o?this.selectList.push(e):this.selectList.splice(o,1)},handleAllSelection:function(t){var e=this,o=Object(a["a"])(t),r=!0;this.foodList.map((function(t){var e=o.findIndex((function(e){return e.id===t.id}));-1===e&&(r=!1)})),r?this.foodList.map((function(t){var o=e.selectList.findIndex((function(e){return e.id===t.id}));-1===o&&e.selectList.push(t)})):this.foodList.map((function(t){var o=e.selectList.findIndex((function(e){return e.id===t.id}));-1!==o&&e.selectList.splice(o,1)}))},getRowKey:function(t){return t.id},foodSelection:function(){},changeFoodType:function(){this.foodFoodCategoryList(),this.foodList=[],this.currentPage=1,this.totalCount=0,this.couponDialogForm.foodName="",this.couponDialogForm.categoryId=""},foodFoodCategoryList:function(){var t=this;return v(s().mark((function e(){var o,r,n,a,c;return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return o="food"===t.couponDialogForm.foodType?t.$apis.apiBackgroundFoodFoodCategoryListPost:t.$apis.apiBackgroundFoodSetMealCategoryListPost,e.next=3,Object(i["Z"])(o({page:1,page_size:9999}));case 3:if(r=e.sent,n=f(r,2),a=n[0],c=n[1],!a){e.next=10;break}return t.$message.error(a.message),e.abrupt("return");case 10:0===c.code?t.categoryList=c.data.results:t.$message.error(c.msg);case 11:case"end":return e.stop()}}),e)})))()}}},x=F,O=(o("1884"),o("2877")),j=Object(O["a"])(x,r,n,!1,null,"271a955a",null);e["default"]=j.exports},aa9c:function(t,e,o){},bbd5:function(t,e,o){"use strict";function r(t){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}o.d(e,"a",(function(){return n}));var n=function t(e){if(!e&&"object"!==r(e))throw new Error("error arguments","deepClone");var o=e.constructor===Array?[]:{};return Object.keys(e).forEach((function(n){e[n]&&"object"===r(e[n])?o[n]=t(e[n]):o[n]=e[n]})),o}}}]);