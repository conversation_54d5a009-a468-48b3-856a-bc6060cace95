(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-merchant-admin-NutritionOrders","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-meal-report-MealPackageRule","view-super-health-system-health-nutrition-AddMerchantCommodityToSuper","view-super-health-system-health-nutrition-IngredientsCategory","view-super-health-system-health-nutrition-constants","view-super-merchant-admin-components-AddFoodDrawer","view-super-merchant-admin-components-addNutritionOrderProject"],{"015b":function(e,t,r){"use strict";r.r(t),r.d(t,"DEFAULT_NUTRITION",(function(){return a})),r.d(t,"ELEMENT_NUTRITION",(function(){return i})),r.d(t,"VITAMIN_NUTRITION",(function(){return o})),r.d(t,"NUTRITION_LIST",(function(){return s})),r.d(t,"LIBRARY_SEARCH_SETTING_SUPER",(function(){return l})),r.d(t,"LIBRARY_SEARCH_SETTING_MERCHANT",(function(){return c})),r.d(t,"LIBRARY_SEARCH_SETTING_CATEORY",(function(){return u})),r.d(t,"COMMODITY_SEARCH_SETTING_SUPER",(function(){return d})),r.d(t,"COMMODITY_SEARCH_SETTING_MERCHANT",(function(){return f}));var n=r("ed08"),a=[{name:"千卡",key:"energy_kcal",unit:"Kcal",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{name:"胆固醇",key:"cholesterol",unit:"mg",type:"default"},{name:"膳食纤维",key:"dietary_fiber",unit:"g",type:"default"}],i=[{key:"Ca",name:"钙",unit:"mg",type:"element"},{key:"P",name:"磷",unit:"mg",type:"element"},{key:"K",name:"钾",unit:"mg",type:"element"},{key:"Na",name:"钠",unit:"mg",type:"element"},{name:"镁",key:"Mg",unit:"mg",type:"element"},{key:"Fe",name:"铁",unit:"mg",type:"element"},{key:"I",name:"碘",unit:"μg",type:"element"},{key:"Se",name:"硒",unit:"μg",type:"element"},{key:"Zn",name:"锌",unit:"mg",type:"element"},{key:"Cu",name:"铜",unit:"mg",type:"element"},{key:"F",name:"氟",unit:"mg",type:"element"},{key:"Cr",name:"铬",unit:"μg",type:"element"},{key:"Mo",name:"钼",unit:"μg",type:"element"},{key:"Mn",name:"锰",unit:"mg",type:"element"}],o=[{key:"VA",name:"维生素A",unit:"μg",type:"vitamin"},{key:"VD",name:"维生素D",unit:"μg",type:"vitamin"},{key:"VE",name:"维生素E",unit:"mg",type:"vitamin"},{key:"VK",name:"维生素K",unit:"μg",type:"vitamin"},{key:"VB1",name:"维生素B1",unit:"mg",type:"vitamin"},{key:"VB2",name:"维生素B2",unit:"mg",type:"vitamin"},{key:"VB6",name:"维生素B6",unit:"mg",type:"vitamin"},{key:"VB12",name:"维生素B12",unit:"μg",type:"vitamin"},{key:"VC",name:"维生素C",unit:"mg",type:"vitamin"},{key:"VB5",name:"泛酸",unit:"mg",type:"vitamin"},{key:"VM",name:"叶酸",unit:"μg",type:"vitamin"},{key:"VB3",name:"烟酸",unit:"mg",type:"vitamin"},{key:"Choline",name:" 胆碱",unit:"mg",type:"vitamin"},{key:"Nicotinamide",name:"烟酰胺",unit:"mg",type:"vitamin"},{key:"VH",name:"生物素",unit:"mg",type:"vitamin"}],s=[].concat(a,i,o),l={date_type:{type:"select",label:"",value:1,maxWidth:"130px",placeholder:"请选择",dataList:[{label:"创建时间",value:1},{label:"修改时间",value:2}]},select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"",clearable:!0,value:[]},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(e){return{id:e.level+"_"+e.id,label:e.name,children:e.sort_list}}},is_enable_nutrition:{type:"select",label:"营养信息",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"有",value:"1"},{label:"无",value:"0"}]},ingredient_name:{type:"input",label:"食材名称",value:"",placeholder:"请输入食材名称"},operator:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"},label_filter:{type:"select",label:"",value:"",placeholder:"",dataList:[{label:"请选择",value:"select",disabled:!0},{label:"包含",value:"Include"},{label:"不包含",value:"Exclude"}]},label_list:{type:"treeselect",label:"",value:[],placeholder:"选择标签",multiple:!0,limit:1,level:1,valueConsistsOf:"LEAF_PRIORITY",normalizer:function(e){return{id:e.id,label:e.name,children:e.label_list}},dataList:[]},is_entering:{type:"select",label:"录入图片",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"已录入",value:"1"},{label:"未录入",value:"0"}]}},c={select_time:{type:"datetimerange",label:"创建时间",format:"yyyy-MM-dd",value:Object(n["y"])(7)},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(e){return{id:e.level+"_"+e.id,label:e.name,children:e.sort_list}}},is_repeat:{type:"select",label:"已有食材",value:"",placeholder:"请选择是否已有食材",dataList:[{label:"是",value:1},{label:"否",value:0}]},is_enable_nutrition:{type:"select",label:"营养信息",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"有",value:"1"},{label:"无",value:"0"}]},ingredient_name:{type:"input",label:"食材名称",value:"",placeholder:"请输入食材名称"},is_entering:{type:"select",label:"录入图片",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"已录入",value:"1"},{label:"未录入",value:"0"}]}},u={select_time:{type:"datetimerange",label:"修改时间",format:"yyyy-MM-dd HH:mm:ss",value:[]},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(e){return{id:e.level+"_"+e.id,label:e.name,children:e.sort_list}}},user_name:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"}},d={date_type:{type:"select",label:"",value:1,maxWidth:"130px",placeholder:"请选择",dataList:[{label:"创建时间",value:1},{label:"修改时间",value:2}]},select_time:{type:"datetimerange",label:"",format:"yyyy-MM-dd HH:mm:ss",value:[]},food_name:{type:"input",label:"菜品/商品名称",value:"",placeholder:"请输入菜品/商品名称"},user_name:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"},attributes:{type:"select",label:"属性",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"商品",value:"goods"},{label:"菜品",value:"foods"}]},label_filter:{type:"select",label:"",value:"",placeholder:"",dataList:[{label:"请选择",value:"select",disabled:!0},{label:"包含",value:"Include"},{label:"不包含",value:"Exclude"}]},label_list:{type:"treeselect",label:"",value:[],placeholder:"选择标签",multiple:!0,limit:1,level:1,valueConsistsOf:"LEAF_PRIORITY",normalizer:function(e){return{id:e.id,label:e.name,children:e.label_list}},dataList:[]}},f={select_time:{type:"datetimerange",label:"创建时间",format:"yyyy-MM-dd",value:Object(n["y"])(7)},food_name:{type:"input",label:"菜品/商品名称",value:"",placeholder:"请输入菜品/商品名称"},is_repeat:{type:"select",label:"已有菜品/商品",value:"",placeholder:"请选择",dataList:[{label:"是",value:1},{label:"否",value:0}]},attributes:{type:"select",label:"属性",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"商品",value:"goods"},{label:"菜品",value:"foods"}]}}},"0ac1":function(e,t,r){"use strict";r("fd99")},"3fa5":function(e,t,r){"use strict";r.d(t,"a",(function(){return i}));r("9e1f"),r("450d");var n=r("6ed5"),a=r.n(n);function i(e,t){return new Promise((function(r,n){a.a.confirm(e.content?e.content:"",e.title?e.title:"提示",{dangerouslyUseHTMLString:!0|e.useHTML,distinguishCancelAndClose:!0,closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:e.cancel_class?e.cancel_class:"ps-cancel-btn",confirmButtonClass:e.confirm_class?e.confirm_class:"ps-btn",confirmButtonText:e.confirmButtonText,cancelButtonText:e.cancelButtonText,center:""===e.center||e.center}).then((function(e){t?r(t()):r()})).catch((function(e){n(e)}))}))}},"4e77":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"NutritionOrders container-wrapper"},[t("refresh-tool",{on:{refreshPage:e.refreshHandle}}),t("div",{staticClass:"nutrition-content table-wrapper"},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],staticClass:"nutrition-order-left"},[t("div",{},[t("el-button",{staticClass:"ps-btn",attrs:{size:"small"},on:{click:function(t){e.showDrawer=!0}}},[e._v("选择项目")]),t("span",{staticClass:"m-l-30"},[e._v("打标对象："+e._s(e.markingText?e.markingText:"请选择项目及餐段"))])],1),t("div",{staticClass:"m-t-30 m-b-30 font-size-20"},[e._v(e._s(e.markingFoodText))]),t("div",{},[t("el-image",{staticClass:"food-img",attrs:{src:e.markNutritionOrder.source_image_key,fit:"contain",lazy:"","preview-src-list":[e.markNutritionOrder.source_image_key]}},[t("div",{staticClass:"el-image__error",attrs:{slot:"error"},slot:"error"},[t("i",{staticClass:"el-icon-picture-outline",staticStyle:{"font-size":"40px"}}),t("div",{staticClass:"error-message m-t-10"},[e._v(" "+e._s(e.markNutritionOrder.source_image_key?"加载图片失败":"暂无图片")+" ")])])])],1),t("div",{staticClass:"m-t-30"},[e._v("本餐打标订单数："+e._s(e.markCount||0)+"笔")])]),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loadingFood,expression:"loadingFood"}],staticClass:"nutrition-order-right m-l-20 flex-1"},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loadingCurrentFood,expression:"loadingCurrentFood"}]},[t("div",{staticClass:"m-t-20"},[t("span",{staticClass:"m-r-30"},[e._v("本餐打标菜品：")]),t("el-button",{staticClass:"ps-origin-btn",on:{click:e.submitHandle}},[e._v("提交")]),t("span",{staticClass:"m-l-20 m-r-20"},[e._v("不选择菜品进行提交默认跳过该笔订单。")]),t("div",{staticClass:"inline-block m-l-10 p-t-10 p-b-10"},[e._l(e.latelyHistory,(function(r,n){return t("el-button",{key:n,staticClass:"ps-origin-btn ellipsis history-btn",attrs:{size:"mini"},on:{click:function(t){return e.clickHistory(r)}}},[e._v(e._s(e.getHistoryText(r)))])})),t("el-dropdown",{staticClass:"m-l-20",on:{command:e.clickHistory}},[t("span",{staticClass:"el-dropdown-link pointer"},[e._v(" 历史记录"),t("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),t("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},e._l(e.historyList,(function(r,n){return t("el-dropdown-item",{key:n,attrs:{command:r}},[e._v(e._s(e.getHistoryText(r)))])})),1)],1)],2)],1),t("div",{staticClass:"food-wrapper"},[e.currentShowMealFoods.length>0?t("ul",{staticClass:"food-box"},e._l(e.currentShowMealFoods,(function(r,n){return t("li",{key:r.id,class:["food-item",e.selectCurrentFood.includes(r.food)?"active":""],on:{click:function(t){return e.clickCurrentFood(r,n)}}},[t("el-tooltip",{attrs:{disabled:r.food_name.length<8,effect:"dark",content:r.food_name,placement:"top-start","open-delay":260}},[t("span",{staticClass:"ellipsis",staticStyle:{display:"block"}},[e._v(e._s(r.food_name))])])],1)})),0):t("el-empty",{attrs:{description:"暂无数据","image-size":200}},[t("div",{staticClass:"custom-img",attrs:{slot:"image"},slot:"image"})])],1)]),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loadingHistoryFood,expression:"loadingHistoryFood"}]},[t("div",{staticClass:"m-t-20 m-b-20"},[t("span",{staticClass:"m-r-30"},[e._v("历史餐段打标菜品：")]),t("VirtualListSelect",{staticClass:"m-r-20",attrs:{"collapse-tags":"",clearable:"",filterable:"",selectData:e.foodList,selectOption:e.selectOption,loading:e.loadingFoodSelect},model:{value:e.selectFood,callback:function(t){e.selectFood=t},expression:"selectFood"}}),t("el-button",{staticClass:"ps-origin-btn",on:{click:e.addNuteitionFood}},[e._v("添加菜品")]),t("el-button",{staticClass:"ps-origin-btn",on:{click:e.openFoodDrawer}},[e._v("新建菜品")])],1),t("div",{staticClass:"m-b-20"}),t("div",{staticClass:"food-wrapper"},[e.historyMealFoods.length>0?t("ul",{staticClass:"food-box"},e._l(e.historyMealFoods,(function(r,n){return t("li",{key:r.id,staticClass:"food-item",on:{click:function(t){return e.clickHistoryFood(r,n)}}},[t("el-tooltip",{attrs:{disabled:r.food_name.length<8,effect:"dark",content:r.food_name,placement:"top-start","open-delay":230}},[t("span",{staticClass:"ellipsis",staticStyle:{display:"block"}},[e._v(e._s(r.food_name))])])],1)})),0):t("el-empty",{attrs:{description:"暂无数据","image-size":200}},[t("div",{staticClass:"custom-img",attrs:{slot:"image"},slot:"image"})])],1)])])]),t("addNutritionOrderProject",{attrs:{show:e.showDrawer,infoData:e.drawerInfoData,size:560},on:{"update:show":function(t){e.showDrawer=t},confirm:e.confirmHandle}}),t("AddFoodDrawer",{attrs:{show:e.showFoodDrawer,size:920},on:{"update:show":function(t){e.showFoodDrawer=t},confirmFood:e.confirmFoodHandle}})],1)},a=[],i=r("b2f2"),o=r("7aba"),s=r("1fe1"),l=r("ed08"),c=r("2f62");function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}function d(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */d=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function c(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,r){return e[t]=r}}function f(e,t,r,n){var i=t&&t.prototype instanceof b?t:b,o=Object.create(i.prototype),s=new E(n||[]);return a(o,"_invoke",{value:O(e,r,s)}),o}function m(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var p="suspendedStart",h="suspendedYield",g="executing",y="completed",v={};function b(){}function w(){}function _(){}var k={};c(k,o,(function(){return this}));var L=Object.getPrototypeOf,I=L&&L(L(T([])));I&&I!==r&&n.call(I,o)&&(k=I);var x=_.prototype=b.prototype=Object.create(k);function D(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function C(e,t){function r(a,i,o,s){var l=m(e[a],e,i);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==u(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,o,s)}),(function(e){r("throw",e,o,s)})):t.resolve(d).then((function(e){c.value=e,o(c)}),(function(e){return r("throw",e,o,s)}))}s(l.arg)}var i;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){r(e,n,t,a)}))}return i=i?i.then(a,a):a()}})}function O(t,r,n){var a=p;return function(i,o){if(a===g)throw Error("Generator is already running");if(a===y){if("throw"===i)throw o;return{value:e,done:!0}}for(n.method=i,n.arg=o;;){var s=n.delegate;if(s){var l=F(s,n);if(l){if(l===v)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===p)throw a=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=g;var c=m(t,r,n);if("normal"===c.type){if(a=n.done?y:h,c.arg===v)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(a=y,n.method="throw",n.arg=c.arg)}}}function F(t,r){var n=r.method,a=t.iterator[n];if(a===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,F(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=m(a,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var o=i.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,v):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function N(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function S(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function E(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(N,this),this.reset(!0)}function T(t){if(t||""===t){var r=t[o];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,i=function r(){for(;++a<t.length;)if(n.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(u(t)+" is not iterable")}return w.prototype=_,a(x,"constructor",{value:_,configurable:!0}),a(_,"constructor",{value:w,configurable:!0}),w.displayName=c(_,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,c(e,l,"GeneratorFunction")),e.prototype=Object.create(x),e},t.awrap=function(e){return{__await:e}},D(C.prototype),c(C.prototype,s,(function(){return this})),t.AsyncIterator=C,t.async=function(e,r,n,a,i){void 0===i&&(i=Promise);var o=new C(f(e,r,n,a),i);return t.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},D(x),c(x,l,"Generator"),c(x,o,(function(){return this})),c(x,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=T,E.prototype={constructor:E,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(S),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(n,a){return s.type="throw",s.arg=t,r.next=n,a&&(r.method="next",r.arg=e),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var l=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),S(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;S(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:T(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),v}},t}function f(e,t){return y(e)||g(e,t)||p(e,t)||m()}function m(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function p(e,t){if(e){if("string"==typeof e)return h(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?h(e,t):void 0}}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function g(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,i,o,s=[],l=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){c=!0,a=e}finally{try{if(!l&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw a}}return s}}function y(e){if(Array.isArray(e))return e}function v(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function b(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){v(i,n,a,o,s,"next",e)}function s(e){v(i,n,a,o,s,"throw",e)}o(void 0)}))}}function w(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function _(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?w(Object(r),!0).forEach((function(t){k(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):w(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function k(e,t,r){return(t=L(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function L(e){var t=I(e,"string");return"symbol"==u(t)?t:t+""}function I(e,t){if("object"!=u(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=u(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var x={name:"NutritionOrders",components:{addNutritionOrderProject:i["default"],AddFoodDrawer:o["default"],VirtualListSelect:s["a"]},props:{},data:function(){return{isLoading:!1,showDrawer:!1,drawerInfoData:{},showFoodDrawer:!1,markNutritionOrder:{},markCount:0,loadingCurrentFood:!1,selectCurrentFood:[],currentMealFoods:[],loadingHistoryFood:!1,selectHistoryFood:[],historyMealFoods:[],loadingFood:!1,loadingFoodSelect:!1,selectFood:"",foodList:[],selectOption:{label:"name",value:"id"},addToCurrentMealFoods:[],historyList:[]}},computed:_({markingText:function(){return"".concat(this.drawerInfoData.companyName||""," ").concat(this.drawerInfoData.date||""," ").concat(this.drawerInfoData.mealName||"")},markingFoodText:function(){var e="";return this.markNutritionOrder.payment_food_list&&this.markNutritionOrder.payment_food_list.length>0&&(e=this.markNutritionOrder.payment_food_list.join("，")),this.drawerInfoData.companyId&&this.markNutritionOrder.id?!e&&this.markNutritionOrder.id?"暂无打标菜品数据":e:"暂无需要打标的数据"},currentShowMealFoods:function(){var e=Object(l["db"])([].concat(this.currentMealFoods,this.addToCurrentMealFoods),"food");return e},latelyHistory:function(){return this.historyList.slice(0,2)}},Object(c["c"])(["userInfo"])),watch:{},created:function(){this.$route.query&&Object.keys(this.$route.query).length>0&&(this.drawerInfoData=this.$route.query,this.confirmHandle(this.drawerInfoData));var e=localStorage.getItem("NutritionOrdersHistory".concat(this.userInfo.username));try{e&&(this.historyList=JSON.parse(e))}catch(t){}},mounted:function(){},methods:{refreshHandle:function(){this.drawerInfoData.companyId&&this.drawerInfoData.date&&this.drawerInfoData.mealType?(this.selectCurrentFood=[],this.getNuteitionOrderList(),this.getMarkFoodList("current"),this.getMarkFoodList(),this.getFoodlist()):this.$message.error("请先选择打标对象！")},getNuteitionOrderList:function(){var e=this;return b(d().mark((function t(){var r,n,a,i,o,s;return d().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.drawerInfoData.companyId&&e.drawerInfoData.date&&e.drawerInfoData.mealType){t.next=3;break}return e.$message.error("请先选择打标对象！"),t.abrupt("return");case 3:return e.isLoading=!0,t.next=6,e.$to(e.$apis.apiBackgroundAdminMarkNutritionOrderMarkNutritionOrderListPost({company_id:e.drawerInfoData.companyId,mark_date:e.drawerInfoData.date,meal_type:e.drawerInfoData.mealType,page:1,page_size:1}));case 6:if(r=t.sent,n=f(r,2),a=n[0],i=n[1],e.isLoading=!1,!a){t.next=14;break}return e.$message.error(a.message),t.abrupt("return");case 14:0===i.code?(e.markNutritionOrder=(null===(o=i.data)||void 0===o?void 0:o.results[0])||{},e.markCount=(null===(s=i.data.collect)||void 0===s?void 0:s.is_mark_count)||0):e.$message.error(i.msg);case 15:case"end":return t.stop()}}),t)})))()},getMarkFoodList:function(e){var t=this;return b(d().mark((function r(){var n,a,i,o,s;return d().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return n={company_id:t.drawerInfoData.companyId,meal_type:t.drawerInfoData.mealType},"current"===e?(n.mark_date=t.drawerInfoData.date,t.loadingCurrentFood=!0):t.loadingHistoryFood=!0,r.next=4,t.$to(t.$apis.apiBackgroundAdminMarkNutritionOrderMarkFoodListPost(_(_({},n),{},{page:1,page_size:999999})));case 4:if(a=r.sent,i=f(a,2),o=i[0],s=i[1],"current"===e?(t.loadingCurrentFood=!1,t.currentMealFoods=[]):(t.loadingHistoryFood=!1,t.historyMealFoods=[]),!o){r.next=12;break}return t.$message.error(o.message),r.abrupt("return");case 12:0===s.code?"current"===e?t.currentMealFoods=s.data.results:(t.historyMealFoods=s.data.results,t.setDisabledFood()):t.$message.error(s.msg);case 13:case"end":return r.stop()}}),r)})))()},getFoodlist:function(){var e=this;return b(d().mark((function t(){var r,n,a,i;return d().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.loadingFoodSelect){t.next=2;break}return t.abrupt("return");case 2:return e.loadingFoodSelect=!0,t.next=5,e.$to(e.$apis.apiBackgroundAdminFoodListPost({page:1,page_size:999999}));case 5:if(r=t.sent,n=f(r,2),a=n[0],i=n[1],e.loadingFoodSelect=!1,!a){t.next=13;break}return e.$message.error(a.message),t.abrupt("return");case 13:0===i.code?(e.foodList=i.data.results,e.setDisabledFood()):e.$message({type:"error",duration:1e3,message:i.msg});case 14:case"end":return t.stop()}}),t)})))()},addNuteitionFood:function(){var e=this;return b(d().mark((function t(){var r,n,a,i;return d().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.selectFood){t.next=2;break}return t.abrupt("return",e.$message.error("请先选择菜品！"));case 2:if(e.drawerInfoData.companyId&&e.drawerInfoData.date&&e.drawerInfoData.mealType){t.next=5;break}return e.$message.error("请先选择打标对象！"),t.abrupt("return");case 5:return e.loadingFood=!0,t.next=8,e.$to(e.$apis.apiBackgroundAdminMarkNutritionOrderMarkFoodAddPost({company:e.drawerInfoData.companyId,mark_date:e.drawerInfoData.date,meal_type:e.drawerInfoData.mealType,food:e.selectFood}));case 8:if(r=t.sent,n=f(r,2),a=n[0],i=n[1],e.loadingFood=!1,!a){t.next=16;break}return e.$message.error(a.message),t.abrupt("return");case 16:0===i.code?(e.selectFood="",e.getMarkFoodList("current"),e.getMarkFoodList()):e.$message.error(i.msg);case 17:case"end":return t.stop()}}),t)})))()},openFoodDrawer:function(){this.drawerInfoData.companyId&&this.drawerInfoData.date&&this.drawerInfoData.mealType?this.showFoodDrawer=!0:this.$message.error("请先选择打标对象！")},confirmHandle:function(e){e.mealType===this.drawerInfoData.mealType&&e.companyId===this.drawerInfoData.companyId&&e.date===this.drawerInfoData.date||(this.addToCurrentMealFoods=[],this.selectCurrentFood=[]),this.drawerInfoData=Object.assign({},e),this.getNuteitionOrderList(),0===this.foodList.length?this.getFoodlist():this.foodList=this.foodList.map((function(e){return e.disabled=!1,e})),this.$changehash({name:"NutritionOrders",query:_({},this.drawerInfoData)}),this.getMarkFoodList("current"),this.getMarkFoodList()},clickCurrentFood:Object(l["d"])((function(e,t){var r=this.selectCurrentFood.indexOf(e.food);r>-1?this.selectCurrentFood.splice(r,1):this.selectCurrentFood.push(e.food)}),100),clickHistoryFood:function(e,t){var r=this.currentMealFoods.some((function(t){return t.food===e.food}));r||(this.selectCurrentFood.push(e.food),this.addToCurrentMealFoods.push(e))},sortSelectCurrentFood:function(e){var t=this;e.sort((function(e,r){var n=t.selectCurrentFood.indexOf(e.food),a=t.selectCurrentFood.indexOf(r.food);return n>-1&&a>-1?1:n-a>0?-1:1}))},setDisabledFood:function(){if(this.foodList.length>0&&this.historyMealFoods.length>0){var e=this.historyMealFoods.map((function(e){return e.food}));this.foodList=this.foodList.map((function(t){return e.includes(t.id)&&(t.disabled=!0),t}))}},submitHandle:function(){var e=this;return b(d().mark((function t(){var r,n,a,i,o,s;return d().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.markNutritionOrder.id){t.next=3;break}return e.$message.error("暂无需要打标订单数据，请检查！"),t.abrupt("return");case 3:return r={id:e.markNutritionOrder.id,is_mark:!0},e.selectCurrentFood.length>0?r.food_ids=e.selectCurrentFood:r.is_jump=!0,e.isLoading=!0,e.loadingFood=!0,t.next=9,e.$to(e.$apis.apiBackgroundAdminMarkNutritionOrderMarkNutritionOrderModifyPost(r));case 9:if(n=t.sent,a=f(n,2),i=a[0],o=a[1],e.loadingFood=!1,!i){t.next=18;break}return e.isLoading=!1,e.$message.error(i.message),t.abrupt("return");case 18:0===o.code?(r.food_ids&&(s=e.currentShowMealFoods.filter((function(e){return r.food_ids.includes(e.food)})),e.historyList.unshift(s),e.historyList.length>10&&e.historyList.pop(),localStorage.setItem("NutritionOrdersHistory".concat(e.userInfo.username),JSON.stringify(e.historyList))),e.selectCurrentFood=[],e.getNuteitionOrderList(),e.getMarkFoodList("current"),e.getMarkFoodList()):(e.isLoading=!1,e.$message.error(o.msg));case 19:case"end":return t.stop()}}),t)})))()},confirmFoodHandle:function(){this.getFoodlist()},getHistoryText:function(e){return e&&0!==e.length?e.reduce((function(e,t){return e?"".concat(e,"+").concat(t.food_name):t.food_name}),""):""},clickHistory:Object(l["d"])((function(e){this.selectCurrentFood=e.map((function(e){return e.food}))}),100)}},D=x,C=(r("0ac1"),r("2877")),O=Object(C["a"])(D,n,a,!1,null,"19560b4c",null);t["default"]=O.exports},"7aba":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("el-drawer",e._g(e._b({staticClass:"drawer-wrapper",attrs:{title:"新建菜品",visible:e.showDrawer,direction:e.direction,wrapperClosable:e.wrapperClosable},on:{"update:visible":function(t){e.showDrawer=t}}},"el-drawer",e.$attrs,!1),e.$listeners),[t("div",{staticClass:"add-food-drawer-box"},[t("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"foodRef",staticClass:"AddFoodDrawer",attrs:{rules:e.formRuls,model:e.formData,"label-width":"80px",size:"small"}},[t("el-form-item",{staticClass:"form-content-flex",attrs:{label:"菜品名称",prop:"name"}},[t("el-input",{staticClass:"ps-input",staticStyle:{width:"50%"},attrs:{placeholder:"请输入菜品名称"},model:{value:e.formData.name,callback:function(t){e.$set(e.formData,"name",t)},expression:"formData.name"}})],1),t("el-form-item",{attrs:{label:"分类",prop:"categoryId"}},[t("el-select",{staticClass:"ps-select",staticStyle:{width:"50%"},attrs:{placeholder:"请下拉选择分类","popper-class":"ps-popper-select","collapse-tags":"",filterable:"",clearable:"","popper-append-to-body":!1},model:{value:e.formData.categoryId,callback:function(t){e.$set(e.formData,"categoryId",t)},expression:"formData.categoryId"}},e._l(e.foodCategoryList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),t("el-form-item",{staticClass:"upload-block-label upload-hidden",attrs:{label:"图片",prop:"foodImages"}},[t("div",{staticClass:"inline-block upload-w"},[t("el-upload",{ref:"fileUpload",class:{"file-upload":!0,"hide-upload":e.formData.foodImagesList.length>0},attrs:{drag:"",action:e.serverUrl,data:e.uploadParams,"file-list":e.formData.foodImagesList,"on-success":e.handleFoodImgSuccess,"on-change":e.handelChange,"before-upload":e.beforeFoodImgUpload,limit:1,"list-type":"picture-card",multiple:!1,headers:e.headersOpts,accept:".jpeg,.jpg,.png,.bmp"},scopedSlots:e._u([{key:"file",fn:function(r){var n=r.file;return t("div",{directives:[{name:"loading",rawName:"v-loading",value:"uploading"===n.status,expression:"file.status==='uploading'"}],attrs:{"element-loading-text":"上传中"}},[t("div",{staticClass:"upload-food-img"},[t("el-image",{staticClass:"el-upload-dragger",attrs:{src:n.url,fit:"contain"}})],1),t("span",{staticClass:"el-upload-list__item-actions"},[t("span",{staticClass:"el-upload-list__item-preview",on:{click:function(t){return e.handlePictureCardPreview(n)}}},[t("i",{staticClass:"el-icon-zoom-in"})]),t("span",{staticClass:"el-upload-list__item-delete",on:{click:function(t){return e.handleImgRemove(n,"foodImages")}}},[t("i",{staticClass:"el-icon-delete"})])])])}}])},[e.fileLists.length<1?t("div",{staticClass:"upload-t"},[t("i",{staticClass:"el-icon-circle-plus"}),t("div",{staticClass:"el-upload__text"},[e._v(" 上传菜品/商品图片 ")])]):e._e()])],1),t("div",{staticClass:"inline-block upload-tips"},[e._v(" 上传：菜品/商品图片。"),t("br"),e._v(" 建议图片需清晰，图片内容与名称相符。"),t("br"),e._v(" 仅支持jpg、png、bmp格式，大小不超过5M ")])]),t("el-form-item",{attrs:{label:"食材占比"}},[t("div",{staticClass:"table-title m-b-6"},[e._v(" 食材占比 "),t("span",{staticClass:"tip-o-7"},[e._v("（菜品每100g所含食材占比，相加必须等于100%）")]),t("el-button",{staticClass:"ps-btn",staticStyle:{"margin-right":"15px"},attrs:{type:"primary",size:"small",icon:"el-icon-plus"},on:{click:e.addIngredients}},[e._v("添加")])],1),t("div",{class:[e.errorMsg.percentageError?"error-border":""]},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.formData.ingredientList,size:"mini","header-row-class-name":"ps-table-header-row"}},[t("el-table-column",{attrs:{prop:"no",label:"食材",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("VirtualListSelect",{attrs:{"collapse-tags":"",clearable:"",filterable:"",selectData:r.row.selectFoodIngredient},on:{change:e.changeIngredient},model:{value:r.row.selectId,callback:function(t){e.$set(r.row,"selectId",t)},expression:"scope.row.selectId"}})]}}])}),t("el-table-column",{attrs:{prop:"id",label:"占比（%）",align:"center","min-width":"120px"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("div",{staticClass:"cantent ps-flex-align-c flex-align-c"},[t("el-slider",{staticClass:"cantent",attrs:{"show-input":"","input-size":"mini"},on:{change:e.changePercentage},model:{value:r.row.percentage,callback:function(t){e.$set(r.row,"percentage",t)},expression:"scope.row.percentage"}}),e._v("% ")],1)]}}])}),t("el-table-column",{attrs:{prop:"",label:"操作",align:"center",width:"100px"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.deleteIngredientHandle(r.row.index)}}},[e._v("删除")])]}}])})],1)],1),e.errorMsg.percentageError?t("div",{staticStyle:{color:"red",padding:"20px"}},[e._v(e._s(e.errorMsg.percentageError))]):e._e()]),t("el-form-item",{attrs:{label:"营养信息"}},[e._l(e.currentNutritionList,(function(r){return[t("div",{key:r.key,staticClass:"inline-block nutrition-item"},[t("span",{staticClass:"inline-block nutrition-label m-r-6 ellipsis"},[e._v(e._s(r.name+"："))]),t("span",{staticClass:"inline-block nutrition-value ellipsis"},[e._v(e._s(e.formData[r.key]?e.formData[r.key]:0))]),t("span",{staticClass:"inline-block nutrition-unit"},[e._v(e._s(r.unit))])])]})),t("div",{staticClass:"pointer"},[t("span",{staticStyle:{color:"#027DB4","text-decoration":"underline"},on:{click:function(t){e.showAll=!e.showAll}}},[e._v(e._s(e.showAll?"收起":"点击查看更多营养信息"))])])],2)],1),t("div",{staticClass:"footer m-l-46"},[t("el-button",{staticStyle:{width:"120px"},attrs:{disabled:e.isLoading},on:{click:e.closeHandler}},[e._v("取消")]),t("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary",disabled:e.isLoading},on:{click:e.submitHandler}},[e._v("保存")])],1)],1),t("el-dialog",{attrs:{visible:e.dialogVisible},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("img",{attrs:{width:"100%",src:e.dialogImageUrl,alt:""}})])],1)},a=[],i=r("ed08"),o=r("015b"),s=r("da92"),l=(r("3fa5"),r("1fe1"));function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function u(e,t){return h(e)||p(e,t)||f(e,t)||d()}function d(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(e,t){if(e){if("string"==typeof e)return m(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?m(e,t):void 0}}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function p(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,i,o,s=[],l=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){c=!0,a=e}finally{try{if(!l&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw a}}return s}}function h(e){if(Array.isArray(e))return e}function g(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */g=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function d(e,t,r,n){var i=t&&t.prototype instanceof b?t:b,o=Object.create(i.prototype),s=new E(n||[]);return a(o,"_invoke",{value:O(e,r,s)}),o}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var m="suspendedStart",p="suspendedYield",h="executing",y="completed",v={};function b(){}function w(){}function _(){}var k={};u(k,o,(function(){return this}));var L=Object.getPrototypeOf,I=L&&L(L(T([])));I&&I!==r&&n.call(I,o)&&(k=I);var x=_.prototype=b.prototype=Object.create(k);function D(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function C(e,t){function r(a,i,o,s){var l=f(e[a],e,i);if("throw"!==l.type){var u=l.arg,d=u.value;return d&&"object"==c(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,o,s)}),(function(e){r("throw",e,o,s)})):t.resolve(d).then((function(e){u.value=e,o(u)}),(function(e){return r("throw",e,o,s)}))}s(l.arg)}var i;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){r(e,n,t,a)}))}return i=i?i.then(a,a):a()}})}function O(t,r,n){var a=m;return function(i,o){if(a===h)throw Error("Generator is already running");if(a===y){if("throw"===i)throw o;return{value:e,done:!0}}for(n.method=i,n.arg=o;;){var s=n.delegate;if(s){var l=F(s,n);if(l){if(l===v)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===m)throw a=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=h;var c=f(t,r,n);if("normal"===c.type){if(a=n.done?y:p,c.arg===v)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(a=y,n.method="throw",n.arg=c.arg)}}}function F(t,r){var n=r.method,a=t.iterator[n];if(a===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,F(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=f(a,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var o=i.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,v):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function N(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function S(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function E(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(N,this),this.reset(!0)}function T(t){if(t||""===t){var r=t[o];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,i=function r(){for(;++a<t.length;)if(n.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(c(t)+" is not iterable")}return w.prototype=_,a(x,"constructor",{value:_,configurable:!0}),a(_,"constructor",{value:w,configurable:!0}),w.displayName=u(_,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,u(e,l,"GeneratorFunction")),e.prototype=Object.create(x),e},t.awrap=function(e){return{__await:e}},D(C.prototype),u(C.prototype,s,(function(){return this})),t.AsyncIterator=C,t.async=function(e,r,n,a,i){void 0===i&&(i=Promise);var o=new C(d(e,r,n,a),i);return t.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},D(x),u(x,l,"Generator"),u(x,o,(function(){return this})),u(x,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=T,E.prototype={constructor:E,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(S),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(n,a){return s.type="throw",s.arg=t,r.next=n,a&&(r.method="next",r.arg=e),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var l=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),S(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;S(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:T(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),v}},t}function y(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function v(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){y(i,n,a,o,s,"next",e)}function s(e){y(i,n,a,o,s,"throw",e)}o(void 0)}))}}var b={inheritAttrs:!1,name:"AddFoodDrawer",components:{VirtualListSelect:l["a"]},props:{show:{required:!0,type:Boolean},wrapperClosable:{type:Boolean,default:!0},direction:{type:String,default:"rtl"},infoData:{type:Object,default:function(){return{}}}},data:function(){var e=function(e,t,r){if(t){var n=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;n.test(t)?r():r(new Error("营养数据有误，仅支持保留两位小数"))}else r()};return{type:"add",isLoading:!1,formData:{name:"",attributes:"foods",tasteList:[],foodImages:[],foodImagesList:[],extraImages:[],ingredientList:[],food_id:"",categoryId:""},fileLists:[],serverUrl:"/api/background/file/upload",headersOpts:{TOKEN:Object(i["B"])()},formRuls:{name:[{required:!0,message:"食材名称不能为空",trigger:"blur"}],attributes:[{required:!0,message:"请选择属性",trigger:"blur"}],nutrition:[{validator:e,trigger:"change"}],foodImages:[{required:!0,message:"请上传菜品图片",trigger:"change"}],categoryId:[{required:!0,message:"请选择分类",trigger:"blur"}]},nutritionList:o["NUTRITION_LIST"],actionUrl:"",uploadParams:{prefix:"super_food_img"},uploadUrl:"",tableData:[{}],ingredientList:[],allSelectIngredient:[],errorMsg:{percentageError:""},ruleSingleInfo:{},dialogImageUrl:"",dialogVisible:!1,showFoodImg:!0,foodCategoryList:[],showAll:!1,uploading:!1,uploadingExtra:!1}},computed:{showDrawer:{get:function(){return this.show},set:function(e){this.$emit("update:show",e)}},currentNutritionList:function(){var e=[];return e=this.showAll?this.nutritionList:this.nutritionList.slice(0,4),e}},created:function(){this.type=this.$route.query.type,this.initLoad()},mounted:function(){},methods:{initLoad:function(){var e=this;return v(g().mark((function t(){return g().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,e.getIngredientslist();case 3:return t.next=5,e.foodFoodCategoryList();case 5:e.isLoading=!1,e.initIngredient(),e.setNutritonData({});case 8:case"end":return t.stop()}}),t)})))()},searchHandle:Object(i["d"])((function(){this.currentPage=1}),300),setNutritonData:function(e){var t=this;e.nutrition||(e.nutrition={});var r=e.nutrition.element?JSON.parse(Object(i["R"])(e.nutrition.element)):{},n=e.nutrition.vitamin?JSON.parse(Object(i["R"])(e.nutrition.vitamin)):{};o["NUTRITION_LIST"].forEach((function(a){"default"===a.type&&t.$set(t.formData,a.key,e.nutrition[a.key]?e.nutrition[a.key]:0),"element"===a.type&&t.$set(t.formData,a.key,r[a.key]?r[a.key]:0),"vitamin"===a.type&&t.$set(t.formData,a.key,n[a.key]?n[a.key]:0)}))},getIngredientslist:function(){var e=this;return v(g().mark((function t(){var r,n,a,o;return g().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(i["Z"])(e.$apis.apiBackgroundAdminIngredientIngredientNamePost({page:1,page_size:999999}));case 2:if(r=t.sent,n=u(r,2),a=n[0],o=n[1],e.isLoading=!1,!a){t.next=10;break}return e.$message.error(a.message),t.abrupt("return");case 10:0===o.code?e.ingredientList=o.data:e.$message.error(o.msg);case 11:case"end":return t.stop()}}),t)})))()},initIngredient:function(e){var t=this;this.formData.ingredientList=[],"add"===this.type?this.formData.ingredientList.push({index:this.formData.ingredientList.length,selectId:"",percentage:0,selectFoodIngredient:Object(i["f"])(this.ingredientList)}):e&&(this.formData.ingredientList=e.ingredients_list.map((function(e,r){return e.index=r,e.selectId=Number(e.ingredient_id),e.percentage=e.ingredient_scale,e.selectFoodIngredient=Object(i["f"])(t.ingredientList),t.ingredientList.map((function(t){t.id===e.selectId&&(e.nutrition=t.nutrition_info)})),e})),this.isDisabledOtherIngredients())},formatParams:function(){var e=this,t={name:this.formData.name,attributes:this.formData.attributes,image:this.formData.foodImages[0],extra_image:this.formData.extraImages,ingredient_list:[],nutrition_info:{},category_id:this.formData.categoryId};this.formData.food_id&&(t.food_id=this.formData.food_id),this.formData.ingredientList.map((function(e){if(e.selectId){var r={ingredient_id:e.selectId,ingredient_scale:e.percentage};t.ingredient_list.push(r)}}));var r={},n={};return o["NUTRITION_LIST"].forEach((function(a){"default"===a.type&&(t.nutrition_info[a.key]=e.formData[a.key]),"element"===a.type&&(r[a.key]=e.formData[a.key]),"vitamin"===a.type&&(n[a.key]=e.formData[a.key])})),t.nutrition_info.element=JSON.stringify(r),t.nutrition_info.vitamin=JSON.stringify(n),t},addIngredients:function(){this.formData.ingredientList.push({index:this.formData.ingredientList.length,selectId:"",percentage:0,selectFoodIngredient:Object(i["f"])(this.ingredientList)}),this.isDisabledOtherIngredients()},deleteIngredientHandle:function(e){this.formData.ingredientList.splice(e,1),this.isDisabledOtherIngredients(),this.computedNutritionAndPercentage(),this.changePercentage()},changeIngredient:function(e){var t={};this.ingredientList.map((function(r){r.id===e&&(t=r)})),this.formData.ingredientList.forEach((function(e){e.selectId===t.id&&(e.nutrition=t.nutrition_info)})),this.errorMsg.percentageError="",this.isDisabledOtherIngredients(),this.computedNutritionAndPercentage()},isDisabledOtherIngredients:function(){var e=this;this.allSelectIngredient=[],this.formData.ingredientList.map((function(t,r){t.selectId&&e.allSelectIngredient.push(t.selectId)})),this.formData.ingredientList.forEach((function(t,r){t.selectFoodIngredient.forEach((function(r){e.allSelectIngredient.includes(r.id)&&t.selectId!==r.id?e.$set(r,"disabled",!0):e.$set(r,"disabled",!1)}))}))},computedNutritionAndPercentage:function(){var e=this,t={};o["NUTRITION_LIST"].forEach((function(e){t[e.key]=0}));var r=0;this.formData.ingredientList.map((function(n,a){if(n.selectId){a<e.allSelectIngredient.length-1?(n.percentage=parseInt(s["a"].divide(100,e.allSelectIngredient.length)),r=s["a"].plus(n.percentage,r)):n.percentage=parseInt(s["a"].minus(100,r));var o=n.percentage/100;if(n.nutrition||(n.nutrition={}),t.energy_kcal=+n.nutrition.energy_kcal?s["a"].plus(t.energy_kcal,n.nutrition.energy_kcal*o):t.energy_kcal?t.energy_kcal:0,t.protein=+n.nutrition.protein?s["a"].plus(t.protein,n.nutrition.protein*o):t.protein?t.protein:0,t.axunge=+n.nutrition.axunge?s["a"].plus(t.axunge,n.nutrition.axunge*o):t.axunge?t.axunge:0,t.carbohydrate=+n.nutrition.carbohydrate?s["a"].plus(t.carbohydrate,n.nutrition.carbohydrate*o):t.carbohydrate?t.carbohydrate:0,t.cholesterol=+n.nutrition.cholesterol?s["a"].plus(t.cholesterol,n.nutrition.cholesterol*o):t.cholesterol?t.cholesterol:0,t.dietary_fiber=+n.nutrition.dietary_fiber?s["a"].plus(t.dietary_fiber,n.nutrition.dietary_fiber*o):t.dietary_fiber?t.dietary_fiber:0,n.nutrition.element&&n.nutrition.vitamin)try{var l=JSON.parse(Object(i["R"])(n.nutrition.element)),c=JSON.parse(Object(i["R"])(n.nutrition.vitamin));for(var u in l)t[u]=s["a"].plus(t[u],+l[u]?l[u]*o:0);for(var d in c)t[d]=s["a"].plus(t[d],+c[d]?c[d]*o:0)}catch(f){}e.deepFormIngredients&&e.deepFormIngredients.length&&e.deepFormIngredients.forEach((function(e){e.id===n.id&&(n.status=!0)}))}})),this.nutritionList.forEach((function(r){var n=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;n.test(t[r.key])?e.$set(e.formData,r.key,t[r.key]):e.$set(e.formData,r.key,t[r.key].toFixed(2))}))},setNutritionAndPercentage:function(){var e=this,t={};o["NUTRITION_LIST"].forEach((function(e){t[e.key]=0})),this.formData.ingredientList.map((function(e,r){if(e.selectId){e.nutrition||(e.nutrition={});var n=e.percentage/100;if(t.energy_kcal=+e.nutrition.energy_kcal?s["a"].plus(t.energy_kcal,e.nutrition.energy_kcal*n):t.energy_kcal?t.energy_kcal:0,t.protein=+e.nutrition.protein?s["a"].plus(t.protein,e.nutrition.protein*n):t.protein?t.protein:0,t.axunge=+e.nutrition.axunge?s["a"].plus(t.axunge,e.nutrition.axunge*n):t.axunge?t.axunge:0,t.carbohydrate=+e.nutrition.carbohydrate?s["a"].plus(t.carbohydrate,e.nutrition.carbohydrate*n):t.carbohydrate?t.carbohydrate:0,t.cholesterol=+e.nutrition.cholesterol?s["a"].plus(t.cholesterol,e.nutrition.cholesterol*n):t.cholesterol?t.cholesterol:0,t.dietary_fiber=+e.nutrition.dietary_fiber?s["a"].plus(t.dietary_fiber,e.nutrition.dietary_fiber*n):t.dietary_fiber?t.dietary_fiber:0,e.nutrition.element&&e.nutrition.vitamin)try{var a=JSON.parse(Object(i["R"])(e.nutrition.element)),o=JSON.parse(Object(i["R"])(e.nutrition.vitamin));for(var l in a)t[l]=s["a"].plus(t[l],+a[l]?a[l]*n:0);for(var c in o)t[c]=s["a"].plus(t[c],+o[c]?o[c]*n:0)}catch(u){}}})),this.nutritionList.forEach((function(r){var n=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;n.test(t[r.key])?e.$set(e.formData,r.key,t[r.key]):e.$set(e.formData,r.key,t[r.key].toFixed(2))}))},changePercentage:function(e){this.setNutritionAndPercentage();var t=this.formData.ingredientList.reduce((function(e,t){return s["a"].plus(t.percentage,e)}),0);this.errorMsg.percentageError=t>100||t<100?"菜品每100g所含食材占比，相加必须等于100%":"",this.formData.ingredientList.length||(this.errorMsg.percentageError="")},handelChange:function(e,t){this.uploadParams.key=this.uploadParams.prefix+(new Date).getTime()+Math.floor(150*Math.random())+".png"},perviewFoodImg:function(e){this.dialogImageUrl=e,this.dialogVisible=!0},handleFoodImgSuccess:function(e,t,r){this.uploading=!1,0===e.code?(this.formData.foodImagesList=r,this.formData.foodImages.push(e.data.public_url),this.$refs.foodRef.clearValidate("foodImages")):this.$message.error(e.msg)},handleImgRemove:function(e,t){var r=this.formData[t+"List"].findIndex((function(t){return t.url===e.url}));this.formData[t].splice(r,1),this.formData[t+"List"].splice(r,1)},beforeFoodImgUpload:function(e){return this.beforeImgUpload(e,"uploading")},beforeImgUpload:function(e,t){var r=[".jpeg",".jpg",".png",".bmp"],n=e.size/1024/1024<5;return r.includes(Object(i["A"])(e.name))?n?void(t&&(this[t]=!0)):(this.$message.error("上传图片大小不能超过 5MB!"),!1):(this.$message.error("上传图片只能是 JPG 格式或者PNG格式!"),!1)},handlePictureCardPreview:function(e){this.dialogImageUrl=e.url,this.dialogVisible=!0},submitHandler:function(){var e=this;this.$refs.foodRef.validate((function(t){if(t&&!e.errorMsg.percentageError){if(e.isLoading)return e.$message.error("请勿重复提交！");e.addFoodList()}}))},addFoodList:function(){var e=this;return v(g().mark((function t(){var r,n,a,o;return g().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(i["Z"])(e.$apis.apiBackgroundAdminFoodAddPost(e.formatParams()));case 3:if(r=t.sent,n=u(r,2),a=n[0],o=n[1],e.isLoading=!1,!a){t.next=11;break}return e.$message.error(a.message),t.abrupt("return");case 11:0===o.code?(e.$message.success(o.msg),e.resetForm(),e.showDrawer=!1,e.$emit("confirmFood")):2===o.code?e.$message.error("已有相同名称菜品！"):e.$message.error(o.msg);case 12:case"end":return t.stop()}}),t)})))()},closeHandler:function(){this.resetForm(),this.showDrawer=!1},resetForm:function(){this.formData={name:"",attributes:"foods",tasteList:[],foodImages:[],foodImagesList:[],extraImages:[],ingredientList:[],food_id:"",categoryId:""},this.fileLists=[],this.errorMsg.percentageError=""},foodFoodCategoryList:function(){var e=this;return v(g().mark((function t(){var r,n,a,o;return g().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(i["Z"])(e.$apis.apiBackgroundAdminFoodCategoryListPost({page:1,page_size:999999}));case 2:if(r=t.sent,n=u(r,2),a=n[0],o=n[1],!a){t.next=9;break}return e.$message.error(a.message),t.abrupt("return");case 9:0===o.code?e.foodCategoryList=o.data.results:e.$message.error(o.msg);case 10:case"end":return t.stop()}}),t)})))()}}},w=b,_=(r("8736"),r("2877")),k=Object(_["a"])(w,n,a,!1,null,null,null);t["default"]=k.exports},8736:function(e,t,r){"use strict";r("aa46f")},a86c:function(e,t,r){"use strict";r("ff47")},aa46f:function(e,t,r){},b2f2:function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("el-drawer",e._g(e._b({staticClass:"drawer-wrapper",attrs:{title:"选择项目",visible:e.showDrawer,direction:e.direction,wrapperClosable:e.wrapperClosable},on:{"update:visible":function(t){e.showDrawer=t}}},"el-drawer",e.$attrs,!1),e.$listeners),[t("div",{staticClass:"p-20"},[t("el-form",{ref:"formRef",attrs:{model:e.formData,"label-width":"80px",rules:e.formRules}},[t("el-form-item",{attrs:{label:"项目",prop:"companyId"}},[t("company-select",{staticClass:"search-item-w ps-select",staticStyle:{width:"300px"},attrs:{clearable:!0,filterable:!0,options:{label:"name",value:"company"},params:e.companyParams,placeholder:"请选择项目"},on:{getselect:e.getCompanySelect},model:{value:e.formData.companyId,callback:function(t){e.$set(e.formData,"companyId",t)},expression:"formData.companyId"}})],1),t("el-form-item",{attrs:{label:"日期",prop:"date"}},[t("el-date-picker",{staticStyle:{width:"300px"},attrs:{type:"date",placeholder:"请选择日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:e.formData.date,callback:function(t){e.$set(e.formData,"date",t)},expression:"formData.date"}})],1),t("el-form-item",{attrs:{label:"餐段",prop:"mealType"}},[t("el-select",{staticClass:"ps-input",staticStyle:{width:"300px"},attrs:{clearable:"",filterable:"",placeholder:"请选择餐段"},on:{change:e.changeMeal},model:{value:e.formData.mealType,callback:function(t){e.$set(e.formData,"mealType",t)},expression:"formData.mealType"}},e._l(e.mealTypeList,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("el-form-item",{staticClass:"m-t-60",attrs:{label:"",prop:""}},[t("el-button",{staticClass:"ps-cancel min-btn-w",on:{click:e.clickCancelHandle}},[e._v("取消")]),t("el-button",{staticClass:"ps-origin-btn min-btn-w",on:{click:e.clickConfirmHandle}},[e._v("确定")])],1)],1)],1)])},a=[],i=r("6e71"),o=r("c9d9"),s=r("ed08"),l={name:"",components:{CompanySelect:i["a"]},props:{show:{required:!0,type:Boolean},wrapperClosable:{type:Boolean,default:!0},direction:{type:String,default:"rtl"},infoData:{type:Object,default:function(){return{}}}},data:function(){return{formData:{companyName:"",companyId:"",date:Object(s["M"])(new Date,"{y}-{m}-{d}"),mealType:"",mealName:""},formRules:{companyId:[{required:!0,message:"请选择项目",trigger:"change"}],date:[{required:!0,message:"请选择日期",trigger:"change"}],mealType:[{required:!0,message:"请选择餐段",trigger:"change"}]},mealTypeList:o["a"],companyParams:{}}},computed:{showDrawer:{get:function(){return this.show&&(this.formData=Object.assign(this.formData,this.infoData)),this.show},set:function(e){this.$emit("update:show",e)}}},watch:{},created:function(){location.origin.indexOf("debug")<0?this.companyParams={company__in:[26,58]}:this.companyParams={}},mounted:function(){},methods:{getCompanySelect:function(e){this.formData.companyName=e.item.name},changeMeal:function(e){var t=this.mealTypeList.find((function(t){return t.value===e}));this.formData.mealName=t.label},clickCancelHandle:function(){this.showDrawer=!1},clickConfirmHandle:function(){var e=this;this.$refs.formRef.validate((function(t){t&&(e.$emit("confirm",e.formData),e.resetForm(),e.showDrawer=!1)}))},resetForm:function(){this.formData={companyName:"",companyId:"",date:"",mealType:""},this.$refs.formRef.clearValidate()}}},c=l,u=(r("a86c"),r("2877")),d=Object(u["a"])(c,n,a,!1,null,null,null);t["default"]=d.exports},c9d9:function(e,t,r){"use strict";r.d(t,"a",(function(){return i})),r.d(t,"d",(function(){return o})),r.d(t,"b",(function(){return s})),r.d(t,"c",(function(){return l})),r.d(t,"e",(function(){return c})),r.d(t,"f",(function(){return u})),r.d(t,"g",(function(){return d}));var n=r("5a0c"),a=r("da92"),i=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],o=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],s={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},l=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],c=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],u=(n().subtract(7,"day").format("YYYY-MM-DD"),n().format("YYYY-MM-DD"),function(e){return e?"number"===typeof e?a["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:a["a"].divide(e,100).toFixed(2):"0.00"}),d=function(e){return a["a"].times(e,100)}},fd99:function(e,t,r){},ff47:function(e,t,r){}}]);