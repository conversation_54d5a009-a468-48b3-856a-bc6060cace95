(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-dashboard-AddDataBoard","view-merchant-dashboard-components-BuduiCanteen","view-merchant-dashboard-components-MealQueue","view-merchant-dashboard-components-QueryDashboard","view-merchant-dashboard-components-SmartCanteenNutrition","view-merchant-dashboard-components-TakeMealReport"],{"00bf":function(t,e,r){"use strict";r.r(e);var o=function(){var t=this,e=t._self._c;return e("div",{staticClass:"smartCanteenNutrition"},[t._m(0),e("div",{staticClass:"chart"},[e("div",{staticClass:"center"},[e("div",{staticClass:"block h-770"},[e("div",{staticClass:"title"},[t._v("餐段")]),e("div",[e("el-button",{staticClass:"ps-bule block-btn",attrs:{type:"text"},on:{click:function(e){return t.openDialog("menu")}}},[t._v(" 编辑 ")])],1)])])]),e("el-dialog",{attrs:{title:"选择组织",visible:t.showDialog,width:"500px","custom-class":"ps-dialog"},on:{"update:visible":function(e){t.showDialog=e}}},[e("el-form",{ref:"dialogFormRef",staticClass:"dialog-form",attrs:{model:t.dialogForm,"label-width":"120px",rules:t.dialogFormRules}},[e("el-form-item",{attrs:{label:"显示组织：",prop:"orgId"}},[e("organization-select",{staticClass:"search-item-w ps-input w-250",attrs:{placeholder:"请选择所属组织",isLazy:!1,multiple:!1,"check-strictly":!0,"append-to-body":!0},on:{change:t.changeOrg},model:{value:t.dialogForm.orgId,callback:function(e){t.$set(t.dialogForm,"orgId",e)},expression:"dialogForm.orgId"}})],1),e("el-form-item",{attrs:{label:"菜谱："}},[e("el-radio-group",{staticClass:"ps-radio",on:{change:t.changeMenuType},model:{value:t.dialogForm.menuType,callback:function(e){t.$set(t.dialogForm,"menuType",e)},expression:"dialogForm.menuType"}},[e("el-radio",{attrs:{label:"week"}},[t._v("周菜谱")]),e("el-radio",{attrs:{label:"month"}},[t._v("月菜谱")])],1)],1),e("el-form-item",{attrs:{label:"设备类型：",prop:"deviceType"}},[e("el-select",{staticClass:"ps-select w-250",attrs:{multiple:"",placeholder:"请选择设备类型","popper-class":"ps-popper-select"},on:{change:t.deviceTypeChange},model:{value:t.dialogForm.deviceType,callback:function(e){t.$set(t.dialogForm,"deviceType",e)},expression:"dialogForm.deviceType"}},t._l(t.deviceList,(function(t){return e("el-option",{key:t.key,attrs:{label:t.name,value:t.key}})})),1)],1),e("el-form-item",{attrs:{label:"设备型号：",prop:t.isDisabledModel?"":"deviceModel"}},[e("el-select",{staticClass:"ps-select w-250",attrs:{multiple:"",placeholder:"请选择设备型号","popper-class":"ps-popper-select",disabled:t.isDisabledModel},on:{change:t.deviceModelChange},model:{value:t.dialogForm.deviceModel,callback:function(e){t.$set(t.dialogForm,"deviceModel",e)},expression:"dialogForm.deviceModel"}},t._l(t.deviceModelList,(function(t){return e("el-option",{key:t.key,attrs:{label:t.key,value:t.key}})})),1)],1),e("el-form-item",{attrs:{label:"菜谱：",prop:"menuId"}},[e("el-select",{staticClass:"ps-select w-250",model:{value:t.dialogForm.menuId,callback:function(e){t.$set(t.dialogForm,"menuId",e)},expression:"dialogForm.menuId"}},t._l(t.menuList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"ps-cancel-btn",on:{click:function(e){t.showDialog=!1}}},[t._v("取 消")]),e("el-button",{staticClass:"ps-btn",attrs:{type:"primary"},on:{click:t.confirmDialog}},[t._v("确 定")])],1)],1)],1)},i=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"header"},[e("div",{staticClass:"title"},[t._v("查询大屏")]),e("div",{staticClass:"time"},[t._v("2024年01月08日 09:55:11 星期一")])])}],n=r("cbfb");function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}function s(t){return d(t)||u(t)||c(t)||l()}function l(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function c(t,e){if(t){if("string"==typeof t)return f(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?f(t,e):void 0}}function u(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function d(t){if(Array.isArray(t))return f(t)}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,o=Array(e);r<e;r++)o[r]=t[r];return o}function p(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */p=function(){return e};var t,e={},r=Object.prototype,o=r.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},n="function"==typeof Symbol?Symbol:{},s=n.iterator||"@@iterator",l=n.asyncIterator||"@@asyncIterator",c=n.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function d(t,e,r,o){var n=e&&e.prototype instanceof b?e:b,a=Object.create(n.prototype),s=new O(o||[]);return i(a,"_invoke",{value:C(t,r,s)}),a}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=d;var h="suspendedStart",m="suspendedYield",g="executing",v="completed",y={};function b(){}function w(){}function _(){}var I={};u(I,s,(function(){return this}));var L=Object.getPrototypeOf,F=L&&L(L(S([])));F&&F!==r&&o.call(F,s)&&(I=F);var k=_.prototype=b.prototype=Object.create(I);function T(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function x(t,e){function r(i,n,s,l){var c=f(t[i],t,n);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==a(d)&&o.call(d,"__await")?e.resolve(d.__await).then((function(t){r("next",t,s,l)}),(function(t){r("throw",t,s,l)})):e.resolve(d).then((function(t){u.value=t,s(u)}),(function(t){return r("throw",t,s,l)}))}l(c.arg)}var n;i(this,"_invoke",{value:function(t,o){function i(){return new e((function(e,i){r(t,o,e,i)}))}return n=n?n.then(i,i):i()}})}function C(e,r,o){var i=h;return function(n,a){if(i===g)throw Error("Generator is already running");if(i===v){if("throw"===n)throw a;return{value:t,done:!0}}for(o.method=n,o.arg=a;;){var s=o.delegate;if(s){var l=M(s,o);if(l){if(l===y)continue;return l}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(i===h)throw i=v,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);i=g;var c=f(e,r,o);if("normal"===c.type){if(i=o.done?v:m,c.arg===y)continue;return{value:c.arg,done:o.done}}"throw"===c.type&&(i=v,o.method="throw",o.arg=c.arg)}}}function M(e,r){var o=r.method,i=e.iterator[o];if(i===t)return r.delegate=null,"throw"===o&&e.iterator.return&&(r.method="return",r.arg=t,M(e,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+o+"' method")),y;var n=f(i,e.iterator,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,y;var a=n.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function D(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function O(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(D,this),this.reset(!0)}function S(e){if(e||""===e){var r=e[s];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,n=function r(){for(;++i<e.length;)if(o.call(e,i))return r.value=e[i],r.done=!1,r;return r.value=t,r.done=!0,r};return n.next=n}}throw new TypeError(a(e)+" is not iterable")}return w.prototype=_,i(k,"constructor",{value:_,configurable:!0}),i(_,"constructor",{value:w,configurable:!0}),w.displayName=u(_,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,u(t,c,"GeneratorFunction")),t.prototype=Object.create(k),t},e.awrap=function(t){return{__await:t}},T(x.prototype),u(x.prototype,l,(function(){return this})),e.AsyncIterator=x,e.async=function(t,r,o,i,n){void 0===n&&(n=Promise);var a=new x(d(t,r,o,i),n);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},T(k),u(k,c,"Generator"),u(k,s,(function(){return this})),u(k,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var o in e)r.push(o);return r.reverse(),function t(){for(;r.length;){var o=r.pop();if(o in e)return t.value=o,t.done=!1,t}return t.done=!0,t}},e.values=S,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(E),!e)for(var r in this)"t"===r.charAt(0)&&o.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function i(o,i){return s.type="throw",s.arg=e,r.next=o,i&&(r.method="next",r.arg=t),!!i}for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n],s=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var l=o.call(a,"catchLoc"),c=o.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&o.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var n=i;break}}n&&("break"===t||"continue"===t)&&n.tryLoc<=e&&e<=n.finallyLoc&&(n=null);var a=n?n.completion:{};return a.type=t,a.arg=e,n?(this.method="next",this.next=n.finallyLoc,y):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),E(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var o=r.completion;if("throw"===o.type){var i=o.arg;E(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,r,o){return this.delegate={iterator:S(e),resultName:r,nextLoc:o},"next"===this.method&&(this.arg=t),y}},e}function h(t,e,r,o,i,n,a){try{var s=t[n](a),l=s.value}catch(t){return void r(t)}s.done?e(l):Promise.resolve(l).then(o,i)}function m(t){return function(){var e=this,r=arguments;return new Promise((function(o,i){var n=t.apply(e,r);function a(t){h(n,o,i,a,s,"next",t)}function s(t){h(n,o,i,a,s,"throw",t)}a(void 0)}))}}var g={name:"management",components:{OrganizationSelect:n["a"]},props:{type:String,cameraList:Array,templateInfo:[Object,Array]},data:function(){return{dialogType:"",showDialog:!1,dialogForm:{orgId:"",menuType:"",deviceType:[],deviceModel:[],menuId:""},dialogFormRules:{orgId:[{required:!0,message:"请选择显示组织",trigger:"blur"}],deviceType:[{required:!0,message:"请选择设备类型",trigger:"blur"}],deviceModel:[{required:!0,message:"请选择设备型号",trigger:"blur"}],menuId:[{required:!0,message:"请选择菜谱",trigger:"blur"}]},deviceList:[],deviceModelList:[],isDisabledModel:!1,menuList:[],dataInfo:{menuType:"week",deviceType:"",deviceModel:"",menuId:"",orgId:""}}},created:function(){"edit"===this.type&&(this.dataInfo=this.templateInfo),this.getOrgDeviceList()},methods:{openDialog:function(t){this.dialogType=t,this.showDialog=!0,this.dialogForm.orgId=this.dataInfo.orgId,this.dialogForm.menuType=this.dataInfo.menuType,this.dialogForm.deviceType=this.dataInfo.deviceType,this.dialogForm.deviceModel=this.dataInfo.deviceModel,this.dialogForm.menuId=this.dataInfo.menuId,this.dialogForm.deviceType.length?this.getDeviceModel():this.isDisabledModel=!1,this.checkGetMemuList()},confirmDialog:function(){var t=this;this.$refs.dialogFormRef.validate((function(e){e&&(t.dataInfo.orgId=t.dialogForm.orgId,t.dataInfo.menuType=t.dialogForm.menuType,t.dataInfo.deviceType=t.dialogForm.deviceType,t.dataInfo.deviceModel=t.dialogForm.deviceModel,t.dataInfo.menuId=t.dialogForm.menuId,t.$refs.dialogFormRef.clearValidate(),t.dialogForm={orgId:"",menuType:"",deviceType:[],deviceModel:[],menuId:""},t.showDialog=!1,t.$emit("comfirm",t.dataInfo))}))},changeOrg:function(){this.dialogForm.menuId="",this.menuList=[],this.checkGetMemuList()},changeMenuType:function(){this.dialogForm.menuId="",this.menuList=[],this.checkGetMemuList()},deviceTypeChange:function(){this.dialogForm.menuId="",this.menuList=[],this.dialogForm.deviceModel=[],this.deviceModelList=[],this.dialogForm.deviceType.length?this.getDeviceModel():this.isDisabledModel=!1,this.checkGetMemuList()},deviceModelChange:function(){this.dialogForm.menuId="",this.menuList=[],this.checkGetMemuList()},checkGetMemuList:function(){this.dialogForm.deviceType.length&&this.dialogForm.orgId&&(-1!==this.dialogForm.deviceType.indexOf("H5")||-1!==this.dialogForm.deviceType.indexOf("MAPP")||this.dialogForm.deviceModel.length)&&this.getMenuList()},getMenuList:function(){var t=this;return m(p().mark((function e(){var r;return p().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.apiBackgroundDatascreenKanbanTempGetKanbanSettingPost({organization_id:[t.dialogForm.orgId],device_type:t.dialogForm.deviceType,device_model:t.dialogForm.deviceModel,menu_type:t.dialogForm.menuType});case 2:r=e.sent,0===r.code?t.menuList=r.data:t.$message.error(r.msg);case 4:case"end":return e.stop()}}),e)})))()},getOrgDeviceList:function(){var t=this;return m(p().mark((function e(){var r;return p().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.apiBackgroundAdminDeviceDeviceTypePost({source:"self"});case 2:r=e.sent,0===r.code?t.deviceList=[{name:"H5",key:"H5"},{name:"小程序",key:"MAPP"}].concat(s(r.data)):t.$message.error(r.msg);case 4:case"end":return e.stop()}}),e)})))()},getDeviceModel:function(){var t=this;return m(p().mark((function e(){var r,o;return p().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=t.dialogForm.deviceType.filter((function(t){return"H5"!==t&&"MAPP"!==t})),r.length){e.next=6;break}return t.isDisabledModel=!0,e.abrupt("return");case 6:t.isDisabledModel=!1;case 7:return e.next=9,t.$apis.apiBackgroundAdminDeviceDeviceModelPost({device_types:r});case 9:o=e.sent,0===o.code?t.deviceModelList=o.data:t.$message.error(o.msg);case 11:case"end":return e.stop()}}),e)})))()}}},v=g,y=(r("4cb6"),r("2877")),b=Object(y["a"])(v,o,i,!1,null,null,null);e["default"]=b.exports},"08b5":function(t,e,r){"use strict";r.r(e);var o=function(){var t=this,e=t._self._c;return e("div",{staticClass:"take-meal-report"},[e("div",{staticClass:"header"},[t._m(0),e("div",{staticClass:"header-title"},[t._v(" 朴食科技智慧食堂 "),e("el-button",{staticClass:"ps-bule block-btn",attrs:{type:"text",size:"small"},on:{click:t.openDialog}},[t._v("编辑")])],1),e("div",{staticClass:"header-account"})]),e("div",{staticClass:"main"},[e("div",{staticClass:"search"},[e("div",{staticClass:"search-left"},[e("div",{staticClass:"search-btn"},[e("div",{class:["search-btn-item","total"===t.tableType?"active-btn":""],on:{click:function(e){return t.changeType("total")}}},[t._v("汇总")]),e("div",{class:["search-btn-item","detail"===t.tableType?"active-btn":""],on:{click:function(e){return t.changeType("detail")}}},[t._v("明细")])]),e("div",{staticClass:"search-select"},[e("div",{staticClass:"search-select-item group"},[e("div",{staticClass:"search-select-label"},[t._v("分组")]),e("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:t.groupId,callback:function(e){t.groupId=e},expression:"groupId"}})],1),e("div",{staticClass:"search-select-item isorder"},[e("div",{staticClass:"search-select-label"},[t._v("点餐情况")]),e("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:t.mealStatus,callback:function(e){t.mealStatus=e},expression:"mealStatus"}})],1)])]),t._m(1)]),e("div",{staticClass:"progress"},[t._m(2),e("el-progress",{attrs:{"stroke-width":18,color:"#1684ff",percentage:45}})],1),"total"===t.tableType?e("div",{staticClass:"total-count"},[t._m(3),t._m(4),t._m(5),t._m(6)]):e("div",{staticClass:"table"},[e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:""}},[e("el-table-column",{attrs:{prop:"name",label:"姓名",align:"center"}}),e("el-table-column",{attrs:{prop:"person_no",label:"人员编号",align:"center"}}),e("el-table-column",{attrs:{prop:"payer_group",label:"所属分组",align:"center"}}),e("el-table-column",{attrs:{prop:"primary",label:"消费点",align:"center"}}),e("el-table-column",{attrs:{prop:"meal_status_alias",label:"点餐情况",align:"center"}})],1),e("div",{staticClass:"table-pagination"},[e("el-pagination",{attrs:{"current-page":t.currentPage,"page-size":t.pageSize,layout:"total, prev, pager, next, jumper",total:t.totalCount,background:""}})],1)],1)]),e("el-dialog",{attrs:{title:"选择组织",visible:t.showDialog,width:"500px","custom-class":"ps-dialog"},on:{"update:visible":function(e){t.showDialog=e}}},[e("el-form",{ref:"dialogFormRef",staticClass:"dialog-form",attrs:{model:t.dialogForm,"label-width":"160px"}},[e("div",{staticClass:"flex",staticStyle:{"align-items":"center"}},[e("el-form-item",{attrs:{label:"显示组织："}},[e("organization-select",{staticClass:"search-item-w ps-input w-180",attrs:{placeholder:"请选择所属组织",isLazy:!1,multiple:!1,"check-strictly":!0,"append-to-body":!0},model:{value:t.dialogForm.orgId,callback:function(e){t.$set(t.dialogForm,"orgId",e)},expression:"dialogForm.orgId"}})],1)],1)]),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"ps-cancel-btn",on:{click:function(e){t.showDialog=!1}}},[t._v("取 消")]),e("el-button",{staticClass:"ps-btn",attrs:{type:"primary"},on:{click:t.confirmDialog}},[t._v("确 定")])],1)],1)],1)},i=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"header-logo"},[e("img",{attrs:{src:r("58b6"),alt:""}})])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"search-right"},[e("div",{staticClass:"date-time"},[e("div",{staticClass:"time"},[t._v("09:00:06")]),e("div",{staticClass:"date"},[t._v("2022年12月12号 星期三")])]),e("div",{staticClass:"meal"},[e("div",{staticClass:"meal-label"},[t._v("当前餐段")]),e("div",{staticClass:"now-meal"},[e("img",{attrs:{src:r("718b"),alt:""}}),e("div",[t._v("午餐")])])])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"progress-top"},[e("div",{staticClass:"progress-label"},[t._v("刷新：45s")]),e("div",{staticClass:"progress-btn"},[e("i",{staticClass:"el-icon-refresh"}),t._v("手动刷新")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"total-count-item"},[e("img",{attrs:{src:r("306c"),alt:""}}),e("div",{staticClass:"count-content"},[e("div",{staticClass:"title"},[t._v("总人数")]),e("div",{staticClass:"count"},[t._v("0")])])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"total-count-item blue"},[e("img",{attrs:{src:r("11fb"),alt:""}}),e("div",{staticClass:"count-content"},[e("div",{staticClass:"title"},[t._v("点餐人数")]),e("div",{staticClass:"count"},[t._v("0")])])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"total-count-item"},[e("img",{attrs:{src:r("090f"),alt:""}}),e("div",{staticClass:"count-content"},[e("div",{staticClass:"title"},[t._v("点餐人数")]),e("div",{staticClass:"count"},[t._v("0")])]),e("div",{staticClass:"other-stall"},[t._v("其他档口")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"total-count-item"},[e("img",{attrs:{src:r("7578"),alt:""}}),e("div",{staticClass:"count-content"},[e("div",{staticClass:"title"},[t._v("未点餐人数")]),e("div",{staticClass:"count"},[t._v("0")])])])}],n=r("cbfb"),a={name:"TakeMealReport",components:{OrganizationSelect:n["a"]},props:{type:String,templateInfo:Object},data:function(){return{tableType:"total",groupId:"",mealStatus:"",dialogForm:{orgId:""},showDialog:!1}},methods:{changeType:function(t){this.tableType=t},openDialog:function(){this.showDialog=!0,this.dialogForm.orgId=this.templateInfo.jianceOrgId},confirmDialog:function(){this.showDialog=!1,this.templateInfo.jianceOrgId=this.dialogForm.orgId,this.$emit("comfirm",this.templateInfo)}}},s=a,l=(r("9434"),r("2877")),c=Object(l["a"])(s,o,i,!1,null,null,null);e["default"]=c.exports},"25d4":function(t,e,r){"use strict";r("b4ab")},3368:function(t,e,r){"use strict";r("6c3c")},"4c90":function(t,e,r){"use strict";r.r(e);var o=function(){var t=this,e=t._self._c;return e("div",{staticClass:"smartCanteenNutrition"},[t._m(0),e("div",{staticClass:"chart"},[e("div",{staticClass:"center"},[e("div",{staticClass:"block h-770"},[e("div",{staticClass:"title"},[t._v("餐段")]),e("div",[e("el-button",{staticClass:"ps-bule block-btn",attrs:{type:"text"},on:{click:function(e){return t.openDialog("menu")}}},[t._v(" 编辑 ")])],1)])])]),e("el-dialog",{attrs:{title:"选择组织",visible:t.showDialog,width:"500px","custom-class":"ps-dialog"},on:{"update:visible":function(e){t.showDialog=e}}},[e("el-form",{ref:"dialogFormRef",staticClass:"dialog-form",attrs:{model:t.dialogForm,"label-width":"120px",rules:t.dialogFormRules}},[e("el-form-item",{attrs:{label:"显示组织：",prop:"orgId"}},[e("organization-select",{staticClass:"search-item-w ps-input w-250",attrs:{placeholder:"请选择所属组织",isLazy:!1,multiple:!1,"check-strictly":!0,"append-to-body":!0},on:{change:t.changeOrg},model:{value:t.dialogForm.orgId,callback:function(e){t.$set(t.dialogForm,"orgId",e)},expression:"dialogForm.orgId"}})],1),e("el-form-item",{attrs:{label:"菜谱："}},[e("el-radio-group",{staticClass:"ps-radio",on:{change:t.changeMenuType},model:{value:t.dialogForm.menuType,callback:function(e){t.$set(t.dialogForm,"menuType",e)},expression:"dialogForm.menuType"}},[e("el-radio",{attrs:{label:"week"}},[t._v("周菜谱")]),e("el-radio",{attrs:{label:"month"}},[t._v("月菜谱")])],1)],1),e("el-form-item",{attrs:{label:"设备类型：",prop:"deviceType"}},[e("el-select",{staticClass:"ps-select w-250",attrs:{multiple:"",placeholder:"请选择设备类型","popper-class":"ps-popper-select"},on:{change:t.deviceTypeChange},model:{value:t.dialogForm.deviceType,callback:function(e){t.$set(t.dialogForm,"deviceType",e)},expression:"dialogForm.deviceType"}},t._l(t.deviceList,(function(t){return e("el-option",{key:t.key,attrs:{label:t.name,value:t.key}})})),1)],1),e("el-form-item",{attrs:{label:"设备型号：",prop:t.isDisabledModel?"":"deviceModel"}},[e("el-select",{staticClass:"ps-select w-250",attrs:{multiple:"",placeholder:"请选择设备型号","popper-class":"ps-popper-select",disabled:t.isDisabledModel},on:{change:t.deviceModelChange},model:{value:t.dialogForm.deviceModel,callback:function(e){t.$set(t.dialogForm,"deviceModel",e)},expression:"dialogForm.deviceModel"}},t._l(t.deviceModelList,(function(t){return e("el-option",{key:t.key,attrs:{label:t.key,value:t.key}})})),1)],1),e("el-form-item",{attrs:{label:"菜谱：",prop:"menuId"}},[e("el-select",{staticClass:"ps-select w-250",attrs:{multiple:""},model:{value:t.dialogForm.menuId,callback:function(e){t.$set(t.dialogForm,"menuId",e)},expression:"dialogForm.menuId"}},t._l(t.menuList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"ps-cancel-btn",on:{click:function(e){t.showDialog=!1}}},[t._v("取 消")]),e("el-button",{staticClass:"ps-btn",attrs:{type:"primary"},on:{click:t.confirmDialog}},[t._v("确 定")])],1)],1)],1)},i=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"header"},[e("div",{staticClass:"title"},[t._v("智慧食堂营养屏")]),e("div",{staticClass:"time"},[t._v("2024年01月08日 09:55:11 星期一")])])}],n=r("cbfb");function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}function s(t){return d(t)||u(t)||c(t)||l()}function l(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function c(t,e){if(t){if("string"==typeof t)return f(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?f(t,e):void 0}}function u(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function d(t){if(Array.isArray(t))return f(t)}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,o=Array(e);r<e;r++)o[r]=t[r];return o}function p(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */p=function(){return e};var t,e={},r=Object.prototype,o=r.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},n="function"==typeof Symbol?Symbol:{},s=n.iterator||"@@iterator",l=n.asyncIterator||"@@asyncIterator",c=n.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function d(t,e,r,o){var n=e&&e.prototype instanceof b?e:b,a=Object.create(n.prototype),s=new O(o||[]);return i(a,"_invoke",{value:C(t,r,s)}),a}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=d;var h="suspendedStart",m="suspendedYield",g="executing",v="completed",y={};function b(){}function w(){}function _(){}var I={};u(I,s,(function(){return this}));var L=Object.getPrototypeOf,F=L&&L(L(S([])));F&&F!==r&&o.call(F,s)&&(I=F);var k=_.prototype=b.prototype=Object.create(I);function T(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function x(t,e){function r(i,n,s,l){var c=f(t[i],t,n);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==a(d)&&o.call(d,"__await")?e.resolve(d.__await).then((function(t){r("next",t,s,l)}),(function(t){r("throw",t,s,l)})):e.resolve(d).then((function(t){u.value=t,s(u)}),(function(t){return r("throw",t,s,l)}))}l(c.arg)}var n;i(this,"_invoke",{value:function(t,o){function i(){return new e((function(e,i){r(t,o,e,i)}))}return n=n?n.then(i,i):i()}})}function C(e,r,o){var i=h;return function(n,a){if(i===g)throw Error("Generator is already running");if(i===v){if("throw"===n)throw a;return{value:t,done:!0}}for(o.method=n,o.arg=a;;){var s=o.delegate;if(s){var l=M(s,o);if(l){if(l===y)continue;return l}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(i===h)throw i=v,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);i=g;var c=f(e,r,o);if("normal"===c.type){if(i=o.done?v:m,c.arg===y)continue;return{value:c.arg,done:o.done}}"throw"===c.type&&(i=v,o.method="throw",o.arg=c.arg)}}}function M(e,r){var o=r.method,i=e.iterator[o];if(i===t)return r.delegate=null,"throw"===o&&e.iterator.return&&(r.method="return",r.arg=t,M(e,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+o+"' method")),y;var n=f(i,e.iterator,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,y;var a=n.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function D(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function O(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(D,this),this.reset(!0)}function S(e){if(e||""===e){var r=e[s];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,n=function r(){for(;++i<e.length;)if(o.call(e,i))return r.value=e[i],r.done=!1,r;return r.value=t,r.done=!0,r};return n.next=n}}throw new TypeError(a(e)+" is not iterable")}return w.prototype=_,i(k,"constructor",{value:_,configurable:!0}),i(_,"constructor",{value:w,configurable:!0}),w.displayName=u(_,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,u(t,c,"GeneratorFunction")),t.prototype=Object.create(k),t},e.awrap=function(t){return{__await:t}},T(x.prototype),u(x.prototype,l,(function(){return this})),e.AsyncIterator=x,e.async=function(t,r,o,i,n){void 0===n&&(n=Promise);var a=new x(d(t,r,o,i),n);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},T(k),u(k,c,"Generator"),u(k,s,(function(){return this})),u(k,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var o in e)r.push(o);return r.reverse(),function t(){for(;r.length;){var o=r.pop();if(o in e)return t.value=o,t.done=!1,t}return t.done=!0,t}},e.values=S,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(E),!e)for(var r in this)"t"===r.charAt(0)&&o.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function i(o,i){return s.type="throw",s.arg=e,r.next=o,i&&(r.method="next",r.arg=t),!!i}for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n],s=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var l=o.call(a,"catchLoc"),c=o.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&o.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var n=i;break}}n&&("break"===t||"continue"===t)&&n.tryLoc<=e&&e<=n.finallyLoc&&(n=null);var a=n?n.completion:{};return a.type=t,a.arg=e,n?(this.method="next",this.next=n.finallyLoc,y):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),E(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var o=r.completion;if("throw"===o.type){var i=o.arg;E(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,r,o){return this.delegate={iterator:S(e),resultName:r,nextLoc:o},"next"===this.method&&(this.arg=t),y}},e}function h(t,e,r,o,i,n,a){try{var s=t[n](a),l=s.value}catch(t){return void r(t)}s.done?e(l):Promise.resolve(l).then(o,i)}function m(t){return function(){var e=this,r=arguments;return new Promise((function(o,i){var n=t.apply(e,r);function a(t){h(n,o,i,a,s,"next",t)}function s(t){h(n,o,i,a,s,"throw",t)}a(void 0)}))}}var g={name:"management",components:{OrganizationSelect:n["a"]},props:{type:String,cameraList:Array,templateInfo:[Object,Array]},data:function(){return{dialogType:"",showDialog:!1,dialogForm:{orgId:"",menuType:"",deviceType:[],deviceModel:[],menuId:[]},dialogFormRules:{orgId:[{required:!0,message:"请选择显示组织",trigger:"blur"}],deviceType:[{required:!0,message:"请选择设备类型",trigger:"blur"}],deviceModel:[{required:!0,message:"请选择设备型号",trigger:"blur"}],menuId:[{required:!0,message:"请选择菜谱",trigger:"blur"}]},deviceList:[],deviceModelList:[],isDisabledModel:!1,menuList:[],dataInfo:{menuType:"week",deviceType:"",deviceModel:"",menuId:[],orgId:""}}},created:function(){"edit"===this.type&&(this.dataInfo=this.templateInfo),this.getOrgDeviceList()},methods:{openDialog:function(t){this.dialogType=t,this.showDialog=!0,this.dialogForm.orgId=this.dataInfo.orgId,this.dialogForm.menuType=this.dataInfo.menuType,this.dialogForm.deviceType=this.dataInfo.deviceType,this.dialogForm.deviceModel=this.dataInfo.deviceModel,this.dialogForm.menuId=this.dataInfo.menuId,this.dialogForm.deviceType.length?this.getDeviceModel():this.isDisabledModel=!1,this.checkGetMemuList()},confirmDialog:function(){var t=this;this.$refs.dialogFormRef.validate((function(e){e&&(t.dataInfo.orgId=t.dialogForm.orgId,t.dataInfo.menuType=t.dialogForm.menuType,t.dataInfo.deviceType=t.dialogForm.deviceType,t.dataInfo.deviceModel=t.dialogForm.deviceModel,t.dataInfo.menuId=t.dialogForm.menuId,t.$refs.dialogFormRef.clearValidate(),t.dialogForm={orgId:"",menuType:"",deviceType:[],deviceModel:[],menuId:[]},t.showDialog=!1,t.$emit("comfirm",t.dataInfo))}))},changeOrg:function(){this.dialogForm.menuId=[],this.menuList=[],this.checkGetMemuList()},changeMenuType:function(){this.dialogForm.menuId=[],this.menuList=[],this.checkGetMemuList()},deviceTypeChange:function(){this.dialogForm.menuId=[],this.menuList=[],this.dialogForm.deviceModel=[],this.deviceModelList=[],this.dialogForm.deviceType.length?this.getDeviceModel():this.isDisabledModel=!1,this.checkGetMemuList()},deviceModelChange:function(){this.dialogForm.menuId=[],this.menuList=[],this.checkGetMemuList()},checkGetMemuList:function(){this.dialogForm.deviceType.length&&this.dialogForm.orgId&&(-1!==this.dialogForm.deviceType.indexOf("H5")||-1!==this.dialogForm.deviceType.indexOf("MAPP")||this.dialogForm.deviceModel.length)&&this.getMenuList()},getMenuList:function(){var t=this;return m(p().mark((function e(){var r;return p().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.apiBackgroundDatascreenKanbanTempGetKanbanSettingPost({organization_id:[t.dialogForm.orgId],device_type:t.dialogForm.deviceType,device_model:t.dialogForm.deviceModel,menu_type:t.dialogForm.menuType});case 2:r=e.sent,0===r.code?t.menuList=r.data:t.$message.error(r.msg);case 4:case"end":return e.stop()}}),e)})))()},getOrgDeviceList:function(){var t=this;return m(p().mark((function e(){var r;return p().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.apiBackgroundDeviceDeviceDeviceTypePost({source:"self"});case 2:r=e.sent,0===r.code?t.deviceList=[{name:"H5",key:"H5"},{name:"小程序",key:"MAPP"}].concat(s(r.data)):t.$message.error(r.msg);case 4:case"end":return e.stop()}}),e)})))()},getDeviceModel:function(){var t=this;return m(p().mark((function e(){var r,o;return p().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=t.dialogForm.deviceType.filter((function(t){return"H5"!==t&&"MAPP"!==t})),r.length){e.next=6;break}return t.isDisabledModel=!0,e.abrupt("return");case 6:t.isDisabledModel=!1;case 7:return e.next=9,t.$apis.apiBackgroundDeviceDeviceDeviceModelPost({device_types:r});case 9:o=e.sent,0===o.code?t.deviceModelList=o.data:t.$message.error(o.msg);case 11:case"end":return e.stop()}}),e)})))()}}},v=g,y=(r("eaed"),r("2877")),b=Object(y["a"])(v,o,i,!1,null,null,null);e["default"]=b.exports},"4cb6":function(t,e,r){"use strict";r("e9cfe")},5382:function(t,e,r){"use strict";r.r(e);var o=function(){var t=this,e=t._self._c;return e("div",{staticClass:"container-wrapper"},[e("refresh-tool",{on:{refreshPage:t.refreshHandle}}),e("div",{staticClass:"search"},[e("el-select",{attrs:{placeholder:"选择模版"},model:{value:t.templateType,callback:function(e){t.templateType=e},expression:"templateType"}},[e("el-option",{attrs:{value:"经营数据大屏",label:"经营数据大屏"}}),e("el-option",{attrs:{value:"监测屏",label:"监测屏"}}),e("el-option",{attrs:{value:"菜品余量大屏",label:"菜品余量大屏"}}),e("el-option",{attrs:{value:"叫号屏",label:"叫号屏"}}),e("el-option",{attrs:{value:"智慧食堂营养屏",label:"智慧食堂营养屏"}}),e("el-option",{attrs:{value:"部队食堂大数据运行平台",label:"部队食堂大数据运行平台"}}),e("el-option",{attrs:{value:"查询大屏",label:"查询大屏"}})],1),e("div",{staticClass:"align-r"},[e("button-icon",{attrs:{color:"origin"},on:{click:function(e){return t.saveSetting()}}},[t._v("保存")])],1)],1),"经营数据大屏"===t.templateType?e("div",[e("management",{attrs:{type:t.type,"camera-list":t.cameraList,"template-info":t.templateInfo},on:{comfirm:t.comfirmTemplate}})],1):t._e(),"监测屏"===t.templateType?e("div",[e("take-meal-report",{attrs:{type:t.type,"template-info":t.templateInfo},on:{comfirm:t.comfirmTemplate}})],1):t._e(),"菜品余量大屏"===t.templateType?e("div",[e("food-stock",{attrs:{type:t.type,"template-info":t.templateInfo},on:{comfirm:t.comfirmTemplate}})],1):t._e(),"叫号屏"===t.templateType?e("div",[e("meal-queue",{attrs:{type:t.type,"template-info":t.templateInfo},on:{comfirm:t.comfirmTemplate}})],1):t._e(),"智慧食堂营养屏"===t.templateType?e("div",[e("smart-canteen-nutrition",{attrs:{type:t.type,"template-info":t.templateInfo},on:{comfirm:t.comfirmTemplate}})],1):t._e(),"部队食堂大数据运行平台"===t.templateType?e("div",[e("budui-canteen",{attrs:{type:t.type,"template-info":t.templateInfo},on:{comfirm:t.comfirmTemplate}})],1):t._e(),"查询大屏"===t.templateType?e("div",[e("query-dashboard",{attrs:{type:t.type,"template-info":t.templateInfo},on:{comfirm:t.comfirmTemplate}})],1):t._e(),e("el-dialog",{attrs:{title:"选择组织",visible:t.showDialog,width:"500px","custom-class":"ps-dialog"},on:{"update:visible":function(e){t.showDialog=e}}},[e("el-form",{ref:"dialogFormRef",staticClass:"dialog-form",attrs:{model:t.dialogForm,"label-width":"160px"}}),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"ps-cancel-btn",on:{click:function(e){t.showDialog=!1}}},[t._v("取 消")]),e("el-button",{staticClass:"ps-btn",attrs:{type:"primary"},on:{click:t.confirmDialog}},[t._v("确 定")])],1)],1)],1)},i=[],n=r("08b5"),a=r("2a59"),s=r("66c2"),l=r("d2a9"),c=r("4c90"),u=r("b42f"),d=r("00bf");function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function p(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */p=function(){return e};var t,e={},r=Object.prototype,o=r.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},n="function"==typeof Symbol?Symbol:{},a=n.iterator||"@@iterator",s=n.asyncIterator||"@@asyncIterator",l=n.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,r){return t[e]=r}}function u(t,e,r,o){var n=e&&e.prototype instanceof b?e:b,a=Object.create(n.prototype),s=new O(o||[]);return i(a,"_invoke",{value:C(t,r,s)}),a}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=u;var h="suspendedStart",m="suspendedYield",g="executing",v="completed",y={};function b(){}function w(){}function _(){}var I={};c(I,a,(function(){return this}));var L=Object.getPrototypeOf,F=L&&L(L(S([])));F&&F!==r&&o.call(F,a)&&(I=F);var k=_.prototype=b.prototype=Object.create(I);function T(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function x(t,e){function r(i,n,a,s){var l=d(t[i],t,n);if("throw"!==l.type){var c=l.arg,u=c.value;return u&&"object"==f(u)&&o.call(u,"__await")?e.resolve(u.__await).then((function(t){r("next",t,a,s)}),(function(t){r("throw",t,a,s)})):e.resolve(u).then((function(t){c.value=t,a(c)}),(function(t){return r("throw",t,a,s)}))}s(l.arg)}var n;i(this,"_invoke",{value:function(t,o){function i(){return new e((function(e,i){r(t,o,e,i)}))}return n=n?n.then(i,i):i()}})}function C(e,r,o){var i=h;return function(n,a){if(i===g)throw Error("Generator is already running");if(i===v){if("throw"===n)throw a;return{value:t,done:!0}}for(o.method=n,o.arg=a;;){var s=o.delegate;if(s){var l=M(s,o);if(l){if(l===y)continue;return l}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(i===h)throw i=v,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);i=g;var c=d(e,r,o);if("normal"===c.type){if(i=o.done?v:m,c.arg===y)continue;return{value:c.arg,done:o.done}}"throw"===c.type&&(i=v,o.method="throw",o.arg=c.arg)}}}function M(e,r){var o=r.method,i=e.iterator[o];if(i===t)return r.delegate=null,"throw"===o&&e.iterator.return&&(r.method="return",r.arg=t,M(e,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+o+"' method")),y;var n=d(i,e.iterator,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,y;var a=n.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function D(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function O(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(D,this),this.reset(!0)}function S(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,n=function r(){for(;++i<e.length;)if(o.call(e,i))return r.value=e[i],r.done=!1,r;return r.value=t,r.done=!0,r};return n.next=n}}throw new TypeError(f(e)+" is not iterable")}return w.prototype=_,i(k,"constructor",{value:_,configurable:!0}),i(_,"constructor",{value:w,configurable:!0}),w.displayName=c(_,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,c(t,l,"GeneratorFunction")),t.prototype=Object.create(k),t},e.awrap=function(t){return{__await:t}},T(x.prototype),c(x.prototype,s,(function(){return this})),e.AsyncIterator=x,e.async=function(t,r,o,i,n){void 0===n&&(n=Promise);var a=new x(u(t,r,o,i),n);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},T(k),c(k,l,"Generator"),c(k,a,(function(){return this})),c(k,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var o in e)r.push(o);return r.reverse(),function t(){for(;r.length;){var o=r.pop();if(o in e)return t.value=o,t.done=!1,t}return t.done=!0,t}},e.values=S,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(E),!e)for(var r in this)"t"===r.charAt(0)&&o.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function i(o,i){return s.type="throw",s.arg=e,r.next=o,i&&(r.method="next",r.arg=t),!!i}for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n],s=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var l=o.call(a,"catchLoc"),c=o.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&o.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var n=i;break}}n&&("break"===t||"continue"===t)&&n.tryLoc<=e&&e<=n.finallyLoc&&(n=null);var a=n?n.completion:{};return a.type=t,a.arg=e,n?(this.method="next",this.next=n.finallyLoc,y):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),E(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var o=r.completion;if("throw"===o.type){var i=o.arg;E(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,r,o){return this.delegate={iterator:S(e),resultName:r,nextLoc:o},"next"===this.method&&(this.arg=t),y}},e}function h(t,e,r,o,i,n,a){try{var s=t[n](a),l=s.value}catch(t){return void r(t)}s.done?e(l):Promise.resolve(l).then(o,i)}function m(t){return function(){var e=this,r=arguments;return new Promise((function(o,i){var n=t.apply(e,r);function a(t){h(n,o,i,a,s,"next",t)}function s(t){h(n,o,i,a,s,"throw",t)}a(void 0)}))}}var g={name:"AddDataBoard",components:{TakeMealReport:n["default"],management:a["default"],FoodStock:s["default"],MealQueue:l["default"],SmartCanteenNutrition:c["default"],BuduiCanteen:u["default"],QueryDashboard:d["default"]},data:function(){return{organizationId:this.$store.getters.organization,type:"",templateType:"",boardId:"",mealTypeList:[],cameraList:[],showDialog:!1,dialogForm:{birthdayShowMeal:"",birthdayShowTime:""},templateInfo:{}}},created:function(){this.initLoad()},methods:{initLoad:function(){var t=this;return m(p().mark((function e(){return p().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$route.query.id&&(t.boardId=t.$route.query.id,t.getBoardInfo()),t.$route.params.type&&(t.type=t.$route.params.type),t.getOrgMeal(),t.getCameraList();case 4:case"end":return e.stop()}}),e)})))()},refreshHandle:function(){this.$refs.searchRef.resetForm(),this.currentPage=1,this.tableData=[],this.initLoad()},getBoardInfo:function(){var t=this;return m(p().mark((function e(){var r;return p().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.$apis.apiBackgroundDatascreenKanbanTempListPost({id:t.boardId,organization_id:t.organizationId});case 3:r=e.sent,t.isLoading=!1,0===r.code?(t.templateType=r.data.data[0].temp_name,t.templateInfo=JSON.parse(r.data.data[0].temp_info),t.dialogForm.birthdayShowMeal=JSON.parse(r.data.data[0].config).birthdayShowMeal,t.dialogForm.birthdayShowTime=JSON.parse(r.data.data[0].config).birthdayShowTime):t.$message.error(r.msg);case 6:case"end":return e.stop()}}),e)})))()},openDialog:function(t){this.dialogType=t,this.showDialog=!0},confirmDialog:function(){this.showDialog=!1},comfirmTemplate:function(t){this.templateInfo=t},saveSetting:function(){var t=this;return m(p().mark((function e(){var r,o;return p().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("叫号屏"!==t.templateType||t.templateInfo.orgId){e.next=2;break}return e.abrupt("return",t.$message.error("请选择消费点"));case 2:if("智慧食堂营养屏"!==t.templateType||t.templateInfo&&Object.keys(t.templateInfo).length){e.next=4;break}return e.abrupt("return",t.$message.error("请编辑营养食堂营养屏数据"));case 4:return t.isLoading=!0,r={organization_id:t.organizationId,temp_type:"custom_temp",temp_name:t.templateType,temp_info:JSON.stringify(t.templateInfo),config:JSON.stringify({birthdayShowMeal:t.dialogForm.birthdayShowMeal,birthdayShowTime:t.dialogForm.birthdayShowTime})},"edit"===t.type&&(r.id=t.boardId),e.next=9,t.$apis.apiBackgroundDatascreenKanbanTempAddOrModifyPost(r);case 9:o=e.sent,t.isLoading=!1,0===o.code?(t.$message.success("成功！"),t.$closeCurrentTab(t.$route.path)):t.$message.error(o.msg);case 12:case"end":return e.stop()}}),e)})))()},getOrgMeal:function(){var t=this;return m(p().mark((function e(){var r,o,i,n;return p().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r={id:t.organizationId},e.next=3,t.$apis.apiBackgroundOrganizationOrganizationGetCommonSettingsPost(r);case 3:o=e.sent,0===o.code?(i=[{key:"breakfast",name:"早餐"},{key:"lunch",name:"午餐"},{key:"afternoon",name:"下午茶"},{key:"dinner",name:"晚餐"},{key:"supper",name:"宵夜"},{key:"morning",name:"凌晨餐"}],n=o.data.meal_time_settings,i.map((function(e,r){n["enable_"+e.key]&&t.mealTypeList.push(e)}))):t.$message.error(o.msg);case 5:case"end":return e.stop()}}),e)})))()},getCameraList:function(){var t=this;return m(p().mark((function e(){var r;return p().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.$apis.apiBackgroundDeviceCameraInfoListPost({organization_id:t.organizationId,page:1,page_size:9999});case 3:r=e.sent,t.isLoading=!1,0===r.code?t.cameraList=r.data.results:t.$message.error(r.msg);case 6:case"end":return e.stop()}}),e)})))()}}},v=g,y=(r("25d4"),r("2877")),b=Object(y["a"])(v,o,i,!1,null,"814f5ce4",null);e["default"]=b.exports},"6c3c":function(t,e,r){},7488:function(t,e,r){},9434:function(t,e,r){"use strict";r("7488")},ab15:function(t,e,r){},b42f:function(t,e,r){"use strict";r.r(e);var o=function(){var t=this,e=t._self._c;return e("div",{staticClass:"BuduiCanteen"},[t._m(0),e("div",{staticClass:"chart"},[e("div",{staticClass:"center"},[e("div",{staticClass:"block h-770"},[e("div",{staticClass:"title"},[t._v("餐段")]),e("div",[e("el-button",{staticClass:"ps-bule block-btn",attrs:{type:"text"},on:{click:function(e){return t.openDialog("menu")}}},[t._v(" 编辑 ")])],1)])])]),e("el-dialog",{attrs:{title:"编辑",visible:t.showDialog,width:"500px","custom-class":"ps-dialog"},on:{"update:visible":function(e){t.showDialog=e}}},[e("el-form",{ref:"dialogFormRef",staticClass:"dialog-form",attrs:{model:t.dialogForm,"label-width":"120px",rules:t.dialogFormRules}},[e("el-form-item",{attrs:{label:"菜品评价显示："}},[e("el-radio-group",{staticClass:"ps-radio",model:{value:t.dialogForm.foodEvaluate,callback:function(e){t.$set(t.dialogForm,"foodEvaluate",e)},expression:"dialogForm.foodEvaluate"}},[e("el-radio",{attrs:{label:"date"}},[t._v("按日期")]),e("el-radio",{attrs:{label:"evaluate"}},[t._v("按好评")])],1)],1),"date"===t.dialogForm.foodEvaluate?e("el-form-item",{attrs:{label:"",prop:"dateValue"}},[e("el-date-picker",{attrs:{type:"daterange","value-format":"yyyy-MM-dd","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.dialogForm.dateValue,callback:function(e){t.$set(t.dialogForm,"dateValue",e)},expression:"dialogForm.dateValue"}})],1):t._e(),e("el-form-item",{attrs:{label:"显示组织：",prop:"orgId"}},[e("organization-select",{staticClass:"search-item-w ps-input w-250",attrs:{placeholder:"请选择所属组织",isLazy:!1,multiple:!1,"check-strictly":!0,"append-to-body":!0},on:{change:t.changeOrg},model:{value:t.dialogForm.orgId,callback:function(e){t.$set(t.dialogForm,"orgId",e)},expression:"dialogForm.orgId"}})],1),e("el-form-item",{attrs:{label:"菜谱："}},[e("el-radio-group",{staticClass:"ps-radio",on:{change:t.changeMenuType},model:{value:t.dialogForm.menuType,callback:function(e){t.$set(t.dialogForm,"menuType",e)},expression:"dialogForm.menuType"}},[e("el-radio",{attrs:{label:"week"}},[t._v("周菜谱")]),e("el-radio",{attrs:{label:"month"}},[t._v("月菜谱")])],1)],1),e("el-form-item",{attrs:{label:"设备类型：",prop:"deviceType"}},[e("el-select",{staticClass:"ps-select w-250",attrs:{multiple:"",placeholder:"请选择设备类型","popper-class":"ps-popper-select"},on:{change:t.deviceTypeChange},model:{value:t.dialogForm.deviceType,callback:function(e){t.$set(t.dialogForm,"deviceType",e)},expression:"dialogForm.deviceType"}},t._l(t.deviceList,(function(t){return e("el-option",{key:t.key,attrs:{label:t.name,value:t.key}})})),1)],1),e("el-form-item",{attrs:{label:"设备型号：",prop:t.isDisabledModel?"":"deviceModel"}},[e("el-select",{staticClass:"ps-select w-250",attrs:{multiple:"",placeholder:"请选择设备型号","popper-class":"ps-popper-select",disabled:t.isDisabledModel},on:{change:t.deviceModelChange},model:{value:t.dialogForm.deviceModel,callback:function(e){t.$set(t.dialogForm,"deviceModel",e)},expression:"dialogForm.deviceModel"}},t._l(t.deviceModelList,(function(t){return e("el-option",{key:t.key,attrs:{label:t.key,value:t.key}})})),1)],1),e("el-form-item",{attrs:{label:"菜谱：",prop:"menuId"}},[e("el-select",{staticClass:"ps-select w-250",model:{value:t.dialogForm.menuId,callback:function(e){t.$set(t.dialogForm,"menuId",e)},expression:"dialogForm.menuId"}},t._l(t.menuList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"ps-cancel-btn",on:{click:function(e){t.showDialog=!1}}},[t._v("取 消")]),e("el-button",{staticClass:"ps-btn",attrs:{type:"primary"},on:{click:t.confirmDialog}},[t._v("确 定")])],1)],1)],1)},i=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"header"},[e("div",{staticClass:"title"},[t._v("智慧食堂营养屏")]),e("div",{staticClass:"time"},[t._v("2024年01月08日 09:55:11 星期一")])])}],n=r("cbfb");function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}function s(t){return d(t)||u(t)||c(t)||l()}function l(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function c(t,e){if(t){if("string"==typeof t)return f(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?f(t,e):void 0}}function u(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function d(t){if(Array.isArray(t))return f(t)}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,o=Array(e);r<e;r++)o[r]=t[r];return o}function p(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */p=function(){return e};var t,e={},r=Object.prototype,o=r.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},n="function"==typeof Symbol?Symbol:{},s=n.iterator||"@@iterator",l=n.asyncIterator||"@@asyncIterator",c=n.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function d(t,e,r,o){var n=e&&e.prototype instanceof b?e:b,a=Object.create(n.prototype),s=new O(o||[]);return i(a,"_invoke",{value:C(t,r,s)}),a}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=d;var h="suspendedStart",m="suspendedYield",g="executing",v="completed",y={};function b(){}function w(){}function _(){}var I={};u(I,s,(function(){return this}));var L=Object.getPrototypeOf,F=L&&L(L(S([])));F&&F!==r&&o.call(F,s)&&(I=F);var k=_.prototype=b.prototype=Object.create(I);function T(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function x(t,e){function r(i,n,s,l){var c=f(t[i],t,n);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==a(d)&&o.call(d,"__await")?e.resolve(d.__await).then((function(t){r("next",t,s,l)}),(function(t){r("throw",t,s,l)})):e.resolve(d).then((function(t){u.value=t,s(u)}),(function(t){return r("throw",t,s,l)}))}l(c.arg)}var n;i(this,"_invoke",{value:function(t,o){function i(){return new e((function(e,i){r(t,o,e,i)}))}return n=n?n.then(i,i):i()}})}function C(e,r,o){var i=h;return function(n,a){if(i===g)throw Error("Generator is already running");if(i===v){if("throw"===n)throw a;return{value:t,done:!0}}for(o.method=n,o.arg=a;;){var s=o.delegate;if(s){var l=M(s,o);if(l){if(l===y)continue;return l}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(i===h)throw i=v,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);i=g;var c=f(e,r,o);if("normal"===c.type){if(i=o.done?v:m,c.arg===y)continue;return{value:c.arg,done:o.done}}"throw"===c.type&&(i=v,o.method="throw",o.arg=c.arg)}}}function M(e,r){var o=r.method,i=e.iterator[o];if(i===t)return r.delegate=null,"throw"===o&&e.iterator.return&&(r.method="return",r.arg=t,M(e,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+o+"' method")),y;var n=f(i,e.iterator,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,y;var a=n.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function D(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function O(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(D,this),this.reset(!0)}function S(e){if(e||""===e){var r=e[s];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,n=function r(){for(;++i<e.length;)if(o.call(e,i))return r.value=e[i],r.done=!1,r;return r.value=t,r.done=!0,r};return n.next=n}}throw new TypeError(a(e)+" is not iterable")}return w.prototype=_,i(k,"constructor",{value:_,configurable:!0}),i(_,"constructor",{value:w,configurable:!0}),w.displayName=u(_,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,u(t,c,"GeneratorFunction")),t.prototype=Object.create(k),t},e.awrap=function(t){return{__await:t}},T(x.prototype),u(x.prototype,l,(function(){return this})),e.AsyncIterator=x,e.async=function(t,r,o,i,n){void 0===n&&(n=Promise);var a=new x(d(t,r,o,i),n);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},T(k),u(k,c,"Generator"),u(k,s,(function(){return this})),u(k,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var o in e)r.push(o);return r.reverse(),function t(){for(;r.length;){var o=r.pop();if(o in e)return t.value=o,t.done=!1,t}return t.done=!0,t}},e.values=S,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(E),!e)for(var r in this)"t"===r.charAt(0)&&o.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function i(o,i){return s.type="throw",s.arg=e,r.next=o,i&&(r.method="next",r.arg=t),!!i}for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n],s=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var l=o.call(a,"catchLoc"),c=o.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&o.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var n=i;break}}n&&("break"===t||"continue"===t)&&n.tryLoc<=e&&e<=n.finallyLoc&&(n=null);var a=n?n.completion:{};return a.type=t,a.arg=e,n?(this.method="next",this.next=n.finallyLoc,y):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),E(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var o=r.completion;if("throw"===o.type){var i=o.arg;E(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,r,o){return this.delegate={iterator:S(e),resultName:r,nextLoc:o},"next"===this.method&&(this.arg=t),y}},e}function h(t,e,r,o,i,n,a){try{var s=t[n](a),l=s.value}catch(t){return void r(t)}s.done?e(l):Promise.resolve(l).then(o,i)}function m(t){return function(){var e=this,r=arguments;return new Promise((function(o,i){var n=t.apply(e,r);function a(t){h(n,o,i,a,s,"next",t)}function s(t){h(n,o,i,a,s,"throw",t)}a(void 0)}))}}var g={name:"management",components:{OrganizationSelect:n["a"]},props:{type:String,cameraList:Array,templateInfo:[Object,Array]},data:function(){return{dialogType:"",showDialog:!1,dialogForm:{foodEvaluate:"",dateValue:[],orgId:"",menuType:"",deviceType:[],deviceModel:[],menuId:""},dialogFormRules:{orgId:[{required:!0,message:"请选择显示组织",trigger:"blur"}],dateValue:[{required:!0,message:"请选择日期",trigger:"blur"}],deviceType:[{required:!0,message:"请选择设备类型",trigger:"blur"}],deviceModel:[{required:!0,message:"请选择设备型号",trigger:"blur"}],menuId:[{required:!0,message:"请选择菜谱",trigger:"blur"}]},deviceList:[],deviceModelList:[],isDisabledModel:!1,menuList:[],dataInfo:{foodEvaluate:"date",dateValue:[],menuType:"week",deviceType:"",deviceModel:"",menuId:"",orgId:""}}},created:function(){"edit"===this.type&&(this.dataInfo=this.templateInfo),this.getOrgDeviceList()},methods:{openDialog:function(t){this.dialogType=t,this.showDialog=!0,this.dialogForm.foodEvaluate=this.dataInfo.foodEvaluate,this.dialogForm.dateValue=this.dataInfo.dateValue,this.dialogForm.orgId=this.dataInfo.orgId,this.dialogForm.menuType=this.dataInfo.menuType,this.dialogForm.deviceType=this.dataInfo.deviceType,this.dialogForm.deviceModel=this.dataInfo.deviceModel,this.dialogForm.menuId=this.dataInfo.menuId,this.dialogForm.deviceType.length?this.getDeviceModel():this.isDisabledModel=!1,this.checkGetMemuList()},confirmDialog:function(){var t=this;this.$refs.dialogFormRef.validate((function(e){e&&(t.dataInfo.foodEvaluate=t.dialogForm.foodEvaluate,t.dataInfo.dateValue=t.dialogForm.dateValue,t.dataInfo.orgId=t.dialogForm.orgId,t.dataInfo.menuType=t.dialogForm.menuType,t.dataInfo.deviceType=t.dialogForm.deviceType,t.dataInfo.deviceModel=t.dialogForm.deviceModel,t.dataInfo.menuId=t.dialogForm.menuId,t.$refs.dialogFormRef.clearValidate(),t.dialogForm={foodEvaluate:"",dateValue:[],orgId:"",menuType:"",deviceType:[],deviceModel:[],menuId:""},t.showDialog=!1,t.$emit("comfirm",t.dataInfo))}))},changeOrg:function(){this.dialogForm.menuId="",this.menuList=[],this.checkGetMemuList()},changeMenuType:function(){this.dialogForm.menuId="",this.menuList=[],this.checkGetMemuList()},deviceTypeChange:function(){this.dialogForm.menuId="",this.menuList=[],this.dialogForm.deviceModel=[],this.deviceModelList=[],this.dialogForm.deviceType.length?this.getDeviceModel():this.isDisabledModel=!1,this.checkGetMemuList()},deviceModelChange:function(){this.dialogForm.menuId="",this.menuList=[],this.checkGetMemuList()},checkGetMemuList:function(){this.dialogForm.deviceType.length&&this.dialogForm.orgId&&(-1!==this.dialogForm.deviceType.indexOf("H5")||-1!==this.dialogForm.deviceType.indexOf("MAPP")||this.dialogForm.deviceModel.length)&&this.getMenuList()},getMenuList:function(){var t=this;return m(p().mark((function e(){var r;return p().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.apiBackgroundDatascreenKanbanTempGetKanbanSettingPost({organization_id:[t.dialogForm.orgId],device_type:t.dialogForm.deviceType,device_model:t.dialogForm.deviceModel,menu_type:t.dialogForm.menuType});case 2:r=e.sent,0===r.code?t.menuList=r.data:t.$message.error(r.msg);case 4:case"end":return e.stop()}}),e)})))()},getOrgDeviceList:function(){var t=this;return m(p().mark((function e(){var r;return p().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.apiBackgroundAdminDeviceDeviceTypePost({source:"self"});case 2:r=e.sent,0===r.code?t.deviceList=[{name:"H5",key:"H5"},{name:"小程序",key:"MAPP"}].concat(s(r.data)):t.$message.error(r.msg);case 4:case"end":return e.stop()}}),e)})))()},getDeviceModel:function(){var t=this;return m(p().mark((function e(){var r,o;return p().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=t.dialogForm.deviceType.filter((function(t){return"H5"!==t&&"MAPP"!==t})),r.length){e.next=6;break}return t.isDisabledModel=!0,e.abrupt("return");case 6:t.isDisabledModel=!1;case 7:return e.next=9,t.$apis.apiBackgroundAdminDeviceDeviceModelPost({device_types:r});case 9:o=e.sent,0===o.code?t.deviceModelList=o.data:t.$message.error(o.msg);case 11:case"end":return e.stop()}}),e)})))()}}},v=g,y=(r("3368"),r("2877")),b=Object(y["a"])(v,o,i,!1,null,null,null);e["default"]=b.exports},b4ab:function(t,e,r){},c3e5:function(t,e,r){},d2a9:function(t,e,r){"use strict";r.r(e);var o=function(){var t=this,e=t._self._c;return e("div",{staticClass:"MealQueue"},[e("div",{staticClass:"header"},[e("div",{staticClass:"time"},[t._v("2022-08-30 08:36:50")]),e("div",{staticClass:"title"},[t._v("叫号屏")]),e("div",{staticClass:"org-title"},[t._v(t._s(t.templateInfo.orgName?t.templateInfo.orgName:"请选择消费点")),e("span",{staticClass:"btn",on:{click:function(e){return t.openDialog("stock")}}},[t._v("编辑")])])]),e("div",{staticClass:"queue"},[t._m(0),e("div",{staticClass:"queue-list"},t._l(8,(function(r,o){return e("div",{key:o,staticClass:"queue-list-item"},[t._v("000"+t._s(r))])})),0)]),e("div",{staticClass:"queue"},[t._m(1),e("div",{staticClass:"queue-list"},t._l(8,(function(r,o){return e("div",{key:o,staticClass:"queue-list-item"},[t._v("000"+t._s(r))])})),0)]),e("el-dialog",{attrs:{title:"选择组织",visible:t.showDialog,width:"500px","custom-class":"ps-dialog"},on:{"update:visible":function(e){t.showDialog=e}}},[e("el-form",{ref:"dialogFormRef",staticClass:"dialog-form",attrs:{model:t.dialogForm,"label-width":"160px"}},[e("el-form-item",{attrs:{label:"显示组织："}},[e("organization-select",{staticClass:"search-item-w ps-input w-180",attrs:{placeholder:"请选择所属组织",isLazy:!1,multiple:!1,"check-strictly":!0,"append-to-body":!0},on:{change:t.changeHandle},model:{value:t.dialogForm.orgId,callback:function(e){t.$set(t.dialogForm,"orgId",e)},expression:"dialogForm.orgId"}})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"ps-cancel-btn",on:{click:function(e){t.showDialog=!1}}},[t._v("取 消")]),e("el-button",{staticClass:"ps-btn",attrs:{type:"primary"},on:{click:t.confirmDialog}},[t._v("确 定")])],1)],1)],1)},i=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"queue-title"},[e("img",{staticClass:"icon-take",attrs:{src:r("0d54"),alt:""}}),e("div",{staticClass:"text"},[t._v("可取餐")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"queue-title"},[e("img",{staticClass:"icon-making",attrs:{src:r("57e2"),alt:""}}),e("div",{staticClass:"text"},[t._v("制作中")])])}],n=r("cbfb"),a={name:"MealQueue",components:{OrganizationSelect:n["a"]},props:{type:String,templateInfo:Object},data:function(){return{dialogType:"",showDialog:!1,dialogForm:{orgId:"",orgName:""}}},methods:{changeHandle:function(t){this.dialogForm.orgName=t.name},openDialog:function(t){this.dialogType=t,this.showDialog=!0,this.dialogForm.orgId=this.templateInfo.orgId},confirmDialog:function(){this.showDialog=!1,this.templateInfo.orgId=this.dialogForm.orgId,this.templateInfo.orgName=this.dialogForm.orgName,this.$emit("comfirm",this.templateInfo)}}},s=a,l=(r("e58f"),r("2877")),c=Object(l["a"])(s,o,i,!1,null,null,null);e["default"]=c.exports},e58f:function(t,e,r){"use strict";r("ab15")},e9cfe:function(t,e,r){},eaed:function(t,e,r){"use strict";r("c3e5")}}]);