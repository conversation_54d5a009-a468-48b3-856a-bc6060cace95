(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-device-admin-components-DeviceDialog"],{"27dc":function(e,t,i){},"50bf":function(e,t,i){"use strict";i("27dc")},e899:function(e,t,i){"use strict";i.r(t);var o=function(){var e=this,t=e._self._c;return t("dialog-message",{attrs:{show:e.visible,title:e.title,showFooter:e.showFooter,loading:e.isLoading,customClass:"ps-dialog",width:e.width},on:{"update:show":function(t){e.visible=t},"update:loading":function(t){e.isLoading=t},close:e.handleClose}},[t("el-form",{ref:"deviceForm",staticClass:"dialog-form",attrs:{model:e.deviceForm,"status-icon":"",rules:e.deviceFormRules,"label-width":"125px",inline:""},nativeOn:{submit:function(e){e.preventDefault()}}},["add"===e.type?t("div",[t("el-form-item",{attrs:{label:"激活码：",prop:"activationCode"}},[t("div",{staticClass:"code-form-item-wrapper"},[t("el-input",{staticStyle:{width:"260px"},attrs:{disabled:""},model:{value:e.deviceForm.activationCode,callback:function(t){e.$set(e.deviceForm,"activationCode",t)},expression:"deviceForm.activationCode"}}),t("el-button",{staticClass:"code-btn-wrapper",attrs:{type:"primary"},on:{click:e.getActivationCode}},[e._v("生成")])],1)]),t("el-form-item",{attrs:{label:"设备名："}},[t("el-input",{staticClass:"ps-input w-350",attrs:{placeholder:"请输入设备名",maxlength:"32"},model:{value:e.deviceForm.deviceName,callback:function(t){e.$set(e.deviceForm,"deviceName",t)},expression:"deviceForm.deviceName"}})],1),t("el-form-item",{attrs:{label:"所属组织：",prop:"organization"}},[t("organization-select",{staticClass:"search-item-w ps-input w-350",attrs:{placeholder:"请选择所属组织",isLazy:!1,multiple:!1,"check-strictly":!0,role:"super","append-to-body":!0,filterable:!1},on:{change:e.organizationSelectChange},model:{value:e.deviceForm.organization,callback:function(t){e.$set(e.deviceForm,"organization",t)},expression:"deviceForm.organization"}})],1),t("el-form-item",{attrs:{label:"设备类型：",prop:"deviceType"}},[t("el-select",{staticClass:"ps-select w-350",attrs:{placeholder:"请选择设备类型","popper-class":"ps-popper-select"},on:{change:e.deviceTypeChange},model:{value:e.deviceForm.deviceType,callback:function(t){e.$set(e.deviceForm,"deviceType",t)},expression:"deviceForm.deviceType"}},e._l(e.deviceTypeList,(function(e){return t("el-option",{key:e.key,attrs:{label:e.name,value:e.key}})})),1)],1),"RLZJ"===e.deviceForm.deviceType?t("div",[t("el-form-item",{attrs:{label:"水控模式",prop:"shuikong"}},[t("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:e.deviceForm.shuikong,callback:function(t){e.$set(e.deviceForm,"shuikong",t)},expression:"deviceForm.shuikong"}})],1),t("el-form-item",{attrs:{label:"考勤功能",prop:"attendance"}},[t("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:e.deviceForm.attendance,callback:function(t){e.$set(e.deviceForm,"attendance",t)},expression:"deviceForm.attendance"}})],1),t("el-form-item",{attrs:{label:"健康码识别功能",prop:"healthyCode"}},[t("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:e.deviceForm.code,callback:function(t){e.$set(e.deviceForm,"code",t)},expression:"deviceForm.code"}})],1),t("el-form-item",{attrs:{label:"体温检测",prop:"temperature"}},[t("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:e.deviceForm.temperature,callback:function(t){e.$set(e.deviceForm,"temperature",t)},expression:"deviceForm.temperature"}})],1),t("el-form-item",{attrs:{label:"门禁功能",prop:"control"}},[t("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:e.deviceForm.control,callback:function(t){e.$set(e.deviceForm,"control",t)},expression:"deviceForm.control"}})],1),t("el-form-item",{attrs:{label:"农行校园设备",prop:"schoolDevice"}},[t("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},on:{change:e.schoolControlChange},model:{value:e.deviceForm.schoolDevice,callback:function(t){e.$set(e.deviceForm,"schoolDevice",t)},expression:"deviceForm.schoolDevice"}})],1)],1):e._e(),t("el-form-item",{attrs:{label:"设备型号：",prop:"deviceModel"}},[t("el-select",{staticClass:"ps-select w-350",attrs:{placeholder:"请选择设备型号","popper-class":"ps-popper-select"},on:{change:e.changeSelectDeviceModel},model:{value:e.deviceForm.deviceModel,callback:function(t){e.$set(e.deviceForm,"deviceModel",t)},expression:"deviceForm.deviceModel"}},e._l(e.deviceModelList,(function(e){return t("el-option",{key:e.key,attrs:{label:e.key,value:e.key}})})),1)],1),"PS-C1050-1"===e.deviceForm.deviceModel||"PS-C1501"===e.deviceForm.deviceModel||"PS-C1051"===e.deviceForm.deviceModel||"PS-D2D"===e.deviceForm.deviceModel||"PS-D2"===e.deviceForm.deviceModel||"PS-D2mini"===e.deviceForm.deviceModel?t("div",["PS-C1050-1"===e.deviceForm.deviceModel||"PS-C1050"===e.deviceForm.deviceModel?t("div",[t("el-form-item",{attrs:{label:"传感器版本-秤",prop:"balanceSensorVersion"}},[t("el-radio-group",{staticClass:"ps-radio",model:{value:e.deviceForm.balanceSensorVersion,callback:function(t){e.$set(e.deviceForm,"balanceSensorVersion",t)},expression:"deviceForm.balanceSensorVersion"}},[t("el-radio",{attrs:{label:"v1"}},[e._v("v1版本")]),t("el-radio",{attrs:{label:"v2"}},[e._v("v2版本")])],1)],1),t("el-form-item",{attrs:{label:"传感器版本-托盘",prop:"traySensorVersion"}},[t("el-radio-group",{staticClass:"ps-radio",model:{value:e.deviceForm.traySensorVersion,callback:function(t){e.$set(e.deviceForm,"traySensorVersion",t)},expression:"deviceForm.traySensorVersion"}},[t("el-radio",{attrs:{label:"v1"}},[e._v("v1版本")]),t("el-radio",{attrs:{label:"v2"}},[e._v("v2版本")]),t("el-radio",{attrs:{label:"v3"}},[e._v("v3版本")])],1)],1)],1):e._e(),t("el-form-item",{attrs:{label:"离线人脸激活码",prop:"offlineFaceActivationCode",rules:[{required:"PS-C1051"===e.deviceForm.deviceModel,message:"请输入离线人脸激活码",trigger:["blur","change"]}]}},[t("el-input",{staticClass:"ps-input w-350",attrs:{placeholder:"请输入离线人脸激活码",maxlength:"16"},model:{value:e.deviceForm.offlineFaceActivationCode,callback:function(t){e.$set(e.deviceForm,"offlineFaceActivationCode",t)},expression:"deviceForm.offlineFaceActivationCode"}})],1)],1):e._e(),"QCG"===e.deviceForm.deviceType?t("div",[t("el-form-item",{attrs:{label:"格子",required:""}},e._l(e.deviceForm.cupboardCeil,(function(o,r){return t("el-form-item",{key:"option"+r,class:[r>0?"m-t-25":"","cupboard-ceil-form"],attrs:{prop:"cupboardCeil[".concat(r,"]"),rules:e.deviceFormRules.cupboardCeil}},[t("el-input",{staticClass:"ps-input",attrs:{placeholder:"请输入格子数"},model:{value:e.deviceForm.cupboardCeil[r],callback:function(t){e.$set(e.deviceForm.cupboardCeil,r,e._n(t))},expression:"deviceForm.cupboardCeil[index]"}}),t("img",{attrs:{src:i("a851"),alt:""},on:{click:function(t){return e.addCupboardCeil()}}}),e.deviceForm.cupboardCeil.length>1?t("img",{attrs:{src:i("1597"),alt:""},on:{click:function(t){return e.delCupboardCeil(r)}}}):e._e()],1)})),1),t("el-form-item",{attrs:{label:"appid：",prop:"cupboardAppid"}},[t("el-input",{staticClass:"ps-input w-350",attrs:{placeholder:"请输入appid"},model:{value:e.deviceForm.cupboardAppid,callback:function(t){e.$set(e.deviceForm,"cupboardAppid",t)},expression:"deviceForm.cupboardAppid"}})],1),t("el-form-item",{attrs:{label:"deviceName：",prop:"cupboardDeviceName"}},[t("el-input",{staticClass:"ps-input w-350",attrs:{placeholder:"请输入deviceName"},model:{value:e.deviceForm.cupboardDeviceName,callback:function(t){e.$set(e.deviceForm,"cupboardDeviceName",t)},expression:"deviceForm.cupboardDeviceName"}})],1),t("el-form-item",{attrs:{label:"deviceSecret：",prop:"cupboardDeviceSecret"}},[t("el-input",{staticClass:"ps-input w-350",attrs:{placeholder:"请输入deviceSecret"},model:{value:e.deviceForm.cupboardDeviceSecret,callback:function(t){e.$set(e.deviceForm,"cupboardDeviceSecret",t)},expression:"deviceForm.cupboardDeviceSecret"}})],1),t("el-form-item",{attrs:{label:"serialport：",prop:"cupboardSerialport"}},[t("el-input",{staticClass:"ps-input w-350",attrs:{placeholder:"请输入serialport（串口号）"},model:{value:e.deviceForm.cupboardSerialport,callback:function(t){e.$set(e.deviceForm,"cupboardSerialport",t)},expression:"deviceForm.cupboardSerialport"}})],1),t("el-form-item",{attrs:{label:"服务器地址：",prop:"cupboardUrl"}},[t("el-input",{staticClass:"ps-input w-350",attrs:{placeholder:"请输入服务器地址"},model:{value:e.deviceForm.cupboardUrl,callback:function(t){e.$set(e.deviceForm,"cupboardUrl",t)},expression:"deviceForm.cupboardUrl"}})],1)],1):e._e(),t("el-form-item",{attrs:{label:"SN码：",prop:"SNCode"}},[t("el-input",{staticClass:"ps-input w-350",attrs:{placeholder:"请输入SN码"},model:{value:e.deviceForm.SNCode,callback:function(t){e.$set(e.deviceForm,"SNCode",t)},expression:"deviceForm.SNCode"}})],1),e.isAutoSellingCabinet?e._e():t("el-form-item",{attrs:{label:"有效期：",prop:"validityDate"}},[t("el-date-picker",{staticClass:"ps-poper-picker",attrs:{type:"daterange",align:"left","unlink-panels":"","range-separator":"至","start-placeholder":"生效时间","end-placeholder":"失效时间","picker-options":e.pickerOptions,"value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:""},model:{value:e.deviceForm.validityDate,callback:function(t){e.$set(e.deviceForm,"validityDate",t)},expression:"deviceForm.validityDate"}})],1)],1):e._e(),"detail"===e.type?t("div",{staticClass:"detail"},[t("el-form-item",{attrs:{label:"所属消费点："}},[t("span",{staticClass:"detail__span"},[e._v(e._s(e.deviceInfo.consumer_name))])]),e._e(),t("el-form-item",{attrs:{label:"设备名："}},[t("span",{staticClass:"detail__span"},[e._v(e._s(e.deviceInfo.device_name))])]),t("el-form-item",{attrs:{label:"设备类型："}},[t("span",{staticClass:"detail__span"},[e._v(e._s(e.deviceInfo.device_type_alias))])]),t("el-form-item",{attrs:{label:"设备型号："}},[t("span",{staticClass:"detail__span"},[e._v(e._s(e.deviceInfo.device_model_alias))])]),"ZDSHG"===e.deviceInfo.device_type?t("el-form-item",{attrs:{label:"SN码："}},[t("span",{staticClass:"detail__span"},[e._v(e._s(e.deviceInfo.serial_no))])]):e._e(),"QCG"===e.deviceInfo.device_type?t("div",[t("el-form-item",{attrs:{label:"deviceName："}},[t("span",{staticClass:"detail__span"},[e._v(e._s(e.cupboardJson.deviceName))])]),t("el-form-item",{attrs:{label:"appid："}},[t("span",{staticClass:"detail__span"},[e._v(e._s(e.cupboardJson.appId))])]),t("el-form-item",{attrs:{label:"deviceSecret："}},[t("span",{staticClass:"detail__span"},[e._v(e._s(e.cupboardJson.deviceSecret))])]),t("el-form-item",{attrs:{label:"串口号："}},[t("span",{staticClass:"detail__span"},[e._v(e._s(e.cupboardJson.serialport))])]),t("el-form-item",{attrs:{label:"柜子数量："}},[t("span",{staticClass:"detail__span"},[e._v(e._s(e.cupboardJson.ceil_list))])])],1):e._e(),"RLZJ"===e.deviceInfo.device_type?t("div",[t("el-form-item",{attrs:{label:"水控模式:"}},[t("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:e.zjJson.shuikong,callback:function(t){e.$set(e.zjJson,"shuikong",t)},expression:"zjJson.shuikong"}})],1),t("el-form-item",{attrs:{label:"考勤功能:"}},[t("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:e.zjJson.attendance,callback:function(t){e.$set(e.zjJson,"attendance",t)},expression:"zjJson.attendance"}})],1),t("el-form-item",{attrs:{label:"健康码识别功能:"}},[t("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:e.zjJson.healthyCode,callback:function(t){e.$set(e.zjJson,"healthyCode",t)},expression:"zjJson.healthyCode"}})],1),t("el-form-item",{attrs:{label:"体温检测:"}},[t("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:e.zjJson.temperature,callback:function(t){e.$set(e.zjJson,"temperature",t)},expression:"zjJson.temperature"}})],1),t("el-form-item",{attrs:{label:"门禁功能:"}},[t("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:e.zjJson.control,callback:function(t){e.$set(e.zjJson,"control",t)},expression:"zjJson.control"}})],1),t("el-form-item",{attrs:{label:"农行校园设备:"}},[t("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:e.zjJson.schoolDevice,callback:function(t){e.$set(e.zjJson,"schoolDevice",t)},expression:"zjJson.schoolDevice"}})],1)],1):e._e(),"ZNC"!==e.deviceInfo.device_type||e.deviceInfo.using||"PS-C1050-1"!==e.deviceInfo.device_model&&"PS-C1050"!==e.deviceInfo.device_model?e._e():t("div",[t("el-form-item",{attrs:{label:"传感器版本-秤",prop:"balanceSensorVersion"}},[t("el-radio-group",{staticClass:"ps-radio",model:{value:e.deviceInfo.balance_sensor_version,callback:function(t){e.$set(e.deviceInfo,"balance_sensor_version",t)},expression:"deviceInfo.balance_sensor_version"}},[t("el-radio",{attrs:{label:"v1"}},[e._v("v1版本")]),t("el-radio",{attrs:{label:"v2"}},[e._v("v2版本")])],1)],1),t("el-form-item",{attrs:{label:"传感器版本-托盘",prop:"traySensorVersion"}},[t("el-radio-group",{staticClass:"ps-radio",model:{value:e.deviceInfo.tray_sensor_version,callback:function(t){e.$set(e.deviceInfo,"tray_sensor_version",t)},expression:"deviceInfo.tray_sensor_version"}},[t("el-radio",{attrs:{label:"v1"}},[e._v("v1版本")]),t("el-radio",{attrs:{label:"v2"}},[e._v("v2版本")]),t("el-radio",{attrs:{label:"v3"}},[e._v("v3版本")])],1)],1)],1)],1):e._e(),"getlog"===e.type?t("div",[t("el-form-item",{attrs:{label:"拉取日期",prop:"logDate"}},[t("el-date-picker",{staticClass:"ps-poper-picker",attrs:{type:"daterange",align:"left","unlink-panels":"","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间","picker-options":e.logPickerOptions,"value-format":"yyyy-MM-dd",format:"yyyy-MM-dd"},model:{value:e.deviceForm.logDate,callback:function(t){e.$set(e.deviceForm,"logDate",t)},expression:"deviceForm.logDate"}})],1)],1):e._e(),"effective"===e.type||"mul_activate_time"===e.type?t("div",[t("el-form-item",{attrs:{label:"有效期：",prop:"validityDate"}},[t("el-date-picker",{staticClass:"ps-poper-picker",attrs:{type:"daterange",align:"left","unlink-panels":"","range-separator":"至","start-placeholder":"生效时间","end-placeholder":"失效时间","picker-options":e.pickerOptions,"value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:""},model:{value:e.deviceForm.validityDate,callback:function(t){e.$set(e.deviceForm,"validityDate",t)},expression:"deviceForm.validityDate"}})],1)],1):e._e(),"name"===e.type?t("div",[t("el-form-item",{attrs:{label:"设备名：",prop:"deviceName"}},[t("el-input",{staticClass:"ps-input w-250",attrs:{placeholder:"请输入设备名",maxlength:"32"},model:{value:e.deviceForm.deviceName,callback:function(t){e.$set(e.deviceForm,"deviceName",t)},expression:"deviceForm.deviceName"}})],1)],1):e._e(),"code"===e.type?t("div",{staticClass:"code-box"},[t("div",{staticClass:"code"},[t("qrcode",{staticClass:"face-img",attrs:{value:"QCG"===e.deviceInfo.device_type?e.deviceInfo.cupboard_json:e.deviceInfo.activation_code,options:{width:280},margin:10,alt:""}}),t("div",{staticClass:"code-info-box"},[t("p",[e._v("设备名："+e._s(e.deviceInfo.device_name))]),t("p",[e._v("设备类型："+e._s(e.deviceInfo.device_type_alias))]),t("p",[e._v("所属组织："+e._s(e.deviceInfo.consumer_name))])])],1)]):e._e(),"weigh"===e.type?t("div",[t("el-form-item",{attrs:{label:"请选择所属商户",prop:"companyId"}},[t("company-select",{staticClass:"search-item-w ps-select",attrs:{clearable:!0,filterable:!0,options:e.companyOptions},on:{getselect:e.changeWeighHandle},model:{value:e.deviceForm.companyId,callback:function(t){e.$set(e.deviceForm,"companyId",t)},expression:"deviceForm.companyId"}})],1),t("div",{staticClass:"origin-text"},[e._v("该激活码支持该商户所有组织使用，仅适用激活双屏结算秤/点餐机“称重模块”")])],1):e._e()]),t("template",{slot:"tool"},[e.showFooter?t("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:e.clickCancleHandle}},[e._v(" 取消 ")]),"weigh"===e.type?t("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickConfirmHandle}},[e._v(" 生成并下载 ")]):t("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickConfirmHandle}},[e._v(" 确定 ")])],1):e._e()]),t("div",{staticClass:"weigh-code-wrapper"},[t("div",{ref:"weighCodeRef",staticClass:"weigh-code p-t-20"},[e.selectWeighCompany.name?t("div",[e._v(e._s(e.selectWeighCompany.name)+"-称重模块激活码")]):e._e(),e.weighCode?t("qrcode",{staticClass:"face-img",attrs:{value:e.weighCode,options:e.weighOption,margin:5,alt:""}}):e._e()],1)])],2)},r=[],a=i("cbfb"),n=i("b2e5"),c=i.n(n),s=i("6e71"),l=i("c0e9"),d=i.n(l),v=i("21a6"),u=i.n(v),p=i("ed08");function m(e){return m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},m(e)}function f(e,t){return _(e)||y(e,t)||g(e,t)||h()}function h(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function g(e,t){if(e){if("string"==typeof e)return b(e,t);var i={}.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?b(e,t):void 0}}function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,o=Array(t);i<t;i++)o[i]=e[i];return o}function y(e,t){var i=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=i){var o,r,a,n,c=[],s=!0,l=!1;try{if(a=(i=i.call(e)).next,0===t){if(Object(i)!==i)return;s=!1}else for(;!(s=(o=a.call(i)).done)&&(c.push(o.value),c.length!==t);s=!0);}catch(e){l=!0,r=e}finally{try{if(!s&&null!=i.return&&(n=i.return(),Object(n)!==n))return}finally{if(l)throw r}}return c}}function _(e){if(Array.isArray(e))return e}function F(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */F=function(){return t};var e,t={},i=Object.prototype,o=i.hasOwnProperty,r=Object.defineProperty||function(e,t,i){e[t]=i.value},a="function"==typeof Symbol?Symbol:{},n=a.iterator||"@@iterator",c=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag";function l(e,t,i){return Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,i){return e[t]=i}}function d(e,t,i,o){var a=t&&t.prototype instanceof b?t:b,n=Object.create(a.prototype),c=new N(o||[]);return r(n,"_invoke",{value:$(e,i,c)}),n}function v(e,t,i){try{return{type:"normal",arg:e.call(t,i)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var u="suspendedStart",p="suspendedYield",f="executing",h="completed",g={};function b(){}function y(){}function _(){}var C={};l(C,n,(function(){return this}));var w=Object.getPrototypeOf,k=w&&w(w(z([])));k&&k!==i&&o.call(k,n)&&(C=k);var S=_.prototype=b.prototype=Object.create(C);function D(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,t){function i(r,a,n,c){var s=v(e[r],e,a);if("throw"!==s.type){var l=s.arg,d=l.value;return d&&"object"==m(d)&&o.call(d,"__await")?t.resolve(d.__await).then((function(e){i("next",e,n,c)}),(function(e){i("throw",e,n,c)})):t.resolve(d).then((function(e){l.value=e,n(l)}),(function(e){return i("throw",e,n,c)}))}c(s.arg)}var a;r(this,"_invoke",{value:function(e,o){function r(){return new t((function(t,r){i(e,o,t,r)}))}return a=a?a.then(r,r):r()}})}function $(t,i,o){var r=u;return function(a,n){if(r===f)throw Error("Generator is already running");if(r===h){if("throw"===a)throw n;return{value:e,done:!0}}for(o.method=a,o.arg=n;;){var c=o.delegate;if(c){var s=j(c,o);if(s){if(s===g)continue;return s}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(r===u)throw r=h,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);r=f;var l=v(t,i,o);if("normal"===l.type){if(r=o.done?h:p,l.arg===g)continue;return{value:l.arg,done:o.done}}"throw"===l.type&&(r=h,o.method="throw",o.arg=l.arg)}}}function j(t,i){var o=i.method,r=t.iterator[o];if(r===e)return i.delegate=null,"throw"===o&&t.iterator.return&&(i.method="return",i.arg=e,j(t,i),"throw"===i.method)||"return"!==o&&(i.method="throw",i.arg=new TypeError("The iterator does not provide a '"+o+"' method")),g;var a=v(r,t.iterator,i.arg);if("throw"===a.type)return i.method="throw",i.arg=a.arg,i.delegate=null,g;var n=a.arg;return n?n.done?(i[t.resultName]=n.value,i.next=t.nextLoc,"return"!==i.method&&(i.method="next",i.arg=e),i.delegate=null,g):n:(i.method="throw",i.arg=new TypeError("iterator result is not an object"),i.delegate=null,g)}function L(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function I(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function N(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(L,this),this.reset(!0)}function z(t){if(t||""===t){var i=t[n];if(i)return i.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,a=function i(){for(;++r<t.length;)if(o.call(t,r))return i.value=t[r],i.done=!1,i;return i.value=e,i.done=!0,i};return a.next=a}}throw new TypeError(m(t)+" is not iterable")}return y.prototype=_,r(S,"constructor",{value:_,configurable:!0}),r(_,"constructor",{value:y,configurable:!0}),y.displayName=l(_,s,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,l(e,s,"GeneratorFunction")),e.prototype=Object.create(S),e},t.awrap=function(e){return{__await:e}},D(x.prototype),l(x.prototype,c,(function(){return this})),t.AsyncIterator=x,t.async=function(e,i,o,r,a){void 0===a&&(a=Promise);var n=new x(d(e,i,o,r),a);return t.isGeneratorFunction(i)?n:n.next().then((function(e){return e.done?e.value:n.next()}))},D(S),l(S,s,"Generator"),l(S,n,(function(){return this})),l(S,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),i=[];for(var o in t)i.push(o);return i.reverse(),function e(){for(;i.length;){var o=i.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},t.values=z,N.prototype={constructor:N,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(I),!t)for(var i in this)"t"===i.charAt(0)&&o.call(this,i)&&!isNaN(+i.slice(1))&&(this[i]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var i=this;function r(o,r){return c.type="throw",c.arg=t,i.next=o,r&&(i.method="next",i.arg=e),!!r}for(var a=this.tryEntries.length-1;a>=0;--a){var n=this.tryEntries[a],c=n.completion;if("root"===n.tryLoc)return r("end");if(n.tryLoc<=this.prev){var s=o.call(n,"catchLoc"),l=o.call(n,"finallyLoc");if(s&&l){if(this.prev<n.catchLoc)return r(n.catchLoc,!0);if(this.prev<n.finallyLoc)return r(n.finallyLoc)}else if(s){if(this.prev<n.catchLoc)return r(n.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<n.finallyLoc)return r(n.finallyLoc)}}}},abrupt:function(e,t){for(var i=this.tryEntries.length-1;i>=0;--i){var r=this.tryEntries[i];if(r.tryLoc<=this.prev&&o.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var a=r;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var n=a?a.completion:{};return n.type=e,n.arg=t,a?(this.method="next",this.next=a.finallyLoc,g):this.complete(n)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.finallyLoc===e)return this.complete(i.completion,i.afterLoc),I(i),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.tryLoc===e){var o=i.completion;if("throw"===o.type){var r=o.arg;I(i)}return r}}throw Error("illegal catch attempt")},delegateYield:function(t,i,o){return this.delegate={iterator:z(t),resultName:i,nextLoc:o},"next"===this.method&&(this.arg=e),g}},t}function C(e,t,i,o,r,a,n){try{var c=e[a](n),s=c.value}catch(e){return void i(e)}c.done?t(s):Promise.resolve(s).then(o,r)}function w(e){return function(){var t=this,i=arguments;return new Promise((function(o,r){var a=e.apply(t,i);function n(e){C(a,o,r,n,c,"next",e)}function c(e){C(a,o,r,n,c,"throw",e)}n(void 0)}))}}var k={name:"trayDialog",components:{OrganizationSelect:a["a"],qrcode:c.a,CompanySelect:s["a"]},props:{loading:Boolean,isshow:Boolean,showFooter:{type:Boolean,default:!0},type:{type:String,default:""},title:{type:String,default:""},width:{type:String,default:"600px"},deviceInfo:{type:Object,default:function(){return{}}},deviceTypeList:{type:Array,default:function(){return[]}},selectList:{type:Array,default:function(){return[]}},minTime:{type:[Number,String],default:""},confirm:Function},data:function(){var e=function(e,t,i){t.length?t[1].split("-")[2]-t[0].split("-")[2]>7?i(new Error("您所选的日期超过7天，请重新选择")):i():i(new Error("请选择日期"))};return{isLoading:!1,pickerOptions:{shortcuts:[{text:"一个月",onClick:function(e){var t=new Date,i=new Date;t.setTime(t.getTime()+2592e6),e.$emit("pick",[i,t])}},{text:"三个月",onClick:function(e){var t=new Date,i=new Date;t.setTime(t.getTime()+7776e6),e.$emit("pick",[i,t])}},{text:"一年",onClick:function(e){var t=new Date,i=new Date;t.setTime(t.getTime()+31536e6),e.$emit("pick",[i,t])}}]},logPickerOptions:{disabledDate:function(e){return e.getTime()>Date.now()},shortcuts:[{text:"最近一周",onClick:function(e){var t=new Date,i=new Date;i.setTime(i.getTime()-5184e5),e.$emit("pick",[i,t])}}]},deviceForm:{activationCode:"",organization:"",deviceName:"",deviceType:"",deviceModel:"",SNCode:"",validityDate:[],logDate:[],cupboardCeil:[32],cupboardAppid:"",cupboardDeviceName:"",cupboardDeviceSecret:"",cupboardSerialport:"",cupboardUrl:"",shuikong:!1,attendance:!1,healthyCode:!1,temperature:!1,control:!1,balanceSensorVersion:"v1",traySensorVersion:"v1",offlineFaceActivationCode:"",schoolDevice:!1},deviceFormRules:{deviceName:[{required:!0,message:"请输入设备名",trigger:"blur"}],organization:[{required:!0,message:"请选择组织",trigger:"blur"}],deviceType:[{required:!0,message:"请选择设备类型",trigger:"change"}],deviceModel:[{required:!0,message:"请选择设备型号",trigger:"change"}],logDate:[{required:!0,validator:e,trigger:"blur"}],validityDate:[{required:!0,message:"请选择有效期",trigger:"change"}],cupboardCeil:[{required:!0,message:"格子数不能为空"},{type:"number",message:"格子数必须为数字值"}],cupboardAppid:[{required:!0,message:"请输入appid",trigger:"blur"}],cupboardDeviceName:[{required:!0,message:"请输入deviceName",trigger:"blur"}],cupboardDeviceSecret:[{required:!0,message:"请输入deviceSecret",trigger:"blur"}],cupboardSerialport:[{required:!0,message:"请输入serialport（串口号）",trigger:"blur"}],cupboardUrl:[{required:!0,message:"请输入服务器地址",trigger:"blur"}],SNCode:[{required:!1,message:"请输入SN码",trigger:"blur"}]},deviceModelList:[],cupboardJson:{},zjJson:{shuikong:!1,attendance:!1,healthyCode:!1,temperature:!1,control:!1,schoolDevice:!1},companyOptions:{label:"name",value:"company"},selectWeighCompany:{},weighCode:"",weighOption:{errorCorrectionLevel:"H",width:280},isAutoSellingCabinet:!1}},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},watch:{isshow:function(e){var t=this;if(e){if("add"===this.type&&this.getActivationCode(),this.weighCode="",this.selectWeighCompany={},"name"===this.type&&(this.deviceForm.deviceName=this.deviceInfo.device_name),this.deviceInfo.cupboard_json&&(this.cupboardJson=JSON.parse(this.deviceInfo.cupboard_json)),this.deviceInfo.zj_json){var i=JSON.parse(this.deviceInfo.zj_json);this.zjJson.shuikong=!!i.sk_on,this.zjJson.attendance=!!i.kq_on,this.zjJson.healthyCode=!!i.jkm_on,this.zjJson.temperature=!!i.tw_on,this.zjJson.control=!!i.mj_on,this.zjJson.schoolDevice,i.abc_school}e&&(this.deviceForm.validityDate=this.getNextYear()),"mul_activate_time"===this.type&&(this.pickerOptions.disabledDate=function(e){return t.minTime>e.getTime()})}}},created:function(){},mounted:function(){},methods:{initLoad:function(){this.getActivationCode()},clickConfirmHandle:function(){var e=this;this.$refs.deviceForm.validate((function(t){var i;if(t)switch(e.type){case"add":i={activation_code:e.deviceForm.activationCode,device_name:e.deviceForm.deviceName,organization_id:e.deviceForm.organization,device_type:e.deviceForm.deviceType,device_model:e.deviceForm.deviceModel,start_date:e.deviceForm.validityDate[0],end_date:e.deviceForm.validityDate[1]},"PS-C1050-1"!==e.deviceForm.deviceModel&&"PS-C1050"!==e.deviceForm.deviceModel||(i.balance_sensor_version=e.deviceForm.balanceSensorVersion,i.tray_sensor_version=e.deviceForm.traySensorVersion),e.deviceForm.offlineFaceActivationCode&&(i.offline_face_activation_code=e.deviceForm.offlineFaceActivationCode),"QCG"===e.deviceForm.deviceType&&(i.cupboard_json={appId:e.deviceForm.cupboardAppid,deviceName:e.deviceForm.cupboardDeviceName,deviceSecret:e.deviceForm.cupboardDeviceSecret,serialport:e.deviceForm.cupboardSerialport,url:e.deviceForm.cupboardUrl,ceil_list:e.deviceForm.cupboardCeil,activation_code:e.deviceForm.activationCode}),"RLZJ"===e.deviceForm.deviceType&&(i.zj_json={sk_on:e.deviceForm.shuikong?1:0,kq_on:e.deviceForm.attendance?1:0,jkm_on:e.deviceForm.healthyCode?1:0,tw_on:e.deviceForm.temperature?1:0,mj_on:e.deviceForm.control?1:0,abc_school:e.deviceForm.schoolDevice?1:0}),e.deviceForm.SNCode&&(i.serial_no=e.deviceForm.SNCode),e.addDevice(i);break;case"detail":switch(e.deviceInfo.device_type){case"RLZJ":i={device_no:e.deviceInfo.device_no,zj_json:{sk_on:e.zjJson.shuikong?1:0,kq_on:e.zjJson.attendance?1:0,jkm_on:e.zjJson.healthyCode?1:0,tw_on:e.zjJson.temperature?1:0,mj_on:e.zjJson.control?1:0,abc_school:e.zjJson.schoolDevice?1:0}},e.modifyDevice(i);break;case"ZNC":"PS-C1050-1"!==e.deviceInfo.device_model&&"PS-C1050"!==e.deviceInfo.device_model||(i={device_no:e.deviceInfo.device_no,balance_sensor_version:e.deviceInfo.balance_sensor_version,tray_sensor_version:e.deviceInfo.tray_sensor_version},e.modifyDevice(i)),e.isshow=!1;break}break;case"name":i={device_no:e.deviceInfo.device_no,device_name:e.deviceForm.deviceName},e.modifyDevice(i);break;case"effective":i={device_no:e.deviceInfo.device_no,time_range:{start_date:e.deviceForm.validityDate[0],end_date:e.deviceForm.validityDate[1]}},e.modifyDevice(i);break;case"mul_activate_time":i={device_nos:e.selectList,choices:6,valid_day:{start_time:e.deviceForm.validityDate[0]+" 00:00:00",end_time:e.deviceForm.validityDate[1]+" 23:59:59"}},e.modifyMulDevice(i);break;case"getlog":i={device_no:e.deviceInfo.device_no,start_date:e.deviceForm.logDate[0],end_date:e.deviceForm.logDate[1]},e.deviceLogPull(i);break;case"weigh":i={company_id:e.deviceForm.companyId},e.addWeighCode(i);break}}))},addDevice:function(e){var t=this;return w(F().mark((function i(){var o;return F().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(!t.isLoading){i.next=2;break}return i.abrupt("return");case 2:return t.isLoading=!0,i.next=5,t.$apis.apiBackgroundAdminDeviceAddPost(e);case 5:o=i.sent,t.isLoading=!1,0===o.code?(t.$message.success("新建成功"),t.$emit("confirm","search")):t.$message.error(o.msg);case 8:case"end":return i.stop()}}),i)})))()},modifyMulDevice:function(e){var t=this;return w(F().mark((function i(){var o;return F().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(!t.isLoading){i.next=2;break}return i.abrupt("return");case 2:return t.isLoading=!0,i.next=5,t.$apis.apiBackgroundAdminDeviceBatchModifyPost(e);case 5:o=i.sent,t.isLoading=!1,0===o.code?(t.$message.success("修改成功"),t.$emit("confirm","search")):t.$message.error(o.msg);case 8:case"end":return i.stop()}}),i)})))()},modifyDevice:function(e){var t=this;return w(F().mark((function i(){var o;return F().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(!t.isLoading){i.next=2;break}return i.abrupt("return");case 2:return t.isLoading=!0,i.next=5,t.$apis.apiBackgroundAdminDeviceModifyPost(e);case 5:o=i.sent,t.isLoading=!1,0===o.code?(t.$message.success("修改成功"),t.$emit("confirm","search")):t.$message.error(o.msg);case 8:case"end":return i.stop()}}),i)})))()},deviceLogPull:function(e){var t=this;return w(F().mark((function i(){var o;return F().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,t.$apis.apiBackgroundAdminDevicePullLogPost(e);case 2:o=i.sent,0===o.code?(t.$message.success("拉取日志成功"),t.$emit("confirm","search")):t.$message.error(o.msg);case 4:case"end":return i.stop()}}),i)})))()},addWeighCode:function(e){var t=this;return w(F().mark((function i(){var o;return F().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(!t.isLoading){i.next=2;break}return i.abrupt("return");case 2:return t.isLoading=!0,i.next=5,t.$apis.apiBackgroundAdminDeviceGenerateActiveCodePost(e);case 5:o=i.sent,0===o.code?(t.weighCode=o.data,t.$message.success(o.msg),t.$nextTick((function(e){t.downWeighCode()}))):(t.isLoading=!1,t.$message.error(o.msg));case 7:case"end":return i.stop()}}),i)})))()},changeWeighHandle:function(e){this.selectWeighCompany=e.item},downWeighCode:function(){var e=this,t=this.$refs.weighCodeRef;d()(t).then((function(t){var i=t.toDataURL();u.a.saveAs(i,e.selectWeighCompany.name+"-称重模块激活码.png"),e.$emit("confirm","search")})),this.isLoading=!1},clickCancleHandle:function(){this.visible=!1,this.deviceForm.deviceName=""},handleClose:function(e){this.$refs.deviceForm.resetFields(),this.isLoading=!1,this.visible=!1,this.deviceForm.deviceName="",this.deviceForm.offlineFaceActivationCode=""},normalizer:function(e){return{id:e.id,label:e.name,children:e.children_list}},getActivationCode:function(){var e=this;return w(F().mark((function t(){var i;return F().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundAdminDeviceGenerateActivationPost();case 2:i=t.sent,0===i.code?e.deviceForm.activationCode=i.data.activation_code:e.$message.error(i.msg);case 4:case"end":return t.stop()}}),t)})))()},deviceTypeChange:function(e){this.deviceFormRules.SNCode[0].required=e&&("ZDSHG"===e||"CJY"===e),this.$refs.deviceForm&&this.$refs.deviceForm.clearValidate(),this.isAutoSellingCabinet=e&&"ZDSHG"===e,this.deviceForm.deviceModel="",this.getDeviceModel()},getDeviceModel:function(){var e=this;return w(F().mark((function t(){var i;return F().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundAdminDeviceDeviceModelPost({device_type:e.deviceForm.deviceType});case 2:i=t.sent,0===i.code?e.deviceModelList=i.data:e.$message.error(i.msg);case 4:case"end":return t.stop()}}),t)})))()},getNextYear:function(){var e,t;"mul_activate_time"===this.type?(e=this.minTime+31536e6,t=[new Date(this.minTime).getFullYear(),(new Date(this.minTime).getMonth()+1).toString().padStart(2,"0"),new Date(this.minTime).getDate().toString().padStart(2,"0")].join("-")):(e=(new Date).getTime()+31536e6,t=[(new Date).getFullYear(),((new Date).getMonth()+1).toString().padStart(2,"0"),(new Date).getDate().toString().padStart(2,"0")].join("-"));var i=[new Date(e).getFullYear(),(new Date(e).getMonth()+1).toString().padStart(2,"0"),new Date(e).getDate().toString().padStart(2,"0")].join("-");return[t,i]},addCupboardCeil:function(){this.deviceForm.cupboardCeil.push("")},delCupboardCeil:function(e){this.deviceForm.cupboardCeil.splice(e,1)},changeSelectDeviceModel:function(){var e=this;this.$nextTick((function(){e.$refs.deviceForm.clearValidate(["offlineFaceActivationCode"])}))},ifActivate:function(){return!!this.deviceInfo.activation_status},schoolControlChange:function(e){e&&(this.deviceForm.organization||(this.$message.error("请先选择所属组织!"),this.$set(this.deviceForm,"schoolDevice",!1)))},organizationSelectChange:function(e){var t=e?e.id:"";t&&this.getSchoolInfoBYOrgId(t)},getSchoolInfoBYOrgId:function(e){var t=this;return w(F().mark((function i(){var o,r,a,n;return F().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,Object(p["Z"])(t.$apis.xxx({id:e}));case 2:if(o=i.sent,r=f(o,2),a=r[0],n=r[1],!a){i.next=8;break}return i.abrupt("return");case 8:n&&0===n.code&&(n.data||{});case 9:case"end":return i.stop()}}),i)})))()}}},S=k,D=(i("50bf"),i("2877")),x=Object(D["a"])(S,o,r,!1,null,"40bf7938",null);t["default"]=x.exports}}]);