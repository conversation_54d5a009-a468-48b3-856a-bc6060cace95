(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-system-components-TableTemplate"],{"1c80":function(t,e,o){"use strict";o.r(e);var r=function(){var t=this,e=t._self._c;return e("div",{staticClass:"table-code-template"},[e("div",{staticClass:"template-title"},[t._v("包厢/桌台二维码模板")]),e("div",{staticClass:"template-setting"},[e("div",{staticClass:"code-setting"},[e("el-form",{ref:"tableFormRef",attrs:{model:t.tableForm,rules:t.tableFormRules}},[e("el-form-item",{staticClass:"inline-box w-250",attrs:{label:"标题",prop:"title"}},[e("el-input",{staticClass:"w-180 ps-input",model:{value:t.tableForm.title,callback:function(e){t.$set(t.tableForm,"title",e)},expression:"tableForm.title"}})],1),e("el-form-item",{staticClass:"inline-box w-300",attrs:{label:"模板"}},[e("el-select",{staticClass:"ps-select w-180",attrs:{placeholder:"请选择模板"},on:{change:t.selectTemplate},model:{value:t.tableForm.template,callback:function(e){t.$set(t.tableForm,"template",e)},expression:"tableForm.template"}},t._l(t.templateList,(function(t){return e("el-option",{key:t.file_path,attrs:{label:t.name,value:t.file_path}})})),1),"custom"===t.tableForm.template?e("div",{staticClass:"template-upload inline-box"},[e("el-upload",{ref:"uploadTemplate",staticClass:"upload-demo",attrs:{headers:t.headersOpts,data:t.uploadParams,action:t.actionUrl,"show-file-list":!1,"on-success":t.handleUploadSuccess,"before-upload":t.beforeUploadTemplate}},[e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"}},[t._v("上传模板")])],1)],1):t._e()],1),"custom"===t.tableForm.template?e("div",[e("el-form-item",{staticClass:"inline-box w-250",attrs:{label:"标题距离顶部",prop:"titlePx"}},[e("el-input",{staticClass:"w-100 ps-input",model:{value:t.tableForm.titlePx,callback:function(e){t.$set(t.tableForm,"titlePx",e)},expression:"tableForm.titlePx"}}),e("span",{staticClass:"m-l-5"},[t._v("px")])],1),e("el-form-item",{staticClass:"inline-box w-250",attrs:{label:"二维码距离顶部",prop:"codePx"}},[e("el-input",{staticClass:"w-100 ps-input",model:{value:t.tableForm.codePx,callback:function(e){t.$set(t.tableForm,"codePx",e)},expression:"tableForm.codePx"}}),e("span",{staticClass:"m-l-5"},[t._v("px")])],1),e("el-form-item",{staticClass:"inline-box w-300",attrs:{label:"桌台信息距离顶部",prop:"tablePx"}},[e("el-input",{staticClass:"w-100 ps-input",model:{value:t.tableForm.tablePx,callback:function(e){t.$set(t.tableForm,"tablePx",e)},expression:"tableForm.tablePx"}}),e("span",{staticClass:"m-l-5"},[t._v("px")])],1),e("div",{staticClass:"tips"},[t._v("自定义模板参数：945x1418px；300分辨率；所有信息默认居中。")]),e("el-form-item",{staticClass:"label-block",attrs:{label:"标题颜色"}},[e("div",{staticClass:"color-list"},[t._l(t.colorList,(function(o){return e("div",{key:o,class:["color-item",t.tableForm.titleColor===o?"color-active":""],on:{click:function(e){return t.chooseColor("tableTitle",o)}}},[e("div",{staticClass:"color-box",style:{backgroundColor:"".concat(o)}})])})),e("el-color-picker",{class:["custom"===t.tableForm.titleColor?"active-picker":""],on:{change:function(e){return t.changeColor("tableTitle",e)}},model:{value:t.tableForm.titleCustomColor,callback:function(e){t.$set(t.tableForm,"titleCustomColor",e)},expression:"tableForm.titleCustomColor"}})],2)]),e("el-form-item",{staticClass:"label-block",attrs:{label:"配送地址颜色"}},[e("div",{staticClass:"color-list"},[t._l(t.colorList,(function(o){return e("div",{key:o,class:["color-item",t.tableForm.tableColor===o?"color-active":""],on:{click:function(e){return t.chooseColor("tableColor",o)}}},[e("div",{staticClass:"color-box",style:{backgroundColor:"".concat(o)}})])})),e("el-color-picker",{class:["custom"===t.tableForm.tableColor?"active-picker":""],on:{change:function(e){return t.changeColor("tableColor",e)}},model:{value:t.tableForm.tableCustomColor,callback:function(e){t.$set(t.tableForm,"tableCustomColor",e)},expression:"tableForm.tableCustomColor"}})],2)])],1):t._e()],1)],1),t.tableForm.fileList?e("div",{staticClass:"preview-img"},[e("img",{attrs:{src:t.tableForm.fileList,alt:"",srcset:""}}),e("div",{staticClass:"qrcode",style:{top:"custom"===t.tableForm.template?"".concat(t.tableForm.codePx/3,"px"):"140px"}},[e("qrcode",{attrs:{value:"https://cashier-v4.debug.packertec.com",options:{width:175,margin:2.5},alt:""}})],1),e("div",{staticClass:"title",style:{color:"".concat("custom"===t.tableForm.titleColor?t.tableForm.titleCustomColor:t.tableForm.titleColor),top:"custom"===t.tableForm.template?"".concat(t.tableForm.titlePx/3,"px"):"404px"}},[t._v(" "+t._s(t.tableForm.title)+" ")]),e("div",{staticClass:"code-info",style:{color:"".concat("custom"===t.tableForm.tableColor?t.tableForm.tableCustomColor:t.tableForm.tableColor),top:"custom"===t.tableForm.template?"".concat(t.tableForm.tablePx/3,"px"):"315px"}},[t._v(" A02 ")])]):t._e()]),e("el-button",{staticClass:"ps-origin-btn w-150",attrs:{size:"small",type:"primary"},on:{click:function(e){return t.checkForm()}}},[t._v("保存")])],1)},a=[],l=o("b2e5"),n=o.n(l);function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return e};var t,e={},o=Object.prototype,r=o.hasOwnProperty,a=Object.defineProperty||function(t,e,o){t[e]=o.value},l="function"==typeof Symbol?Symbol:{},n=l.iterator||"@@iterator",c=l.asyncIterator||"@@asyncIterator",u=l.toStringTag||"@@toStringTag";function m(t,e,o){return Object.defineProperty(t,e,{value:o,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{m({},"")}catch(t){m=function(t,e,o){return t[e]=o}}function f(t,e,o,r){var l=e&&e.prototype instanceof y?e:y,n=Object.create(l.prototype),i=new $(r||[]);return a(n,"_invoke",{value:E(t,o,i)}),n}function p(t,e,o){try{return{type:"normal",arg:t.call(e,o)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var h="suspendedStart",b="suspendedYield",d="executing",v="completed",g={};function y(){}function C(){}function F(){}var x={};m(x,n,(function(){return this}));var w=Object.getPrototypeOf,_=w&&w(w(j([])));_&&_!==o&&r.call(_,n)&&(x=_);var L=F.prototype=y.prototype=Object.create(x);function P(t){["next","throw","return"].forEach((function(e){m(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function o(a,l,n,s){var c=p(t[a],t,l);if("throw"!==c.type){var u=c.arg,m=u.value;return m&&"object"==i(m)&&r.call(m,"__await")?e.resolve(m.__await).then((function(t){o("next",t,n,s)}),(function(t){o("throw",t,n,s)})):e.resolve(m).then((function(t){u.value=t,n(u)}),(function(t){return o("throw",t,n,s)}))}s(c.arg)}var l;a(this,"_invoke",{value:function(t,r){function a(){return new e((function(e,a){o(t,r,e,a)}))}return l=l?l.then(a,a):a()}})}function E(e,o,r){var a=h;return function(l,n){if(a===d)throw Error("Generator is already running");if(a===v){if("throw"===l)throw n;return{value:t,done:!0}}for(r.method=l,r.arg=n;;){var i=r.delegate;if(i){var s=T(i,r);if(s){if(s===g)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===h)throw a=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=d;var c=p(e,o,r);if("normal"===c.type){if(a=r.done?v:b,c.arg===g)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(a=v,r.method="throw",r.arg=c.arg)}}}function T(e,o){var r=o.method,a=e.iterator[r];if(a===t)return o.delegate=null,"throw"===r&&e.iterator.return&&(o.method="return",o.arg=t,T(e,o),"throw"===o.method)||"return"!==r&&(o.method="throw",o.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var l=p(a,e.iterator,o.arg);if("throw"===l.type)return o.method="throw",o.arg=l.arg,o.delegate=null,g;var n=l.arg;return n?n.done?(o[e.resultName]=n.value,o.next=e.nextLoc,"return"!==o.method&&(o.method="next",o.arg=t),o.delegate=null,g):n:(o.method="throw",o.arg=new TypeError("iterator result is not an object"),o.delegate=null,g)}function I(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function $(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(I,this),this.reset(!0)}function j(e){if(e||""===e){var o=e[n];if(o)return o.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,l=function o(){for(;++a<e.length;)if(r.call(e,a))return o.value=e[a],o.done=!1,o;return o.value=t,o.done=!0,o};return l.next=l}}throw new TypeError(i(e)+" is not iterable")}return C.prototype=F,a(L,"constructor",{value:F,configurable:!0}),a(F,"constructor",{value:C,configurable:!0}),C.displayName=m(F,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===C||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,F):(t.__proto__=F,m(t,u,"GeneratorFunction")),t.prototype=Object.create(L),t},e.awrap=function(t){return{__await:t}},P(k.prototype),m(k.prototype,c,(function(){return this})),e.AsyncIterator=k,e.async=function(t,o,r,a,l){void 0===l&&(l=Promise);var n=new k(f(t,o,r,a),l);return e.isGeneratorFunction(o)?n:n.next().then((function(t){return t.done?t.value:n.next()}))},P(L),m(L,u,"Generator"),m(L,n,(function(){return this})),m(L,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),o=[];for(var r in e)o.push(r);return o.reverse(),function t(){for(;o.length;){var r=o.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=j,$.prototype={constructor:$,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(O),!e)for(var o in this)"t"===o.charAt(0)&&r.call(this,o)&&!isNaN(+o.slice(1))&&(this[o]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var o=this;function a(r,a){return i.type="throw",i.arg=e,o.next=r,a&&(o.method="next",o.arg=t),!!a}for(var l=this.tryEntries.length-1;l>=0;--l){var n=this.tryEntries[l],i=n.completion;if("root"===n.tryLoc)return a("end");if(n.tryLoc<=this.prev){var s=r.call(n,"catchLoc"),c=r.call(n,"finallyLoc");if(s&&c){if(this.prev<n.catchLoc)return a(n.catchLoc,!0);if(this.prev<n.finallyLoc)return a(n.finallyLoc)}else if(s){if(this.prev<n.catchLoc)return a(n.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<n.finallyLoc)return a(n.finallyLoc)}}}},abrupt:function(t,e){for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var l=a;break}}l&&("break"===t||"continue"===t)&&l.tryLoc<=e&&e<=l.finallyLoc&&(l=null);var n=l?l.completion:{};return n.type=t,n.arg=e,l?(this.method="next",this.next=l.finallyLoc,g):this.complete(n)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var o=this.tryEntries[e];if(o.finallyLoc===t)return this.complete(o.completion,o.afterLoc),O(o),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var o=this.tryEntries[e];if(o.tryLoc===t){var r=o.completion;if("throw"===r.type){var a=r.arg;O(o)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,o,r){return this.delegate={iterator:j(e),resultName:o,nextLoc:r},"next"===this.method&&(this.arg=t),g}},e}function c(t,e,o,r,a,l,n){try{var i=t[l](n),s=i.value}catch(t){return void o(t)}i.done?e(s):Promise.resolve(s).then(r,a)}function u(t){return function(){var e=this,o=arguments;return new Promise((function(r,a){var l=t.apply(e,o);function n(t){c(l,r,a,n,i,"next",t)}function i(t){c(l,r,a,n,i,"throw",t)}n(void 0)}))}}var m={name:"TableTemplate",components:{qrcode:n.a},props:{tableInfo:{type:Object,default:function(){return{}}},templateList:{type:Array,default:function(){return[]}},colorList:{type:Array,default:function(){return[]}},actionUrl:String,headersOpts:{type:Object,default:function(){return{}}},confirm:Function},data:function(){return{tableForm:{title:"",template:"",titlePx:"1212",codePx:"420",tablePx:"945",titleColor:"#000000",titleCustomColor:"",tableColor:"#000000",tableCustomColor:"",fileList:""},tableFormRules:{title:[{required:!0,message:"请填写标题",trigger:"blur"}],titlePx:[{required:!0,message:"请填写标题距离",trigger:"blur"}],codePx:[{required:!0,message:"请填写二维码距离",trigger:"blur"}],tablePx:[{required:!0,message:"请填写桌台距离",trigger:"blur"}]},uploadParams:{prefix:"tableQrcodeTemplate"}}},watch:{tableInfo:function(){this.tableInfo&&(this.tableForm.title=this.tableInfo.settings.title,this.tableInfo.type?(this.tableForm.titlePx=this.tableInfo.settings.title2top,this.tableForm.tablePx=this.tableInfo.settings.addr2top,this.tableForm.codePx=this.tableInfo.settings.qrcode2top,this.tableForm.template="custom",this.tableForm.titleColor="custom",this.tableForm.titleCustomColor=this.tableInfo.settings.title_color,this.tableForm.tableColor="custom",this.tableForm.tableCustomColor=this.tableInfo.settings.addr_color,this.tableForm.fileList=this.tableInfo.temp_path):(this.tableForm.template=this.tableInfo.temp_path,this.tableForm.titleColor=this.tableInfo.settings.title_color,this.tableForm.tableColor=this.tableInfo.settings.addr_color,this.tableForm.fileList=location.origin+"/api/temporary/"+this.tableInfo.temp_path,this.setSystemColor(this.tableInfo.temp_path)))}},methods:{checkForm:function(){var t=this;this.$refs.tableFormRef.validate((function(e){if(!e)return t.$message.error("数据填写有误，请检查"),!1;t.saveTemplate()}))},saveTemplate:function(){var t=this;return u(s().mark((function e(){return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$confirm("确认保存吗？","提示",{confirmButtonText:t.$t("dialog.confirm_btn"),cancelButtonText:t.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var e=u(s().mark((function e(o,r,a){var l,n;return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("confirm"!==o){e.next=15;break}return r.confirmButtonLoading=!0,l={organization:t.$store.getters.organization,qrcode_type:"table"},t.tableInfo&&(l.id=t.tableInfo.id),l.name=t.templateList.filter((function(e){return e.file_path===t.tableForm.template})).name,l.settings={title:t.tableForm.title},"custom"===t.tableForm.template?(l.type=1,l.temp_path=t.tableForm.fileList,l.settings.title2top=t.tableForm.titlePx,l.settings.addr2top=t.tableForm.tablePx,l.settings.qrcode2top=t.tableForm.codePx,l.settings.title_color="custom"===t.tableForm.titleColor?t.tableForm.titleCustomColor:t.tableForm.titleColor,l.settings.addr_color="custom"===t.tableForm.tableColor?t.tableForm.tableCustomColor:t.tableForm.tableColor):(l.type=0,l.temp_path=t.tableForm.template),e.next=9,t.$apis.apiBackgroundTablecodeRsvQrocodeTemplateModifyPost(l);case 9:n=e.sent,0===n.code?(t.$message.success("保存成功"),t.confirm()):t.$message.error(n.msg),a(),r.confirmButtonLoading=!1,e.next=16;break;case 15:r.confirmButtonLoading||a();case 16:case"end":return e.stop()}}),e)})));function o(t,o,r){return e.apply(this,arguments)}return o}()}).then((function(t){})).catch((function(t){}));case 1:case"end":return e.stop()}}),e)})))()},selectTemplate:function(t){"custom"===t?(this.tableForm.fileList=-1===this.tableInfo.temp_path.indexOf("http")?"":this.tableInfo.temp_path,this.tableForm.titleColor="#000000",this.tableForm.tableColor="#000000"):(this.tableForm.fileList=location.origin+"/api/temporary/"+t,this.setSystemColor(t))},setSystemColor:function(t){-1!==t.indexOf("01")?(this.tableForm.titleColor="#ffffff",this.tableForm.tableColor="#0C97F6"):-1!==t.indexOf("02")?(this.tableForm.titleColor="#FEC037",this.tableForm.tableColor="#FEC037"):-1!==t.indexOf("03")?(this.tableForm.titleColor="#ffffff",this.tableForm.tableColor="#E84F3D"):-1!==t.indexOf("04")?(this.tableForm.titleColor="#2A8CE3",this.tableForm.tableColor="#2A8CE3"):-1!==t.indexOf("05")&&(this.tableForm.titleColor="#ffffff",this.tableForm.tableColor="#FEC037")},chooseColor:function(t,e){"tableTitle"===t?this.tableForm.titleColor=e:"tableColor"===t&&(this.tableForm.tableColor=e)},changeColor:function(t,e){"tableTitle"===t?(this.tableForm.titleColor="custom",this.tableForm.titleCustomColor=e):"tableColor"===t&&(this.tableForm.tableColor="custom",this.tableForm.tableCustomColor=e)},handleUploadSuccess:function(t,e){0===t.code?(this.$refs.uploadTemplate.clearFiles(),this.tableForm.fileList=t.data.public_url,this.getImgPx(this.tableForm.fileList)):this.$message.error(t.msg)},beforeUploadTemplate:function(t){var e="image/jpeg"===t.type||"image/png"===t.type,o=t.size/1024/1024<5;return this.uploadParams.key=(new Date).getTime()+Math.floor(150*Math.random()),e||this.$message.error("模板只能是 JPG PNG格式!"),o||this.$message.error("模板图片大小不能超过 5MB!"),e&&o},getImgPx:function(t){var e=new Image;return e.src=t,new Promise((function(t){e.onload=function(){var o=e.width,r=e.height;t({width:o,height:r})}}))}}},f=m,p=(o("6d6f"),o("2877")),h=Object(p["a"])(f,r,a,!1,null,null,null);e["default"]=h.exports},"5e0a":function(t,e,o){},"6d6f":function(t,e,o){"use strict";o("5e0a")}}]);