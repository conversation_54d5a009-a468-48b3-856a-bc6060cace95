(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-report-bank-financial-statements-constantsConfig"],{"80fe":function(e,a,l){"use strict";l.r(a),l.d(a,"recentSevenDayTime",(function(){return u})),l.d(a,"recentSevenDay",(function(){return c})),l.d(a,"SETTLEMENT_DETAILS",(function(){return s})),l.d(a,"ACCOUNT_BILLING_DETAILS",(function(){return _})),l.d(a,"BANK_FLOW_DETAILS",(function(){return v})),l.d(a,"BANK_FLOW_REFUND_DETAILS",(function(){return p})),l.d(a,"PAYWAY_DEFAULT",(function(){return d})),l.d(a,"PAYWAY_CHONGZHI",(function(){return o})),l.d(a,"PAYWAY_XIAOFEI",(function(){return y}));var t=l("5a0c"),u=[t().subtract(7,"day").format("YYYY-MM-DD HH:mm:ss"),t().format("YYYY-MM-DD HH:mm:ss")],c=[t().subtract(7,"day").format("YYYY-MM-DD"),t().format("YYYY-MM-DD")],r=[{label:"出金中",value:"ORDER_OUTPAYING"},{label:"出金失败",value:"ORDER_OUTPAY_FAILED"},{label:"出金状态未知",value:"ORDER_OUTPAY_UNKNOWN"},{label:"出金成功",value:"ORDER_OUTPAY"},{label:"未出金",value:"ORDER_OUTPAY_NO"},{label:"无需出金",value:"ORDER_OUTPAY_NO_NEED"}],i=[{label:"支付成功",value:"ORDER_SUCCESS"},{label:"支付失败",value:"ORDER_FAILED"}],n=[{label:"部分退款",value:"ORDER_PART_REFUND"},{label:"全额退款",value:"ORDER_ALL_REFUND"}],b=[{label:"农行支付（用于充值）",value:"merchant"},{label:"微信支付（JSAPI）",value:"jsapi"},{label:"微信支付（B扫C）",value:"wechat_micropay"},{label:"支付宝支付（B扫C）",value:"ali_micropay"},{label:"农行掌银付款码（B扫C）",value:"abc_micropay"},{label:"微信/支付宝支付（B扫C）",value:"wechat_alipay"},{label:"授权支付",value:"agent"},{label:"出金",value:"out_payment"},{label:"快e付",value:"fast_e_pay"}],s={select_time:{type:"daterange",label:"清算时间",value:c},sub_mch_id:{type:"select",value:"",label:"账户",dataList:[]},status:{type:"select",value:"",label:"状态",dataList:r,clearable:!0}},_={select_time:{type:"daterange",label:"经营时间",value:c},sub_mch_id:{type:"select",value:"",label:"账户",dataList:[]}},v={select_time:{type:"daterange",label:"日期",value:c},order_type:{type:"select",value:"",label:"支付方式",dataList:b,clearable:!0},status:{type:"select",value:"",label:"订单状态",dataList:i,clearable:!0},order_no:{type:"input",value:"",label:"订单号",dataList:[],clearable:!0},sub_mch_id:{type:"select",value:"",label:"账户",dataList:[]},org_ids:{type:"organizationSelect",value:[],label:"组织",listNameKey:"name",listValueKey:"id",dataList:[],checkStrictly:!0,multiple:!0,collapseTags:!0,clearable:!0}},p={select_time:{type:"daterange",label:"日期",value:c},order_type:{type:"select",value:"",label:"支付方式",dataList:b,clearable:!0},status:{type:"select",value:"",label:"订单状态",dataList:n,clearable:!0},order_no:{type:"input",value:"",label:"订单号",dataList:[],clearable:!0},refund_no:{type:"input",value:"",label:"退款订单号",dataList:[],clearable:!0},sub_mch_id:{type:"select",value:"",label:"账户",dataList:[]},org_ids:{type:"organizationSelect",value:[],label:"组织",listNameKey:"name",listValueKey:"id",dataList:[],checkStrictly:!0,multiple:!0,collapseTags:!0,clearable:!0}},d=[{label:"农行支付（用于充值）",value:["merchant"]},{label:"农行支付（农行卡授权代扣支付）",value:["abc_micropay"]},{label:"农行支付（快E付支付）",value:["fast_e_pay"]}],o=[{label:"农行支付（用于充值）",value:["merchant"]}],y=[{label:"储值支付",value:["chuzhi_pay"]},{label:"农行支付（农行卡授权代扣支付)",value:["abc_micropay"]},{label:"农行支付（快E付支付）",value:["fast_e_pay"]}]}}]);