(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-report-alipay-constantsConfig","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-meal-report-MealPackageRule"],{"4ddc":function(e,a,t){"use strict";t.r(a),t.d(a,"recentSevenDay",(function(){return m})),t.d(a,"ALIPAY_QYCODE_CONSUME",(function(){return c})),t.d(a,"ALIPAY_QYCODE_CONSUME_TABLE",(function(){return p})),t.d(a,"ALIPAY_QYCODE_REFUND",(function(){return f})),t.d(a,"ALIPAY_QYCODE_REFUND_TABLE",(function(){return _}));var l=t("5a0c"),n=t("c9d9");t("ed08");function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function i(e,a){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);a&&(l=l.filter((function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable}))),t.push.apply(t,l)}return t}function u(e){for(var a=1;a<arguments.length;a++){var t=null!=arguments[a]?arguments[a]:{};a%2?i(Object(t),!0).forEach((function(a){y(e,a,t[a])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach((function(a){Object.defineProperty(e,a,Object.getOwnPropertyDescriptor(t,a))}))}return e}function y(e,a,t){return(a=o(a))in e?Object.defineProperty(e,a,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[a]=t,e}function o(e){var a=d(e,"string");return"symbol"==r(a)?a:a+""}function d(e,a){if("object"!=r(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var l=t.call(e,a||"default");if("object"!=r(l))return l;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===a?String:Number)(e)}var m=[l().subtract(6,"day").format("YYYY-MM-DD"),l().format("YYYY-MM-DD")],b={discount_type:{type:"select",label:"额度方式",value:"",dataList:[{label:"全部",value:"ALL"},{label:"记账",value:"JZ"},{label:"实扣",value:"SK"}]},org_ids:{type:"organizationSelect",value:[],label:"所属组织",listNameKey:"name",listValueKey:"id",dataList:[],multiple:!0,checkStrictly:!0,collapseTags:!0,clearable:!0},name:{type:"input",label:"姓名",value:"",placeholder:"请输入姓名"},phone:{type:"input",label:"手机",value:"",placeholder:"请输入手机"},meal_type:{type:"select",label:"餐段",value:"",dataList:n["a"],placeholder:"请选择"},payer_department_group_ids:{type:"organizationDepartmentSelect",multiple:!0,checkStrictly:!0,flat:!1,label:"部门",value:[],placeholder:"请选择部门",dataList:[],limit:1,level:1,clearable:!0},payer_group_ids:{type:"groupSelect",label:"分组",value:[],placeholder:"请选择分组",dataList:[],multiple:!0,collapseTags:!0,clearable:!0}},c=u(u({select_time:{type:"daterange",value:m,format:"yyyy-MM-dd",clearable:!1,label:"创建时间"}},b),{},{out_trade_no:{type:"input",value:"",label:"第三方订单号",placeholder:"请输入订单号",labelWidth:"100px"}}),p=[{key:"trade_no",label:"订单号"},{key:"out_trade_no",label:"第三方单号"},{key:"create_time",label:"创建时间"},{key:"pay_time",label:"扣款时间"},{key:"org_name",label:"所属组织"},{key:"origin_fee",label:"订单总金额",type:"money"},{key:"food_subsidy_fee",label:"企业代付金额",type:"money"},{key:"pay_fee",label:"支付宝个人支付",type:"money"},{key:"pay_method_alias",label:"支付方式"},{key:"sub_payway_alias",label:"支付类型"},{key:"payment_order_type_alias",label:"消费类型"},{key:"meal_type_alias",label:"餐段"},{key:"name",label:"人员姓名"},{key:"phone",label:"手机号码"},{key:"payer_group_name",label:"分组"},{key:"payer_department_group_name",label:"部门"}],f=u({select_time:{type:"daterange",value:m,format:"yyyy-MM-dd",clearable:!1,label:"退款时间"}},b),_=[{key:"origin_trade_no",label:"原订单号"},{key:"refund_no",label:"退款订单号"},{key:"create_time",label:"退款时间"},{key:"finish_time",label:"退款到账时间"},{key:"refund_fee",label:"退款总金额",type:"money"},{key:"refund_food_subsidy_fee",label:"退款企业金额",type:"money"},{key:"pay_fee",label:"退款支付宝个人金额",type:"money"},{key:"pay_method_alias",label:"支付方式"},{key:"sub_payway_alias",label:"支付类型"},{key:"payment_order_type_alias",label:"消费类型"},{key:"meal_type_alias",label:"餐段"},{key:"name",label:"姓名"},{key:"phone",label:"手机号"},{key:"payer_group_name",label:"分组"},{key:"payer_department_group_name",label:"部门"}]},c9d9:function(e,a,t){"use strict";t.d(a,"a",(function(){return r})),t.d(a,"d",(function(){return i})),t.d(a,"b",(function(){return u})),t.d(a,"c",(function(){return y})),t.d(a,"e",(function(){return o})),t.d(a,"f",(function(){return d})),t.d(a,"g",(function(){return m}));var l=t("5a0c"),n=t("da92"),r=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],i=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],u={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},y=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],o=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],d=(l().subtract(7,"day").format("YYYY-MM-DD"),l().format("YYYY-MM-DD"),function(e){return e?"number"===typeof e?n["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:n["a"].divide(e,100).toFixed(2):"0.00"}),m=function(e){return n["a"].times(e,100)}}}]);