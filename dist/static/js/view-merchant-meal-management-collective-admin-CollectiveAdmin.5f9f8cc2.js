(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-collective-admin-CollectiveAdmin","view-merchant-meal-management-collective-admin-collectiveMenuDialog","view-merchant-meal-management-collective-admin-constants"],{"398a":function(e,t,r){"use strict";r("eeca")},"3f77":function(e,t,r){"use strict";r("f49a")},"4a9c":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("dialog-message",{attrs:{show:e.visible,title:e.title,loading:e.isLoading,customClass:"ps-dialog",width:e.width},on:{"update:show":function(t){e.visible=t},"update:loading":function(t){e.isLoading=t},close:e.handleClose}},[t("el-form",{ref:"formData",staticClass:"dialog-form collective-mel-dialog",attrs:{model:e.form,rules:e.rules,size:"small"}},[t("el-form-item",{staticClass:"block-label",attrs:{label:"菜谱名称：",prop:"name"}},[t("el-input",{staticClass:"ps-input",attrs:{placeholder:"请输入菜谱名称",maxlength:"15"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),t("el-form-item",{staticClass:"block-label",attrs:{label:"所属集体"}},[t("el-select",{staticClass:"ps-select",staticStyle:{width:"100%"},attrs:{disabled:""},model:{value:e.form.collective_value,callback:function(t){e.$set(e.form,"collective_value",t)},expression:"form.collective_value"}},e._l(e.collective_groups,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),t("el-form-item",{staticClass:"block-label",attrs:{label:"日期"}},[t("el-radio-group",{model:{value:e.form.menu_type,callback:function(t){e.$set(e.form,"menu_type",t)},expression:"form.menu_type"}},[t("el-radio",{staticClass:"ps-radio",attrs:{label:"week"}},[e._v("周菜谱")]),t("el-radio",{staticClass:"ps-radio",attrs:{label:"month"}},[e._v("月菜谱")])],1)],1),"week"===e.form.menu_type?t("div",{staticClass:"ps-flex-align-c"},[t("el-form-item",{attrs:{label:""}},[t("el-date-picker",{staticClass:"w-200 m-r-20",attrs:{type:"month","picker-options":e.pickerOptions,placeholder:"选择月","value-format":"yyyy-MM",clearable:!1},on:{change:e.changeWeekMont},model:{value:e.form.weekMontValue,callback:function(t){e.$set(e.form,"weekMontValue",t)},expression:"form.weekMontValue"}})],1),t("el-form-item",{attrs:{label:""}},[t("el-select",{staticClass:"ps-select",staticStyle:{width:"300px"},attrs:{"popper-class":"ps-popper-select",placeholder:"选择周",multiple:"","collapse-tags":"",size:"small"},model:{value:e.form.weekList,callback:function(t){e.$set(e.form,"weekList",t)},expression:"form.weekList"}},e._l(e.weekListGroups,(function(r){return t("el-option",{key:r.start_end,attrs:{label:r.label,value:r.start_end,disabled:e.weekDisabled(r)}})})),1)],1)],1):e._e(),t("div",["month"===e.form.menu_type?t("el-form-item",{attrs:{label:""}},[t("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"months","picker-options":e.pickerOptions,"value-format":"yyyy-MM",placeholder:"选择月",clearable:!1},model:{value:e.form.monthList,callback:function(t){e.$set(e.form,"monthList",t)},expression:"form.monthList"}})],1):e._e()],1),t("el-form-item",{attrs:{label:"设备类型：",prop:"device_types"}},[t("el-select",{staticClass:"ps-select",staticStyle:{width:"100%"},attrs:{multiple:"",placeholder:"请选择设备类型","popper-class":"ps-popper-select"},on:{change:e.deviceTypeChange},model:{value:e.form.device_types,callback:function(t){e.$set(e.form,"device_types",t)},expression:"form.device_types"}},e._l(e.deviceArr,(function(e){return t("el-option",{key:e.key,attrs:{label:e.name,value:e.key}})})),1)],1),t("el-form-item",{attrs:{label:"设备型号：",prop:e.isDisabledModel?"":"device_model"}},[t("el-select",{staticClass:"ps-select",staticStyle:{width:"100%"},attrs:{multiple:"",placeholder:"请选择设备型号","popper-class":"ps-popper-select",disabled:e.isDisabledModel},model:{value:e.form.device_model,callback:function(t){e.$set(e.form,"device_model",t)},expression:"form.device_model"}},e._l(e.deviceModelList,(function(e){return t("el-option",{key:e.key,attrs:{label:e.key,value:e.key}})})),1)],1),t("el-form-item",{attrs:{label:"可见分组",prop:"use_user_groups"}},[t("user-group-select",{staticStyle:{width:"100%"},attrs:{multiple:"",disabled:e.isDisabledGroup,options:e.groupOptions},model:{value:e.form.use_user_groups,callback:function(t){e.$set(e.form,"use_user_groups",t)},expression:"form.use_user_groups"}})],1)],1),t("template",{slot:"tool"},[t("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:e.clickCancleHandle}},[e._v(" 取消 ")]),t("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickConfirmHandle}},[e._v(" 保存 ")])],1)])],2)},o=[],i=r("5a0c"),a=r("390a"),l=r("ed08");function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function s(e){return d(e)||f(e)||v(e)||u()}function u(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function d(e){if(Array.isArray(e))return g(e)}function p(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */p=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function f(e,t,r,n){var i=t&&t.prototype instanceof b?t:b,a=Object.create(i.prototype),l=new j(n||[]);return o(a,"_invoke",{value:O(e,r,l)}),a}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var m="suspendedStart",h="suspendedYield",v="executing",g="completed",y={};function b(){}function w(){}function _(){}var k={};u(k,a,(function(){return this}));var L=Object.getPrototypeOf,x=L&&L(L($([])));x&&x!==r&&n.call(x,a)&&(k=x);var D=_.prototype=b.prototype=Object.create(k);function C(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function M(e,t){function r(o,i,a,l){var s=d(e[o],e,i);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==c(f)&&n.call(f,"__await")?t.resolve(f.__await).then((function(e){r("next",e,a,l)}),(function(e){r("throw",e,a,l)})):t.resolve(f).then((function(e){u.value=e,a(u)}),(function(e){return r("throw",e,a,l)}))}l(s.arg)}var i;o(this,"_invoke",{value:function(e,n){function o(){return new t((function(t,o){r(e,n,t,o)}))}return i=i?i.then(o,o):o()}})}function O(t,r,n){var o=m;return function(i,a){if(o===v)throw Error("Generator is already running");if(o===g){if("throw"===i)throw a;return{value:e,done:!0}}for(n.method=i,n.arg=a;;){var l=n.delegate;if(l){var c=P(l,n);if(c){if(c===y)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===m)throw o=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var s=d(t,r,n);if("normal"===s.type){if(o=n.done?g:h,s.arg===y)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=g,n.method="throw",n.arg=s.arg)}}}function P(t,r){var n=r.method,o=t.iterator[n];if(o===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,P(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var i=d(o,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,y;var a=i.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,y):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function S(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function j(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(S,this),this.reset(!0)}function $(t){if(t||""===t){var r=t[a];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function r(){for(;++o<t.length;)if(n.call(t,o))return r.value=t[o],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(c(t)+" is not iterable")}return w.prototype=_,o(D,"constructor",{value:_,configurable:!0}),o(_,"constructor",{value:w,configurable:!0}),w.displayName=u(_,s,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,u(e,s,"GeneratorFunction")),e.prototype=Object.create(D),e},t.awrap=function(e){return{__await:e}},C(M.prototype),u(M.prototype,l,(function(){return this})),t.AsyncIterator=M,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new M(f(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},C(D),u(D,s,"Generator"),u(D,a,(function(){return this})),u(D,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=$,j.prototype={constructor:j,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(E),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function o(n,o){return l.type="throw",l.arg=t,r.next=n,o&&(r.method="next",r.arg=e),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],l=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),E(r),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;E(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:$(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),y}},t}function m(e,t){return b(e)||y(e,t)||v(e,t)||h()}function h(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function v(e,t){if(e){if("string"==typeof e)return g(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?g(e,t):void 0}}function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function y(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,l=[],c=!0,s=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);c=!0);}catch(e){s=!0,o=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return l}}function b(e){if(Array.isArray(e))return e}function w(e,t,r,n,o,i,a){try{var l=e[i](a),c=l.value}catch(e){return void r(e)}l.done?t(c):Promise.resolve(c).then(n,o)}function _(e){return function(){var t=this,r=arguments;return new Promise((function(n,o){var i=e.apply(t,r);function a(e){w(i,n,o,a,l,"next",e)}function l(e){w(i,n,o,a,l,"throw",e)}a(void 0)}))}}var k={name:"collectiveMelDialog",props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:"创建菜谱"},width:{type:String,default:"600px"},isshow:Boolean,formDataDialog:{type:Object,default:function(){return{}}},confirm:Function},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},components:{UserGroupSelect:a["a"]},data:function(){return{isLoading:!1,rules:{name:[{required:!0,message:"请输入名称",trigger:"blur"}],device_types:[{required:!0,message:"请选择设备类型",trigger:"change"}],device_model:[{required:!0,message:"请选择设备型号",trigger:"change"}]},deviceArr:[],collective_groups:[],deviceModelList:[],form:{id:-1,name:"",collective_value:"",menu_type:"week",device_types:[],device_model:[],use_user_groups:[],weekMontValue:"",monthList:[],weekList:[]},isDisabledModel:!1,isDisabledGroup:!0,groupOptions:{label:"group_name",value:"id"},pickerOptions:{disabledDate:function(e){return e.getTime()<Date.now()-864e5}},weekListGroups:[]}},created:function(){this.initLoad(),this.collective_groups.push({name:this.formDataDialog.name,id:this.formDataDialog.id}),this.form.collective_value=this.formDataDialog.id},mounted:function(){},methods:{getWeekList:function(e){var t=[],r=i(e).startOf("week").add(1,"day"),n={label:r.format("MM.DD")+"-"+i(e).endOf("week").add(1,"day").format("MM.DD"),start:r.format("YYYY-MM-DD"),end:i(e).endOf("week").add(1,"day").format("YYYY-MM-DD"),start_end:r.format("YYYY-MM-DD")+"_"+i(e).endOf("week").add(1,"day").format("YYYY-MM-DD")};t.push(n);for(var o=1;o<4;o++)t.push({label:r.add(o,"week").startOf("week").add(1,"day").format("MM.DD")+"-"+r.add(o,"week").endOf("week").add(1,"day").format("MM.DD"),start:r.add(o,"week").startOf("week").add(1,"day").format("YYYY-MM-DD"),end:r.add(o,"week").endOf("week").add(1,"day").format("YYYY-MM-DD"),start_end:r.add(o,"week").startOf("week").add(1,"day").format("YYYY-MM-DD")+"_"+r.add(o,"week").endOf("week").add(1,"day").format("YYYY-MM-DD")});return t},getCollectiveCreateFoodMenu:function(e){var t=this;return _(p().mark((function r(){var n,o,i,a;return p().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return t.isLoading=!0,r.next=3,Object(l["Z"])(t.$apis.apiBackgroundFoodCollectiveCreateFoodMenuPost(e));case 3:if(n=r.sent,o=m(n,2),i=o[0],a=o[1],t.isLoading=!1,!i){r.next=11;break}return t.$message.error(i.message),r.abrupt("return");case 11:0===a.code?(t.$message.success(a.msg),t.visible=!1,t.confirm()):t.$message.error(a.msg);case 12:case"end":return r.stop()}}),r)})))()},clickConfirmHandle:function(){var e=this;this.$refs.formData.validate((function(t){if(!t)return!1;var r={name:e.form.name,menu_type:e.form.menu_type,menu_weekly_data:[],menu_monthly_data:[],collective_id:e.form.collective_value,device_types:e.form.device_types,device_model:e.form.device_model,use_user_groups:e.form.use_user_groups};return e.form.weekList.length&&e.form.weekList.forEach((function(e){var t=e.split("_");r.menu_weekly_data.push({start_date:t[0],end_date:t[1]})})),e.form.monthList.length&&e.form.monthList.forEach((function(e){r.menu_monthly_data.push({month:i(e).format("YYYYMM")})})),"week"!==e.form.menu_type||e.form.weekList.length?"month"!==e.form.menu_type||e.form.monthList.length?void e.getCollectiveCreateFoodMenu(r):e.$message.error("请选择月"):e.$message.error("请选择周")}))},clickCancleHandle:function(){this.visible=!1},handleClose:function(e){this.visible=!1},initLoad:function(){var e=this;return _(p().mark((function t(){return p().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getOrgDeviceList();case 2:e.getDeviceModel();case 3:case"end":return t.stop()}}),t)})))()},deviceTypeChange:function(){this.form.device_model=[],this.$refs.formData.clearValidate(),this.getDeviceModel()},getDeviceModel:function(){var e=this;return _(p().mark((function t(){var r,n,o;return p().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(r=["H5","MAPP","ZNC"],e.isDisabledGroup=!0,r.map((function(t){e.form.device_types.includes(t)&&(e.isDisabledGroup=!1)})),n=e.form.device_types.filter((function(e){return"H5"!==e&&"MAPP"!==e})),n.length){t.next=9;break}return e.isDisabledModel=!0,t.abrupt("return");case 9:e.isDisabledModel=!1;case 10:return t.next=12,e.$apis.apiBackgroundDeviceDeviceDeviceModelPost({device_types:n});case 12:o=t.sent,0===o.code?e.deviceModelList=o.data:e.$message.error(o.msg);case 14:case"end":return t.stop()}}),t)})))()},getOrgDeviceList:function(){var e=this;return _(p().mark((function t(){var r;return p().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundDeviceDeviceDeviceTypePost({source:"self"});case 2:r=t.sent,0===r.code?e.deviceArr=[{name:"H5",key:"H5"},{name:"小程序",key:"MAPP"}].concat(s(r.data)):e.$message.error(r.msg);case 4:case"end":return t.stop()}}),t)})))()},changeWeekMont:function(e){this.weekListGroups=this.getWeekList(i(e).add(1,"day"))},weekDisabled:function(e){var t=!1;return(new Date).getTime()>new Date(e.end).getTime()&&(t=!0),t}}},L=k,x=(r("398a"),r("2877")),D=Object(x["a"])(L,n,o,!1,null,"53819005",null);t["default"]=D.exports},8426:function(e,t,r){"use strict";r.r(t),r.d(t,"COLLECTIVE_ADMIN",(function(){return n}));var n={select_date:{type:"datetimerange",label:"创建时间",format:"yyyy-MM-dd HH:mm:ss",value:[]},name:{type:"input",label:"名称",value:"",placeholder:"请输入名字"},min_age:{type:"input",label:"最小年龄",value:"",placeholder:"请输入最小年龄"},max_age:{type:"input",label:"最大年龄",value:"",placeholder:"请输入最大年龄"}}},c848:function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"collective-admin container-wrapper"},[t("refresh-tool",{on:{refreshPage:e.refreshHandle}}),t("search-form",{ref:"searchRef",attrs:{"form-setting":e.searchFormSetting,"label-width":"105px",autoSearch:!1},on:{search:e.searchHandle}}),t("div",{staticClass:"table-wrapper"},[t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title",staticStyle:{width:"900px"}},[e._v(" 数据列表 ")]),t("div",{staticClass:"align-r"},[t("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_food.collective.add"],expression:"['background_food.collective.add']"}],attrs:{color:"origin",type:"add"},on:{click:function(t){return e.addAndEditCollective("add")}}},[e._v(" 新增集体 ")])],1)]),t("div",{staticClass:"table-content"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},[t("el-table-column",{attrs:{prop:"name",label:"名称",align:"center"}}),t("el-table-column",{attrs:{prop:"number",label:"总人数",align:"center"}}),t("el-table-column",{attrs:{prop:"man_number",label:"男性人数",align:"center"}}),t("el-table-column",{attrs:{prop:"women_number",label:"女性人数",align:"center"}}),t("el-table-column",{attrs:{prop:"",label:"所含人群及人数",align:"center",width:"220px"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("div",{staticClass:"collapse-wrapper"},[t("div",{staticClass:"collapse-list hide"},e._l(r.row.crowd_list,(function(r,n){return t("el-tag",{key:n,staticClass:"m-r-5 m-t-5 collapse-data",attrs:{size:"medium",effect:"plain",type:"info"}},[e._v(" "+e._s(r.crowd_name)+"（"+e._s(r.number)+"） ")])})),1)])]}}])}),t("el-table-column",{attrs:{prop:"",label:"标签",align:"center",width:"220px"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("div",{staticClass:"collapse-wrapper"},[t("div",{staticClass:"collapse-list hide"},e._l(r.row.label,(function(n,o){return t("el-tag",{key:o,staticClass:"m-r-5 m-t-5 collapse-data",attrs:{size:"medium",effect:"plain",type:"danger",closable:""},on:{close:function(t){return e.closeTag(n,r.row)}}},[e._v(" "+e._s(n.name)+" ")])})),1)])]}}])}),t("el-table-column",{attrs:{prop:"remark",label:"备注",align:"center"}}),t("el-table-column",{attrs:{prop:"operator_name",label:"创建人",align:"center"}}),t("el-table-column",{attrs:{prop:"create_time",label:"创建时间",align:"center"}}),t("el-table-column",{attrs:{fixed:"right",label:"操作",width:"180",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_food.collective.create_food_menu"],expression:"['background_food.collective.create_food_menu']"}],staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.clickCollectiveMelDialog(r.row)}}},[e._v(" 创建菜谱 ")]),t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_food.collective.modify"],expression:"['background_food.collective.modify']"}],staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.addAndEditCollective("modify",r.row)}}},[e._v(" 编辑 ")]),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.clickMenuPeview(r.row)}}},[e._v(" 预览 ")]),t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_food.collective.delete"],expression:"['background_food.collective.delete']"}],staticClass:"ps-red",attrs:{type:"text",size:"small"},on:{click:function(t){return e.clickCollectiveDel(r.row)}}},[e._v(" 删除 ")])]}}])})],1)],1),t("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[t("el-pagination",{staticClass:"ps-text",attrs:{"current-page":e.currentPage,"page-sizes":[10,20,50,100,500],"page-size":e.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)]),e.collectiveDialogVisible?t("add-and-modify-collective",{attrs:{isshow:e.collectiveDialogVisible,title:e.collectiveDialogTitle,type:e.collectiveDialogType,formDataDialog:e.formcollectiveDataDialog},on:{"update:isshow":function(t){e.collectiveDialogVisible=t},confirm:e.searchHandle}}):e._e(),e.collectiveMelDialogVisible?t("collective-menu-dialog",{ref:"selectLaber",attrs:{isshow:e.collectiveMelDialogVisible,formDataDialog:e.dialogMelInfo,width:"600px"},on:{"update:isshow":function(t){e.collectiveMelDialogVisible=t},confirm:e.searchHandle}}):e._e(),t("dialog-message",{attrs:{show:e.dialogMenuPeview,title:"选择预览菜谱",customClass:"ps-dialog",width:"350px"},on:{"update:show":function(t){e.dialogMenuPeview=t}}},[t("div",{staticClass:"ps-center"},[t("el-select",{staticClass:"ps-select w-250",attrs:{placeholder:"请选择"},model:{value:e.menuPeviewId,callback:function(t){e.menuPeviewId=t},expression:"menuPeviewId"}},e._l(e.menuPeviewList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.menu_name,value:e.id}})})),1)],1),t("template",{slot:"tool"},[t("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:function(t){e.dialogMenuPeview=!1}}},[e._v(" 取消 ")]),t("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickMenuPeviewDetermine}},[e._v(" 确定 ")])],1)])],2)],1)},o=[],i=r("f72e"),a=r("ed08"),l=r("8426"),c=r("4a9c");function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function u(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */u=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function f(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{f({},"")}catch(e){f=function(e,t,r){return e[t]=r}}function d(e,t,r,n){var i=t&&t.prototype instanceof b?t:b,a=Object.create(i.prototype),l=new j(n||[]);return o(a,"_invoke",{value:O(e,r,l)}),a}function p(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var m="suspendedStart",h="suspendedYield",v="executing",g="completed",y={};function b(){}function w(){}function _(){}var k={};f(k,a,(function(){return this}));var L=Object.getPrototypeOf,x=L&&L(L($([])));x&&x!==r&&n.call(x,a)&&(k=x);var D=_.prototype=b.prototype=Object.create(k);function C(e){["next","throw","return"].forEach((function(t){f(e,t,(function(e){return this._invoke(t,e)}))}))}function M(e,t){function r(o,i,a,l){var c=p(e[o],e,i);if("throw"!==c.type){var u=c.arg,f=u.value;return f&&"object"==s(f)&&n.call(f,"__await")?t.resolve(f.__await).then((function(e){r("next",e,a,l)}),(function(e){r("throw",e,a,l)})):t.resolve(f).then((function(e){u.value=e,a(u)}),(function(e){return r("throw",e,a,l)}))}l(c.arg)}var i;o(this,"_invoke",{value:function(e,n){function o(){return new t((function(t,o){r(e,n,t,o)}))}return i=i?i.then(o,o):o()}})}function O(t,r,n){var o=m;return function(i,a){if(o===v)throw Error("Generator is already running");if(o===g){if("throw"===i)throw a;return{value:e,done:!0}}for(n.method=i,n.arg=a;;){var l=n.delegate;if(l){var c=P(l,n);if(c){if(c===y)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===m)throw o=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var s=p(t,r,n);if("normal"===s.type){if(o=n.done?g:h,s.arg===y)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=g,n.method="throw",n.arg=s.arg)}}}function P(t,r){var n=r.method,o=t.iterator[n];if(o===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,P(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var i=p(o,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,y;var a=i.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,y):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function S(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function j(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(S,this),this.reset(!0)}function $(t){if(t||""===t){var r=t[a];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function r(){for(;++o<t.length;)if(n.call(t,o))return r.value=t[o],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(s(t)+" is not iterable")}return w.prototype=_,o(D,"constructor",{value:_,configurable:!0}),o(_,"constructor",{value:w,configurable:!0}),w.displayName=f(_,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,f(e,c,"GeneratorFunction")),e.prototype=Object.create(D),e},t.awrap=function(e){return{__await:e}},C(M.prototype),f(M.prototype,l,(function(){return this})),t.AsyncIterator=M,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new M(d(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},C(D),f(D,c,"Generator"),f(D,a,(function(){return this})),f(D,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=$,j.prototype={constructor:j,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(E),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function o(n,o){return l.type="throw",l.arg=t,r.next=n,o&&(r.method="next",r.arg=e),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],l=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),E(r),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;E(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:$(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),y}},t}function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach((function(t){p(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function p(e,t,r){return(t=m(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function m(e){var t=h(e,"string");return"symbol"==s(t)?t:t+""}function h(e,t){if("object"!=s(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function v(e,t){return _(e)||w(e,t)||y(e,t)||g()}function g(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function y(e,t){if(e){if("string"==typeof e)return b(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?b(e,t):void 0}}function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function w(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,l=[],c=!0,s=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);c=!0);}catch(e){s=!0,o=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return l}}function _(e){if(Array.isArray(e))return e}function k(e,t,r,n,o,i,a){try{var l=e[i](a),c=l.value}catch(e){return void r(e)}l.done?t(c):Promise.resolve(c).then(n,o)}function L(e){return function(){var t=this,r=arguments;return new Promise((function(n,o){var i=e.apply(t,r);function a(e){k(i,n,o,a,l,"next",e)}function l(e){k(i,n,o,a,l,"throw",e)}a(void 0)}))}}var x={name:"MerchantcollectiveAdmin",data:function(){return{isLoading:!1,dialogLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[],searchFormSetting:l["COLLECTIVE_ADMIN"],collectiveDialogVisible:!1,collectiveDialogTitle:"",collectiveDialogType:"",formcollectiveDataDialog:{},collectiveMelDialogVisible:!1,dialogMenuPeview:!1,dialogMelInfo:{},menuPeviewList:[],menuPeviewId:""}},components:{addAndModifyCollective:i["default"],collectiveMenuDialog:c["default"]},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getCollectiveList()},searchHandle:Object(a["d"])((function(e){e&&"search"===e&&(this.currentPage=1,this.initLoad())}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.currentPage=1,this.initLoad()},getCollectiveList:function(){var e=this;return L(u().mark((function t(){var r,n,o,i;return u().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(a["Z"])(e.$apis.apiBackgroundFoodCollectiveListPost(d(d({},e.formatQueryParams(e.searchFormSetting)),{},{page:e.currentPage,page_size:e.pageSize})));case 3:if(r=t.sent,n=v(r,2),o=n[0],i=n[1],e.isLoading=!1,!o){t.next=11;break}return e.$message.error(o.message),t.abrupt("return");case 11:0===i.code?(e.totalCount=i.data.count,e.tableData=i.data.results):e.$message.error(i.msg);case 12:case"end":return t.stop()}}),t)})))()},formatQueryParams:function(e){var t={};for(var r in e)e[r].value&&("select_date"!==r?t[r]=e[r].value:e[r].value.length>0&&(t.start_date=e[r].value[0],t.end_date=e[r].value[1]));return t},closeTag:function(e,t){var r=this;this.$confirm("是否删除该标签",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-warn",center:!0,beforeClose:function(){var n=L(u().mark((function n(o,i,l){var c,s,f,d,p;return u().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if("confirm"!==o){n.next=20;break}if(!r.dialogLoading){n.next=3;break}return n.abrupt("return",r.$message.error("请勿重复提交！"));case 3:return r.dialogLoading=!0,i.confirmButtonLoading=!0,c={id:t.id,label_id:e.id},n.next=8,Object(a["Z"])(r.$apis.apiBackgroundFoodCollectiveDeleteLabelPost(c));case 8:if(s=n.sent,f=v(s,2),d=f[0],p=f[1],r.dialogLoading=!1,!d){n.next=16;break}return r.$message.error(d.message),n.abrupt("return");case 16:0===p.code?(l(),r.$message.success(p.msg),r.getCollectiveList()):r.$message.error(p.msg),i.confirmButtonLoading=!1,n.next=21;break;case 20:i.confirmButtonLoading||l();case 21:case"end":return n.stop()}}),n)})));function o(e,t,r){return n.apply(this,arguments)}return o}()}).then((function(e){})).catch((function(e){}))},clickCollectiveDel:function(e){var t=this;this.$confirm("是否删除该数据",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-warn",center:!0,beforeClose:function(){var r=L(u().mark((function r(n,o,i){var l,c,s,f;return u().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if("confirm"!==n){r.next=19;break}if(!t.dialogLoading){r.next=3;break}return r.abrupt("return",t.$message.error("请勿重复提交！"));case 3:return t.dialogLoading=!0,o.confirmButtonLoading=!0,r.next=7,Object(a["Z"])(t.$apis.apiBackgroundFoodCollectiveDeletePost({ids:[e.id]}));case 7:if(l=r.sent,c=v(l,2),s=c[0],f=c[1],t.dialogLoading=!1,!s){r.next=15;break}return t.$message.error(s.message),r.abrupt("return");case 15:0===f.code?(i(),t.$message.success(f.msg),t.getCollectiveList()):t.$message.error(f.msg),o.confirmButtonLoading=!1,r.next=20;break;case 19:o.confirmButtonLoading||i();case 20:case"end":return r.stop()}}),r)})));function n(e,t,n){return r.apply(this,arguments)}return n}()}).then((function(e){})).catch((function(e){}))},handleSizeChange:function(e){this.pageSize=e,this.getFoodDietGroupList()},handleCurrentChange:function(e){this.currentPage=e,this.getFoodDietGroupList()},addAndEditCollective:function(e,t){this.collectiveDialogType=e,this.collectiveDialogVisible=!0,"add"===e?this.collectiveDialogTitle="新增集体":(this.formcollectiveDataDialog=t,this.collectiveDialogTitle="修改集体")},clickCollectiveMelDialog:function(e){this.dialogMelInfo=e,this.collectiveMelDialogVisible=!0},clickMenuPeviewDetermine:function(){var e=this;if(!this.menuPeviewId)return this.$message.error("请选择菜谱");var t=this.menuPeviewList.find((function(t){return t.id===Number(e.menuPeviewId)}));this.$router.push({name:"week"===t.key?"MerchantAddWeekRecipesSeparate":"MerchantAddMonthRecipesSeparate",query:{id:t.id,menu_type:t.key}})},clickMenuPeview:function(e){if(!e.monthly_menu.length&&!e.weekly_menu.length)return this.$message.error("请新增菜谱");this.menuPeviewList=[],this.menuPeviewId="",e.monthly_menu&&e.monthly_menu.length&&e.monthly_menu.forEach((function(e){e.menu_name="月菜谱-".concat(e.name),e.key="month"})),e.weekly_menu&&e.weekly_menu.length&&e.weekly_menu.forEach((function(e){e.menu_name="周菜谱-".concat(e.name),e.key="week"})),this.menuPeviewList=this.menuPeviewList.concat(e.weekly_menu,e.monthly_menu),this.dialogMenuPeview=!0}}},D=x,C=(r("3f77"),r("2877")),M=Object(C["a"])(D,n,o,!1,null,"6ab31034",null);t["default"]=M.exports},eeca:function(e,t,r){},f49a:function(e,t,r){}}]);