(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-set-meal-admin-SetMealAdmin"],{"35e6":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"set-meal-admin container-wrapper"},[e("refresh-tool",{on:{refreshPage:t.refreshHandle}}),e("search-form",{ref:"searchRef",attrs:{"form-setting":t.searchFormSetting,"label-width":"105px",autoSearch:!1},on:{search:t.searchHandle}}),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("数据列表")]),e("div",{staticClass:"align-r"},[e("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_food.set_meal.add"],expression:"['background_food.set_meal.add']"}],attrs:{color:"origin",type:"add"},on:{click:function(e){return t.addOrEditSetMeal("add")}}},[t._v(" 新建套餐 ")]),e("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_food.set_meal_category.list"],expression:"['background_food.set_meal_category.list']"}],attrs:{color:"plain",type:"menu"},on:{click:t.goToSetMealClassify}},[t._v("套餐分类")])],1)]),e("div",{staticClass:"table-content"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},[e("el-table-column",{key:"index",attrs:{type:"index",label:"序号",align:"center"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("div",{domProps:{textContent:t._s((t.currentPage-1)*t.pageSize+1+r.$index)}})]}}])}),e("el-table-column",{attrs:{prop:"name",label:"套餐名称",align:"center"}}),e("el-table-column",{attrs:{prop:"category_name",label:"套餐分类",align:"center"}}),e("el-table-column",{attrs:{prop:"",label:"套餐价格",align:"center"},scopedSlots:t._u([{key:"default",fn:function(r){return["fixed"===r.row.price_type?e("div",[t._v(" "+t._s(t._f("formatMoney")(r.row.fixed_price))+" ")]):"total"===r.row.price_type?e("div",[t._v("菜品价格合计")]):t._e()]}}])}),e("el-table-column",{attrs:{prop:"operator_name",label:"创建人",align:"center"}}),e("el-table-column",{attrs:{fixed:"right",label:"操作",width:"180",align:"center"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_food.set_meal.modify"],expression:"['background_food.set_meal.modify']"}],staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.addOrEditSetMeal("modify",r.row)}}},[t._v("编辑")]),e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_food.set_meal.delete"],expression:"['background_food.set_meal.delete']"}],staticClass:"ps-warn",attrs:{type:"text",size:"small"},on:{click:function(e){return t.clickDelete(r.row)}}},[t._v(" 删除 ")])]}}])})],1)],1),e("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[e("el-pagination",{staticClass:"ps-text",attrs:{"current-page":t.currentPage,"page-sizes":[10,20,50,100,500],"page-size":t.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:t.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)])],1)},a=[],o=r("ed08");function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},c=o.iterator||"@@iterator",l=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function p(t,e,r,n){var o=e&&e.prototype instanceof b?e:b,i=Object.create(o.prototype),s=new $(n||[]);return a(i,"_invoke",{value:P(t,r,s)}),i}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=p;var d="suspendedStart",g="suspendedYield",m="executing",y="completed",v={};function b(){}function w(){}function x(){}var _={};f(_,c,(function(){return this}));var S=Object.getPrototypeOf,L=S&&S(S(z([])));L&&L!==r&&n.call(L,c)&&(_=L);var O=x.prototype=b.prototype=Object.create(_);function k(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function j(t,e){function r(a,o,s,c){var l=h(t[a],t,o);if("throw"!==l.type){var u=l.arg,f=u.value;return f&&"object"==i(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,s,c)}),(function(t){r("throw",t,s,c)})):e.resolve(f).then((function(t){u.value=t,s(u)}),(function(t){return r("throw",t,s,c)}))}c(l.arg)}var o;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return o=o?o.then(a,a):a()}})}function P(e,r,n){var a=d;return function(o,i){if(a===m)throw Error("Generator is already running");if(a===y){if("throw"===o)throw i;return{value:t,done:!0}}for(n.method=o,n.arg=i;;){var s=n.delegate;if(s){var c=C(s,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===d)throw a=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=m;var l=h(e,r,n);if("normal"===l.type){if(a=n.done?y:g,l.arg===v)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(a=y,n.method="throw",n.arg=l.arg)}}}function C(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,C(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var o=h(a,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,v;var i=o.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function M(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function $(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function z(e){if(e||""===e){var r=e[c];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,o=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(i(e)+" is not iterable")}return w.prototype=x,a(O,"constructor",{value:x,configurable:!0}),a(x,"constructor",{value:w,configurable:!0}),w.displayName=f(x,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,x):(t.__proto__=x,f(t,u,"GeneratorFunction")),t.prototype=Object.create(O),t},e.awrap=function(t){return{__await:t}},k(j.prototype),f(j.prototype,l,(function(){return this})),e.AsyncIterator=j,e.async=function(t,r,n,a,o){void 0===o&&(o=Promise);var i=new j(p(t,r,n,a),o);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},k(O),f(O,u,"Generator"),f(O,c,(function(){return this})),f(O,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=z,$.prototype={constructor:$,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(M),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return s.type="throw",s.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],s=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var c=n.call(i,"catchLoc"),l=n.call(i,"finallyLoc");if(c&&l){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),M(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;M(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:z(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function c(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function l(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?c(Object(r),!0).forEach((function(e){u(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function u(t,e,r){return(e=f(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function f(t){var e=p(t,"string");return"symbol"==i(e)?e:e+""}function p(t,e){if("object"!=i(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function h(t,e){return v(t)||y(t,e)||g(t,e)||d()}function d(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function g(t,e){if(t){if("string"==typeof t)return m(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?m(t,e):void 0}}function m(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function y(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,o,i,s=[],c=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(s.push(n.value),s.length!==e);c=!0);}catch(t){l=!0,a=t}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(l)throw a}}return s}}function v(t){if(Array.isArray(t))return t}function b(t,e,r,n,a,o,i){try{var s=t[o](i),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,a)}function w(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var o=t.apply(e,r);function i(t){b(o,n,a,i,s,"next",t)}function s(t){b(o,n,a,i,s,"throw",t)}i(void 0)}))}}var x={name:"SetMealAdmin",data:function(){return{isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[],searchFormSetting:{name:{type:"input",label:"套餐名称",value:"",placeholder:"请输入套餐名称",maxlength:10},category:{type:"select",label:"套餐分类",listNameKey:"name",listValueKey:"id",value:[],placeholder:"请选择套餐分类",multiple:!0,collapseTags:!0,dataList:[]}}}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getSetMealList(),this.getSetMealCategoryList()},searchHandle:Object(o["d"])((function(t){t&&"search"===t&&(this.currentPage=1,this.getSetMealList())}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.currentPage=1,this.initLoad()},getSetMealList:function(){var t=this;return w(s().mark((function e(){var r,n,a,i;return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(o["Z"])(t.$apis.apiBackgroundFoodSetMealListPost(l(l({},t.formatQueryParams(t.searchFormSetting)),{},{page:t.currentPage,page_size:t.pageSize})));case 3:if(r=e.sent,n=h(r,2),a=n[0],i=n[1],t.isLoading=!1,!a){e.next=11;break}return t.$message.error(a.message),e.abrupt("return");case 11:0===i.code?(t.totalCount=i.data.count,t.tableData=i.data.results):t.$message.error(i.msg);case 12:case"end":return e.stop()}}),e)})))()},getSetMealCategoryList:function(){var t=this;return w(s().mark((function e(){var r,n,a,i;return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(o["Z"])(t.$apis.apiBackgroundFoodSetMealCategoryListPost({page:1,page_size:9999}));case 3:if(r=e.sent,n=h(r,2),a=n[0],i=n[1],t.isLoading=!1,!a){e.next=11;break}return t.$message.error(a.message),e.abrupt("return");case 11:0===i.code?t.searchFormSetting.category.dataList=i.data.results:t.$message.error(i.msg);case 12:case"end":return e.stop()}}),e)})))()},clickDelete:function(t){var e=this,r=["是否删除当前套餐？","删除后，已添加此套餐的菜谱会同步删除，但不影响用户已点的套餐"],n=[],a=this.$createElement;for(var i in r)n.push(a("p",null,r[i]));this.$confirm("提示",{title:"提示",message:a("div",null,n),confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-warn",center:!0,beforeClose:function(){var r=w(s().mark((function r(n,a,i){var c,l,u,f;return s().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if("confirm"!==n){r.next=18;break}return a.confirmButtonLoading=!0,e.isLoading=!0,r.next=5,Object(o["Z"])(e.$apis.apiBackgroundFoodSetMealDeletePost({ids:[t.id]}));case 5:if(c=r.sent,l=h(c,2),u=l[0],f=l[1],e.isLoading=!1,a.confirmButtonLoading=!1,i(),!u){r.next=15;break}return e.$message.error(u.message),r.abrupt("return");case 15:0===f.code?(e.getSetMealList(),e.$message.success(f.msg)):e.$message.error(f.msg),r.next=19;break;case 18:a.confirmButtonLoading||i();case 19:case"end":return r.stop()}}),r)})));function n(t,e,n){return r.apply(this,arguments)}return n}()}).then((function(t){})).catch((function(t){}))},formatQueryParams:function(t){var e={};for(var r in t)""!==t[r].value&&null!==t[r].value&&0!==t[r].value.length&&("select_date"!==r?e[r]=t[r].value:t[r].value.length>0&&(e.start_date=t[r].value[0],e.end_date=t[r].value[1]));return e},addOrEditSetMeal:function(t,e){this.$router.push({name:"MerchantAddEditSetMeal",query:{type:t,data:this.$encodeQuery(e)}})},goToSetMealClassify:function(){this.$router.push({name:"MerchantSetMealClassify"})},handleSizeChange:function(t){this.pageSize=t,this.getSetMealList()},handleCurrentChange:function(t){this.currentPage=t,this.getSetMealList()}}},_=x,S=r("2877"),L=Object(S["a"])(_,n,a,!1,null,"f4fa5faa",null);e["default"]=L.exports}}]);