(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-meal-report-HeadCount","view-merchant-meal-management-meal-report-component-declaration"],{"0aa5":function(t,e,r){},"76e3":function(t,e,r){"use strict";r("0aa5")},c741:function(t,e,r){"use strict";r.r(e),r.d(e,"RECENTSEVENTDAY",(function(){return a})),r.d(e,"PICKEROPTIONS",(function(){return o})),r.d(e,"MEALDETAILS_TABLE_CLOUMN",(function(){return i})),r.d(e,"MEALDETAILS_TABLE_DATE",(function(){return l})),r.d(e,"HEADCOUNT_TABLE_CLOUMN",(function(){return c}));var n=r("5a0c"),a=[n().subtract(7,"day").format("YYYY-MM-DD"),n().format("YYYY-MM-DD")],o={shortcuts:[{text:"最近一周",onClick:function(t){var e=new Date,r=new Date;r.setTime(r.getTime()-6048e5),t.$emit("pick",[r,e])}},{text:"最近一个月",onClick:function(t){var e=new Date,r=new Date;r.setTime(r.getTime()-2592e6),t.$emit("pick",[r,e])}},{text:"最近三个月",onClick:function(t){var e=new Date,r=new Date;r.setTime(r.getTime()-7776e6),t.$emit("pick",[r,e])}}]},i=[{label:"总单号",prop:"unified_trade_no",width:"170"},{label:"订单号",prop:"trade_no",width:"190"},{label:"分组",prop:"payer_group_name"},{label:"姓名",prop:"name",width:"90"},{label:"人员编号",prop:"person_no",width:"90"},{label:"部门",prop:"payer_department_group_name"},{label:"创建时间",prop:"create_time"},{label:"报餐时间",prop:"report_date"},{label:"用餐时间",prop:"dining_time",width:"95"},{label:"报餐餐段",prop:"meal_type_alias",width:"80"},{label:"报餐方式",prop:"report_meal_type_alias",width:"80"},{label:"扣费方式",prop:"consume_type_alias",width:"80"},{label:"份数",prop:"count",width:"50"},{label:"订单金额",prop:"pay_fee",width:"80"},{label:"取餐状态",prop:"take_meal_status_alias",width:"80"},{label:"报餐消费点",prop:"consumption_name",width:"100"}],l=[{total_number:"142",order_number:"111",grouping:"",username:"",user_number:"",department:"",change_time:"",dinner_time:"",meal_type:"",deduction_type:"",total_numcountber:"",order_money:"",get_meal_state:"",consumption:""},{total_number:"142",order_number:"111",grouping:"",username:"",user_number:"",department:"",change_time:"",dinner_time:"",meal_type:"",deduction_type:"",total_numcountber:"",order_money:"",get_meal_state:"",consumption:""},{total_number:"22",order_number:"111",grouping:"",username:"",user_number:"",department:"",change_time:"",dinner_time:"",meal_type:"",deduction_type:"",total_numcountber:"",order_money:"",get_meal_state:"",consumption:""}],c=[{label:"时间",prop:"report_date"},{label:"人员编号",prop:"person_no"},{label:"姓名",prop:"name"},{label:"分组",prop:"card_user_groups"},{label:"部门",prop:"department_group_name"}]},ec15:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"container-wrapper"},[e("refresh-tool",{on:{refreshPage:t.refreshHandle}}),e("search-form",{ref:"searchRef",attrs:{"form-setting":t.searchFormSetting,autoSearch:!1},on:{search:t.searchHandle}}),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("数据列表")]),e("div",{staticClass:"align-r"},[e("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_order.order_report_meal.not_report_list_export"],expression:"['background_order.order_report_meal.not_report_list_export']"}],attrs:{color:"origin",type:"export"},on:{click:t.handleExport}},[t._v("导出Excel")])],1)]),e("div",{staticClass:"table-content"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:"",border:"","header-row-class-name":"ps-table-header-row"}},t._l(t.table_column,(function(r,n){return e("el-table-column",{key:n,attrs:{prop:r.prop,label:r.label,width:r.width,align:"center"},scopedSlots:t._u(["card_user_groups"===r.prop?{key:"default",fn:function(r){var n=r.row;return[e("div",t._l(n.card_user_groups,(function(r,a){return e("span",{key:a},[t._v(t._s(r)+t._s(a!==n.card_user_groups.length-1?"、":""))])})),0)]}}:null],null,!0)})})),1)],1),e("div",{staticClass:"block ps-pagination",staticStyle:{display:"flex","justify-content":"space-between","align-items":"center","padding-top":"20px"}},[e("div",{staticStyle:{display:"flex","flex-direction":"column","align-items":"flex-start","justify-content":"flex-end",width:"250px"}},[e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"bottom-statistics",attrs:{"element-loading-custom-class":"el-loading-wrapp","element-loading-spinner":"loading","element-loading-text":t.elementLoadingText}},[t._v("未报餐人数： "+t._s(t.count))]),e("div",{staticClass:"bottom-statistics"},[t._v("说明：当天无人报餐则不显示")])]),e("el-pagination",{staticClass:"ps-text",attrs:{"current-page":t.currentPage,"page-sizes":[10,20,50,100,500],"page-size":t.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:t.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)])],1)},a=[],o=r("ed08"),i=r("c741"),l=r("f63a"),c=r("5a0c"),u=r.n(c);function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function p(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */p=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",l=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function h(t,e,r,n){var o=e&&e.prototype instanceof _?e:_,i=Object.create(o.prototype),l=new D(n||[]);return a(i,"_invoke",{value:P(t,r,l)}),i}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var f="suspendedStart",m="suspendedYield",g="executing",v="completed",y={};function _(){}function b(){}function w(){}var L={};u(L,i,(function(){return this}));var x=Object.getPrototypeOf,O=x&&x(x(N([])));O&&O!==r&&n.call(O,i)&&(L=O);var E=w.prototype=_.prototype=Object.create(L);function S(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function C(t,e){function r(a,o,i,l){var c=d(t[a],t,o);if("throw"!==c.type){var u=c.arg,p=u.value;return p&&"object"==s(p)&&n.call(p,"__await")?e.resolve(p.__await).then((function(t){r("next",t,i,l)}),(function(t){r("throw",t,i,l)})):e.resolve(p).then((function(t){u.value=t,i(u)}),(function(t){return r("throw",t,i,l)}))}l(c.arg)}var o;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return o=o?o.then(a,a):a()}})}function P(e,r,n){var a=f;return function(o,i){if(a===g)throw Error("Generator is already running");if(a===v){if("throw"===o)throw i;return{value:t,done:!0}}for(n.method=o,n.arg=i;;){var l=n.delegate;if(l){var c=j(l,n);if(c){if(c===y)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===f)throw a=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=g;var u=d(e,r,n);if("normal"===u.type){if(a=n.done?v:m,u.arg===y)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(a=v,n.method="throw",n.arg=u.arg)}}}function j(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,j(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var o=d(a,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,y;var i=o.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function T(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function D(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function N(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,o=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(s(e)+" is not iterable")}return b.prototype=w,a(E,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:b,configurable:!0}),b.displayName=u(w,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,u(t,c,"GeneratorFunction")),t.prototype=Object.create(E),t},e.awrap=function(t){return{__await:t}},S(C.prototype),u(C.prototype,l,(function(){return this})),e.AsyncIterator=C,e.async=function(t,r,n,a,o){void 0===o&&(o=Promise);var i=new C(h(t,r,n,a),o);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},S(E),u(E,c,"Generator"),u(E,i,(function(){return this})),u(E,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=N,D.prototype={constructor:D,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(T),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return l.type="throw",l.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],l=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var c=n.call(i,"catchLoc"),u=n.call(i,"finallyLoc");if(c&&u){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,y):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),T(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;T(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:N(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),y}},e}function h(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function d(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?h(Object(r),!0).forEach((function(e){f(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function f(t,e,r){return(e=m(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function m(t){var e=g(t,"string");return"symbol"==s(e)?e:e+""}function g(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function v(t,e,r,n,a,o,i){try{var l=t[o](i),c=l.value}catch(t){return void r(t)}l.done?e(c):Promise.resolve(c).then(n,a)}function y(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var o=t.apply(e,r);function i(t){v(o,n,a,i,l,"next",t)}function l(t){v(o,n,a,i,l,"throw",t)}i(void 0)}))}}var _={mixins:[l["a"]],data:function(){var t=Object(o["y"])(7);return{searchFormSetting:{select_date:{clearable:!1,pickerOptions:{},label:"报餐时间",type:"daterange",value:[t[0],t[1]]},name:{type:"input",value:"",label:"姓名",placeholder:"请输入姓名"},person_no:{type:"input",value:"",label:"人员编号",placeholder:"请输入人员编号"},card_department_group:{type:"departmentSelect",multiple:!0,flat:!1,label:"部门",value:[],placeholder:"请选择部门",checkStrictly:!0,dataList:[],collapseTags:!0,limit:1,level:1},card_user_group_ids:{type:"groupSelect",label:"适用分组",value:[],placeholder:"请选择",multiple:!0}},tableData:[],table_column:i["HEADCOUNT_TABLE_CLOUMN"],elementLoadingText:"正在加载中...",count:0,isLoading:!1,pageSize:10,totalCount:0,currentPage:1}},created:function(){this.initLoad()},methods:{initLoad:function(){this.getHeadCountList()},formatQueryParams:function(t){var e={};for(var r in t)""!==t[r].value&&null!==t[r].value&&0!==t[r].value.length&&("select_date"!==r?e[r]=t[r].value:t[r].value.length>0&&(e.start_date=t[r].value[0],e.end_date=t[r].value[1]));return e},getHeadCountList:function(){var t=this;return y(p().mark((function e(){var r;return p().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.$apis.apiBackgroundOrderOrderReportMealNotReportListPost(d(d({},t.formatQueryParams(t.searchFormSetting)),{},{page:t.currentPage,page_size:t.pageSize}));case 3:r=e.sent,t.isLoading=!1,0===r.code?(t.tableData=r.data.results,t.totalCount=r.data.count,t.count=r.data.count):t.$message.error(r.msg);case 6:case"end":return e.stop()}}),e)})))()},searchHandle:Object(o["d"])((function(t){if(t&&"search"===t){this.currentPage=1;var e=u()(this.searchFormSetting.select_date.value[0]),r=u()(this.searchFormSetting.select_date.value[1]).format("YYYY-MM-DD");if(Math.abs(e.diff(r,"day"))>7)return this.$message.error("所选择的时间超过七天，请缩小时间范围后重试");this.getHeadCountList()}}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.currentPage=1,this.initLoad()},handleSizeChange:function(t){this.pageSize=t,this.getHeadCountList()},handleCurrentChange:function(t){this.currentPage=t,this.getHeadCountList()},handleExport:function(){var t={url:"apiBackgroundOrderOrderReportMealNotReportListExportPost",params:d(d({},this.formatQueryParams(this.searchFormSetting)),{},{page:this.currentPage,page_size:this.pageSize})};this.exportHandle(t)}}},b=_,w=(r("76e3"),r("2877")),L=Object(w["a"])(b,n,a,!1,null,"f6b07aa4",null);e["default"]=L.exports}}]);