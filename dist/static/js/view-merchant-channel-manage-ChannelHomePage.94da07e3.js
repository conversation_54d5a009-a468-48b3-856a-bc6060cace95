(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-channel-manage-ChannelHomePage","view-merchant-channel-manage-components-DataAnalysis","view-merchant-channel-manage-components-MapDistribution","view-merchant-channel-manage-components-Ranking"],{1532:function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t._self._c;return e("div",[e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isBusinessLoading,expression:"isBusinessLoading"}],staticClass:"line-trend"},[e("div",{staticClass:"top flex"},[e("div",{staticClass:"title"},[t._v("数据分析")]),e("div",{staticClass:"m-l-10"},[e("el-select",{attrs:{placeholder:"请选择",multiple:"",clearable:"","collapse-tags":"","multiple-limit":t.limitNum},on:{change:t.orgsChange},model:{value:t.orgsId,callback:function(e){t.orgsId=e},expression:"orgsId"}},t._l(t.orgsOptions,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),e("div",{staticClass:"m-l-10 m-t-10"},[t._v(t._s(t.chooseNum)+"/10")]),e("div",{staticClass:"m-l-10 ps-text-gray"},[t._v("默认展示所有项目汇总数据，可选择其中至多10个进行比较")])]),e("div",{staticClass:"nav-cen flex"},t._l(t.businessSummery,(function(n){return e("div",{key:n.key,staticClass:"item",class:t.selectTrend.key===n.key?"nav-active":"",on:{click:function(e){return t.changetrendHandle(n)}}},[t._v(" "+t._s(n.label)+" ")])})),0),e("div",{directives:[{name:"show",rawName:"v-show",value:!t.isShowNoMore,expression:"!isShowNoMore"}],ref:"line_chart",attrs:{id:"line_chart"}}),e("div",{directives:[{name:"show",rawName:"v-show",value:t.isShowNoMore,expression:"isShowNoMore"}],staticClass:"flex flex-center empty-text"},[t._v(" 暂无更多 ")])])])},a=[],i=n("ed08"),o=n("2232"),s=n("5a0c");function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,a=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function h(t,e,n,r){var i=e&&e.prototype instanceof b?e:b,o=Object.create(i.prototype),s=new E(r||[]);return a(o,"_invoke",{value:j(t,n,s)}),o}function f(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var d="suspendedStart",p="suspendedYield",v="executing",g="completed",y={};function b(){}function _(){}function w(){}var C={};u(C,o,(function(){return this}));var O=Object.getPrototypeOf,k=O&&O(O(M([])));k&&k!==n&&r.call(k,o)&&(C=k);var x=w.prototype=b.prototype=Object.create(C);function L(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function D(t,e){function n(a,i,o,s){var c=f(t[a],t,i);if("throw"!==c.type){var l=c.arg,u=l.value;return u&&"object"==m(u)&&r.call(u,"__await")?e.resolve(u.__await).then((function(t){n("next",t,o,s)}),(function(t){n("throw",t,o,s)})):e.resolve(u).then((function(t){l.value=t,o(l)}),(function(t){return n("throw",t,o,s)}))}s(c.arg)}var i;a(this,"_invoke",{value:function(t,r){function a(){return new e((function(e,a){n(t,r,e,a)}))}return i=i?i.then(a,a):a()}})}function j(e,n,r){var a=d;return function(i,o){if(a===v)throw Error("Generator is already running");if(a===g){if("throw"===i)throw o;return{value:t,done:!0}}for(r.method=i,r.arg=o;;){var s=r.delegate;if(s){var c=T(s,r);if(c){if(c===y)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===d)throw a=g,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=v;var l=f(e,n,r);if("normal"===l.type){if(a=r.done?g:p,l.arg===y)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(a=g,r.method="throw",r.arg=l.arg)}}}function T(e,n){var r=n.method,a=e.iterator[r];if(a===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,T(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),y;var i=f(a,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,y;var o=i.arg;return o?o.done?(n[e.resultName]=o.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,y):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,y)}function I(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function S(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(I,this),this.reset(!0)}function M(e){if(e||""===e){var n=e[o];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,i=function n(){for(;++a<e.length;)if(r.call(e,a))return n.value=e[a],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw new TypeError(m(e)+" is not iterable")}return _.prototype=w,a(x,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:_,configurable:!0}),_.displayName=u(w,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===_||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,u(t,l,"GeneratorFunction")),t.prototype=Object.create(x),t},e.awrap=function(t){return{__await:t}},L(D.prototype),u(D.prototype,s,(function(){return this})),e.AsyncIterator=D,e.async=function(t,n,r,a,i){void 0===i&&(i=Promise);var o=new D(h(t,n,r,a),i);return e.isGeneratorFunction(n)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},L(x),u(x,l,"Generator"),u(x,o,(function(){return this})),u(x,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=M,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(S),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function a(r,a){return s.type="throw",s.arg=e,n.next=r,a&&(n.method="next",n.arg=t),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var c=r.call(o,"catchLoc"),l=r.call(o,"finallyLoc");if(c&&l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),S(n),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var a=r.arg;S(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:M(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),y}},e}function l(t,e){return p(t)||d(t,e)||h(t,e)||u()}function u(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function h(t,e){if(t){if("string"==typeof t)return f(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?f(t,e):void 0}}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function d(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,a,i,o,s=[],c=!0,l=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(s.push(r.value),s.length!==e);c=!0);}catch(t){l=!0,a=t}finally{try{if(!c&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(l)throw a}}return s}}function p(t){if(Array.isArray(t))return t}function v(t,e,n,r,a,i,o){try{var s=t[i](o),c=s.value}catch(t){return void n(t)}s.done?e(c):Promise.resolve(c).then(r,a)}function g(t){return function(){var e=this,n=arguments;return new Promise((function(r,a){var i=t.apply(e,n);function o(t){v(i,r,a,o,s,"next",t)}function s(t){v(i,r,a,o,s,"throw",t)}o(void 0)}))}}function m(t){return m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},m(t)}var y={name:"DataAnalysis",props:{chartData:{type:Object,default:function(){}},channelId:{type:Array,default:function(){return[]}},timeValue:{type:Object,default:function(){return{}}},channelIdFormat:{type:Array,default:function(){return[]}}},data:function(){return{isBusinessLoading:!1,businessSummery:o["TAB_DATA_LIST"],selectTrend:{},lineChart:null,LineChartObj:{},orgsOptions:[],orgsId:"",chooseNum:0,chartXLineList:[],intervalDays:0,isShowNoMore:!1,limitNum:10}},created:function(){this.selectTrend=this.businessSummery[0],this.initDic()},mounted:function(){window.addEventListener("resize",this.resizeChartHandle)},watch:{channelId:function(t){},channelIdFormat:function(t){this.initDic()},timeValue:{deep:!0,handler:function(t){this.setDataTimeList(t)}},chartData:{deep:!0,handler:function(t){t&&"object"===m(t)&&Object.keys(t).length>0?(this.isShowNoMore=!1,this.initTurnoverTrendLine(Object(i["f"])(t))):(this.clearChart(),this.isShowNoMore=!0)}}},methods:{initTurnoverTrendLine:function(t){var e=this,n=Object(i["f"])(o["TREND_SETTING"]);n.xAxis.data=this.chartXLineList,n.tooltip.formatter='<div style="padding:5px;font-size:16px;font-weight: 540;"><span style="margin-right:10px;">{a0}</span>{b0}<br /><span style=" color: #666;display:inline-block;margin: 5px 20px 0 0;">'.concat(this.selectTrend.label,"</span>").concat("交易金额"===this.selectTrend.label?"￥":"","{c0}</div>"),Reflect.has(t,"analysis_data")&&Array.isArray(t.analysis_data)&&t.analysis_data.length>0?(this.isShowNoMore=!1,this.setChartYData(n,t),this.lineChart||(this.lineChart=this.$echarts.init(this.$refs.line_chart)),this.lineChart&&(this.lineChart.clear(),this.$nextTick((function(){e.lineChart.setOption(n),e.resizeChartHandle()})))):(this.clearChart(),this.isShowNoMore=!0)},setChartYData:function(t,e){var n=this,r=e.analysis_data||[],a=[],o=[],s=this.timeValue.type;r.forEach((function(e){var r=Object(i["f"])(t.series[0]);r.name=e.org_name||"",o.push(e.org_name);var c=e[n.selectTrend.key]||[],l=[],u="quarter"!==s?Object(i["f"])(n.chartXLineList):["1","2","3","4"];u.forEach((function(t){var e=t,r=0,a=c.find((function(t){return Reflect.has(t,e)}));a&&(r=a[e],"real_fee_list"===n.selectTrend.key&&(r=Object(i["i"])(r))),l.push(r)})),r.data=Object(i["f"])(l),a.push(r)})),t.legend.data=Object(i["f"])(o),t.series=Object(i["f"])(a)},changetrendHandle:function(t){this.selectTrend=t;var e=Object(i["f"])(this.chartData);this.initTurnoverTrendLine(e)},resizeChartHandle:Object(i["d"])((function(){this.lineChart&&this.lineChart.resize()}),300),getDiffDay:function(t,e){return s(t).diff(s(e),"days")},setDataTimeList:function(t){if(null!=t&&"object"===m(t)&&Reflect.has(t,"type")){var e,n,r=[],a=t.type||"",o=t.value||[];switch(a){case"day":e=o[0]?o[0]:"",n=o[1]?o[1]:"";var c=s(n).diff(s(e),"days")+1;if(this.intervalDays=c,c>0)for(var l=0;l<c;l++){var u=s(e).add(l,"day").format("YYYY-MM-DD");r.push(u)}else r=[e];break;case"month":e=o[0]?o[0].slice(0,7):"",n=o[1]?o[1].slice(0,7):"";var h=s(n).diff(s(e),"months")+1;if(this.intervalDays=h,h>0)for(var f=0;f<h;f++){var d=s(e).add(f,"month").format("YYYY-MM");r.push(d)}else r=[e];break;case"quarter":r=["第一季度","第二季度","第三季度","第四季度"];break;default:break}this.chartXLineList=Object(i["f"])(r)}},initDic:function(){var t=this;return g(c().mark((function e(){var n,r,a,o,s,u;return c().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n={},Array.isArray(t.channelIdFormat)&&t.channelIdFormat.length>0?n.channel_ids=Object(i["f"])(t.channelIdFormat):delete n.channel_ids,e.next=4,Object(i["Z"])(t.$apis.apiBackgroundChannelDataStatisticsGetOrganizationListPost(n));case 4:if(r=e.sent,a=l(r,2),o=a[0],s=a[1],!o){e.next=10;break}return e.abrupt("return");case 10:0===s.code&&(u=s.data||[],Array.isArray(u)&&u.length>0?t.orgsOptions=Object(i["f"])(u):t.orgsOptions=[]);case 11:case"end":return e.stop()}}),e)})))()},orgsChange:function(t){this.chooseNum=t?t.length:0,this.$emit("orgsChooseChange",t)},clearChart:function(){null!=this.lineChart&&""!==this.lineChart&&void 0!==this.lineChart&&(this.lineChart.dispose(),this.lineChart=null)}}},b=y,_=(n("b3c6"),n("2877")),w=Object(_["a"])(b,r,a,!1,null,"20af4070",null);e["default"]=w.exports},"2cb4":function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t._self._c;return e("div",{staticClass:"ranking-container flex"},[e("div",{staticClass:"food-top ranking-item m-t-20 m-b-20"},[e("div",{staticClass:"top flex"},[e("div",{staticClass:"title"},[t._v("登录数排行TOP20")]),e("div",{staticClass:"m-l-10"},[e("el-select",{staticClass:"w-150",attrs:{placeholder:"请选择"},on:{change:function(e){t.changeOrgsChoose("login",e)}},model:{value:t.orgsChooseIdLogin,callback:function(e){t.orgsChooseIdLogin=e},expression:"orgsChooseIdLogin"}},t._l(t.orgsChooseList,(function(t){return e("el-option",{key:t.value,attrs:{label:t.name,value:t.value}})})),1)],1)]),e("div",{staticClass:"m-t-10",staticStyle:{width:"200px"}}),e("div",{staticClass:"ranking-list"},[t._l(t.loginRankingData,(function(n,r){return e("div",{key:r,staticClass:"top-item flex"},[e("div",{staticClass:"left flex"},[e("div",{staticClass:"count",class:t.foodTOPThreeClass(r+1)},[t._v(t._s(r+1))]),e("div",{staticClass:"food"},[t._v(t._s("1"==t.orgsChooseIdLogin?n.org_name:n.channel_name))])]),e("div",{staticClass:"right"},[t._v(t._s(t._f("formatPriceTo3")(n.total_login_count))+"人")])])})),t.loginRankingData.length?t._e():e("div",{staticClass:"flex flex-center empty-text"},[t._v(" 暂无更多 ")])],2)]),e("div",{staticClass:"food-top ranking-item m-l-20 m-t-20 m-b-20"},[e("div",{staticClass:"top flex"},[e("div",{staticClass:"title"},[t._v("注册数排行TOP20")]),e("div",{staticClass:"m-l-10"},[e("el-select",{staticClass:"w-150",attrs:{placeholder:"请选择"},on:{change:function(e){t.changeOrgsChoose("register",e)}},model:{value:t.orgsChooseIdRegister,callback:function(e){t.orgsChooseIdRegister=e},expression:"orgsChooseIdRegister"}},t._l(t.orgsChooseList,(function(t){return e("el-option",{key:t.value,attrs:{label:t.name,value:t.value}})})),1)],1)]),e("div",{staticClass:"ranking-list"},[t._l(t.registerRankingData,(function(n,r){return e("div",{key:r,staticClass:"top-item flex"},[e("div",{staticClass:"left flex"},[e("div",{staticClass:"count",class:t.foodTOPThreeClass(r+1)},[t._v(t._s(r+1))]),e("div",{staticClass:"food"},[t._v(t._s("1"==t.orgsChooseIdRegister?n.org_name:n.channel_name))])]),e("div",{staticClass:"right"},[t._v(t._s(t._f("formatPriceTo3")(n.total_register_count))+"人")])])})),t.registerRankingData.length?t._e():e("div",{staticClass:"flex flex-center empty-text"},[t._v(" 暂无更多 ")])],2)]),e("div",{staticClass:"food-top ranking-item m-l-20 m-t-20 m-b-20"},[e("div",{staticClass:"top flex"},[e("div",{staticClass:"title"},[t._v("交易金额排行TOP20")]),e("div",{staticClass:"m-l-10"},[e("el-select",{staticClass:"w-150",attrs:{placeholder:"请选择"},on:{change:function(e){t.changeOrgsChoose("transaction",e)}},model:{value:t.orgsChooseIdTransaction,callback:function(e){t.orgsChooseIdTransaction=e},expression:"orgsChooseIdTransaction"}},t._l(t.orgsChooseList,(function(t){return e("el-option",{key:t.value,attrs:{label:t.name,value:t.value}})})),1)],1)]),e("div",{staticClass:"ranking-list"},[t._l(t.totalFeeRankingData,(function(n,r){return e("div",{key:r,staticClass:"top-item flex"},[e("div",{staticClass:"left flex"},[e("div",{staticClass:"count",class:t.foodTOPThreeClass(r+1)},[t._v(t._s(r+1))]),e("div",{staticClass:"food"},[t._v(t._s("1"==t.orgsChooseIdTransaction?n.org_name:n.channel_name))])]),e("div",{staticClass:"right"},[t._v(" ￥"+t._s(t._f("formatPriceTo3")(t._f("formatMoney")(n.total_real_fee)))+" ")])])})),t.totalFeeRankingData.length?t._e():e("div",{staticClass:"flex flex-center empty-text"},[t._v(" 暂无更多 ")])],2)])])},a=[],i=n("ed08"),o=n("2232"),s={name:"Ranking",props:{chartData:{type:Object,default:function(){}},channelId:{type:Array,default:function(){return[]}},timeValue:{type:Object,default:function(){return{}}}},data:function(){return{loginRankingData:[],loginProjectRankingData:[],loginChannelRankingData:[],registerRankingData:[],registerProjectRankingData:[],registerChannelRankingData:[],totalFeeRankingData:[],totalFeeProjectRankingData:[],totalFeeChannelRankingData:[],orgsChooseList:Object(i["f"])(o["ORGS_CHOOSE_LIST"]),orgsChooseIdLogin:"1",orgsChooseIdRegister:"1",orgsChooseIdTransaction:"1"}},created:function(){},mounted:function(){window.addEventListener("resize",this.resizeChartHandle)},computed:{},watch:{chartData:{deep:!0,handler:function(t){this.initFoodSalesRanking(Object(i["f"])(t))}},channelId:function(t){},timeValue:{deep:!0,handler:function(t){}}},methods:{initFoodSalesRanking:function(t){var e=t.analysis_data||[];this.loginRankingData=Object(i["f"])(this.sortData(e,"total_login_count")),this.registerRankingData=Object(i["f"])(this.sortData(e,"total_register_count")),this.totalFeeRankingData=Object(i["f"])(this.sortData(e,"total_real_fee")),this.loginProjectRankingData=Object(i["f"])(this.loginRankingData),this.registerProjectRankingData=Object(i["f"])(this.registerRankingData),this.totalFeeProjectRankingData=Object(i["f"])(this.totalFeeRankingData);var n=this.getChannelList(e);n&&(this.loginChannelRankingData=Object(i["f"])(this.sortData(n,"total_login_count")),this.registerChannelRankingData=Object(i["f"])(this.sortData(n,"total_register_count")),this.totalFeeChannelRankingData=Object(i["f"])(this.sortData(n,"total_real_fee")))},getChannelList:function(t){var e=[],n=[];return t.forEach((function(t){n.push(t.channel_name)})),n=new Set(n),n.forEach((function(n){var r=n,a=t.filter((function(t){return t.channel_name===r}));if(a&&1===a.length)e.push(a[0]);else{for(var i=a[0],o=1;o<a.length;o++)i.total_login_count=i.total_login_count+a[o].total_login_count,i.total_register_count=i.total_register_count+a[o].total_register_count,i.total_real_fee=i.total_real_fee+a[o].total_real_fee;e.push(i)}})),e},sortData:function(t,e,n){var r=t;return r.sort((function(t,r){return"up"===n?t[e]-r[e]:r[e]-t[e]})),r},foodTOPThreeClass:function(t){return 1===t?"No1":2===t?"No2":3===t?"No3":void 0},changeOrgsChoose:function(t,e){switch(t){case"login":this.loginRankingData="1"===e?Object(i["f"])(this.loginProjectRankingData):Object(i["f"])(this.loginChannelRankingData);break;case"register":this.registerRankingData="1"===e?Object(i["f"])(this.registerProjectRankingData):Object(i["f"])(this.registerChannelRankingData);break;case"transaction":this.totalFeeRankingData="1"===e?Object(i["f"])(this.totalFeeProjectRankingData):Object(i["f"])(this.totalFeeChannelRankingData);break;default:break}}}},c=s,l=(n("65d7"),n("2877")),u=Object(l["a"])(c,r,a,!1,null,"b5b33532",null);e["default"]=u.exports},"2e7c":function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t._self._c;return e("div",[e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isBusinessLoading,expression:"isBusinessLoading"}],staticClass:"line-trend"},[t._m(0),e("div",{staticClass:"nav-cen flex"},t._l(t.businessSummery,(function(n){return e("div",{key:n.key,staticClass:"item",class:t.selectTrend.key===n.key?"nav-active":"",on:{click:function(e){return t.changetrendHandle(n)}}},[t._v(" "+t._s(n.label)+" ")])})),0),e("div",{directives:[{name:"show",rawName:"v-show",value:!t.isShowNoMore,expression:"!isShowNoMore"}],staticClass:"map_container"},[e("div",{ref:"mapbox",staticClass:"map-item",attrs:{id:"mapbox"}}),e("div",{staticClass:"map_right"},t._l(t.provinceTopList,(function(n,r){return e("div",{key:r,staticClass:"right_item"},[t._v(t._s(n.name)+"："+t._s("total_real_fee"===t.selectTrend.total?"¥"+n.value:n.value+"人"))])})),0)]),e("div",{directives:[{name:"show",rawName:"v-show",value:t.isShowNoMore,expression:"isShowNoMore"}],staticClass:"flex flex-center empty-text"},[t._v(" 暂无更多 ")])])])},a=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"top flex"},[e("div",{staticClass:"title"},[t._v("分布情况")])])}],i=n("ed08"),o=n("2232"),s=n("42de"),c=n("3eba");n("675c");var l={name:"MapDistribution",props:{chartData:{type:Object,default:function(){}},channelId:{type:Array,default:function(){return[]}},timeValue:{type:Object,default:function(){return{}}}},data:function(){return{isBusinessLoading:!1,businessSummery:o["TAB_DATA_LIST"],mapChart:null,selectTrend:{},provinceTopList:[],isShowNoMore:!1}},created:function(){},mounted:function(){this.selectTrend=this.businessSummery[0],window.addEventListener("resize",this.resizeChartHandle)},computed:{},watch:{channelId:function(t){},timeValue:{deep:!0,handler:function(t){}},chartData:{deep:!0,handler:function(t){Reflect.has(t,"map_data")&&Array.isArray(t.map_data)&&t.map_data.length>0?(this.isShowNoMore=!1,this.initMap(t.map_data)):this.isShowNoMore=!0}}},methods:{initMap:function(t){var e=this.getMapOption(t);c.registerMap("china",s),null!==this.mapChart&&this.mapChart.dispose();var n=this.$refs.mapbox;this.mapChart=c.init(n),this.mapChart.setOption(e)},getMapOption:function(t){var e=this,n=Object(i["f"])(t);t=this.setMapDataList(n);var r=this.getDataList(n);this.provinceTopList=r.length>10?Object(i["f"])(r.slice(0,10)):Object(i["f"])(r),Array.isArray(this.provinceTopList)&&this.provinceTopList.length>0&&this.provinceTopList.sort(this.compare("value",0));var a=sessionStorage.getItem("theme_color")||"#0184DF",o=e.selectTrend.label,s=e.selectTrend.total,c=this.getMaxValue(r),l={tooltip:{formatter:function(t){(isNaN(t.value)||null===t.value)&&(t.value=0);var e="total_real_fee"===s?"¥"+t.value:t.value+"人",n="".concat(o)+"</br>"+"".concat(t.name)+": ".concat(e);return n},backgroundColor:a,textStyle:{color:"#fff"}},visualMap:{show:!0,min:0,max:c,text:["高","低"],realtime:!1,calculable:!0,orient:"horizontal",inRange:{color:["white",a]}},series:[{name:"中国",type:"map",mapType:"china",zoom:1.3,left:"150",top:"50",center:[106,31.8],itemStyle:{normal:{areaColor:"#fff",borderColor:"gray",borderWidth:1,label:{show:!1,color:"#fff"},emphasis:{label:{show:!0}}}},data:r}]};return l},changetrendHandle:function(t){this.selectTrend=t;var e=this.chartData.map_data||[];this.initMap(e)},setMapDataList:function(t){return t&&0!==t.length?(t=t.map((function(t){var e=t.district,n=s.features.find((function(t){return t.properties.adcode===e}));return n&&(t.name=n.properties.name),t})),t):[]},getDataList:function(t){var e=this,n=[];return t.forEach((function(t){var r=t[e.selectTrend.total];"total_real_fee"===e.selectTrend.total&&(r=Object(i["i"])(r)),n.push({name:t.name,value:r})})),n},getMaxValue:function(t){if(!t)return 1e3;var e="value",n=t.reduce((function(t,n){return t[e]>n[e]?t:n}))[e];return n?Number(n)+1:1e3},compare:function(t,e){return function(n,r){var a=n[t],i=r[t];return 0===e?i<a?-1:i>a?1:0:1===e?i>a?-1:i<a?1:0:void 0}}}},u=l,h=(n("e5e8"),n("2877")),f=Object(h["a"])(u,r,a,!1,null,"36fb670d",null);e["default"]=f.exports},3891:function(t,e,n){},5329:function(t,e,n){"use strict";n("f263")},"65d7":function(t,e,n){"use strict";n("dd46")},a810:function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"home-page container-wrapper"},[e("div",{staticClass:"nav-header flex"},[e("div",{staticClass:"select"},[e("el-cascader",{ref:"channelMul",staticClass:"ps-select",staticStyle:{width:"80%"},attrs:{placeholder:"请选择渠道",clearable:"",options:t.channelTreeList,"show-all-levels":!1,props:t.cascaderProps,"collapse-tags":""},on:{change:t.channelIdChange},model:{value:t.channelId,callback:function(e){t.channelId=e},expression:"channelId"}})],1),e("div",{staticClass:"flex"},[e("div",{staticClass:"nav"},t._l(t.timeList,(function(n){return e("el-button",{key:n.value,class:[t.dateType===n.value?"time-active":"",""],on:{click:function(e){return t.changeTimeFn(n)}}},[t._v(" "+t._s(n.label)+" ")])})),1),e("div",{staticClass:"time"},["day"==t.dateType?e("el-date-picker",{staticClass:"ps-picker",attrs:{clearable:!1,type:"daterange",align:"right","range-separator":"⇀","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd","unlink-panels":"","picker-options":t.pickerOptions,"popper-class":"ps-poper-picker"},on:{change:t.timeChange},model:{value:t.dateValue,callback:function(e){t.dateValue=e},expression:"dateValue"}}):t._e(),"month"==t.dateType?e("el-date-picker",{attrs:{type:"monthrange",placeholder:"选择年","start-placeholder":"开始月份","range-separator":"至","end-placeholder":"结束月份","value-format":"yyyy-MM","unlink-panels":"","picker-options":t.pickerMonthOptions},on:{change:t.timeChange},model:{value:t.monthValue,callback:function(e){t.monthValue=e},expression:"monthValue"}}):t._e(),"quarter"==t.dateType?e("el-select",{attrs:{placeholder:"请选择年份"},on:{change:t.timeChange},model:{value:t.yearValue,callback:function(e){t.yearValue=e},expression:"yearValue"}},t._l(t.yearList,(function(t){return e("el-option",{key:t.value,attrs:{label:t.name,value:t.value}})})),1):t._e()],1)])]),e("div",{staticClass:"scroll-on"},[e("data-analysis",{attrs:{chartData:t.echartData,channelId:t.channelId,timeValue:t.timeValue,channelIdFormat:t.channelIdFormat},on:{orgsChooseChange:t.orgsChooseChange}}),e("ranking",{attrs:{chartData:t.echartData,channelId:t.channelId,timeValue:t.timeValue}}),e("map-distribution",{attrs:{channelId:t.channelId,timeValue:t.timeValue,chartData:t.echartData}})],1)])},a=[],i=n("2232"),o=n("5a0c"),s=n("ed08"),c=n("2f62"),l=n("1532"),u=n("2cb4"),h=n("2e7c");function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function d(t,e){return y(t)||m(t,e)||v(t,e)||p()}function p(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function v(t,e){if(t){if("string"==typeof t)return g(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?g(t,e):void 0}}function g(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function m(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,a,i,o,s=[],c=!0,l=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(s.push(r.value),s.length!==e);c=!0);}catch(t){l=!0,a=t}finally{try{if(!c&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(l)throw a}}return s}}function y(t){if(Array.isArray(t))return t}function b(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */b=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,a=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function l(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function(t,e,n){return t[e]=n}}function u(t,e,n,r){var i=e&&e.prototype instanceof y?e:y,o=Object.create(i.prototype),s=new E(r||[]);return a(o,"_invoke",{value:j(t,n,s)}),o}function h(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=u;var d="suspendedStart",p="suspendedYield",v="executing",g="completed",m={};function y(){}function _(){}function w(){}var C={};l(C,o,(function(){return this}));var O=Object.getPrototypeOf,k=O&&O(O(M([])));k&&k!==n&&r.call(k,o)&&(C=k);var x=w.prototype=y.prototype=Object.create(C);function L(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function D(t,e){function n(a,i,o,s){var c=h(t[a],t,i);if("throw"!==c.type){var l=c.arg,u=l.value;return u&&"object"==f(u)&&r.call(u,"__await")?e.resolve(u.__await).then((function(t){n("next",t,o,s)}),(function(t){n("throw",t,o,s)})):e.resolve(u).then((function(t){l.value=t,o(l)}),(function(t){return n("throw",t,o,s)}))}s(c.arg)}var i;a(this,"_invoke",{value:function(t,r){function a(){return new e((function(e,a){n(t,r,e,a)}))}return i=i?i.then(a,a):a()}})}function j(e,n,r){var a=d;return function(i,o){if(a===v)throw Error("Generator is already running");if(a===g){if("throw"===i)throw o;return{value:t,done:!0}}for(r.method=i,r.arg=o;;){var s=r.delegate;if(s){var c=T(s,r);if(c){if(c===m)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===d)throw a=g,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=v;var l=h(e,n,r);if("normal"===l.type){if(a=r.done?g:p,l.arg===m)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(a=g,r.method="throw",r.arg=l.arg)}}}function T(e,n){var r=n.method,a=e.iterator[r];if(a===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,T(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),m;var i=h(a,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,m;var o=i.arg;return o?o.done?(n[e.resultName]=o.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,m):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,m)}function I(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function S(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(I,this),this.reset(!0)}function M(e){if(e||""===e){var n=e[o];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,i=function n(){for(;++a<e.length;)if(r.call(e,a))return n.value=e[a],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw new TypeError(f(e)+" is not iterable")}return _.prototype=w,a(x,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:_,configurable:!0}),_.displayName=l(w,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===_||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,l(t,c,"GeneratorFunction")),t.prototype=Object.create(x),t},e.awrap=function(t){return{__await:t}},L(D.prototype),l(D.prototype,s,(function(){return this})),e.AsyncIterator=D,e.async=function(t,n,r,a,i){void 0===i&&(i=Promise);var o=new D(u(t,n,r,a),i);return e.isGeneratorFunction(n)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},L(x),l(x,c,"Generator"),l(x,o,(function(){return this})),l(x,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=M,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(S),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function a(r,a){return s.type="throw",s.arg=e,n.next=r,a&&(n.method="next",n.arg=t),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var c=r.call(o,"catchLoc"),l=r.call(o,"finallyLoc");if(c&&l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),S(n),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var a=r.arg;S(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:M(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),m}},e}function _(t,e,n,r,a,i,o){try{var s=t[i](o),c=s.value}catch(t){return void n(t)}s.done?e(c):Promise.resolve(c).then(r,a)}function w(t){return function(){var e=this,n=arguments;return new Promise((function(r,a){var i=t.apply(e,n);function o(t){_(i,r,a,o,s,"next",t)}function s(t){_(i,r,a,o,s,"throw",t)}o(void 0)}))}}function C(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function O(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?C(Object(n),!0).forEach((function(e){k(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):C(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function k(t,e,n){return(e=x(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function x(t){var e=L(t,"string");return"symbol"==f(e)?e:e+""}function L(t,e){if("object"!=f(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=f(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var D={name:"ChannelHomePage",components:{DataAnalysis:l["default"],Ranking:u["default"],MapDistribution:h["default"]},data:function(){return{isLoading:!1,today:o().format("YYYY-MM-DD"),timeList:Object(s["f"])(i["TIME_CHOOSE_LIST"]),pickerOptions:i["TIME_DAY_OPTION"],pickerMonthOptions:i["TIME_MONTH_OPTION"],dateType:"day",dateValue:[],yearValue:[],monthValue:[],timeValue:{},rangOpt:{format:"{y}-{m}-{d}"},echartData:{},secondCategories:[],organizationId:[],yearList:this.getYearList(),cascaderProps:{label:"name",value:"id",children:"children_list",checkStrictly:!0,multiple:!0},channelTreeList:[],channelId:[],channelIdFormat:[],orgsId:[]}},computed:O({},Object(c["c"])(["userInfo"])),created:function(){},mounted:function(){this.initDic(),this.initDefaultData(),this.getDataList()},methods:{initDefaultData:function(){this.dateType="day",this.organizationId=[+Object(s["x"])("organization")],this.dateValue=Object(s["t"])(-6,this.rangOpt),this.timeValue={type:this.dateType,value:this.dateValue}},initDic:function(){var t=this;return w(b().mark((function e(){var n;return b().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.getChannelTreeList();case 2:n=e.sent,Array.isArray(n)&&n.length>0&&(t.channelTreeList=Object(s["f"])(n));case 4:case"end":return e.stop()}}),e)})))()},getDataList:function(){var t=this;return w(b().mark((function e(){var n,r,a,i,o;return b().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,n={start_date:t.timeValue.value[0],end_date:t.timeValue.value[1],date_type:t.dateType},null!=t.channelIdFormat&&t.channelIdFormat.length>0?n.channel_ids=t.channelIdFormat:delete n.channel_ids,Array.isArray(t.orgsId)&&t.orgsId.length>0?n.org_ids=t.orgsId:delete n.org_ids,e.next=6,Object(s["Z"])(t.$apis.apiBackgroundChannelDataStatisticsHomepageDataPost(n));case 6:if(r=e.sent,a=d(r,2),i=a[0],o=a[1],t.isLoading=!1,!i){e.next=14;break}return t.$message.error(i.message),e.abrupt("return");case 14:0===o.code?t.echartData=o.data||{}:t.$message.error(o.msg);case 15:case"end":return e.stop()}}),e)})))()},changeTimeFn:function(t){this.dateType=t.value},getYearList:function(){for(var t=2021,e=(new Date).getFullYear(),n=[],r=t;r<=e;r++){var a={name:r+"年",value:r};n.push(a)}return n},getChannelTreeList:function(){var t=this;return new Promise((function(e){var n=t.userInfo.channel_id||"";t.$apis.apiBackgroundChannelChannelTreeListPost({channel_id:n}).then((function(t){if(Reflect.has(t,"code")&&0===t.code){var n=t.data||{};Object(s["T"])(n.results,"children_list"),e(n.results)}e([])})).catch((function(t){e([])}))}))},timeChange:function(t){var e=t;this.dateValue="day"===this.dateType?this.dateValue:[],this.monthValue="month"===this.dateType?this.monthValue:[],this.yearValue="quarter"===this.dateType?this.yearValue:"","quarter"===this.dateType&&(e=[t+"-01-01",t+"-12-31"]),"month"===this.dateType&&(e=this.setMonthDay(t)),this.timeValue={type:this.dateType,value:e},this.getDataList()},setMonthDay:function(t){if(!t||t.length<2)return[];var e=t[0]+"-01",n=t[1]+"-01",r=new Date(n.replace(/-/g,"/"));return r.setMonth(r.getMonth()+1),r.setDate(0),n=t[1]+"-"+r.getDate(),t=[e,n],t},channelIdChange:function(t){if(!t||0===t.length)return this.channelIdFormat=[],void this.getDataList();var e=this.$refs.channelMul?this.$refs.channelMul.getCheckedNodes():[],n=[];e&&e.length>0&&e.forEach((function(t){n.push(t.value)})),this.channelIdFormat=Object(s["f"])(n),this.getDataList()},orgsChooseChange:function(t){this.orgsId=t||[],this.getDataList()}}},j=D,T=(n("5329"),n("2877")),I=Object(T["a"])(j,r,a,!1,null,"b04c6704",null);e["default"]=I.exports},b3c6:function(t,e,n){"use strict";n("f7133")},dd46:function(t,e,n){},e5e8:function(t,e,n){"use strict";n("3891")},f263:function(t,e,n){},f7133:function(t,e,n){}}]);