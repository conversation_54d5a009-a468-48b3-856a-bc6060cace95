(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-health-system-components-CreateOrEditArchives","view-merchant-health-system-components-ArchivesDetail","view-merchant-health-system-constants"],{"9af0":function(t,e,r){"use strict";r.r(e);var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"ps-el-drawer"},[e("el-drawer",{attrs:{title:"add"===t.drawerType?"创建档案":"编辑档案",visible:t.visible,"show-close":!1,size:"30%"}},[e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.formLoading,expression:"formLoading"}],staticClass:"p-20 flex-col"},[e("el-form",{ref:"formDataRef",attrs:{model:t.formData,"status-icon":"",rules:t.formDataRules,"label-width":"90px","label-position":"right"},nativeOn:{submit:function(t){t.preventDefault()}}},[e("el-form-item",{attrs:{label:"选择用户",prop:"name"}},[e("el-select",{staticClass:"w-220",attrs:{placeholder:"输入用户姓名搜索",filterable:!0,remote:!0,"remote-method":t.getUserInfo,loading:t.isLoading,disabled:"edit"===t.drawerType},on:{change:t.showInfo},model:{value:t.formData.name,callback:function(e){t.$set(t.formData,"name",e)},expression:"formData.name"}},t._l(t.userList,(function(t,r){return e("el-option",{key:r,attrs:{label:t.name,value:t.id}})})),1)],1),e("el-form-item",{attrs:{label:"人员编号"}},[e("el-input",{staticClass:"w-220",attrs:{placeholder:"选择用户后回填",disabled:!0},model:{value:t.formData.person_no,callback:function(e){t.$set(t.formData,"person_no",e)},expression:"formData.person_no"}})],1),e("el-form-item",{attrs:{label:"手机号"}},[e("el-input",{staticClass:"w-220",attrs:{placeholder:"选择用户后回填",disabled:!0},model:{value:t.formData.phone,callback:function(e){t.$set(t.formData,"phone",e)},expression:"formData.phone"}})],1),e("el-form-item",{attrs:{label:"性别",prop:"gender"}},[e("el-radio",{attrs:{label:"MAN"},model:{value:t.formData.gender,callback:function(e){t.$set(t.formData,"gender",e)},expression:"formData.gender"}},[t._v("男")]),e("el-radio",{attrs:{label:"WOMEN"},model:{value:t.formData.gender,callback:function(e){t.$set(t.formData,"gender",e)},expression:"formData.gender"}},[t._v("女")])],1),e("el-form-item",{attrs:{label:"身高",prop:"height"}},[e("el-input",{staticClass:"w-220 m-r-10",attrs:{placeholder:"请输入",type:"number"},model:{value:t.formData.height,callback:function(e){t.$set(t.formData,"height",e)},expression:"formData.height"}}),e("span",[t._v("cm")])],1),e("el-form-item",{attrs:{label:"体重",prop:"weight"}},[e("el-input",{staticClass:"w-220 m-r-10",attrs:{placeholder:"请输入",type:"number"},model:{value:t.formData.weight,callback:function(e){t.$set(t.formData,"weight",e)},expression:"formData.weight"}}),e("span",[t._v("kg")])],1),e("el-form-item",{attrs:{label:"目标体重",prop:"targe_weight"}},[e("el-input",{staticClass:"w-220 m-r-10",attrs:{placeholder:"请输入",type:"number"},model:{value:t.formData.targe_weight,callback:function(e){t.$set(t.formData,"targe_weight",e)},expression:"formData.targe_weight"}}),e("span",[t._v("kg")])],1),e("el-form-item",{attrs:{label:"出生日期",prop:"birthday"}},[e("el-date-picker",{staticClass:"m-r-10",attrs:{type:"date",placeholder:"选择日期",format:"yyyy 年 MM 月 dd 日","value-format":"yyyy-MM-dd","picker-options":t.pickerOptions},model:{value:t.formData.birthday,callback:function(e){t.$set(t.formData,"birthday",e)},expression:"formData.birthday"}}),e("span",[t._v(t._s(t.computedAge(t.formData.birthday))+"岁")])],1),e("el-form-item",{attrs:{label:"从事职业",prop:"job"}},[e("el-select",{staticClass:"w-220",attrs:{placeholder:"请选择"},model:{value:t.formData.job,callback:function(e){t.$set(t.formData,"job",e)},expression:"formData.job"}},t._l(t.jobList,(function(t,r){return e("el-option",{key:r,attrs:{label:t.label,value:t.value}})})),1)],1),e("el-form-item",{attrs:{label:"个人特征",prop:"disease"}},[e("el-select",{staticClass:"w-220",attrs:{multiple:!0,clearable:!0,filterable:!0,"collapse-tags":!0,placeholder:"请选择"},model:{value:t.formData.disease,callback:function(e){t.$set(t.formData,"disease",e)},expression:"formData.disease"}},t._l(t.diseaseList,(function(t,r){return e("el-option",{key:r,attrs:{label:t.name,value:t.id}})})),1)],1),e("el-form-item",{attrs:{label:"饮食禁忌"}},[e("el-select",{staticClass:"w-220",attrs:{"collapse-tags":!0,multiple:!0,filterable:!0,clearable:!0,placeholder:"请选择"},on:{change:t.showSelectFood},model:{value:t.formData.taboo_food,callback:function(e){t.$set(t.formData,"taboo_food",e)},expression:"formData.taboo_food"}},t._l(t.foodList,(function(t,r){return e("el-option",{key:r,attrs:{label:t.name,value:t.id}})})),1),e("div",{staticClass:"ps-flex-align-c flex-wrap m-t-5"},t._l(t.selectFoodList,(function(r,a){return e("el-tag",{key:a,staticClass:"m-r-10 m-t-5 m-b-5",attrs:{size:"small",type:"info",closable:"","disable-transitions":""},on:{close:function(e){return t.deleteHandle(a,r)}}},[e("span",{staticClass:"el-select__tags-text"},[t._v(t._s(r.name))])])})),1)],1),e("el-form-item",{attrs:{label:"重点关注"}},[e("el-radio",{attrs:{label:!0},model:{value:t.formData.is_follow,callback:function(e){t.$set(t.formData,"is_follow",e)},expression:"formData.is_follow"}},[t._v("是")]),e("el-radio",{attrs:{label:!1},model:{value:t.formData.is_follow,callback:function(e){t.$set(t.formData,"is_follow",e)},expression:"formData.is_follow"}},[t._v("否")])],1)],1),e("div",{staticClass:"ps-el-drawer-footer ps-flex-align-c flex-align-c"},[e("div",{staticClass:"m-r-30"},[e("el-button",{staticClass:"w-100",attrs:{size:"small"},on:{click:t.cancel}},[t._v("取消")]),e("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:function(e){return t.save(t.drawerType)}}},[t._v("保存")])],1)])],1)])],1)},o=[],n=r("5a0c"),i=r.n(n),s=r("9c61"),l=r("ed08");function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,a)}return r}function u(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach((function(e){h(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function h(t,e,r){return(e=d(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function d(t){var e=m(t,"string");return"symbol"==c(e)?e:e+""}function m(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var a=r.call(t,e||"default");if("object"!=c(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function p(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */p=function(){return e};var t,e={},r=Object.prototype,a=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},n="function"==typeof Symbol?Symbol:{},i=n.iterator||"@@iterator",s=n.asyncIterator||"@@asyncIterator",l=n.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function u(t,e,r,a){var n=e&&e.prototype instanceof v?e:v,i=Object.create(n.prototype),s=new F(a||[]);return o(i,"_invoke",{value:E(t,r,s)}),i}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=u;var d="suspendedStart",m="suspendedYield",g="executing",b="completed",y={};function v(){}function w(){}function D(){}var _={};f(_,i,(function(){return this}));var L=Object.getPrototypeOf,x=L&&L(L(I([])));x&&x!==r&&a.call(x,i)&&(_=x);var j=D.prototype=v.prototype=Object.create(_);function k(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function O(t,e){function r(o,n,i,s){var l=h(t[o],t,n);if("throw"!==l.type){var f=l.arg,u=f.value;return u&&"object"==c(u)&&a.call(u,"__await")?e.resolve(u.__await).then((function(t){r("next",t,i,s)}),(function(t){r("throw",t,i,s)})):e.resolve(u).then((function(t){f.value=t,i(f)}),(function(t){return r("throw",t,i,s)}))}s(l.arg)}var n;o(this,"_invoke",{value:function(t,a){function o(){return new e((function(e,o){r(t,a,e,o)}))}return n=n?n.then(o,o):o()}})}function E(e,r,a){var o=d;return function(n,i){if(o===g)throw Error("Generator is already running");if(o===b){if("throw"===n)throw i;return{value:t,done:!0}}for(a.method=n,a.arg=i;;){var s=a.delegate;if(s){var l=S(s,a);if(l){if(l===y)continue;return l}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(o===d)throw o=b,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);o=g;var c=h(e,r,a);if("normal"===c.type){if(o=a.done?b:m,c.arg===y)continue;return{value:c.arg,done:a.done}}"throw"===c.type&&(o=b,a.method="throw",a.arg=c.arg)}}}function S(e,r){var a=r.method,o=e.iterator[a];if(o===t)return r.delegate=null,"throw"===a&&e.iterator.return&&(r.method="return",r.arg=t,S(e,r),"throw"===r.method)||"return"!==a&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+a+"' method")),y;var n=h(o,e.iterator,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,y;var i=n.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function $(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function C(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function F(t){this.tryEntries=[{tryLoc:"root"}],t.forEach($,this),this.reset(!0)}function I(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,n=function r(){for(;++o<e.length;)if(a.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return n.next=n}}throw new TypeError(c(e)+" is not iterable")}return w.prototype=D,o(j,"constructor",{value:D,configurable:!0}),o(D,"constructor",{value:w,configurable:!0}),w.displayName=f(D,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,D):(t.__proto__=D,f(t,l,"GeneratorFunction")),t.prototype=Object.create(j),t},e.awrap=function(t){return{__await:t}},k(O.prototype),f(O.prototype,s,(function(){return this})),e.AsyncIterator=O,e.async=function(t,r,a,o,n){void 0===n&&(n=Promise);var i=new O(u(t,r,a,o),n);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},k(j),f(j,l,"Generator"),f(j,i,(function(){return this})),f(j,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var a in e)r.push(a);return r.reverse(),function t(){for(;r.length;){var a=r.pop();if(a in e)return t.value=a,t.done=!1,t}return t.done=!0,t}},e.values=I,F.prototype={constructor:F,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(C),!e)for(var r in this)"t"===r.charAt(0)&&a.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(a,o){return s.type="throw",s.arg=e,r.next=a,o&&(r.method="next",r.arg=t),!!o}for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n],s=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=a.call(i,"catchLoc"),c=a.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&a.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var n=o;break}}n&&("break"===t||"continue"===t)&&n.tryLoc<=e&&e<=n.finallyLoc&&(n=null);var i=n?n.completion:{};return i.type=t,i.arg=e,n?(this.method="next",this.next=n.finallyLoc,y):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),C(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var a=r.completion;if("throw"===a.type){var o=a.arg;C(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,a){return this.delegate={iterator:I(e),resultName:r,nextLoc:a},"next"===this.method&&(this.arg=t),y}},e}function g(t,e,r,a,o,n,i){try{var s=t[n](i),l=s.value}catch(t){return void r(t)}s.done?e(l):Promise.resolve(l).then(a,o)}function b(t){return function(){var e=this,r=arguments;return new Promise((function(a,o){var n=t.apply(e,r);function i(t){g(n,a,o,i,s,"next",t)}function s(t){g(n,a,o,i,s,"throw",t)}i(void 0)}))}}function y(t){return _(t)||D(t)||w(t)||v()}function v(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function w(t,e){if(t){if("string"==typeof t)return L(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?L(t,e):void 0}}function D(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function _(t){if(Array.isArray(t))return L(t)}function L(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,a=Array(e);r<e;r++)a[r]=t[r];return a}var x={props:{drawerType:String,isShow:Boolean,fileData:{type:Object,default:function(){return{}}},foodList:{type:Array,default:function(){return[]}},diseaseList:{type:Array,default:function(){return[]}}},data:function(){var t=function(t,e,r){if(e){var a=/^(\d+(\.\d{1})?)$/;e>300?r(new Error("请输入正确的身高")):a.test(e)?r():r(new Error("至多一位小数"))}else r(new Error("请输入身高"))},e=function(t,e,r){if(e){var a=/^(\d+(\.\d{1})?)$/;e>200?r(new Error("请输入正确的体重")):a.test(e)?r():r(new Error("至多一位小数"))}else r(new Error("请输入"))};return{formDataRules:{name:[{required:!0,message:"用户名不能为空",trigger:["change","blur"]}],gender:[{required:!0,message:"请选择性别",trigger:["change","blur"]}],height:[{required:!0,message:"请输入身高",trigger:["change","blur"]},{validator:t,trigger:["change","blur"]}],weight:[{required:!0,message:"请输入体重",trigger:["change","blur"]},{validator:e,trigger:["change","blur"]}],targe_weight:[{required:!0,message:"请输入目标体重",trigger:["change","blur"]},{validator:e,trigger:["change","blur"]}],birthday:[{required:!0,message:"请输入出生日期",trigger:["change","blur"]}],job:[{required:!0,message:"请选择职业",trigger:["change","blur"]}],disease:[{required:!0,message:"请选择个人特征",trigger:["change","blur"]}]},formData:{name:"",person_no:"",phone:"",card_info_id:"",gender:"",height:"",weight:"",targe_weight:"",birthday:"",job:"",disease:"",taboo_food:"",is_follow:""},formLoading:!1,userList:[],jobList:s["JOB_LIST"],selectFoodList:[],isLoading:!1,pickerOptions:{disabledDate:function(t){return t.getTime()>Date.now()}}}},computed:{computedAge:function(){return function(t){var e=18;if(t){var r=i()(t),a=i()();e=a.year()-r.year(),(a.month()<r.month()||a.month()===r.month()&&a.date()<r.date())&&e--}return e}},visible:{get:function(){return this.isShow},set:function(t){t||(this.selectFoodList=[]),this.$emit("update:isShow",t)}}},watch:{visible:function(t){t&&(this.formLoading=!0,this.setFormData())}},methods:{setFormData:function(){var t=this;if("edit"===this.drawerType)this.formData.name=this.fileData.name,this.formData.person_no=this.fileData.person_no,this.formData.phone=this.fileData.phone,this.formData.card_info_id=this.fileData.user_id,this.formData.gender="男"===this.fileData.gender?"MAN":"WOMEN",this.formData.height=this.fileData.height,this.formData.weight=this.fileData.weight,this.formData.targe_weight=this.fileData.weight_target,this.formData.birthday=this.fileData.birthday,this.formData.job=this.fileData.job,this.formData.is_follow=this.fileData.is_follow,this.formData.disease=this.fileData.disease_list.map((function(t){return t.id})),this.formData.taboo_food=this.fileData.ingredient_taboo.map((function(t){return t.id})),this.selectFoodList=Object(l["f"])(this.fileData.ingredient_taboo);else{var e=i()().subtract(18,"year").format("YYYY-MM-DD");this.formData.birthday=e}this.$nextTick((function(){t.$refs.formDataRef.clearValidate()})),this.formLoading=!1},deleteHandle:function(t,e){var r=this.formData.taboo_food.toSpliced(t,1);this.formData.taboo_food=y(new Set(r));var a=this.selectFoodList.filter((function(t){return t.id!==e.id}));this.selectFoodList=y(new Set(a))},deleteItem:function(t,e){var r=[];r=t.toSpliced(e,1),t=Object(l["f"])(r)},getUserInfo:function(t,e){var r=this;return b(p().mark((function e(){var a;return p().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t){e.next=7;break}return r.isLoading=!0,a={name:t},e.next=5,r.$apis.apiBackgroundHealthyHealthyInfoCardInfoListPost(a).then((function(t){r.isLoading=!1,0===t.code?r.userList=Object(l["f"])(t.data):r.$message.error(t.msg)}));case 5:e.next=8;break;case 7:r.userList=[];case 8:case"end":return e.stop()}}),e)})))()},showInfo:function(t){var e=this.userList.filter((function(e){return e.id===t}));e&&e.length&&(this.formData.person_no=e[0].person_no||"",this.formData.phone=e[0].phone||"",this.formData.card_info_id=e[0].id||"",this.formData.gender=e[0].gender||"")},showSelectFood:function(t){var e=this,r=[];t&&t.length&&t.forEach((function(t){var a=e.foodList.filter((function(e){return e.id===t}));r.push(a[0])})),this.selectFoodList=Object(l["f"])(r)},save:function(t){var e=this;this.$refs.formDataRef.validate((function(r){if(r){var a={id:"edit"===t?e.fileData.id:void 0,gender:e.formData.gender||"",height:e.formData.height||"",weight:parseFloat(e.formData.weight)||"",weight_target:parseFloat(e.formData.targe_weight)||"",birthday:e.formData.birthday||"",job:e.formData.job||"",disease_ids:e.formData.disease||[],ingredient_ids:e.formData.taboo_food||[],is_follow:e.formData.is_follow||!1,card_info_id:e.formData.card_info_id||""};"add"===t?e.addNewHealthyInfo(a):e.editHealthyInfo(a)}else e.$message.error("档案有未填写的地方，请检查后重试")}))},cancel:function(){var t={name:"",person_no:"",phone:"",card_info_id:"",gender:"",height:"",weight:"",targe_weight:"",birthday:"",job:"",disease:"",taboo_food:"",is_follow:""};this.formData=u({},t),this.visible=!1},addNewHealthyInfo:function(t){var e=this;this.$apis.apiBackgroundHealthyHealthyInfoAddPost(t).then((function(t){0===t.code?(e.$refs.formDataRef.resetFields(),e.$message.success("新增成功"),e.visible=!1,e.$emit("refresh")):e.$message.error(t.msg)}))},editHealthyInfo:function(t){var e=this;this.$apis.apiBackgroundHealthyHealthyInfoModifyPost(t).then((function(t){0===t.code?(e.$refs.formDataRef.resetFields(),e.$message.success("修改成功"),e.visible=!1,e.$emit("refresh")):e.$message.error(t.msg)}))}}},j=x,k=(r("c2fa"),r("2877")),O=Object(k["a"])(j,a,o,!1,null,"b58a633e",null);e["default"]=O.exports},"9c61":function(t,e,r){"use strict";r.r(e),r.d(e,"MEALTIME_SETTING",(function(){return a})),r.d(e,"JOB_LIST",(function(){return o}));var a={tooltip:{trigger:"item",borderColor:"#FCA155",textStyle:{color:"#000",fontWeight:500},backgroundColor:"#fff",extraCssText:"box-shadow: 0 0 10px rgba(0,0,0,0.2);font-weight: 540;",formatter:function(t){var e=t.marker,r=t.percent;return e+t.name+"&nbsp;&nbsp;&nbsp;"+r+"%"}},title:{text:"0",x:"center",y:"center",top:"25%",textStyle:{color:"#fd953c",fontSize:18}},legend:{top:"62%",orient:"vertical",y:"bottom",padding:[0,0,0,0]},series:[{type:"pie",radius:["45%","60%"],avoidLabelOverlap:!1,top:"-10%",height:200,itemStyle:{borderRadius:10,borderColor:"#f1f2f5",borderWidth:3},hoverAnimation:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!1,fontSize:"20",fontWeight:"bold"}},labelLine:{show:!1},data:[]}],color:["#07DED0","#FE985F","#e98397","#F97C95","#58AFFE","#F8C345"]},o=[{label:"学生",value:"student",status:!1,type:"0"},{label:"在职教学人员",value:"teacher",status:!1,type:"1"},{label:"军人",value:"soldier",status:!1,type:"2"},{label:"国家机关、党群组织、企业、事业单位负责人",value:"org_leaders",status:!1,type:"3"},{label:"其他专业技术和管理人员",value:"manage",status:!1,type:"4"},{label:"商业、服务业人员",value:"service",status:!1,type:"5"},{label:"办事人员和有关人员（含公务员）",value:"clerks",status:!1,type:"6"},{label:"农、林、牧、渔、水利生产人员",value:"farmers",status:!1,type:"7"},{label:"生产、运输设备操作人员及其有关人员",value:"production",status:!1,type:"8"},{label:"不便分类的其他从业人员",value:"other",status:!1,type:"9"},{label:"待业人员",value:"unemployed",status:!1,type:"10"}]},c2fa:function(t,e,r){"use strict";r("d734")},d734:function(t,e,r){}}]);