(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-consumption-rules-service-admin-AddConsumeServiceRule","view-merchant-consumption-rules-service-admin-AddChargeServiceRule","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-components-mealFoodList-FoodDiscountDialog","view-merchant-meal-management-meal-report-MealPackageRule"],{"467c":function(e,t,r){"use strict";r("87f1")},"87f1":function(e,t,r){},"93ef":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"add-conrules-wrapper container-wrapper circular-bead"},[t("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"consumptionFormRef",staticClass:"consumption-form-wrapper ps-small-box",attrs:{rules:e.formDataRule,model:e.formData,size:"small"}},[t("el-form-item",{staticClass:"name-b",attrs:{label:"规则名称：",prop:"name","label-width":"130px"}},[t("el-input",{staticClass:"ps-input",staticStyle:{width:"215px"},attrs:{placeholder:"请输入规则名称",maxlength:"15"},model:{value:e.formData.name,callback:function(t){e.$set(e.formData,"name",t)},expression:"formData.name"}})],1),t("el-form-item",{staticClass:"min-label-w",attrs:{label:"适用消费点：",prop:"orgNos","label-width":"130px"}},[t("consume-select",{staticStyle:{width:"215px"},attrs:{multiple:"","collapse-tags":""},model:{value:e.formData.orgNos,callback:function(t){e.$set(e.formData,"orgNos",t)},expression:"formData.orgNos"}})],1),t("el-form-item",{key:"groupNos",staticClass:"min-label-w",attrs:{label:"适用分组：",prop:e.formData.isTourist?"":"groupNos","label-width":"130px"}},[t("user-group-select",{staticClass:"ps-input",staticStyle:{width:"215px"},attrs:{multiple:!0,"collapse-tags":!0,placeholder:"请选择分组"},model:{value:e.formData.groupNos,callback:function(t){e.$set(e.formData,"groupNos",t)},expression:"formData.groupNos"}})],1),t("el-form-item",{staticClass:"min-label-w",attrs:{label:"适用就餐方式：",prop:"takeType","label-width":"130px"}},[t("el-checkbox-group",{staticClass:"ps-checkbox",model:{value:e.formData.takeType,callback:function(t){e.$set(e.formData,"takeType",t)},expression:"formData.takeType"}},e._l(e.takeTypeList,(function(r){return t("el-checkbox",{key:r.value,attrs:{label:r.value,name:"takeType"}},[e._v(" "+e._s(r.label)+" ")])})),1)],1),t("el-form-item",{staticClass:"min-label-w",attrs:{label:"适用餐段：",prop:"mealTime","label-width":"130px"}},[t("el-checkbox-group",{staticClass:"ps-checkbox",model:{value:e.formData.mealTime,callback:function(t){e.$set(e.formData,"mealTime",t)},expression:"formData.mealTime"}},e._l(e.mealType,(function(r){return t("el-checkbox",{key:r.value,attrs:{label:r.value,name:"mealTime"}},[e._v(" "+e._s(r.label)+" ")])})),1)],1),t("el-form-item",{staticClass:"block-label",attrs:{label:"选择需要收取手续费的扣款方式：",prop:"selectListId"}},[t("div",{staticClass:"m-t-20"},[t("el-table",{ref:"historytableData",staticStyle:{width:"800px"},attrs:{data:e.historytableData,stripe:"",height:"300","header-row-class-name":"ps-table-header-row"},on:{"selection-change":e.handleSelectionChange}},[t("el-table-column",{attrs:{type:"selection","class-name":"ps-checkbox",width:"55"}}),t("el-table-column",{attrs:{prop:"payway_alias",label:"支付渠道",align:"center"}}),t("el-table-column",{attrs:{prop:"sub_payway_alias",label:"扣款钱包",align:"center"}}),t("el-table-column",{attrs:{"show-overflow-tooltip":"",label:"手续费",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return[!r.row.service_fee_value&&e.formData.selectListId.length&&e.formData.selectListId.includes(r.row.id)?t("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.serviceSetting(r.row)}}},[e._v(" 设置 ")]):e.formData.selectListId.length&&e.formData.selectListId.includes(r.row.id)&&1===r.row.service_fee_type&&r.row.service_fee_value?t("span",{staticClass:"ps-origin",on:{click:function(t){return e.serviceSetting(r.row)}}},[e._v(" "+e._s(r.row.service_fee_value)+"元 ")]):e.formData.selectListId.length&&e.formData.selectListId.includes(r.row.id)&&0===r.row.service_fee_type&&r.row.service_fee_value?t("span",{staticClass:"ps-origin",on:{click:function(t){return e.serviceSetting(r.row)}}},[e._v(" "+e._s(r.row.service_fee_value)+"% ")]):e._e()]}}])})],1)],1)]),t("div",{staticClass:"footer"},[t("div",{staticClass:"m-b-20",staticStyle:{color:"red"}},[e._v("注：禁用状态才支持修改规则信息")]),t("el-button",{staticClass:"ps-origin-plain-btn",attrs:{disabled:"enable"===e.modifyData.status,type:"primary"},on:{click:function(t){return e.saveConsumHandle("stop")}}},[e._v(" 保存 ")]),t("el-button",{staticClass:"ps-origin-btn",attrs:{type:"primary",disabled:"enable"===e.modifyData.status},on:{click:function(t){return e.saveConsumHandle("enable")}}},[e._v(" 保存并启用 ")])],1)],1),t("el-dialog",{attrs:{title:"手续费设置",visible:e.serviceSettingDialog,width:"400px","custom-class":"ps-dialog"},on:{"update:visible":function(t){e.serviceSettingDialog=t}}},[t("el-form",{ref:"serviceSettingForm",attrs:{rules:e.serviceSettingDialogRuls,model:e.serviceSettingDialogFormData}},[t("div",{staticClass:"ps-flex"},[t("el-form-item",{staticClass:"p-r-20"},[t("el-radio",{staticClass:"ps-radio",attrs:{label:1},model:{value:e.serviceSettingDialogFormData.service_fee_type,callback:function(t){e.$set(e.serviceSettingDialogFormData,"service_fee_type",t)},expression:"serviceSettingDialogFormData.service_fee_type"}},[e._v(" 定额 ")])],1),0!==e.serviceSettingDialogFormData.service_fee_type?t("el-form-item",{attrs:{prop:"quota"}},[t("div",{staticClass:"ps-flex"},[t("el-input",{staticClass:"ps-input w-150 p-r-10",attrs:{size:"small"},model:{value:e.serviceSettingDialogFormData.quota,callback:function(t){e.$set(e.serviceSettingDialogFormData,"quota",t)},expression:"serviceSettingDialogFormData.quota"}}),t("span",[e._v("元")])],1),t("span",[e._v("实收金额=订单金额+定额")])]):e._e()],1),t("div",{staticClass:"ps-flex"},[t("el-form-item",{staticClass:"p-r-20",attrs:{label:""}},[t("el-radio",{staticClass:"ps-radio",attrs:{label:0},model:{value:e.serviceSettingDialogFormData.service_fee_type,callback:function(t){e.$set(e.serviceSettingDialogFormData,"service_fee_type",t)},expression:"serviceSettingDialogFormData.service_fee_type"}},[e._v(" 百分比 ")])],1),1!==e.serviceSettingDialogFormData.service_fee_type?t("el-form-item",{attrs:{prop:"discount"}},[t("div",{staticClass:"ps-flex"},[t("el-input",{staticClass:"ps-input w-150 p-r-10",attrs:{size:"small"},model:{value:e.serviceSettingDialogFormData.discount,callback:function(t){e.$set(e.serviceSettingDialogFormData,"discount",t)},expression:"serviceSettingDialogFormData.discount"}}),t("span",[e._v("%")])],1),t("span",[e._v("实收金额=订单金额+（订单金额*百分比）")])]):e._e()],1)]),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.serviceSettingDialog=!1}}},[e._v("取 消")]),t("el-button",{staticClass:"ps-btn",attrs:{type:"primary"},on:{click:e.determineServiceSettingDialog}},[e._v(" 确 定 ")])],1)],1)],1)},n=[],i=r("2f62"),o=r("ed08"),s=r("390a"),l=r("7c9c"),c=r("c9d9"),u=r("d0dd"),f=r("da92");function m(e){return m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},m(e)}function d(e,t){return h(e)||y(e,t)||g(e,t)||p()}function p(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function g(e,t){if(e){if("string"==typeof e)return v(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?v(e,t):void 0}}function v(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,a=Array(t);r<t;r++)a[r]=e[r];return a}function y(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var a,n,i,o,s=[],l=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(a=i.call(r)).done)&&(s.push(a.value),s.length!==t);l=!0);}catch(e){c=!0,n=e}finally{try{if(!l&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw n}}return s}}function h(e){if(Array.isArray(e))return e}function b(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */b=function(){return t};var e,t={},r=Object.prototype,a=r.hasOwnProperty,n=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function c(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,r){return e[t]=r}}function u(e,t,r,a){var i=t&&t.prototype instanceof h?t:h,o=Object.create(i.prototype),s=new T(a||[]);return n(o,"_invoke",{value:E(e,r,s)}),o}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var d="suspendedStart",p="suspendedYield",g="executing",v="completed",y={};function h(){}function _(){}function w(){}var D={};c(D,o,(function(){return this}));var k=Object.getPrototypeOf,x=k&&k(k(j([])));x&&x!==r&&a.call(x,o)&&(D=x);var S=w.prototype=h.prototype=Object.create(D);function C(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function L(e,t){function r(n,i,o,s){var l=f(e[n],e,i);if("throw"!==l.type){var c=l.arg,u=c.value;return u&&"object"==m(u)&&a.call(u,"__await")?t.resolve(u.__await).then((function(e){r("next",e,o,s)}),(function(e){r("throw",e,o,s)})):t.resolve(u).then((function(e){c.value=e,o(c)}),(function(e){return r("throw",e,o,s)}))}s(l.arg)}var i;n(this,"_invoke",{value:function(e,a){function n(){return new t((function(t,n){r(e,a,t,n)}))}return i=i?i.then(n,n):n()}})}function E(t,r,a){var n=d;return function(i,o){if(n===g)throw Error("Generator is already running");if(n===v){if("throw"===i)throw o;return{value:e,done:!0}}for(a.method=i,a.arg=o;;){var s=a.delegate;if(s){var l=F(s,a);if(l){if(l===y)continue;return l}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(n===d)throw n=v,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);n=g;var c=f(t,r,a);if("normal"===c.type){if(n=a.done?v:p,c.arg===y)continue;return{value:c.arg,done:a.done}}"throw"===c.type&&(n=v,a.method="throw",a.arg=c.arg)}}}function F(t,r){var a=r.method,n=t.iterator[a];if(n===e)return r.delegate=null,"throw"===a&&t.iterator.return&&(r.method="return",r.arg=e,F(t,r),"throw"===r.method)||"return"!==a&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+a+"' method")),y;var i=f(n,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,y;var o=i.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,y):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function O(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function $(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function T(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(O,this),this.reset(!0)}function j(t){if(t||""===t){var r=t[o];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function r(){for(;++n<t.length;)if(a.call(t,n))return r.value=t[n],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(m(t)+" is not iterable")}return _.prototype=w,n(S,"constructor",{value:w,configurable:!0}),n(w,"constructor",{value:_,configurable:!0}),_.displayName=c(w,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===_||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,c(e,l,"GeneratorFunction")),e.prototype=Object.create(S),e},t.awrap=function(e){return{__await:e}},C(L.prototype),c(L.prototype,s,(function(){return this})),t.AsyncIterator=L,t.async=function(e,r,a,n,i){void 0===i&&(i=Promise);var o=new L(u(e,r,a,n),i);return t.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},C(S),c(S,l,"Generator"),c(S,o,(function(){return this})),c(S,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var a in t)r.push(a);return r.reverse(),function e(){for(;r.length;){var a=r.pop();if(a in t)return e.value=a,e.done=!1,e}return e.done=!0,e}},t.values=j,T.prototype={constructor:T,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach($),!t)for(var r in this)"t"===r.charAt(0)&&a.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(a,n){return s.type="throw",s.arg=t,r.next=a,n&&(r.method="next",r.arg=e),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var l=a.call(o,"catchLoc"),c=a.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),$(r),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var a=r.completion;if("throw"===a.type){var n=a.arg;$(r)}return n}}throw Error("illegal catch attempt")},delegateYield:function(t,r,a){return this.delegate={iterator:j(t),resultName:r,nextLoc:a},"next"===this.method&&(this.arg=e),y}},t}function _(e,t,r,a,n,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(a,n)}function w(e){return function(){var t=this,r=arguments;return new Promise((function(a,n){var i=e.apply(t,r);function o(e){_(i,a,n,o,s,"next",e)}function s(e){_(i,a,n,o,s,"throw",e)}o(void 0)}))}}function D(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function k(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?D(Object(r),!0).forEach((function(t){x(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):D(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function x(e,t,r){return(t=S(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function S(e){var t=C(e,"string");return"symbol"==m(t)?t:t+""}function C(e,t){if("object"!=m(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=m(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var L={components:{UserGroupSelect:s["a"],ConsumeSelect:l["a"]},data:function(){return{type:"",isLoading:!1,historytableData:[{coupon_no:1}],mealType:c["a"],takeTypeList:[{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],formDataRule:{name:[{required:!0,message:"消费规则名称不能为空",trigger:"blur"}],orgNos:[{required:!0,message:"消费点不能为空",trigger:"change"}],groupNos:[{required:!0,message:"适用分组不能为空",trigger:"change"}],takeType:[{required:!0,message:"适用就餐方式不能为空",trigger:"change"}],mealTime:[{required:!0,message:"适用餐段不能为空",trigger:"change"}],selectListId:[{required:!0,message:"扣款方式不能为空",trigger:"change"}]},formData:{name:"",orgNos:[],groupNos:[],isTourist:!1,takeType:[],mealTime:[],selectListId:[]},serviceSettingDialog:!1,serviceSettingDialogFormData:{service_fee_type:1,quota:"",discount:""},serviceSettingDialogRuls:{quota:[{required:!0,message:"请输入金额",trigger:"blur"},{validator:u["c"],trigger:"blur"}],discount:[{required:!0,message:"请输入折扣",trigger:"blur"},{validator:u["f"],trigger:"blur"}]},serviceSettingData:{},modifyData:{}}},computed:k({},Object(i["c"])(["organization"])),created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){var e=this;return w(b().mark((function t(){return b().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.type=e.$route.params.type,"modify"===e.type&&(e.modifyData=e.$decodeQuery(e.$route.query.data),e.formData={name:e.modifyData.name,orgNos:e.modifyData.rule_info.org_names?Object.values(e.modifyData.rule_info.org_names):[],groupNos:e.modifyData.rule_info.group_names?Object.values(e.modifyData.rule_info.group_names):[],isTourist:e.modifyData.is_tourist,takeType:e.modifyData.take_type,mealTime:e.modifyData.meal_time,selectListId:[]}),e.getChargeGetPayinfoList();case 3:case"end":return t.stop()}}),t)})))()},checkboxIsTourist:function(){var e=this;this.formData.isTourist&&this.$nextTick((function(){e.$refs.consumptionFormRef.clearValidate()}))},getChargeGetPayinfoList:function(){var e=this;return w(b().mark((function t(){var r,a,n,i;return b().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(o["Z"])(e.$apis.apiBackgroundMarketingCommissionChargeGetPayinfoListPost({organizations:[e.organization]}));case 3:if(r=t.sent,a=d(r,2),n=a[0],i=a[1],e.isLoading=!1,!n){t.next=11;break}return e.$message.error(n.message),t.abrupt("return");case 11:0===i.code?e.historytableData=i.data.map((function(t){return t.service_fee_type=1,t.service_fee_value="","modify"===e.type&&e.modifyData.rule.forEach((function(r){t.id===r.payinfo&&(e.$nextTick((function(){e.$refs.historytableData.toggleRowSelection(t)})),t.service_fee_value=r.charge_fixed?f["a"].divide(r.charge_fixed,100):r.charge_percent,t.service_fee_type=r.charge_fixed?1:0,e.formData.selectListId.push(r.payinfo))})),t})):e.$message.error(i.msg);case 12:case"end":return t.stop()}}),t)})))()},getCommissionChargeConsumeAdd:function(e){var t=this;return w(b().mark((function r(){var a,n,i,s;return b().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return t.isLoading=!0,r.next=3,Object(o["Z"])(t.$apis.apiBackgroundMarketingCommissionChargeConsumeAddPost(e));case 3:if(a=r.sent,n=d(a,2),i=n[0],s=n[1],t.isLoading=!1,!i){r.next=11;break}return t.$message.error(i.message),r.abrupt("return");case 11:0===s.code?(t.$message.success(s.msg),t.$closeCurrentTab(t.$route.path)):t.$message.error(s.msg);case 12:case"end":return r.stop()}}),r)})))()},getConsumeModify:function(e){var t=this;return w(b().mark((function r(){var a,n,i,s;return b().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return t.isLoading=!0,r.next=3,Object(o["Z"])(t.$apis.apiBackgroundMarketingCommissionChargeConsumeModifyPost(e));case 3:if(a=r.sent,n=d(a,2),i=n[0],s=n[1],t.isLoading=!1,!i){r.next=11;break}return t.$message.error(i.message),r.abrupt("return");case 11:0===s.code?(t.$message.success(s.msg),t.$closeCurrentTab(t.$route.path)):t.$message.error(s.msg);case 12:case"end":return r.stop()}}),r)})))()},handleSelectionChange:function(e){var t=this;this.formData.selectListId=[];var r=Object.freeze(e);r.map((function(e){t.formData.selectListId.push(e.id)}))},serviceSetting:function(e){this.serviceSettingDialogFormData={service_fee_type:1,quota:"",discount:""},this.serviceSettingData=e,this.serviceSettingDialogFormData.service_fee_type=e.service_fee_type,1===e.service_fee_type&&(this.serviceSettingDialogFormData.discount="",this.serviceSettingDialogFormData.quota=e.service_fee_value?e.service_fee_value:""),0===e.service_fee_type&&(this.serviceSettingDialogFormData.discount=e.service_fee_value,this.serviceSettingDialogFormData.quota=""),this.serviceSettingDialog=!0},determineServiceSettingDialog:function(){var e=this;this.$refs.serviceSettingForm.validate((function(t){t&&(e.serviceSettingData.service_fee_type=e.serviceSettingDialogFormData.service_fee_type,e.serviceSettingData.service_fee_value=1===e.serviceSettingDialogFormData.service_fee_type?e.serviceSettingDialogFormData.quota:e.serviceSettingDialogFormData.discount,e.serviceSettingDialog=!1)}))},saveConsumHandle:function(e){var t=this;this.$refs.consumptionFormRef.validate((function(r){if(r){for(var a={name:t.formData.name,org_nos:t.formData.orgNos,group_nos:t.formData.groupNos,is_tourist:t.formData.isTourist,take_type:t.formData.takeType,meal_time:t.formData.mealTime,rules:[],status:e},n=!1,i=0;i<t.historytableData.length;i++){var o=t.historytableData[i];t.formData.selectListId.length&&t.formData.selectListId.includes(o.id)&&(o.service_fee_value?a.rules.push({payway_alias:o.payway_alias,sub_payway_alias:o.sub_payway_alias,pay_scene_alias:o.pay_scene_alias,payinfo_id:o.id,type:o.service_fee_type,numerical:1===o.service_fee_type?f["a"].times(Number(o.service_fee_value),100):Number(o.service_fee_value)}):n=!0)}if(n)return t.$message.error("请输入手续费");"add"===t.type?t.getCommissionChargeConsumeAdd(a):"modify"===t.type&&t.getConsumeModify(k({rule_no:t.modifyData.rule_no},a))}}))}}},E=L,F=(r("467c"),r("2877")),O=Object(F["a"])(E,a,n,!1,null,null,null);t["default"]=O.exports},c9d9:function(e,t,r){"use strict";r.d(t,"a",(function(){return i})),r.d(t,"d",(function(){return o})),r.d(t,"b",(function(){return s})),r.d(t,"c",(function(){return l})),r.d(t,"e",(function(){return c})),r.d(t,"f",(function(){return u})),r.d(t,"g",(function(){return f}));var a=r("5a0c"),n=r("da92"),i=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],o=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],s={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},l=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],c=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],u=(a().subtract(7,"day").format("YYYY-MM-DD"),a().format("YYYY-MM-DD"),function(e){return e?"number"===typeof e?n["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:n["a"].divide(e,100).toFixed(2):"0.00"}),f=function(e){return n["a"].times(e,100)}},d0dd:function(e,t,r){"use strict";r.d(t,"a",(function(){return a})),r.d(t,"b",(function(){return n})),r.d(t,"g",(function(){return i})),r.d(t,"c",(function(){return o})),r.d(t,"f",(function(){return s})),r.d(t,"d",(function(){return l})),r.d(t,"e",(function(){return c}));var a=function(e,t,r){if(t){var a=/^-?(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;a.test(t)?r():r(new Error("金额格式有误"))}else r(new Error("请输入金额"))},n=function(e,t,r){if(t){var a=/^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;a.test(t)?r():r(new Error("金额格式有误"))}else r()},i=function(e,t,r){if(!t)return r(new Error("手机号不能为空"));var a=/^1[3456789]\d{9}$/;a.test(t)?r():r(new Error("请输入正确手机号"))},o=function(e,t,r){if(!t)return r(new Error("金额有误"));var a=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;a.test(t)?r():r(new Error("金额格式有误"))},s=function(e,t,r){if(""===t)return r(new Error("不能为空"));var a=/^\d+$/;a.test(t)?r():r(new Error("请输入正确数字"))},l=function(e,t,r){if(""!==t){var a=/^(\+|-)?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;a.test(t)?r():r(new Error("金额格式有误"))}else r(new Error("请输入金额"))},c=function(e,t,r){var a=/^[\u4E00-\u9FA5\w-]+$/;a.test(t)?r():r(new Error("格式不正确，不能包含特殊字符"))}}}]);