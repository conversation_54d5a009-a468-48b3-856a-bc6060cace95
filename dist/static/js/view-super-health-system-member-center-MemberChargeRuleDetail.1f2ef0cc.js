(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-member-center-MemberChargeRuleDetail","view-super-health-system-member-center-components-MemberChargeDetailDialog","view-super-health-system-member-center-constants"],{"1b89":function(e,t,r){"use strict";r("5887")},"476b":function(e,t,r){"use strict";r("5930")},"55fb":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"SuperMemberList container-wrapper"},[t("refresh-tool",{on:{refreshPage:e.refreshHandle}}),t("search-form",{ref:"searchRef",attrs:{"form-setting":e.searchFormSetting},on:{search:e.search<PERSON><PERSON><PERSON>,reset:e.reset<PERSON><PERSON><PERSON>}}),t("div",{staticClass:"table-wrapper"},[t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[e._v("数据列表")]),t("div",{staticClass:"align-r"},[t("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(t){return e.openDialog("add")}}},[e._v("新建")])],1)]),t("div",{staticClass:"table-content"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData,height:"460",stripe:"","header-row-class-name":"ps-table-header-row"}},e._l(e.tableSettings,(function(r){return t("table-column",{key:r.key,attrs:{col:r},scopedSlots:e._u([{key:"status",fn:function(r){var a=r.row;return[t("el-switch",{directives:[{name:"loading",rawName:"v-loading",value:e.switchLoading,expression:"switchLoading"}],attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},on:{change:function(t){return e.switchFacePay(t,a,a.index)}},model:{value:a.is_enable,callback:function(t){e.$set(a,"is_enable",t)},expression:"row.is_enable"}})]}},{key:"label",fn:function(t){var r=t.row;return[e._v(" "+e._s(e.getMemberLabelTxt(r))+" ")]}},{key:"trigger",fn:function(t){var r=t.row;return[e._v(" "+e._s(e.getMemberTriggerTxt(r))+" ")]}},{key:"buyCount",fn:function(t){var r=t.row;return[e._v(" "+e._s("-1"==r.buy_count?"无限制":r.buy_count)+" ")]}},{key:"operation",fn:function(r){var a=r.row;return[t("el-button",{staticClass:"ps-text-blue",attrs:{type:"text",size:"small"},on:{click:function(t){return e.openDialog("edit",a)}}},[e._v("编辑")])]}}],null,!0)})})),1)],1),t("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[t("pagination",{attrs:{"current-page":e.currentPage,"page-size":e.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:e.totalCount},on:{"size-change":e.handlerSizeChange,"current-change":e.handlerPageChange,"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t},"update:pageSize":function(t){e.pageSize=t},"update:page-size":function(t){e.pageSize=t}}})],1)]),t("member-charge-detail-dialog",{attrs:{isshow:e.dialogVisible,title:e.dialogTitle,type:e.dialogType,"member-cycle":e.type,"base-price":e.basePrice,"select-info":e.selectInfo,confirm:e.searchHandler},on:{"update:isshow":function(t){e.dialogVisible=t}}})],1)},n=[],i=r("c8c2"),o=r("ed08"),l=r("ab18");function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function c(e,t){return d(e)||f(e,t)||m(e,t)||u()}function u(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function m(e,t){if(e){if("string"==typeof e)return p(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?p(e,t):void 0}}function p(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,a=Array(t);r<t;r++)a[r]=e[r];return a}function f(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var a,n,i,o,l=[],s=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;s=!1}else for(;!(s=(a=i.call(r)).done)&&(l.push(a.value),l.length!==t);s=!0);}catch(e){c=!0,n=e}finally{try{if(!s&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw n}}return l}}function d(e){if(Array.isArray(e))return e}function h(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */h=function(){return t};var e,t={},r=Object.prototype,a=r.hasOwnProperty,n=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function m(e,t,r,a){var i=t&&t.prototype instanceof v?t:v,o=Object.create(i.prototype),l=new M(a||[]);return n(o,"_invoke",{value:O(e,r,l)}),o}function p(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=m;var f="suspendedStart",d="suspendedYield",y="executing",b="completed",g={};function v(){}function _(){}function w(){}var k={};u(k,o,(function(){return this}));var E=Object.getPrototypeOf,L=E&&E(E(F([])));L&&L!==r&&a.call(L,o)&&(k=L);var x=w.prototype=v.prototype=Object.create(k);function C(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function T(e,t){function r(n,i,o,l){var c=p(e[n],e,i);if("throw"!==c.type){var u=c.arg,m=u.value;return m&&"object"==s(m)&&a.call(m,"__await")?t.resolve(m.__await).then((function(e){r("next",e,o,l)}),(function(e){r("throw",e,o,l)})):t.resolve(m).then((function(e){u.value=e,o(u)}),(function(e){return r("throw",e,o,l)}))}l(c.arg)}var i;n(this,"_invoke",{value:function(e,a){function n(){return new t((function(t,n){r(e,a,t,n)}))}return i=i?i.then(n,n):n()}})}function O(t,r,a){var n=f;return function(i,o){if(n===y)throw Error("Generator is already running");if(n===b){if("throw"===i)throw o;return{value:e,done:!0}}for(a.method=i,a.arg=o;;){var l=a.delegate;if(l){var s=A(l,a);if(s){if(s===g)continue;return s}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(n===f)throw n=b,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);n=y;var c=p(t,r,a);if("normal"===c.type){if(n=a.done?b:d,c.arg===g)continue;return{value:c.arg,done:a.done}}"throw"===c.type&&(n=b,a.method="throw",a.arg=c.arg)}}}function A(t,r){var a=r.method,n=t.iterator[a];if(n===e)return r.delegate=null,"throw"===a&&t.iterator.return&&(r.method="return",r.arg=e,A(t,r),"throw"===r.method)||"return"!==a&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+a+"' method")),g;var i=p(n,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,g;var o=i.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,g):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function S(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function P(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function M(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(S,this),this.reset(!0)}function F(t){if(t||""===t){var r=t[o];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function r(){for(;++n<t.length;)if(a.call(t,n))return r.value=t[n],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(s(t)+" is not iterable")}return _.prototype=w,n(x,"constructor",{value:w,configurable:!0}),n(w,"constructor",{value:_,configurable:!0}),_.displayName=u(w,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===_||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,u(e,c,"GeneratorFunction")),e.prototype=Object.create(x),e},t.awrap=function(e){return{__await:e}},C(T.prototype),u(T.prototype,l,(function(){return this})),t.AsyncIterator=T,t.async=function(e,r,a,n,i){void 0===i&&(i=Promise);var o=new T(m(e,r,a,n),i);return t.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},C(x),u(x,c,"Generator"),u(x,o,(function(){return this})),u(x,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var a in t)r.push(a);return r.reverse(),function e(){for(;r.length;){var a=r.pop();if(a in t)return e.value=a,e.done=!1,e}return e.done=!0,e}},t.values=F,M.prototype={constructor:M,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(P),!t)for(var r in this)"t"===r.charAt(0)&&a.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(a,n){return l.type="throw",l.arg=t,r.next=a,n&&(r.method="next",r.arg=e),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],l=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var s=a.call(o,"catchLoc"),c=a.call(o,"finallyLoc");if(s&&c){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,g):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),P(r),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var a=r.completion;if("throw"===a.type){var n=a.arg;P(r)}return n}}throw Error("illegal catch attempt")},delegateYield:function(t,r,a){return this.delegate={iterator:F(t),resultName:r,nextLoc:a},"next"===this.method&&(this.arg=e),g}},t}function y(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function b(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y(Object(r),!0).forEach((function(t){g(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function g(e,t,r){return(t=v(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function v(e){var t=_(e,"string");return"symbol"==s(t)?t:t+""}function _(e,t){if("object"!=s(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=s(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function w(e,t,r,a,n,i,o){try{var l=e[i](o),s=l.value}catch(e){return void r(e)}l.done?t(s):Promise.resolve(s).then(a,n)}function k(e){return function(){var t=this,r=arguments;return new Promise((function(a,n){var i=e.apply(t,r);function o(e){w(i,a,n,o,l,"next",e)}function l(e){w(i,a,n,o,l,"throw",e)}o(void 0)}))}}var E={name:"SuperMemberChargeRuleDetail",components:{MemberChargeDetailDialog:l["default"]},props:{},data:function(){return{isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[],searchFormSetting:Object(o["f"])(i["SEARCH_FORM_CHARGE_DETAIL_DATA"]),tableSettings:Object(o["f"])(i["TABLE_HEAD_CHARGE_DETAIL_DATA"]),dialogVisible:!1,switchLoading:!1,dialogTitle:"",dialogType:"",selectInfo:{},type:"",basePrice:""}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.$route.query&&(this.type=this.$route.query.type,this.basePrice=this.$route.query.basePrice,"permanent"!==this.type&&"week"!==this.type||(this.tableSettings=this.tableSettings.filter((function(e){return"buy_count"!==e.key})))),this.getMemberCharge(),this.getMemberLabel(),this.getMemberCycle()},searchHandler:Object(o["d"])((function(){this.dialogVisible=!1,this.currentPage=1,this.getMemberCharge()}),300),refreshHandle:function(){this.currentPage=1,this.tableData=[],this.$refs.searchRef&&this.$refs.searchRef.resetForm(),this.initLoad()},resetHandler:function(){this.currentPage=1,this.getMemberCharge()},getMemberCharge:function(){var e=this;return k(h().mark((function t(){var r,a;return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,r=b(b({},e.formatQueryParams(e.searchFormSetting)),{},{page:e.currentPage,page_size:e.pageSize,is_base:!1,member_cycle:[e.type]}),t.next=4,e.$apis.apiBackgroundMemberMemberChargeRuleListPost(r);case 4:a=t.sent,e.isLoading=!1,0===a.code?(e.tableData=a.data.results.map((function(e){return e.show_all_remark=!1,e})),e.totalCount=a.data.count):e.$message.error(a.msg);case 7:case"end":return t.stop()}}),t)})))()},formatQueryParams:function(e){var t={};for(var r in e)""!==e[r].value&&null!==e[r].value&&0!==e[r].value.length&&(t[r]="member_permissions"===r||"trigger_type"===r?[e[r].value]:e[r].value);return t},handlerPageChange:function(e){this.currentPage=e,this.getMemberCharge()},handlerSizeChange:function(e){this.pageSize=e,this.getMemberCharge()},delMemberCharge:function(e){var t=this;return k(h().mark((function r(){return h().wrap((function(r){while(1)switch(r.prev=r.next){case 0:t.$confirm("确定删除该会员收费规则？","提示",{confirmButtonText:t.$t("dialog.confirm_btn"),cancelButtonText:t.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var r=k(h().mark((function r(a,n,i){var o;return h().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if("confirm"!==a){r.next=9;break}return r.next=3,t.$apis.apiBackgroundMemberMemberChargeRuleDeletePost({ids:[e]});case 3:o=r.sent,0===o.code?(t.$message.success("删除成功"),t.getMemberCharge()):t.$message.error(o.msg),i(),n.confirmButtonLoading=!1,r.next=10;break;case 9:n.confirmButtonLoading||i();case 10:case"end":return r.stop()}}),r)})));function a(e,t,a){return r.apply(this,arguments)}return a}()}).then((function(e){})).catch((function(e){}));case 1:case"end":return r.stop()}}),r)})))()},switchFacePay:function(e,t,r){var a=this;return k(h().mark((function e(){var n,i,l,s,u;return h().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=r-1>=0?r-1:0,a.switchLoading=!0,e.next=4,Object(o["Z"])(a.$apis.apiBackgroundMemberMemberChargeRuleModifyPost({id:t.id,name:t.name,member_labels:t.member_labels,member_cycle:t.member_cycle,is_enable:t.is_enable,trigger_type:t.trigger_type,discount:t.discount,origin_fee:t.origin_fee,buy_count:t.buy_count,remark:t.remark}));case 4:if(i=e.sent,l=c(i,2),s=l[0],u=l[1],a.switchLoading=!1,!s){e.next=12;break}return a.$set(a.tableData[n],"is_enable",!t.is_enable),e.abrupt("return",a.$message.error("状态更新失败"));case 12:u&&0===u.code?a.$message.success("成功"):(a.$set(a.tableData[n],"is_enable",!t.is_enable),a.$message.error(u.msg));case 13:case"end":return e.stop()}}),e)})))()},openDialog:function(e,t){if(this.dialogType=e,t&&"object"===s(t)&&(this.selectInfo=Object(o["f"])(t)),"add"===e)this.dialogTitle="新建";else if("edit"===e&&(this.dialogTitle="编辑",t.is_enable))return this.$message.error("亲，启用中的规则无法编辑");this.dialogVisible=!0},getMemberLabel:function(){var e=this;return k(h().mark((function t(){var r;return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundMemberMemberLabelListPost({page:1,page_size:99999});case 2:r=t.sent,0===r.code?e.searchFormSetting.member_labels.dataList=r.data.results:e.$message.error(r.msg);case 4:case"end":return t.stop()}}),t)})))()},getMemberCycle:function(){var e=this;return k(h().mark((function t(){var r,a,n;return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundMemberMemberChargeRuleGetMemberCyclePost({page:1,page_size:99999});case 2:if(r=t.sent,0===r.code)for(n in a=[],r.data)a.push({value:n,label:r.data[n]});else e.$message.error(r.msg);case 4:case"end":return t.stop()}}),t)})))()},getMemberLabelTxt:function(e){var t="object"===s(e)&&e.member_labels_list?e.member_labels_list:[],r="";return t&&t.length>0&&t.forEach((function(e){r=r+e.name+" "})),r},getMemberTriggerTxt:function(e){if(!e||"object"!==s(e))return"";var t="";switch(e.trigger_type){case"all":t="全部标签";break;case"any":t="任意标签";break;default:break}return t}}},L=E,x=(r("1b89"),r("2877")),C=Object(x["a"])(L,a,n,!1,null,"a7628f9c",null);t["default"]=C.exports},5887:function(e,t,r){},5930:function(e,t,r){},ab18:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e._self._c;return t("dialog-message",{attrs:{show:e.visible,title:e.title,loading:e.isLoading,customClass:"ps-dialog",width:e.width},on:{"update:show":function(t){e.visible=t},"update:loading":function(t){e.isLoading=t},close:e.handleClose}},[t("el-form",{ref:"dialogFormRef",staticClass:"member-form",attrs:{model:e.dialogForm,"status-icon":"",rules:e.dialogFormRules,"label-width":"120px"},nativeOn:{submit:function(e){e.preventDefault()}}},["add"===e.type||"edit"===e.type?t("div",[t("el-form-item",{attrs:{label:"名称：",prop:"name"}},[t("el-input",{staticClass:"ps-input w-250",attrs:{maxlength:"10","show-word-limit":""},model:{value:e.dialogForm.name,callback:function(t){e.$set(e.dialogForm,"name",t)},expression:"dialogForm.name"}})],1),t("el-form-item",{attrs:{label:"会员标签：",prop:"label"}},[t("el-select",{staticClass:"ps-input w-250",attrs:{placeholder:"请选择标签",multiple:""},model:{value:e.dialogForm.label,callback:function(t){e.$set(e.dialogForm,"label",t)},expression:"dialogForm.label"}},e._l(e.labelList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),t("el-form-item",{attrs:{label:"触发类型：",prop:"labelType"}},[t("el-radio-group",{on:{change:e.labelTypeChange},model:{value:e.dialogForm.labelType,callback:function(t){e.$set(e.dialogForm,"labelType",t)},expression:"dialogForm.labelType"}},[t("el-radio",{staticClass:"ps-radio",attrs:{label:"all"}},[e._v("全部标签")]),t("el-radio",{staticClass:"ps-radio",attrs:{label:"any"}},[e._v("任意标签")])],1),t("div",{staticClass:"ps-warn-text"},[e._v(e._s(e.warnTip))])],1),t("el-form-item",{attrs:{label:"折扣：",prop:"discount"}},[t("el-input",{staticClass:"ps-input w-250",attrs:{maxlength:"20",type:"number"},on:{input:e.discountInputChange},model:{value:e.dialogForm.discount,callback:function(t){e.$set(e.dialogForm,"discount",t)},expression:"dialogForm.discount"}}),t("span",{staticClass:"m-l-10"},[e._v("%")])],1),t("el-form-item",{attrs:{label:"折扣后价格：",prop:"discountPrice"}},[t("div",[e._v(e._s(e.dialogForm.discountPrice)+"元")])]),"permanent"!==e.memberCycle&&"week"!==e.memberCycle?t("el-form-item",{attrs:{label:"限购次数：",prop:"limitTimes"}},[t("div",{staticClass:"ps-flex limit-style"},[t("el-radio-group",{on:{change:e.limitTimesChange},model:{value:e.dialogForm.limitTimes,callback:function(t){e.$set(e.dialogForm,"limitTimes",t)},expression:"dialogForm.limitTimes"}},[t("el-radio",{staticClass:"ps-radio",attrs:{label:e.defaultLimit}},[e._v("不限制")]),t("el-radio",{staticClass:"ps-radio",attrs:{label:"count"}},[e._v("任意标签")])],1),t("el-form-item",{attrs:{prop:"times"}},[t("div",{staticClass:"ps-flex"},[t("el-input",{staticClass:"w-100 m-l-10",attrs:{type:"number",placeholder:"请输入"},model:{value:e.dialogForm.times,callback:function(t){e.$set(e.dialogForm,"times",t)},expression:"dialogForm.times"}}),t("span",{staticClass:"m-l-10"},[e._v("次/人")])],1)])],1)]):e._e(),t("el-form-item",{attrs:{label:"说明：",prop:"remark"}},[t("el-input",{staticClass:"ps-input w-250",attrs:{type:"textarea",rows:3,maxlength:"50","show-word-limit":""},model:{value:e.dialogForm.remark,callback:function(t){e.$set(e.dialogForm,"remark",t)},expression:"dialogForm.remark"}})],1)],1):e._e()]),t("template",{slot:"tool"},[t("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:e.clickCancleHandle}},[e._v(" 取消 ")]),t("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickConfirmHandle}},[e._v(" 确定 ")])],1)])],2)},n=[],i=r("ed08"),o=r("da92");function l(e){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},l(e)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return t};var e,t={},r=Object.prototype,a=r.hasOwnProperty,n=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function m(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{m({},"")}catch(e){m=function(e,t,r){return e[t]=r}}function p(e,t,r,a){var i=t&&t.prototype instanceof v?t:v,o=Object.create(i.prototype),l=new M(a||[]);return n(o,"_invoke",{value:O(e,r,l)}),o}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=p;var d="suspendedStart",h="suspendedYield",y="executing",b="completed",g={};function v(){}function _(){}function w(){}var k={};m(k,o,(function(){return this}));var E=Object.getPrototypeOf,L=E&&E(E(F([])));L&&L!==r&&a.call(L,o)&&(k=L);var x=w.prototype=v.prototype=Object.create(k);function C(e){["next","throw","return"].forEach((function(t){m(e,t,(function(e){return this._invoke(t,e)}))}))}function T(e,t){function r(n,i,o,s){var c=f(e[n],e,i);if("throw"!==c.type){var u=c.arg,m=u.value;return m&&"object"==l(m)&&a.call(m,"__await")?t.resolve(m.__await).then((function(e){r("next",e,o,s)}),(function(e){r("throw",e,o,s)})):t.resolve(m).then((function(e){u.value=e,o(u)}),(function(e){return r("throw",e,o,s)}))}s(c.arg)}var i;n(this,"_invoke",{value:function(e,a){function n(){return new t((function(t,n){r(e,a,t,n)}))}return i=i?i.then(n,n):n()}})}function O(t,r,a){var n=d;return function(i,o){if(n===y)throw Error("Generator is already running");if(n===b){if("throw"===i)throw o;return{value:e,done:!0}}for(a.method=i,a.arg=o;;){var l=a.delegate;if(l){var s=A(l,a);if(s){if(s===g)continue;return s}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(n===d)throw n=b,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);n=y;var c=f(t,r,a);if("normal"===c.type){if(n=a.done?b:h,c.arg===g)continue;return{value:c.arg,done:a.done}}"throw"===c.type&&(n=b,a.method="throw",a.arg=c.arg)}}}function A(t,r){var a=r.method,n=t.iterator[a];if(n===e)return r.delegate=null,"throw"===a&&t.iterator.return&&(r.method="return",r.arg=e,A(t,r),"throw"===r.method)||"return"!==a&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+a+"' method")),g;var i=f(n,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,g;var o=i.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,g):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function S(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function P(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function M(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(S,this),this.reset(!0)}function F(t){if(t||""===t){var r=t[o];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function r(){for(;++n<t.length;)if(a.call(t,n))return r.value=t[n],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(l(t)+" is not iterable")}return _.prototype=w,n(x,"constructor",{value:w,configurable:!0}),n(w,"constructor",{value:_,configurable:!0}),_.displayName=m(w,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===_||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,m(e,u,"GeneratorFunction")),e.prototype=Object.create(x),e},t.awrap=function(e){return{__await:e}},C(T.prototype),m(T.prototype,c,(function(){return this})),t.AsyncIterator=T,t.async=function(e,r,a,n,i){void 0===i&&(i=Promise);var o=new T(p(e,r,a,n),i);return t.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},C(x),m(x,u,"Generator"),m(x,o,(function(){return this})),m(x,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var a in t)r.push(a);return r.reverse(),function e(){for(;r.length;){var a=r.pop();if(a in t)return e.value=a,e.done=!1,e}return e.done=!0,e}},t.values=F,M.prototype={constructor:M,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(P),!t)for(var r in this)"t"===r.charAt(0)&&a.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(a,n){return l.type="throw",l.arg=t,r.next=a,n&&(r.method="next",r.arg=e),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],l=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var s=a.call(o,"catchLoc"),c=a.call(o,"finallyLoc");if(s&&c){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,g):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),P(r),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var a=r.completion;if("throw"===a.type){var n=a.arg;P(r)}return n}}throw Error("illegal catch attempt")},delegateYield:function(t,r,a){return this.delegate={iterator:F(t),resultName:r,nextLoc:a},"next"===this.method&&(this.arg=e),g}},t}function c(e,t,r,a,n,i,o){try{var l=e[i](o),s=l.value}catch(e){return void r(e)}l.done?t(s):Promise.resolve(s).then(a,n)}function u(e){return function(){var t=this,r=arguments;return new Promise((function(a,n){var i=e.apply(t,r);function o(e){c(i,a,n,o,l,"next",e)}function l(e){c(i,a,n,o,l,"throw",e)}o(void 0)}))}}var m={name:"ForLeaveRuleDialog",props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:""},width:{type:String,default:"500px"},selectInfo:{type:Object,default:function(){return{}}},memberCycle:{type:String,default:""},basePrice:{type:Number,default:0},isshow:Boolean,confirm:Function},data:function(){var e=this,t=function(e,t,r,a){if("count"===a){if(""===t)return r(new Error("不能为空"));var n=/^[1-9][0-9]{0,2}$/;n.test(t)?r():r(new Error("请输入大于零的正整数，上限为999"))}else r()},r=function(e,t,r,a){if(""===t)return r(new Error("不能为空"));var n=/^[0-9]{0,3}$/,i=parseInt(t);!n.test(t)||i>100?r(new Error("请输入大于零的正整数，上限为100,输入100表示不打折")):r()};return{isLoading:!1,dialogForm:{name:"",label:[],labelType:"all",discount:"",discountPrice:"",limitTimes:-1,times:"",remark:""},dialogFormRules:{name:[{required:!0,message:"请输入名称",trigger:"blur"}],labelType:[{required:!0,message:"请选择触发类型",trigger:"change"}],discount:[{required:!0,message:"请输入折扣",trigger:"blur"},{required:!0,validator:r,trigger:"blur"}],times:[{required:!0,validator:function(r,a,n){return t(r,a,n,e.dialogForm.limitTimes)},trigger:"blur"}]},labelList:[],cycleList:[],warnTip:"全部标签满足才触发优惠规则",defaultLimit:-1}},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},watch:{visible:function(){this.visible?(this.getMemberLabel(),this.getMemberCycle(),"edit"===this.type&&(this.dialogForm.name=this.selectInfo.name,this.dialogForm.label=this.selectInfo.member_labels,this.dialogForm.discount=this.selectInfo.discount,this.dialogForm.discountPrice=Object(i["i"])(this.selectInfo.origin_fee),this.dialogForm.labelType=this.selectInfo.trigger_type||"all",-1!==this.selectInfo.buy_count?(this.dialogForm.times=this.selectInfo.buy_count,this.dialogForm.limitTimes="count"):this.dialogForm.limitTimes=-1,this.dialogForm.remark=this.selectInfo.remark)):this.$refs.dialogFormRef.resetFields()}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getMemberLabel()},clickConfirmHandle:function(){var e=this;this.$refs.dialogFormRef.validate((function(t){if(t){var r,a={name:e.dialogForm.name,member_labels:e.dialogForm.label,trigger_type:e.dialogForm.labelType,member_cycle:e.memberCycle,discount:e.dialogForm.discount,origin_fee:Object(i["Y"])(e.dialogForm.discountPrice)};switch(-1!==e.dialogForm.limitTimes?a.buy_count=parseInt(e.dialogForm.times):a.buy_count=-1,e.dialogForm.remark&&(a.remark=e.dialogForm.remark),e.type){case"add":r=e.$apis.apiBackgroundMemberMemberChargeRuleAddPost(a);break;case"edit":a.id=Number(e.selectInfo.id),r=e.$apis.apiBackgroundMemberMemberChargeRuleModifyPost(a);break}e.confirmOperation(r)}}))},confirmOperation:function(e){var t=this;return u(s().mark((function r(){var a;return s().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!t.isLoading){r.next=2;break}return r.abrupt("return");case 2:return t.isLoading=!0,r.next=5,e;case 5:a=r.sent,t.isLoading=!1,0===a.code?(t.$message.success("成功"),t.confirm()):t.$message.error(a.msg);case 8:case"end":return r.stop()}}),r)})))()},clickCancleHandle:function(){this.visible=!1},handleClose:function(e){this.isLoading=!1,this.visible=!1,this.$refs.dialogFormRef.resetFields()},getMemberLabel:function(){var e=this;return u(s().mark((function t(){var r;return s().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundMemberMemberLabelListPost({page:1,page_size:99999});case 2:r=t.sent,0===r.code?e.labelList=r.data.results:e.$message.error(r.msg);case 4:case"end":return t.stop()}}),t)})))()},getMemberCycle:function(){var e=this;return u(s().mark((function t(){var r,a,n;return s().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundMemberMemberChargeRuleGetMemberCyclePost({page:1,page_size:99999});case 2:if(r=t.sent,0===r.code){for(n in a=[],r.data)a.push({value:n,label:r.data[n]});e.cycleList=a}else e.$message.error(r.msg);case 4:case"end":return t.stop()}}),t)})))()},labelTypeChange:function(e){this.warnTip="all"===e?"全部标签满足才触发优惠规则":"任一标签满足即触发规则"},discountInputChange:function(e){e&&(this.dialogForm.discountPrice=o["a"].times(o["a"].divide(this.basePrice,100),e))},limitTimesChange:function(e){-1===e&&this.$set(this.dialogForm,"times","")}}},p=m,f=(r("476b"),r("2877")),d=Object(f["a"])(p,a,n,!1,null,"741c4ffc",null);t["default"]=d.exports},c8c2:function(e,t,r){"use strict";r.r(t),r.d(t,"getRequestParams",(function(){return u})),r.d(t,"RECENTSEVEN",(function(){return m})),r.d(t,"DIC_OBTAIN_TYPE",(function(){return p})),r.d(t,"DIC_SEND_TYPE",(function(){return f})),r.d(t,"DIC_MEMBER_STATUS",(function(){return d})),r.d(t,"DIC_TRIGGER_TYPE",(function(){return h})),r.d(t,"DIC_MENBER_STATUS",(function(){return y})),r.d(t,"DIC_PERMISSION_TYPE",(function(){return b})),r.d(t,"DIC_MEMBER_CYCLE",(function(){return g})),r.d(t,"SEARCH_FORM_RECORD_DATA",(function(){return v})),r.d(t,"SEARCH_FORM_EXCLUSIVE_DATA",(function(){return _})),r.d(t,"TABLE_HEAD_RECORD_DATA",(function(){return w})),r.d(t,"TABLE_HEAD_EXCLUSIVE_DATA",(function(){return k})),r.d(t,"SEARCH_FORM_SMS_MANAGER_DATA",(function(){return E})),r.d(t,"TABLE_HEAD_SEND_DATA",(function(){return L})),r.d(t,"TABLE_HEAD_CHARGE_INDEX_DATA",(function(){return x})),r.d(t,"TABLE_HEAD_CHARGE_INDEX_CONTENT",(function(){return C})),r.d(t,"SEARCH_FORM_CHARGE_DETAIL_DATA",(function(){return T})),r.d(t,"TABLE_HEAD_CHARGE_DETAIL_DATA",(function(){return O})),r.d(t,"SEARCH_FORM_PERMISSION_MANAGER_DATA",(function(){return A})),r.d(t,"TABLE_HEAD_PERMISSION_MANAGER_DATA",(function(){return S})),r.d(t,"SEARCH_FORM_KEY_MANAGER_DATA",(function(){return P})),r.d(t,"TABLE_HEAD_KEY_MANAGER_DATA",(function(){return M})),r.d(t,"SEARCH_FORM_PROMOTIONAL_PICTURE_DATA",(function(){return F})),r.d(t,"TABLE_HEAD_PROMOTIONAL_PICTURE_DATA",(function(){return D}));var a=r("5a0c");function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach((function(t){l(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function l(e,t,r){return(t=s(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function s(e){var t=c(e,"string");return"symbol"==n(t)?t:t+""}function c(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=n(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var u=function(e,t,r){var a,n={};Object.keys(e).forEach((function(t){("select_time"!==t&&""!==e[t].value&&e[t].value&&0!==e[t].value.length||"boolean"===typeof e[t].value)&&(n[t]=e[t].value)}));var i=o({page:t,page_size:r},n);return 2===(null===(a=e.select_time)||void 0===a||null===(a=a.value)||void 0===a?void 0:a.length)&&(i.start_date=e.select_time.value[0],i.end_date=e.select_time.value[1]),i},m=[a().subtract(7,"day").format("YYYY-MM-DD"),a().format("YYYY-MM-DD")],p=[{name:"线上购买",value:"buy",label:"buy"},{name:"手动发放",value:"manual_release",label:"manual_release"}],f=[{name:"消费通知",value:"consume",label:"consume"},{name:"点餐提醒",value:"order",label:"order"},{name:"充值提醒",value:"recharge",label:"recharge"},{name:"余额提醒",value:"balance",label:"balance"},{name:"提现通知",value:"withdraw",label:"withdraw"},{name:"通行通知",value:"is_pass",label:"is_pass"}],d=[{name:"使用中",value:"using",label:"using"},{name:"已过期",value:"expired",label:"expired"},{name:"非会员",value:"non",label:"non"}],h=[{name:"全部标签",value:"all",label:"all"},{name:"任意标签",value:"any",label:"any"}],y=[{name:"开启",value:!0,label:!0},{name:"关闭",value:!1,label:!1}],b=[{name:"关联链接",value:"association"},{name:"固定模块",value:"fixed"}],g=[{name:"月卡",value:"month"},{name:"季卡",value:"season"},{name:"年卡",value:"year"},{name:"永久会员",value:"permanent"},{name:"畅享会员",value:"week"}],v={select_time:{type:"daterange",label:"购买时间",clearable:!0,format:"yyyy-MM-dd",value:m},member_permission_name:{type:"input",label:"权益",value:"",placeholder:"请输入权益"},nickname:{type:"input",label:"用户姓名",value:"",placeholder:"请输入用户姓名"},phone:{type:"input",label:"手机号",value:"",placeholder:"请输入手机号"},receive_type:{type:"select",value:"",label:"获取方式",dataList:p,multiple:!1,collapseTags:!0,listNameKey:"name",listValueKey:"value",clearable:!0}},_={member_permissions:{type:"select",value:[],label:"权益",dataList:[],multiple:!1,collapseTags:!0,listNameKey:"name",listValueKey:"id",clearable:!0}},w=[{label:"订单号",key:"trade_no",width:"200",fixed:"left"},{label:"购买时间",key:"create_time",width:"200"},{label:"权益",key:"member_permission_name",width:"140"},{label:"金额",key:"origin_fee",width:"140",type:"slot",slotName:"price"},{label:"有效期",key:"days",width:"120",type:"slot",slotName:"days"},{label:"用户姓名",key:"nickname",width:"150"},{label:"手机号",key:"phone",width:"150"},{label:"操作员",key:"operator_name"},{label:"获取方式",key:"receive_type_alias"}],k=[{label:"序号",key:"index",width:"100",fixed:"left"},{label:"权益",key:"member_permissions_name",width:"200",type:"slot",slotName:"permission"},{label:"有效期",key:"days",width:"200",type:"slot",slotName:"days"},{label:"价格",key:"origin_fee",width:"200",type:"slot",slotName:"price"},{label:"说明",key:"remark",width:"200"},{label:"操作员",key:"operator_name"},{label:"修改时间",key:"update_time",width:"200"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"200"}],E={select_time:{type:"daterange",label:"推送时间",clearable:!0,format:"yyyy-MM-dd",value:m},name:{type:"input",label:"用户名",value:"",placeholder:"请输用户名"},phone:{type:"input",label:"手机号",value:"",placeholder:"请输入手机号"},organization_id:{type:"select",multiple:!0,filterable:!0,collapseTags:!0,label:"消费点",value:[],placeholder:"请选择消费点",listNameKey:"name",listValueKey:"id",dataList:[]},company_id:{type:"CompanySelect",value:[],label:"所属项目点",dataList:[],multiple:!0,checkStrictly:!0,clearable:!0,labelWidth:"100px",companyOpts:{label:"name",value:"company"},role:"super"},receive_type:{type:"select",value:[],label:"推送类型",dataList:f,multiple:!1,collapseTags:!0,listNameKey:"name",clearable:!0}},L=[{label:"推送时间",key:"create_time",width:"200"},{label:"用户名",key:"user_name",width:"150"},{label:"手机号",key:"phone",width:"150"},{label:"所属项目点",key:"company_name",width:"150"},{label:"消费点",key:"organization_name",width:"150"},{label:"推送类型",key:"push_type_alias",width:"150"},{label:"推送内容",key:"remark"},{label:"短信服务商",key:"sms_type_alias"}],x=[{label:"会员类型",key:"member_cycle_alias",width:"140"},{label:"说明",key:"remark",width:"250",type:"slot",slotName:"remark",align:"center"},{label:"基础价格（元）",key:"origin_fee",type:"slot",slotName:"price"},{label:"操作时间",key:"update_time"},{label:"操作人",key:"operator_name"},{label:"操作",key:"contact_name",type:"slot",slotName:"operation"}],C=[{member_cycle_alias:"年卡",origin_fee:"155622",remark:"说明说明说明说明"},{member_cycle_alias:"季卡",origin_fee:"26",remark:"11111"},{member_cycle_alias:"月卡",origin_fee:"32",remark:"2222"},{member_cycle_alias:"畅享会员",price:"44",remark:"333"},{member_cycle_alias:"永久会员",origin_fee:"522",remark:"66666666"}],T={name:{type:"input",label:"名称",value:"",placeholder:"请输入名称"},member_labels:{type:"select",value:[],label:"会员标签",dataList:[],multiple:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",clearable:!0},trigger_type:{type:"select",value:[],label:"标签触发类型",dataList:h,collapseTags:!0,listNameKey:"name",listValueKey:"value",clearable:!0,labelWidth:"200"},is_enable:{type:"select",value:[],label:"状态",dataList:y,listNameKey:"name",listValueKey:"value",clearable:!0}},O=[{label:"名称",key:"name",width:"140"},{label:"会员标签",key:"member_labels",width:"250",type:"slot",slotName:"label"},{label:"标签触发类型",key:"trigger_type",type:"slot",slotName:"trigger"},{label:"优惠折扣",key:"discount"},{label:"优惠价",key:"origin_fee",type:"money"},{label:"限购次数",key:"buy_count",type:"slot",slotName:"buyCount"},{label:"说明",key:"remark",showTooltip:!0},{label:"操作时间",key:"update_time"},{label:"操作人",key:"operator_name"},{label:"状态",key:"status",type:"slot",slotName:"status"},{label:"操作",key:"contact_name",type:"slot",slotName:"operation"}],A={name:{type:"input",label:"权限名称",value:"",placeholder:"请输入权限名称"},type:{type:"select",value:[],label:"类型",dataList:b,collapseTags:!0,listNameKey:"name",listValueKey:"value",clearable:!0},associate_url:{type:"input",label:"关联链接",value:"",placeholder:"请输入关联链接"}},S=[{label:"权限名称",key:"name",width:"140"},{label:"图标",key:"icon_url",width:"250",type:"slot",slotName:"image"},{label:"类型",key:"type_alias"},{label:"键值",key:"permission_key"},{label:"关联链接",key:"associate_url",width:"250",type:"slot",slotName:"associateUrl"},{label:"权限说明",key:"remark",showTooltip:!0,width:"200"},{label:"操作时间",key:"update_time"},{label:"操作人",key:"operator_name"},{label:"是否需要会员",key:"status",type:"slot",slotName:"status"},{label:"操作",key:"contact_name",type:"slot",slotName:"operation"}],P={name:{type:"input",label:"名称",value:"",placeholder:"请输入名称"}},M=[{label:"名称",key:"name",width:"140"},{label:"键值",key:"permission_key"},{label:"说明",key:"remark"},{label:"操作时间",key:"update_time"},{label:"操作人",key:"operator_name"},{label:"操作",key:"operation",type:"slot",slotName:"operation"}],F={name:{type:"input",label:"权限名称",value:"",placeholder:"请输入权限名称"},type:{type:"select",value:[],label:"类型",dataList:b,collapseTags:!0,listNameKey:"name",listValueKey:"value",clearable:!0},associate_url:{type:"input",label:"关联链接",value:"",placeholder:"请输入关联链接"}},D=[{label:"名称",key:"name",width:"140"},{label:"图片",key:"url",width:"250",type:"slot",slotName:"image"},{label:"优先级",key:"priority"},{label:"状态",key:"is_release",type:"slot",slotName:"status"},{label:"显示界面",key:"ui_type_alias",type:"slot",slotName:"UIType"},{label:"操作时间",key:"update_time"},{label:"操作人",key:"operator_name"},{label:"操作",key:"contact_name",type:"slot",slotName:"operation"}]}}]);