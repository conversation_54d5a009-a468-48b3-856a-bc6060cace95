(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-merchant-admin-SupervisionChannel","view-super-merchant-admin-components-BankMerchant-merchantBaseInfo","view-super-merchant-admin-components-PermissionConfiguration","view-super-merchant-admin-components-accountManagement","view-super-merchant-admin-components-channelInfo","view-super-merchant-admin-components-functionConfiguration","view-super-merchant-admin-constants-bankMerchantConstants"],{1105:function(e,t,n){},1122:function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"ps-el-drawer"},[t("el-drawer",{attrs:{visible:e.visible,"show-close":!1,size:"75%"},scopedSlots:e._u([{key:"title",fn:function(){return[t("div",{staticClass:"ps-flex-align-c flex-align-c"},[t("div",{staticClass:"m-r-20"},[e._v("资金监管平台")]),t("div",{staticClass:"ps-el-popover"},[t("el-popover",{attrs:{placement:"bottom",title:"部分特殊权限控制说明：",width:"710",trigger:"hover"}},[t("div",{staticClass:"popover"},[e._v(" 留着看看有啥用 ")]),t("i",{staticClass:"el-icon-question",attrs:{slot:"reference"},slot:"reference"})])],1)])]},proxy:!0}])},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],staticClass:"p-20"},[t("el-tabs",{staticClass:"version-configuration-content-box",attrs:{type:"card","tab-position":"left"}},e._l(e.merchantFeatureList,(function(n,r){return t("el-tab-pane",{key:r,attrs:{label:n.verbose_name}},[t("div",{staticClass:"p-20"},[t("div",{staticClass:"m-b-10 w-150"},[t("el-checkbox",{staticClass:"ps-flex-align-c flex-align-c",attrs:{indeterminate:n.isIndeterminate,disabled:n.canNotSelect},model:{value:n.isSelect,callback:function(t){e.$set(n,"isSelect",t)},expression:"item.isSelect"}},[t("span",{staticClass:"font-size-18 f-w-500"},[e._v("全选当前页")])])],1),e._l(n.children,(function(n,r){return t("div",{key:r,staticClass:"m-b-20"},[t("div",{staticClass:"w-150"},[t("el-checkbox",{staticClass:"ps-flex-align-c flex-align-c m-b-10",attrs:{indeterminate:n.isIndeterminate,disabled:n.canNotSelect},model:{value:n.isSelect,callback:function(t){e.$set(n,"isSelect",t)},expression:"item1.isSelect"}},[t("span",{staticClass:"font-size-18 f-w-700"},[e._v(e._s(n.verbose_name))])])],1),t("div",{staticStyle:{"border-top":"1px solid #e5e7ea"}},e._l(n.children,(function(n,r){return t("div",{key:r,staticClass:"box-item flex-start",style:r%2===0?{backgroundColor:"#ffffff"}:{backgroundColor:"#f8f9fa"}},[t("div",{class:[n.children.length?"":"box-item-left","p-20"]},[t("el-checkbox",{attrs:{indeterminate:n.isIndeterminate},model:{value:n.isSelect,callback:function(t){e.$set(n,"isSelect",t)},expression:"item2.isSelect"}},[t("span",{staticClass:"ellipsis w-100"},[e._v(e._s(n.verbose_name))])])],1),t("div",{class:[n.children.length?"box-item-right1":"","p-20","w-100-p"]},e._l(n.children,(function(n,r){return t("div",{key:r},[t("el-checkbox",{attrs:{disabled:n.canNotSelect},model:{value:n.isSelect,callback:function(t){e.$set(n,"isSelect",t)},expression:"item3.isSelect"}},[t("span",{staticClass:"ellipsis w-150"},[e._v(e._s(n.verbose_name))])])],1)})),0),t("div",{class:[n.children.length?"box-item-right2":"","w-100-p"]},e._l(n.children,(function(r,a){return t("div",{key:a,staticClass:"three-level flex-start",style:a<n.children.length-1?{borderBottom:"1px solid #e5e7ea"}:{}},[t("el-checkbox",{staticClass:"p-20",attrs:{disabled:r.canNotSelect},model:{value:r.isSelect,callback:function(t){e.$set(r,"isSelect",t)},expression:"item3.isSelect"}},[t("span",{staticClass:"ellipsis w-150"},[e._v(e._s(r.verbose_name))])]),r.children.length?t("div",{staticClass:"three-level-right p-20 w-100-p"},e._l(r.children,(function(n,r){return t("div",{key:r},[t("el-checkbox",{attrs:{disabled:n.canNotSelect},model:{value:n.isSelect,callback:function(t){e.$set(n,"isSelect",t)},expression:"item4.isSelect"}},[t("span",{staticClass:"ellipsis w-150"},[e._v(e._s(n.verbose_name))])])],1)})),0):e._e()],1)})),0)])})),0)])}))],2)])})),1),t("div",{staticClass:"version-configuration-content-footer"},[t("div",{staticClass:"button-area m-r-40"},[t("el-button",{staticClass:"w-100",attrs:{size:"small"},on:{click:e.cancel}},[e._v("取消")]),t("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:e.save}},[e._v("保存")])],1),t("div",{staticClass:"checkbox-area m-r-40"},[t("el-checkbox",{on:{change:function(t){return e.isSelectAll("selectAll",!0)}},model:{value:e.selectAll,callback:function(t){e.selectAll=t},expression:"selectAll"}},[t("span",{staticClass:"font-size-16"},[e._v("全选")])]),t("el-checkbox",{on:{change:function(t){return e.isSelectAll("selectNone",!1)}},model:{value:e.selectNone,callback:function(t){e.selectNone=t},expression:"selectNone"}},[t("span",{staticClass:"font-size-16"},[e._v("全不选")])])],1),t("div",[e._v("定制数量："+e._s(1)+"/"+e._s(1))])])],1)])],1)},a=[];n("ed08");function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function o(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */o=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,a=Object.defineProperty||function(e,t,n){e[t]=n.value},s="function"==typeof Symbol?Symbol:{},l=s.iterator||"@@iterator",c=s.asyncIterator||"@@asyncIterator",u=s.toStringTag||"@@toStringTag";function d(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(e){d=function(e,t,n){return e[t]=n}}function h(e,t,n,r){var i=t&&t.prototype instanceof _?t:_,o=Object.create(i.prototype),s=new F(r||[]);return a(o,"_invoke",{value:T(e,n,s)}),o}function f(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=h;var p="suspendedStart",m="suspendedYield",g="executing",b="completed",v={};function _(){}function y(){}function w(){}var C={};d(C,l,(function(){return this}));var k=Object.getPrototypeOf,A=k&&k(k(N([])));A&&A!==n&&r.call(A,l)&&(C=A);var S=w.prototype=_.prototype=Object.create(C);function E(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,t){function n(a,o,s,l){var c=f(e[a],e,o);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==i(d)&&r.call(d,"__await")?t.resolve(d.__await).then((function(e){n("next",e,s,l)}),(function(e){n("throw",e,s,l)})):t.resolve(d).then((function(e){u.value=e,s(u)}),(function(e){return n("throw",e,s,l)}))}l(c.arg)}var o;a(this,"_invoke",{value:function(e,r){function a(){return new t((function(t,a){n(e,r,t,a)}))}return o=o?o.then(a,a):a()}})}function T(t,n,r){var a=p;return function(i,o){if(a===g)throw Error("Generator is already running");if(a===b){if("throw"===i)throw o;return{value:e,done:!0}}for(r.method=i,r.arg=o;;){var s=r.delegate;if(s){var l=L(s,r);if(l){if(l===v)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===p)throw a=b,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=g;var c=f(t,n,r);if("normal"===c.type){if(a=r.done?b:m,c.arg===v)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(a=b,r.method="throw",r.arg=c.arg)}}}function L(t,n){var r=n.method,a=t.iterator[r];if(a===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,L(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var i=f(a,t.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,v;var o=i.arg;return o?o.done?(n[t.resultName]=o.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,v):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function O(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function D(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function F(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(O,this),this.reset(!0)}function N(t){if(t||""===t){var n=t[l];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,o=function n(){for(;++a<t.length;)if(r.call(t,a))return n.value=t[a],n.done=!1,n;return n.value=e,n.done=!0,n};return o.next=o}}throw new TypeError(i(t)+" is not iterable")}return y.prototype=w,a(S,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:y,configurable:!0}),y.displayName=d(w,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,d(e,u,"GeneratorFunction")),e.prototype=Object.create(S),e},t.awrap=function(e){return{__await:e}},E(x.prototype),d(x.prototype,c,(function(){return this})),t.AsyncIterator=x,t.async=function(e,n,r,a,i){void 0===i&&(i=Promise);var o=new x(h(e,n,r,a),i);return t.isGeneratorFunction(n)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},E(S),d(S,u,"Generator"),d(S,l,(function(){return this})),d(S,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=N,F.prototype={constructor:F,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(D),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function a(r,a){return s.type="throw",s.arg=t,n.next=r,a&&(n.method="next",n.arg=e),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var l=r.call(o,"catchLoc"),c=r.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),D(n),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var a=r.arg;D(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:N(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),v}},t}function s(e,t,n,r,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,a)}function l(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function o(e){s(i,r,a,o,l,"next",e)}function l(e){s(i,r,a,o,l,"throw",e)}o(void 0)}))}}var c={props:{isShow:Boolean},data:function(){return{isLoading:!1,merchantFeatureList:[],selectNone:!1,selectAll:!1,haveBeenSelectKey:[],versionList:[],selectedVersionData:{}}},computed:{visible:{get:function(){return this.isShow},set:function(e){this.$emit("update:isShow",e)}}},watch:{selectAll:function(e,t){e&&this.selectNone&&(this.selectNone=!1)},selectNone:function(e,t){e&&this.selectAll&&(this.selectAll=!1)},visible:function(e){}},created:function(){},methods:{getPermissionList:function(){return l(o().mark((function e(){return o().wrap((function(e){while(1)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e)})))()},save:function(){this.selectAll=!1,this.selectNone=!1,this.visible=!1},cancel:function(){this.selectAll=!1,this.selectNone=!1,this.visible=!1}}},u=c,d=(n("7f4b"),n("2877")),h=Object(d["a"])(u,r,a,!1,null,"b866637c",null);t["default"]=h.exports},"1e28":function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"channel-info-content"},[t("el-form",{attrs:{"label-position":"left","label-width":"100px"}},[t("el-form-item",{attrs:{label:"数据大屏："}},[t("div",{staticClass:"table-style"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],attrs:{data:e.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},e._l(e.tableSetting,(function(n,r){return t("table-column",{key:r,attrs:{col:n},scopedSlots:e._u([{key:"status",fn:function(n){var r=n.row;return[t("el-switch",{on:{change:function(t){return e.changeStatus(r)}},model:{value:r.isUsing,callback:function(t){e.$set(r,"isUsing",t)},expression:"row.isUsing"}})]}}],null,!0)})})),1)],1)])],1)],1)},a=[],i=n("ed08"),o={props:{organizationData:{type:Object,default:function(){return{}}},tabType:{type:String,default:""}},data:function(){return{isLoading:!1,tableData:[],tableSetting:[{label:"大屏名称",key:"name"},{label:"地址",key:"link_address"},{label:"备注",key:"remark"},{label:"操作时间",key:"operateTime"},{label:"操作人",key:"operator"},{label:"使用状态（启用/禁用）",key:"status",type:"slot",slotName:"status"}],drawerType:""}},watch:{tabType:{handler:function(e,t){"functionConfiguration"===e&&this.getDataList()},immediate:!0}},methods:{getDataList:function(){var e=this;this.isLoading=!0,this.$apis.apiBackgroundFundSupervisionSupervisionChannelScreenListPost({supervision_channel_id:this.organizationData.id}).then((function(t){if(e.isLoading=!1,0===t.code){var n=[];t.data.results&&t.data.results.length&&(n=t.data.results.map((function(e){return Object.assign(e,{isUsing:!1}),"enable"===e.status?e.isUsing=!0:e.isUsing=!1,e}))),e.tableData=Object(i["f"])(n||[])}else e.$message.error(t.msg)}))},changeStatus:function(e){var t=this;this.$apis.apiBackgroundFundSupervisionSupervisionChannelScreenModifyPost({id:e.id,status:e.isUsing?"enable":"disable"}).then((function(e){0===e.code?t.$message.success("修改成功"):t.$message.error(e.msg),t.getDataList()}))}}},s=o,l=(n("ad6e"),n("2877")),c=Object(l["a"])(s,r,a,!1,null,"3b1108bb",null);t["default"]=c.exports},"3c35f":function(e,t){(function(t){e.exports=t}).call(this,{})},"3ef7":function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"channel-info-content"},[t("el-form",{attrs:{"label-position":"right","label-width":"120px",model:e.formLabelAlign}},[t("el-form-item",{attrs:{label:"所属上级："}},[t("div",[e._v(e._s(e.formLabelAlign.father||"--"))])]),t("el-form-item",{attrs:{label:"渠道名称："}},[t("div",{staticClass:"ps-flex-align-c"},[t("div",{staticClass:"m-r-20"},[e._v(e._s(e.formLabelAlign.name||"--"))]),t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.showEditChannelDrawer(e.formLabelAlign.name)}}},[e._v("修改")])],1)]),t("el-form-item",{attrs:{label:"跳转链接："}},[t("el-button",{attrs:{type:"text",disabled:!e.formLabelAlign.token},on:{click:function(t){return e.gotoPage(e.formLabelAlign.token)}}},[e._v("登录")])],1),t("el-form-item",{attrs:{label:"绑定组织："}},[t("div",{staticClass:"flex-b-c"},[t("el-button",{staticClass:"m-r-20",attrs:{type:"text"},on:{click:e.gotoBind}},[e._v("去绑定")]),t("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"",placement:"top-end"}},[t("div",{staticClass:"text-center",attrs:{slot:"content"},slot:"content"},[e._v(" 进行绑定操作时，如上级/下级已被绑定，请先"),t("br"),e._v("解绑后再进行操作，解绑前请自行记录当前绑"),t("br"),e._v("定组织的数据权限。绑定后可根据数据需求进"),t("br"),e._v("行组织树的权限控制。 ")]),t("i",{staticClass:"el-icon-question font-size-24"})])],1),t("div",{staticClass:"table-style"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],attrs:{data:e.formLabelAlign.org,stripe:"","header-row-class-name":"ps-table-header-row"}},e._l(e.tableSetting,(function(n,r){return t("table-column",{key:r,attrs:{col:n},scopedSlots:e._u([{key:"updateTime",fn:function(t){var n=t.row;return[e._v(" "+e._s(e.computedTime(n.update_time))+" ")]}},{key:"operation",fn:function(n){var r=n.row;return[t("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.setPermission(r)}}},[e._v("数据权限")]),t("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.removeHandle(r)}}},[e._v("移除")])]}}],null,!0)})})),1)],1)])],1),t("div",{staticClass:"ps-el-drawer"},[t("el-drawer",{attrs:{title:"修改渠道名称",visible:e.editChannelDrawerShow,"show-close":!1,size:"40%"}},[t("div",{staticClass:"p-20"},[t("el-form",{ref:"editChannelFormRef",attrs:{model:e.editChannelForm,"label-width":"80px","label-position":"left"}},[t("el-form-item",{attrs:{label:"渠道名称",prop:"name",rules:[{required:!0,message:"请输入渠道名称",trigger:["change","blur"]}]}},[t("el-input",{staticClass:"w-300",attrs:{placeholder:"请输入渠道名称，不超过30个字",maxlength:"30"},model:{value:e.editChannelForm.name,callback:function(t){e.$set(e.editChannelForm,"name",t)},expression:"editChannelForm.name"}})],1)],1),t("div",{staticClass:"ps-el-drawer-footer"},[t("el-button",{staticClass:"w-100",attrs:{size:"small"},on:{click:function(t){return e.cancelHandle("edit")}}},[e._v("取消")]),t("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:function(t){return e.saveHandle("edit")}}},[e._v("保存")])],1)],1)]),t("el-drawer",{attrs:{title:"绑定组织",visible:e.bindOrgDrawerShow,"show-close":!1,size:"40%"}},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.checkChannelLoading,expression:"checkChannelLoading"}],staticClass:"p-20"},[t("div",{staticClass:"red m-b-10 font-size-14"},[e._v("绑定需要保存后生效。本次新增绑定组织："+e._s(e.addCount)+"个")]),t("el-form",{ref:"bindOrgFormRef",attrs:{model:e.bindOrgForm,"label-width":"80px","label-position":"left"}},[t("el-form-item",{attrs:{label:"项目名称",prop:"name",rules:{required:!e.bindOrgForm.address,message:"请输入项目名称",trigger:["change","blur"]}}},[t("el-select",{staticClass:"w-300",attrs:{filterable:"",clearable:"",placeholder:"输入项目点名称进行查询"},model:{value:e.bindOrgForm.name,callback:function(t){e.$set(e.bindOrgForm,"name",t)},expression:"bindOrgForm.name"}},e._l(e.orgList,(function(e,n){return t("el-option",{key:n,attrs:{label:e.name,value:e.id}})})),1)],1),t("el-form-item",{attrs:{label:"项目地址",prop:"address",rules:{required:!e.bindOrgForm.name,message:"请输入项目地址",trigger:["change","blur"]}}},[t("el-cascader",{ref:"cascaderRef",staticClass:"w-300 m-r-20",attrs:{placeholder:"请选择项目点地址进行查询",options:e.addrOptions,filterable:""},model:{value:e.bindOrgForm.address,callback:function(t){e.$set(e.bindOrgForm,"address",t)},expression:"bindOrgForm.address"}}),t("el-button",{staticClass:"ps-origin-btn",on:{click:e.getBingOrgList}},[e._v("查询")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.bindLoading,expression:"bindLoading"}],attrs:{data:e.bindOrgTableData,stripe:"","header-row-class-name":"ps-table-header-row","row-key":"id","default-expand-all":"","tree-props":{children:"children_list",hasChildren:"hasChildren"}}},e._l(e.bindOrgTableSetting,(function(n,r){return t("table-column",{key:n.key+r,attrs:{col:n},scopedSlots:e._u([{key:"districtAlias",fn:function(n){var r=n.row;return[t("span",[e._v(e._s(e.computedAddress(r.district_alias)))])]}},{key:"operation",fn:function(n){var r=n.row;return[r.supervision_channel_bind?t("div",[t("el-popconfirm",{attrs:{title:"解绑后项目数据将不再上传，确定要解绑？"},on:{confirm:function(t){return e.bindHandle(r,"unBind")}}},[t("el-button",{staticClass:"ps-red",attrs:{slot:"reference",type:"text",size:"small",disabled:0!==r.supervision_channel_id&&e.organizationData.id!==r.supervision_channel_id},slot:"reference"},[e._v("解绑")])],1)],1):t("div",[t("el-button",{staticClass:"ps-origin-text",attrs:{type:"text",size:"small",disabled:!r.isCanBind},on:{click:function(t){return e.bindHandle(r,"unBind")}}},[e._v("绑定")])],1)]}}],null,!0)})})),1),t("div",{staticClass:"ps-el-drawer-footer"},[t("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:function(t){return e.cancelHandle("bind")}}},[e._v("关闭")])],1)],1)]),t("el-drawer",{attrs:{title:"数据权限",visible:e.dataPermissionDrawerShow,"show-close":!1,size:"40%"}},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.dataPermissionLoading,expression:"dataPermissionLoading"}],staticClass:"p-20"},[t("span",[e._v("注：默认计算绑定的组织及下级组织的数据，被选择的组织为禁用状态，被禁用的组织将不统计该项数据。仅适用绑定组织为只计算绑定的组织数据其下级组织全为禁用状态。")]),t("el-form",{ref:"dataPermissionDataRef",attrs:{model:e.dataPermissionData,"label-position":"right","label-width":"100px"}},e._l(e.dataPermissionData.list,(function(n,r){return t("el-form-item",{key:r,attrs:{label:n.label}},[t("el-switch",{attrs:{"active-text":"全部禁用"},on:{change:function(t){return e.allIsOpenClose(n)}},model:{value:n.isAllClose,callback:function(t){e.$set(n,"isAllClose",t)},expression:"item.isAllClose"}}),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],attrs:{"show-header":!1,data:n.orgData,stripe:"","header-row-class-name":"ps-table-header-row","default-expand-all":"","row-key":"id","tree-props":{children:"children_list",hasChildren:"hasChildren"}}},e._l(e.permissionSetting,(function(r,a){return t("table-column",{key:a,attrs:{col:r},scopedSlots:e._u([{key:"operation",fn:function(r){var a=r.row;return[t("el-radio-group",{on:{input:function(t){return e.changeOrgTreeStatus(e.orgTree,a,n.dataType)}},model:{value:a.isOpen,callback:function(t){e.$set(a,"isOpen",t)},expression:"row.isOpen"}},[t("el-radio",{attrs:{label:"1"}},[e._v("启用")]),t("el-radio",{attrs:{label:"0"}},[e._v("禁用")])],1),t("el-checkbox",{staticClass:"m-l-10",on:{change:function(t){return e.selectThisIsOpen(a,n.orgData)}},model:{value:a.selectThisRow,callback:function(t){e.$set(a,"selectThisRow",t)},expression:"row.selectThisRow"}},[e._v("仅当前")])]}}],null,!0)})})),1)],1)})),1),t("div",{staticClass:"ps-el-drawer-footer"},[t("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:function(t){return e.cancelHandle("permission")}}},[e._v("关闭")]),t("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:function(t){return e.saveHandle("permission")}}},[e._v("保存")])],1)],1)])],1),t("PermissionConfiguration",{attrs:{isShow:e.permissionConfigurationShow}})],1)},a=[],i=n("ed08"),o=n("ddcc"),s=n("ef6c"),l=n("1122"),c=n("5a0c"),u=n.n(c);function d(e){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},d(e)}function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function f(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach((function(t){p(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function p(e,t,n){return(t=m(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function m(e){var t=g(e,"string");return"symbol"==d(t)?t:t+""}function g(e,t){if("object"!=d(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=d(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function b(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */b=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,a=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function u(e,t,n,r){var i=t&&t.prototype instanceof _?t:_,o=Object.create(i.prototype),s=new F(r||[]);return a(o,"_invoke",{value:T(e,n,s)}),o}function h(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var f="suspendedStart",p="suspendedYield",m="executing",g="completed",v={};function _(){}function y(){}function w(){}var C={};c(C,o,(function(){return this}));var k=Object.getPrototypeOf,A=k&&k(k(N([])));A&&A!==n&&r.call(A,o)&&(C=A);var S=w.prototype=_.prototype=Object.create(C);function E(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,t){function n(a,i,o,s){var l=h(e[a],e,i);if("throw"!==l.type){var c=l.arg,u=c.value;return u&&"object"==d(u)&&r.call(u,"__await")?t.resolve(u.__await).then((function(e){n("next",e,o,s)}),(function(e){n("throw",e,o,s)})):t.resolve(u).then((function(e){c.value=e,o(c)}),(function(e){return n("throw",e,o,s)}))}s(l.arg)}var i;a(this,"_invoke",{value:function(e,r){function a(){return new t((function(t,a){n(e,r,t,a)}))}return i=i?i.then(a,a):a()}})}function T(t,n,r){var a=f;return function(i,o){if(a===m)throw Error("Generator is already running");if(a===g){if("throw"===i)throw o;return{value:e,done:!0}}for(r.method=i,r.arg=o;;){var s=r.delegate;if(s){var l=L(s,r);if(l){if(l===v)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===f)throw a=g,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=m;var c=h(t,n,r);if("normal"===c.type){if(a=r.done?g:p,c.arg===v)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(a=g,r.method="throw",r.arg=c.arg)}}}function L(t,n){var r=n.method,a=t.iterator[r];if(a===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,L(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var i=h(a,t.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,v;var o=i.arg;return o?o.done?(n[t.resultName]=o.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,v):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function O(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function D(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function F(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(O,this),this.reset(!0)}function N(t){if(t||""===t){var n=t[o];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,i=function n(){for(;++a<t.length;)if(r.call(t,a))return n.value=t[a],n.done=!1,n;return n.value=e,n.done=!0,n};return i.next=i}}throw new TypeError(d(t)+" is not iterable")}return y.prototype=w,a(S,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:y,configurable:!0}),y.displayName=c(w,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,c(e,l,"GeneratorFunction")),e.prototype=Object.create(S),e},t.awrap=function(e){return{__await:e}},E(x.prototype),c(x.prototype,s,(function(){return this})),t.AsyncIterator=x,t.async=function(e,n,r,a,i){void 0===i&&(i=Promise);var o=new x(u(e,n,r,a),i);return t.isGeneratorFunction(n)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},E(S),c(S,l,"Generator"),c(S,o,(function(){return this})),c(S,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=N,F.prototype={constructor:F,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(D),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function a(r,a){return s.type="throw",s.arg=t,n.next=r,a&&(n.method="next",n.arg=e),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var l=r.call(o,"catchLoc"),c=r.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),D(n),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var a=r.arg;D(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:N(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),v}},t}function v(e,t,n,r,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,a)}function _(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function o(e){v(i,r,a,o,s,"next",e)}function s(e){v(i,r,a,o,s,"throw",e)}o(void 0)}))}}var y={components:{PermissionConfiguration:l["default"]},props:{organizationData:{type:Object,default:function(){return{}}},tabType:{type:String,default:""}},data:function(){return{isLoading:!1,bindLoading:!1,checkChannelLoading:!1,formLabelAlign:{father:"",name:"",token:"",org:[]},tableSetting:[{label:"绑定组织",key:"org_name"},{label:"所属项目",key:"company_name"},{label:"修改时间",key:"update_time",type:"slot",slotName:"updateTime"},{label:"操作人",key:"operator_name"},{label:"操作",key:"operation",type:"slot",slotName:"operation"}],editChannelDrawerShow:!1,editChannelForm:{name:""},bindOrgDrawerShow:!1,bindOrgForm:{name:"",address:""},orgList:[],addrOptions:s["regionData"],bindOrgTableData:[],bindOrgTableSetting:o["DEFAULT_CHANNEL_TABLE_SETTING"],permissionSetting:[{label:"组织名称",key:"name",align:"left"},{label:"操作",key:"operation",type:"slot",slotName:"operation"}],dataPermissionDrawerShow:!1,dataPermissionLoading:!1,dataPermissionData:{list:[{isAllClose:!1,dataType:"revenue_data",label:"收入数据",orgData:[]},{isAllClose:!1,dataType:"expenditure_data",label:"支出数据",orgData:[]},{isAllClose:!1,dataType:"recorded_data",label:"入账数据",orgData:[]},{isAllClose:!1,dataType:"supplier_information",label:"供应商信息",orgData:[]},{isAllClose:!1,dataType:"purchasing_data",label:"采购数据",orgData:[]},{isAllClose:!1,dataType:"in_out_data",label:"出入库数据",orgData:[]},{isAllClose:!1,dataType:"recipe_data",label:"食谱数据",orgData:[]},{isAllClose:!1,dataType:"sample_data",label:"留样数据",orgData:[]},{isAllClose:!1,dataType:"monitoring_data",label:"监控数据",orgData:[]}]},orgTree:[],addCount:0,selectRowData:{},permissionConfigurationShow:!1}},computed:{computedTime:function(){return function(e){return u()(e).format("YYYY-MM-DD HH:mm:ss")}},computedAddress:function(){return function(e){var t=e.replace(/^\[|\]$/g,"");return t}}},watch:{organizationData:{handler:function(e,t){e&&(this.formLabelAlign.father=e.parent_alias||"--",this.formLabelAlign.name=e.name||"--",this.formLabelAlign.token=e.get_login_token||"",this.formLabelAlign.org=Object(i["f"])(e.binded_org_info||[]))},immediate:!0,deep:!0}},created:function(){this.getOrganizationList()},methods:{cancelHandle:function(e){switch(e){case"edit":this.$refs.editChannelFormRef.resetFields(),this.editChannelDrawerShow=!1;break;case"bind":this.bindOrgDrawerShow=!1,this.$emit("refresh",this.organizationData);break;case"permission":this.$refs.dataPermissionDataRef.resetFields(),this.dataPermissionDrawerShow=!1;break}},showEditChannelDrawer:function(e){this.editChannelForm.name=e,this.editChannelDrawerShow=!0},saveHandle:function(e){switch(e){case"edit":this.changeName();break;case"permission":this.savePermission();break}},gotoBind:function(){this.addCount=0,this.bindOrgDrawerShow=!0},bindHandle:function(e,t){var n=this;return _(b().mark((function r(){var a;return b().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return a={supervision_channel_id:n.organizationData.id,org_id:e.id,bind_type:!e.supervision_channel_bind},r.next=3,n.$apis.apiBackgroundFundSupervisionSupervisionChannelOrgBingConfigPost(a).then((function(r){0===r.code?(e.supervision_channel_bind||n.addCount++,n.$message.success(e.supervision_channel_bind?"解绑成功":"绑定成功"),"unBind"===t?(n.bindOrgTableData=[],n.getBingOrgList()):"remove"===t&&(n.bindOrgTableData=[],n.$emit("refresh",n.organizationData))):n.$message.error(r.msg)}));case 3:case"end":return r.stop()}}),r)})))()},changeName:function(){var e=this,t={id:this.organizationData.id,name:this.editChannelForm.name};this.$apis.apiBackgroundFundSupervisionSupervisionChannelModifyPost(t).then((function(t){0===t.code?e.$message.success("修改成功"):e.$message.error(t.msg),e.editChannelDrawerShow=!1,e.$emit("refresh",e.organizationData)}))},savePermission:function(){var e=this,t=[];this.setConfigArr(this.orgTree,t),this.$apis.apiBackgroundFundSupervisionSupervisionChannelOrgPermissionConfigPost({supervision_channel_id:this.organizationData.id,config:t}).then((function(t){0===t.code?e.$message.success("保存成功"):e.$message.error(t.msg),e.dataPermissionDrawerShow=!1,e.$emit("refresh",e.organizationData)}))},allIsOpenClose:function(e){var t=this;e.isAllClose&&this.$apis.apiBackgroundFundSupervisionSupervisionChannelBatchDisableOrgPermissionPost({org_id:this.selectRowData.org_id,data_type:e.dataType}).then((function(e){0===e.code?t.setPermission(t.selectRowData):t.$message.error(e.msg)}))},changeOrgTreeStatus:function(e,t,n){var r=this;e.forEach((function(e){e.id===t.id?e[n]="1"===t.isOpen:e.has_children&&e.children_list.length&&r.changeOrgTreeStatus(e.children_list,t,n)}))},setConfigArr:function(e,t){var n=this;e.length&&e.forEach((function(e){var r={organization_id:e.id,revenue_data:e.revenue_data,expenditure_data:e.expenditure_data,recorded_data:e.recorded_data,purchasing_data:e.purchasing_data,supplier_information:e.supplier_information,in_out_data:e.in_out_data,recipe_data:e.recipe_data,sample_data:e.sample_data,monitoring_data:e.monitoring_data};t.push(r),e.has_children&&e.children_list.length&&n.setConfigArr(e.children_list,t)}))},getOrganizationList:function(){var e=this;this.checkChannelLoading=!0,this.$apis.apiBackgroundAdminOrganizationListPost({page:1,page_size:9999,parent__is_null:"1",status__in:["enable"]}).then((function(t){e.checkChannelLoading=!1,0===t.code?e.orgList=t.data.results.map((function(e){var t={id:e.id,name:e.name};return t})):e.$message.error(t.msg)}))},gotoPage:function(){window.open("https://baidu.com","_blank")},getBingOrgList:function(){var e=this;this.$refs.bindOrgFormRef.validate((function(t){t?(e.bindLoading=!0,e.$apis.apiBackgroundFundSupervisionSupervisionChannelTreeListPost({org_id:e.bindOrgForm.name||void 0,district:e.bindOrgForm.address?JSON.stringify(e.bindOrgForm.address):void 0}).then((function(t){if(0===t.code){var n=[];if(t.data.length){var r=t.data.map((function(e){return e[0]}));n=Object(i["f"])(r)}else n=[];e.addStatus(n),e.setIsCanBind(n);var a=new Map;e.treeToMap(n,a),a.forEach((function(t){t.supervision_channel_bind&&t.parent&&e.findAndSet(t,a)})),e.bindOrgTableData=Object(i["f"])(n),e.bindLoading=!1}else e.$message.error(t.msg)}))):e.$message.error("查询条件不能为空")}))},findAndSet:function(e,t){var n=t.get(e.parent);n.isCanBind=!1,t.set(e.parent,f({},n)),n.parent&&this.findAndSet(n,t)},treeToMap:function(e,t){var n=this;e.forEach((function(e){t.set(e.id,e),e.children_list&&e.children_list.length>0&&n.treeToMap(e.children_list,t)}))},setIsCanBind:function(e){var t=this;e.forEach((function(e){e.supervision_channel_bind?(e.isCanBind=!1,e.children_list&&e.children_list.length&&t.setFalseStatus(e.children_list)):(e.isCanBind=!0,e.children_list&&e.children_list.length&&t.setIsCanBind(e.children_list))}))},setFalseStatus:function(e){var t=this;e.forEach((function(e){e.isCanBind=!1,e.supervision_channel_bind=!1,e.children_list&&e.children_list.length&&t.setFalseStatus(e.children_list)}))},addStatus:function(e){var t=this;e.length&&e.forEach((function(e){Object.assign(e,{isCanBind:!0}),e.has_children&&e.children_list.length&&t.addStatus(e.children_list)}))},load:function(e,t,n){var r=this,a={supervision_channel_id:this.organizationData.id,parent__in:e.tree_id};this.$apis.apiBackgroundFundSupervisionSupervisionChannelBingOrgListPost(a).then((function(e){0===e.code?n(e.data.results||[]):r.$message.error(e.msg)}))},setPermission:function(e){var t=this;this.selectRowData=Object(i["f"])(e),this.dataPermissionDrawerShow=!0,this.$nextTick((function(){t.dataPermissionLoading=!0})),this.$apis.apiBackgroundFundSupervisionSupervisionChannelGetOrgPermissionPost({org_id:e.org_id}).then((function(e){t.dataPermissionLoading=!1,0===e.code?(t.addIsOpen(e.data),t.orgTree=Object(i["f"])(e.data),t.dataPermissionData.list.forEach((function(e){e.orgData=Object(i["f"])(t.orgTree),t.changeIsOpenStatus(e.orgData,e.dataType)}))):t.$message.error(e.msg)}))},addIsOpen:function(e){var t=this;e.length&&e.forEach((function(e){Object.assign(e,{isOpen:"0",selectThisRow:!1}),e.has_children&&e.children_list.length&&t.addIsOpen(e.children_list)}))},changeIsOpenStatus:function(e,t){var n=this;e.length&&e.forEach((function(e){e[t]?e.isOpen="1":e.isOpen="0",e.has_children&&e.children_list.length&&n.changeIsOpenStatus(e.children_list,t)}))},selectThisIsOpen:function(e,t){var n=this;t.length&&t.forEach((function(t){t.id===e.id?t.isOpen="1":(t.isOpen="0",t.selectThisRow=!1),t.has_children&&t.children_list.length&&n.selectThisIsOpen(e,t.children_list)}))},removeHandle:function(e){var t=this;this.$confirm("您正在移除组织：".concat(e.company_name,"。移除后组织数据将不再计入渠道，请谨慎操作。确定要移除该组织？"),"提示",{distinguishCancelAndClose:!0,confirmButtonText:"确定",cancelButtonText:"取消"}).then((function(){var n={id:e.org_id,supervision_channel_bind:!0};t.bindHandle(n,"remove")})).catch((function(e){t.$message("已取消")}))},showPermissionConfiguration:function(){this.permissionConfigurationShow=!0}}},w=y,C=(n("7227"),n("2877")),k=Object(C["a"])(w,r,a,!1,null,"ab01560c",null);t["default"]=k.exports},7227:function(e,t,n){"use strict";n("c0fa")},7328:function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"container-wrapper has-organization"},[t("refresh-tool",{on:{refreshPage:e.refreshHandle}}),t("div",{staticClass:"super-organization"},[t("div",{staticClass:"organization-tree"},[t("div",{staticClass:"m-b-20"},[t("el-button",{staticClass:"ps-origin-btn",attrs:{size:"mini"},on:{click:function(t){e.checkChannelDrawerShow=!0}}},[e._v("查询")]),t("el-button",{staticClass:"ps-origin-btn",attrs:{size:"mini"},on:{click:function(t){return e.showAddChannelDrawer("parent")}}},[e._v("新建")])],1),t("el-input",{staticClass:"tree-search ps-input",attrs:{type:"primary",placeholder:"请输入",clearable:""},model:{value:e.treeFilterText,callback:function(t){e.treeFilterText=t},expression:"treeFilterText"}}),t("el-tree",{directives:[{name:"loading",rawName:"v-loading",value:e.treeLoading,expression:"treeLoading"}],ref:"treeRef",class:{"tree-box":e.selectId},attrs:{data:e.treeList,props:e.treeProps,load:e.loadTree,lazy:e.isLazy,"check-on-click-node":!0,"expand-on-click-node":!1,"highlight-current":!0,"current-node-key":e.selectId,"node-key":"id"},on:{"node-click":function(t){return e.treeHandleNodeClick(t)}},scopedSlots:e._u([{key:"default",fn:function(n){var r=n.node,a=n.data;return t("div",{staticClass:"custom-tree-node"},[t("span",{staticClass:"ellipsis tree-lable"},[e._v(" "+e._s(r.label)+" "),"disable"===a.status?t("span",{staticClass:"stop-box"},[e._v("停")]):e._e()]),t("span",[t("el-popover",{attrs:{placement:"right-start",width:"auto","popper-class":"custon-tree-popper",trigger:"hover"}},[t("div",{class:["popover-btn-box",e.treeLoading?"no-pointer":""]},[t("el-button",{attrs:{disabled:"disable"===a.status,type:"text"},on:{click:function(t){return e.addChildTreeHandle("child",a)}}},[e._v("添加下级")]),t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.changeStatus(a)}}},[e._v("停用/启用")])],1),t("i",{staticClass:"el-icon-more tree-icon",attrs:{slot:"reference"},slot:"reference"})])],1)])}}])})],1),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],staticClass:"organization-r"},[t("div",{key:"tab",staticClass:"organization-tab-group"},e._l(e.tabList,(function(n){return t("div",{key:n.value,class:["organization-tab",n.value===e.tabType?"is-checked":"",n.disable?"is-disable":""],attrs:{label:n.value},on:{click:function(t){return e.clickTabHandle(n)}}},[t("span",{staticClass:"tab-label"},[e._v(e._s(n.name))])])})),0),t("transition-group",{attrs:{name:e.slideTransition}},["channelInfo"===e.tabType?t("div",{key:"channelInfo"},[e.isLoading?e._e():t("channel-info",{attrs:{tabType:e.tabType,organizationData:e.selectTree},on:{refresh:e.refresh}})],1):e._e(),"accountManagement"===e.tabType?t("div",{key:"accountManagement"},[e.isLoading?e._e():t("account-management",{attrs:{tabType:e.tabType,organizationData:e.selectTree}})],1):e._e(),"functionConfiguration"===e.tabType?t("div",{key:"functionConfiguration"},[e.isLoading?e._e():t("function-configuration",{attrs:{tabType:e.tabType,organizationData:e.selectTree}})],1):e._e()])],1)]),t("div",{staticClass:"ps-el-drawer"},[t("el-drawer",{attrs:{title:"新建渠道",visible:e.addChannelDrawerShow,"show-close":!1,size:"40%"}},[t("div",{staticClass:"p-20"},[t("el-form",{ref:"addChannelFormRef",attrs:{model:e.addChannelForm,"label-width":"80px","label-position":"left"}},[t("el-form-item",{attrs:{label:"渠道名称",prop:"name",rules:[{required:!0,message:"请输入渠道名称",trigger:["change","blur"]}]}},[t("el-input",{staticClass:"w-300",attrs:{placeholder:"请输入渠道名称，不超过30个字",maxlength:"30"},model:{value:e.addChannelForm.name,callback:function(t){e.$set(e.addChannelForm,"name",t)},expression:"addChannelForm.name"}})],1)],1),t("div",{staticClass:"ps-el-drawer-footer"},[t("el-button",{staticClass:"w-100",attrs:{size:"small"},on:{click:function(t){return e.cancelHandle("addChannelForm")}}},[e._v("取消")]),t("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:e.saveHandle}},[e._v("保存")])],1)],1)]),t("el-drawer",{attrs:{title:"组织查询",visible:e.checkChannelDrawerShow,"show-close":!1,size:"40%"}},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.checkChannelLoading,expression:"checkChannelLoading"}],staticClass:"p-20"},[t("el-form",{ref:"checkChannelFormRef",attrs:{model:e.checkChannelForm,"label-width":"80px","label-position":"left"}},[t("el-form-item",{attrs:{label:"项目名称",prop:"name",rules:{required:!e.checkChannelForm.address,message:"请输入项目名称",trigger:["change","blur"]}}},[t("el-select",{staticClass:"w-300",attrs:{filterable:"",clearable:"",placeholder:"输入项目点名称进行查询"},model:{value:e.checkChannelForm.name,callback:function(t){e.$set(e.checkChannelForm,"name",t)},expression:"checkChannelForm.name"}},e._l(e.orgList,(function(e,n){return t("el-option",{key:n,attrs:{label:e.name,value:e.id}})})),1)],1),t("el-form-item",{attrs:{label:"项目地址",prop:"address",rules:{required:!e.checkChannelForm.name,message:"请输入项目地址",trigger:["change","blur"]}}},[t("el-cascader",{ref:"cascaderRef",staticClass:"w-300 m-r-20",attrs:{placeholder:"请选择项目点地址进行查询",options:e.addrOptions,filterable:""},model:{value:e.checkChannelForm.address,callback:function(t){e.$set(e.checkChannelForm,"address",t)},expression:"checkChannelForm.address"}}),t("el-button",{staticClass:"ps-origin-btn",on:{click:e.checkOrg}},[e._v("查询")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.bindLoading,expression:"bindLoading"}],attrs:{data:e.checkChannelTableData,stripe:"","default-expand-all":"","header-row-class-name":"ps-table-header-row","row-key":"id","tree-props":{children:"children_list",hasChildren:"hasChildren"}}},e._l(e.checkChannelTableSetting,(function(n,r){return t("table-column",{key:n.key+r,attrs:{col:n},scopedSlots:e._u([{key:"operation",fn:function(n){var r=n.row;return[t("div",{directives:[{name:"show",rawName:"v-show",value:!r.supervision_channel_bind,expression:"!row.supervision_channel_bind"}]},[e._v("--")]),t("el-button",{directives:[{name:"show",rawName:"v-show",value:r.supervision_channel_bind,expression:"row.supervision_channel_bind"}],staticClass:"ps-red",attrs:{type:"text",size:"small"},on:{click:function(t){return e.untieHandle(r)}}},[e._v("解绑")])]}}],null,!0)})})),1),t("div",{staticClass:"ps-el-drawer-footer"},[t("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:function(t){return e.cancelHandle("checkChannelForm")}}},[e._v("关闭")])],1)],1)])],1)],1)},a=[],i=n("ed08"),o=n("26a1"),s=n("3ef7"),l=n("f025"),c=n("1e28"),u=n("ddcc"),d=n("ef6c");function h(e){return h="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},h(e)}function f(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */f=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,a=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function u(e,t,n,r){var i=t&&t.prototype instanceof _?t:_,o=Object.create(i.prototype),s=new F(r||[]);return a(o,"_invoke",{value:T(e,n,s)}),o}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var p="suspendedStart",m="suspendedYield",g="executing",b="completed",v={};function _(){}function y(){}function w(){}var C={};c(C,o,(function(){return this}));var k=Object.getPrototypeOf,A=k&&k(k(N([])));A&&A!==n&&r.call(A,o)&&(C=A);var S=w.prototype=_.prototype=Object.create(C);function E(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,t){function n(a,i,o,s){var l=d(e[a],e,i);if("throw"!==l.type){var c=l.arg,u=c.value;return u&&"object"==h(u)&&r.call(u,"__await")?t.resolve(u.__await).then((function(e){n("next",e,o,s)}),(function(e){n("throw",e,o,s)})):t.resolve(u).then((function(e){c.value=e,o(c)}),(function(e){return n("throw",e,o,s)}))}s(l.arg)}var i;a(this,"_invoke",{value:function(e,r){function a(){return new t((function(t,a){n(e,r,t,a)}))}return i=i?i.then(a,a):a()}})}function T(t,n,r){var a=p;return function(i,o){if(a===g)throw Error("Generator is already running");if(a===b){if("throw"===i)throw o;return{value:e,done:!0}}for(r.method=i,r.arg=o;;){var s=r.delegate;if(s){var l=L(s,r);if(l){if(l===v)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===p)throw a=b,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=g;var c=d(t,n,r);if("normal"===c.type){if(a=r.done?b:m,c.arg===v)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(a=b,r.method="throw",r.arg=c.arg)}}}function L(t,n){var r=n.method,a=t.iterator[r];if(a===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,L(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var i=d(a,t.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,v;var o=i.arg;return o?o.done?(n[t.resultName]=o.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,v):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function O(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function D(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function F(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(O,this),this.reset(!0)}function N(t){if(t||""===t){var n=t[o];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,i=function n(){for(;++a<t.length;)if(r.call(t,a))return n.value=t[a],n.done=!1,n;return n.value=e,n.done=!0,n};return i.next=i}}throw new TypeError(h(t)+" is not iterable")}return y.prototype=w,a(S,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:y,configurable:!0}),y.displayName=c(w,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,c(e,l,"GeneratorFunction")),e.prototype=Object.create(S),e},t.awrap=function(e){return{__await:e}},E(x.prototype),c(x.prototype,s,(function(){return this})),t.AsyncIterator=x,t.async=function(e,n,r,a,i){void 0===i&&(i=Promise);var o=new x(u(e,n,r,a),i);return t.isGeneratorFunction(n)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},E(S),c(S,l,"Generator"),c(S,o,(function(){return this})),c(S,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=N,F.prototype={constructor:F,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(D),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function a(r,a){return s.type="throw",s.arg=t,n.next=r,a&&(n.method="next",n.arg=e),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var l=r.call(o,"catchLoc"),c=r.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),D(n),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var a=r.arg;D(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:N(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),v}},t}function p(e,t){return _(e)||v(e,t)||g(e,t)||m()}function m(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function g(e,t){if(e){if("string"==typeof e)return b(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?b(e,t):void 0}}function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function v(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i,o,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);l=!0);}catch(e){c=!0,a=e}finally{try{if(!l&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw a}}return s}}function _(e){if(Array.isArray(e))return e}function y(e,t,n,r,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,a)}function w(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function o(e){y(i,r,a,o,s,"next",e)}function s(e){y(i,r,a,o,s,"throw",e)}o(void 0)}))}}var C={name:"SuperOrganizationAdmin",components:{channelInfo:s["default"],accountManagement:l["default"],functionConfiguration:c["default"]},data:function(){return{addrOptions:d["regionData"],treeSelectId:0,treeLoading:!1,treeList:[],isLazy:!0,treeFilterText:"",treeProps:{children:"children_list",label:"name",isLeaf:function(e,t){return!e.has_children}},addType:"",selectTree:{},selectId:"",parentTreeData:{},operate:"",tabType:"channelInfo",tabList:[{name:"渠道信息",value:"channelInfo",index:1},{name:"账号管理",value:"accountManagement",index:2},{name:"功能配置",value:"functionConfiguration",index:3}],time:(new Date).getTime(),isLoading:!1,bindLoading:!1,checkChannelLoading:!1,slideTransition:"slide-left",addChannelDrawerShow:!1,addChannelForm:{name:""},orgList:[],checkChannelDrawerShow:!1,checkChannelForm:{name:"",address:""},checkChannelTableData:[],checkChannelTableSetting:u["DEFAULT_CHANNEL_TABLE_SETTING"]}},watch:{treeFilterText:function(e){this.filterHandle()},checkChannelDrawerShow:function(e){e&&this.getOrganizationList()}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getChannelList()},refreshHandle:function(){this.treeFilterText="",this.tabType="channelInfo",this.initLoad()},refresh:function(e){this.getChannelList(),this.treeHandleNodeClick(e)},loadTree:function(e,t){var n=this;return w(f().mark((function r(){var a,o,s,l,c;return f().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(0!==e.level){r.next=2;break}return r.abrupt("return");case 2:return a={status__in:["enable","disable"],page:1,page_size:99999},e.data&&e.data.id?a.parent__in=e.data.id:(a.parent__is_null="1",n.treeLoading=!0),r.next=6,Object(i["Z"])(n.$apis.apiBackgroundFundSupervisionSupervisionChannelListPost(a));case 6:if(o=r.sent,s=p(o,2),l=s[0],c=s[1],n.treeLoading=!1,!l){r.next=15;break}return t([]),n.$message.error(l.message),r.abrupt("return");case 15:0===c.code?t(c.data.results):(t([]),n.$message.error(c.msg));case 16:case"end":return r.stop()}}),r)})))()},filterHandle:Object(i["d"])((function(){this.getChannelList(this.treeFilterText)}),300),getChannelList:function(e,t,n){var r=this;return w(f().mark((function a(){var s,l,c,u,d,h;return f().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return s={status__in:["enable","disable"]},t?s.parent__in=t:s.parent__is_null="1",e&&(s.name__contains=e),r.treeLoading=!0,r.isLoading=!0,a.next=7,Object(i["Z"])(r.$apis.apiBackgroundFundSupervisionSupervisionChannelListPost(s));case 7:if(l=a.sent,c=p(l,2),u=c[0],d=c[1],r.isLoading=!1,r.treeLoading=!1,!u){a.next=16;break}return r.$message.error(u.message),a.abrupt("return");case 16:0===d.code?(h=d.data.results,h.length&&h[0].has_children&&r.$nextTick((function(){var e=document.querySelectorAll(".el-tree-node__expand-icon");Object(o["a"])(e[0],"expanded")||e[0]&&e[0].click()})),t?n&&n(t,h):r.treeList=h,r.selectId=r.treeList[0].id,r.selectTree=r.treeList[0],r.selectId&&r.$nextTick((function(){r.$refs.treeRef.setCurrentKey(r.selectId)}))):r.$message.error(d.msg);case 17:case"end":return a.stop()}}),a)})))()},getOrganizationList:function(){var e=this;this.checkChannelLoading=!0,this.$apis.apiBackgroundAdminOrganizationListPost({page:1,page_size:9999,parent__is_null:"1",status__in:["enable"]}).then((function(t){e.checkChannelLoading=!1,0===t.code?e.orgList=t.data.results.map((function(e){var t={id:e.id,name:e.name};return t})):e.$message.error(t.msg)}))},deleteEmptyChildren:function(e,t){t=t||"children_list";var n=this;function r(e){e.map((function(e){e[t]&&e[t].length>0?r(e[t]):n.$delete(e,t)}))}return r(e),e},clickTabHandle:function(e){var t=this;if(!e.disable){var n=this.tabList.filter((function(e){return e.value===t.tabType}))[0];this.slideTransition=n.index<e.index?"slide-left":"slide-right",this.tabType=e.value}},tableRowClassName:function(e){e.row;var t=e.rowIndex,n="";return(t+1)%2===0&&(n+="table-header-row"),n},treeHandleNodeClick:function(e){var t=this;return w(f().mark((function n(){return f().wrap((function(n){while(1)switch(n.prev=n.next){case 0:t.isLoading=!0,t.selectId=e.id,t.selectTree=e,setTimeout((function(){t.isLoading=!1}),500);case 4:case"end":return n.stop()}}),n)})))()},addRootTreeHandle:function(e,t){var n=this;return w(f().mark((function r(){return f().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return n.type="",n.tabType="channelInfo",r.next=4,n.$sleep(300);case 4:n.type=t,n.selectId=e.id;case 6:case"end":return r.stop()}}),r)})))()},showAddChannelDrawer:function(e){this.addType=e,this.addChannelDrawerShow=!0},addChildTreeHandle:function(e,t){var n=this;return w(f().mark((function r(){return f().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return n.addType="",n.tabType="channelInfo",n.isLoading=!0,r.next=5,n.$sleep(100);case 5:n.isLoading=!1,n.addType=e,n.parentTreeData=Object(i["f"])(t),n.addChannelDrawerShow=!0;case 9:case"end":return r.stop()}}),r)})))()},changeStatus:function(e){var t=this;return w(f().mark((function n(){var r,a;return f().wrap((function(n){while(1)switch(n.prev=n.next){case 0:r="",a=!0,n.t0=e.status,n.next="enable"===n.t0?5:7;break;case 5:return r="您正在关闭渠道：".concat(e.name,"。关闭后渠道账号将不可用，数据统计自动停止，请谨慎操作。确定要关闭该渠道？"),n.abrupt("break",10);case 7:return r="您正在启用渠道：".concat(e.name,"。启用后渠道账号将恢复，数据统计重新计算。确定要启用该渠道？"),a=!1,n.abrupt("break",10);case 10:t.$confirm(r,"提示",{confirmButtonText:t.$t("dialog.confirm_btn"),cancelButtonText:t.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:a?"ps-warn":"ps-btn",center:!0,beforeClose:function(){var n=w(f().mark((function n(r,a,o){var s,l,c,u;return f().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if("confirm"!==r){n.next=18;break}return a.confirmButtonLoading=!0,t.treeLoading=!0,n.next=5,Object(i["Z"])(t.$apis.apiBackgroundFundSupervisionSupervisionChannelModifyPost({id:e.id,status:"enable"===e.status?"disable":"enable",is_root:!e.level,name:e.name}));case 5:if(s=n.sent,l=p(s,2),c=l[0],u=l[1],t.treeLoading=!1,a.confirmButtonLoading=!1,o(),!c){n.next=15;break}return t.$message.error(c.message),n.abrupt("return");case 15:0===u.code?(t.$message.success(u.msg),t.getChannelList(t.treeFilterText,e.parent,t.updateTreeChildren)):t.$message.error(u.msg),n.next=19;break;case 18:a.confirmButtonLoading||o();case 19:case"end":return n.stop()}}),n)})));function r(e,t,r){return n.apply(this,arguments)}return r}()}).then((function(e){})).catch((function(e){}));case 11:case"end":return n.stop()}}),n)})))()},updateTreeChildren:function(e,t){var n=this.$refs.treeRef;n.updateKeyChildren(e,t)},cancelHandle:function(e){"addChannelForm"===e?(this.$refs.addChannelFormRef.resetFields(),this.addChannelDrawerShow=!1):(this.getChannelList(),this.checkChannelDrawerShow=!1)},saveHandle:function(){var e=this,t={is_root:"parent"===this.addType,parent:"parent"===this.addType?void 0:this.parentTreeData.id,name:this.addChannelForm.name};this.$apis.apiBackgroundFundSupervisionSupervisionChannelAddPost(t).then((function(t){0===t.code?(e.$refs.addChannelFormRef.resetFields(),e.$message.success("创建成功"),e.addChannelDrawerShow=!1,e.getChannelList()):e.$message.error(t.msg)}))},untieHandle:function(e){var t=this,n={org_id:e.id,bind_type:!e.supervision_channel_bind};this.$apis.apiBackgroundFundSupervisionSupervisionChannelOrgBingConfigPost(n).then((function(n){0===n.code?(t.$message.success(e.supervision_channel_bind?"解绑成功":"绑定成功"),t.$refs.checkChannelFormRef.resetFields(),t.checkChannelTableData=[]):t.$message.error(n.msg)}))},checkOrg:function(){var e=this;this.$refs.checkChannelFormRef.validate((function(t){if(t){e.bindLoading=!0;var n={org_id:e.checkChannelForm.name||void 0,district:e.checkChannelForm.address?JSON.stringify(e.checkChannelForm.address):void 0};e.$apis.apiBackgroundFundSupervisionSupervisionChannelTreeListPost(n).then((function(t){if(e.bindLoading=!1,0===t.code){var n=[];n=t.data.length?t.data.map((function(e){return e[0]})):[],e.checkChannelTableData=Object(i["f"])(n)}else e.$message.error(t.msg)}))}else e.$message.error("查询条件不能为空")}))},load:function(e,t,n){var r=this,a={parent__in:e.tree_id};this.$apis.apiBackgroundFundSupervisionSupervisionChannelBingOrgListPost(a).then((function(e){0===e.code?n(e.data.results||[]):r.$message.error(e.msg)}))}}},k=C,A=(n("fdcc"),n("2877")),S=Object(A["a"])(k,r,a,!1,null,null,null);t["default"]=S.exports},"7f4b":function(e,t,n){"use strict";n("ab45")},8237:function(module,exports,__webpack_require__){(function(process,global){var __WEBPACK_AMD_DEFINE_RESULT__;
/**
 * [js-md5]{@link https://github.com/emn178/js-md5}
 *
 * @namespace md5
 * @version 0.7.3
 * <AUTHOR> Yi-Cyuan [<EMAIL>]
 * @copyright Chen, Yi-Cyuan 2014-2017
 * @license MIT
 */(function(){"use strict";var ERROR="input is invalid type",WINDOW="object"===typeof window,root=WINDOW?window:{};root.JS_MD5_NO_WINDOW&&(WINDOW=!1);var WEB_WORKER=!WINDOW&&"object"===typeof self,NODE_JS=!root.JS_MD5_NO_NODE_JS&&"object"===typeof process&&process.versions&&process.versions.node;NODE_JS?root=global:WEB_WORKER&&(root=self);var COMMON_JS=!root.JS_MD5_NO_COMMON_JS&&"object"===typeof module&&module.exports,AMD=__webpack_require__("3c35f"),ARRAY_BUFFER=!root.JS_MD5_NO_ARRAY_BUFFER&&"undefined"!==typeof ArrayBuffer,HEX_CHARS="0123456789abcdef".split(""),EXTRA=[128,32768,8388608,-**********],SHIFT=[0,8,16,24],OUTPUT_TYPES=["hex","array","digest","buffer","arrayBuffer","base64"],BASE64_ENCODE_CHAR="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),blocks=[],buffer8;if(ARRAY_BUFFER){var buffer=new ArrayBuffer(68);buffer8=new Uint8Array(buffer),blocks=new Uint32Array(buffer)}!root.JS_MD5_NO_NODE_JS&&Array.isArray||(Array.isArray=function(e){return"[object Array]"===Object.prototype.toString.call(e)}),!ARRAY_BUFFER||!root.JS_MD5_NO_ARRAY_BUFFER_IS_VIEW&&ArrayBuffer.isView||(ArrayBuffer.isView=function(e){return"object"===typeof e&&e.buffer&&e.buffer.constructor===ArrayBuffer});var createOutputMethod=function(e){return function(t){return new Md5(!0).update(t)[e]()}},createMethod=function(){var e=createOutputMethod("hex");NODE_JS&&(e=nodeWrap(e)),e.create=function(){return new Md5},e.update=function(t){return e.create().update(t)};for(var t=0;t<OUTPUT_TYPES.length;++t){var n=OUTPUT_TYPES[t];e[n]=createOutputMethod(n)}return e},nodeWrap=function(method){var crypto=eval("require('crypto')"),Buffer=eval("require('buffer').Buffer"),nodeMethod=function(e){if("string"===typeof e)return crypto.createHash("md5").update(e,"utf8").digest("hex");if(null===e||void 0===e)throw ERROR;return e.constructor===ArrayBuffer&&(e=new Uint8Array(e)),Array.isArray(e)||ArrayBuffer.isView(e)||e.constructor===Buffer?crypto.createHash("md5").update(new Buffer(e)).digest("hex"):method(e)};return nodeMethod};function Md5(e){if(e)blocks[0]=blocks[16]=blocks[1]=blocks[2]=blocks[3]=blocks[4]=blocks[5]=blocks[6]=blocks[7]=blocks[8]=blocks[9]=blocks[10]=blocks[11]=blocks[12]=blocks[13]=blocks[14]=blocks[15]=0,this.blocks=blocks,this.buffer8=buffer8;else if(ARRAY_BUFFER){var t=new ArrayBuffer(68);this.buffer8=new Uint8Array(t),this.blocks=new Uint32Array(t)}else this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];this.h0=this.h1=this.h2=this.h3=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}Md5.prototype.update=function(e){if(!this.finalized){var t,n=typeof e;if("string"!==n){if("object"!==n)throw ERROR;if(null===e)throw ERROR;if(ARRAY_BUFFER&&e.constructor===ArrayBuffer)e=new Uint8Array(e);else if(!Array.isArray(e)&&(!ARRAY_BUFFER||!ArrayBuffer.isView(e)))throw ERROR;t=!0}var r,a,i=0,o=e.length,s=this.blocks,l=this.buffer8;while(i<o){if(this.hashed&&(this.hashed=!1,s[0]=s[16],s[16]=s[1]=s[2]=s[3]=s[4]=s[5]=s[6]=s[7]=s[8]=s[9]=s[10]=s[11]=s[12]=s[13]=s[14]=s[15]=0),t)if(ARRAY_BUFFER)for(a=this.start;i<o&&a<64;++i)l[a++]=e[i];else for(a=this.start;i<o&&a<64;++i)s[a>>2]|=e[i]<<SHIFT[3&a++];else if(ARRAY_BUFFER)for(a=this.start;i<o&&a<64;++i)r=e.charCodeAt(i),r<128?l[a++]=r:r<2048?(l[a++]=192|r>>6,l[a++]=128|63&r):r<55296||r>=57344?(l[a++]=224|r>>12,l[a++]=128|r>>6&63,l[a++]=128|63&r):(r=65536+((1023&r)<<10|1023&e.charCodeAt(++i)),l[a++]=240|r>>18,l[a++]=128|r>>12&63,l[a++]=128|r>>6&63,l[a++]=128|63&r);else for(a=this.start;i<o&&a<64;++i)r=e.charCodeAt(i),r<128?s[a>>2]|=r<<SHIFT[3&a++]:r<2048?(s[a>>2]|=(192|r>>6)<<SHIFT[3&a++],s[a>>2]|=(128|63&r)<<SHIFT[3&a++]):r<55296||r>=57344?(s[a>>2]|=(224|r>>12)<<SHIFT[3&a++],s[a>>2]|=(128|r>>6&63)<<SHIFT[3&a++],s[a>>2]|=(128|63&r)<<SHIFT[3&a++]):(r=65536+((1023&r)<<10|1023&e.charCodeAt(++i)),s[a>>2]|=(240|r>>18)<<SHIFT[3&a++],s[a>>2]|=(128|r>>12&63)<<SHIFT[3&a++],s[a>>2]|=(128|r>>6&63)<<SHIFT[3&a++],s[a>>2]|=(128|63&r)<<SHIFT[3&a++]);this.lastByteIndex=a,this.bytes+=a-this.start,a>=64?(this.start=a-64,this.hash(),this.hashed=!0):this.start=a}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this}},Md5.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var e=this.blocks,t=this.lastByteIndex;e[t>>2]|=EXTRA[3&t],t>=56&&(this.hashed||this.hash(),e[0]=e[16],e[16]=e[1]=e[2]=e[3]=e[4]=e[5]=e[6]=e[7]=e[8]=e[9]=e[10]=e[11]=e[12]=e[13]=e[14]=e[15]=0),e[14]=this.bytes<<3,e[15]=this.hBytes<<3|this.bytes>>>29,this.hash()}},Md5.prototype.hash=function(){var e,t,n,r,a,i,o=this.blocks;this.first?(e=o[0]-680876937,e=(e<<7|e>>>25)-271733879<<0,r=(-1732584194^2004318071&e)+o[1]-117830708,r=(r<<12|r>>>20)+e<<0,n=(-271733879^r&(-271733879^e))+o[2]-1126478375,n=(n<<17|n>>>15)+r<<0,t=(e^n&(r^e))+o[3]-1316259209,t=(t<<22|t>>>10)+n<<0):(e=this.h0,t=this.h1,n=this.h2,r=this.h3,e+=(r^t&(n^r))+o[0]-680876936,e=(e<<7|e>>>25)+t<<0,r+=(n^e&(t^n))+o[1]-389564586,r=(r<<12|r>>>20)+e<<0,n+=(t^r&(e^t))+o[2]+606105819,n=(n<<17|n>>>15)+r<<0,t+=(e^n&(r^e))+o[3]-1044525330,t=(t<<22|t>>>10)+n<<0),e+=(r^t&(n^r))+o[4]-176418897,e=(e<<7|e>>>25)+t<<0,r+=(n^e&(t^n))+o[5]+1200080426,r=(r<<12|r>>>20)+e<<0,n+=(t^r&(e^t))+o[6]-1473231341,n=(n<<17|n>>>15)+r<<0,t+=(e^n&(r^e))+o[7]-45705983,t=(t<<22|t>>>10)+n<<0,e+=(r^t&(n^r))+o[8]+1770035416,e=(e<<7|e>>>25)+t<<0,r+=(n^e&(t^n))+o[9]-1958414417,r=(r<<12|r>>>20)+e<<0,n+=(t^r&(e^t))+o[10]-42063,n=(n<<17|n>>>15)+r<<0,t+=(e^n&(r^e))+o[11]-1990404162,t=(t<<22|t>>>10)+n<<0,e+=(r^t&(n^r))+o[12]+1804603682,e=(e<<7|e>>>25)+t<<0,r+=(n^e&(t^n))+o[13]-40341101,r=(r<<12|r>>>20)+e<<0,n+=(t^r&(e^t))+o[14]-1502002290,n=(n<<17|n>>>15)+r<<0,t+=(e^n&(r^e))+o[15]+1236535329,t=(t<<22|t>>>10)+n<<0,e+=(n^r&(t^n))+o[1]-165796510,e=(e<<5|e>>>27)+t<<0,r+=(t^n&(e^t))+o[6]-1069501632,r=(r<<9|r>>>23)+e<<0,n+=(e^t&(r^e))+o[11]+643717713,n=(n<<14|n>>>18)+r<<0,t+=(r^e&(n^r))+o[0]-373897302,t=(t<<20|t>>>12)+n<<0,e+=(n^r&(t^n))+o[5]-701558691,e=(e<<5|e>>>27)+t<<0,r+=(t^n&(e^t))+o[10]+38016083,r=(r<<9|r>>>23)+e<<0,n+=(e^t&(r^e))+o[15]-660478335,n=(n<<14|n>>>18)+r<<0,t+=(r^e&(n^r))+o[4]-405537848,t=(t<<20|t>>>12)+n<<0,e+=(n^r&(t^n))+o[9]+568446438,e=(e<<5|e>>>27)+t<<0,r+=(t^n&(e^t))+o[14]-1019803690,r=(r<<9|r>>>23)+e<<0,n+=(e^t&(r^e))+o[3]-187363961,n=(n<<14|n>>>18)+r<<0,t+=(r^e&(n^r))+o[8]+1163531501,t=(t<<20|t>>>12)+n<<0,e+=(n^r&(t^n))+o[13]-1444681467,e=(e<<5|e>>>27)+t<<0,r+=(t^n&(e^t))+o[2]-51403784,r=(r<<9|r>>>23)+e<<0,n+=(e^t&(r^e))+o[7]+1735328473,n=(n<<14|n>>>18)+r<<0,t+=(r^e&(n^r))+o[12]-1926607734,t=(t<<20|t>>>12)+n<<0,a=t^n,e+=(a^r)+o[5]-378558,e=(e<<4|e>>>28)+t<<0,r+=(a^e)+o[8]-2022574463,r=(r<<11|r>>>21)+e<<0,i=r^e,n+=(i^t)+o[11]+1839030562,n=(n<<16|n>>>16)+r<<0,t+=(i^n)+o[14]-35309556,t=(t<<23|t>>>9)+n<<0,a=t^n,e+=(a^r)+o[1]-1530992060,e=(e<<4|e>>>28)+t<<0,r+=(a^e)+o[4]+1272893353,r=(r<<11|r>>>21)+e<<0,i=r^e,n+=(i^t)+o[7]-155497632,n=(n<<16|n>>>16)+r<<0,t+=(i^n)+o[10]-1094730640,t=(t<<23|t>>>9)+n<<0,a=t^n,e+=(a^r)+o[13]+681279174,e=(e<<4|e>>>28)+t<<0,r+=(a^e)+o[0]-358537222,r=(r<<11|r>>>21)+e<<0,i=r^e,n+=(i^t)+o[3]-722521979,n=(n<<16|n>>>16)+r<<0,t+=(i^n)+o[6]+76029189,t=(t<<23|t>>>9)+n<<0,a=t^n,e+=(a^r)+o[9]-640364487,e=(e<<4|e>>>28)+t<<0,r+=(a^e)+o[12]-421815835,r=(r<<11|r>>>21)+e<<0,i=r^e,n+=(i^t)+o[15]+530742520,n=(n<<16|n>>>16)+r<<0,t+=(i^n)+o[2]-995338651,t=(t<<23|t>>>9)+n<<0,e+=(n^(t|~r))+o[0]-198630844,e=(e<<6|e>>>26)+t<<0,r+=(t^(e|~n))+o[7]+1126891415,r=(r<<10|r>>>22)+e<<0,n+=(e^(r|~t))+o[14]-1416354905,n=(n<<15|n>>>17)+r<<0,t+=(r^(n|~e))+o[5]-57434055,t=(t<<21|t>>>11)+n<<0,e+=(n^(t|~r))+o[12]+1700485571,e=(e<<6|e>>>26)+t<<0,r+=(t^(e|~n))+o[3]-1894986606,r=(r<<10|r>>>22)+e<<0,n+=(e^(r|~t))+o[10]-1051523,n=(n<<15|n>>>17)+r<<0,t+=(r^(n|~e))+o[1]-2054922799,t=(t<<21|t>>>11)+n<<0,e+=(n^(t|~r))+o[8]+1873313359,e=(e<<6|e>>>26)+t<<0,r+=(t^(e|~n))+o[15]-30611744,r=(r<<10|r>>>22)+e<<0,n+=(e^(r|~t))+o[6]-1560198380,n=(n<<15|n>>>17)+r<<0,t+=(r^(n|~e))+o[13]+1309151649,t=(t<<21|t>>>11)+n<<0,e+=(n^(t|~r))+o[4]-145523070,e=(e<<6|e>>>26)+t<<0,r+=(t^(e|~n))+o[11]-1120210379,r=(r<<10|r>>>22)+e<<0,n+=(e^(r|~t))+o[2]+718787259,n=(n<<15|n>>>17)+r<<0,t+=(r^(n|~e))+o[9]-343485551,t=(t<<21|t>>>11)+n<<0,this.first?(this.h0=e+1732584193<<0,this.h1=t-271733879<<0,this.h2=n-1732584194<<0,this.h3=r+271733878<<0,this.first=!1):(this.h0=this.h0+e<<0,this.h1=this.h1+t<<0,this.h2=this.h2+n<<0,this.h3=this.h3+r<<0)},Md5.prototype.hex=function(){this.finalize();var e=this.h0,t=this.h1,n=this.h2,r=this.h3;return HEX_CHARS[e>>4&15]+HEX_CHARS[15&e]+HEX_CHARS[e>>12&15]+HEX_CHARS[e>>8&15]+HEX_CHARS[e>>20&15]+HEX_CHARS[e>>16&15]+HEX_CHARS[e>>28&15]+HEX_CHARS[e>>24&15]+HEX_CHARS[t>>4&15]+HEX_CHARS[15&t]+HEX_CHARS[t>>12&15]+HEX_CHARS[t>>8&15]+HEX_CHARS[t>>20&15]+HEX_CHARS[t>>16&15]+HEX_CHARS[t>>28&15]+HEX_CHARS[t>>24&15]+HEX_CHARS[n>>4&15]+HEX_CHARS[15&n]+HEX_CHARS[n>>12&15]+HEX_CHARS[n>>8&15]+HEX_CHARS[n>>20&15]+HEX_CHARS[n>>16&15]+HEX_CHARS[n>>28&15]+HEX_CHARS[n>>24&15]+HEX_CHARS[r>>4&15]+HEX_CHARS[15&r]+HEX_CHARS[r>>12&15]+HEX_CHARS[r>>8&15]+HEX_CHARS[r>>20&15]+HEX_CHARS[r>>16&15]+HEX_CHARS[r>>28&15]+HEX_CHARS[r>>24&15]},Md5.prototype.toString=Md5.prototype.hex,Md5.prototype.digest=function(){this.finalize();var e=this.h0,t=this.h1,n=this.h2,r=this.h3;return[255&e,e>>8&255,e>>16&255,e>>24&255,255&t,t>>8&255,t>>16&255,t>>24&255,255&n,n>>8&255,n>>16&255,n>>24&255,255&r,r>>8&255,r>>16&255,r>>24&255]},Md5.prototype.array=Md5.prototype.digest,Md5.prototype.arrayBuffer=function(){this.finalize();var e=new ArrayBuffer(16),t=new Uint32Array(e);return t[0]=this.h0,t[1]=this.h1,t[2]=this.h2,t[3]=this.h3,e},Md5.prototype.buffer=Md5.prototype.arrayBuffer,Md5.prototype.base64=function(){for(var e,t,n,r="",a=this.array(),i=0;i<15;)e=a[i++],t=a[i++],n=a[i++],r+=BASE64_ENCODE_CHAR[e>>>2]+BASE64_ENCODE_CHAR[63&(e<<4|t>>>4)]+BASE64_ENCODE_CHAR[63&(t<<2|n>>>6)]+BASE64_ENCODE_CHAR[63&n];return e=a[i],r+=BASE64_ENCODE_CHAR[e>>>2]+BASE64_ENCODE_CHAR[e<<4&63]+"==",r};var exports=createMethod();COMMON_JS?module.exports=exports:(root.md5=exports,AMD&&(__WEBPACK_AMD_DEFINE_RESULT__=function(){return exports}.call(exports,__webpack_require__,exports,module),void 0===__WEBPACK_AMD_DEFINE_RESULT__||(module.exports=__WEBPACK_AMD_DEFINE_RESULT__)))})()}).call(this,__webpack_require__("4362"),__webpack_require__("c8ba"))},"87f2":function(e,t,n){},"921e":function(e,t,n){e.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}},ab45:function(e,t,n){},ad6e:function(e,t,n){"use strict";n("1105")},c0fa:function(e,t,n){},ddcc:function(e,t,n){"use strict";n.r(t),n.d(t,"TABLE_HEAD_DATA",(function(){return r})),n.d(t,"SEARCH_FORM_SET_DATA",(function(){return a})),n.d(t,"DIC_MERCHANT_STATUS",(function(){return i})),n.d(t,"DIC_MERCHANT_TYPE",(function(){return o})),n.d(t,"DIC_MERCHANT_ID_TYPE",(function(){return s})),n.d(t,"DIC_PERSON_MERCHANT_CATEGORY",(function(){return l})),n.d(t,"DIC_MERCHANT_CONTACT_ID",(function(){return c})),n.d(t,"DIC_CERTIFICATE_TYPE",(function(){return u})),n.d(t,"DIC_ACCOUNT_TYPE",(function(){return d})),n.d(t,"DIC_IS_NOT",(function(){return h})),n.d(t,"UPLOAD_DIALOG_DATA_LIST",(function(){return f})),n.d(t,"PRINT_BANK_TABBLE_SETTING",(function(){return p})),n.d(t,"DEFAULT_CHANNEL_TABLE_SETTING",(function(){return m}));var r=[{label:"二级商户编号",key:"sub_mch_id",width:"140",fixed:"left"},{label:"一级商户",key:"php_path",width:"120"},{label:"二级商户名称",key:"sub_mch_name",width:"120"},{label:"二级商户类型",key:"sub_mch_type_alias",width:"120"},{label:"二级商户证件类型",key:"company_cert_type_alias",width:"140"},{label:"二级商户证件有效期结束时间",key:"end_certificate_validity",width:"140"},{label:"法定代表人",key:"contact_name",width:"120"},{label:"法定代表人证件类型",key:"certificate_type_alias",width:"150"},{label:"法定代表人证件编号",key:"certificate_no",width:"150"},{label:"法定代表人证件有效结束时间",key:"fr_cert_end_date",width:"140"},{label:"银行账号",key:"account",width:"120"},{label:"银行账户户名",key:"account_name",width:"120"},{label:"开户银行名称",key:"bank_name",width:"120"},{label:"银行预留手机号",key:"mobile_phone",width:"120"},{label:"账户类型",key:"account_type_alias",width:"120"},{label:"二级商户状态",key:"is_passed_alias",width:"120"},{label:"历史二级商户号",key:"history_sub_mch_ids",type:"slot",slotName:"detail",width:"120"},{label:"签署组织",key:"organization_name",width:"120",type:"slot",slotName:"organizationName"},{label:"签署账号",key:"get_agreement_info",width:"120",type:"slot",slotName:"accountName"},{label:"是否签署协议",key:"is_sign",width:"120",type:"slot",slotName:"isSign"},{label:"操作人",key:"operator",width:"120"},{label:"操作时间",key:"update_time",width:"120"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"200"}],a={sub_mch_id:{type:"input",value:"",label:"二级商户编号",placeholder:"请输入二级商户编号"},sub_mch_name:{type:"input",value:"",label:"二级商户名称",placeholder:"请输入二级商户名称"},is_passed:{type:"select",label:"商户状态",value:[],placeholder:"请选择商户状态",listNameKey:"name",listValueKey:"value",dataList:[]},php_path:{type:"input",value:"",label:"一级商户",placeholder:"请输入一级商户"}},i=[{name:"全部",value:"",label:""},{name:"已验证,未审核(不可交易)",value:"0",label:"VERIFIED_NOT_REVIEWED_0"},{name:"已验证,审核通过",value:"1",label:"VERIFIED_REVIEWED"},{name:"已验证,未审核(暂可交易)",value:"2",label:"VERIFIED_NOT_REVIEWED_2"},{name:"未验证,未审核",value:"3",label:"NOT_VERIFIED_NOT_REVIEWED"},{name:"已解约",value:"4",label:"TERMINATED"},{name:"已关闭",value:"5",label:"CLOSED"},{name:"审核拒绝",value:"8",label:"AUDIT_REJECTED"},{name:"驳回",value:"9",label:"TURN_DOWN"}],o=[{name:"个人商户",value:"1",label:"INDIVIDUAL"},{name:"企业",value:"2",label:"ENTERPRISE"},{name:"个体工商户",value:"3",label:"INDIVIDUAL_BUSINESS"},{name:"政府、金融机构及事业单位",value:"4",label:"BUSINESS_UNIT"}],s=[{name:"个体工商户营业执照",value:"610049",label:"INDIVIDUAL_LICENSE"},{name:"企业营业执照",value:"610047",label:"ENTERPRISE_LICENSE"},{name:"组织机构代码",value:"610001",label:"ORGANIZATION_CODE"},{name:"统一社会信用代码",value:"611009",label:"CREDIT_CODE"},{name:"事业单位法人证书",value:"610170",label:"BUSINESS_UNIT_CERTIFICATE"},{name:"社会团体登记证书",value:"610023",label:"SOCIAL_GROUPS_CERTIFICATE"},{name:"民办非企业登记证书",value:"610025",label:"PRIVATE_CERTIFICATE"},{name:"农民专业合作社营业执照",value:"610079",label:"FARMER_LICENSE"},{name:"主管部门颁居民委员会批文",value:"610033",label:"COMMITTEE_APPROVAL"},{name:"政府主管部门批文",value:"610037",label:"GOVERNMENT_APPROVAL"},{name:"财政部门证明",value:"610039",label:"FINANCIAL_PROVE"},{name:"其他机构证件标识",value:"619999",label:"OTHER"}],l=[{name:"有固定经营场所的实体商户",value:"0",label:"FIXED"},{name:"无固定经营场所的实体商户",value:"1",label:"NOT_FIXED"},{name:"网络商户",value:"2",label:"NET"}],c=[{name:"商户信息核实联系人",value:"01",label:"VERIFY_CONTACT"},{name:"商户巡检联系人",value:"02",label:"INSPECTION_CONTACT"},{name:"客户投诉处理联系人",value:"03",label:"COMPLAINT_HANDLING_CONTACT"}],u=[{name:"身份证",value:"110001",label:"ID_CARD"},{name:"临时居民身份证",value:"110003",label:"TEMPORARY_ID_CARD"},{name:"中国人民解放军军人身份证件",value:"110007",label:"MILITARY_ID"},{name:"中国人民武装警察身份证件",value:"110009",label:"POLICE_ID"},{name:"港澳居民来往内地通行证",value:"110019",label:"HONG_KONG_AND_MACAU_PASS"},{name:"台湾居民来往大陆通行证",value:"110021",label:"TAIWAN_PASS"},{name:"中华人民共和国护照",value:"110023",label:"CHINESE_PASSPORT"},{name:"外国护照",value:"110025",label:"FOREIGN_PASSPORT"},{name:"其他证件",value:"119999",label:"OTHER"}],d=[{name:"借记卡",value:"401",label:"DEBIT_CARD"},{name:"企业户",value:"601",label:"ENTERPRISE_HOUSEHOLD"},{name:"二类户",value:"701",label:"CLASS_II_HOUSEHOLDS"},{name:"三类户",value:"702",label:"CLASS_III_HOUSEHOLDS"}],h=[{name:"是",value:!0},{name:"否",value:!1}],f=[{name:"法人身份证人像面照片",required:!0,fileName:"",fileKey:"id_card_face_url",fileUrl:""},{name:"法人身份证国徽面照片",required:!0,fileName:"",fileKey:"id_card_national_emblem_url",fileUrl:""},{name:"法人护照、通行证照片",required:!0,fileName:"",fileKey:"passport_url",fileUrl:""},{name:"个体工商户/企业营业执照照片",required:!0,fileName:"",fileKey:"license_url",fileUrl:""},{name:"辅助证明材料",required:!0,fileName:"",fileKey:"auxiliary_proof_url",fileUrl:""},{name:"政府机关/事业单位/社会组织登记证书照片",required:!0,fileName:"",fileKey:"certificate_url",fileUrl:""},{name:"法定代表人授权函",required:!1,fileName:"",fileKey:"authorization_letter_url",fileUrl:""},{name:"定位证明材料",required:!1,fileName:"",fileKey:"gps_prove_url",fileUrl:""},{name:"固定经营场所证明材料",required:!1,fileName:"",fileKey:"fixed_place_prove_url",fileUrl:""},{name:"合法合规用途证明材料：",required:!1,fileName:"",fileKey:"use_prove_url",fileUrl:""}],p=[{key:"ori_sub_mer_no",label:"*原二级商户号"},{key:"sub_merchant_short_name",label:"*二级商户名称"},{key:"sub_mch_type",label:"*二级商户类型"},{key:"sub_mch_name",label:"*二级商户经营名称"},{key:"service_phone",label:"*二级商户客服电话"},{key:"industry",label:"*二级商户所属行业"},{key:"business_range",label:"二级商户经营范围"},{key:"address",label:"*二级商户实际经营地址"},{key:"company_cert_type",label:"二级商户证件类型"},{key:"company_cert_no",label:"二级商户证件编号"},{key:"end_certificate_validity",label:"二级商户证件有效期"},{key:"sub_mer_class",label:"个人商户类别"},{key:"account",label:"*银行账号"},{key:"account_name",label:"*银行账户户名"},{key:"bank_name",label:"*开户银行名称"},{key:"mobile_phone",label:"*银行预留手机号"},{key:"account_type",label:"*账户类型"},{key:"apply_service",label:"*申请服务"},{key:"sub_mer_contact_name",label:"*联系人姓名"},{key:"mer_mobile_phone",label:"*联系人手机号码"},{key:"sub_mer_contact_cert",label:"*联系人证件号码"},{key:"sub_mer_contact_mail",label:"联系人邮箱"},{key:"sub_mer_contact_type",label:"*商户联系人业务标识"},{key:"contact_name",label:"*法定代表人姓名"},{key:"certificate_type",label:"*法定代表人证件类型"},{key:"certificate_no",label:"*法定代表人证件编号"},{key:"certificate_beg_date",label:"*法定代表人证件有效期开始时间"},{key:"fr_cert_end_date",label:"*法定代表人证件有效期结束时间"},{key:"fr_residence",label:"*法定代表人证件居住地址"},{key:"fr_is_controller",label:"法定代表人是否为受益所有人"},{key:"fr_is_agent",label:"法定代表人是否为实际办理业务人员"},{key:"controller_name",label:"受益所有人姓名"},{key:"controller_cert_type",label:"受益所有人证件类型"},{key:"controller_cert_no",label:"受益所有人证件号码"},{key:"controller_cert_beg_date",label:"受益所有人证件有效期开始时间"},{key:"controller_cert_end_date",label:"受益所有人证件有效期结束时间"},{key:"controller_residence",label:"受益所有人证件居住地址"},{key:"agent_name",label:"授权办理业务人员姓名"},{key:"agent_cert_type",label:"授权办理业务人员证件类型"},{key:"agent_cert_no",label:"授权办理业务人员证件号码"},{key:"agent_cert_beg_date",label:"授权办理业务人员证件有效期开始时间"},{key:"agent_cert_end_date",label:"授权办理业务人员证件有效期结束时间"},{key:"agent_residence",label:"授权办理业务人员证件居住地址"}],m=[{label:"所属项目",key:"name",align:"left"},{label:"所属地址",key:"district_alias",type:"slot",slotName:"districtAlias"},{label:"监管渠道",key:"supervision_channel_name"},{label:"操作",key:"operation",type:"slot",slotName:"operation",width:"200"}]},e5129:function(e,t,n){"use strict";n("87f2")},f025:function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"channel-info-content"},[t("el-form",{attrs:{"label-position":"left","label-width":"100px"}},[t("el-form-item",{attrs:{label:"账号管理："}},[t("el-button",{staticClass:"m-r-20",attrs:{type:"text"},on:{click:function(t){return e.showDrawer("add")}}},[e._v("添加账号")]),t("div",{staticClass:"table-style"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],attrs:{data:e.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},e._l(e.tableSetting,(function(n,r){return t("table-column",{key:r,attrs:{col:n},scopedSlots:e._u([{key:"isDoubleFactor",fn:function(n){var r=n.row;return[t("el-switch",{on:{change:function(t){return e.openOrNot(r)}},model:{value:r.is_double_factor,callback:function(t){e.$set(r,"is_double_factor",t)},expression:"row.is_double_factor"}})]}},{key:"operation",fn:function(n){var r=n.row;return[t("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.showDrawer("edit",r)}}},[e._v("编辑")]),t("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.deleteHandle(r)}}},[e._v("移除")])]}}],null,!0)})})),1),t("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[t("el-pagination",{staticClass:"ps-text",attrs:{"current-page":e.page,"page-sizes":[10,20,50,100,500],"page-size":e.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)],1)],1),t("div",{staticClass:"ps-el-drawer"},[t("el-drawer",{attrs:{title:"add"===e.drawerType?"添加账号":"编辑账号",visible:e.addAccountDrawerShow,"show-close":!1,size:"40%"}},[t("div",{staticClass:"p-20"},[t("div",{staticClass:"red font-size-14 m-b-20"},[e._v("提示：账号、手机号只能对应一个监管渠道。")]),t("el-form",{ref:"addAccountFormRef",attrs:{rules:e.addAccountFormRule,model:e.addAccountForm,"label-width":"80px","label-position":"right"}},[t("el-form-item",{attrs:{label:"用户姓名",prop:"name"}},[t("el-input",{staticClass:"w-300",attrs:{placeholder:"请输入姓名",maxlength:"10"},model:{value:e.addAccountForm.name,callback:function(t){e.$set(e.addAccountForm,"name",t)},expression:"addAccountForm.name"}})],1),t("el-form-item",{attrs:{label:"手机号码",prop:"phone"}},[t("el-input",{staticClass:"w-300",attrs:{placeholder:"请输入手机号码",maxlength:"11"},model:{value:e.addAccountForm.phone,callback:function(t){e.$set(e.addAccountForm,"phone",t)},expression:"addAccountForm.phone"}})],1),t("el-form-item",{attrs:{label:"登录账号",prop:"account"}},[t("el-input",{staticClass:"w-300",attrs:{placeholder:"请输入登录账号",maxlength:"12",disabled:"edit"===e.drawerType},model:{value:e.addAccountForm.account,callback:function(t){e.$set(e.addAccountForm,"account",t)},expression:"addAccountForm.account"}})],1),t("el-form-item",{attrs:{label:"登录密码",prop:"password",rules:[{required:"add"===e.drawerType,message:"请输入密码",trigger:["change","blur"]},{min:8,message:"登录密码不能小于8位",trigger:["change","blur"]},{pattern:/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,20}$/,message:"请输入8~20位数，需包含英文和数字的密码",trigger:["change","blur"]}]}},[t("el-input",{staticClass:"w-300",attrs:{placeholder:"add"===e.drawerType?"请输入登录密码":"无需修改密码则不填",maxlength:"20"},model:{value:e.addAccountForm.password,callback:function(t){e.$set(e.addAccountForm,"password",t)},expression:"addAccountForm.password"}})],1),t("el-form-item",{attrs:{label:"登录校验",prop:"isCheck"}},[t("div",{staticClass:"ps-flex-align-c flex-align-c"},[t("div",{staticClass:"m-r-10"},[e._v("手机验证码校验")]),t("el-switch",{model:{value:e.addAccountForm.isCheck,callback:function(t){e.$set(e.addAccountForm,"isCheck",t)},expression:"addAccountForm.isCheck"}})],1)])],1),t("div",{staticClass:"ps-el-drawer-footer"},[t("el-button",{staticClass:"w-100",attrs:{size:"small"},on:{click:e.cancelHandle}},[e._v("取消")]),t("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:function(t){return e.saveHandle("save")}}},[e._v("保存")]),t("el-button",{directives:[{name:"show",rawName:"v-show",value:"add"===e.drawerType,expression:"drawerType === 'add'"}],staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:function(t){return e.saveHandle("keepGoing")}}},[e._v("保存并继续")])],1)],1)])],1)],1)},a=[],i=n("ed08"),o=n("8237"),s=n.n(o),l={props:{organizationData:{type:Object,default:function(){return{}}},tabType:{type:String,default:""}},data:function(){return{isLoading:!1,tableData:[],tableSetting:[{label:"姓名",key:"name"},{label:"账号",key:"username"},{label:"手机号",key:"mobile"},{label:"验证码校验",key:"is_double_factor",type:"slot",slotName:"isDoubleFactor"},{label:"修改时间",key:"update_time"},{label:"操作人",key:"operator_name"},{label:"操作",key:"operation",type:"slot",slotName:"operation",width:"200"}],drawerType:"",selectId:"",addAccountDrawerShow:!1,addAccountForm:{name:"",phone:"",account:"",password:"",isCheck:!1},page:1,pageSize:10,totalCount:0,addAccountFormRule:{name:[{required:!0,message:"请输入姓名",trigger:["change","blur"]}],phone:[{required:!0,message:"请输入手机号",trigger:["change","blur"]},{min:8,message:"请输入正确的手机号",trigger:["change","blur"]}],account:[{required:!0,message:"请输入登录账号",trigger:["change","blur"]},{min:5,message:"登录账号不能小于5位",trigger:["change","blur"]}]}}},watch:{tabType:{handler:function(e,t){"accountManagement"===e&&this.getAccountDataList()},immediate:!0}},created:function(){this.getAccountDataList()},methods:{showDrawer:function(e,t){var n=this;this.drawerType=e,"edit"===e?(this.selectId=t.id,this.addAccountForm.name=t.name,this.addAccountForm.phone=t.mobile,this.addAccountForm.account=t.username,this.addAccountForm.password="",this.addAccountForm.isCheck=!1):(this.selectId="",this.addAccountForm.name="",this.addAccountForm.phone="",this.addAccountForm.account="",this.addAccountForm.password="",this.addAccountForm.isCheck=!1),this.addAccountDrawerShow=!0,setTimeout((function(){n.$refs.addAccountFormRef.clearValidate()}),10)},handleSizeChange:function(e){this.pageSize=e,this.getAccountDataList()},handleCurrentChange:function(e){this.page=e,this.getAccountDataList()},cancelHandle:function(){this.$refs.addAccountFormRef.resetFields(),this.addAccountDrawerShow=!1},saveHandle:function(e){var t=this;this.$refs.addAccountFormRef.validate((function(n){if(!n)return t.$message.error("请检查表单填写的内容是否正确");var r={id:"add"===t.drawerType?void 0:t.selectId,supervision_channel_id:t.organizationData.id,username:t.addAccountForm.account,password:t.addAccountForm.password?s()(t.addAccountForm.password):void 0,mobile:t.addAccountForm.phone,name:t.addAccountForm.name,is_double_factor:t.addAccountForm.isCheck};"add"===t.drawerType?t.addAccount(r,e):t.editAccount(r)}))},getAccountDataList:function(){var e=this;this.isLoading=!0,this.$apis.apiBackgroundFundSupervisionAuditAccountListPost({supervision_channel_id:this.organizationData.id}).then((function(t){e.isLoading=!1,0===t.code&&(e.tableData=Object(i["f"])(t.data.results||[]),e.totalCount=t.data.count)}))},addAccount:function(e,t){var n=this;this.$apis.apiBackgroundFundSupervisionAuditAccountAddPost(e).then((function(e){if(0===e.code){if(n.$message.success("新增成功"),n.$refs.addAccountFormRef.resetFields(),"keepGoing"===t)return n.getAccountDataList();n.addAccountDrawerShow=!1,n.getAccountDataList()}else n.$message.error(e.msg)}))},editAccount:function(e){var t=this;this.$apis.apiBackgroundFundSupervisionAuditAccountModifyPost(e).then((function(e){0===e.code?(t.$refs.addAccountFormRef.resetFields(),t.$message.success("修改成功")):t.$message.error(e.msg),t.addAccountDrawerShow=!1,t.getAccountDataList()}))},openOrNot:function(e){var t={id:e.id,username:e.username,is_double_factor:e.is_double_factor};this.editAccount(t)},deleteHandle:function(e){var t=this;this.$confirm("确定要删除 ".concat(e.name," 的人员信息，删除后不可恢复，请谨慎操作。"),"提示",{distinguishCancelAndClose:!0,confirmButtonText:"确定",cancelButtonText:"取消"}).then((function(){t.$apis.apiBackgroundFundSupervisionAuditAccountDeletePost({ids:[e.id]}).then((function(e){0===e.code?t.$message.success("删除成功"):t.$message.error(e.msg),t.getAccountDataList()}))})).catch((function(e){t.$message("已取消删除")}))}}},c=l,u=(n("e5129"),n("2877")),d=Object(u["a"])(c,r,a,!1,null,"5e4db5e3",null);t["default"]=d.exports},fdcc:function(e,t,n){"use strict";n("921e")}}]);