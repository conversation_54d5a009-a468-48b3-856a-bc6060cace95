(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-upgrade-upgradeService","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-meal-report-MealPackageRule","view-upgrade-upgradeServiceContent"],{"6efc":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"upgrade-service container-wrapper"},[t("refresh-tool",{attrs:{"show-refresh":!1}}),t("div",{staticClass:"table-wrapper",staticStyle:{padding:"15px 40px"}},[t("el-radio-group",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],on:{input:e.gotoRecord},model:{value:e.chargeTypeRadio,callback:function(t){e.chargeTypeRadio=t},expression:"chargeTypeRadio"}},[1===e.serviceType?t("el-radio-button",{attrs:{label:"expansion"}},[e._v("扩容")]):e._e(),3!==e.serviceType?t("el-radio-button",{attrs:{label:"renew"}},[e._v("续费")]):e._e(),t("el-radio-button",{attrs:{label:"record"}},[e._v("交易记录")])],1),"record"!==e.chargeTypeRadio?t("upgradeServiceContent",{ref:"upgradeServiceContent",attrs:{companyInfo:e.companyInfo,serviceType:e.serviceType,chargeTypeRadioNum:e.chargeTypeRadio},on:{refresh:e.getMerchantInfo,showQRCode:e.showQRCode}}):e._e(),"record"===e.chargeTypeRadio?t("div",[t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[e._v("交易记录")]),t("div",{staticClass:"float-r"},[t("button-icon",{attrs:{color:"origin"},on:{click:e.goExport}},[e._v("导出Excel")])],1)]),t("div",{staticClass:"table-content"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableView",attrs:{data:e.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},e._l(e.tableSetting,(function(i){return t("table-column",{key:i.key,attrs:{col:i},scopedSlots:e._u([{key:"realFee",fn:function(i){var n=i.row;return[t("div",[e._v(" ￥"+e._s("ORDER_SUCCESS"===n.order_status?e.realFee(n.real_fee):e.realFee(n.origin_fee))+" ")])]}},{key:"voucherUrl",fn:function(i){var n=i.row;return[t("div",[e._v(" "+e._s("ORDER_SUCCESS"===n.order_status||"alipay"===n.pay_method||"wxpay"===n.pay_method?"--":n.voucher_url?"已上传":"未上传")+" ")])]}},{key:"operation",fn:function(i){var n=i.row;return["transfer"===n.pay_method?t("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.showCredentialDialog(n)}}},[e._v(e._s(n.voucher_url?"重新上传":"上传凭证"))]):e._e(),3!==n.invoice_status?t("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.showInvoiceApplyDialog(n)}}},[e._v(e._s(2===n.invoice_status?"申请发票":"重新申请"))]):e._e()]}}],null,!0)})})),1)],1),t("ul",{staticClass:"total m-t-10"},[t("li",[e._v(" 交易总金额: "),t("span",[e._v("￥"+e._s(e.realFee(e.total_fee)))])])]),t("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[t("el-pagination",{staticClass:"ps-text",attrs:{"current-page":e.page,"page-sizes":[10,20,50,100,500],"page-size":e.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)]):e._e()],1),t("div",{staticClass:"ps-el-drawer"},[t("el-drawer",{attrs:{title:"对公转账流程",visible:e.flowDialogShow,"show-close":!1,size:"55%"}},[t("div",{staticClass:"p-20"},[[t("div",{staticClass:"dialog-content"},[t("div",{staticClass:"dialog-content-item"},[t("div",{staticClass:"dialog-content-item-title"},[e._v(" 1、已生成订单 ")]),t("div",{staticClass:"dialog-content-item-content"},[e._v(" 订单号："),t("span",{attrs:{id:"dialog-tradeNo"}},[e._v(e._s(e.tradeNO))]),t("span",{staticStyle:{"margin-left":"20px",color:"#02A7F0"},attrs:{"data-clipboard-target":"#dialog-tradeNo",id:"copy-btn"},on:{click:e.copyFun}},[e._v(" 点此复制 ")])])]),t("div",{staticClass:"dialog-content-item"},[t("div",{staticClass:"dialog-content-item-title"},[e._v(" 2、线下汇款 ")]),t("div",{staticClass:"dialog-content-item-content"},[e._v(" 汇款信息： ")]),t("div",{staticClass:"dialog-content-item-content"},[e._v(" 金额：￥"+e._s(e.totalPrice)+" ")]),t("div",{staticClass:"dialog-content-item-content"},[e._v(" 开户名：广州市派克朴食科技责任有限公司 ")]),t("div",{staticClass:"dialog-content-item-content"},[e._v(" 开户行：中国建设银行股份有限公司广州太古汇支行 ")]),t("div",{staticClass:"dialog-content-item-content"},[e._v(" 账号：1234567890987654321 ")]),t("div",{staticClass:"dialog-content-item-content"},[e._v(" 备注："+e._s(e.tradeNO)+" "),t("span",{staticClass:"tip"},[e._v("*汇款备注：打款时请务必填写此备注信息（点击上方复制订单号）")])])]),t("div",{staticClass:"dialog-content-item"},[t("div",{staticClass:"dialog-content-item-title"},[e._v(" 3、等待处理 ")]),t("div",{staticClass:"dialog-content-item-content"},[t("div",[e._v("汇款后，请前往交易记录处上传汇款凭证，我们将及时为您处理（3~5个工作日）")]),t("div",[e._v("如有任何疑问，请联系客服：4008082098")])])])])],t("div",{staticClass:"ps-el-drawer-footer ps-flex-align-c flex-align-c"},[t("div",{staticClass:"m-r-30"},[t("el-button",{staticClass:"w-100",attrs:{size:"small"},on:{click:e.cancelOrder}},[e._v("取消")]),t("el-button",{staticClass:"w-100 ps-btn",attrs:{size:"small"},on:{click:e.orderConfirmation}},[e._v("保存")])],1)])],2)]),t("el-drawer",{attrs:{title:"扫码支付",visible:e.QRcodeShow,"show-close":!1,size:"25%"}},[t("div",{staticClass:"p-20"},[t("div",[t("qrcode",{attrs:{value:e.paymentQRCode,options:{width:300},margin:10,alt:""}})],1),t("div",{staticClass:"ps-el-drawer-footer ps-flex-align-c flex-align-c"},[t("div",{staticClass:"m-r-30"},[t("el-button",{staticClass:"w-100",attrs:{size:"small"},on:{click:e.handleClose}},[e._v("取消")]),t("el-button",{staticClass:"w-100 ps-btn",attrs:{size:"small"},on:{click:e.confirmPayment}},[e._v("我已完成付款")])],1)])])]),t("el-drawer",{attrs:{title:"对公转账流程",visible:e.invoiceApplyDialogShow,"show-close":!1,size:"30%"}},[t("div",{staticClass:"p-20"},[t("el-form",{ref:"invoiceForm",staticClass:"invoice-form",attrs:{model:e.invoiceInfoData,"status-icon":"",rules:1===e.invoiceInfoData.invoice_type?e.invoiceInfoRules1:e.invoiceInfoRules2,"label-width":"80px","label-position":"left"},nativeOn:{submit:function(e){e.preventDefault()}}},[t("div",{staticClass:"invoice-form-title"},[e._v("发票类型：")]),t("el-form-item",[t("el-radio-group",{on:{change:e.changeInvoiceType},model:{value:e.invoiceInfoData.invoice_type,callback:function(t){e.$set(e.invoiceInfoData,"invoice_type",t)},expression:"invoiceInfoData.invoice_type"}},[t("el-radio",{attrs:{label:1}},[e._v("电子普通发票")]),t("el-radio",{attrs:{label:2}},[e._v("增值税专用发票")])],1)],1),t("div",{staticClass:"invoice-form-title"},[e._v("公司信息：")]),1===e.invoiceInfoData.invoice_type?t("el-form-item",{attrs:{label:"发票抬头",prop:"title"}},[t("el-input",{staticClass:"w-220",attrs:{placeholder:"请填写"},model:{value:e.invoiceInfoData.title,callback:function(t){e.$set(e.invoiceInfoData,"title",t)},expression:"invoiceInfoData.title"}})],1):e._e(),2===e.invoiceInfoData.invoice_type?t("el-form-item",{attrs:{label:"客户名称",prop:"customer_name"}},[t("el-input",{staticClass:"w-220",attrs:{placeholder:"请填写"},model:{value:e.invoiceInfoData.customer_name,callback:function(t){e.$set(e.invoiceInfoData,"customer_name",t)},expression:"invoiceInfoData.customer_name"}})],1):e._e(),t("el-form-item",{attrs:{label:"税号",prop:"tax_no"}},[t("el-input",{staticClass:"w-220",attrs:{placeholder:1===e.invoiceInfoData.invoice_type?"请填写纳税人识别号":"请填写"},model:{value:e.invoiceInfoData.tax_no,callback:function(t){e.$set(e.invoiceInfoData,"tax_no",t)},expression:"invoiceInfoData.tax_no"}})],1),2===e.invoiceInfoData.invoice_type?t("el-form-item",{attrs:{label:"地址",prop:"company_address"}},[t("el-input",{staticClass:"w-220",attrs:{placeholder:"请填写"},model:{value:e.invoiceInfoData.company_address,callback:function(t){e.$set(e.invoiceInfoData,"company_address",t)},expression:"invoiceInfoData.company_address"}})],1):e._e(),2===e.invoiceInfoData.invoice_type?t("el-form-item",{attrs:{label:"电话",prop:"company_phone"}},[t("el-input",{staticClass:"w-220",attrs:{placeholder:"请填写"},model:{value:e.invoiceInfoData.company_phone,callback:function(t){e.$set(e.invoiceInfoData,"company_phone",t)},expression:"invoiceInfoData.company_phone"}})],1):e._e(),2===e.invoiceInfoData.invoice_type?t("el-form-item",{attrs:{label:"开户行",prop:"bank_name"}},[t("el-input",{staticClass:"w-220",attrs:{placeholder:"请填写"},model:{value:e.invoiceInfoData.bank_name,callback:function(t){e.$set(e.invoiceInfoData,"bank_name",t)},expression:"invoiceInfoData.bank_name"}})],1):e._e(),2===e.invoiceInfoData.invoice_type?t("el-form-item",{attrs:{label:"账户",prop:"account"}},[t("el-input",{staticClass:"w-220",attrs:{placeholder:"请填写"},model:{value:e.invoiceInfoData.account,callback:function(t){e.$set(e.invoiceInfoData,"account",t)},expression:"invoiceInfoData.account"}})],1):e._e(),t("div",{staticClass:"invoice-form-title"},[e._v("接收信息：")]),1===e.invoiceInfoData.invoice_type?t("el-form-item",{attrs:{label:"邮箱地址",prop:"email"}},[t("el-input",{staticClass:"w-220",attrs:{placeholder:"请填写接收发票的邮箱地址"},model:{value:e.invoiceInfoData.email,callback:function(t){e.$set(e.invoiceInfoData,"email",t)},expression:"invoiceInfoData.email"}})],1):e._e(),2===e.invoiceInfoData.invoice_type?t("el-form-item",{attrs:{label:"邮寄地址",prop:"mailing_address"}},[t("el-input",{staticClass:"w-220",attrs:{placeholder:"请填写接收发票的邮寄地址"},model:{value:e.invoiceInfoData.mailing_address,callback:function(t){e.$set(e.invoiceInfoData,"mailing_address",t)},expression:"invoiceInfoData.mailing_address"}})],1):e._e()],1),t("div",{staticClass:"ps-el-drawer-footer ps-flex-align-c flex-align-c"},[t("div",{staticClass:"m-r-30"},[t("el-button",{staticClass:"w-100",attrs:{size:"small"},on:{click:e.handleClose}},[e._v("取消")]),t("el-button",{staticClass:"w-100 ps-btn",attrs:{disabled:e.isLoading,size:"small",type:"primary"},on:{click:e.submitApply}},[e._v(" 提交申请 ")])],1)])],1)]),t("el-drawer",{attrs:{title:"对公转账流程",visible:e.uploadCredentialsShow,"show-close":!1,size:"35%"}},[t("div",{staticClass:"p-20"},[[t("div",{staticClass:"upload-dialog-content"},[t("div",{staticClass:"content-detail m-b-20"},[t("span",{staticClass:"content-title"},[e._v("上传汇款凭证后，我们将及时为您处理（3~5个工作日）")])]),t("div",{staticClass:"inline-block upload-w"},[t("el-upload",{directives:[{name:"loading",rawName:"v-loading",value:e.uploading,expression:"uploading"}],ref:"fileUpload",staticClass:"upload-demo",attrs:{"element-loading-text":"上传中",action:e.serverUrl,data:e.uploadParams,"file-list":e.fileLists,"on-success":e.uploadSuccess,"before-upload":e.beforeFoodImgUpload,limit:1,multiple:!1,"show-file-list":!1,headers:e.headersOpts,accept:".jpeg,.jpg,.png,.bmp"}},[e._t("default",(function(){return[e.formData.imageList.length?e._e():t("el-button",{staticClass:"upload-button m-b-20",attrs:{size:"small",type:"primary"}},[e._v(" 选择文件 ")]),e.formData.imageList.length?t("el-image",{staticClass:"upload-image m-b-20",attrs:{src:e.formData.imageList[0],fit:"contain"}}):e._e()]}))],2)],1),t("div",{staticClass:"content-detail font-size-12"},[t("span",{staticClass:"m-b-5"},[e._v("上传格式：JPG、PNG")]),t("span",[e._v("文件大小：不大于5M")])])])],t("div",{staticClass:"ps-el-drawer-footer ps-flex-align-c flex-align-c"},[t("div",{staticClass:"m-r-30"},[t("el-button",{staticClass:"w-100",attrs:{size:"small"},on:{click:e.handleClose}},[e._v("取消")]),t("el-button",{staticClass:"w-100 ps-btn",attrs:{size:"small"},on:{click:e.submitCredential}},[e._v("确认上传")])],1)])],2)])],1)],1)},a=[],o=i("ed08"),r=i("f63a"),s=i("b735"),c=i("c9d9"),l=i("b2e5"),u=i.n(l),d=i("5a0c"),f=i.n(d),p=i("b311"),m=i.n(p);function v(e){return v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},v(e)}function _(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */_=function(){return t};var e,t={},i=Object.prototype,n=i.hasOwnProperty,a=Object.defineProperty||function(e,t,i){e[t]=i.value},o="function"==typeof Symbol?Symbol:{},r=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function l(e,t,i){return Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,i){return e[t]=i}}function u(e,t,i,n){var o=t&&t.prototype instanceof y?t:y,r=Object.create(o.prototype),s=new P(n||[]);return a(r,"_invoke",{value:S(e,i,s)}),r}function d(e,t,i){try{return{type:"normal",arg:e.call(t,i)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var f="suspendedStart",p="suspendedYield",m="executing",h="completed",g={};function y(){}function b(){}function w(){}var C={};l(C,r,(function(){return this}));var I=Object.getPrototypeOf,k=I&&I(I($([])));k&&k!==i&&n.call(k,r)&&(C=k);var x=w.prototype=y.prototype=Object.create(C);function D(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function T(e,t){function i(a,o,r,s){var c=d(e[a],e,o);if("throw"!==c.type){var l=c.arg,u=l.value;return u&&"object"==v(u)&&n.call(u,"__await")?t.resolve(u.__await).then((function(e){i("next",e,r,s)}),(function(e){i("throw",e,r,s)})):t.resolve(u).then((function(e){l.value=e,r(l)}),(function(e){return i("throw",e,r,s)}))}s(c.arg)}var o;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){i(e,n,t,a)}))}return o=o?o.then(a,a):a()}})}function S(t,i,n){var a=f;return function(o,r){if(a===m)throw Error("Generator is already running");if(a===h){if("throw"===o)throw r;return{value:e,done:!0}}for(n.method=o,n.arg=r;;){var s=n.delegate;if(s){var c=L(s,n);if(c){if(c===g)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===f)throw a=h,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=m;var l=d(t,i,n);if("normal"===l.type){if(a=n.done?h:p,l.arg===g)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(a=h,n.method="throw",n.arg=l.arg)}}}function L(t,i){var n=i.method,a=t.iterator[n];if(a===e)return i.delegate=null,"throw"===n&&t.iterator.return&&(i.method="return",i.arg=e,L(t,i),"throw"===i.method)||"return"!==n&&(i.method="throw",i.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var o=d(a,t.iterator,i.arg);if("throw"===o.type)return i.method="throw",i.arg=o.arg,i.delegate=null,g;var r=o.arg;return r?r.done?(i[t.resultName]=r.value,i.next=t.nextLoc,"return"!==i.method&&(i.method="next",i.arg=e),i.delegate=null,g):r:(i.method="throw",i.arg=new TypeError("iterator result is not an object"),i.delegate=null,g)}function O(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function R(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function P(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(O,this),this.reset(!0)}function $(t){if(t||""===t){var i=t[r];if(i)return i.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,o=function i(){for(;++a<t.length;)if(n.call(t,a))return i.value=t[a],i.done=!1,i;return i.value=e,i.done=!0,i};return o.next=o}}throw new TypeError(v(t)+" is not iterable")}return b.prototype=w,a(x,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:b,configurable:!0}),b.displayName=l(w,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,l(e,c,"GeneratorFunction")),e.prototype=Object.create(x),e},t.awrap=function(e){return{__await:e}},D(T.prototype),l(T.prototype,s,(function(){return this})),t.AsyncIterator=T,t.async=function(e,i,n,a,o){void 0===o&&(o=Promise);var r=new T(u(e,i,n,a),o);return t.isGeneratorFunction(i)?r:r.next().then((function(e){return e.done?e.value:r.next()}))},D(x),l(x,c,"Generator"),l(x,r,(function(){return this})),l(x,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),i=[];for(var n in t)i.push(n);return i.reverse(),function e(){for(;i.length;){var n=i.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=$,P.prototype={constructor:P,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(R),!t)for(var i in this)"t"===i.charAt(0)&&n.call(this,i)&&!isNaN(+i.slice(1))&&(this[i]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var i=this;function a(n,a){return s.type="throw",s.arg=t,i.next=n,a&&(i.method="next",i.arg=e),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o],s=r.completion;if("root"===r.tryLoc)return a("end");if(r.tryLoc<=this.prev){var c=n.call(r,"catchLoc"),l=n.call(r,"finallyLoc");if(c&&l){if(this.prev<r.catchLoc)return a(r.catchLoc,!0);if(this.prev<r.finallyLoc)return a(r.finallyLoc)}else if(c){if(this.prev<r.catchLoc)return a(r.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<r.finallyLoc)return a(r.finallyLoc)}}}},abrupt:function(e,t){for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var r=o?o.completion:{};return r.type=e,r.arg=t,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(r)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.finallyLoc===e)return this.complete(i.completion,i.afterLoc),R(i),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.tryLoc===e){var n=i.completion;if("throw"===n.type){var a=n.arg;R(i)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,i,n){return this.delegate={iterator:$(t),resultName:i,nextLoc:n},"next"===this.method&&(this.arg=e),g}},t}function h(e,t,i,n,a,o,r){try{var s=e[o](r),c=s.value}catch(e){return void i(e)}s.done?t(c):Promise.resolve(c).then(n,a)}function g(e){return function(){var t=this,i=arguments;return new Promise((function(n,a){var o=e.apply(t,i);function r(e){h(o,n,a,r,s,"next",e)}function s(e){h(o,n,a,r,s,"throw",e)}r(void 0)}))}}function y(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,n)}return i}function b(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?y(Object(i),!0).forEach((function(t){w(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):y(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function w(e,t,i){return(t=C(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function C(e){var t=I(e,"string");return"symbol"==v(t)?t:t+""}function I(e,t){if("object"!=v(e)||!e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var n=i.call(e,t||"default");if("object"!=v(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var k={name:"upgradeServive",mixins:[r["a"]],components:{upgradeServiceContent:s["default"],qrcode:u.a},data:function(){return{chargeTypeRadio:"",isLoading:!1,companyInfo:{},serviceType:0,tableSetting:[{label:"订单号",key:"trade_no"},{label:"创建时间",key:"create_time"},{label:"支付时间",key:"pay_time"},{label:"到账时间",key:"finish_time"},{label:"支付金额",key:"real_fee",type:"slot",slotName:"realFee"},{label:"支付方式",key:"pay_method_alias"},{label:"交易类型",key:"transaction_type_alias"},{label:"订单状态",key:"order_status_alias"},{label:"转账凭证",key:"voucher_url",type:"slot",slotName:"voucherUrl"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"200"}],tableData:[],page:1,pageSize:10,totalCount:0,total_fee:0,orderId:"",invoiceApplyDialogShow:!1,invoiceInfoData:{invoice_type:1,title:"",tax_no:"",customer_name:"",company_address:"",company_phone:"",bank_name:"",account:"",email:"",mailing_address:""},invoiceInfoRules1:{title:[{required:!0,message:"此项不能为空",trigger:"blur"}],tax_no:[{required:!0,message:"此项不能为空",trigger:"blur"}],email:[{required:!0,message:"此项不能为空",trigger:"blur"},{pattern:/^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/,message:"请输入正确的邮箱地址",trigger:"blur"}]},invoiceInfoRules2:{tax_no:[{required:!0,message:"此项不能为空",trigger:"blur"}],customer_name:[{required:!0,message:"此项不能为空",trigger:"blur"}],company_address:[{required:!0,message:"此项不能为空",trigger:"blur"}],company_phone:[{required:!0,message:"此项不能为空",trigger:"blur"},{pattern:/^[0-9]*$/,message:"只能输入数字",trigger:"change"}],bank_name:[{required:!0,message:"此项不能为空",trigger:"blur"}],account:[{required:!0,message:"此项不能为空",trigger:"blur"}],mailing_address:[{required:!0,message:"此项不能为空",trigger:"blur"}]},uploadCredentialsShow:!1,uploading:!1,serverUrl:"/api/background/file/upload",uploadParams:{prefix:"be_on_loan_img"},fileLists:[],headersOpts:{TOKEN:Object(o["B"])()},formData:{imageList:[]},invoiceList:[],sameInvoiceInfo:[],isAdd:!1,invoiceStatus:0,paymentQRCode:"",QRcodeShow:!1,tradeNO:"",clauseShow:!1,flowDialogShow:!1,totalPrice:0}},created:function(){this.getMerchantInfo()},watch:{serviceType:function(e){1===this.serviceType?this.chargeTypeRadio="expansion":2===this.serviceType?this.chargeTypeRadio="renew":this.chargeTypeRadio="record"}},computed:{realFee:function(){return function(e){return Object(c["f"])(e)}}},methods:{initLoad:function(){this.getChargeOrderList(),this.getInvoiceList()},handleSizeChange:function(e){this.isLoading=!0,this.pageSize=e,this.page=1,this.initLoad()},handleCurrentChange:function(e){this.isLoading=!0,this.page=e,this.pageSize=10,this.initLoad()},getMerchantInfo:function(){var e=this;this.isLoading=!0,this.$apis.apiBackgroundTollBackgroundTollGetSettingsPost().then((function(t){0===t.code?(e.isLoading=!1,e.companyInfo=t.data,e.serviceType=t.data.toll_type):e.$message.error(t.msg)}))},goExport:function(){var e={type:"Transaction",url:"apiBackgroundTollBackgroundTollOrderListExportPost"};this.exportHandle(e)},getChargeOrderList:function(){var e=this;this.isLoading=!0;var t={page:this.page,page_size:this.pageSize};this.$apis.apiBackgroundTollBackgroundTollOrderListPost(t).then((function(t){0===t.code?(t.data.results.map((function(e){return e.pay_time&&(e.pay_time=f()(e.pay_time).format("YYYY-MM-DD HH:mm:ss")),e.finish_time&&(e.finish_time=f()(e.finish_time).format("YYYY-MM-DD HH:mm:ss")),e})),e.tableData=t.data.results,e.totalCount=t.data.count,e.total_fee=t.data.total_fee):e.$message.error(t.msg)})),this.isLoading=!1},handleClose:function(){this.flowDialogShow=!1,this.clauseShow=!1,this.QRcodeShow=!1,this.invoiceApplyDialogShow=!1,this.uploadCredentialsShow=!1},beforeFoodImgUpload:function(e){var t=[".jpeg",".jpg",".png",".bmp"],i=e.size/1024/1024<=5;return t.includes(Object(o["A"])(e.name))?i?void(this.uploading=!0):(this.$message.error("上传图片大小不能超过 5MB!"),!1):(this.$message.error("上传图片只能是 JPG 格式或者PNG格式!"),!1)},uploadSuccess:function(e,t,i){this.uploading=!1,0===e.code?(this.fileLists=i,this.formData.imageList=[e.data.public_url]):this.$message.error(e.msg)},submitCredential:function(){var e=this;if(!this.formData.imageList[0])return this.$message.error("请先选择文件");this.$apis.apiBackgroundTollBackgroundTollOrderAddVoucherUrlPost({id:this.orderId,url:this.formData.imageList[0]}).then((function(t){0===t.code?(e.$message.success("上传成功"),e.uploadCredentialsShow=!1,e.formData.imageList=[],e.fileLists=[],e.isLoading=!0,e.initLoad()):(e.$message.error(t.msg),e.uploadCredentialsShow=!1)})),this.isLoading=!1},showCredentialDialog:function(e){this.orderId=e.id,this.uploadCredentialsShow=!0},showInvoiceApplyDialog:function(e){this.sameInvoiceInfo=this.invoiceList.filter((function(t){return t.id===e.invoice_info})),0!==this.sameInvoiceInfo.length&&(this.invoiceInfoData=b({},this.sameInvoiceInfo[0])),this.orderId=e.id,this.invoiceStatus=e.invoice_status,this.invoiceApplyDialogShow=!0},changeInvoiceType:function(){this.$refs.invoiceForm.clearValidate()},getInvoiceList:function(){var e=this;this.$apis.apiBackgroundTollBackgroundInvoiceInfoListPost().then((function(t){0===t.code?e.invoiceList=t.data.results:e.$message.error(t.msg)}))},submitApply:function(){var e=this;return g(_().mark((function t(){return _().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$refs.invoiceForm.validate(function(){var t=g(_().mark((function t(i){var n,a,o,r;return _().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!i){t.next=16;break}if(n={},n=1===e.invoiceInfoData.invoice_type?{invoice_type:1,title:e.invoiceInfoData.title,tax_no:e.invoiceInfoData.tax_no,email:e.invoiceInfoData.email}:{invoice_type:2,tax_no:e.invoiceInfoData.tax_no,customer_name:e.invoiceInfoData.customer_name,company_address:e.invoiceInfoData.company_address,company_phone:e.invoiceInfoData.company_phone,bank_name:e.invoiceInfoData.bank_name,account:e.invoiceInfoData.account,mailing_address:e.invoiceInfoData.mailing_address},0===e.sameInvoiceInfo.length){t.next=8;break}a=b({id:e.sameInvoiceInfo[0].id},n),e.modifyInvoiceInfo(a),t.next=13;break;case 8:return t.next=10,e.invoiceInfoAdd(n);case 10:o=t.sent,r={id:e.orderId,invoice_id:o.data.id},e.addInvoiceInfo(r);case 13:e.$refs.invoiceForm.resetFields(),t.next=17;break;case 16:return t.abrupt("return",e.$message.error("请确认信息是否填写正确"));case 17:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}());case 1:case"end":return t.stop()}}),t)})))()},invoiceInfoAdd:function(e){var t=this;return new Promise((function(i,n){t.$apis.apiBackgroundTollBackgroundInvoiceInfoAddPost(e).then((function(e){i(e)})).catch((function(e){n(e)}))}))},addInvoiceInfo:function(e){var t=this;this.$apis.apiBackgroundTollBackgroundTollOrderAddInvoiceInfoPost(e).then((function(e){0===e.code?(t.$message.success("申请发票成功"),t.invoiceApplyDialogShow=!1,t.initLoad()):t.$message.error(e.msg)}))},modifyInvoiceInfo:function(e){var t=this;this.$apis.apiBackgroundTollBackgroundInvoiceInfoModifyPost(e).then((function(e){0===e.code?(t.$message.success("申请发票成功"),t.invoiceApplyDialogShow=!1,t.initLoad()):t.$message.error(e.msg)}))},showQRCode:function(e,t){e.url?(this.QRcodeShow=!0,this.paymentQRCode=e.url):(this.flowDialogShow=!0,this.totalPrice=t),this.tradeNO=e.trade_no},confirmPayment:function(){var e=this;this.isLoading=!0,this.$apis.apiBackgroundTollBackgroundTollOrderListPost({trade_no:this.tradeNO}).then((function(t){if(0===t.code)switch(t.data.results[0].order_status){case"ORDER_SUCCESS":e.$message.success("交易成功");break;case"ORDER_FAILED":e.$message.error("交易失败，请重试");break;case"ORDER_PAYING":e.$message.error("交易失败，该订单待支付，请重试");break;case"ORDER_WATTING_PAY":e.$message.error("该订单等待支付中，请稍等");break;case"ORDER_CLOSE":e.$message.error("该订单已关闭，请重新购买");break;case"ORDER_TIME_OUT":e.$message.error("该订单已过期");break}else e.$message.error(t.msg);e.QRcodeShow=!1,e.getMerchantInfo(),e.initLoad()}))},cancelOrder:function(){var e=this;this.$apis.apiBackgroundAdminBackgroundTollOrderCancelPost({trade_no:this.tradeNO}).then((function(t){0===t.code?(e.flowDialogShow=!1,e.$message.success("已取消本次订单")):e.$message.error(t.msg)}))},copyFun:function(){var e=this,t=new m.a("#copy-btn");t.on("success",(function(i){return i.clearSelection(),t.destroy(),e.$message.success("复制成功")})),t.on("error",(function(i){return t.destroy(),e.$message.error("该浏览器不支持复制")}))},orderConfirmation:function(){this.flowDialogShow=!1,this.$message.success("下单成功")},gotoRecord:function(){"record"===this.chargeTypeRadio&&this.initLoad()}}},x=k,D=(i("71e9"),i("2877")),T=Object(D["a"])(x,n,a,!1,null,"46ebee28",null);t["default"]=T.exports},"71e9":function(e,t,i){"use strict";i("e923")},a6fa:function(e,t,i){"use strict";i("c9a5")},b735:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"upgrade-service-content"},[t("div",{staticClass:"upgrade-service-content-header"},[t("div",{staticClass:"header-left"},[t("div",{staticClass:"w-350 ps-flex-align-c flex-align-c"},[t("span",{staticClass:"m-r-100 f-w-700 font-size-24"},[e._v(e._s(e.companyInfo.company_name))]),t("span",{staticClass:"tips"},[e._v(e._s(e.companyInfo.toll_rule_info?e.companyInfo.toll_rule_info.toll_version_name:""))])]),t("span",[e._v("到期时间："+e._s(e.timeFormat(e.companyInfo.service_end_time)))])]),t("div",{staticClass:"header-right"},[t("div",[t("el-statistic",{class:[e.companyInfo.due_day_num<=30?"red":""],attrs:{title:"剩余服务天数","value-style":e.companyInfo.due_day_num<=30?e.fontStyle:{}}},[t("template",{slot:"formatter"},[e._v(" "+e._s(e.companyInfo.due_day_num)+"天 ")])],2)],1),1===e.serviceType?t("div",[t("el-statistic",{attrs:{title:"用户规模使用情况"}},[t("template",{slot:"formatter"},[e._v(" "+e._s(e.companyInfo.use_user_count)+"人/"+e._s(e.companyInfo.user_scale)+"人 ")])],2)],1):e._e()])]),t("div",{staticClass:"upgrade-service-content-content"},[e._m(0),1===e.serviceType&&"expansion"===e.chargeTypeRadioNum?t("div",{staticClass:"userSize"},[t("div",{staticClass:"userSize-header"},[t("h3",[e._v("用户规模")]),"expansion"===e.chargeTypeRadioNum?t("span",{staticClass:"m-l-20 font-size-14"},[e._v("购买后，用户上限为"+e._s(e.maximum||" -- ")+"人")]):e._e()]),t("div",[t("span",{staticClass:"m-r-20 font-size-14"},[e._v("新增用户上限")]),t("el-input-number",{attrs:{step:500,min:1,max:99999},on:{change:e.handleChange},model:{value:e.userSize,callback:function(t){e.userSize=t},expression:"userSize"}})],1)]):e._e(),"renew"===e.chargeTypeRadioNum?t("div",{staticClass:"userSize"},[e._m(1),t("div",[t("span",{staticClass:"m-r-20 font-size-14"},[e._v("现有用户规模: "+e._s(e.companyInfo.user_scale)+" 人")])])]):e._e(),"renew"===e.chargeTypeRadioNum?t("div",{staticClass:"renew"},[t("div",{staticClass:"renew-header"},[t("h3",[e._v("续费")]),t("span",{staticClass:"m-l-20 font-size-14"},[e._v("服务时间："+e._s(e.timeFormat(e.serviceTime.service_start_time))+" 至 "+e._s(e.timeFormat(e.serviceTime.service_end_time)))])]),t("div",{staticClass:"renew-content"},[t("div",{class:["renew-content-item",1===e.chooseRenew?"isClick":""],on:{click:function(t){e.renew(1,1===e.serviceType?e.price(e.companyInfo.toll_rule_info.fee):e.price(e.companyInfo.renew_fee_list[0]))}}},[t("div",[e._m(2),t("div",{staticClass:"m-t-5"},[t("span",{staticClass:"renew-content-item-price"},[e._v(e._s(1===e.serviceType?"￥"+e.price(e.companyInfo.toll_rule_info.fee):"￥"+e.price(e.companyInfo.renew_fee_list[0])))]),1===e.serviceType?t("span",{staticStyle:{"font-size":"14px"}},[e._v("/人/年")]):e._e()])])]),e.companyInfo.toll_rule_info&&e.companyInfo.toll_rule_info.second_discount||2===e.serviceType?t("div",{class:["renew-content-item",2===e.chooseRenew?"isClick":""],on:{click:function(t){e.renew(2,1===e.serviceType?e.price(e.companyInfo.toll_rule_info.fee*e.price(e.companyInfo.toll_rule_info.second_discount)):e.price(e.companyInfo.renew_fee_list[1]))}}},[1===e.serviceType?t("div",{staticClass:"renew-content-item-tip"},[e._v(" "+e._s(e.discount(e.companyInfo.toll_rule_info.second_discount))+"折 ")]):e._e(),t("div",[e._m(3),t("div",{staticClass:"m-t-5"},[t("span",{staticClass:"renew-content-item-price"},[e._v(" "+e._s(1===e.serviceType?"≈￥"+e.price(e.companyInfo.toll_rule_info.fee*e.price(e.companyInfo.toll_rule_info.second_discount)):"￥"+e.price(e.companyInfo.renew_fee_list[1]))+" ")]),1===e.serviceType?t("span",{staticStyle:{"font-size":"14px"}},[e._v("/人/年")]):e._e()])]),1===e.serviceType?t("span",{staticClass:"renew-content-item-bottom"},[e._v(e._s(e.companyInfo.toll_rule_info.alias))]):e._e()]):e._e(),e.companyInfo.toll_rule_info&&e.companyInfo.toll_rule_info.third_discount||2===e.serviceType?t("div",{class:["renew-content-item",3===e.chooseRenew?"isClick":""],on:{click:function(t){e.renew(3,1===e.serviceType?e.price(e.companyInfo.toll_rule_info.fee*e.price(e.companyInfo.toll_rule_info.third_discount)):e.price(e.companyInfo.renew_fee_list[2]))}}},[1===e.serviceType?t("div",{staticClass:"renew-content-item-tip"},[e._v(" "+e._s(e.discount(e.companyInfo.toll_rule_info.third_discount))+"折 ")]):e._e(),t("div",[e._m(4),t("div",{staticClass:"m-t-5"},[t("span",{staticClass:"renew-content-item-price"},[e._v(" "+e._s(1===e.serviceType?"≈￥"+e.price(e.companyInfo.toll_rule_info.fee*e.price(e.companyInfo.toll_rule_info.third_discount)):"￥"+e.price(e.companyInfo.renew_fee_list[2]))+" ")]),1===e.serviceType?t("span",{staticStyle:{"font-size":"14px"}},[e._v("/人/年")]):e._e()])]),1===e.serviceType?t("span",{staticClass:"renew-content-item-bottom"},[e._v(e._s(e.companyInfo.toll_rule_info.alias))]):e._e()]):e._e(),e.companyInfo.toll_rule_info&&e.companyInfo.toll_rule_info.fourth_discount&&2!==e.serviceType?t("div",{class:["renew-content-item",4===e.chooseRenew?"isClick":""],on:{click:function(t){e.renew(4,e.price(e.companyInfo.toll_rule_info.fee*e.price(e.companyInfo.toll_rule_info.fourth_discount)))}}},[1===e.serviceType?t("div",{staticClass:"renew-content-item-tip"},[e._v(" "+e._s(e.discount(e.companyInfo.toll_rule_info.fourth_discount))+"折 ")]):e._e(),t("div",[e._m(5),t("div",{staticClass:"m-t-5"},[t("span",{staticClass:"renew-content-item-price"},[e._v(" "+e._s("≈￥"+e.price(e.companyInfo.toll_rule_info.fee*e.price(e.companyInfo.toll_rule_info.fourth_discount)))+" ")]),1===e.serviceType?t("span",{staticStyle:{"font-size":"14px"}},[e._v("/人/年")]):e._e()])]),1===e.serviceType?t("span",{staticClass:"renew-content-item-bottom"},[e._v(e._s(e.companyInfo.toll_rule_info.alias))]):e._e()]):e._e(),e.companyInfo.toll_rule_info&&e.companyInfo.toll_rule_info.fifth_discount&&2!==e.serviceType?t("div",{class:["renew-content-item",5===e.chooseRenew?"isClick":""],on:{click:function(t){e.renew(5,e.price(e.companyInfo.toll_rule_info.fee*e.price(e.companyInfo.toll_rule_info.fifth_discount)))}}},[1===e.serviceType?t("div",{staticClass:"renew-content-item-tip"},[e._v(" "+e._s(e.discount(e.companyInfo.toll_rule_info.fifth_discount))+"折 ")]):e._e(),t("div",[e._m(6),t("div",{staticClass:"m-t-5"},[t("span",{staticClass:"renew-content-item-price"},[e._v(" "+e._s("≈￥"+e.price(e.companyInfo.toll_rule_info.fee*e.price(e.companyInfo.toll_rule_info.fifth_discount)))+" ")]),1===e.serviceType?t("span",{staticStyle:{"font-size":"14px"}},[e._v("/人/年")]):e._e()])]),1===e.serviceType?t("span",{staticClass:"renew-content-item-bottom"},[e._v(e._s(e.companyInfo.toll_rule_info.alias))]):e._e()]):e._e()])]):e._e(),t("div",{staticClass:"payWay"},[e._m(7),t("div",{staticClass:"payWay-content"},e._l(e.payWayList,(function(i,n){return t("div",{key:n,class:["payWay-content-item","m-r-20",e.clickNum===n?"isClick":""],on:{click:function(t){return e.selectThis(i)}}},[t("img",{attrs:{src:i.imgUrl}}),t("span",{staticClass:"m-l-16"},[e._v(e._s(i.text))])])})),0)])]),t("div",{staticClass:"upgrade-service-content-footer"},[t("div",{staticClass:"footer-top"},[t("div",{staticClass:"footer-top-left"},[t("span",{staticClass:"font-size-16"},[e._v("实付金额：")]),t("span",{staticClass:"m-l-10 font-size-28 priceShow"},[e._v("￥ "+e._s(e.totalPrice))])]),t("div",{staticClass:"footer-top-right"},[t("span",{staticClass:"font-size-14"},[e._v("付款完成后可申请发票")]),t("div",{staticClass:"buyNow m-l-20",on:{click:e.payNow}},[e._v("立即购买")])])]),t("div",{staticClass:"m-t-10"},[t("el-checkbox",{model:{value:e.isRead,callback:function(t){e.isRead=t},expression:"isRead"}}),t("span",{staticClass:"checkbox-label",style:e.isRead?{color:"#FF9B45"}:{}},[e._v(" 我已认真阅读 ")]),e._l(e.agreementList,(function(i,n){return t("span",{key:n,staticStyle:{color:"#2694F1","text-decoration":"underline","font-size":"14px"},on:{click:function(t){return e.gotoTermsOfService(i)}}},[e._v(" 《"+e._s(i.agreement_type_alias)+"》 ")])}))],2)])])},a=[function(){var e=this,t=e._self._c;return t("span",{staticClass:"font-size-14"},[e._v("如购买服务不满足需求，或需要系统升级，请联系客服："),t("span",{staticStyle:{color:"#2694F1","text-decoration":"underline"}},[e._v("4008082098")])])},function(){var e=this,t=e._self._c;return t("div",{staticClass:"userSize-header"},[t("h3",[e._v("用户规模")])])},function(){var e=this,t=e._self._c;return t("span",[t("span",{staticClass:"renew-content-item-title"},[e._v("1")]),e._v("年")])},function(){var e=this,t=e._self._c;return t("span",[t("span",{staticClass:"renew-content-item-title"},[e._v("2")]),e._v("年")])},function(){var e=this,t=e._self._c;return t("span",[t("span",{staticClass:"renew-content-item-title"},[e._v("3")]),e._v("年")])},function(){var e=this,t=e._self._c;return t("span",[t("span",{staticClass:"renew-content-item-title"},[e._v("4")]),e._v("年")])},function(){var e=this,t=e._self._c;return t("span",[t("span",{staticClass:"renew-content-item-title"},[e._v("5")]),e._v("年")])},function(){var e=this,t=e._self._c;return t("div",[t("h3",[e._v("支付方式")])])}],o=i("5a0c"),r=i("c9d9"),s=i("da92"),c={name:"upgradeServiceContent",props:{companyInfo:{type:Object,default:function(){return{}}},serviceType:{type:[Number,String],default:""},chargeTypeRadioNum:{type:String,default:""}},computed:{timeFormat:function(){return function(e){return o(e).format("YYYY-MM-DD")}},discount:function(){return function(e){if(e)return s["a"].divide(e,10).toFixed(1)}},price:function(){return function(e){if(e)return Object(r["f"])(e)}},maximum:function(){return this.companyInfo.user_scale+this.userSize}},watch:{chargeTypeRadioNum:function(e,t){e!==t&&(this.isRead=!1),"renew"===e&&(this.isRead=!0),this.totalPrice=0,this.clickNum=4},companyInfo:{handler:function(e){e&&(this.serviceTime.service_start_time=e.service_start_time,this.serviceTime.service_end_time=e.service_end_time)},immediate:!0}},data:function(){return{userSize:100,payWayList:[{id:0,imgUrl:i("b4df"),value:"wxpay",text:"微信支付"},{id:1,imgUrl:i("5559"),value:"alipay",text:"支付宝"},{id:2,imgUrl:i("6558"),value:"transfer",text:"对公转账"}],clickNum:4,payMethod:"",chooseRenew:0,renewForPersonAYear:0,totalPrice:0,agreementList:[{agreement_type_alias:"服务条款",agreement_type:"TS"}],isRead:!1,fontStyle:{color:"#fd594e"},serviceTime:{service_start_time:"",service_end_time:""}}},methods:{handleChange:function(){if(!this.userSize)return this.$message.error("用户上限不能为空");if(0===this.companyInfo.due_day_num&&2===this.serviceType)return this.$message.error("目前服务天数为0，请先续费");var e=this.setParams();this.calculatePrice(e)},selectThis:function(e){if(0===this.companyInfo.due_day_num&&2===this.serviceType)return this.$message.error("目前服务天数为0，请先续费");this.clickNum=e.id,this.payMethod=e.value,4!==this.clickNum&&this.handleChange()},renew:function(e,t){this.chooseRenew=e,this.renewForPersonAYear=t,this.serviceTime.service_end_time=o(this.companyInfo.service_end_time).add(e,"year").format("YYYY-MM-DD"),this.handleChange()},payNow:function(){var e=this;if(!this.isRead)return this.$message.error("请阅读服务条款并勾选已读后重试");if(4===this.clickNum)return this.$message.error("请选择支付方式");var t=this.setParams();Object.assign(t,{pay_method:this.payMethod}),t.price=Object(r["g"])(this.totalPrice),this.$apis.apiBackgroundTollBackgroundTollOrderCreatePost(t).then((function(t){0===t.code?(e.$emit("showQRCode",t.data,e.totalPrice),e.$emit("refresh")):e.$message.error(t.msg)}))},setParams:function(){var e={transaction_type:this.chargeTypeRadioNum,price:1===this.serviceType?this.companyInfo.toll_rule_info.fee:0,user_scale:"expansion"===this.chargeTypeRadioNum?this.userSize:this.companyInfo.user_scale};if("renew"===this.chargeTypeRadioNum)switch(Object.assign(e,{renew_year:this.chooseRenew}),this.serviceType){case 1:e.price=Object(r["g"])(this.renewForPersonAYear);break;case 2:if(!this.chooseRenew)return this.$message.error("请选择续费年限");e.price=this.companyInfo.renew_fee_list[this.chooseRenew-1];break}return e},calculatePrice:function(e){var t=this;this.$apis.apiBackgroundTollBackgroundTollOrderGetCalcFeePost(e).then((function(e){0===e.code?t.totalPrice=e.data.calc_fee?Object(r["f"])(e.data.calc_fee):0:t.$message.error(e.msg)}))},gotoTermsOfService:function(e){var t=window.location.origin+"/#/agreement?type="+e.agreement_type+"&key=AGREEMENTLIST";window.open(t,"_blank")}}},l=c,u=(i("a6fa"),i("2877")),d=Object(u["a"])(l,n,a,!1,null,"1b387a12",null);t["default"]=d.exports},c9a5:function(e,t,i){},c9d9:function(e,t,i){"use strict";i.d(t,"a",(function(){return o})),i.d(t,"d",(function(){return r})),i.d(t,"b",(function(){return s})),i.d(t,"c",(function(){return c})),i.d(t,"e",(function(){return l})),i.d(t,"f",(function(){return u})),i.d(t,"g",(function(){return d}));var n=i("5a0c"),a=i("da92"),o=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],r=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],s={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},c=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],l=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],u=(n().subtract(7,"day").format("YYYY-MM-DD"),n().format("YYYY-MM-DD"),function(e){return e?"number"===typeof e?a["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:a["a"].divide(e,100).toFixed(2):"0.00"}),d=function(e){return a["a"].times(e,100)}},e923:function(e,t,i){}}]);