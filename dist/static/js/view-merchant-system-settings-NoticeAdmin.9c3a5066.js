(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-system-settings-NoticeAdmin"],{"21b7":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"container-wrapper",attrs:{id:"notice-admin-container"}},[e("refresh-tool",{on:{refreshPage:t.refreshHandle}}),e("div",{staticClass:"notice-admin-list"},[e("search-form",{ref:"searchRef",attrs:{"form-setting":t.searchFormSetting,autoSearch:!1},on:{search:t.searchHandle,reset:t.resetHandle}}),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("数据列表")]),e("div",{staticClass:"align-r"},[e("button-icon",{attrs:{buttonData:t.buttonData},on:{gotoAddNotice:function(e){return t.gotoAddNotice("add")}}})],1)]),e("div",{staticClass:"table-content"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableData",staticClass:"ps-table",staticStyle:{width:"100%"},attrs:{data:t.tableData,"row-key":"id",border:"","row-class-name":t.tableRowClassName,"header-row-class-name":"ps-table-header-row"}},[e("el-table-column",{attrs:{prop:"title",label:"公告标题",align:"center"}}),e("el-table-column",{attrs:{prop:"organizationIds",label:"接收组织",align:"center"},scopedSlots:t._u([{key:"default",fn:function(r){return["all_org"==r.row.receiver_type?e("span",[t._v("全部组织")]):e("div",[e("el-button",{attrs:{type:"text"},on:{click:function(e){return t.showCompanyDialog(r.row)}}},[t._v("查看")])],1)]}}])}),e("el-table-column",{attrs:{prop:"status_alias",label:"状态",align:"center"}}),e("el-table-column",{attrs:{prop:"read_count",label:"查看人数",align:"center"}}),e("el-table-column",{attrs:{prop:"post_time",label:"发布时间",align:"center"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("span",[t._v(t._s(r.row.post_time?r.row.post_time:"--"))])]}}])}),e("el-table-column",{attrs:{prop:"update_time",label:"修改时间",align:"center"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("span",[t._v(t._s(r.row.update_time?r.row.update_time:"--"))])]}}])}),e("el-table-column",{attrs:{prop:"sender_name",label:"创建人",align:"center"}}),e("el-table-column",{attrs:{fixed:"right",label:"操作",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.goToNoticeDetail(r.row)}}},[t._v("查看")]),e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_messages.messages.mpdify"],expression:"['background_messages.messages.mpdify']"}],attrs:{type:"text",size:"small",disabled:2==r.row.status},on:{click:function(e){return t.addNoticeHandle("modify",r.row)}}},[t._v("编辑")]),e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_messages.messages.delete"],expression:"['background_messages.messages.delete']"}],staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteNoticeHandle(r.row)}}},[t._v("删除")]),e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_messages.messages.bulk_push"],expression:"['background_messages.messages.bulk_push']"}],attrs:{type:"text",size:"small",disabled:2==r.row.status},on:{click:function(e){return t.releaseNoticeHandle(r.row)}}},[t._v("发布")])]}}])})],1)],1),e("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[e("el-pagination",{staticClass:"ps-text",attrs:{"current-page":t.currentPage,"page-sizes":[10,20,30,40],"page-size":t.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:t.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)])],1),e("el-dialog",{attrs:{title:"接收商户",visible:t.dialogVisible,width:"440px","custom-class":"notice-dialog"},on:{"update:visible":function(e){t.dialogVisible=e},closed:t.closeDialogHandle}},[e("div",{staticClass:"content"},[e("el-table",{attrs:{data:t.dialogData,"max-height":"500"}},[e("el-table-column",{attrs:{type:"index",width:"50",label:"序号",align:"center"}}),e("el-table-column",{attrs:{property:"name",label:"商户名称",align:"center"}})],1)],1),e("div",{staticClass:"dialog-footer",staticStyle:{"text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[e("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){t.dialogVisible=!1}}},[t._v("确定")])],1)])],1)},a=[],o=r("ed08");function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},c=o.iterator||"@@iterator",l=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function p(t,e,r,n){var o=e&&e.prototype instanceof y?e:y,i=Object.create(o.prototype),s=new E(n||[]);return a(i,"_invoke",{value:C(t,r,s)}),i}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=p;var g="suspendedStart",h="suspendedYield",m="executing",v="completed",b={};function y(){}function w(){}function x(){}var _={};f(_,c,(function(){return this}));var k=Object.getPrototypeOf,L=k&&k(k($([])));L&&L!==r&&n.call(L,c)&&(_=L);var O=x.prototype=y.prototype=Object.create(_);function j(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function r(a,o,s,c){var l=d(t[a],t,o);if("throw"!==l.type){var u=l.arg,f=u.value;return f&&"object"==i(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,s,c)}),(function(t){r("throw",t,s,c)})):e.resolve(f).then((function(t){u.value=t,s(u)}),(function(t){return r("throw",t,s,c)}))}c(l.arg)}var o;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return o=o?o.then(a,a):a()}})}function C(e,r,n){var a=g;return function(o,i){if(a===m)throw Error("Generator is already running");if(a===v){if("throw"===o)throw i;return{value:t,done:!0}}for(n.method=o,n.arg=i;;){var s=n.delegate;if(s){var c=P(s,n);if(c){if(c===b)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===g)throw a=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=m;var l=d(e,r,n);if("normal"===l.type){if(a=n.done?v:h,l.arg===b)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(a=v,n.method="throw",n.arg=l.arg)}}}function P(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,P(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),b;var o=d(a,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,b;var i=o.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,b):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,b)}function D(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function N(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(D,this),this.reset(!0)}function $(e){if(e||""===e){var r=e[c];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,o=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(i(e)+" is not iterable")}return w.prototype=x,a(O,"constructor",{value:x,configurable:!0}),a(x,"constructor",{value:w,configurable:!0}),w.displayName=f(x,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,x):(t.__proto__=x,f(t,u,"GeneratorFunction")),t.prototype=Object.create(O),t},e.awrap=function(t){return{__await:t}},j(S.prototype),f(S.prototype,l,(function(){return this})),e.AsyncIterator=S,e.async=function(t,r,n,a,o){void 0===o&&(o=Promise);var i=new S(p(t,r,n,a),o);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},j(O),f(O,u,"Generator"),f(O,c,(function(){return this})),f(O,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=$,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(N),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return s.type="throw",s.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],s=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var c=n.call(i,"catchLoc"),l=n.call(i,"finallyLoc");if(c&&l){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,b):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),b},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),N(r),b}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;N(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:$(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),b}},e}function c(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function l(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?c(Object(r),!0).forEach((function(e){u(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function u(t,e,r){return(e=f(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function f(t){var e=p(t,"string");return"symbol"==i(e)?e:e+""}function p(t,e){if("object"!=i(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function d(t,e){return b(t)||v(t,e)||h(t,e)||g()}function g(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function h(t,e){if(t){if("string"==typeof t)return m(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?m(t,e):void 0}}function m(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function v(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,o,i,s=[],c=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(s.push(n.value),s.length!==e);c=!0);}catch(t){l=!0,a=t}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(l)throw a}}return s}}function b(t){if(Array.isArray(t))return t}function y(t,e,r,n,a,o,i){try{var s=t[o](i),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,a)}function w(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var o=t.apply(e,r);function i(t){y(o,n,a,i,s,"next",t)}function s(t){y(o,n,a,i,s,"throw",t)}i(void 0)}))}}var x={name:"NoticeAdmin",data:function(){return{searchForm:{noticeTypeTitle:"",creator:""},creatorList:[],tableData:[],pageSize:10,totalCount:0,currentPage:1,isLoading:!1,selectDate:[],pickerOptions:{disabledDate:function(t){return t.getTime()>Date.now()},shortcuts:[{text:"最近一周",onClick:function(t){var e=new Date,r=new Date;r.setTime(r.getTime()-6048e5),t.$emit("pick",[r,e])}},{text:"最近一个月",onClick:function(t){var e=new Date,r=new Date;r.setTime(r.getTime()-2592e6),t.$emit("pick",[r,e])}},{text:"最近三个月",onClick:function(t){var e=new Date,r=new Date;r.setTime(r.getTime()-7776e6),t.$emit("pick",[r,e])}}]},searchFormSetting:{date_type:{type:"select",label:"",value:"update_time",maxWidth:"100px",placeholder:"请选择",dataList:[{label:"修改时间",value:"update_time"},{label:"发布时间",value:"post_time"}]},select_time:{type:"datetimerange",label:"",value:[]},sender:{type:"input",label:"创建人",value:"",placeholder:"请输入创建人"},title:{type:"input",label:"公告标题",value:"",placeholder:"请输入公告标题"},status:{type:"select",label:"公告状态",value:"",placeholder:"请选择公告状态",dataList:[{label:"全部",value:""},{label:"待发布",value:1},{label:"已发布",value:2}]}},buttonData:[{name:this.$t("button.add_notice"),click:"gotoAddNotice",type:"add",color:"origin",permission:["background_messages.messages.add"]}],dialogData:[],dialogVisible:!1}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getMsgList()},resetHandle:function(){this.currentPage=1,this.getMsgList()},searchHandle:Object(o["d"])((function(t){t&&"search"===t&&(this.currentPage=1,this.getMsgList())}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.currentPage=1,this.tableData=[],this.initLoad()},formatQueryParams:function(t){var e={};for(var r in t)""!==t[r].value&&null!==t[r].value&&("select_time"!==r?e[r]=t[r].value:t[r].value.length>0&&(e.start_time=t[r].value[0],e.end_time=t[r].value[1]));return e},getMsgList:function(){var t=this;return w(s().mark((function e(){var r,n,a,i;return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(o["Z"])(t.$apis.apiBackgroundMessagesMessagesListPost(l(l({},t.formatQueryParams(t.searchFormSetting)),{},{page:t.currentPage,page_size:t.pageSize})));case 3:if(r=e.sent,n=d(r,2),a=n[0],i=n[1],t.isLoading=!1,!a){e.next=11;break}return t.$message.error(a.message),e.abrupt("return");case 11:0===i.code?(t.totalCount=i.data.count,t.tableData=i.data.results):t.$message.error(i.msg);case 12:case"end":return e.stop()}}),e)})))()},revokeNotice:function(t){var e=this;return w(s().mark((function r(){var n,a,i,c;return s().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return e.isLoading=!0,r.next=3,Object(o["Z"])(e.$apis.apiBackgroundMessagesMessagesDeletePost({msg_no:t,options:0}));case 3:if(n=r.sent,a=d(n,2),i=a[0],c=a[1],e.isLoading=!1,!i){r.next=11;break}return e.$message.error(i.message),r.abrupt("return");case 11:0===c.code?e.getMsgList():e.$message.error(c.msg);case 12:case"end":return r.stop()}}),r)})))()},tableRowClassName:function(t){var e=t.row,r=(t.rowIndex,"");return e.row_color&&(r="table-header-row"),r},handleSizeChange:function(t){this.pageSize=t,this.getMsgList()},handleCurrentChange:function(t){this.currentPage=t,this.getMsgList()},gotoAddNotice:function(t,e){this.$router.push({name:"MerchantNoticeAdd",params:{type:t},query:{id:e}})},addNoticeHandle:function(t,e){var r={type:t};e&&(r.msg_no=e.msg_no),this.$router.push({name:"MerchantNoticeAdd",params:{type:t},query:r})},goToNoticeDetail:function(t){this.$router.push({name:"MerchantNoticeDetail",query:{msg_no:t.msg_no}})},deleteNoticeHandle:function(t){var e=this;return w(s().mark((function r(){var n;return s().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!e.isLoading){r.next=2;break}return r.abrupt("return");case 2:n={msg_no:t.msg_no},e.$confirm("确定要删除公告吗，删除后不可恢复。","提示",{confirmButtonText:"删 除",cancelButtonText:"取 消",closeOnClickModal:!1,customClass:"ps-confirm",beforeClose:function(){var t=w(s().mark((function t(r,a,i){var c,l,u,f;return s().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if("confirm"!==r){t.next=18;break}return a.confirmButtonLoading=!0,e.isLoading=!0,t.next=5,Object(o["Z"])(e.$apis.apiBackgroundMessagesMessagesDeletePost(n));case 5:if(c=t.sent,l=d(c,2),u=l[0],f=l[1],e.isLoading=!1,a.confirmButtonLoading=!1,!u){t.next=14;break}return e.$message.error(u.message),t.abrupt("return");case 14:i(),0===f.code?(e.$message.success("删除成功"),e.getMsgList()):e.$message.error(f.msg),t.next=19;break;case 18:a.confirmButtonLoading||i();case 19:case"end":return t.stop()}}),t)})));function r(e,r,n){return t.apply(this,arguments)}return r}()}).then((function(t){})).catch((function(t){}));case 4:case"end":return r.stop()}}),r)})))()},releaseNoticeHandle:function(t){var e=this;return w(s().mark((function r(){var n;return s().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!e.isLoading){r.next=2;break}return r.abrupt("return");case 2:n={msg_nos:[t.msg_no]},e.$confirm("确定要发布公告吗，发布后不可撤回。","提示",{confirmButtonText:"确 定",cancelButtonText:"取 消",closeOnClickModal:!1,customClass:"ps-confirm",beforeClose:function(){var t=w(s().mark((function t(r,a,i){var c,l,u,f;return s().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if("confirm"!==r){t.next=18;break}return a.confirmButtonLoading=!0,e.isLoading=!0,t.next=5,Object(o["Z"])(e.$apis.apiBackgroundMessagesMessagesBulkPushPost(n));case 5:if(c=t.sent,l=d(c,2),u=l[0],f=l[1],e.isLoading=!1,a.confirmButtonLoading=!1,!u){t.next=14;break}return e.$message.error(u.message),t.abrupt("return");case 14:i(),0===f.code?(e.$message.success("发布成功"),e.getMsgList()):e.$message.error(f.msg),t.next=19;break;case 18:a.confirmButtonLoading||i();case 19:case"end":return t.stop()}}),t)})));function r(e,r,n){return t.apply(this,arguments)}return r}()}).then((function(t){})).catch((function(t){}));case 4:case"end":return r.stop()}}),r)})))()},closeDialogHandle:function(){this.dialogData=[]},showCompanyDialog:function(t){this.dialogData=t.receivers_name,this.dialogVisible=!0}}},_=x,k=(r("4908d"),r("2877")),L=Object(k["a"])(_,n,a,!1,null,null,null);e["default"]=L.exports},3591:function(t,e,r){},"4908d":function(t,e,r){"use strict";r("3591")}}]);