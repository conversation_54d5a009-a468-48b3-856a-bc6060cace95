(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-components-mealFoodList-NutritionTable","view-merchant-meal-management-food-admin-constants"],{"0449":function(e,t,a){"use strict";a.r(t),a.d(t,"DEFAULT_NUTRITION",(function(){return l})),a.d(t,"ELEMENT_NUTRITION",(function(){return n})),a.d(t,"VITAMIN_NUTRITION",(function(){return i})),a.d(t,"NUTRITION_LIST",(function(){return u})),a.d(t,"LIBRARY_SEARCH_SETTING_MERCHANT",(function(){return m})),a.d(t,"LIBRARY_SEARCH_SETTING_CATEORY",(function(){return r})),a.d(t,"COMMODITY_SEARCH_SETTING_MERCHANT",(function(){return o})),a.d(t,"SPEC_LIST",(function(){return s}));var l=[{name:"千卡",key:"energy_kcal",unit:"Kcal",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{name:"胆固醇",key:"cholesterol",unit:"mg",type:"default"},{name:"膳食纤维",key:"dietary_fiber",unit:"g",type:"default"}],n=[{key:"Ca",name:"钙",unit:"mg",type:"element"},{key:"P",name:"磷",unit:"mg",type:"element"},{key:"K",name:"钾",unit:"mg",type:"element"},{key:"Na",name:"钠",unit:"mg",type:"element"},{name:"镁",key:"Mg",unit:"mg",type:"element"},{key:"Fe",name:"铁",unit:"mg",type:"element"},{key:"I",name:"碘",unit:"μg",type:"element"},{key:"Se",name:"硒",unit:"μg",type:"element"},{key:"Zn",name:"锌",unit:"mg",type:"element"},{key:"Cu",name:"铜",unit:"mg",type:"element"},{key:"F",name:"氟",unit:"mg",type:"element"},{key:"Cr",name:"铬",unit:"μg",type:"element"},{key:"Mo",name:"钼",unit:"μg",type:"element"},{key:"Mn",name:"锰",unit:"mg",type:"element"}],i=[{key:"VA",name:"维生素A",unit:"μg",type:"vitamin"},{key:"VD",name:"维生素D",unit:"μg",type:"vitamin"},{key:"VE",name:"维生素E",unit:"mg",type:"vitamin"},{key:"VK",name:"维生素K",unit:"μg",type:"vitamin"},{key:"VB1",name:"维生素B1",unit:"mg",type:"vitamin"},{key:"VB2",name:"维生素B2",unit:"mg",type:"vitamin"},{key:"VB6",name:"维生素B6",unit:"mg",type:"vitamin"},{key:"VB12",name:"维生素B12",unit:"μg",type:"vitamin"},{key:"VC",name:"维生素C",unit:"mg",type:"vitamin"},{key:"VB5",name:"泛酸",unit:"mg",type:"vitamin"},{key:"VM",name:"叶酸",unit:"μg",type:"vitamin"},{key:"VB3",name:"烟酸",unit:"mg",type:"vitamin"},{key:"Choline",name:" 胆碱",unit:"mg",type:"vitamin"},{key:"Nicotinamide",name:"烟酰胺",unit:"mg",type:"vitamin"},{key:"VH",name:"生物素",unit:"mg",type:"vitamin"}],u=[].concat(l,n,i),m={select_time:{type:"datetimerange",label:"创建时间",format:"yyyy-MM-dd HH:mm:ss",value:[]},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(e){return{id:e.level+"_"+e.id,label:e.name,children:e.sort_list}}},create_source:{type:"organizationSelect",label:"创建来源",multiple:!1,checkStrictly:!0,isLazy:!1,value:"",placeholder:"请选择创建来源",dataList:[{name:"全部",id:""},{company:0,create_time:"2022-01-25 09:49:26",has_children:!1,id:1,level:-1,level_name:"",level_tag:-1,name:"系统",parent:null,status:"enable",status_alias:"正常",tree_id:-1}]},is_enable_nutrition:{type:"select",label:"营养录入",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"是",value:"1"},{label:"否",value:"0"}]},ingredient_name:{type:"input",label:"食材名称",value:"",placeholder:"请输入食材名称"},label_filter:{type:"select",label:"",value:"",placeholder:"",dataList:[{label:"请选择",value:"select",disabled:!0},{label:"包含",value:"Include"},{label:"不包含",value:"Exclude"}]},label_list:{type:"treeselect",label:"",value:[],placeholder:"选择标签",multiple:!0,limit:1,level:1,valueConsistsOf:"LEAF_PRIORITY",normalizer:function(e){return{id:e.id,label:e.name,children:e.label_list}},dataList:[]},is_entering:{type:"select",label:"录入图片",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"已录入",value:"1"},{label:"未录入",value:"0"}]},is_entering22:{type:"select",label:"是否应季",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"是",value:"1"},{label:"否",value:"0"}]}},r={select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"修改时间",value:[]},category:{type:"select",label:"分类",value:[],placeholder:"请选择分类",multiple:!0,collapseTags:!0,dataList:[{label:"挂失",value:"LOSS"},{label:"退卡",value:"QUIT"},{label:"使用中",value:"ENABLE"},{label:"未发卡",value:"UNUSED"}]},person_no:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"}},o={select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"创建时间",value:[]},nutrition:{type:"select",label:"营养录入",value:[],placeholder:"请选择",multiple:!0,collapseTags:!0,dataList:[{label:"全部",value:""},{label:"是",value:"1"},{label:"否",value:"0"}]},perso_no:{type:"input",label:"菜品/商品名称",value:"",placeholder:"请输入菜品/商品名称"},hasx:{type:"select",label:"已有菜品/商品",value:"",placeholder:"请选择已有菜品/商品",dataList:[{label:"是",value:!0},{label:"否",value:!1}]},attributes:{type:"select",label:"属性",value:"",placeholder:"请选择属性",dataList:[{label:"是",value:!0},{label:"否",value:!1}]}},s=[{name:"份"},{name:"碗"},{name:"瓶"},{name:"碟"},{name:"盅"},{name:"盆"},{name:"件"},{name:"串"},{name:"例"},{name:"只"},{name:"边"}]},"0e41":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e._self._c;return t("div",[t("el-form",{ref:"formIngredients",attrs:{model:e.formData,rules:e.formRuls,size:"small"}},[e._l(e.currentNutritionList,(function(a){return[t("div",{key:a.key,staticClass:"nutrition-item"},[t("el-form-item",{attrs:{prop:a.key,label:a.name+"：",rules:e.formRuls.nutrition}},[t("el-input",{staticClass:"ps-input",staticStyle:{width:"120px"},attrs:{readonly:e.readonly,disabled:"",placeholder:"请输入"},on:{change:e.changeNutrition},model:{value:e.formData[a.key],callback:function(t){e.$set(e.formData,a.key,t)},expression:"formData[nutrition.key]"}}),t("span",{staticStyle:{"margin-left":"10px"}},[e._v(e._s(a.unit))])],1)],1)]})),t("div",{staticClass:"text-center pointer"},[t("span",{staticStyle:{color:"#027DB4"},on:{click:function(t){e.showAll=!e.showAll}}},[e._v(e._s(e.showAll?"收起":"查看更多营养信息"))])])],2)],1)},n=[],i=a("0449"),u={props:{tableDataNutrition:Object,readonly:{type:Boolean,default:!1}},data:function(){var e=function(e,t,a){if(t){var l=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;l.test(t)?a():a(new Error("营养数据有误，仅支持两位小数"))}else a()};return{formData:{},nutritionList:i["NUTRITION_LIST"],formRuls:{nutrition:[{validator:e,trigger:"change"}]},showAll:!1}},watch:{tableDataNutrition:{handler:function(e){this.initData()},deep:!0}},computed:{currentNutritionList:function(){var e=[];return e=this.showAll?this.nutritionList:this.nutritionList.slice(0,4),e}},mounted:function(){this.initData()},methods:{initData:function(e){var t=this;this.nutritionList.forEach((function(e){var a=t.tableDataNutrition[e.key]?t.tableDataNutrition[e.key]:0;t.$set(t.formData,e.key,a)}))},changeNutrition:function(e){this.$emit("update:tableDataNutrition",this.formData)}}},m=u,r=(a("f50f"),a("2877")),o=Object(r["a"])(m,l,n,!1,null,null,null);t["default"]=o.exports},"95d4":function(e,t,a){},f50f:function(e,t,a){"use strict";a("95d4")}}]);