(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-components-ColumnItem"],{"981e":function(e,l,t){"use strict";t.r(l);var o=function(){var e=this,l=e._self._c;return l("el-table-column",{attrs:{prop:e.col.prop,label:e.col.label,align:e.alignType}},[e._l(e.col.children,(function(t,o){return[t.children?l("column-item",{key:o,attrs:{col:t,showDialogNutritionDisabled:e.showDialogNutritionDisabled}}):l("el-table-column",{key:o,attrs:{label:t.label,prop:t.prop,align:e.alignType},scopedSlots:e._u([{key:"default",fn:function(o){return[l("el-input",{staticClass:"input",attrs:{disabled:e.showDialogNutritionDisabled},model:{value:o.row[t.prop],callback:function(l){e.$set(o.row,t.prop,l)},expression:"scope.row[item.prop]"}})]}}],null,!0)})]}))],2)},n=[],a={name:"ColumnItem",props:{col:{type:Object},alignType:{type:String,default:"center"},showDialogNutritionDisabled:{type:Boolean,default:!1}}},i=a,r=t("2877"),p=Object(r["a"])(i,o,n,!1,null,null,null);l["default"]=p.exports}}]);