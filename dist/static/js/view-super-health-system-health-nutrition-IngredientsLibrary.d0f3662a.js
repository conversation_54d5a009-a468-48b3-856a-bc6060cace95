(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-health-nutrition-IngredientsLibrary","view-super-health-system-components-selectLaber","view-super-health-system-health-nutrition-CommodityLibrary","view-super-health-system-health-nutrition-IngredientsCategory","view-super-health-system-health-nutrition-constants"],{"015b":function(e,t,n){"use strict";n.r(t),n.d(t,"DEFAULT_NUTRITION",(function(){return a})),n.d(t,"ELEMENT_NUTRITION",(function(){return i})),n.d(t,"VITAMIN_NUTRITION",(function(){return o})),n.d(t,"NUTRITION_LIST",(function(){return l})),n.d(t,"LIBRARY_SEARCH_SETTING_SUPER",(function(){return s})),n.d(t,"LIBRARY_SEARCH_SETTING_MERCHANT",(function(){return c})),n.d(t,"LIBRARY_SEARCH_SETTING_CATEORY",(function(){return u})),n.d(t,"COMMODITY_SEARCH_SETTING_SUPER",(function(){return p})),n.d(t,"COMMODITY_SEARCH_SETTING_MERCHANT",(function(){return d}));var r=n("ed08"),a=[{name:"千卡",key:"energy_kcal",unit:"Kcal",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{name:"胆固醇",key:"cholesterol",unit:"mg",type:"default"},{name:"膳食纤维",key:"dietary_fiber",unit:"g",type:"default"}],i=[{key:"Ca",name:"钙",unit:"mg",type:"element"},{key:"P",name:"磷",unit:"mg",type:"element"},{key:"K",name:"钾",unit:"mg",type:"element"},{key:"Na",name:"钠",unit:"mg",type:"element"},{name:"镁",key:"Mg",unit:"mg",type:"element"},{key:"Fe",name:"铁",unit:"mg",type:"element"},{key:"I",name:"碘",unit:"μg",type:"element"},{key:"Se",name:"硒",unit:"μg",type:"element"},{key:"Zn",name:"锌",unit:"mg",type:"element"},{key:"Cu",name:"铜",unit:"mg",type:"element"},{key:"F",name:"氟",unit:"mg",type:"element"},{key:"Cr",name:"铬",unit:"μg",type:"element"},{key:"Mo",name:"钼",unit:"μg",type:"element"},{key:"Mn",name:"锰",unit:"mg",type:"element"}],o=[{key:"VA",name:"维生素A",unit:"μg",type:"vitamin"},{key:"VD",name:"维生素D",unit:"μg",type:"vitamin"},{key:"VE",name:"维生素E",unit:"mg",type:"vitamin"},{key:"VK",name:"维生素K",unit:"μg",type:"vitamin"},{key:"VB1",name:"维生素B1",unit:"mg",type:"vitamin"},{key:"VB2",name:"维生素B2",unit:"mg",type:"vitamin"},{key:"VB6",name:"维生素B6",unit:"mg",type:"vitamin"},{key:"VB12",name:"维生素B12",unit:"μg",type:"vitamin"},{key:"VC",name:"维生素C",unit:"mg",type:"vitamin"},{key:"VB5",name:"泛酸",unit:"mg",type:"vitamin"},{key:"VM",name:"叶酸",unit:"μg",type:"vitamin"},{key:"VB3",name:"烟酸",unit:"mg",type:"vitamin"},{key:"Choline",name:" 胆碱",unit:"mg",type:"vitamin"},{key:"Nicotinamide",name:"烟酰胺",unit:"mg",type:"vitamin"},{key:"VH",name:"生物素",unit:"mg",type:"vitamin"}],l=[].concat(a,i,o),s={date_type:{type:"select",label:"",value:1,maxWidth:"130px",placeholder:"请选择",dataList:[{label:"创建时间",value:1},{label:"修改时间",value:2}]},select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"",clearable:!0,value:[]},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(e){return{id:e.level+"_"+e.id,label:e.name,children:e.sort_list}}},is_enable_nutrition:{type:"select",label:"营养信息",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"有",value:"1"},{label:"无",value:"0"}]},ingredient_name:{type:"input",label:"食材名称",value:"",placeholder:"请输入食材名称"},operator:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"},label_filter:{type:"select",label:"",value:"",placeholder:"",dataList:[{label:"请选择",value:"select",disabled:!0},{label:"包含",value:"Include"},{label:"不包含",value:"Exclude"}]},label_list:{type:"treeselect",label:"",value:[],placeholder:"选择标签",multiple:!0,limit:1,level:1,valueConsistsOf:"LEAF_PRIORITY",normalizer:function(e){return{id:e.id,label:e.name,children:e.label_list}},dataList:[]},is_entering:{type:"select",label:"录入图片",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"已录入",value:"1"},{label:"未录入",value:"0"}]}},c={select_time:{type:"datetimerange",label:"创建时间",format:"yyyy-MM-dd",value:Object(r["y"])(7)},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(e){return{id:e.level+"_"+e.id,label:e.name,children:e.sort_list}}},is_repeat:{type:"select",label:"已有食材",value:"",placeholder:"请选择是否已有食材",dataList:[{label:"是",value:1},{label:"否",value:0}]},is_enable_nutrition:{type:"select",label:"营养信息",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"有",value:"1"},{label:"无",value:"0"}]},ingredient_name:{type:"input",label:"食材名称",value:"",placeholder:"请输入食材名称"},is_entering:{type:"select",label:"录入图片",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"已录入",value:"1"},{label:"未录入",value:"0"}]}},u={select_time:{type:"datetimerange",label:"修改时间",format:"yyyy-MM-dd HH:mm:ss",value:[]},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(e){return{id:e.level+"_"+e.id,label:e.name,children:e.sort_list}}},user_name:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"}},p={date_type:{type:"select",label:"",value:1,maxWidth:"130px",placeholder:"请选择",dataList:[{label:"创建时间",value:1},{label:"修改时间",value:2}]},select_time:{type:"datetimerange",label:"",format:"yyyy-MM-dd HH:mm:ss",value:[]},food_name:{type:"input",label:"菜品/商品名称",value:"",placeholder:"请输入菜品/商品名称"},user_name:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"},attributes:{type:"select",label:"属性",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"商品",value:"goods"},{label:"菜品",value:"foods"}]},label_filter:{type:"select",label:"",value:"",placeholder:"",dataList:[{label:"请选择",value:"select",disabled:!0},{label:"包含",value:"Include"},{label:"不包含",value:"Exclude"}]},label_list:{type:"treeselect",label:"",value:[],placeholder:"选择标签",multiple:!0,limit:1,level:1,valueConsistsOf:"LEAF_PRIORITY",normalizer:function(e){return{id:e.id,label:e.name,children:e.label_list}},dataList:[]}},d={select_time:{type:"datetimerange",label:"创建时间",format:"yyyy-MM-dd",value:Object(r["y"])(7)},food_name:{type:"input",label:"菜品/商品名称",value:"",placeholder:"请输入菜品/商品名称"},is_repeat:{type:"select",label:"已有菜品/商品",value:"",placeholder:"请选择",dataList:[{label:"是",value:1},{label:"否",value:0}]},attributes:{type:"select",label:"属性",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"商品",value:"goods"},{label:"菜品",value:"foods"}]}}},"1a24":function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e._self._c;return t("dialog-message",{attrs:{show:e.visible,title:e.title,loading:e.isLoading,customClass:"ps-dialog",width:e.width},on:{"update:show":function(t){e.visible=t},"update:loading":function(t){e.isLoading=t},close:e.handleClose}},[t("div",{staticClass:"healthTagDialog"},[t("el-input",{staticClass:"ps-input",attrs:{placeholder:"请输入要查询的标签组/标签名称名称"},on:{input:e.searchHandle},model:{value:e.name,callback:function(t){e.name=t},expression:"name"}}),t("el-card",{staticClass:"m-t-20",attrs:{shadow:"never"}},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v(" 已选 "),t("span",[e._v(e._s(e.selectLabelIdList.length))]),e._v(" 个标签 ")])]),e._l(e.tableData,(function(n,r){return t("div",{key:r},[t("el-collapse",{model:{value:e.activeLaberList,callback:function(t){e.activeLaberList=t},expression:"activeLaberList"}},[t("el-collapse-item",{attrs:{name:n.id}},[t("template",{slot:"title"},[t("span",[e._v(" "+e._s(n.name)+" "),t("span",[e._v("（"+e._s(n.label_list.length)+"）")])]),t("span",{staticClass:"tips-r"},[t("span",{staticClass:"open"},[e._v("展开")]),t("span",{staticClass:"close"},[e._v("收起")])])]),t("div",{staticClass:"ps-flex-align-c flex-align-c flex-wrap"},[n.inputVisible?t("el-input",{ref:"saveTagInput"+n.id,refInFor:!0,staticClass:"ps-input w-100 p-r-10 p-t-5",attrs:{size:"mini",autofocus:"",maxlength:"15"},on:{blur:function(t){return e.handleInputConfirm(n)}},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleInputConfirm(n)}},model:{value:n.inputValue,callback:function(t){e.$set(n,"inputValue",t)},expression:"item.inputValue"}}):t("button-icon",{staticClass:"p-t-5",attrs:{color:"origin",type:"add"},on:{click:function(t){return e.showInput(n)}}},[e._v(" 添加标签 ")]),t("div",{staticStyle:{flex:"1"}},[t("el-checkbox-group",{attrs:{size:"mini"},model:{value:e.selectLabelIdList,callback:function(t){e.selectLabelIdList=t},expression:"selectLabelIdList"}},e._l(n.label_list,(function(r,a){return t("el-checkbox-button",{key:a,attrs:{label:r.id,disabled:r.disabled},on:{change:function(t){return e.checkboxChangge(r,n)}}},[e._v(" "+e._s(r.name)+" ")])})),1)],1)],1)],2)],1)],1)}))],2)],1),t("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[t("el-pagination",{staticClass:"ps-text",attrs:{"page-size":e.pageSize,"current-page":e.currentPage,layout:"total, prev, pager, next",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"current-change":e.handleCurrentChange,"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t}}})],1),t("template",{slot:"tool"},[t("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:e.clickCancleHandle}},[e._v(" 取消 ")]),t("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickConfirmHandle}},[e._v(" 保存 ")])],1)])],2)},a=[],i=n("ed08");function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function l(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */l=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,a=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},s=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function p(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{p({},"")}catch(e){p=function(e,t,n){return e[t]=n}}function d(e,t,n,r){var i=t&&t.prototype instanceof v?t:v,o=Object.create(i.prototype),l=new A(r||[]);return a(o,"_invoke",{value:T(e,n,l)}),o}function f(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var h="suspendedStart",m="suspendedYield",g="executing",b="completed",y={};function v(){}function L(){}function _(){}var w={};p(w,s,(function(){return this}));var k=Object.getPrototypeOf,x=k&&k(k(P([])));x&&x!==n&&r.call(x,s)&&(w=x);var S=_.prototype=v.prototype=Object.create(w);function C(e){["next","throw","return"].forEach((function(t){p(e,t,(function(e){return this._invoke(t,e)}))}))}function I(e,t){function n(a,i,l,s){var c=f(e[a],e,i);if("throw"!==c.type){var u=c.arg,p=u.value;return p&&"object"==o(p)&&r.call(p,"__await")?t.resolve(p.__await).then((function(e){n("next",e,l,s)}),(function(e){n("throw",e,l,s)})):t.resolve(p).then((function(e){u.value=e,l(u)}),(function(e){return n("throw",e,l,s)}))}s(c.arg)}var i;a(this,"_invoke",{value:function(e,r){function a(){return new t((function(t,a){n(e,r,t,a)}))}return i=i?i.then(a,a):a()}})}function T(t,n,r){var a=h;return function(i,o){if(a===g)throw Error("Generator is already running");if(a===b){if("throw"===i)throw o;return{value:e,done:!0}}for(r.method=i,r.arg=o;;){var l=r.delegate;if(l){var s=E(l,r);if(s){if(s===y)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===h)throw a=b,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=g;var c=f(t,n,r);if("normal"===c.type){if(a=r.done?b:m,c.arg===y)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(a=b,r.method="throw",r.arg=c.arg)}}}function E(t,n){var r=n.method,a=t.iterator[r];if(a===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,E(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),y;var i=f(a,t.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,y;var o=i.arg;return o?o.done?(n[t.resultName]=o.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,y):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,y)}function O(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function $(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function A(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(O,this),this.reset(!0)}function P(t){if(t||""===t){var n=t[s];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,i=function n(){for(;++a<t.length;)if(r.call(t,a))return n.value=t[a],n.done=!1,n;return n.value=e,n.done=!0,n};return i.next=i}}throw new TypeError(o(t)+" is not iterable")}return L.prototype=_,a(S,"constructor",{value:_,configurable:!0}),a(_,"constructor",{value:L,configurable:!0}),L.displayName=p(_,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===L||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,p(e,u,"GeneratorFunction")),e.prototype=Object.create(S),e},t.awrap=function(e){return{__await:e}},C(I.prototype),p(I.prototype,c,(function(){return this})),t.AsyncIterator=I,t.async=function(e,n,r,a,i){void 0===i&&(i=Promise);var o=new I(d(e,n,r,a),i);return t.isGeneratorFunction(n)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},C(S),p(S,u,"Generator"),p(S,s,(function(){return this})),p(S,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=P,A.prototype={constructor:A,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach($),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function a(r,a){return l.type="throw",l.arg=t,n.next=r,a&&(n.method="next",n.arg=e),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],l=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var s=r.call(o,"catchLoc"),c=r.call(o,"finallyLoc");if(s&&c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),$(n),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var a=r.arg;$(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:P(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),y}},t}function s(e,t){return f(e)||d(e,t)||u(e,t)||c()}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(e,t){if(e){if("string"==typeof e)return p(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?p(e,t):void 0}}function p(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function d(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i,o,l=[],s=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);s=!0);}catch(e){c=!0,a=e}finally{try{if(!s&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw a}}return l}}function f(e){if(Array.isArray(e))return e}function h(e,t,n,r,a,i,o){try{var l=e[i](o),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,a)}function m(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function o(e){h(i,r,a,o,l,"next",e)}function l(e){h(i,r,a,o,l,"throw",e)}o(void 0)}))}}var g={name:"selectLaber",props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:"选择标签"},width:{type:String,default:"600px"},isshow:Boolean,ruleSingleInfo:{type:Object,default:function(){return{}}}},data:function(){return{isLoading:!1,name:"",activeLaberList:[],selectLabelIdList:[],selectLabelListData:[],tableData:[],labelType:"",pageSize:3,totalCount:0,currentPage:1}},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},created:function(){this.ruleSingleInfo.selectLabelIdList&&this.ruleSingleInfo.selectLabelIdList.length&&(this.selectLabelIdList=this.ruleSingleInfo.selectLabelIdList),this.ruleSingleInfo.selectLabelListData&&this.ruleSingleInfo.selectLabelListData.length&&(this.selectLabelListData=this.ruleSingleInfo.selectLabelListData),this.labelType=this.ruleSingleInfo.labelType,this.getLabelGroupList()},mounted:function(){},methods:{searchHandle:Object(i["d"])((function(){this.currentPage=1,this.getLabelGroupList()}),300),getLabelGroupList:function(){var e=this;return m(l().mark((function t(){var n,r,a,o,c;return l().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,n={type:e.labelType,page:e.currentPage,page_size:e.pageSize},e.name&&(n.name=e.name),t.next=5,Object(i["Z"])(e.$apis.apiBackgroundHealthyAdminLabelGroupListPost(n));case 5:if(r=t.sent,a=s(r,2),o=a[0],c=a[1],e.isLoading=!1,!o){t.next=13;break}return e.$message.error(o.message),t.abrupt("return");case 13:0===c.code?(e.totalCount=c.data.count,e.totalPageSize=e.$computedTotalPageSize(e.totalCount,e.pageSize),e.tableData=c.data.results.map((function(t){return t.inputVisible=!1,t.inputValue="",t.label_list.forEach((function(n){n.label_group_name=t.name,e.ruleSingleInfo.selectLabelAllIds&&e.ruleSingleInfo.selectLabelAllIds.length&&e.ruleSingleInfo.selectLabelAllIds.includes(n.id)&&!e.selectLabelIdList.includes(n.id)?n.disabled=!0:n.disabled=!1})),e.activeLaberList.push(t.id),t}))):e.$message.error(c.msg);case 14:case"end":return t.stop()}}),t)})))()},handleChange:function(){},checkboxChangge:function(e,t){var n=this,r=this.selectLabelIdList.indexOf(e.id);-1!==r?this.selectLabelListData.push(e):this.selectLabelListData.map((function(t,r){e.id===t.id&&n.selectLabelListData.splice(r,1)}))},handleInputConfirm:function(e){e.inputValue?this.getLabelGroupAddLabel(e):e.inputVisible=!1},showInput:function(e){var t=this;e.inputVisible=!0,this.$nextTick((function(n){t.$refs["saveTagInput"+e.id][0].$refs.input.focus()}))},getLabelGroupAddLabel:function(e){var t=this;return m(l().mark((function n(){var r,a,o,c;return l().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return t.isLoading=!0,n.next=3,Object(i["Z"])(t.$apis.apiBackgroundHealthyAdminLabelGroupAddLabelPost({name:e.inputValue,label_group_id:e.id}));case 3:if(r=n.sent,a=s(r,2),o=a[0],c=a[1],t.isLoading=!1,!o){n.next=11;break}return t.$message.error(o.message),n.abrupt("return");case 11:0===c.code?(e.inputValue="",e.inputVisible=!1,t.getLabelGroupList()):t.$message.error(c.msg);case 12:case"end":return n.stop()}}),n)})))()},clickConfirmHandle:function(){var e={selectLabelIdList:this.selectLabelIdList,selectLabelListData:this.selectLabelListData};this.$emit("selectLaberData",e),this.visible=!1},clickCancleHandle:function(){this.visible=!1},handleClose:function(e){this.visible=!1},handleCurrentChange:function(e){this.currentPage=e,this.getLabelGroupList()}}},b=g,y=(n("27c8"),n("2877")),v=Object(y["a"])(b,r,a,!1,null,null,null);t["default"]=v.exports},"27c8":function(e,t,n){"use strict";n("c4a9")},"2fbdc":function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"super-ingredients-library container-wrapper"},[t("refresh-tool",{on:{refreshPage:e.refreshHandle}}),t("search-form",{ref:"searchRef",attrs:{"form-setting":e.searchFormSetting,"label-width":"105px"},on:{search:e.searchHandle}},[t("template",{slot:"perv"},[t("div",{staticClass:"tab"},[t("div",{class:["tab-item","system"===e.tabType?"active":""],on:{click:function(t){return e.tabClick("system")}}},[e._v(" 系统食材 ")]),t("div",{class:["tab-item","merchant"===e.tabType?"active":""],on:{click:function(t){return e.tabClick("merchant")}}},[e._v(" 商户食材 ")])])])],2),t("div",{staticClass:"table-wrapper"},[t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[e._v("数据列表")]),t("div",{staticClass:"align-r"},["system"===e.tabType?t("button-icon",{attrs:{color:"plain",type:"del"},on:{click:function(t){return e.batchLabelClick("batchLabelDel")}}},[e._v(" 批量移除标签 ")]):e._e(),"system"===e.tabType?t("button-icon",{attrs:{color:"plain",type:"mul"},on:{click:function(t){return e.batchLabelClick("batchLabelAdd")}}},[e._v(" 批量打标签 ")]):e._e(),"system"===e.tabType?t("button-icon",{attrs:{color:"origin",type:"add"},on:{click:e.addIngredients}},[e._v(" 添加食材 ")]):e._e(),"system"===e.tabType?t("button-icon",{attrs:{color:"plain",type:"Import"},on:{click:function(t){return e.importHandler("modify_import")}}},[e._v(" 导入编辑 ")]):e._e(),"system"===e.tabType?t("button-icon",{attrs:{color:"plain",type:"Import"},on:{click:function(t){return e.importHandler("import")}}},[e._v(" 批量导入食材 ")]):e._e(),"system"===e.tabType?t("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.ingredient.ingredient_image_bat_add"],expression:"['background.admin.ingredient.ingredient_image_bat_add']"}],attrs:{color:"plain",type:"Import"},on:{click:function(t){return e.importHandler("importImage")}}},[e._v(" 导入食材图片 ")]):e._e(),"system"===e.tabType?t("button-icon",{attrs:{color:"plain",type:"menu"},on:{click:e.gotoCategory}},[e._v(" 分类管理 ")]):e._e(),"merchant"===e.tabType?t("span",{staticStyle:{"font-size":"12px"}},[e._v(" 是否允许商户上传信息 "),t("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},on:{change:e.updateSettingHandler},model:{value:e.updateSetting,callback:function(t){e.updateSetting=t},expression:"updateSetting"}})],1):e._e(),t("button-icon",{attrs:{color:"origin",type:"export"},on:{click:e.gotoExport}},[e._v("导出EXCEL")])],1)]),t("div",{key:e.tabType,staticClass:"table-content"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","row-key":"id","header-row-class-name":"ps-table-header-row"},on:{"selection-change":e.handleSelectionChange}},["system"===e.tabType?t("el-table-column",{attrs:{type:"selection",width:"50",align:"center","class-name":"ps-checkbox","reserve-selection":!0}}):e._e(),t("el-table-column",{attrs:{type:"index",width:"80",label:"图片",align:"center"},scopedSlots:e._u([{key:"default",fn:function(e){return[t("el-image",{staticClass:"column-image",attrs:{lazy:!0,src:e.row.image?e.row.image:n("cb7d"),"preview-src-list":[e.row.image?e.row.image:n("cb7d")]}},[t("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[t("i",{staticClass:"el-icon-picture-outline"})])])]}}])}),t("el-table-column",{attrs:{prop:"id",label:"食材ID",align:"center"}}),t("el-table-column",{attrs:{prop:"name",label:"食材名称",align:"center"}}),t("el-table-column",{attrs:{prop:"all_alias_name",label:"食材别名",align:"center",width:"120","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{prop:"category_name",label:"一级分类",align:"center"}}),t("el-table-column",{attrs:{prop:"sort_name",label:"二级分类",align:"center"}}),"merchant"===e.tabType?t("el-table-column",{attrs:{prop:"create_source_name",label:"来源",align:"center"}}):e._e(),t("el-table-column",{attrs:{prop:"nutrition",label:"营养信息",align:"center"},scopedSlots:e._u([{key:"default",fn:function(n){return[n.row.is_enable_nutrition?t("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.showDialogHandler("nutrition",n.row)}}},[e._v(" 查看 ")]):t("span",[e._v("--")])]}}])}),"system"===e.tabType?t("el-table-column",{attrs:{prop:"xx",label:"标签",align:"center",width:"220px"},scopedSlots:e._u([{key:"default",fn:function(n){return[t("div",{staticClass:"collapse-wrapper"},[t("div",{staticClass:"collapse-list hide"},[e._l(n.row.label,(function(r,a){return t("el-tag",{key:a,staticClass:"m-r-5 m-t-5 collapse-data",attrs:{size:"medium",effect:"plain",type:"light",closable:""},on:{close:function(t){return e.closeTag(r,n.row)}}},[e._v(" "+e._s(r.name)+" ")])})),n.row.label&&n.row.label.length>3?[t("span",{staticClass:"collapse-more",on:{click:e.showMoreHandler}},[e._v(" 查看更多 "),t("i",{staticClass:"el-icon-arrow-down"})]),t("span",{staticClass:"collapse-hide",on:{click:e.hideMoreHandler}},[e._v(" 收起 "),t("i",{staticClass:"el-icon-arrow-up"})])]:e._e()],2)])]}}],null,!1,2657971599)}):e._e(),"merchant"===e.tabType?t("el-table-column",{attrs:{prop:"is_repeat",label:"已有食材",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.is_repeat?"是":"否")+" ")]}}],null,!1,3065278787)}):e._e(),t("el-table-column",{attrs:{prop:"operator_name",label:"操作人",align:"center"}}),t("el-table-column",{attrs:{prop:"create_time",label:"创建时间",align:"center"}}),"system"===e.tabType?t("el-table-column",{attrs:{prop:"update_time",label:"修改时间",align:"center"}}):e._e(),t("el-table-column",{attrs:{label:"操作",width:"180",align:"center"},scopedSlots:e._u([{key:"default",fn:function(n){return["merchant"===e.tabType?[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.addToSystem(n.row.id)}}},[e._v(" 创建到食材库 ")])]:[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.modifyIngredients(n.row)}}},[e._v(" 编辑 ")]),t("span",{staticStyle:{margin:"0 10px",color:"#e2e8f0"}},[e._v("|")]),t("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.deleteHandler("single",n.row.id)}}},[e._v(" 删除 ")])]]}}])})],1)],1),t("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[t("el-pagination",{staticClass:"ps-text",attrs:{"current-page":e.currentPage,"page-size":e.pageSize,layout:"total, prev, pager, next, jumper",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)]),t("el-dialog",{attrs:{title:e.dialogTitle,visible:e.dialogVisible,width:"700px",top:"20vh","custom-class":"ps-dialog","close-on-click-modal":!1},on:{"update:visible":function(t){e.dialogVisible=t},closed:e.dialogHandleClose}},[t("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],attrs:{model:e.formData,size:"small"}},[t("div",{staticClass:"table-content"},[e._l(e.nutritionList,(function(n){return[t("div",{key:n.key,staticClass:"nutrition-item"},[t("div",{staticClass:"nutrition-label"},[e._v(e._s(n.name+"："))]),t("el-form-item",{attrs:{prop:n.key}},[t("el-input",{staticClass:"ps-input",staticStyle:{width:"120px"},attrs:{readonly:""},model:{value:e.formData[n.key],callback:function(t){e.$set(e.formData,n.key,t)},expression:"formData[nutrition.key]"}}),t("span",{staticStyle:{"margin-left":"10px"}},[e._v(e._s(n.unit))])],1)],1)]}))],2)])],1),e.selectLaberDialogVisible?t("select-laber",{attrs:{isshow:e.selectLaberDialogVisible,title:e.titleSelectLaber,width:"600px",ruleSingleInfo:e.ruleSingleInfo},on:{"update:isshow":function(t){e.selectLaberDialogVisible=t},selectLaberData:e.selectLaberData}}):e._e()],1)},a=[],i=n("f63a"),o=n("ed08"),l=n("015b"),s=n("1a24");function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function u(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */u=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,a=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function p(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{p({},"")}catch(e){p=function(e,t,n){return e[t]=n}}function d(e,t,n,r){var i=t&&t.prototype instanceof v?t:v,o=Object.create(i.prototype),l=new A(r||[]);return a(o,"_invoke",{value:T(e,n,l)}),o}function f(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var h="suspendedStart",m="suspendedYield",g="executing",b="completed",y={};function v(){}function L(){}function _(){}var w={};p(w,o,(function(){return this}));var k=Object.getPrototypeOf,x=k&&k(k(P([])));x&&x!==n&&r.call(x,o)&&(w=x);var S=_.prototype=v.prototype=Object.create(w);function C(e){["next","throw","return"].forEach((function(t){p(e,t,(function(e){return this._invoke(t,e)}))}))}function I(e,t){function n(a,i,o,l){var s=f(e[a],e,i);if("throw"!==s.type){var u=s.arg,p=u.value;return p&&"object"==c(p)&&r.call(p,"__await")?t.resolve(p.__await).then((function(e){n("next",e,o,l)}),(function(e){n("throw",e,o,l)})):t.resolve(p).then((function(e){u.value=e,o(u)}),(function(e){return n("throw",e,o,l)}))}l(s.arg)}var i;a(this,"_invoke",{value:function(e,r){function a(){return new t((function(t,a){n(e,r,t,a)}))}return i=i?i.then(a,a):a()}})}function T(t,n,r){var a=h;return function(i,o){if(a===g)throw Error("Generator is already running");if(a===b){if("throw"===i)throw o;return{value:e,done:!0}}for(r.method=i,r.arg=o;;){var l=r.delegate;if(l){var s=E(l,r);if(s){if(s===y)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===h)throw a=b,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=g;var c=f(t,n,r);if("normal"===c.type){if(a=r.done?b:m,c.arg===y)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(a=b,r.method="throw",r.arg=c.arg)}}}function E(t,n){var r=n.method,a=t.iterator[r];if(a===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,E(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),y;var i=f(a,t.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,y;var o=i.arg;return o?o.done?(n[t.resultName]=o.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,y):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,y)}function O(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function $(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function A(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(O,this),this.reset(!0)}function P(t){if(t||""===t){var n=t[o];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,i=function n(){for(;++a<t.length;)if(r.call(t,a))return n.value=t[a],n.done=!1,n;return n.value=e,n.done=!0,n};return i.next=i}}throw new TypeError(c(t)+" is not iterable")}return L.prototype=_,a(S,"constructor",{value:_,configurable:!0}),a(_,"constructor",{value:L,configurable:!0}),L.displayName=p(_,s,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===L||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,p(e,s,"GeneratorFunction")),e.prototype=Object.create(S),e},t.awrap=function(e){return{__await:e}},C(I.prototype),p(I.prototype,l,(function(){return this})),t.AsyncIterator=I,t.async=function(e,n,r,a,i){void 0===i&&(i=Promise);var o=new I(d(e,n,r,a),i);return t.isGeneratorFunction(n)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},C(S),p(S,s,"Generator"),p(S,o,(function(){return this})),p(S,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=P,A.prototype={constructor:A,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach($),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function a(r,a){return l.type="throw",l.arg=t,n.next=r,a&&(n.method="next",n.arg=e),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],l=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var s=r.call(o,"catchLoc"),c=r.call(o,"finallyLoc");if(s&&c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),$(n),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var a=r.arg;$(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:P(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),y}},t}function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){f(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function f(e,t,n){return(t=h(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function h(e){var t=m(e,"string");return"symbol"==c(t)?t:t+""}function m(e,t){if("object"!=c(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=c(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function g(e,t){return _(e)||L(e,t)||y(e,t)||b()}function b(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function y(e,t){if(e){if("string"==typeof e)return v(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?v(e,t):void 0}}function v(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function L(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i,o,l=[],s=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);s=!0);}catch(e){c=!0,a=e}finally{try{if(!s&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw a}}return l}}function _(e){if(Array.isArray(e))return e}function w(e,t,n,r,a,i,o){try{var l=e[i](o),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,a)}function k(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function o(e){w(i,r,a,o,l,"next",e)}function l(e){w(i,r,a,o,l,"throw",e)}o(void 0)}))}}var x={name:"SuperIngredientsLibrary",mixins:[i["a"]],data:function(){return{tabType:"system",isLoading:!1,pageSize:10,totalCount:0,currentPage:1,updateSetting:!1,tableData:[],searchFormSetting:l["LIBRARY_SEARCH_SETTING_SUPER"],dialogData:{},dialogTitle:"营养信息",dialogType:"",dialogVisible:!1,dialogLoading:!1,nutritionList:l["NUTRITION_LIST"],formData:{},checkList:[],selectLaberDialogVisible:!1,titleSelectLaber:"",batchLabelType:"",ruleSingleInfo:{}}},components:{selectLaber:s["default"]},created:function(){this.getAllLabelGroupList(),this.getCategoryCategoryNameList()},mounted:function(){},methods:{initLoad:function(){this.getListHandler(),this.getAllLabelGroupList()},searchHandle:Object(o["d"])((function(e){e&&"search"===e&&(this.currentPage=1,this.getListHandler())}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.currentPage=1,this.initLoad()},tabClick:function(e){this.tabType=e,this.currentPage=1,this.searchFormSetting="system"===e?l["LIBRARY_SEARCH_SETTING_SUPER"]:l["LIBRARY_SEARCH_SETTING_MERCHANT"],this.tableData=[]},formatQueryParams:function(e){var t={},n=function(n){(e[n].value||e[n].value&&e[n].value.length)&&("select_time"!==n?"sort_id"===n?e[n].dataList.map((function(r){"1"===e[n].value.split("_")[0]?t.category_id=Number(e[n].value.split("_")[1]):"2"===e[n].value.split("_")[0]&&(t.sort_id=Number(e[n].value.split("_")[1]))})):t[n]=e[n].value:e[n].value.length>0&&(t.start_time=e[n].value[0]+" 00:00:00",t.end_time=e[n].value[1]+" 23:59:59"))};for(var r in e)n(r);return t},getIngredientslist:function(){var e=this;return k(u().mark((function t(){var n,r,a,i;return u().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(o["Z"])(e.$apis.apiBackgroundAdminIngredientListPost(d(d({},e.formatQueryParams(e.searchFormSetting)),{},{page:e.currentPage,page_size:e.pageSize})));case 3:if(n=t.sent,r=g(n,2),a=r[0],i=r[1],e.isLoading=!1,!a){t.next=11;break}return e.$message.error(a.message),t.abrupt("return");case 11:0===i.code?(e.totalCount=i.data.count,e.tableData=i.data.results.map((function(e){return null!==e.alias_name?e.all_alias_name=e.alias_name.join(","):e.alias_name=[],e}))):e.$message.error(i.msg);case 12:case"end":return t.stop()}}),t)})))()},getIngredientsManchantlist:function(){var e=this;return k(u().mark((function t(){var n,r,a,i;return u().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(o["Z"])(e.$apis.apiBackgroundAdminIngredientMerchantIngredientPost(d(d({},e.formatQueryParams(e.searchFormSetting)),{},{page:e.currentPage,page_size:e.pageSize})));case 3:if(n=t.sent,r=g(n,2),a=r[0],i=r[1],e.isLoading=!1,!a){t.next=11;break}return e.$message.error(a.message),t.abrupt("return");case 11:0===i.code?(e.totalCount=i.data.count,e.tableData=i.data.results.map((function(e){return null!==e.alias_name?e.all_alias_name=e.alias_name.join(","):e.alias_name=[],e})),e.updateSetting=i.data.ingredient_upload):e.$message.error(i.msg);case 12:case"end":return t.stop()}}),t)})))()},getCategoryCategoryNameList:function(){var e=this;return k(u().mark((function t(){var n,r,a,i;return u().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(o["Z"])(e.$apis.apiBackgroundAdminIngredientCategoryCategoryNameListPost());case 3:if(n=t.sent,r=g(n,2),a=r[0],i=r[1],e.isLoading=!1,!a){t.next=11;break}return e.$message.error(a.message),t.abrupt("return");case 11:0===i.code?(l["LIBRARY_SEARCH_SETTING_MERCHANT"].sort_id.dataList=e.deleteEmptyGroup(i.data),l["LIBRARY_SEARCH_SETTING_SUPER"].sort_id.dataList=e.deleteEmptyGroup(i.data)):e.$message.error(i.msg);case 12:case"end":return t.stop()}}),t)})))()},deleteEmptyGroup:function(e){var t=this;function n(e){e.map((function(e){e.sort_list&&e.sort_list.length>0?n(e.sort_list):t.$delete(e,"sort_list")}))}return n(e),e},showDialogHandler:function(e,t){"nutrition"===e&&this.setDialogNutriton(t),this.dialogVisible=!0},setDialogNutriton:function(e){var t=this;this.formData={},e.nutrition_info||(e.nutrition_info={});var n=e.nutrition_info.element?JSON.parse(Object(o["R"])(e.nutrition_info.element)):{},r=e.nutrition_info.vitamin?JSON.parse(Object(o["R"])(e.nutrition_info.vitamin)):{};l["NUTRITION_LIST"].forEach((function(a){"default"===a.type&&t.$set(t.formData,a.key,e.nutrition_info[a.key]?e.nutrition_info[a.key]:0),"element"===a.type&&t.$set(t.formData,a.key,n[a.key]?n[a.key]:0),"vitamin"===a.type&&t.$set(t.formData,a.key,r[a.key]?r[a.key]:0)}))},distributeHandler:function(){var e=this;this.$confirm("是否更新食材到商户端食材库？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var t=k(u().mark((function t(n,r,a){var i,l,s,c;return u().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if("confirm"!==n){t.next=19;break}if(!e.isLoading){t.next=3;break}return t.abrupt("return",e.$message.error("请勿重复提交！"));case 3:return e.isLoading=!0,r.confirmButtonLoading=!0,t.next=7,Object(o["Z"])(e.$apis.apiBackgroundAdminIngredientSyncIngredientPost());case 7:if(i=t.sent,l=g(i,2),s=l[0],c=l[1],e.isLoading=!1,!s){t.next=15;break}return e.$message.error(s.message),t.abrupt("return");case 15:0===c.code?(a(),e.$message.success(c.msg),e.getListHandler()):e.$message.error(c.msg),r.confirmButtonLoading=!1,t.next=20;break;case 19:r.confirmButtonLoading||a();case 20:case"end":return t.stop()}}),t)})));function n(e,n,r){return t.apply(this,arguments)}return n}()}).then((function(e){})).catch((function(e){}))},deleteHandler:function(e,t){var n=this,r=[];if("single"===e)r=[t];else{if(!this.checkList.length)return this.$message.error("请先选择要删除的数据！");r=this.checkList}this.$confirm("确定删除？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-warn",center:!0,beforeClose:function(){var e=k(u().mark((function e(t,a,i){var l,s,c,p;return u().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("confirm"!==t){e.next=19;break}if(!n.dialogLoading){e.next=3;break}return e.abrupt("return",n.$message.error("请勿重复提交！"));case 3:return n.dialogLoading=!0,a.confirmButtonLoading=!0,e.next=7,Object(o["Z"])(n.$apis.apiBackgroundAdminIngredientDeletePost({ids:r}));case 7:if(l=e.sent,s=g(l,2),c=s[0],p=s[1],n.dialogLoading=!1,!c){e.next=15;break}return n.$message.error(c.message),e.abrupt("return");case 15:0===p.code?(i(),n.$message.success(p.msg),n.getListHandler(),n.checkList=[]):n.$message.error(p.msg),a.confirmButtonLoading=!1,e.next=20;break;case 19:a.confirmButtonLoading||i();case 20:case"end":return e.stop()}}),e)})));function t(t,n,r){return e.apply(this,arguments)}return t}()}).then((function(e){})).catch((function(e){}))},updateSettingHandler:function(e){var t=this,n="是否允许商户上传信息?";this.updateSetting||(n="是否关闭用户上传信息?"),this.$confirm(n,{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var e=k(u().mark((function e(n,r,a){var i,l,s,c;return u().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("confirm"!==n){e.next=19;break}if(!t.isLoading){e.next=3;break}return e.abrupt("return",t.$message.error("请勿重复提交！"));case 3:return t.isLoading=!0,r.confirmButtonLoading=!0,e.next=7,Object(o["Z"])(t.$apis.apiBackgroundAdminIngredientChangeUploadPost({is_enable:t.updateSetting?1:0}));case 7:if(i=e.sent,l=g(i,2),s=l[0],c=l[1],t.isLoading=!1,!s){e.next=15;break}return t.$message.error(s.message),e.abrupt("return");case 15:0===c.code?(a(),t.$message.success(c.msg),t.getListHandler()):t.$message.error(c.msg),r.confirmButtonLoading=!1,e.next=20;break;case 19:r.confirmButtonLoading||(a(),t.updateSetting=!t.updateSetting);case 20:case"end":return e.stop()}}),e)})));function n(t,n,r){return e.apply(this,arguments)}return n}()}).then((function(e){})).catch((function(e){}))},addToSystem:function(e){var t=this;this.$confirm('是否创建到食材库?<p style="color:red;">注：食材重名自动覆盖！<p>',{dangerouslyUseHTMLString:!0,confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var n=k(u().mark((function n(r,a,i){var l,s,c,p;return u().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if("confirm"!==r){n.next=19;break}if(!t.isLoading){n.next=3;break}return n.abrupt("return",t.$message.error("请勿重复提交！"));case 3:return t.isLoading=!0,a.confirmButtonLoading=!0,n.next=7,Object(o["Z"])(t.$apis.apiBackgroundAdminIngredientAddToSystemPost({id:e}));case 7:if(l=n.sent,s=g(l,2),c=s[0],p=s[1],t.isLoading=!1,!c){n.next=15;break}return t.$message.error(c.message),n.abrupt("return");case 15:0===p.code?(i(),t.$message.success(p.msg),t.getListHandler()):t.$message.error(p.msg),a.confirmButtonLoading=!1,n.next=20;break;case 19:a.confirmButtonLoading||i();case 20:case"end":return n.stop()}}),n)})));function r(e,t,r){return n.apply(this,arguments)}return r}()}).then((function(e){})).catch((function(e){}))},addIngredients:function(){this.$router.push({name:"SuperAddIngredients",query:{type:"add"},params:{type:"add"}})},modifyIngredients:function(e){this.$router.push({name:"SuperAddIngredients",query:{type:"modify",data:this.$encodeQuery(e)},params:{type:"modify"}})},handleSelectionChange:function(e){var t=this;this.checkList=[],e.map((function(e){t.checkList.push(e.id)}))},showMoreHandler:function(e){e.target.parentNode.classList.remove("hide")},hideMoreHandler:function(e){e.target.parentNode.classList.add("hide")},batchLabelClick:function(e){if(this.batchLabelType=e,"batchLabelDel"===e?this.titleSelectLaber="批量移除标签":"batchLabelAdd"===e&&(this.titleSelectLaber="批量打标签"),!this.checkList.length)return this.$message.error("请先选择要".concat(this.titleSelectLaber,"的数据！"));this.ruleSingleInfo={labelType:"ingredient"},this.selectLaberDialogVisible=!0},getAllLabelGroupList:function(){var e=this;return k(u().mark((function t(){var n,r,a,i;return u().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(o["Z"])(e.$apis.apiBackgroundHealthyAdminLabelGroupAllLabelGroupListPost({page_size:999999,page:1,type:"ingredient"}));case 3:if(n=t.sent,r=g(n,2),a=r[0],i=r[1],e.isLoading=!1,!a){t.next=11;break}return e.$message.error(a.message),t.abrupt("return");case 11:0===i.code?(i.data.results.map((function(e){return e.label_list.length||(e.isDisabled=!0),e})),e.searchFormSetting.label_list.dataList=i.data.results):e.$message({type:"error",duration:1e3,message:i.msg});case 12:case"end":return t.stop()}}),t)})))()},selectLaberData:function(e){var t=this;this.$confirm("是否".concat(this.titleSelectLaber),{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-warn",center:!0,beforeClose:function(){var n=k(u().mark((function n(r,a,i){var l,s,c,p,d,f,h,m,b;return u().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if("confirm"!==r){n.next=32;break}if(!t.dialogLoading){n.next=3;break}return n.abrupt("return",t.$message.error("请勿重复提交！"));case 3:if(t.dialogLoading=!0,a.confirmButtonLoading=!0,l={ids:t.checkList,label_list:e.selectLabelIdList},s="",c=g(s,2),p=c[0],d=c[1],"batchLabelAdd"!==t.batchLabelType){n.next=17;break}return n.next=11,Object(o["Z"])(t.$apis.apiBackgroundAdminIngredientBatchAddLabelPost(l));case 11:f=n.sent,h=g(f,2),p=h[0],d=h[1],n.next=24;break;case 17:return n.next=20,Object(o["Z"])(t.$apis.apiBackgroundAdminIngredientBatchDeleteLabelPost(l));case 20:m=n.sent,b=g(m,2),p=b[0],d=b[1];case 24:if(t.dialogLoading=!1,!p){n.next=28;break}return t.$message.error(p.message),n.abrupt("return");case 28:0===d.code?(i(),t.$message.success(d.msg),t.$refs.tableData.clearSelection(),t.getListHandler(),t.checkList=[]):t.$message.error(d.msg),a.confirmButtonLoading=!1,n.next=33;break;case 32:a.confirmButtonLoading||i();case 33:case"end":return n.stop()}}),n)})));function r(e,t,r){return n.apply(this,arguments)}return r}()}).then((function(e){})).catch((function(e){}))},closeTag:function(e,t){this.batchLabelType="delSingleTag",this.titleSelectLaber="删除该标签";var n={selectLabelIdList:[e.id]};this.checkList=[t.id],this.selectLaberData(n)},getListHandler:function(){"system"===this.tabType?this.getIngredientslist():this.getIngredientsManchantlist()},handleSizeChange:function(e){this.pageSize=e,this.getListHandler()},handleCurrentChange:function(e){this.currentPage=e,this.getListHandler()},dialogHandleClose:function(){this.formData={}},importHandler:function(e){"importImage"===e?this.$router.push({name:"SuperImportIngredientImage",params:{type:e}}):this.$router.push({name:"SuperImportIngredients",params:{type:e}})},gotoCategory:function(){this.$router.push({name:"SuperImportIngredientsCategory"})},gotoExport:function(){var e={type:"SuperIngredientsLibrary",params:d(d({},this.formatQueryParams(this.searchFormSetting)),{},{page:this.currentPage,page_size:this.pageSize})};"system"===this.tabType?e.type="SuperIngredientsLibrarySystem":e.type="SuperIngredientsLibraryMerchant",this.exportHandle(e)}}},S=x,C=(n("75bb"),n("2877")),I=Object(C["a"])(S,r,a,!1,null,"a86d4766",null);t["default"]=I.exports},"3e25":function(e,t,n){},"75bb":function(e,t,n){"use strict";n("3e25")},c4a9:function(e,t,n){}}]);