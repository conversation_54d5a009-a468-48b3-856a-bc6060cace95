(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-points-admin-components-commodiyModifyDrawer"],{"11cc":function(t,r,e){},"21c9":function(t,r,e){"use strict";e.r(r);var a=function(){var t=this,r=t._self._c;return r("div",{staticClass:"drawer-box"},[r("customDrawer",{attrs:{show:t.visible,loading:t.isLoading,title:t.drawerTitle,size:800},on:{"update:show":function(r){t.visible=r},confirm:t.saveSetting}},[r("div",{staticClass:"drawer-container"},[r("div",{staticClass:"drawer-content"},[r("el-form",{ref:"drawerFormDataRef",attrs:{model:t.drawerFormData,rules:t.drawerFormDataRuls}},[r("el-form-item",{attrs:{label:"商品名称",prop:"name","label-width":"80px"}},[r("el-input",{staticClass:"w-350 ps-input",attrs:{maxlength:"20",placeholder:"请输入商品名称"},model:{value:t.drawerFormData.name,callback:function(r){t.$set(t.drawerFormData,"name",r)},expression:"drawerFormData.name"}})],1),r("el-form-item",{attrs:{label:"识别图片","label-width":"80px",prop:"imagesUrl"}},[r("div",{staticClass:"msg-tips"},[t._v("最多上传6张，格式支持jpg、png等，每张不超过5M")]),r("el-upload",{ref:"uploadExtraImage",staticClass:"upload-food",attrs:{drag:"",data:t.uploadParams,action:t.actionUrl,multiple:!0,"file-list":t.drawerFormData.imagesUrlList,"list-type":"picture-card","on-change":t.handelChange,"on-success":t.handleImgSuccess,"before-upload":t.beforeFoodImgUpload,limit:6,headers:t.headersOpts},scopedSlots:t._u([{key:"file",fn:function(e){var a=e.file;return r("div",{directives:[{name:"loading",rawName:"v-loading",value:"uploading"===a.status,expression:"file.status === 'uploading'"}],attrs:{"element-loading-text":"上传中"}},[r("div",{staticClass:"upload-food-img"},[r("img",{attrs:{src:a.url,alt:""}})]),r("span",{staticClass:"el-upload-list__item-actions"},[r("span",{staticClass:"el-upload-list__item-preview",on:{click:function(r){return t.handlePictureCardPreview(a)}}},[r("i",{staticClass:"el-icon-zoom-in"})]),r("span",{staticClass:"el-upload-list__item-delete",on:{click:function(r){return t.handleFoodImgRemove(a,"extraImages")}}},[r("i",{staticClass:"el-icon-delete"})])])])}}])},[t.drawerFormData.imagesUrl.length<6?r("i",{staticClass:"el-icon-plus"}):t._e()])],1),r("el-form-item",{attrs:{label:"上架时间",prop:"listingTime","label-width":"80px"}},[r("el-date-picker",{attrs:{type:"daterange","value-format":"yyyy-MM-dd","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",disabled:t.drawerFormData.isPermanent},model:{value:t.drawerFormData.listingTime,callback:function(r){t.$set(t.drawerFormData,"listingTime",r)},expression:"drawerFormData.listingTime"}}),r("el-checkbox",{staticStyle:{"padding-left":"20px"},on:{change:t.changeIsPermanent},model:{value:t.drawerFormData.isPermanent,callback:function(r){t.$set(t.drawerFormData,"isPermanent",r)},expression:"drawerFormData.isPermanent"}},[t._v(" 永久 ")])],1),r("el-form-item",{attrs:{label:"商品类型",prop:"commodityType","label-width":"80px"}},[r("el-radio-group",{staticClass:"ps-radio",model:{value:t.drawerFormData.commodityType,callback:function(r){t.$set(t.drawerFormData,"commodityType",r)},expression:"drawerFormData.commodityType"}},[r("el-radio",{attrs:{label:"virtual"}},[t._v("虚拟商品")]),r("el-radio",{attrs:{label:"physical"}},[t._v("实物商品")])],1),"virtual"===t.drawerFormData.commodityType?r("div",{staticStyle:{display:"flex"}},[r("el-form-item",{attrs:{prop:"virtualCommodityType"}},[r("el-select",{staticClass:"ps-select w-180",attrs:{placeholder:"请选择类型",clearable:""},model:{value:t.drawerFormData.virtualCommodityType,callback:function(r){t.$set(t.drawerFormData,"virtualCommodityType",r)},expression:"drawerFormData.virtualCommodityType"}},t._l(t.virtualCommodityTypeList,(function(t){return r("el-option",{key:t.type,attrs:{label:t.name,value:t.type}})})),1)],1),t.drawerFormData.virtualCommodityType?r("el-form-item",{attrs:{"label-width":"20px",prop:"virtualNum"}},[r("el-input",{staticClass:"w-150 ps-input",attrs:{placeholder:"请输入"},model:{value:t.drawerFormData.virtualNum,callback:function(r){t.$set(t.drawerFormData,"virtualNum",r)},expression:"drawerFormData.virtualNum"}}),r("span",{staticStyle:{"padding-left":"10px"}},[t._v(" "+t._s("member"===t.drawerFormData.virtualCommodityType?"天":"次")+" ")])],1):t._e()],1):t._e(),"physical"===t.drawerFormData.commodityType?r("div",[r("el-form-item",{attrs:{prop:"physicalCode"}},[r("el-input",{staticClass:"w-150 ps-input",attrs:{placeholder:"请输入商品编码"},model:{value:t.drawerFormData.physicalCode,callback:function(r){t.$set(t.drawerFormData,"physicalCode",r)},expression:"drawerFormData.physicalCode"}})],1)],1):t._e()],1),r("el-form-item",{attrs:{label:"商品价格",prop:"commodityPriceType","label-width":"80px"}},[r("el-radio-group",{staticClass:"ps-radio",model:{value:t.drawerFormData.commodityPriceType,callback:function(r){t.$set(t.drawerFormData,"commodityPriceType",r)},expression:"drawerFormData.commodityPriceType"}},[r("el-radio",{attrs:{label:"money_points"}},[t._v("金额+积分")]),r("el-radio",{attrs:{label:"money"}},[t._v("金额")]),r("el-radio",{attrs:{label:"points"}},[t._v("积分")])],1),r("div",{staticStyle:{display:"flex"}},["money"===t.drawerFormData.commodityPriceType||"money_points"===t.drawerFormData.commodityPriceType?r("el-form-item",{attrs:{prop:"fee"}},[r("el-input",{staticClass:"w-150 ps-input",attrs:{placeholder:"请输入金额"},model:{value:t.drawerFormData.fee,callback:function(r){t.$set(t.drawerFormData,"fee",r)},expression:"drawerFormData.fee"}}),r("span",{staticStyle:{"padding-left":"10px"}},[t._v("元")]),"money_points"===t.drawerFormData.commodityPriceType?r("span",{staticStyle:{"padding-right":"10px"}},[t._v(" + ")]):t._e()],1):t._e(),"points"===t.drawerFormData.commodityPriceType||"money_points"===t.drawerFormData.commodityPriceType?r("el-form-item",{attrs:{prop:"points"}},[r("el-input",{staticClass:"w-150 ps-input",attrs:{placeholder:"请输入积分"},model:{value:t.drawerFormData.points,callback:function(r){t.$set(t.drawerFormData,"points",r)},expression:"drawerFormData.points"}}),r("span",{staticStyle:{"padding-left":"10px"}},[t._v("积分")])],1):t._e()],1)],1),r("el-form-item",{attrs:{label:"库存数量",prop:"buyStockNum","label-width":"80px"}},[r("el-input",{staticClass:"w-350 ps-input",attrs:{placeholder:"请输入库存数量",disabled:t.drawerFormData.buyStockNumType},model:{value:t.drawerFormData.buyStockNum,callback:function(r){t.$set(t.drawerFormData,"buyStockNum",r)},expression:"drawerFormData.buyStockNum"}}),r("el-checkbox",{staticStyle:{"padding-left":"20px"},on:{change:t.changeIsBuyStock},model:{value:t.drawerFormData.buyStockNumType,callback:function(r){t.$set(t.drawerFormData,"buyStockNumType",r)},expression:"drawerFormData.buyStockNumType"}},[t._v(" 不限制 ")])],1),r("el-form-item",{attrs:{label:"可兑换数",required:""}},[r("div",{staticStyle:{display:"flex"}},[r("el-form-item",{attrs:{prop:"buyLimitType","lable-width":"70px"}},[r("el-select",{staticClass:"ps-select w-180",model:{value:t.drawerFormData.buyLimitType,callback:function(r){t.$set(t.drawerFormData,"buyLimitType",r)},expression:"drawerFormData.buyLimitType"}},t._l(t.buyLimitTypeList,(function(t){return r("el-option",{key:t.type,attrs:{label:t.name,value:t.type}})})),1)],1),"non"!==t.drawerFormData.buyLimitType&&t.drawerFormData.buyLimitType?r("el-form-item",{staticStyle:{padding:"0 10px"},attrs:{prop:"buyLimitNum"}},[r("el-input",{staticClass:"ps-input",staticStyle:{width:"170px"},attrs:{placeholder:"请输入次数"},model:{value:t.drawerFormData.buyLimitNum,callback:function(r){t.$set(t.drawerFormData,"buyLimitNum",r)},expression:"drawerFormData.buyLimitNum"}}),r("span",{staticStyle:{"padding-left":"10px"}},[t._v("次")])],1):t._e()],1)]),r("el-form-item",{attrs:{label:"图文详情",prop:"details","label-width":"80px"}},[r("TinymceUeditor",{attrs:{content:t.drawerFormData.details},on:{message:t.messageTinymceUeditor},model:{value:t.drawerFormData.details,callback:function(r){t.$set(t.drawerFormData,"details",r)},expression:"drawerFormData.details"}})],1),r("el-form-item",{attrs:{label:"优先级",prop:"priority","label-width":"80px"}},[r("el-input",{staticClass:"w-350 ps-input",attrs:{maxlength:"20",placeholder:"请输入优先级"},model:{value:t.drawerFormData.priority,callback:function(r){t.$set(t.drawerFormData,"priority",r)},expression:"drawerFormData.priority"}}),r("div",[t._v("现有优先级："+t._s(t.priorityText()))])],1)],1)],1)])]),r("el-dialog",{attrs:{visible:t.dialogVisible},on:{"update:visible":function(r){t.dialogVisible=r}}},[r("img",{attrs:{width:"100%",src:t.dialogImageUrl,alt:""}})])],1)},i=[],o=e("ed08"),n=e("56f9"),s=e("e925"),l=e("da92");function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function c(t,r){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);r&&(a=a.filter((function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable}))),e.push.apply(e,a)}return e}function m(t){for(var r=1;r<arguments.length;r++){var e=null!=arguments[r]?arguments[r]:{};r%2?c(Object(e),!0).forEach((function(r){d(t,r,e[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):c(Object(e)).forEach((function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(e,r))}))}return t}function d(t,r,e){return(r=p(r))in t?Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[r]=e,t}function p(t){var r=y(t,"string");return"symbol"==u(r)?r:r+""}function y(t,r){if("object"!=u(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var a=e.call(t,r||"default");if("object"!=u(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}function f(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */f=function(){return r};var t,r={},e=Object.prototype,a=e.hasOwnProperty,i=Object.defineProperty||function(t,r,e){t[r]=e.value},o="function"==typeof Symbol?Symbol:{},n=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function c(t,r,e){return Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}),t[r]}try{c({},"")}catch(t){c=function(t,r,e){return t[r]=e}}function m(t,r,e,a){var o=r&&r.prototype instanceof v?r:v,n=Object.create(o.prototype),s=new $(a||[]);return i(n,"_invoke",{value:L(t,e,s)}),n}function d(t,r,e){try{return{type:"normal",arg:t.call(r,e)}}catch(t){return{type:"throw",arg:t}}}r.wrap=m;var p="suspendedStart",y="suspendedYield",h="executing",w="completed",g={};function v(){}function b(){}function D(){}var F={};c(F,n,(function(){return this}));var _=Object.getPrototypeOf,T=_&&_(_(M([])));T&&T!==e&&a.call(T,n)&&(F=T);var x=D.prototype=v.prototype=Object.create(F);function k(t){["next","throw","return"].forEach((function(r){c(t,r,(function(t){return this._invoke(r,t)}))}))}function P(t,r){function e(i,o,n,s){var l=d(t[i],t,o);if("throw"!==l.type){var c=l.arg,m=c.value;return m&&"object"==u(m)&&a.call(m,"__await")?r.resolve(m.__await).then((function(t){e("next",t,n,s)}),(function(t){e("throw",t,n,s)})):r.resolve(m).then((function(t){c.value=t,n(c)}),(function(t){return e("throw",t,n,s)}))}s(l.arg)}var o;i(this,"_invoke",{value:function(t,a){function i(){return new r((function(r,i){e(t,a,r,i)}))}return o=o?o.then(i,i):i()}})}function L(r,e,a){var i=p;return function(o,n){if(i===h)throw Error("Generator is already running");if(i===w){if("throw"===o)throw n;return{value:t,done:!0}}for(a.method=o,a.arg=n;;){var s=a.delegate;if(s){var l=S(s,a);if(l){if(l===g)continue;return l}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(i===p)throw i=w,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);i=h;var u=d(r,e,a);if("normal"===u.type){if(i=a.done?w:y,u.arg===g)continue;return{value:u.arg,done:a.done}}"throw"===u.type&&(i=w,a.method="throw",a.arg=u.arg)}}}function S(r,e){var a=e.method,i=r.iterator[a];if(i===t)return e.delegate=null,"throw"===a&&r.iterator.return&&(e.method="return",e.arg=t,S(r,e),"throw"===e.method)||"return"!==a&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+a+"' method")),g;var o=d(i,r.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,g;var n=o.arg;return n?n.done?(e[r.resultName]=n.value,e.next=r.nextLoc,"return"!==e.method&&(e.method="next",e.arg=t),e.delegate=null,g):n:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,g)}function C(t){var r={tryLoc:t[0]};1 in t&&(r.catchLoc=t[1]),2 in t&&(r.finallyLoc=t[2],r.afterLoc=t[3]),this.tryEntries.push(r)}function N(t){var r=t.completion||{};r.type="normal",delete r.arg,t.completion=r}function $(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(C,this),this.reset(!0)}function M(r){if(r||""===r){var e=r[n];if(e)return e.call(r);if("function"==typeof r.next)return r;if(!isNaN(r.length)){var i=-1,o=function e(){for(;++i<r.length;)if(a.call(r,i))return e.value=r[i],e.done=!1,e;return e.value=t,e.done=!0,e};return o.next=o}}throw new TypeError(u(r)+" is not iterable")}return b.prototype=D,i(x,"constructor",{value:D,configurable:!0}),i(D,"constructor",{value:b,configurable:!0}),b.displayName=c(D,l,"GeneratorFunction"),r.isGeneratorFunction=function(t){var r="function"==typeof t&&t.constructor;return!!r&&(r===b||"GeneratorFunction"===(r.displayName||r.name))},r.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,D):(t.__proto__=D,c(t,l,"GeneratorFunction")),t.prototype=Object.create(x),t},r.awrap=function(t){return{__await:t}},k(P.prototype),c(P.prototype,s,(function(){return this})),r.AsyncIterator=P,r.async=function(t,e,a,i,o){void 0===o&&(o=Promise);var n=new P(m(t,e,a,i),o);return r.isGeneratorFunction(e)?n:n.next().then((function(t){return t.done?t.value:n.next()}))},k(x),c(x,l,"Generator"),c(x,n,(function(){return this})),c(x,"toString",(function(){return"[object Generator]"})),r.keys=function(t){var r=Object(t),e=[];for(var a in r)e.push(a);return e.reverse(),function t(){for(;e.length;){var a=e.pop();if(a in r)return t.value=a,t.done=!1,t}return t.done=!0,t}},r.values=M,$.prototype={constructor:$,reset:function(r){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(N),!r)for(var e in this)"t"===e.charAt(0)&&a.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(r){if(this.done)throw r;var e=this;function i(a,i){return s.type="throw",s.arg=r,e.next=a,i&&(e.method="next",e.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var n=this.tryEntries[o],s=n.completion;if("root"===n.tryLoc)return i("end");if(n.tryLoc<=this.prev){var l=a.call(n,"catchLoc"),u=a.call(n,"finallyLoc");if(l&&u){if(this.prev<n.catchLoc)return i(n.catchLoc,!0);if(this.prev<n.finallyLoc)return i(n.finallyLoc)}else if(l){if(this.prev<n.catchLoc)return i(n.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<n.finallyLoc)return i(n.finallyLoc)}}}},abrupt:function(t,r){for(var e=this.tryEntries.length-1;e>=0;--e){var i=this.tryEntries[e];if(i.tryLoc<=this.prev&&a.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=r&&r<=o.finallyLoc&&(o=null);var n=o?o.completion:{};return n.type=t,n.arg=r,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(n)},complete:function(t,r){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&r&&(this.next=r),g},finish:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.finallyLoc===t)return this.complete(e.completion,e.afterLoc),N(e),g}},catch:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.tryLoc===t){var a=e.completion;if("throw"===a.type){var i=a.arg;N(e)}return i}}throw Error("illegal catch attempt")},delegateYield:function(r,e,a){return this.delegate={iterator:M(r),resultName:e,nextLoc:a},"next"===this.method&&(this.arg=t),g}},r}function h(t,r){return D(t)||b(t,r)||g(t,r)||w()}function w(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function g(t,r){if(t){if("string"==typeof t)return v(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?v(t,r):void 0}}function v(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,a=Array(r);e<r;e++)a[e]=t[e];return a}function b(t,r){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var a,i,o,n,s=[],l=!0,u=!1;try{if(o=(e=e.call(t)).next,0===r){if(Object(e)!==e)return;l=!1}else for(;!(l=(a=o.call(e)).done)&&(s.push(a.value),s.length!==r);l=!0);}catch(t){u=!0,i=t}finally{try{if(!l&&null!=e.return&&(n=e.return(),Object(n)!==n))return}finally{if(u)throw i}}return s}}function D(t){if(Array.isArray(t))return t}function F(t,r,e,a,i,o,n){try{var s=t[o](n),l=s.value}catch(t){return void e(t)}s.done?r(l):Promise.resolve(l).then(a,i)}function _(t){return function(){var r=this,e=arguments;return new Promise((function(a,i){var o=t.apply(r,e);function n(t){F(o,a,i,n,s,"next",t)}function s(t){F(o,a,i,n,s,"throw",t)}n(void 0)}))}}var T={props:{drawerType:{type:String,default:function(){return"add"}},isshow:Boolean,drawerModifyData:{type:Object,default:function(){return{}}},collectData:{type:Object,default:function(){return{}}}},components:{TinymceUeditor:n["a"]},data:function(){var t=this,r=function(r,e,a){if(!t.drawerFormData.imagesUrl.length)return a(new Error("请上传商品图片"));a()},e=function(t,r,e){if(r)if(Object(s["m"])(r)&&Number(r)){if(Number(r)>9999.99)return e(new Error("最大值不能超过9999.99"));e()}else e(new Error("格式错误"));else e(new Error("请输入"))},a=function(t,r,e){var a=/^\d+$/;if(!r)return e(new Error("格式错误"));a.test(r)&&Number(r)?e():e(new Error("格式错误"))},i=function(r,e,a){var i=/^\d+$/;if(t.drawerFormData.buyStockNumType)a();else{if(!e)return a(new Error("格式错误"));i.test(e)?a():a(new Error("格式错误"))}};return{drawerTitle:"新建商品",isLoading:!1,drawerFormData:{name:"",imagesUrlList:[],imagesUrl:[],listingTime:[],isPermanent:!1,commodityType:"",virtualCommodityType:"",virtualNum:"",physicalCode:"",commodityPriceType:"",fee:"",points:"",buyStockNum:"",buyStockNumType:!1,buyLimitType:"",buyLimitNum:"",details:"",priority:""},drawerFormDataRuls:{name:[{required:!0,message:"请输入商品名称",trigger:"blur"}],imagesUrl:[{required:!0,validator:r,trigger:"blur"}],listingTime:[{required:!0,message:"请选择上架时间",trigger:["change","blur"]}],commodityType:[{required:!0,message:"请选择商品类型",trigger:"change"}],virtualCommodityType:[{required:!0,message:"请选择商品",trigger:"change"}],virtualNum:[{required:!0,validator:a,trigger:["change","blur"]}],physicalCode:[{required:!0,message:"请输入商品编码",trigger:["change","blur"]}],commodityPriceType:[{required:!0,message:"请选择商品价格",trigger:["change","blur"]}],fee:[{required:!0,validator:e,trigger:["change","blur"]}],points:[{required:!0,validator:a,message:"请输入积分",trigger:["change","blur"]}],buyStockNum:[{required:!0,validator:i,message:"请输入库存数量",trigger:["change","blur"]}],buyLimitType:[{required:!0,message:"请选择可兑换数",trigger:["change","blur"]}],buyLimitNum:[{required:!0,validator:a,message:"请输入次数",trigger:["change","blur"]}],details:[{required:!0,message:"请输入图文详情",trigger:["change","blur"]}],priority:[{required:!0,validator:a,message:"请输入优先级",trigger:["change","blur"]}]},actionUrl:"/api/background/file/upload",headersOpts:{TOKEN:Object(o["B"])()},uploadParams:{prefix:"commodityImage"},dialogImageUrl:"",dialogVisible:!1,virtualCommodityTypeList:[{name:"食堂会员",type:"member"},{name:"AI营养师咨询",type:"ai_nutritionist"}],buyLimitTypeList:[{name:"不限制",type:"non"},{name:"每天限制",type:"day"},{name:"每周限制",type:"week"},{name:"每月限制",type:"month"},{name:"每人限制",type:"person"}]}},computed:{visible:{get:function(){return this.isshow},set:function(t){this.$emit("update:isshow",t)}}},mounted:function(){this.initDrawerForm()},methods:{initDrawerForm:function(){"modify"===this.drawerType?(this.drawerTitle="修改商品",this.drawerFormData.name=this.drawerModifyData.name,this.drawerFormData.imagesUrlList=this.drawerModifyData.images_url.map((function(t){var r={url:t,name:t,status:"success",uid:t};return r})),this.drawerFormData.imagesUrl=this.drawerModifyData.images_url,this.drawerFormData.isPermanent=this.drawerModifyData.is_permanent,this.drawerModifyData.is_permanent||(this.drawerFormData.listingTime=[this.drawerModifyData.start_date,this.drawerModifyData.end_date]),this.changeIsPermanent(),this.drawerFormData.commodityType=this.drawerModifyData.commodity_type,"virtual"===this.drawerModifyData.commodity_type&&(this.drawerFormData.virtualCommodityType=this.drawerModifyData.virtual_commodity_type,"member"===this.drawerModifyData.virtual_commodity_type?this.drawerFormData.virtualNum=this.drawerModifyData.commodity_extra.day:"ai_nutritionist"===this.drawerFormData.virtualCommodityType&&(this.drawerFormData.virtualNum=this.drawerModifyData.commodity_extra.count)),"physical"===this.drawerModifyData.commodity_type&&(this.drawerFormData.physicalCode=this.drawerModifyData.physical_code),this.drawerFormData.commodityPriceType=this.drawerModifyData.commodity_price_type,"money_points"===this.drawerModifyData.commodity_price_type?(this.drawerFormData.fee=l["a"].divide(this.drawerModifyData.fee,100),this.drawerFormData.points=this.drawerModifyData.points):"money"===this.drawerModifyData.commodity_price_type?this.drawerFormData.fee=l["a"].divide(this.drawerModifyData.fee,100):"points"===this.drawerModifyData.commodity_price_type&&(this.drawerFormData.points=this.drawerModifyData.points),-1===this.drawerModifyData.buy_stock_num?(this.drawerFormData.buyStockNumType=!0,this.changeIsBuyStock()):this.drawerFormData.buyStockNum=this.drawerModifyData.buy_stock_num,this.drawerFormData.buyLimitType=this.drawerModifyData.buy_limit_type,"non"!==this.drawerModifyData.buy_limit_type&&(this.drawerFormData.buyLimitNum=this.drawerModifyData.buy_limit_num),this.drawerFormData.details=this.drawerModifyData.details,this.drawerFormData.priority=this.drawerModifyData.priority):this.drawerTitle="新建商品"},closeClick:function(){this.visible=!1},priorityText:function(){return this.collectData.priority_list&&this.collectData.priority_list.length?this.collectData.priority_list.join(","):""},initParams:function(){var t={name:this.drawerFormData.name,images_url:this.drawerFormData.imagesUrl,is_permanent:this.drawerFormData.isPermanent,commodity_type:this.drawerFormData.commodityType,commodity_price_type:this.drawerFormData.commodityPriceType,buy_limit_type:this.drawerFormData.buyLimitType,details:this.drawerFormData.details,priority:this.drawerFormData.priority};return this.drawerFormData.isPermanent||(t.start_date=this.drawerFormData.listingTime[0],t.end_date=this.drawerFormData.listingTime[1]),"virtual"===this.drawerFormData.commodityType&&(t.virtual_commodity_type=this.drawerFormData.virtualCommodityType,"member"===this.drawerFormData.virtualCommodityType?t.commodity_extra={day:this.drawerFormData.virtualNum}:"ai_nutritionist"===this.drawerFormData.virtualCommodityType&&(t.commodity_extra={count:this.drawerFormData.virtualNum})),"physical"===this.drawerFormData.commodityType&&(t.physical_code=this.drawerFormData.physicalCode),"money_points"===this.drawerFormData.commodityPriceType?(t.fee=l["a"].times(this.drawerFormData.fee,100),t.points=this.drawerFormData.points):"money"===this.drawerFormData.commodityPriceType?t.fee=l["a"].times(this.drawerFormData.fee,100):"points"===this.drawerFormData.commodityPriceType&&(t.points=this.drawerFormData.points),this.drawerFormData.buyStockNumType?t.buy_stock_num=-1:t.buy_stock_num=this.drawerFormData.buyStockNum,"non"!==this.drawerFormData.buyLimitType&&(t.buy_limit_num=this.drawerFormData.buyLimitNum),t},saveSetting:function(){var t=this;this.$refs.drawerFormDataRef.validate((function(r){r&&("modify"===t.drawerType?t.modifyDrawerForm():t.addDrawerForm())}))},addDrawerForm:function(){var t=this;return _(f().mark((function r(){var e,a,i,o;return f().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return t.isLoading=!0,r.next=3,t.$to(t.$apis.apiBackgroundMemberPointsPointsCommodityAddPost(t.initParams()));case 3:if(e=r.sent,a=h(e,2),i=a[0],o=a[1],t.isLoading=!1,!i){r.next=11;break}return t.$message.error(i.message),r.abrupt("return");case 11:0===o.code?(t.visible=!1,t.$message.success("修改成功"),t.$emit("clickSaveDrawer")):t.$message.error(o.msg);case 12:case"end":return r.stop()}}),r)})))()},modifyDrawerForm:function(){var t=this;return _(f().mark((function r(){var e,a,i,o;return f().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return t.isLoading=!0,r.next=3,t.$to(t.$apis.apiBackgroundMemberPointsPointsCommodityModifyPost(m({id:t.drawerModifyData.id},t.initParams())));case 3:if(e=r.sent,a=h(e,2),i=a[0],o=a[1],t.isLoading=!1,!i){r.next=11;break}return t.$message.error(i.message),r.abrupt("return");case 11:0===o.code?(t.visible=!1,t.$message.success("修改成功"),t.$emit("clickSaveDrawer")):t.$message.error(o.msg);case 12:case"end":return r.stop()}}),r)})))()},changeIsPermanent:function(){this.drawerFormData.isPermanent?this.drawerFormDataRuls.listingTime[0].required=!1:this.drawerFormDataRuls.listingTime[0].required=!0},changeIsBuyStock:function(){this.drawerFormData.buyStockNumType?this.drawerFormDataRuls.buyStockNum[0].required=!1:this.drawerFormDataRuls.buyStockNum[0].required=!0},messageTinymceUeditor:function(t){this.drawerFormData.details=t},handelChange:function(){this.uploadParams.key=this.uploadParams.prefix+(new Date).getTime()+Math.floor(150*Math.random())+".png"},handleImgSuccess:function(t,r,e){0===t.code?(this.drawerFormData.imagesUrlList=e,this.drawerFormData.imagesUrl.push(t.data.public_url)):this.$message.error(t.msg)},handlePictureCardPreview:function(t){this.dialogImageUrl=t.url,this.dialogVisible=!0},handleFoodImgRemove:function(t,r){var e=this.drawerFormData.imagesUrlList.findIndex((function(r){return r.url===t.url}));this.drawerFormData.imagesUrl.splice(e,1),this.drawerFormData.imagesUrlList.splice(e,1)},beforeFoodImgUpload:function(t){var r=[".jpeg",".jpg",".png",".bmp"],e=t.size/1024/1024<5;return r.includes(Object(o["A"])(t.name))?e?void 0:(this.$message.error("上传图片大小不能超过 5MB!"),!1):(this.$message.error("上传图片只能是JPG/BMP/PNG格式!"),!1)}}},x=T,k=(e("4e9e"),e("2877")),P=Object(k["a"])(x,a,i,!1,null,"6b54f40e",null);r["default"]=P.exports},"4e9e":function(t,r,e){"use strict";e("11cc")},e925:function(t,r,e){"use strict";e.d(r,"c",(function(){return a})),e.d(r,"g",(function(){return i})),e.d(r,"i",(function(){return o})),e.d(r,"e",(function(){return n})),e.d(r,"h",(function(){return s})),e.d(r,"f",(function(){return l})),e.d(r,"d",(function(){return u})),e.d(r,"m",(function(){return c})),e.d(r,"l",(function(){return m})),e.d(r,"n",(function(){return d})),e.d(r,"j",(function(){return p})),e.d(r,"b",(function(){return y})),e.d(r,"k",(function(){return f})),e.d(r,"a",(function(){return h}));var a=function(t){return/^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(t)},i=function(t){return/^([0-9]{3,4}-)?[0-9]{7,8}$/.test(t.toString())},o=function(t){return/^\w{5,20}$/.test(t)},n=function(t){return/^(?=.*[0-9])(?=.*[a-zA-Z])(.{8,20})$/.test(t)},s=function(t){return/(^([0-9]{3,4}-)?[0-9]{7,8}$)|(^1[3,4,5,6,7,8,9][0-9]{9}$)/.test(t.toString())},l=function(t){return/^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(t.toString())},u=function(t){return/\d/.test(t)},c=function(t){return/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(t)},m=function(t){return/^(([1-9]?[0-9])|([1-9]?[0-9]\.[1-9])|([1-9]?[1-9]\.[0]))$/.test(t)},d=function(t){return/^(([0-9])|([0-9]\.\d{1,3})|([1-9]\.[0]))$/.test(t)},p=function(t){return/^-?(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(t)},y=function(t){return/^[0-9]+$/.test(t)},f=function(t){return/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1}|[1-9][0-9]*\.\d{1})))$/.test(t)},h=function(t){return/^[a-zA-Z0-9]+$/.test(t)}}}]);