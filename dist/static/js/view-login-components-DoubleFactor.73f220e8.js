(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-login-components-DoubleFactor"],{"1d61":function(t,e,r){},a76d:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{attrs:{calss:"ps-dialog-doublefactor"}},[e("el-dialog",{attrs:{"custom-class":"ps-dialog "+t.customClass,title:t.title,visible:t.visible,width:t.width,top:t.top,"close-on-click-modal":!1,"destroy-on-close":!0,"close-on-press-escape":!1,"show-close":!1,center:t.center},on:{"update:visible":function(e){t.visible=e},closed:t.handleClose}},[e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"content"},[e("el-form",{ref:"doubleFormRef",staticClass:"form",attrs:{model:t.checkFormData,rules:t.rules,"label-width":"0"}},[e("el-form-item",{staticClass:"phone",attrs:{prop:"phone"}},[t._v(" 手机号: "+t._s(t.userInfo.mobile)+" ")]),e("el-form-item",{staticClass:"phone-code",attrs:{prop:"smsCode"}},[e("verification-code",{attrs:{sendAuthCode:t.sendAuthCode,disabled:t.sendCodeDisabled,"reset-handle":t.resetHandle},on:{click:t.getPhoneCode},model:{value:t.checkFormData.smsCode,callback:function(e){t.$set(t.checkFormData,"smsCode",e)},expression:"checkFormData.smsCode"}})],1)],1)],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"check-btn",attrs:{disabled:t.isLoading,type:"primary"},on:{click:t.clickConfirmHandle}},[t._v(t._s(t.confirmText))]),e("el-button",{staticClass:"ps-warn-text",staticStyle:{"margin-left":"0"},attrs:{disabled:t.isLoading,type:"text"},on:{click:t.layoutOutHandle}},[t._v("取 消")])],1)]),e("dialog-message",{attrs:{width:"450px",title:t.messageTitle,"show-close":!1,message:t.messageContent,"confirm-text":t.massageConfirmText,show:t.showDialog},on:{"update:show":function(e){t.showDialog=e},close:function(e){return t.closeTimeDialogHandle("close")},confirm:t.closeTimeDialogHandle}})],1)},o=[],i=r("ed08");r("b2e5");function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},c=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function h(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{h({},"")}catch(t){h=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var i=e&&e.prototype instanceof w?e:w,a=Object.create(i.prototype),s=new H(n||[]);return o(a,"_invoke",{value:O(t,r,s)}),a}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var m="suspendedStart",p="suspendedYield",g="executing",y="completed",v={};function w(){}function b(){}function C(){}var x={};h(x,c,(function(){return this}));var L=Object.getPrototypeOf,k=L&&L(L(A([])));k&&k!==r&&n.call(k,c)&&(x=k);var E=C.prototype=w.prototype=Object.create(x);function D(t){["next","throw","return"].forEach((function(e){h(t,e,(function(t){return this._invoke(e,t)}))}))}function T(t,e){function r(o,i,s,c){var l=d(t[o],t,i);if("throw"!==l.type){var u=l.arg,h=u.value;return h&&"object"==a(h)&&n.call(h,"__await")?e.resolve(h.__await).then((function(t){r("next",t,s,c)}),(function(t){r("throw",t,s,c)})):e.resolve(h).then((function(t){u.value=t,s(u)}),(function(t){return r("throw",t,s,c)}))}c(l.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function O(e,r,n){var o=m;return function(i,a){if(o===g)throw Error("Generator is already running");if(o===y){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var s=n.delegate;if(s){var c=_(s,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===m)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=g;var l=d(e,r,n);if("normal"===l.type){if(o=n.done?y:p,l.arg===v)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(o=y,n.method="throw",n.arg=l.arg)}}}function _(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,_(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=d(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function S(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function H(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(S,this),this.reset(!0)}function A(e){if(e||""===e){var r=e[c];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(a(e)+" is not iterable")}return b.prototype=C,o(E,"constructor",{value:C,configurable:!0}),o(C,"constructor",{value:b,configurable:!0}),b.displayName=h(C,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,C):(t.__proto__=C,h(t,u,"GeneratorFunction")),t.prototype=Object.create(E),t},e.awrap=function(t){return{__await:t}},D(T.prototype),h(T.prototype,l,(function(){return this})),e.AsyncIterator=T,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new T(f(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},D(E),h(E,u,"Generator"),h(E,c,(function(){return this})),h(E,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=A,H.prototype={constructor:H,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return s.type="throw",s.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),l=n.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),j(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;j(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:A(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function c(t,e){return d(t)||f(t,e)||u(t,e)||l()}function l(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(t,e){if(t){if("string"==typeof t)return h(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?h(t,e):void 0}}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function f(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,s=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(s.push(n.value),s.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return s}}function d(t){if(Array.isArray(t))return t}function m(t,e,r,n,o,i,a){try{var s=t[i](a),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,o)}function p(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){m(i,n,o,a,s,"next",t)}function s(t){m(i,n,o,a,s,"throw",t)}a(void 0)}))}}var g={name:"DoubleFactor",props:{title:{type:String,default:"验证登录"},width:{type:String,default:"330px"},top:{type:String,default:"15vh"},confirmText:{type:String,default:"验 证"},customClass:{type:String,default:function(){return"ps-dialog-doublefactor"}},center:Boolean,userInfo:{type:Object}},data:function(){return{isLoading:!1,visible:!1,sendAuthCode:!0,sendCodeDisabled:!1,checkFormData:{phone:"",smsCode:""},rules:{smsCode:[{required:!0,message:"请输入验证码",trigger:"change"}]},showDialog:!1,countDownHandle:null,messageType:"",messageTitle:"",messageContent:"",massageConfirmText:""}},computed:{},watch:{},created:function(){},mounted:function(){this.visible=!("1"===Object(i["x"])("CHECKDOUBLEFACTOR")),this.userInfo.mobile||(this.visible=!1),document.addEventListener("keydown",this.enterKeydowHandler)},methods:{enterKeydowHandler:function(t){13===t.keyCode&&this.clickConfirmHandle()},getPhoneCode:function(){var t=this;return p(s().mark((function e(){var r,n,o,a;return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(i["Z"])(t.$apis.apiBackgroundVerificationCodeAutoPost());case 2:if(r=e.sent,n=c(r,2),o=n[0],a=n[1],t.isLoading=!1,!o){e.next=10;break}return t.$message.error(o.message),e.abrupt("return");case 10:0===a.code?(t.sendAuthCode=!1,t.$message.success("发送成功")):t.$message.error(a.msg);case 11:case"end":return e.stop()}}),e)})))()},resetHandle:function(t){this.sendAuthCode=!0},clickConfirmHandle:function(){var t=this;this.$refs.doubleFormRef.validate((function(e){e&&t.checkSmsCode()}))},checkSmsCode:function(){var t=this;return p(s().mark((function e(){var r,n,o,a;return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.isLoading){e.next=2;break}return e.abrupt("return");case 2:return t.isLoading=!0,e.next=5,Object(i["Z"])(t.$apis.apiBackgroundCheckVerificationCodePost({sms_code:t.checkFormData.smsCode}));case 5:if(r=e.sent,n=c(r,2),o=n[0],a=n[1],t.isLoading=!1,!o){e.next=13;break}return t.$message.error(o.message),e.abrupt("return");case 13:0===a.code?(t.visible=!1,Object(i["U"])("CHECKDOUBLEFACTOR","1"),t.$message.success("验证成功"),t.showChangePwdHandle(),t.$emit("doubleConfirm")):t.$message.error(a.msg);case 14:case"end":return e.stop()}}),e)})))()},clickCancleHandle:function(){this.visible=!1,this.$emit("cancel")},handleClose:function(t){this.isLoading=!1,this.$emit("close")},countDown:function(){var t=Number(Object(i["x"])("REQUESTTIME")),e=(new Date).getTime()-t;if(e>18e5&&!this.showDialog)return clearTimeout(this.countDownHandle),this.messageType="1",this.massageConfirmText="重新登录",this.messageTitle="确定登出",this.messageContent="由于长时间没有操作，登录已过期，请重新登录。",void(this.showDialog=!0);this.countDownHandle=setTimeout(this.countDown,1e3)},closeTimeDialogHandle:function(t){this.showDialog=!1,this.layoutOutHandle()},layoutOutHandle:function(t){this.visible=!1,this.$store.dispatch("user/logout"),this.$emit("doubleCancel")},showChangePwdHandle:function(){if(this.userInfo.last_change_pwd_time&&this.userInfo.is_expire_change_pwd){var t=(new Date).getTime()-new Date(this.userInfo.last_change_pwd_time.replace(new RegExp(/-/gm),"/")).getTime();if(t<=7776e6){if(t>=75168e5){this.messageType="2",this.massageConfirmText="修改密码",this.messageTitle="提示";var e=parseInt(90-Math.floor(t/864e5));this.messageContent=e?"您的登录密码".concat(e,"天后即将过期，为了不影响您正常使用，建议及时修改。"):"您的登录密码1天后即将过期，为了不影响您正常使用，建议及时修改。","1"!==Object(i["x"])("ISEXPIRECHANGEPWD")&&(this.showDialog=!0)}}else this.messageType="3",this.massageConfirmText="确 定",this.messageContent="帐号失效，无法登陆",this.showDialog=!0}}},beforeDestroy:function(){document.removeEventListener("keydown",this.enterKeydowHandler),this.countDownHandle&&clearTimeout(this.countDownHandle)}},y=g,v=(r("e6b5"),r("2877")),w=Object(v["a"])(y,n,o,!1,null,null,null);e["default"]=w.exports},e6b5:function(t,e,r){"use strict";r("1d61")}}]);