(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-supervision-and-management-canteen-management","view-merchant-supervision-and-management-canteen-management-components-canteenInfo","view-merchant-supervision-and-management-canteen-management-components-constants"],{"0438":function(e,t,i){},"058a":function(e,t,i){"use strict";i.r(t),i.d(t,"businessLicenseForm",(function(){return r})),i.d(t,"businessLicenseFormList",(function(){return a})),i.d(t,"cateringServiceLicenseForm",(function(){return n})),i.d(t,"cateringServiceLicenseList",(function(){return s})),i.d(t,"foodBusinessLicenseForm",(function(){return o})),i.d(t,"foodBusinessLicenseList",(function(){return l})),i.d(t,"sanitaryPermitForm",(function(){return c})),i.d(t,"sanitaryPermitList",(function(){return p})),i.d(t,"fireSafetyCertificateForm",(function(){return u})),i.d(t,"fireSafetyCertificateList",(function(){return f})),i.d(t,"taxRegistrationCertificateForm",(function(){return d})),i.d(t,"taxRegistrationCertificateList",(function(){return g}));var r={creditCode:"",name:"",type:"",businessScope:"",registrationDate:"",img:""},a=[{prop:"creditCode",label:"统一社会信用代码",rule:[{required:!0,message:"请输入统一社会信用代码",trigger:["change","blur"]}],type:"input"},{prop:"name",label:"名称",rule:[{required:!0,message:"请输入名称",trigger:["change","blur"]}],type:"input"},{prop:"type",label:"类型",rule:[{required:!0,message:"请输入类型",trigger:["change","blur"]}],type:"input"},{prop:"businessScope",label:"经营范围",rule:[{required:!0,message:"请输入经营范围",trigger:["change","blur"]}],type:"input",inputType:"textarea"},{prop:"registrationDate",label:"注册日期",rule:[{required:!0,message:"请输入注册日期",trigger:["change","blur"]}],type:"date"},{prop:"img",label:"资质照片",rule:[{required:!0,message:"请上传资质照片",trigger:["change","blur"]}],type:"img"}],n={name:"",address:"",type:"",remark:"",expirationDate:null,img:""},s=[{prop:"name",label:"单位名称",rule:[{required:!0,message:"请输入单位名称",trigger:["change","blur"]}],type:"input"},{prop:"address",label:"地址",rule:[{required:!0,message:"请输入地址",trigger:["change","blur"]}],type:"input"},{prop:"type",label:"类型",rule:[{required:!0,message:"请输入类型",trigger:["change","blur"]}],type:"input"},{prop:"remark",label:"备注",rule:[{required:!0,message:"请输入备注",trigger:["change","blur"]}],type:"input",inputType:"textarea"},{prop:"expirationDate",label:"有效期限",rule:[{required:!0,message:"请输入有效期限",trigger:["change","blur"]}],type:"dateRange"},{prop:"img",label:"资质照片",rule:[{required:!0,message:"请上传资质照片",trigger:["change","blur"]}],type:"img"}],o={name:"",creditCode:"",legalRepresentative:"",address:"",businessPremises:"",mainFormOfBusiness:"",businessProject:"",licenseNumber:"",tipOffTelephone:"",issuingAuthority:"",expirationDate:[],img:""},l=[{prop:"name",label:"经营者名称",rule:[{required:!0,message:"请输入经营者名称",trigger:["change","blur"]}],type:"input"},{prop:"creditCode",label:"统一社会信用代码",rule:[{required:!0,message:"请输入统一社会信用代码",trigger:["change","blur"]}],type:"input"},{prop:"legalRepresentative",label:"法定代表人",rule:[{required:!0,message:"请输入法定代表人",trigger:["change","blur"]}],type:"input"},{prop:"address",label:"住所",rule:[{required:!0,message:"请输入住所",trigger:["change","blur"]}],type:"input"},{prop:"businessPremises",label:"经营场所",rule:[{required:!0,message:"请输入经营场所",trigger:["change","blur"]}],type:"input"},{prop:"mainFormOfBusiness",label:"主体业态",rule:[{required:!0,message:"请输入主体业态",trigger:["change","blur"]}],type:"input"},{prop:"businessProject",label:"经营项目",rule:[{required:!0,message:"请输入经营项目",trigger:["change","blur"]}],type:"input"},{prop:"licenseNumber",label:"许可证编号",rule:[{required:!0,message:"请输入许可证编号",trigger:["change","blur"]}],type:"input"},{prop:"tipOffTelephone",label:"举报电话",rule:[{required:!0,message:"请输入举报电话",trigger:["change","blur"]}],type:"input"},{prop:"issuingAuthority",label:"发证机关",rule:[{required:!0,message:"请输入发证机关",trigger:["change","blur"]}],type:"input"},{prop:"expirationDate",label:"有效期限",rule:[{required:!0,message:"请输入有效期限",trigger:["change","blur"]}],type:"dateRange"},{prop:"img",label:"资质照片",rule:[{required:!0,message:"请上传资质照片",trigger:["change","blur"]}],type:"img"}],c={name:"",personInCharge:"",address:"",permittedItem:"",issuingAuthority:"",expirationDate:[],img:""},p=[{prop:"name",label:"单位名称",rule:[{required:!0,message:"请输入单位名称",trigger:["change","blur"]}],type:"input"},{prop:"personInCharge",label:"负责人",rule:[{required:!0,message:"请输入负责人",trigger:["change","blur"]}],type:"input"},{prop:"address",label:"地址",rule:[{required:!0,message:"请输入地址",trigger:["change","blur"]}],type:"input"},{prop:"permittedItem",label:"许可项目",rule:[{required:!0,message:"请输入许可项目",trigger:["change","blur"]}],type:"input"},{prop:"issuingAuthority",label:"发证机关",rule:[{required:!0,message:"请输入发证机关",trigger:["change","blur"]}],type:"input"},{prop:"expirationDate",label:"有效期限",rule:[{required:!0,message:"请输入有效期限",trigger:["change","blur"]}],type:"dateRange"},{prop:"img",label:"资质照片",rule:[{required:!0,message:"请上传资质照片",trigger:["change","blur"]}],type:"img"}],u={placeName:"",address:"",buildingName:"",floorArea:"",personResponsibleForSafety:"",natureOfUse:"",img:""},f=[{prop:"placeName",label:"场所名称",rule:[{required:!0,message:"请输入场所名称",trigger:["change","blur"]}],type:"input"},{prop:"address",label:"地址",rule:[{required:!0,message:"请输入地址",trigger:["change","blur"]}],type:"input"},{prop:"buildingName",label:"场所所在建筑名称",rule:[{required:!0,message:"请输入场所所在建筑名称",trigger:["change","blur"]}],type:"input"},{prop:"floorArea",label:"场所建筑面积",rule:[{required:!0,message:"请输入场所建筑面积",trigger:["change","blur"]}],type:"input"},{prop:"personResponsibleForSafety",label:"消防安全责任人",rule:[{required:!0,message:"请输入消防安全责任人",trigger:["change","blur"]}],type:"input"},{prop:"natureOfUse",label:"使用性质",rule:[{required:!0,message:"请输入使用性质",trigger:["change","blur"]}],type:"input"},{prop:"img",label:"资质照片",rule:[{required:!0,message:"请上传资质照片",trigger:["change","blur"]}],type:"img"}],d={taxpayerName:"",legalRepresentative:"",address:"",typeOfRegistration:"",businessScope:"",withholdingObligation:"",img:""},g=[{prop:"taxpayerName",label:"纳税人",rule:[{required:!0,message:"请输入纳税人",trigger:["change","blur"]}],type:"input"},{prop:"legalRepresentative",label:"法定代表人（负责人）",rule:[{required:!0,message:"请输入法定代表人（负责人）",trigger:["change","blur"]}],type:"input"},{prop:"address",label:"地址",rule:[{required:!0,message:"请输入地址",trigger:["change","blur"]}],type:"input"},{prop:"typeOfRegistration",label:"登记注册类型",rule:[{required:!0,message:"请输入登记注册类型",trigger:["change","blur"]}],type:"input"},{prop:"businessScope",label:"经营范围",rule:[{required:!0,message:"请输入经营范围",trigger:["change","blur"]}],type:"input"},{prop:"withholdingObligation",label:"扣缴义务",rule:[{required:!0,message:"请输入扣缴义务",trigger:["change","blur"]}],type:"input"},{prop:"img",label:"资质照片",rule:[{required:!0,message:"请上传资质照片",trigger:["change","blur"]}],type:"img"}]},"0972":function(e,t,i){"use strict";i("0438")},49318:function(e,t,i){},"5dd9":function(e,t,i){"use strict";i.r(t);var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"info-show p-20"},[t("el-form",{ref:"infoShowFormRef",attrs:{model:e.infoShowForm,"label-position":"top"}},[t("el-form-item",{attrs:{prop:"level"},scopedSlots:e._u([{key:"label",fn:function(){return[t("div",{staticClass:"ps-flex-align-c flex-align-c"},[t("div",{staticClass:"m-r-20"},[e._v("餐饮服务食品安全等级公示")]),t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_fund_supervision.publicity_info.modify_security_level"],expression:"['background_fund_supervision.publicity_info.modify_security_level']"}],staticClass:"ps-origin-text",attrs:{type:"text"},on:{click:function(t){return e.showDrawer("level",e.infoShowForm.level)}}},[e._v("修改")])],1)]},proxy:!0}])},[t("div",{staticClass:"level-show p-l-20"},[t("div",{staticClass:"level-show-left m-r-40"},[t("svg-icon",{directives:[{name:"show",rawName:"v-show",value:e.infoShowForm.level,expression:"infoShowForm.level"}],staticStyle:{width:"128px",height:"128px"},attrs:{"icon-class":e.infoShowForm.level}})],1),t("div",{staticClass:"level-show-right"},[t("div",{staticClass:"level-show-right-tips m-r-20"},[t("svg-icon",{staticClass:"m-b-10 m-r-10",staticStyle:{width:"36px",height:"36px"},attrs:{"icon-class":"excellent"}}),t("span",[e._v("A级：优秀")])],1),t("div",{staticClass:"level-show-right-tips m-r-20"},[t("svg-icon",{staticClass:"m-b-10 m-r-10",staticStyle:{width:"36px",height:"36px"},attrs:{"icon-class":"good"}}),t("span",[e._v("B级：良好")])],1),t("div",{staticClass:"level-show-right-tips m-r-20"},[t("svg-icon",{staticClass:"m-b-10 m-r-10",staticStyle:{width:"36px",height:"36px"},attrs:{"icon-class":"average"}}),t("span",[e._v("C级：一般")])],1)])])]),t("el-form-item",{attrs:{prop:"type"},scopedSlots:e._u([{key:"label",fn:function(){return[t("div",{staticClass:"ps-flex-align-c flex-align-c"},[t("div",{staticClass:"m-r-20"},[e._v("食堂类型")]),t("el-button",{staticClass:"ps-origin-text",attrs:{type:"text"},on:{click:function(t){return e.showDrawer("type",e.infoShowForm.canteen_type)}}},[e._v("修改")])],1)]},proxy:!0}])},[t("div",{},[e._v(" "+e._s(e.infoShowForm.canteen_type_alias)+" ")])]),t("el-form-item",{attrs:{prop:"plan"},scopedSlots:e._u([{key:"label",fn:function(){return[t("div",{staticClass:"ps-flex-align-c flex-align-c"},[t("div",{staticClass:"m-r-20"},[e._v("营养改善计划")]),t("el-button",{staticClass:"ps-origin-text",attrs:{type:"text"},on:{click:function(t){return e.showDrawer("plan",e.infoShowForm.plan)}}},[e._v("修改")])],1)]},proxy:!0}])},[t("div",{},[e._v(" "+e._s(e.computedPlan(e.infoShowForm.plan))+" ")])]),t("el-form-item",{attrs:{prop:"tableData"},scopedSlots:e._u([{key:"label",fn:function(){return[t("div",{staticClass:"ps-flex-align-c flex-align-c"},[t("div",{staticClass:"m-r-20"},[e._v("资质公示")]),t("el-button",{staticClass:"ps-origin-text",attrs:{type:"text"},on:{click:function(t){return e.showInfoDrawer("add")}}},[e._v("新增")])],1)]},proxy:!0}])},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],attrs:{data:e.infoShowForm.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},e._l(e.tableSetting,(function(i,r){return t("table-column",{key:r,attrs:{col:i},scopedSlots:e._u([{key:"img",fn:function(e){var i=e.row;return[t("el-image",{staticStyle:{width:"200px",height:"100px"},attrs:{src:i.image,"preview-src-list":[i.image],fit:"contain"}})]}},{key:"timeRange",fn:function(i){var r=i.row;return["CY"!==r.qualification_type&&"SP"!==r.qualification_type&&"WS"!==r.qualification_type?t("div",[e._v(" "+e._s("永久有效")+" ")]):t("div",[e._v(" "+e._s(e.computedDate(r.extra.expirationDate[0]))+" 至 "+e._s(e.computedDate(r.extra.expirationDate[1]))+" ")])]}},{key:"faceUrl",fn:function(e){var i=e.row;return[t("el-image",{staticStyle:{width:"100px",height:"100px"},attrs:{src:i.face_url,"preview-src-list":[i.face_url],fit:"fill"}})]}},{key:"operation",fn:function(i){var r=i.row;return[t("el-button",{staticClass:"ps-origin-text",attrs:{type:"text"},on:{click:function(t){return e.showInfoDrawer("detail",r)}}},[e._v("详情")]),t("el-button",{staticClass:"ps-origin-text",attrs:{type:"text"},on:{click:function(t){return e.showInfoDrawer("edit",r)}}},[e._v("编辑")]),t("el-button",{staticClass:"ps-origin-text",attrs:{type:"text"},on:{click:function(t){return e.deleteInfo(r)}}},[e._v("删除")])]}}],null,!0)})})),1)],1)],1),t("div",{staticClass:"ps-el-drawer"},[t("el-drawer",{attrs:{title:"修改食品安全等级",visible:e.foodSafetyDrawerShow,"show-close":!1,size:"40%"}},[t("div",{staticClass:"p-20"},[t("el-form",{ref:"foodSafetyFormRef",attrs:{model:e.foodSafetyForm,"label-width":"80px","label-position":"right"}},[t("el-form-item",{attrs:{label:"安全等级",prop:"level",rules:[{required:!0,message:"请选择安全等级",trigger:["change","blur"]}]}},[t("el-select",{attrs:{placeholder:"请选择"},model:{value:e.foodSafetyForm.level,callback:function(t){e.$set(e.foodSafetyForm,"level",t)},expression:"foodSafetyForm.level"}},e._l(e.levelList,(function(e,i){return t("el-option",{key:i,attrs:{label:e.label,value:e.value}})})),1)],1)],1),t("div",{staticClass:"ps-el-drawer-footer"},[t("el-button",{staticClass:"w-100",attrs:{size:"small"},on:{click:function(t){return e.cancelHandle("level")}}},[e._v("取消")]),t("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:function(t){return e.saveHandle("level")}}},[e._v("保存")])],1)],1)]),t("el-drawer",{attrs:{title:"修改食堂类型",visible:e.canteenTypeDrawerShow,"show-close":!1,size:"40%"}},[t("div",{staticClass:"p-20"},[t("el-form",{ref:"canteenTypeFormRef",attrs:{model:e.canteenTypeForm,"label-width":"80px","label-position":"right"}},[t("el-form-item",{attrs:{label:"食堂类型",prop:"type",rules:[{required:!0,message:"请选择食堂类型",trigger:["change","blur"]}]}},[t("el-select",{attrs:{placeholder:"请选择"},model:{value:e.canteenTypeForm.type,callback:function(t){e.$set(e.canteenTypeForm,"type",t)},expression:"canteenTypeForm.type"}},e._l(e.canteenTypeList,(function(e,i){return t("el-option",{key:i,attrs:{label:e.label,value:e.value}})})),1)],1)],1),t("div",{staticClass:"ps-el-drawer-footer"},[t("el-button",{staticClass:"w-100",attrs:{size:"small"},on:{click:function(t){return e.cancelHandle("type")}}},[e._v("取消")]),t("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:function(t){return e.saveHandle("type")}}},[e._v("保存")])],1)],1)]),t("el-drawer",{attrs:{title:"修改营养改善计划",visible:e.planDrawerShow,"show-close":!1,size:"40%"}},[t("div",{staticClass:"p-20"},[t("el-form",{ref:"planFormRef",attrs:{model:e.planForm,"label-width":"120px","label-position":"right"}},[t("el-form-item",{attrs:{label:"营养改善计划",prop:"type",rules:[{required:!0,message:"请选择对应的状态",trigger:["change","blur"]}]}},[t("el-select",{attrs:{placeholder:"请选择"},model:{value:e.planForm.type,callback:function(t){e.$set(e.planForm,"type",t)},expression:"planForm.type"}},e._l(e.planList,(function(e,i){return t("el-option",{key:i,attrs:{label:e.label,value:e.value}})})),1)],1)],1),t("div",{staticClass:"ps-el-drawer-footer"},[t("el-button",{staticClass:"w-100",attrs:{size:"small"},on:{click:function(t){return e.cancelHandle("plan")}}},[e._v("取消")]),t("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:function(t){return e.saveHandle("plan")}}},[e._v("保存")])],1)],1)]),t("el-drawer",{attrs:{title:e.computedTitle(e.selectType),visible:e.infoDrawerShow,"show-close":!1,size:"35%"}},[t("div",{staticClass:"p-20"},[t("el-form",{ref:"infoFormRef",attrs:{model:e.infoForm,"label-width":"auto","label-position":"right"}},[t("el-form-item",{attrs:{label:"资质类型",prop:"typeOfQualification",rules:[{required:!0,message:"请选择相应的资质类型",trigger:["change","blur"]}]}},[t("el-select",{staticClass:"w-350",attrs:{placeholder:"请选择",disabled:"detail"===e.selectType||"edit"===e.selectType},on:{change:function(t){return e.changeTypeOfQualification(e.infoForm.typeOfQualification,!0)}},model:{value:e.infoForm.typeOfQualification,callback:function(t){e.$set(e.infoForm,"typeOfQualification",t)},expression:"infoForm.typeOfQualification"}},e._l(e.typeOfQualificationList,(function(e,i){return t("el-option",{key:i,attrs:{label:e.label,value:e.value}})})),1)],1),e._l(e.infoFormList,(function(i,r){return t("div",{key:r},["input"===i.type?t("el-form-item",{attrs:{label:i.label,prop:i.prop,rules:i.rule}},[t("el-input",{staticClass:"w-350",attrs:{placeholder:"请输入",disabled:"detail"===e.selectType},model:{value:e.infoForm["".concat(i.prop)],callback:function(t){e.$set(e.infoForm,"".concat(i.prop),t)},expression:"infoForm[`${item.prop}`]"}})],1):e._e(),"date"===i.type?t("el-form-item",{attrs:{label:i.label,prop:i.prop,rules:i.rule}},[t("el-date-picker",{key:i.type,staticClass:"w-350",attrs:{disabled:"detail"===e.selectType,"append-to-body":"",format:"yyyy年MM月dd日","value-format":"yyyy-MM-dd",type:"date",placeholder:"选择日期"},model:{value:e.infoForm["".concat(i.prop)],callback:function(t){e.$set(e.infoForm,"".concat(i.prop),t)},expression:"infoForm[`${item.prop}`]"}})],1):e._e(),"dateRange"===i.type?t("el-form-item",{attrs:{label:i.label,prop:i.prop,rules:i.rule}},[t("el-date-picker",{key:i.type,staticClass:"w-350",attrs:{disabled:"detail"===e.selectType,"append-to-body":"",format:"yyyy年MM月dd日","value-format":"yyyy-MM-dd",type:"daterange","range-separator":"~","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.infoForm["".concat(i.prop)],callback:function(t){e.$set(e.infoForm,"".concat(i.prop),t)},expression:"infoForm[`${item.prop}`]"}})],1):e._e(),"img"===i.type?t("el-form-item",{attrs:{label:i.label,prop:i.prop,rules:i.rule}},[t("div",{staticClass:"certification-info-show-tips"},[e._v(" 图片最大不超过2MB，仅支持jpg,png格式 ")]),t("el-upload",{directives:[{name:"loading",rawName:"v-loading",value:e.uploading,expression:"uploading"}],ref:"fileUpload",refInFor:!0,staticClass:"upload-w",attrs:{disabled:"detail"===e.selectType,"element-loading-text":"上传中",action:e.serverUrl,"file-list":e.fileLists,"on-success":e.uploadSuccessForList,"before-upload":e.beforeImgUpload,limit:1,multiple:!1,"show-file-list":!1,headers:e.headersOpts,accept:".jpeg,.jpg,.png,.bmp"}},[e.infoForm["".concat(i.prop)]?t("img",{staticClass:"certification-info-show-img",attrs:{src:e.infoForm["".concat(i.prop)]}}):t("div",{staticStyle:{width:"200px",height:"100px",border:"1px dashed #C0C4CC","text-align":"center","line-height":"100px"}},[t("i",{staticClass:"el-icon-plus"})])])],1):e._e()],1)}))],2),t("div",{staticClass:"ps-el-drawer-footer"},["detail"!==e.selectType?t("el-button",{staticClass:"w-100",attrs:{size:"small"},on:{click:function(t){return e.cancelHandle("info")}}},[e._v("取消")]):e._e(),"detail"!==e.selectType?t("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:function(t){return e.saveHandle("info")}}},[e._v("保存")]):e._e(),"detail"===e.selectType?t("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:function(t){return e.cancelHandle("info")}}},[e._v("关闭")]):e._e()],1)],1)])],1)],1)},a=[],n=i("ed08"),s=i("058a"),o=i("5a0c"),l=i.n(o);function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function p(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,r)}return i}function u(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?p(Object(i),!0).forEach((function(t){f(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):p(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function f(e,t,i){return(t=d(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function d(e){var t=g(e,"string");return"symbol"==c(t)?t:t+""}function g(e,t){if("object"!=c(e)||!e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var r=i.call(e,t||"default");if("object"!=c(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var m={data:function(){return{isLoading:!1,infoShowForm:{level:"",canteen_type:"",canteen_type_alias:"",plan:!1,tableData:[]},tableSetting:[{label:"图片",key:"image",align:"center",type:"slot",slotName:"img"},{label:"资质类型",key:"qualification_type_alias",align:"center"},{label:"有效期",key:"time",align:"center",type:"slot",slotName:"timeRange"},{label:"创建时间",key:"create_time",align:"center"},{label:"修改时间",key:"update_time",align:"center"},{label:"操作人",key:"operator_name",align:"center"},{label:"操作",key:"operation",type:"slot",slotName:"operation"}],foodSafetyDrawerShow:!1,foodSafetyForm:{level:""},levelList:[{label:"A级：优秀",value:"excellent"},{label:"B级：良好",value:"good"},{label:"C级：一般",value:"average"}],canteenTypeDrawerShow:!1,canteenTypeForm:{type:"chengbao"},canteenTypeList:[{label:"承包/托管经营食堂",value:"chengbao"},{label:"自营食堂",value:"ziying"}],planDrawerShow:!1,planForm:{type:!1},planList:[{label:"未参与",value:!1},{label:"已参与",value:!0}],uploading:!1,serverUrl:"/api/background/file/upload",fileLists:[],headersOpts:{TOKEN:Object(n["B"])()},infoDrawerShow:!1,selectType:"",selectId:"",typeOfQualificationList:[{label:"营业执照",value:"YY"},{label:"餐饮服务许可证",value:"CY"},{label:"食品经营许可证",value:"SP"},{label:"卫生许可证",value:"WS"},{label:"消费安全证书",value:"XF"},{label:"税务登记证",value:"SW"}],infoFormList:[],infoForm:{typeOfQualification:""}}},computed:{computedPlan:function(){return function(e){return e?"已参与":"未参与"}},computedTitle:function(){return function(e){var t="";switch(e){case"add":t="新增资质";break;case"edit":t="编辑资质";break;case"detail":t="资质详情";break}return t}},computedDate:function(){return function(e){return l()(e).format("YYYY年MM月DD日")}}},created:function(){this.initLoad()},methods:{initLoad:function(){this.getCanteenInfo(),this.getQualificationPublicity()},getCanteenInfo:function(){var e=this;this.infoShowForm.level="excellent",this.infoShowForm.canteen_type="",this.infoShowForm.canteen_type_alias="",this.infoShowForm.plan=!1,this.$apis.apiBackgroundFundSupervisionPublicityInfoGetCanteenInfoPost().then((function(t){0===t.code?(e.infoShowForm.level=t.data.security_level,e.infoShowForm.canteen_type=t.data.canteen_type,e.infoShowForm.canteen_type_alias=t.data.canteen_type_alias,e.infoShowForm.plan=t.data.plan):e.$message.error(t.msg)}))},getQualificationPublicity:function(){var e=this;this.isLoading=!0,this.$apis.apiBackgroundFundSupervisionPublicityInfoGetQualificationInfoPost().then((function(t){e.isLoading=!1,0===t.code?e.infoShowForm.tableData=Object(n["f"])(t.data):e.$message.error(t.msg)}))},uploadSuccessForList:function(e,t,i){this.uploading=!1,e&&0===e.code?(this.fileLists=[],this.infoForm.img=e.data.public_url):(this.infoForm.img="",this.$message.error(e.msg))},beforeImgUpload:function(e){var t=[".jpeg",".jpg",".png",".bmp"],i=e.size/1024/1024<=2;return t.includes(Object(n["A"])(e.name))?i?void(this.uploading=!0):(this.$message.error("上传图片大小不能超过 2MB!"),!1):(this.$message.error("上传图片只能是 JPG 格式或者PNG格式!"),!1)},showInfoDrawer:function(e,t){var i=this;this.selectType=e,this.selectId=t&&t.id?t.id:"","add"!==e&&(this.changeTypeOfQualification(t.extra.typeOfQualification,!1),this.$nextTick((function(){i.infoForm=Object(n["f"])(t.extra)}))),this.infoDrawerShow=!0},changeTypeOfQualification:function(e,t){switch(this.infoForm={typeOfQualification:e},e){case"YY":this.infoForm=Object(n["f"])(u(u({},this.infoForm),s["businessLicenseForm"])),this.infoFormList=Object(n["f"])(s["businessLicenseFormList"]);break;case"CY":this.infoForm=Object(n["f"])(u(u({},this.infoForm),s["cateringServiceLicenseForm"])),this.infoFormList=Object(n["f"])(s["cateringServiceLicenseList"]);break;case"SP":this.infoForm=Object(n["f"])(u(u({},this.infoForm),s["foodBusinessLicenseForm"])),this.infoFormList=Object(n["f"])(s["foodBusinessLicenseList"]);break;case"WS":this.infoForm=Object(n["f"])(u(u({},this.infoForm),s["sanitaryPermitForm"])),this.infoFormList=Object(n["f"])(s["sanitaryPermitList"]);break;case"XF":this.infoForm=Object(n["f"])(u(u({},this.infoForm),s["fireSafetyCertificateForm"])),this.infoFormList=Object(n["f"])(s["fireSafetyCertificateList"]);break;case"SW":this.infoForm=Object(n["f"])(u(u({},this.infoForm),s["taxRegistrationCertificateForm"])),this.infoFormList=Object(n["f"])(s["taxRegistrationCertificateList"]);break}t&&this.$refs.infoFormRef.clearValidate()},showDrawer:function(e,t){switch(e){case"level":this.foodSafetyForm.level=t,this.foodSafetyDrawerShow=!0;break;case"type":this.canteenTypeForm.type=t,this.canteenTypeDrawerShow=!0;break;case"plan":this.planForm.type=t,this.planDrawerShow=!0;break}},cancelHandle:function(e){switch(e){case"level":this.$refs.foodSafetyFormRef.resetFields(),this.foodSafetyDrawerShow=!1;break;case"type":this.$refs.canteenTypeFormRef.resetFields(),this.canteenTypeDrawerShow=!1;break;case"plan":this.$refs.planFormRef.resetFields(),this.planDrawerShow=!1;break;case"info":this.$refs.infoFormRef.resetFields(),this.infoDrawerShow=!1;break}},saveHandle:function(e){switch(e){case"level":this.changeLevel();break;case"type":this.changeType();break;case"plan":this.changePlan();break;case"info":this.addOrEditInfo();break}},changeLevel:function(){var e=this;this.$apis.apiBackgroundFundSupervisionPublicityInfoModifySecurityLevelPost({security_level:this.foodSafetyForm.level}).then((function(t){0===t.code?e.$message.success("修改成功"):e.$message.error(t.msg),e.$refs.foodSafetyFormRef.resetFields(),e.foodSafetyDrawerShow=!1,e.getCanteenInfo()}))},changeType:function(){var e=this;this.$apis.apiBackgroundFundSupervisionPublicityInfoModifyCanteenTypePost({canteen_type:this.canteenTypeForm.type}).then((function(t){0===t.code?e.$message.success("修改成功"):e.$message.error(t.msg),e.$refs.canteenTypeFormRef.resetFields(),e.canteenTypeDrawerShow=!1,e.getCanteenInfo()}))},changePlan:function(){var e=this;this.$apis.apiBackgroundFundSupervisionPublicityInfoModifyCanteenPlanPost({plan:this.planForm.type}).then((function(t){0===t.code?e.$message.success("修改成功"):e.$message.error(t.msg),e.$refs.planFormRef.resetFields(),e.planDrawerShow=!1,e.getCanteenInfo()}))},addOrEditInfo:function(){var e=this;this.$refs.infoFormRef.validate((function(t){if(t){var i={id:"edit"===e.selectType?e.selectId:void 0,qualification_type:e.infoForm.typeOfQualification,image:e.infoForm.img,extra:u({},e.infoForm)};"add"===e.selectType?e.addInfo(i):e.editInfo(i)}else e.$message.error("请检查表单内容是否正确")}))},addInfo:function(e){var t=this;this.$apis.apiBackgroundFundSupervisionPublicityInfoAddQualificationPost(e).then((function(e){0===e.code?t.$message.success("新增成功"):t.$message.error(e.msg),t.$refs.infoFormRef.resetFields(),t.infoDrawerShow=!1,t.getQualificationPublicity()}))},editInfo:function(e){var t=this;this.$apis.apiBackgroundFundSupervisionPublicityInfoModifyQualificationPost(e).then((function(e){0===e.code?t.$message.success("编辑成功"):t.$message.error(e.msg),t.$refs.infoFormRef.resetFields(),t.infoDrawerShow=!1,t.getQualificationPublicity()}))},deleteInfo:function(e){var t=this;this.$confirm("确定要删除".concat(e.qualification_type_alias,"的公示信息？删除后不可恢复，请谨慎操作。"),"提示",{distinguishCancelAndClose:!0,confirmButtonText:"确定",cancelButtonText:"取消"}).then((function(){t.deleteHandle(e)})).catch((function(i){t.$message.info("您已取消删除".concat(e.qualification_type_alias,"的公示信息"))}))},deleteHandle:function(e){var t=this;this.$apis.apiBackgroundFundSupervisionPublicityInfoDeleteQualificationPost({id:e.id}).then((function(e){0===e.code?(t.$message.success("删除成功"),t.getQualificationPublicity()):t.$message.error(e.msg)}))}}},b=m,y=(i("dd61"),i("2877")),h=Object(y["a"])(b,r,a,!1,null,"141745c4",null);t["default"]=h.exports},"84ea":function(e,t,i){"use strict";i.r(t);var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"public-information container-wrapper"},[t("div",{staticClass:"table-type"},[t("el-button",{class:["CanteenInfo"===e.tableType?"ps-origin-btn":""],on:{click:function(t){return e.changeTableType("CanteenInfo")}}},[e._v("食堂信息")]),t("el-button",{class:["SecurityInfo"===e.tableType?"ps-origin-btn":""],on:{click:function(t){return e.changeTableType("SecurityInfo")}}},[e._v("食安公示")])],1),t("div",{staticClass:"table-wrapper"},["CanteenInfo"===e.tableType?t("div",[t("canteen-info")],1):t("div",[t("security-info")],1)])])},a=[],n=i("5dd9"),s=i("7bae"),o={components:{canteenInfo:n["default"],securityInfo:s["default"]},data:function(){return{tableType:"CanteenInfo"}},methods:{changeTableType:function(e){this.tableType=e}}},l=o,c=(i("0972"),i("2877")),p=Object(c["a"])(l,r,a,!1,null,"1495a641",null);t["default"]=p.exports},dd61:function(e,t,i){"use strict";i("49318")}}]);