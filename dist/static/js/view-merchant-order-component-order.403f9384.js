(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-order-component-order"],{"5b80":function(t,e,i){"use strict";i.r(e),i.d(e,"RECENTSEVENTDAY",(function(){return u})),i.d(e,"PICKEROPTIONS",(function(){return d})),i.d(e,"PAYMENTSTATE",(function(){return l})),i.d(e,"PAYMENTWALLET",(function(){return x})),i.d(e,"MEALTYPE",(function(){return n})),i.d(e,"GETMEALTYPE",(function(){return v})),i.d(e,"PAYMENTTYPE",(function(){return E})),i.d(e,"PAYMENTMETHOD",(function(){return R})),i.d(e,"columns",(function(){return r})),i.d(e,"REFUNDSTATUS",(function(){return c})),i.d(e,"PAYMENTSTATUS",(function(){return o})),i.d(e,"REFUNDTYPE",(function(){return A})),i.d(e,"RECHAR_CHANNELARR",(function(){return P})),i.d(e,"RECHARGE_STATEARR",(function(){return D})),i.d(e,"RECHARGE_TYPEARR",(function(){return C})),i.d(e,"REFUND_CHANNELARR",(function(){return T})),i.d(e,"REFUND_STATEARR",(function(){return N})),i.d(e,"PAYMENTSTATUS_COM",(function(){return O}));var a=i("5a0c"),u=[a().subtract(7,"day").format("YYYY-MM-DD"),a().format("YYYY-MM-DD")],d={shortcuts:[{text:"最近一周",onClick:function(t){var e=new Date,i=new Date;i.setTime(i.getTime()-6048e5),t.$emit("pick",[i,e])}},{text:"最近一个月",onClick:function(t){var e=new Date,i=new Date;i.setTime(i.getTime()-2592e6),t.$emit("pick",[i,e])}},{text:"最近三个月",onClick:function(t){var e=new Date,i=new Date;i.setTime(i.getTime()-7776e6),t.$emit("pick",[i,e])}}]},l=[{id:1,text:"全部",value:""},{id:2,text:"待支付",value:"ORDER_PAYING"},{id:3,text:"支付成功",value:"ORDER_SUCCESS"},{id:4,text:"支付失败",value:"ORDER_FAILED"},{id:5,text:"交易冲正中",value:"ORDER_REVERSALING"},{id:6,text:"交易冲正",value:"ORDER_REVERSAL"},{id:7,text:"退款中",value:"ORDER_REFUNDING"},{id:8,text:"关闭(用户未支付)",value:"ORDER_CLOSE"},{id:9,text:"过期",value:"ORDER_TIME_OUT"},{id:10,text:"未知",value:"ORDER_UNKNOWN"}],x=[{id:1,text:"全部",value:""},{id:2,text:"朴食储值卡务钱包",value:"store"},{id:3,text:"农行电子账户钱包",value:"electronic"},{id:4,text:"补贴钱包",value:"subsidy"},{id:5,text:"优惠钱包",value:"discount"},{id:6,text:"第三方钱包",value:"other"}],n=[{id:1,text:"全部",value:""},{id:2,text:"早餐",value:"breakfast"},{id:3,text:"午餐",value:"lunch"},{id:4,text:"下午茶",value:"afternoon"},{id:5,text:"晚餐",value:"dinner"},{id:6,text:"夜宵",value:"supper"},{id:7,text:"凌晨餐",value:"morning"}],v=[{id:1,text:"全部",value:""},{id:2,text:"堂食",value:"on_scene"},{id:3,text:"食堂自提",value:"bale"},{id:4,text:"外卖配送",value:"waimai"},{id:5,text:"配送柜自取",value:"cupboard"}],E=[{id:1,text:"全部",value:""},{id:2,text:"储值钱包支付",value:"wallet"},{id:3,text:"电子钱包支付",value:"ewallet"},{id:4,text:"第三方钱包",value:"twallet"},{id:5,text:"授权代扣支付",value:"daikou"},{id:6,text:"数字人民币支付",value:"ermb"},{id:7,text:"JSAPI支付",value:"jsapi"},{id:8,text:"H5支付",value:"h5"},{id:9,text:"WAP支付",value:"wap"},{id:10,text:"小程序支付",value:"miniapp"},{id:11,text:"现金支付",value:"cash"},{id:12,text:"B扫C支付",value:"micropay"},{id:13,text:"C扫B支付",value:"scanpay"},{id:14,text:"刷卡支付",value:"cardpay"},{id:15,text:"刷脸支付",value:"facepay"},{id:16,text:"会员码支付",value:"facecode"},{id:17,text:"缴费方式支付",value:"jf"},{id:18,text:"快e付支付",value:"fastepay"}],R=[{id:1,text:"全部",value:""},{id:2,text:"朴食储值支付",value:"PushiPay"},{id:3,text:"一卡通-鑫澳康支付",value:"OCOMPAY"},{id:4,text:"一卡通-石药支付",value:"SHIYAOPAY"},{id:5,text:"农行支付",value:"ABCPay"},{id:6,text:"建行支付",value:"CCBPAY"},{id:7,text:"中行支付",value:"BOCPAY"},{id:8,text:"工行支付",value:"ICBCPAY"},{id:9,text:"美团支付",value:"MEITUANPAY"},{id:10,text:"收钱吧支付",value:"ShouqianbaPay"},{id:11,text:"微信支付",value:"WechatPay"},{id:13,text:"现金支付",value:"CashPay"}],r=[{id:1,label:"图片",column:"",width:"100px"},{id:2,label:"识别图片",column:"",width:"100px"},{id:3,label:"菜品名称",column:"",width:"100px"},{id:4,label:"销售价格",column:"",width:"100px"},{id:5,label:"数量",column:"",width:"100px"},{id:6,label:"重量",column:"",width:"100px"},{id:7,label:"消费金额",column:"",width:"100px"},{id:8,label:"营养详情",column:"detailChange",width:"100px"}],c=[{id:1,text:"全部",value:""},{id:2,text:"退款中",value:"ORDER_REFUNDING"},{id:3,text:"已退款",value:"ORDER_SUCCESS"}],o=[{id:1,text:"全部"},{id:2,text:"未结"},{id:3,text:"支付成功"},{id:4,text:"支付失败"},{id:5,text:"已部分退款"},{id:6,text:"已全额退款"}],A=[{id:1,label:"全部",value:""},{id:2,label:"全额退款",value:"ORDER_ALL_REFUND"},{id:3,label:"部分退款",value:"ORDER_PART_REFUND"}],P=[{id:1,text:"全部",value:""},{id:2,text:"微信支付",value:"WechatPay"},{id:3,text:"农行支付",value:"ABCPay"},{id:4,text:"现金支付",value:"CashPay"},{id:5,text:"收钱吧支付",value:"ShouqianbaPay"},{id:6,text:"建行支付",value:"CCBPAY"},{id:7,text:"中行支付",value:"BOCPAY"},{id:8,text:"工行支付",value:"ICBCPAY"},{id:9,text:"美团支付",value:"MEITUANPAY"},{id:10,text:"朴食储值支付",value:"PushiPay"},{id:11,text:"一卡通-鑫澳康支付",value:"OCOMPAY"},{id:12,text:"一卡通-石药支付",value:"SHIYAOPAY"},{id:13,text:"未知",value:"UNKNOWN"}],D=[{id:1,text:"全部",value:""},{id:2,text:"充值中",value:"ORDER_REVERSALING"},{id:3,text:"充值失败",value:"ORDER_FAILED"},{id:4,text:"充值成功",value:"ORDER_SUCCESS"}],C=[{id:1,text:"全部",value:""},{id:2,text:"线上",value:"charge"},{id:3,text:"线下",value:"charge_offline"}],T=[{id:1,text:"全部",value:""},{id:2,text:"微信",value:"WechatPay"},{id:3,text:"农行",value:"ABCPay"},{id:4,text:"现金",value:"CashPay"}],N=[{id:1,text:"全部",value:""},{id:2,text:"退款中",value:"ORDER_REFUNDING"},{id:3,text:"退款失败",value:"ORDER_FAILED"},{id:4,text:"退款成功",value:"ORDER_SUCCESS"}],O=[{id:1,text:"全部"},{id:2,text:"未结"},{id:3,text:"支付成功"},{id:4,text:"支付失败"},{id:5,text:"已重新扣款"}]}}]);