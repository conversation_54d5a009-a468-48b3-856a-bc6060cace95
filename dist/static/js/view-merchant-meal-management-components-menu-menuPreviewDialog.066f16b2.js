(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-components-menu-menuPreviewDialog","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-AreaFood","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-meal-report-MealPackageRule"],{c9d9:function(e,n,a){"use strict";a.d(n,"a",(function(){return u})),a.d(n,"d",(function(){return r})),a.d(n,"b",(function(){return m})),a.d(n,"c",(function(){return l})),a.d(n,"e",(function(){return d})),a.d(n,"f",(function(){return f})),a.d(n,"g",(function(){return o}));var i=a("5a0c"),t=a("da92"),u=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],r=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],m={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},l=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],d=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],f=(i().subtract(7,"day").format("YYYY-MM-DD"),i().format("YYYY-MM-DD"),function(e){return e?"number"===typeof e?t["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:t["a"].divide(e,100).toFixed(2):"0.00"}),o=function(e){return t["a"].times(e,100)}},cc06:function(e,n,a){"use strict";function i(e,n){var a={},i=n.useKeyList&&Object.keys(n.useKeyList).length;return i&&Object.keys(n.useKeyList).forEach((function(i,u){n.useKeyList[i].forEach((function(n,u){a[n]={row:[],mergeNum:0,key:i},a=t(a,e,n,i)}))})),n.mergeKeyList&&n.mergeKeyList.forEach((function(n,i){a[n]={row:[],mergeNum:0},a=t(a,e,n)})),a}function t(e,n,a,i){return n.forEach((function(t,r){if(0===r)e[a].row.push(1),e[a].mergeNum=r;else{var m=i?t[i]===n[r-1][i]:!i,l=t[a]===n[r-1][a]&&m;if(l){var d=u(e[a].row);e[a].row[d]+=1,e[a].row.push(0),e[a].mergeNum=r}else e[a].row.push(1),e[a].mergeNum=r}})),e}function u(e){var n=e.length-1;while(n>0){if(e[n])break;n--}return n}function r(e,n,a,i){var t=e[n].row[a],u=t>0?1:0;return[t,u]}a.d(n,"a",(function(){return i})),a.d(n,"b",(function(){return r}))}}]);