(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-system-components-AddressTemplate"],{"3be6":function(t,e,r){},5960:function(t,e,r){"use strict";r("3be6")},c7d2:function(t,e,r){"use strict";r.r(e);var o=function(){var t=this,e=t._self._c;return e("div",{staticClass:"address-code-template"},[e("div",{staticClass:"template-title"},[t._v(t._s(t.title))]),e("div",{staticClass:"template-setting"},[e("div",{staticClass:"code-setting"},[e("el-form",{ref:"addressFormRef",attrs:{model:t.addressForm,rules:t.addressFormRules}},["fk"!==t.type?e("el-form-item",{staticClass:"inline-box w-250",attrs:{label:"标题",prop:"title"}},[e("el-input",{staticClass:"w-180 ps-input",model:{value:t.addressForm.title,callback:function(e){t.$set(t.addressForm,"title",e)},expression:"addressForm.title"}})],1):t._e(),e("el-form-item",{staticClass:"inline-box w-300",attrs:{label:"模板"}},[e("el-select",{staticClass:"ps-select w-180",attrs:{placeholder:"请选择模板"},on:{change:t.selectTemplate},model:{value:t.addressForm.template,callback:function(e){t.$set(t.addressForm,"template",e)},expression:"addressForm.template"}},t._l(t.templateList,(function(t){return e("el-option",{key:t.file_path,attrs:{label:t.name,value:t.file_path}})})),1),"custom"===t.addressForm.template?e("div",{staticClass:"template-upload inline-box"},[e("el-upload",{ref:"uploadTemplate",staticClass:"upload-demo",attrs:{headers:t.headersOpts,data:t.uploadParams,action:t.actionUrl,"show-file-list":!1,"on-success":t.handleUploadSuccess,"before-upload":t.beforeUploadTemplate}},[e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"}},[t._v("上传模板")])],1)],1):t._e()],1),"custom"===t.addressForm.template?e("div",[e("el-form-item",{staticClass:"inline-box w-250",attrs:{label:"标题距离顶部",prop:"titlePx"}},[e("el-input",{staticClass:"w-100 ps-input",model:{value:t.addressForm.titlePx,callback:function(e){t.$set(t.addressForm,"titlePx",e)},expression:"addressForm.titlePx"}}),e("span",{staticClass:"m-l-5"},[t._v("px")])],1),e("el-form-item",{staticClass:"inline-box w-250",attrs:{label:"二维码距离顶部",prop:"codePx"}},[e("el-input",{staticClass:"w-100 ps-input",model:{value:t.addressForm.codePx,callback:function(e){t.$set(t.addressForm,"codePx",e)},expression:"addressForm.codePx"}}),e("span",{staticClass:"m-l-5"},[t._v("px")])],1),e("el-form-item",{staticClass:"inline-box w-300",attrs:{label:"地址信息距离顶部",prop:"addressPx"}},[e("el-input",{staticClass:"w-100 ps-input",model:{value:t.addressForm.addressPx,callback:function(e){t.$set(t.addressForm,"addressPx",e)},expression:"addressForm.addressPx"}}),e("span",{staticClass:"m-l-5"},[t._v("px")])],1),e("div",{staticClass:"tips"},[t._v("自定义模板参数：945x1418px；300分辨率；所有信息默认居中。")]),e("el-form-item",{staticClass:"label-block",attrs:{label:"标题颜色"}},[e("div",{staticClass:"color-list"},[t._l(t.colorList,(function(r){return e("div",{key:r,class:["color-item",t.addressForm.titleColor===r?"color-active":""],on:{click:function(e){return t.chooseColor("addressTitle",r)}}},[e("div",{staticClass:"color-box",style:{backgroundColor:"".concat(r)}})])})),e("el-color-picker",{class:["custom"===t.addressForm.titleColor?"active-picker":""],on:{change:function(e){return t.changeColor("addressTitle",e)}},model:{value:t.addressForm.titleCustomColor,callback:function(e){t.$set(t.addressForm,"titleCustomColor",e)},expression:"addressForm.titleCustomColor"}})],2)]),e("el-form-item",{staticClass:"label-block",attrs:{label:"配送地址颜色"}},[e("div",{staticClass:"color-list"},[t._l(t.colorList,(function(r){return e("div",{key:r,class:["color-item",t.addressForm.addressColor===r?"color-active":""],on:{click:function(e){return t.chooseColor("addressColor",r)}}},[e("div",{staticClass:"color-box",style:{backgroundColor:"".concat(r)}})])})),e("el-color-picker",{class:["custom"===t.addressForm.addressColor?"active-picker":""],on:{change:function(e){return t.changeColor("addressColor",e)}},model:{value:t.addressForm.addressCustomColor,callback:function(e){t.$set(t.addressForm,"addressCustomColor",e)},expression:"addressForm.addressCustomColor"}})],2)])],1):t._e()],1)],1),t.addressForm.fileList?e("div",{staticClass:"preview-img"},[e("img",{attrs:{src:t.addressForm.fileList,alt:"",srcset:""}}),t.defautPxInfo.codePx?e("div",{staticClass:"qrcode",style:{top:"custom"===t.addressForm.template?"".concat(t.addressForm.codePx/3,"px"):"".concat(t.defautPxInfo.codePx/3,"px")}},[e("qrcode",{attrs:{value:"https://cashier-v4.debug.packertec.com",options:{width:143,margin:0},alt:""}})],1):t._e(),t.defautPxInfo.titlePx?e("div",{staticClass:"title",style:{color:"".concat("custom"===t.addressForm.titleColor?t.addressForm.titleCustomColor:t.addressForm.titleColor),top:"custom"===t.addressForm.template?"".concat(t.addressForm.titlePx/3,"px"):"".concat(t.defautPxInfo.titlePx/3,"px")}},[t._v("> "+t._s(t.addressForm.title)+" ")]):t._e(),t.defautPxInfo.addressPx?e("div",{staticClass:"code-info",style:{color:"".concat("custom"===t.addressForm.addressColor?t.addressForm.addressCustomColor:t.addressForm.addressColor),top:"custom"===t.addressForm.template?"".concat(t.addressForm.addressPx/3,"px"):"".concat(t.defautPxInfo.addressPx/3,"px")}},[t._v("> 宿舍楼六楼603 ")]):t._e()]):t._e()]),e("el-button",{staticClass:"ps-origin-btn w-150",attrs:{size:"small",type:"primary"},on:{click:function(e){return t.checkForm()}}},[t._v("保存")])],1)},s=[],a=r("b2e5"),i=r.n(a);function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}function d(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */d=function(){return e};var t,e={},r=Object.prototype,o=r.hasOwnProperty,s=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",l=a.asyncIterator||"@@asyncIterator",c=a.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function f(t,e,r,o){var a=e&&e.prototype instanceof C?e:C,i=Object.create(a.prototype),n=new $(o||[]);return s(i,"_invoke",{value:I(t,r,n)}),i}function m(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="suspendedYield",v="executing",g="completed",y={};function C(){}function x(){}function F(){}var b={};u(b,i,(function(){return this}));var w=Object.getPrototypeOf,P=w&&w(w(S([])));P&&P!==r&&o.call(P,i)&&(b=P);var _=F.prototype=C.prototype=Object.create(b);function L(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function r(s,a,i,d){var l=m(t[s],t,a);if("throw"!==l.type){var c=l.arg,u=c.value;return u&&"object"==n(u)&&o.call(u,"__await")?e.resolve(u.__await).then((function(t){r("next",t,i,d)}),(function(t){r("throw",t,i,d)})):e.resolve(u).then((function(t){c.value=t,i(c)}),(function(t){return r("throw",t,i,d)}))}d(l.arg)}var a;s(this,"_invoke",{value:function(t,o){function s(){return new e((function(e,s){r(t,o,e,s)}))}return a=a?a.then(s,s):s()}})}function I(e,r,o){var s=p;return function(a,i){if(s===v)throw Error("Generator is already running");if(s===g){if("throw"===a)throw i;return{value:t,done:!0}}for(o.method=a,o.arg=i;;){var n=o.delegate;if(n){var d=O(n,o);if(d){if(d===y)continue;return d}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(s===p)throw s=g,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);s=v;var l=m(e,r,o);if("normal"===l.type){if(s=o.done?g:h,l.arg===y)continue;return{value:l.arg,done:o.done}}"throw"===l.type&&(s=g,o.method="throw",o.arg=l.arg)}}}function O(e,r){var o=r.method,s=e.iterator[o];if(s===t)return r.delegate=null,"throw"===o&&e.iterator.return&&(r.method="return",r.arg=t,O(e,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+o+"' method")),y;var a=m(s,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,y;var i=a.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function T(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function $(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function S(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var s=-1,a=function r(){for(;++s<e.length;)if(o.call(e,s))return r.value=e[s],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(n(e)+" is not iterable")}return x.prototype=F,s(_,"constructor",{value:F,configurable:!0}),s(F,"constructor",{value:x,configurable:!0}),x.displayName=u(F,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===x||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,F):(t.__proto__=F,u(t,c,"GeneratorFunction")),t.prototype=Object.create(_),t},e.awrap=function(t){return{__await:t}},L(k.prototype),u(k.prototype,l,(function(){return this})),e.AsyncIterator=k,e.async=function(t,r,o,s,a){void 0===a&&(a=Promise);var i=new k(f(t,r,o,s),a);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},L(_),u(_,c,"Generator"),u(_,i,(function(){return this})),u(_,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var o in e)r.push(o);return r.reverse(),function t(){for(;r.length;){var o=r.pop();if(o in e)return t.value=o,t.done=!1,t}return t.done=!0,t}},e.values=S,$.prototype={constructor:$,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(T),!e)for(var r in this)"t"===r.charAt(0)&&o.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function s(o,s){return n.type="throw",n.arg=e,r.next=o,s&&(r.method="next",r.arg=t),!!s}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],n=i.completion;if("root"===i.tryLoc)return s("end");if(i.tryLoc<=this.prev){var d=o.call(i,"catchLoc"),l=o.call(i,"finallyLoc");if(d&&l){if(this.prev<i.catchLoc)return s(i.catchLoc,!0);if(this.prev<i.finallyLoc)return s(i.finallyLoc)}else if(d){if(this.prev<i.catchLoc)return s(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return s(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var s=this.tryEntries[r];if(s.tryLoc<=this.prev&&o.call(s,"finallyLoc")&&this.prev<s.finallyLoc){var a=s;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=t,i.arg=e,a?(this.method="next",this.next=a.finallyLoc,y):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),T(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var o=r.completion;if("throw"===o.type){var s=o.arg;T(r)}return s}}throw Error("illegal catch attempt")},delegateYield:function(e,r,o){return this.delegate={iterator:S(e),resultName:r,nextLoc:o},"next"===this.method&&(this.arg=t),y}},e}function l(t,e,r,o,s,a,i){try{var n=t[a](i),d=n.value}catch(t){return void r(t)}n.done?e(d):Promise.resolve(d).then(o,s)}function c(t){return function(){var e=this,r=arguments;return new Promise((function(o,s){var a=t.apply(e,r);function i(t){l(a,o,s,i,n,"next",t)}function n(t){l(a,o,s,i,n,"throw",t)}i(void 0)}))}}var u={name:"AddressTemplate",components:{qrcode:i.a},props:{type:String,title:String,addrInfo:{type:Object,default:function(){return{}}},defautPxInfo:{type:Object,default:function(){return{titlePx:"0",codePx:"0",addressPx:"0"}}},templateList:{type:Array,default:function(){return[]}},colorList:{type:Array,default:function(){return[]}},actionUrl:String,headersOpts:{type:Object,default:function(){return{}}},confirm:Function},data:function(){return{addressForm:{title:"",template:"",titlePx:"1217",codePx:"493",addressPx:"996",titleColor:"#000000",titleCustomColor:"",addressColor:"#000000",addressCustomColor:"",fileList:""},addressFormRules:{title:[{required:!0,message:"请填写标题",trigger:"blur"}],titlePx:[{required:!0,message:"请填写标题距离",trigger:"blur"}],codePx:[{required:!0,message:"请填写二维码距离",trigger:"blur"}],addressPx:[{required:!0,message:"请填写地址距离",trigger:"blur"}]},uploadParams:{prefix:"addrQrcodeTemplate"}}},created:function(){this.addressForm.titlePx=this.defautPxInfo.titlePx,this.addressForm.codePx=this.defautPxInfo.codePx,this.addressForm.addressPx=this.defautPxInfo.addressPx},watch:{addrInfo:function(){this.addrInfo&&(this.addressForm.title=this.addrInfo.settings.title,this.addrInfo.type?(this.addressForm.titlePx=this.addrInfo.settings.title2top,this.addressForm.addressPx=this.addrInfo.settings.addr2top,this.addressForm.codePx=this.addrInfo.settings.qrcode2top,this.addressForm.template="custom",-1===this.colorList.indexOf(this.addrInfo.settings.title_color)?(this.addressForm.titleColor="custom",this.addressForm.titleCustomColor=this.addrInfo.settings.title_color):(this.addressForm.titleColor=this.addrInfo.settings.title_color,this.addressForm.titleCustomColor=""),-1===this.colorList.indexOf(this.addrInfo.settings.addr_color)?(this.addressForm.addressColor="custom",this.addressForm.addressCustomColor=this.addrInfo.settings.addr_color):(this.addressForm.addressColor=this.addrInfo.settings.addr_color,this.addressForm.addressCustomColor=""),this.addressForm.fileList=this.addrInfo.temp_path):(this.addressForm.template=this.addrInfo.temp_path,this.addressForm.titleColor=this.addrInfo.settings.title_color,this.addressForm.addressColor=this.addrInfo.settings.addr_color,this.addressForm.fileList=location.origin+"/api/temporary/"+this.addrInfo.temp_path,this.setSystemColor(this.addrInfo.temp_path)))}},methods:{checkForm:function(){var t=this;this.$refs.addressFormRef.validate((function(e){if(!e)return t.$message.error("数据填写有误，请检查"),!1;t.saveTemplate()}))},saveTemplate:function(){var t=this;return c(d().mark((function e(){return d().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$confirm("确认保存吗？","提示",{confirmButtonText:t.$t("dialog.confirm_btn"),cancelButtonText:t.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var e=c(d().mark((function e(r,o,s){var a,i;return d().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("confirm"!==r){e.next=15;break}return o.confirmButtonLoading=!0,a={organization:t.$store.getters.organization,qrcode_type:t.type},t.addrInfo&&(a.id=t.addrInfo.id),a.name=t.templateList.filter((function(e){return e.file_path===t.addressForm.template})).name,a.settings={title:t.addressForm.title},"custom"===t.addressForm.template?(a.type=1,a.temp_path=t.addressForm.fileList,a.settings.title2top=t.addressForm.titlePx,a.settings.addr2top=t.addressForm.addressPx,a.settings.qrcode2top=t.addressForm.codePx,a.settings.title_color="custom"===t.addressForm.titleColor?t.addressForm.titleCustomColor:t.addressForm.titleColor,a.settings.addr_color="custom"===t.addressForm.addressColor?t.addressForm.addressCustomColor:t.addressForm.addressColor):(a.type=0,a.temp_path=t.addressForm.template),e.next=9,t.$apis.apiBackgroundTablecodeRsvQrocodeTemplateModifyPost(a);case 9:i=e.sent,0===i.code?(t.$message.success("保存成功"),t.confirm()):t.$message.error(i.msg),s(),o.confirmButtonLoading=!1,e.next=16;break;case 15:o.confirmButtonLoading||s();case 16:case"end":return e.stop()}}),e)})));function r(t,r,o){return e.apply(this,arguments)}return r}()}).then((function(t){})).catch((function(t){}));case 1:case"end":return e.stop()}}),e)})))()},selectTemplate:function(t){"custom"===t?(this.addressForm.fileList=-1===this.addrInfo.temp_path.indexOf("http")?"":this.addrInfo.temp_path,this.addressForm.titleColor="#000000",this.addressForm.addressColor="#000000"):(this.addressForm.fileList=location.origin+"/api/temporary/"+t,this.setSystemColor(t))},setSystemColor:function(t){-1!==t.indexOf("01")?(this.addressForm.titleColor="#ffffff",this.addressForm.addressColor="#30C957"):-1!==t.indexOf("02")?(this.addressForm.titleColor="#2A8CE3",this.addressForm.addressColor="#2A8CE3"):-1!==t.indexOf("03")?(this.addressForm.titleColor="#FC8B2F",this.addressForm.addressColor="#ffffff"):-1!==t.indexOf("04")?(this.addressForm.titleColor="#E9513C",this.addressForm.addressColor="#E9513C"):-1!==t.indexOf("05")&&(this.addressForm.titleColor="#ffffff",this.addressForm.addressColor="#ffffff")},chooseColor:function(t,e){"addressTitle"===t?this.addressForm.titleColor=e:"addressColor"===t&&(this.addressForm.addressColor=e)},changeColor:function(t,e){"addressTitle"===t?(this.addressForm.titleColor="custom",this.addressForm.titleCustomColor=e):"addressColor"===t&&(this.addressForm.addressColor="custom",this.addressForm.addressCustomColor=e)},handleUploadSuccess:function(t,e){0===t.code?(this.$refs.uploadTemplate.clearFiles(),this.addressForm.fileList=t.data.public_url,this.getImgPx(this.addressForm.fileList)):this.$message.error(t.msg)},beforeUploadTemplate:function(t){var e="image/jpeg"===t.type||"image/png"===t.type,r=t.size/1024/1024<5;return this.uploadParams.key=(new Date).getTime()+Math.floor(150*Math.random()),e||this.$message.error("模板只能是 JPG PNG格式!"),r||this.$message.error("模板图片大小不能超过 5MB!"),e&&r},getImgPx:function(t){var e=new Image;return e.src=t,new Promise((function(t){e.onload=function(){var r=e.width,o=e.height;t({width:r,height:o})}}))}}},f=u,m=(r("5960"),r("2877")),p=Object(m["a"])(f,o,s,!1,null,null,null);e["default"]=p.exports}}]);