(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-consumption-rules-coupon-admin-components-DetailCorrelationOrderDialog"],{"030e":function(e,t,o){"use strict";o("5822")},3563:function(e,t,o){"use strict";o.r(t);var n=function(){var e=this,t=e._self._c;return t("dialog-message",{attrs:{show:e.visible,title:"关联订单",customClass:"ps-dialog",width:"900px","destroy-on-close":!1},on:{"update:show":function(t){e.visible=t},close:e.handleClose}},[t("div",{staticClass:"table-content"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.correlationOrderInfo.coupon_order,stripe:"","header-row-class-name":"ps-table-header-row"}},e._l(e.tableSettings,(function(o){return t("table-column",{key:o.key,attrs:{col:o},scopedSlots:e._u([{key:"extra",fn:function(o){var n=o.row;return["DISCOUNT"===e.correlationOrderInfo.coupon_type?t("span",[e._v(" "+e._s(e._f("formatMoney")(n.discount_fee))+" ")]):e._e(),"FULL_DISCOUNT"===e.correlationOrderInfo.coupon_type||"INSTANT_DISCOUNT"===e.correlationOrderInfo.coupon_type?t("span",[e._v(" "+e._s(e._f("formatMoney")(n.deduction_fee))+" ")]):t("span",[e._v(e._s(e.couponExtra(n)))])]}}],null,!0)})})),1)],1),t("template",{slot:"tool"},[t("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-cancel-btn",on:{click:e.handleClose}},[e._v("关闭")])],1)])],2)},a=[],r={name:"HistoryDialog",props:{correlationOrderInfo:{type:Object,default:function(){return{}}},title:{type:String,default:""},isshow:Boolean},data:function(){return{isLoading:!1,tableSettings:[{label:"子订单号",key:"trade_no"},{label:"订单金额",key:"origin_fee",type:"money"},{label:"优惠内容",key:"extra",type:"slot",slotName:"extra"}]}},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},watch:{},created:function(){},mounted:function(){},methods:{handleClose:function(e){this.visible=!1},couponExtra:function(e){var t=e.coupon_extra.map((function(e){return e.name}));return t.join(",")}}},i=r,s=(o("030e"),o("2877")),l=Object(s["a"])(i,n,a,!1,null,null,null);t["default"]=l.exports},5822:function(e,t,o){e.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}}}]);