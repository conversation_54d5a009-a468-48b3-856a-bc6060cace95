(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-supplier-admin-RelationSupplierIngredient","view-merchant-meal-management-supplier-admin-components-nutrition","view-merchant-meal-management-supplier-admin-components-tupplierIngredientDialog"],{"16f7":function(t,e,n){"use strict";n("c1db")},"18c2":function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t._self._c;return e("div",{staticClass:"relationSupplierIngredient container-wrapper"},[e("refresh-tool",{on:{refreshPage:t.refreshHandle}}),e("search-form",{ref:"searchRef",attrs:{"form-setting":t.searchFormSetting,"label-width":"100px"},on:{search:t.searchHandle}}),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("数据列表")]),e("div",{staticClass:"align-r"},[e("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(e){return t.addOrEditIngredient("add")}}},[t._v(" 新建 ")]),e("button-icon",{attrs:{color:"plain",type:"Import"},on:{click:function(e){return t.importHandler("import")}}},[t._v(" 批量导入 ")])],1)]),e("div",{staticClass:"table-content"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:"",border:"","header-row-class-name":"ps-table-header-row"},on:{"selection-change":t.handleSelectionChange}},[e("el-table-column",{attrs:{prop:"create_time",label:"关联时间",align:"center"}}),e("el-table-column",{attrs:{prop:"ingredient_name",label:"食材名称",align:"center"}}),e("el-table-column",{attrs:{prop:"xx",label:"营养信息",align:"center"},scopedSlots:t._u([{key:"default",fn:function(n){return[n.row.is_enable_nutrition?e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.showDialogHandler("nutrition",n.row)}}},[t._v(" 查看 ")]):e("span",[t._v("--")])]}}])}),e("el-table-column",{attrs:{prop:"sourced_code",label:"食材溯源码",align:"center",width:"100"},scopedSlots:t._u([{key:"default",fn:function(n){return[e("el-image",{staticClass:"w-100-p",attrs:{src:n.row.sourced_code,"preview-src-list":t.srcList,alt:"暂无图片"},on:{click:function(e){return t.imgsourcedCodeClick(n.row)}}})]}}])}),e("el-table-column",{attrs:{fixed:"right",label:"操作",width:"180",align:"center"},scopedSlots:t._u([{key:"default",fn:function(n){return[e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.addOrEditIngredient("modify",n.row)}}},[t._v(" 编辑 ")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.mulOperation("del",n.row)}}},[t._v(" 删除 ")])]}}])})],1)],1),t.ingredientDialogVisible?e("tupplier-ingredient-dialog",{attrs:{isshow:t.ingredientDialogVisible,type:t.ingredientDialogType,title:t.ingredientDialogTitle,width:"500","ingredient-info":t.ingredientInfo},on:{"update:isshow":function(e){t.ingredientDialogVisible=e},confirm:t.searchHandle}}):t._e(),e("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[e("el-pagination",{staticClass:"ps-text",attrs:{"current-page":t.currentPage,"page-sizes":[10,20,50,100,500],"page-size":t.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:t.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1),e("el-dialog",{attrs:{title:t.dialogTitle,visible:t.dialogVisible,width:t.dialogWidth,top:"20vh","custom-class":"ps-dialog","close-on-click-modal":!1},on:{"update:visible":function(e){t.dialogVisible=e},closed:t.dialogHandleClose}},[e("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],attrs:{model:t.formData,size:"small"}},["nutrition"===t.dialogType?e("div",[t._l(t.nutritionList,(function(n){return[e("div",{key:n.key,staticClass:"nutrition-item"},[e("div",{staticClass:"nutrition-label"},[t._v(t._s(n.name+"："))]),e("el-form-item",{attrs:{prop:n.key}},[e("el-input",{staticClass:"ps-input",staticStyle:{width:"120px"},attrs:{readonly:""},model:{value:t.formData[n.key],callback:function(e){t.$set(t.formData,n.key,e)},expression:"formData[nutrition.key]"}}),e("span",{staticStyle:{"margin-left":"10px"}},[t._v(t._s(n.unit))])],1)],1)]}))],2):t._e()]),"nutrition"!==t.dialogType?e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"ps-cancel-btn",attrs:{size:"small"},on:{click:function(e){t.dialogVisible=!1}}},[t._v(" 取消 ")]),e("el-button",{staticClass:"ps-origin-btn",attrs:{type:"primary",size:"small"},on:{click:function(e){t.dialogVisible=!1}}},[t._v(" 确定 ")])],1):t._e()],1),e("el-dialog",{attrs:{title:"批量导入",visible:t.importShowDialog,width:"600px","custom-class":"ps-dialog"},on:{"update:visible":function(e){t.importShowDialog=e}}},[e("import-upload-file",{attrs:{uploadFormItemLabel:"批量导入","file-type":"zip",link:t.importLink},on:{publicUrl:t.publicUrl}}),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"ps-cancel-btn",on:{click:function(e){t.importShowDialog=!1}}},[t._v("取 消")]),e("el-button",{staticClass:"ps-btn",attrs:{type:"primary"},on:{click:t.imortHandler}},[t._v("确 定")])],1)],1)],1)},i=[],o=n("f63a"),a=n("228c"),s=n("ed08"),c=n("4856"),l=n("a1d6");function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function p(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */p=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(t,e,n){t[e]=n.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function l(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function(t,e,n){return t[e]=n}}function d(t,e,n,r){var o=e&&e.prototype instanceof b?e:b,a=Object.create(o.prototype),s=new T(r||[]);return i(a,"_invoke",{value:C(t,n,s)}),a}function f(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=d;var h="suspendedStart",g="suspendedYield",m="executing",y="completed",v={};function b(){}function w(){}function k(){}var _={};l(_,a,(function(){return this}));var L=Object.getPrototypeOf,x=L&&L(L(P([])));x&&x!==n&&r.call(x,a)&&(_=x);var S=k.prototype=b.prototype=Object.create(_);function I(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function O(t,e){function n(i,o,a,s){var c=f(t[i],t,o);if("throw"!==c.type){var l=c.arg,p=l.value;return p&&"object"==u(p)&&r.call(p,"__await")?e.resolve(p.__await).then((function(t){n("next",t,a,s)}),(function(t){n("throw",t,a,s)})):e.resolve(p).then((function(t){l.value=t,a(l)}),(function(t){return n("throw",t,a,s)}))}s(c.arg)}var o;i(this,"_invoke",{value:function(t,r){function i(){return new e((function(e,i){n(t,r,e,i)}))}return o=o?o.then(i,i):i()}})}function C(e,n,r){var i=h;return function(o,a){if(i===m)throw Error("Generator is already running");if(i===y){if("throw"===o)throw a;return{value:t,done:!0}}for(r.method=o,r.arg=a;;){var s=r.delegate;if(s){var c=D(s,r);if(c){if(c===v)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===h)throw i=y,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=m;var l=f(e,n,r);if("normal"===l.type){if(i=r.done?y:g,l.arg===v)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(i=y,r.method="throw",r.arg=l.arg)}}}function D(e,n){var r=n.method,i=e.iterator[r];if(i===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,D(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var o=f(i,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,v;var a=o.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,v):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function P(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function n(){for(;++i<e.length;)if(r.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return o.next=o}}throw new TypeError(u(e)+" is not iterable")}return w.prototype=k,i(S,"constructor",{value:k,configurable:!0}),i(k,"constructor",{value:w,configurable:!0}),w.displayName=l(k,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,k):(t.__proto__=k,l(t,c,"GeneratorFunction")),t.prototype=Object.create(S),t},e.awrap=function(t){return{__await:t}},I(O.prototype),l(O.prototype,s,(function(){return this})),e.AsyncIterator=O,e.async=function(t,n,r,i,o){void 0===o&&(o=Promise);var a=new O(d(t,n,r,i),o);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},I(S),l(S,c,"Generator"),l(S,a,(function(){return this})),l(S,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=P,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function i(r,i){return s.type="throw",s.arg=e,n.next=r,i&&(n.method="next",n.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),l=r.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),j(n),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;j(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:P(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),v}},e}function d(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function f(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?d(Object(n),!0).forEach((function(e){h(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function h(t,e,n){return(e=g(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function g(t){var e=m(t,"string");return"symbol"==u(e)?e:e+""}function m(t,e){if("object"!=u(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=u(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function y(t,e,n,r,i,o,a){try{var s=t[o](a),c=s.value}catch(t){return void n(t)}s.done?e(c):Promise.resolve(c).then(r,i)}function v(t){return function(){var e=this,n=arguments;return new Promise((function(r,i){var o=t.apply(e,n);function a(t){y(o,r,i,a,s,"next",t)}function s(t){y(o,r,i,a,s,"throw",t)}a(void 0)}))}}var b={name:"MealReportDetail",mixins:[o["a"]],data:function(){return{importLink:"",supplierId:"",tableData:[],srcList:["https://fuss10.elemecdn.com/8/27/f01c15bb73e1ef3793e64e6b7bbccjpeg.jpeg"],isLoading:!1,searchFormSetting:{ingredient_name:{type:"input",value:"",label:"食材名称",placeholder:"请输入食材名称"}},ingredientDialogVisible:!1,ingredientInfo:{},ingredientDialogType:"",ingredientDialogTitle:"关联食材",pageSize:10,totalCount:0,currentPage:1,dialogType:"",dialogVisible:!1,dialogTitle:"",dialogWidth:"",formData:{},nutritionList:c["NUTRITION_LIST"],importShowDialog:!1,importUrl:""}},components:{tupplierIngredientDialog:a["default"],ImportUploadFile:l["a"]},created:function(){this.importLink="".concat(location.origin,"/api/temporary/template_excel/supplier_ingredient_import.zip"),this.supplierId=this.$route.query.supplier_id,this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getSupplierIngredientList()},searchHandle:Object(s["d"])((function(){this.currentPage=1,this.getSupplierIngredientList()}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.currentPage=1,this.initLoad()},formatQueryParams:function(t){var e={};for(var n in t)""!==t[n].value&&null!==t[n].value&&0!==t[n].value.length&&("select_date"!==n?e[n]=t[n].value:t[n].value.length>0&&(e.start_date=t[n].value[0],e.end_date=t[n].value[1]));return e},getSupplierIngredientList:function(){var t=this;return v(p().mark((function e(){var n;return p().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.$apis.apiBackgroundFoodIngredientSupplierSupplierIngredientListPost(f(f({},t.formatQueryParams(t.searchFormSetting)),{},{supplier_id:t.supplierId,page:t.currentPage,page_size:t.pageSize}));case 3:n=e.sent,t.isLoading=!1,0===n.code?(t.tableData=n.data.results,t.totalCount=n.data.count):t.$message.error(n.msg);case 6:case"end":return e.stop()}}),e)})))()},mulOperation:function(t,e){var n=this;this.$confirm("确定删除该条食材数据？","提示",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var t=v(p().mark((function t(r,i,o){return p().wrap((function(t){while(1)switch(t.prev=t.next){case 0:"confirm"===r?(i.confirmButtonLoading=!0,n.deleteSupplierIngredient([e.id]),o(),i.confirmButtonLoading=!1):i.confirmButtonLoading||o();case 1:case"end":return t.stop()}}),t)})));function r(e,n,r){return t.apply(this,arguments)}return r}()})},deleteSupplierIngredient:function(t){var e=this;return v(p().mark((function n(){var r;return p().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,e.$apis.apiBackgroundFoodIngredientSupplierDeleteSupplierIngredientPost({ids:t});case 2:r=n.sent,0===r.code?(e.$message.success(r.msg),e.getSupplierIngredientList()):e.$message.error(r.msg);case 4:case"end":return n.stop()}}),n)})))()},imgsourcedCodeClick:function(t){this.srcList[0]=t.sourced_code},showDialogHandler:function(t,e){var n=this;if(this.dialogType=t,"nutrition"===t){var r=e.ingredient_nutrition;r||(r={});var i=r.element?JSON.parse(Object(s["R"])(r.element)):{},o=r.vitamin?JSON.parse(Object(s["R"])(r.vitamin)):{};c["NUTRITION_LIST"].forEach((function(t){"default"===t.type&&n.$set(n.formData,t.key,r[t.key]),"element"===t.type&&n.$set(n.formData,t.key,i[t.key]),"vitamin"===t.type&&n.$set(n.formData,t.key,o[t.key])})),this.dialogTitle="营养信息",this.dialogWidth="700px"}this.dialogVisible=!0},dialogHandleClose:function(){this.formData={}},imortHandler:function(){var t=this;return v(p().mark((function e(){var n;return p().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.importShowDialog=!1,n={type:"importSupplierBatchAddSupplierIngredient",immediate:!0,params:{face_zip_url:t.importUrl,supplier_id:t.supplierId}},t.exportHandle(n);case 3:case"end":return e.stop()}}),e)})))()},publicUrl:function(t){this.importUrl=t},importHandler:function(){this.importShowDialog=!0},handleSizeChange:function(t){this.pageSize=t,this.getSupplierIngredientList()},handleCurrentChange:function(t){this.currentPage=t,this.getSupplierIngredientList()},handleSelectionChange:function(t){},addOrEditIngredient:function(t,e){this.ingredientDialogType=t,this.ingredientDialogVisible=!0,this.ingredientInfo="add"===t?{supplier_id:this.supplierId}:{id:e.id,ingredient_id:Number(e.ingredient_id),sourced_code:e.sourced_code}}}},w=b,k=(n("8ed4"),n("2877")),_=Object(k["a"])(w,r,i,!1,null,"5ae4c886",null);e["default"]=_.exports},"228c":function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t._self._c;return e("dialog-message",{attrs:{show:t.visible,title:t.title,showFooter:t.showFooter,loading:t.isLoading,customClass:"ps-dialog",width:t.width,top:"200px"},on:{"update:show":function(e){t.visible=e},"update:loading":function(e){t.isLoading=e},close:t.handleClose}},[e("el-form",{ref:"formData",staticClass:"dialog-form",attrs:{model:t.formData,"status-icon":"",rules:t.formDataRules,"label-width":"120px"},nativeOn:{submit:function(t){t.preventDefault()}}},[e("el-form-item",{attrs:{label:"食材名称",prop:"ingredient_id"}},[e("el-select",{staticClass:"ps-select",attrs:{placeholder:"请选择",size:"small"},model:{value:t.formData.ingredient_id,callback:function(e){t.$set(t.formData,"ingredient_id",e)},expression:"formData.ingredient_id"}},t._l(t.ingredientNameList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),e("el-form-item",{attrs:{label:"食材溯源码",prop:"sourced_code"}},[e("el-upload",{ref:"uploadRegistration",staticClass:"avatar-uploader",attrs:{data:t.uploadParams,action:t.actionUrl,"show-file-list":!1,"on-success":t.handleAvatarSuccess,"before-upload":t.beforeAvatarUpload}},[t.formData.sourced_code?e("img",{staticClass:"avatar",attrs:{src:t.formData.sourced_code}}):e("i",{staticClass:"el-icon-plus avatar-uploader-icon"}),e("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v(" 上传的图片不可超过2M ")])])],1)],1),e("template",{slot:"tool"},[t.showFooter?e("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:t.isLoading},on:{click:t.clickCancleHandle}},[t._v(" 取消 ")]),e("el-button",{staticClass:"ps-btn",attrs:{disabled:t.isLoading,type:"primary"},on:{click:t.clickConfirmHandle}},[t._v(" 确定 ")])],1):t._e()])],2)},i=[];function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function a(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */a=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(t,e,n){t[e]=n.value},s="function"==typeof Symbol?Symbol:{},c=s.iterator||"@@iterator",l=s.asyncIterator||"@@asyncIterator",u=s.toStringTag||"@@toStringTag";function p(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{p({},"")}catch(t){p=function(t,e,n){return t[e]=n}}function d(t,e,n,r){var o=e&&e.prototype instanceof b?e:b,a=Object.create(o.prototype),s=new T(r||[]);return i(a,"_invoke",{value:C(t,n,s)}),a}function f(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=d;var h="suspendedStart",g="suspendedYield",m="executing",y="completed",v={};function b(){}function w(){}function k(){}var _={};p(_,c,(function(){return this}));var L=Object.getPrototypeOf,x=L&&L(L(P([])));x&&x!==n&&r.call(x,c)&&(_=x);var S=k.prototype=b.prototype=Object.create(_);function I(t){["next","throw","return"].forEach((function(e){p(t,e,(function(t){return this._invoke(e,t)}))}))}function O(t,e){function n(i,a,s,c){var l=f(t[i],t,a);if("throw"!==l.type){var u=l.arg,p=u.value;return p&&"object"==o(p)&&r.call(p,"__await")?e.resolve(p.__await).then((function(t){n("next",t,s,c)}),(function(t){n("throw",t,s,c)})):e.resolve(p).then((function(t){u.value=t,s(u)}),(function(t){return n("throw",t,s,c)}))}c(l.arg)}var a;i(this,"_invoke",{value:function(t,r){function i(){return new e((function(e,i){n(t,r,e,i)}))}return a=a?a.then(i,i):i()}})}function C(e,n,r){var i=h;return function(o,a){if(i===m)throw Error("Generator is already running");if(i===y){if("throw"===o)throw a;return{value:t,done:!0}}for(r.method=o,r.arg=a;;){var s=r.delegate;if(s){var c=D(s,r);if(c){if(c===v)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===h)throw i=y,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=m;var l=f(e,n,r);if("normal"===l.type){if(i=r.done?y:g,l.arg===v)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(i=y,r.method="throw",r.arg=l.arg)}}}function D(e,n){var r=n.method,i=e.iterator[r];if(i===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,D(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var o=f(i,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,v;var a=o.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,v):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function P(e){if(e||""===e){var n=e[c];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,a=function n(){for(;++i<e.length;)if(r.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return a.next=a}}throw new TypeError(o(e)+" is not iterable")}return w.prototype=k,i(S,"constructor",{value:k,configurable:!0}),i(k,"constructor",{value:w,configurable:!0}),w.displayName=p(k,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,k):(t.__proto__=k,p(t,u,"GeneratorFunction")),t.prototype=Object.create(S),t},e.awrap=function(t){return{__await:t}},I(O.prototype),p(O.prototype,l,(function(){return this})),e.AsyncIterator=O,e.async=function(t,n,r,i,o){void 0===o&&(o=Promise);var a=new O(d(t,n,r,i),o);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},I(S),p(S,u,"Generator"),p(S,c,(function(){return this})),p(S,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=P,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function i(r,i){return s.type="throw",s.arg=e,n.next=r,i&&(n.method="next",n.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),l=r.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),j(n),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;j(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:P(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),v}},e}function s(t,e,n,r,i,o,a){try{var s=t[o](a),c=s.value}catch(t){return void n(t)}s.done?e(c):Promise.resolve(c).then(r,i)}function c(t){return function(){var e=this,n=arguments;return new Promise((function(r,i){var o=t.apply(e,n);function a(t){s(o,r,i,a,c,"next",t)}function c(t){s(o,r,i,a,c,"throw",t)}a(void 0)}))}}var l={name:"trayDialog",props:{loading:Boolean,type:{type:String,default:"add"},title:{type:String,default:"关联食材"},width:{type:String,default:"500px"},showFooter:{type:Boolean,default:!0},isshow:Boolean,ingredientInfo:{type:Object,default:function(){return{}}},confirm:Function},data:function(){return{isLoading:!1,formData:{ingredient_id:"",sourced_code:""},ingredientNameList:[],formDataRules:{ingredient_id:[{required:!0,message:"请选择食材名称",trigger:"change"}],sourced_code:[{required:!0,message:"请上传食材溯源码",trigger:"change"}]},actionUrl:"",uploadParams:{}}},computed:{visible:{get:function(){return this.isshow},set:function(t){this.$emit("update:isshow",t)}}},created:function(){this.getUploadToken(),this.getIngredientNameList()},mounted:function(){},methods:{getUploadToken:function(){var t=this;return c(a().mark((function e(){var n;return a().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.getUploadToken({prefix:"jpeg/png"});case 2:n=e.sent,0===n.code?(t.actionUrl=n.data.host,t.uploadParams={key:n.data.prefix+(new Date).getTime()+Math.floor(150*Math.random()),prefix:n.data.prefix,policy:n.data.policy,OSSAccessKeyId:n.data.accessid,signature:n.data.signature,callback:n.data.callback,success_action_status:"200"}):t.$message.error(n.msg);case 4:case"end":return e.stop()}}),e)})))()},getIngredientNameList:function(){var t=this;return c(a().mark((function e(){var n;return a().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.$apis.apiBackgroundFoodIngredientIngredientNamePost();case 3:n=e.sent,t.isLoading=!1,0===n.code?(t.ingredientNameList=n.data,t.initLoad()):t.$message.error(n.msg);case 6:case"end":return e.stop()}}),e)})))()},initLoad:function(){"modify"===this.type&&(this.formData=this.ingredientInfo)},clickConfirmHandle:function(){var t,e=this,n={ingredient_id:this.formData.ingredient_id,sourced_code:this.formData.sourced_code};this.$refs.formData.validate((function(r){r&&("add"===e.type?(n.supplier_id=e.ingredientInfo.supplier_id,t=e.$apis.apiBackgroundFoodIngredientSupplierAddSupplierIngredientPost(n)):(n.id=e.ingredientInfo.id,t=e.$apis.apiBackgroundFoodIngredientSupplierModifySupplierIngredientPost(n)),e.addEditTray(t))}))},addEditTray:function(t){var e=this;return c(a().mark((function n(){var r;return a().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!e.isLoading){n.next=2;break}return n.abrupt("return");case 2:return e.isLoading=!0,n.next=5,t;case 5:r=n.sent,e.isLoading=!1,e.visible=!1,0===r.code?(e.$message.success("成功"),e.$emit("confirm","search")):e.$message.error(r.msg);case 9:case"end":return n.stop()}}),n)})))()},clickCancleHandle:function(){this.visible=!1},handleAvatarSuccess:function(t,e){0===t.code?(this.$refs.uploadRegistration.clearFiles(),this.formData.sourced_code=t.data.public_url,this.getUploadToken()):this.$message.error(t.msg)},beforeAvatarUpload:function(t){var e=this;return c(a().mark((function n(){var r,i,o,s,c;return a().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(r="image/jpeg"===t.type||"image/png"===t.type,i=t.size/1024/1024<2,r){n.next=7;break}return e.$message.error("上传图片只能为jpg或png格式"),n.abrupt("return",!1);case 7:if(i){n.next=12;break}return e.$message.error("上传图片大小不能超过2MB"),n.abrupt("return",!1);case 12:return o=250,s=250,n.next=16,new Promise((function(e,n){var r=window.URL||window.webkitURL,i=new Image;i.onload=function(){var t=i.width===o&&i.height===s;t?e():n()},i.src=r.createObjectURL(t)})).then((function(){return!0}),(function(){return!1}));case 16:if(c=n.sent,c){n.next=20;break}return e.$message.error("上传图片尺寸必须为"+o+"*"+s+"!"),n.abrupt("return",Promise.reject());case 20:case"end":return n.stop()}}),n)})))()},handleClose:function(t){this.isLoading=!1,this.visible=!1,this.$refs.formData.resetFields()}}},u=l,p=(n("16f7"),n("2877")),d=Object(p["a"])(u,r,i,!1,null,null,null);e["default"]=d.exports},"3dda":function(t,e,n){},4856:function(t,e,n){"use strict";n.r(e),n.d(e,"DEFAULT_NUTRITION",(function(){return r})),n.d(e,"ELEMENT_NUTRITION",(function(){return i})),n.d(e,"VITAMIN_NUTRITION",(function(){return o})),n.d(e,"NUTRITION_LIST",(function(){return a}));var r=[{name:"千焦耳",key:"energy_mj",unit:"kj",type:"default"},{name:"千卡",key:"energy_kcal",unit:"Kcal",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"}],i=[{key:"Ca",name:"钙",unit:"mg",type:"element"},{key:"P",name:"磷",unit:"mg",type:"element"},{key:"K",name:"钾",unit:"mg",type:"element"},{key:"Na",name:"钠",unit:"mg",type:"element"},{name:"镁",key:"Mg",unit:"mg",type:"element"},{key:"Fe",name:"铁",unit:"mg",type:"element"},{key:"I",name:"碘",unit:"μg",type:"element"},{key:"Se",name:"硒",unit:"μg",type:"element"},{key:"Zn",name:"锌",unit:"mg",type:"element"},{key:"Cu",name:"铜",unit:"mg",type:"element"},{key:"F",name:"氟",unit:"mg",type:"element"},{key:"Cr",name:"铬",unit:"μg",type:"element"},{key:"Mo",name:"钼",unit:"μg",type:"element"},{key:"Mn",name:"锰",unit:"mg",type:"element"}],o=[{key:"VA",name:"维生素A",unit:"μg",type:"vitamin"},{key:"VD",name:"维生素D",unit:"μg",type:"vitamin"},{key:"VE",name:"维生素E",unit:"mg",type:"vitamin"},{key:"VK",name:"维生素K",unit:"μg",type:"vitamin"},{key:"VB1",name:"维生素B1",unit:"mg",type:"vitamin"},{key:"VB2",name:"维生素B2",unit:"mg",type:"vitamin"},{key:"VB6",name:"维生素B6",unit:"mg",type:"vitamin"},{key:"VB12",name:"维生素B12",unit:"μg",type:"vitamin"},{key:"VC",name:"维生素C",unit:"mg",type:"vitamin"},{key:"VB5",name:"泛酸",unit:"mg",type:"vitamin"},{key:"VM",name:"叶酸",unit:"μg",type:"vitamin"},{key:"VB3",name:"烟酸",unit:"mg",type:"vitamin"},{key:"Choline",name:" 胆碱",unit:"mg",type:"vitamin"},{key:"Nicotinamide",name:"烟酰胺",unit:"mg",type:"vitamin"},{key:"VH",name:"生物素",unit:"mg",type:"vitamin"}],a=[].concat(r,i,o)},"8ed4":function(t,e,n){"use strict";n("3dda")},c1db:function(t,e,n){}}]);