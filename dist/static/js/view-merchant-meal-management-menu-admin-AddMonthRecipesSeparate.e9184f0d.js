(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-menu-admin-AddMonthRecipesSeparate","recipes_manage~view-merchant-meal-management-components-menu-menuPreviewDialog~view-merchant-meal-ma~870a4654"],{"22ec":function(e,t,r){},"497c":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}]},[t("div",{staticClass:"box-header-week"},[t("span",[e._v("本月菜谱（"+e._s(e.currentYear)+"）")]),e._m(0)]),t("div",[t("div",{staticClass:"week-header-wrapper"},e._l(e.weekDays,(function(r){return t("div",{key:r,staticClass:"week-day"},[e._v(e._s(r))])})),0),t("div",{staticClass:"content"},e._l(e.dayList,(function(r,n){return t("div",{key:"".concat(r,"_").concat(n),staticClass:"item",style:{borderRight:n===e.dayList.length-1?e.border:""}},[""!==r?t("div",[e._v(e._s(e.currentMonth)+"月"+e._s(r.dayNumber)+"日")]):e._e(),t("div",{class:{"meal-cell-wrapper-bg":r&&!e.cateringFoodNumber(r.data.foods)},staticStyle:{cursor:"pointer"},on:{click:function(t){return e.openEditDialog(r)}}},[""!==r?t("div",{staticClass:"operate"},[e.cateringFoodNumber(r.data.foods)?t("span",{staticStyle:{"font-size":"12px"}},[e._v(" 已配餐（"+e._s(e.cateringFoodNumber(r.data.foods))+"） ")]):t("span",{staticClass:"copy"},[e._v("未配餐")]),e.cateringFoodNumber(r.data.foods)?t("span",{staticClass:"edit",on:{click:function(t){return t.stopPropagation(),e.openCopyDialog(r)}}},[e._v(" 复制 ")]):e._e()]):e._e(),e.isNutritionGuidance&&""!==r&&r.data.foods.length>0?t("div",{staticClass:"fee-wrapper"},[t("div",{staticClass:"header-wrapper",staticStyle:{padding:"0px"}},[t("div",{staticClass:"marker-wrapper marker-box"},e._l(e.markerList,(function(n){return t("div",{key:n.label},[t("span",{class:n.className,staticStyle:{width:"9px",height:"9px"}}),t("span",{staticStyle:{"margin-right":"10px"}},[e._v(e._s(n.label)+":"+e._s(r[n.key]))])])})),0)])]):e._e()])])})),0)]),t("el-dialog",{attrs:{title:"复制到",visible:e.showCopyDialog,"close-on-press-escape":!1,"close-on-click-modal":!1,"destroy-on-close":!0,width:"600px"},on:{"update:visible":function(t){e.showCopyDialog=t}}},[t("p",[t("span",{staticStyle:{"margin-right":"48px"}},[e._v("已选："+e._s(e.currentDate))])]),t("el-date-picker",{attrs:{type:"dates",placeholder:"选择日期",size:"small","default-value":e.dayValue,"picker-options":e.pickerOptions,"popper-class":"el-picker-box"},model:{value:e.copyDateVal,callback:function(t){e.copyDateVal=t},expression:"copyDateVal"}}),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{size:"small"},on:{click:function(t){e.showCopyDialog=!1}}},[e._v("取 消")]),t("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.handleCopy}},[e._v("确 定")])],1)],1),e.menuPreviewDialogVisible?t("menu-preview-dialog",{ref:"menuPreviewDialog",attrs:{isshow:e.menuPreviewDialogVisible,formDataDialog:e.dialogMenuPeviewInfo,width:"900px"},on:{"update:isshow":function(t){e.menuPreviewDialogVisible=t}}}):e._e(),e.selectModelDialogVisible?t("select-model-dialog",{ref:"selectModelDialog",attrs:{isshow:e.selectModelDialogVisible,formDataDialog:e.selectModelDialogInfo,width:"900px"},on:{"update:isshow":function(t){e.selectModelDialogVisible=t},clickSelect:e.clickSelect}}):e._e()],1)},i=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"meal-type"},[t("div")])}],o=r("5a0c"),a=r("663f"),s=r("b9c1"),c=r("ed08"),u=r("c9d9");function l(e){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},l(e)}function f(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */f=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,i=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function d(e,t,r,n){var o=t&&t.prototype instanceof b?t:b,a=Object.create(o.prototype),s=new C(n||[]);return i(a,"_invoke",{value:P(e,r,s)}),a}function p(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var h="suspendedStart",y="suspendedYield",m="executing",v="completed",g={};function b(){}function w(){}function _(){}var k={};u(k,a,(function(){return this}));var x=Object.getPrototypeOf,D=x&&x(x(N([])));D&&D!==r&&n.call(D,a)&&(k=D);var L=_.prototype=b.prototype=Object.create(k);function O(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function M(e,t){function r(i,o,a,s){var c=p(e[i],e,o);if("throw"!==c.type){var u=c.arg,f=u.value;return f&&"object"==l(f)&&n.call(f,"__await")?t.resolve(f.__await).then((function(e){r("next",e,a,s)}),(function(e){r("throw",e,a,s)})):t.resolve(f).then((function(e){u.value=e,a(u)}),(function(e){return r("throw",e,a,s)}))}s(c.arg)}var o;i(this,"_invoke",{value:function(e,n){function i(){return new t((function(t,i){r(e,n,t,i)}))}return o=o?o.then(i,i):i()}})}function P(t,r,n){var i=h;return function(o,a){if(i===m)throw Error("Generator is already running");if(i===v){if("throw"===o)throw a;return{value:e,done:!0}}for(n.method=o,n.arg=a;;){var s=n.delegate;if(s){var c=j(s,n);if(c){if(c===g)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===h)throw i=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=m;var u=p(t,r,n);if("normal"===u.type){if(i=n.done?v:y,u.arg===g)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(i=v,n.method="throw",n.arg=u.arg)}}}function j(t,r){var n=r.method,i=t.iterator[n];if(i===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,j(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var o=p(i,t.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,g;var a=o.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,g):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function S(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function C(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(S,this),this.reset(!0)}function N(t){if(t||""===t){var r=t[a];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,o=function r(){for(;++i<t.length;)if(n.call(t,i))return r.value=t[i],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(l(t)+" is not iterable")}return w.prototype=_,i(L,"constructor",{value:_,configurable:!0}),i(_,"constructor",{value:w,configurable:!0}),w.displayName=u(_,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,u(e,c,"GeneratorFunction")),e.prototype=Object.create(L),e},t.awrap=function(e){return{__await:e}},O(M.prototype),u(M.prototype,s,(function(){return this})),t.AsyncIterator=M,t.async=function(e,r,n,i,o){void 0===o&&(o=Promise);var a=new M(d(e,r,n,i),o);return t.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},O(L),u(L,c,"Generator"),u(L,a,(function(){return this})),u(L,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=N,C.prototype={constructor:C,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(E),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function i(n,i){return s.type="throw",s.arg=t,r.next=n,i&&(r.method="next",r.arg=e),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),u=n.call(a,"finallyLoc");if(c&&u){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),E(r),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;E(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:N(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),g}},t}function d(e,t){return v(e)||m(e,t)||h(e,t)||p()}function p(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function h(e,t){if(e){if("string"==typeof e)return y(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?y(e,t):void 0}}function y(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function m(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,o,a,s=[],c=!0,u=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(e){u=!0,i=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw i}}return s}}function v(e){if(Array.isArray(e))return e}function g(e,t,r,n,i,o,a){try{var s=e[o](a),c=s.value}catch(e){return void r(e)}s.done?t(c):Promise.resolve(c).then(n,i)}function b(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var o=e.apply(t,r);function a(e){g(o,n,i,a,s,"next",e)}function s(e){g(o,n,i,a,s,"throw",e)}a(void 0)}))}}var w={name:"AddMonthRecipes",components:{menuPreviewDialog:a["default"],selectModelDialog:s["default"]},props:{menuType:{type:String,default:""},menuId:{type:String,default:""}},data:function(){return{dayValue:"",pickerOptions:this.disabledDate(),isLoading:!1,currentDate:"",copyDateVal:[],showCopyDialog:!1,currentMonth:"",currentYear:"",border:"1px solid #ebeef5",dayList:[],weekDays:["星期一","星期二","星期三","星期四","星期五","星期六","星期日"],isNutritionGuidance:!1,markerList:[{key:"insufficientTotal",className:"marker-primary",label:"不足"},{key:"suitableTotal",className:"marker-secondary",label:"适宜"},{key:"overdoseTotal",className:"marker-thridary",label:"过量"}],menuPreviewDialogVisible:!1,dialogMenuPeviewInfo:{},selectModelDialogVisible:!1,selectModelDialogInfo:{}}},mounted:function(){this.initLoad()},methods:{disabledDate:function(){var e=this;return{disabledDate:function(t){var r=new Date(e.dayValue).getMonth(),n=new Date(t).getMonth();return r!==n}}},mealTypeName:function(e){var t="";return u["a"].forEach((function(r){r.value===e&&(t=r.label)})),t},initLoad:function(){this.initDayList()},clickMenuPreview:function(){this.menuPreviewDialogVisible=!0},switchGuidance:function(e){this.selectModelDialogInfo={menuType:this.menuType,menuId:this.menuId},this.isNutritionGuidance=!1,e?this.selectModelDialogVisible=!0:this.getModifyNutritionGuidance()},getModifyNutritionGuidance:function(){var e=this;return b(f().mark((function t(){var r,n,i,o,a;return f().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return r={id:e.menuId,operate:0,menu_type:e.menuType},e.isLoading=!0,t.next=4,Object(c["Z"])(e.$apis.apiBackgroundFoodMenuModifyNutritionGuidancePost(r));case 4:if(n=t.sent,i=d(n,2),o=i[0],a=i[1],e.isLoading=!1,!o){t.next=12;break}return e.$message.error(o.message),t.abrupt("return");case 12:0===a.code?(e.$message.success("关闭营养指导"),e.initLoad()):e.$message.error(a.msg);case 13:case"end":return t.stop()}}),t)})))()},initDayList:function(){var e=this;return b(f().mark((function t(){var r,n,i,a,s,u,l,p,h,y,m,v,g;return f().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(r=Number(e.$route.query.id),r){t.next=4;break}return e.$message.error("id获取失败"),t.abrupt("return");case 4:return e.isLoading=!0,t.next=7,Object(c["Z"])(e.$apis.apiBackgroundFoodMenuMonthlyMonthlyDetailPost({id:r}));case 7:if(n=t.sent,i=d(n,2),a=i[0],s=i[1],e.isLoading=!1,!a){t.next=15;break}return e.$message.error(a.message),t.abrupt("return");case 15:if(0!==s.code){t.next=38;break}for(u=Object.keys(s.data.daily_data),l=o(u[0]).daysInMonth(),e.currentMonth=o(u[0]).format("M"),e.currentYear=o(u[0]).format("YYYY")+"年"+o(u[0]).format("MM")+"月",sessionStorage.setItem("mealDailyData",JSON.stringify(s.data.daily_data)),p=[],h=u[0],y=o(o(h).startOf("month")).day(),0===y&&(y=7),e.isNutritionGuidance=s.data.is_nutrition_guidance,e.$emit("guidanceChange",e.isNutritionGuidance),m=1;m<y;m++)p.push("");v=f().mark((function e(){var t,r,n,i;return f().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t=s.data.daily_data[u[g-1]].foods,r=0,n=0,i=0,t.forEach((function(e){var t=e.total_nutrition;for(var o in t)t[o]>=120?i+=1:t[o]>=80?n+=1:t[o]<80&&(r+=1)})),p.push({dayNumber:g<10?"0".concat(g):g,data:s.data.daily_data[u[g-1]],insufficientTotal:r,suitableTotal:n,overdoseTotal:i,menu_date:u[g-1]});case 6:case"end":return e.stop()}}),e)})),g=1;case 30:if(!(g<=l)){t.next=35;break}return t.delegateYield(v(),"t0",32);case 32:g++,t.next=30;break;case 35:e.dayList=p,t.next=39;break;case 38:e.$message.error(s.msg);case 39:case"end":return t.stop()}}),t)})))()},openCopyDialog:function(e){if(""!==this.currentYear){var t=this.currentYear;t+="".concat(e.dayNumber,"日"),this.currentDate=t,this.copyDateVal=[],this.dayValue=e.menu_date}this.showCopyDialog=!0},openEditDialog:function(e){var t=e.menu_date;t=t.replace("年","-").replace("月","-"),this.$router.push({name:"MerchantAddMealMonthRecipes",query:{guidance:this.guidance,menuType:this.menuType,menuId:this.menuId,monthDate:t,isNutritionGuidance:this.isNutritionGuidance?"guidance":"false"}})},tabClick:function(e){this.foodMenuType=e},handleCopy:function(){var e=this;return b(f().mark((function t(){var r,n;return f().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return r={id:e.$route.query.id,copy_date:e.currentDate.replace("年","-").replace("月","-").replace("日",""),dates:e.copyDateVal.map((function(e){return o(e).format("YYYY-MM-DD")}))},t.next=3,e.$apis.apiBackgroundFoodMenuMonthlyCopyDayFoodPost(r);case 3:n=t.sent,0===n.code?(e.$message.success("操作成功"),e.showCopyDialog=!1,e.initDayList()):e.$message.error(n.msg);case 5:case"end":return t.stop()}}),t)})))()},clickSelect:function(e){this.selectModelDialogVisible=!1,this.initLoad()},cateringFoodNumber:function(e){var t=0,r=0;return e&&e.length&&e.forEach((function(e){t+=e.food_count,r+=e.set_meal_count})),t+r}}},_=w,k=(r("ccd9"),r("2877")),x=Object(k["a"])(_,n,i,!1,null,null,null);t["default"]=x.exports},"4ff8":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"page"},[t("div",{staticClass:"container-wrapper"},[t("div",{staticClass:"table-wrapper"},[t("div",{staticClass:"ps-flex-bw p-t-20 p-l-20 p-b-20 p-r-20"},[t("div",{staticClass:"tab"},[t("div",{class:["tab-item","foodSeting"===e.foodMenuType?"active":""],on:{click:function(t){return e.tabClick("foodSeting")}}},[e._v(" 菜品配置 ")]),e.isNutritionGuidance?t("div",{class:["tab-item","nutritionAnalysis"===e.foodMenuType?"active":""],on:{click:function(t){return e.tabClick("nutritionAnalysis")}}},[e._v(" 营养分析 ")]):e._e()]),t("div",["foodSeting"===e.foodMenuType?t("button-icon",{attrs:{color:"plain",type:"Import"},on:{click:function(t){return e.openImport()}}},[e._v(" 导入月菜谱 ")]):e._e(),"foodSeting"===e.foodMenuType?t("el-button",{attrs:{size:"mini",color:"plain",icon:"el-icon-view"},on:{click:e.clickMenuPreview}},[e._v(" 预览菜谱 ")]):e._e()],1)]),"foodSeting"===e.foodMenuType?t("month-menu-food-seting",{attrs:{menuType:e.menu_type,menuId:e.menu_id,monthDate:e.monthDate},on:{guidanceChange:e.guidanceChange}}):e._e(),"nutritionAnalysis"===e.foodMenuType?t("menu-nutrition-analysis",{attrs:{menuType:e.menu_type,menuId:e.menu_id}}):e._e()],1)]),e.menuPreviewDialogVisible?t("menu-preview-dialog",{ref:"menuPreviewDialog",attrs:{isshow:e.menuPreviewDialogVisible,formDataDialog:e.dialogMenuPeviewInfo,width:"900px"},on:{"update:isshow":function(t){e.menuPreviewDialogVisible=t}}}):e._e()],1)},i=[],o=r("497c"),a=r("663f"),s=r("f60f"),c={name:"AddMonthRecipesSeparate",mounted:function(){this.initLoad()},components:{monthMenuFoodSeting:o["default"],menuPreviewDialog:a["default"],menuNutritionAnalysis:s["default"]},data:function(){return{menuPreviewDialogVisible:!1,dialogMenuPeviewInfo:{},foodMenuType:"foodSeting",menu_type:"",menu_id:"",monthDate:"",isNutritionGuidance:!1}},methods:{guidanceChange:function(e){this.isNutritionGuidance=e},initLoad:function(){this.menu_type=this.$route.query.menu_type,this.menu_id=String(this.$route.query.id),this.monthDate=this.$route.query.date},clickMenuPreview:function(){this.menuPreviewDialogVisible=!0,this.dialogMenuPeviewInfo={menuId:this.menu_id,menuType:this.menu_type}},tabClick:function(e){this.foodMenuType=e},openImport:function(){this.$router.push({name:"MerchantImportRecipesSeparate",query:{type:"month",menu:this.menu_id}})}}},u=c,l=(r("b6a6"),r("2877")),f=Object(l["a"])(u,n,i,!1,null,"ac54534c",null);t["default"]=f.exports},"663f":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("dialog-message",{attrs:{show:e.visible,title:e.title,customClass:"ps-dialog",width:e.width,height:200},on:{"update:show":function(t){e.visible=t},close:e.handleClose}},[t("div",{staticClass:"menu-preview-dialog"},[t("div",[t("span",{staticStyle:{"font-weight":"bold"}},[e._v(e._s(e.dateMenuPreview))])]),t("div",{staticClass:"tab-box"},[t("div",{class:["tab-item","detailed"===e.tabsType?"active":""],on:{click:function(t){return e.tabClick("detailed")}}},[e._v(" 详细数据 ")]),t("div",{class:["tab-item","basic"===e.tabsType?"active":""],on:{click:function(t){return e.tabClick("basic")}}},[e._v(" 基本数据 ")])]),"detailed"===e.tabsType?t("div",[t("span",{staticClass:"food-total"},[e._v("菜品总数："+e._s(e.menuPreviewInfo.food_count))]),t("span",{staticClass:"food-total"},[e._v("菜品总重量（g）："+e._s(e.menuPreviewInfo.total_food_weight))]),t("span",{staticClass:"food-total"},[e._v(" 食材总重量（g）："+e._s(e.menuPreviewInfo.total_ingredient_weight)+" ")]),t("span",{staticClass:"food-total"},[e._v(" 成本合计：¥"+e._s(e._f("formatMoney")(e.menuPreviewInfo.total_origin_price))+" ")]),t("span",{staticClass:"food-total"},[e._v(" 菜品合计：¥"+e._s(e._f("formatMoney")(e.menuPreviewInfo.total_food_price))+" ")])]):e._e(),"basic"===e.tabsType?t("div"):e._e(),"detailed"===e.tabsType?t("div",{staticClass:"m-t-10"},[t("custom-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{border:"","table-data":e.tableData,"table-setting":e.tableSetting,stripe:"","span-method":e.objectSpanMethod,"header-row-class-name":"ps-table-header-row"}})],1):e._e(),"basic"===e.tabsType?t("div",{staticClass:"table-content m-t-10"},[t("custom-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"basicTableData",staticStyle:{width:"100%"},attrs:{border:"","table-data":e.basicTableData,"table-setting":e.tableBasicSetting,stripe:"","header-row-class-name":"ps-table-header-row"}})],1):e._e()]),t("template",{slot:"tool"},[t("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:e.clickCancleHandle}},[e._v(" 取消 ")]),t("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.gotoExport}},[e._v(" 导出数据 ")])],1)])],2)},i=[],o=r("cc06"),a=r("ed08"),s=r("c9d9"),c=r("f63a");function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}function l(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */l=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,i=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function f(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{f({},"")}catch(e){f=function(e,t,r){return e[t]=r}}function d(e,t,r,n){var o=t&&t.prototype instanceof b?t:b,a=Object.create(o.prototype),s=new C(n||[]);return i(a,"_invoke",{value:P(e,r,s)}),a}function p(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var h="suspendedStart",y="suspendedYield",m="executing",v="completed",g={};function b(){}function w(){}function _(){}var k={};f(k,a,(function(){return this}));var x=Object.getPrototypeOf,D=x&&x(x(N([])));D&&D!==r&&n.call(D,a)&&(k=D);var L=_.prototype=b.prototype=Object.create(k);function O(e){["next","throw","return"].forEach((function(t){f(e,t,(function(e){return this._invoke(t,e)}))}))}function M(e,t){function r(i,o,a,s){var c=p(e[i],e,o);if("throw"!==c.type){var l=c.arg,f=l.value;return f&&"object"==u(f)&&n.call(f,"__await")?t.resolve(f.__await).then((function(e){r("next",e,a,s)}),(function(e){r("throw",e,a,s)})):t.resolve(f).then((function(e){l.value=e,a(l)}),(function(e){return r("throw",e,a,s)}))}s(c.arg)}var o;i(this,"_invoke",{value:function(e,n){function i(){return new t((function(t,i){r(e,n,t,i)}))}return o=o?o.then(i,i):i()}})}function P(t,r,n){var i=h;return function(o,a){if(i===m)throw Error("Generator is already running");if(i===v){if("throw"===o)throw a;return{value:e,done:!0}}for(n.method=o,n.arg=a;;){var s=n.delegate;if(s){var c=j(s,n);if(c){if(c===g)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===h)throw i=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=m;var u=p(t,r,n);if("normal"===u.type){if(i=n.done?v:y,u.arg===g)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(i=v,n.method="throw",n.arg=u.arg)}}}function j(t,r){var n=r.method,i=t.iterator[n];if(i===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,j(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var o=p(i,t.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,g;var a=o.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,g):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function S(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function C(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(S,this),this.reset(!0)}function N(t){if(t||""===t){var r=t[a];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,o=function r(){for(;++i<t.length;)if(n.call(t,i))return r.value=t[i],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(u(t)+" is not iterable")}return w.prototype=_,i(L,"constructor",{value:_,configurable:!0}),i(_,"constructor",{value:w,configurable:!0}),w.displayName=f(_,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,f(e,c,"GeneratorFunction")),e.prototype=Object.create(L),e},t.awrap=function(e){return{__await:e}},O(M.prototype),f(M.prototype,s,(function(){return this})),t.AsyncIterator=M,t.async=function(e,r,n,i,o){void 0===o&&(o=Promise);var a=new M(d(e,r,n,i),o);return t.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},O(L),f(L,c,"Generator"),f(L,a,(function(){return this})),f(L,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=N,C.prototype={constructor:C,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(E),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function i(n,i){return s.type="throw",s.arg=t,r.next=n,i&&(r.method="next",r.arg=e),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),u=n.call(a,"finallyLoc");if(c&&u){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),E(r),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;E(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:N(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),g}},t}function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach((function(t){p(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function p(e,t,r){return(t=h(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function h(e){var t=y(e,"string");return"symbol"==u(t)?t:t+""}function y(e,t){if("object"!=u(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=u(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function m(e,t){return _(e)||w(e,t)||g(e,t)||v()}function v(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function g(e,t){if(e){if("string"==typeof e)return b(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?b(e,t):void 0}}function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function w(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,o,a,s=[],c=!0,u=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(e){u=!0,i=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw i}}return s}}function _(e){if(Array.isArray(e))return e}function k(e,t,r,n,i,o,a){try{var s=e[o](a),c=s.value}catch(e){return void r(e)}s.done?t(c):Promise.resolve(c).then(n,i)}function x(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var o=e.apply(t,r);function a(e){k(o,n,i,a,s,"next",e)}function s(e){k(o,n,i,a,s,"throw",e)}a(void 0)}))}}var D={name:"menuPreviewDialog",mixins:[c["a"]],props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:"预览菜谱"},width:{type:String,default:"600px"},isshow:Boolean,confirm:Function,formDataDialog:{type:Object,default:function(){return{}}}},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},data:function(){return{isLoading:!1,tabsType:"detailed",tableData:[],menuPreviewInfo:{},tableSetting:[{label:"日期",key:"weekDate",width:"160"},{label:"餐段",key:"mealName"},{label:"菜品名称",key:"foodName"},{label:"食材组成（g）",key:"ingredientName"},{label:"成本价",key:"origin_price",type:"money"},{label:"菜品/商品价",key:"food_price",type:"money"}],tableBasicSetting:[{label:"日期",key:"weekDate",width:"160"},{label:"早餐",key:"breakfast"},{label:"午餐",key:"lunch"},{label:"下午茶",key:"afternoon"},{label:"晚餐",key:"dinner"},{label:"夜宵",key:"supper"},{label:"凌晨餐",key:"morning"}],mergeOpts:{useKeyList:{weekDate:["mealName"]},mergeKeyList:["weekDate"]},basicTableData:[],printType:"menuPreviewDetailed",dateMenuPreview:""}},mounted:function(){},created:function(){this.getMenuPreview()},methods:{clickConfirmHandle:function(){this.visible=!1},clickCancleHandle:function(){this.visible=!1},handleClose:function(e){this.visible=!1},tabClick:function(e){this.tabsType=e,"detailed"===e?this.getMenuPreview():this.getFoodMenuPreview()},getMenuPreview:function(){var e=this;return x(l().mark((function t(){var r,n,i,c,u,f,p,h,y;return l().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(a["Z"])(e.$apis.apiBackgroundFoodMenuMenuPreviewPost({id:e.formDataDialog.menuId,menu_type:e.formDataDialog.menuType}));case 3:if(r=t.sent,n=m(r,2),i=n[0],c=n[1],e.isLoading=!1,!i){t.next=11;break}return e.$message.error(i.message),t.abrupt("return");case 11:if(0!==c.code){t.next=32;break}e.menuPreviewInfo=c.data.detailed,u=c.data.detailed.detailed_food_data,u&&Object.keys(u).length&&(e.dateMenuPreview="".concat(Object.keys(u)[0],"至").concat(Object.keys(u)[Object.keys(u).length-1])),f=[],t.t0=l().keys(u);case 17:if((t.t1=t.t0()).done){t.next=28;break}p=t.t1.value,h=l().mark((function e(t){var r;return l().wrap((function(e){while(1)switch(e.prev=e.next){case 0:r={date:p,weekDate:"星期".concat(Object(a["M"])(new Date(p),"{a}"),"(").concat(p,")"),mealName:"",mealSection:"",ingredientName:"",foodName:""},r.mealSection=t,r.mealName=s["a"].find((function(e){return e.value===t})).label,u[p][t].forEach((function(e){r.foodName=e.food_name+"("+e.weight+"g)";var t=[];e.ingredient_info_list.forEach((function(e){t.push(e.ingredient_name+":"+e.ingredient_weight)})),r.ingredientName=t.join("、"),f.push(d(d({},r),e))}));case 4:case"end":return e.stop()}}),e)})),t.t2=l().keys(u[p]);case 21:if((t.t3=t.t2()).done){t.next=26;break}return y=t.t3.value,t.delegateYield(h(y),"t4",24);case 24:t.next=21;break;case 26:t.next=17;break;case 28:e.tableData=f,e.rowMergeArrs=Object(o["a"])(e.tableData,e.mergeOpts),t.next=33;break;case 32:e.$message.error(c.msg);case 33:case"end":return t.stop()}}),t)})))()},getFoodMenuPreview:function(){var e=this;return x(l().mark((function t(){var r,n,i,o,s,c,u,f;return l().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(a["Z"])(e.$apis.apiBackgroundFoodMenuMenuFoodPreviewPost({id:e.formDataDialog.menuId,menu_type:e.formDataDialog.menuType}));case 3:if(r=t.sent,n=m(r,2),i=n[0],o=n[1],e.isLoading=!1,!i){t.next=11;break}return e.$message.error(i.message),t.abrupt("return");case 11:if(0!==o.code){t.next=25;break}s=o.data,s&&Object.keys(s).length&&(e.dateMenuPreview="".concat(Object.keys(s)[0],"至").concat(Object.keys(s)[Object.keys(s).length-1])),c=[],u=l().mark((function e(){var t;return l().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t={weekDate:"星期".concat(Object(a["M"])(new Date(f),"{a}"),"(").concat(f,")"),breakfast:"",lunch:"",afternoon:"",dinner:"",supper:"",morning:""},s[f].forEach((function(e){t[e.meal_type]=e.food_data.join("、")})),c.push(t);case 3:case"end":return e.stop()}}),e)})),t.t0=l().keys(s);case 17:if((t.t1=t.t0()).done){t.next=22;break}return f=t.t1.value,t.delegateYield(u(),"t2",20);case 20:t.next=17;break;case 22:e.basicTableData=c,t.next=26;break;case 25:e.$message.error(o.msg);case 26:case"end":return t.stop()}}),t)})))()},objectSpanMethod:function(e){e.row;var t=e.column,r=e.rowIndex,n=e.columnIndex,i=Object.keys(this.mergeOpts.useKeyList),a=this.mergeOpts.useKeyList&&i.length;if(a)for(var s in this.mergeOpts.useKeyList)if(this.mergeOpts.useKeyList[s].includes(t.property))return Object(o["b"])(this.rowMergeArrs,t.property,r,n);if(this.mergeOpts.mergeKeyList&&this.mergeOpts.mergeKeyList.length&&this.mergeOpts.mergeKeyList.includes(t.property))return Object(o["b"])(this.rowMergeArrs,t.property,r,n)},gotoExport:function(){var e={type:"",params:{id:this.formDataDialog.menuId,menu_type:this.formDataDialog.menuType}};"detailed"===this.tabsType?e.type="ExportMenuPreview":"basic"===this.tabsType&&(e.type="ExportMenuFoodPreview"),this.exportHandle(e)}}},L=D,O=(r("a8d9"),r("2877")),M=Object(O["a"])(L,n,i,!1,null,"79de482c",null);t["default"]=M.exports},a8d9:function(e,t,r){"use strict";r("df24")},b697:function(e,t,r){},b6a6:function(e,t,r){"use strict";r("b697")},ccd9:function(e,t,r){"use strict";r("22ec")},df24:function(e,t,r){}}]);