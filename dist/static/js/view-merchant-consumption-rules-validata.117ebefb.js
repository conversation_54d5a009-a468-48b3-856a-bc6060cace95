(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-consumption-rules-validata"],{8946:function(r,n,t){"use strict";t.r(n),t.d(n,"validateTelphone",(function(){return e})),t.d(n,"validataPrice",(function(){return i})),t.d(n,"validataPriceCanEmpty",(function(){return o})),t.d(n,"validateNumber",(function(){return a}));var e=function(r,n,t){if(!n)return t(new Error("手机号不能为空"));var e=/^1[3456789]\d{9}$/;e.test(n)?t():t(new Error("请输入正确手机号"))},i=function(r,n,t){if(!n)return t(new Error("金额有误"));var e=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;e.test(n)?t():t(new Error("金额格式有误"))},o=function(r,n,t){if(!n)return t();var e=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;e.test(n)?t():t(new Error("金额格式有误"))},a=function(r,n,t){if(""===n)return t(new Error("不能为空"));var e=/^\d+$/;e.test(n)?t():t(new Error("请输入正确数字"))}}}]);