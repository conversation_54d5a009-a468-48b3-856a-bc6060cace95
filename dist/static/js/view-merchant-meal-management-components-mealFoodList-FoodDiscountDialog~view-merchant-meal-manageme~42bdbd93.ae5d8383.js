(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-components-mealFoodList-FoodDiscountDialog~view-merchant-meal-manageme~42bdbd93"],{"468a":function(t,e,r){"use strict";r("cd52")},"9dfe":function(t,e,r){"use strict";r.r(e);var o=function(){var t=this,e=t._self._c;return e("div",{staticClass:"food-discount-dialog"},[e("el-dialog",{attrs:{title:"优惠",visible:t.showDialog,width:"700px","custom-class":"ps-dialog"},on:{"update:visible":function(e){t.showDialog=e}}},[e("el-form",{ref:"dialogForm",staticClass:"dialog-form",staticStyle:{overflow:"auto",height:"540px"},attrs:{model:t.dialogForm,"status-icon":"",rules:t.dialogFormRules,"label-width":"120px"},nativeOn:{submit:function(t){t.preventDefault()}}},[e("div",{staticClass:"title"},[t._v("优惠")]),t._l(t.dialogForm.discountData,(function(r,o){return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.foodBatchAddDiscountLoading,expression:"foodBatchAddDiscountLoading"}],key:o,staticStyle:{"padding-bottom":"10px"}},[e("div",{staticClass:"discount-box"},["add"==t.showDiscountDialogType?e("div",{staticStyle:{padding:"20px"}},[1!=t.dialogForm.discountData.length?e("el-button",{staticClass:"ps-warn-btn",attrs:{type:"primary"},on:{click:function(e){return e.preventDefault(),t.deleteFunList(o)}}},[t._v(" 删除 ")]):t._e(),o==t.dialogForm.discountData.length-1?e("el-button",{staticClass:"ps-blue",attrs:{type:"primary"},on:{click:function(e){return e.preventDefault(),t.addFunList()}}},[t._v(" 新增 ")]):t._e()],1):t._e(),e("div",{staticClass:"add-form-wrapper"},[e("el-form-item",{attrs:{label:"优惠类型：",rules:t.dialogFormRules.discountType,prop:"discountData["+o+"].discountType","label-width":"100px"}},[e("el-select",{staticClass:"ps-select",attrs:{placeholder:"请下拉选择分类","popper-class":"ps-popper-select","collapse-tags":"",clearable:""},model:{value:r.discountType,callback:function(e){t.$set(r,"discountType",e)},expression:"item.discountType"}},t._l(t.discountTypeList,(function(t){return e("el-option",{key:t.key,attrs:{label:t.name,value:t.key}})})),1)],1),e("el-form-item",{attrs:{label:"优惠价格：",rules:t.dialogFormRules.discountPirce,prop:"discountData["+o+"].discountPirce"}},[e("el-input",{attrs:{placeholder:"请输入价格"},model:{value:r.discountPirce,callback:function(e){t.$set(r,"discountPirce",e)},expression:"item.discountPirce"}})],1),e("el-form-item",{attrs:{label:"",rules:t.dialogFormRules.countType,prop:"discountData["+o+"].countType","label-width":"100px"}},[e("el-select",{staticClass:"ps-select",attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select","collapse-tags":"",clearable:""},model:{value:r.countType,callback:function(e){t.$set(r,"countType",e)},expression:"item.countType"}},t._l(t.countTypeList,(function(t){return e("el-option",{key:t.key,attrs:{label:t.name,value:t.key}})})),1)],1)],1),e("el-form-item",{attrs:{label:"优惠时间：",rules:t.dialogFormRules.discountDate,prop:"discountData["+o+"].discountDate","label-width":"100px"}},[e("el-date-picker",{attrs:{type:"daterange",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:r.discountDate,callback:function(e){t.$set(r,"discountDate",e)},expression:"item.discountDate"}})],1),e("el-form-item",{attrs:{label:"人员分组：",rules:t.dialogFormRules.groupType,prop:"discountData["+o+"].groupType","label-width":"100px"}},[e("el-select",{staticClass:"ps-select",attrs:{placeholder:"请下拉选择分组","popper-class":"ps-popper-select",multiple:"","collapse-tags":"",clearable:""},model:{value:r.groupType,callback:function(e){t.$set(r,"groupType",e)},expression:"item.groupType"}},t._l(t.groupList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.group_name,value:t.id}})})),1)],1),e("el-form-item",{attrs:{label:"优惠形式：",rules:t.dialogFormRules.discountMode,prop:"discountData["+o+"].discountMode","label-width":"100px"}},[e("el-select",{staticClass:"ps-select",attrs:{placeholder:"请下拉选择形式","popper-class":"ps-popper-select","collapse-tags":"",clearable:""},model:{value:r.discountMode,callback:function(e){t.$set(r,"discountMode",e)},expression:"item.discountMode"}},t._l(t.discountModeList,(function(t){return e("el-option",{key:t.key,attrs:{label:t.name,value:t.key}})})),1)],1),e("el-form-item",{attrs:{label:"优惠餐段：",rules:t.dialogFormRules.mealType,prop:"discountData["+o+"].mealType","label-width":"100px"}},[e("el-select",{staticClass:"ps-select",attrs:{placeholder:"请下拉选择餐段","popper-class":"ps-popper-select",multiple:"","collapse-tags":"",clearable:""},model:{value:r.mealType,callback:function(e){t.$set(r,"mealType",e)},expression:"item.mealType"}},t._l(t.mealTypeList,(function(t){return e("el-option",{key:t.key,attrs:{label:t.name,value:t.key}})})),1)],1)],1)])}))],2),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"ps-cancel-btn",on:{click:t.cancel}},[t._v("取 消")]),"add"==t.showDiscountDialogType?e("el-button",{staticClass:"ps-blue",attrs:{type:"primary",loading:t.foodBatchAddDiscountLoading},on:{click:function(e){return t.determineOrganization("disable")}}},[t._v(" 保存退出 ")]):t._e(),"add"==t.showDiscountDialogType?e("el-button",{staticClass:"ps-btn",attrs:{type:"primary",loading:t.foodBatchAddDiscountLoading},on:{click:function(e){return t.determineOrganization("enable")}}},[t._v(" 立即启用 ")]):t._e()],1)],1)],1)},n=[],a=r("ed08"),i=r("d0dd");function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function(){return e};var t,e={},r=Object.prototype,o=r.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",l=a.toStringTag||"@@toStringTag";function d(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{d({},"")}catch(t){d=function(t,e,r){return t[e]=r}}function p(t,e,r,o){var a=e&&e.prototype instanceof b?e:b,i=Object.create(a.prototype),s=new A(o||[]);return n(i,"_invoke",{value:O(t,r,s)}),i}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=p;var h="suspendedStart",y="suspendedYield",m="executing",g="completed",v={};function b(){}function w(){}function D(){}var L={};d(L,i,(function(){return this}));var T=Object.getPrototypeOf,k=T&&T(T($([])));k&&k!==r&&o.call(k,i)&&(L=k);var x=D.prototype=b.prototype=Object.create(L);function _(t){["next","throw","return"].forEach((function(e){d(t,e,(function(t){return this._invoke(e,t)}))}))}function F(t,e){function r(n,a,i,c){var u=f(t[n],t,a);if("throw"!==u.type){var l=u.arg,d=l.value;return d&&"object"==s(d)&&o.call(d,"__await")?e.resolve(d.__await).then((function(t){r("next",t,i,c)}),(function(t){r("throw",t,i,c)})):e.resolve(d).then((function(t){l.value=t,i(l)}),(function(t){return r("throw",t,i,c)}))}c(u.arg)}var a;n(this,"_invoke",{value:function(t,o){function n(){return new e((function(e,n){r(t,o,e,n)}))}return a=a?a.then(n,n):n()}})}function O(e,r,o){var n=h;return function(a,i){if(n===m)throw Error("Generator is already running");if(n===g){if("throw"===a)throw i;return{value:t,done:!0}}for(o.method=a,o.arg=i;;){var s=o.delegate;if(s){var c=P(s,o);if(c){if(c===v)continue;return c}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(n===h)throw n=g,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);n=m;var u=f(e,r,o);if("normal"===u.type){if(n=o.done?g:y,u.arg===v)continue;return{value:u.arg,done:o.done}}"throw"===u.type&&(n=g,o.method="throw",o.arg=u.arg)}}}function P(e,r){var o=r.method,n=e.iterator[o];if(n===t)return r.delegate=null,"throw"===o&&e.iterator.return&&(r.method="return",r.arg=t,P(e,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+o+"' method")),v;var a=f(n,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,v;var i=a.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function A(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function $(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,a=function r(){for(;++n<e.length;)if(o.call(e,n))return r.value=e[n],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(s(e)+" is not iterable")}return w.prototype=D,n(x,"constructor",{value:D,configurable:!0}),n(D,"constructor",{value:w,configurable:!0}),w.displayName=d(D,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,D):(t.__proto__=D,d(t,l,"GeneratorFunction")),t.prototype=Object.create(x),t},e.awrap=function(t){return{__await:t}},_(F.prototype),d(F.prototype,u,(function(){return this})),e.AsyncIterator=F,e.async=function(t,r,o,n,a){void 0===a&&(a=Promise);var i=new F(p(t,r,o,n),a);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},_(x),d(x,l,"Generator"),d(x,i,(function(){return this})),d(x,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var o in e)r.push(o);return r.reverse(),function t(){for(;r.length;){var o=r.pop();if(o in e)return t.value=o,t.done=!1,t}return t.done=!0,t}},e.values=$,A.prototype={constructor:A,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var r in this)"t"===r.charAt(0)&&o.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function n(o,n){return s.type="throw",s.arg=e,r.next=o,n&&(r.method="next",r.arg=t),!!n}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],s=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=o.call(i,"catchLoc"),u=o.call(i,"finallyLoc");if(c&&u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&o.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var a=n;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=t,i.arg=e,a?(this.method="next",this.next=a.finallyLoc,v):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),j(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var o=r.completion;if("throw"===o.type){var n=o.arg;j(r)}return n}}throw Error("illegal catch attempt")},delegateYield:function(e,r,o){return this.delegate={iterator:$(e),resultName:r,nextLoc:o},"next"===this.method&&(this.arg=t),v}},e}function u(t,e){return h(t)||f(t,e)||d(t,e)||l()}function l(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(t,e){if(t){if("string"==typeof t)return p(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?p(t,e):void 0}}function p(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,o=Array(e);r<e;r++)o[r]=t[r];return o}function f(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var o,n,a,i,s=[],c=!0,u=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(o=a.call(r)).done)&&(s.push(o.value),s.length!==e);c=!0);}catch(t){u=!0,n=t}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw n}}return s}}function h(t){if(Array.isArray(t))return t}function y(t,e,r,o,n,a,i){try{var s=t[a](i),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(o,n)}function m(t){return function(){var e=this,r=arguments;return new Promise((function(o,n){var a=t.apply(e,r);function i(t){y(a,o,n,i,s,"next",t)}function s(t){y(a,o,n,i,s,"throw",t)}i(void 0)}))}}var g={props:{showFoodDiscountDialog:Boolean,showDiscountDialogType:String,selectListId:Array,formFoodDataDialog:{type:Object,default:function(){return{}}},confirm:Function},data:function(){return{isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[],dialogForm:{discountData:[{discountType:"",discountPirce:"",countType:"",mealType:[],discountMode:"",groupType:[],discountDate:[]}]},dialogFormRules:{discountType:[{required:!0,message:"请选择类型",trigger:"change"}],discountPirce:[{required:!0,validator:i["a"],message:"请输入价格",trigger:"blur"}],countType:[{required:!0,message:"请选择",trigger:"change"}],discountDate:[{required:!0,message:"请选择日期",trigger:"change"}],groupType:[{required:!0,message:"请选择分组",trigger:"change"}],discountMode:[{required:!0,message:"请选择形式",trigger:"change"}],mealType:[{required:!0,message:"请选择餐段",trigger:"change"}]},discountTypeList:[{name:"特价",key:1},{name:"折扣",key:2}],countTypeList:[{name:"菜品价格",key:1},{name:"称重价格",key:2}],mealTypeList:[{name:"早餐",key:"breakfast"},{name:"午餐",key:"lunch"},{name:"下午茶",key:"afternoon"},{name:"晚餐",key:"dinner"},{name:"夜宵",key:"supper"},{name:"凌晨餐",key:"morning"},{name:"全天",key:"whole"}],discountModeList:[{name:"线上",key:1},{name:"线下",key:2},{name:"线上+线下",key:3}],groupList:[],foodBatchAddDiscountLoading:!1}},computed:{showDialog:{get:function(){return this.showFoodDiscountDialog},set:function(t){this.$emit("update:showFoodDiscountDialog",t)}}},mounted:function(){},created:function(){this.userGroupList(),"history"===this.showDiscountDialogType&&this.getFoodListDiscount()},methods:{cancel:function(){this.showDialog=!1},getFoodListDiscount:function(){var t=this;return m(c().mark((function e(){var r,o,n,i;return c().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.foodBatchAddDiscountLoading=!0,e.next=3,Object(a["Z"])(t.$apis.apiBackgroundFoodFoodListDiscountPost({food_id:t.formFoodDataDialog.id,page_size:9999,page:1}));case 3:if(r=e.sent,o=u(r,2),n=o[0],i=o[1],t.foodBatchAddDiscountLoading=!1,!n){e.next=11;break}return t.$message.error(n.message),e.abrupt("return");case 11:0===i.code?(i.data.results&&i.data.results.length?t.dialogForm.discountData=i.data.results.map((function(t){var e={discountType:t.discount_type,discountPirce:Object(a["i"])(t.discount_fee),countType:t.count_type,mealType:t.meal_types,discountMode:t.discount_mode,groupType:t.user_groups,discountDate:[t.start_date,t.end_date]};return e})):t.dialogForm.discountData=[{discountType:"",discountPirce:"",countType:"",mealType:[],discountMode:"",groupType:[],discountDate:[]}],t.$message.success(i.msg)):t.$message.error(i.msg);case 12:case"end":return e.stop()}}),e)})))()},setFoodBatchAddDiscount:function(t){var e=this;return m(c().mark((function r(){var o,n,i,s,l;return c().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return o=e.dialogForm.discountData.map((function(t){var e={discount_type:t.discountType,discount_fee:Object(a["Y"])(t.discountPirce),count_type:t.countType,start_date:t.discountDate[0],end_date:t.discountDate[1],meal_types:t.mealType,user_groups:t.groupType,discount_mode:t.discountMode};return e})),e.foodBatchAddDiscountLoading=!0,r.next=4,Object(a["Z"])(e.$apis.apiBackgroundFoodFoodBatchAddDiscountPost({food_ids:e.selectListId,data:o,status:t}));case 4:if(n=r.sent,i=u(n,2),s=i[0],l=i[1],e.foodBatchAddDiscountLoading=!1,!s){r.next=12;break}return e.$message.error(s.message),r.abrupt("return");case 12:0===l.code?(e.$message.success(l.msg),e.showDialog=!1,e.$emit("confirm","search")):e.$message.error(l.msg);case 13:case"end":return r.stop()}}),r)})))()},determineOrganization:function(t){var e=this;this.$refs.dialogForm.validate((function(r){if(!r)return!1;e.setFoodBatchAddDiscount(t)}))},deleteFunList:function(t){this.dialogForm.discountData.splice(t,1)},addFunList:function(){this.dialogForm.discountData.push({discountType:"",discountPirce:"",countType:"",mealType:[],discountMode:"",groupType:[],discountDate:[]})},userGroupList:function(){var t=this;return m(c().mark((function e(){var r;return c().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.apiCardServiceCardUserGroupListPost({status:"enable",page:1,page_size:99999});case 2:r=e.sent,0===r.code?t.groupList=r.data.results:t.$message.error(r.msg);case 4:case"end":return e.stop()}}),e)})))()},foodAddDiscount:function(){var t=this;return m(c().mark((function e(){var r;return c().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.apiBackgroundFoodFoodAddDiscountPost({food_ids:"enable",page:1,page_size:99999});case 2:r=e.sent,0===r.code?t.groupList=r.data.results:t.$message.error(r.msg);case 4:case"end":return e.stop()}}),e)})))()}}},v=g,b=(r("468a"),r("2877")),w=Object(b["a"])(v,o,n,!1,null,"b30796ba",null);e["default"]=w.exports},cd52:function(t,e,r){}}]);