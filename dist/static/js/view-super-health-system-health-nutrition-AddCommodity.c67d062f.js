(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-health-nutrition-AddCommodity","view-super-health-system-components-selectLaber","view-super-health-system-health-nutrition-AddMerchantCommodityToSuper","view-super-health-system-health-nutrition-CommodityLibrary","view-super-health-system-health-nutrition-IngredientsCategory","view-super-health-system-health-nutrition-constants","view-super-health-system-threemeallist"],{"015b":function(t,e,a){"use strict";a.r(e),a.d(e,"DEFAULT_NUTRITION",(function(){return r})),a.d(e,"ELEMENT_NUTRITION",(function(){return i})),a.d(e,"VITAMIN_NUTRITION",(function(){return o})),a.d(e,"NUTRITION_LIST",(function(){return l})),a.d(e,"LIBRARY_SEARCH_SETTING_SUPER",(function(){return s})),a.d(e,"LIBRARY_SEARCH_SETTING_MERCHANT",(function(){return c})),a.d(e,"LIBRARY_SEARCH_SETTING_CATEORY",(function(){return u})),a.d(e,"COMMODITY_SEARCH_SETTING_SUPER",(function(){return d})),a.d(e,"COMMODITY_SEARCH_SETTING_MERCHANT",(function(){return f}));var n=a("ed08"),r=[{name:"千卡",key:"energy_kcal",unit:"Kcal",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{name:"胆固醇",key:"cholesterol",unit:"mg",type:"default"},{name:"膳食纤维",key:"dietary_fiber",unit:"g",type:"default"}],i=[{key:"Ca",name:"钙",unit:"mg",type:"element"},{key:"P",name:"磷",unit:"mg",type:"element"},{key:"K",name:"钾",unit:"mg",type:"element"},{key:"Na",name:"钠",unit:"mg",type:"element"},{name:"镁",key:"Mg",unit:"mg",type:"element"},{key:"Fe",name:"铁",unit:"mg",type:"element"},{key:"I",name:"碘",unit:"μg",type:"element"},{key:"Se",name:"硒",unit:"μg",type:"element"},{key:"Zn",name:"锌",unit:"mg",type:"element"},{key:"Cu",name:"铜",unit:"mg",type:"element"},{key:"F",name:"氟",unit:"mg",type:"element"},{key:"Cr",name:"铬",unit:"μg",type:"element"},{key:"Mo",name:"钼",unit:"μg",type:"element"},{key:"Mn",name:"锰",unit:"mg",type:"element"}],o=[{key:"VA",name:"维生素A",unit:"μg",type:"vitamin"},{key:"VD",name:"维生素D",unit:"μg",type:"vitamin"},{key:"VE",name:"维生素E",unit:"mg",type:"vitamin"},{key:"VK",name:"维生素K",unit:"μg",type:"vitamin"},{key:"VB1",name:"维生素B1",unit:"mg",type:"vitamin"},{key:"VB2",name:"维生素B2",unit:"mg",type:"vitamin"},{key:"VB6",name:"维生素B6",unit:"mg",type:"vitamin"},{key:"VB12",name:"维生素B12",unit:"μg",type:"vitamin"},{key:"VC",name:"维生素C",unit:"mg",type:"vitamin"},{key:"VB5",name:"泛酸",unit:"mg",type:"vitamin"},{key:"VM",name:"叶酸",unit:"μg",type:"vitamin"},{key:"VB3",name:"烟酸",unit:"mg",type:"vitamin"},{key:"Choline",name:" 胆碱",unit:"mg",type:"vitamin"},{key:"Nicotinamide",name:"烟酰胺",unit:"mg",type:"vitamin"},{key:"VH",name:"生物素",unit:"mg",type:"vitamin"}],l=[].concat(r,i,o),s={date_type:{type:"select",label:"",value:1,maxWidth:"130px",placeholder:"请选择",dataList:[{label:"创建时间",value:1},{label:"修改时间",value:2}]},select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"",clearable:!0,value:[]},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(t){return{id:t.level+"_"+t.id,label:t.name,children:t.sort_list}}},is_enable_nutrition:{type:"select",label:"营养信息",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"有",value:"1"},{label:"无",value:"0"}]},ingredient_name:{type:"input",label:"食材名称",value:"",placeholder:"请输入食材名称"},operator:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"},label_filter:{type:"select",label:"",value:"",placeholder:"",dataList:[{label:"请选择",value:"select",disabled:!0},{label:"包含",value:"Include"},{label:"不包含",value:"Exclude"}]},label_list:{type:"treeselect",label:"",value:[],placeholder:"选择标签",multiple:!0,limit:1,level:1,valueConsistsOf:"LEAF_PRIORITY",normalizer:function(t){return{id:t.id,label:t.name,children:t.label_list}},dataList:[]},is_entering:{type:"select",label:"录入图片",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"已录入",value:"1"},{label:"未录入",value:"0"}]}},c={select_time:{type:"datetimerange",label:"创建时间",format:"yyyy-MM-dd",value:Object(n["y"])(7)},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(t){return{id:t.level+"_"+t.id,label:t.name,children:t.sort_list}}},is_repeat:{type:"select",label:"已有食材",value:"",placeholder:"请选择是否已有食材",dataList:[{label:"是",value:1},{label:"否",value:0}]},is_enable_nutrition:{type:"select",label:"营养信息",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"有",value:"1"},{label:"无",value:"0"}]},ingredient_name:{type:"input",label:"食材名称",value:"",placeholder:"请输入食材名称"},is_entering:{type:"select",label:"录入图片",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"已录入",value:"1"},{label:"未录入",value:"0"}]}},u={select_time:{type:"datetimerange",label:"修改时间",format:"yyyy-MM-dd HH:mm:ss",value:[]},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(t){return{id:t.level+"_"+t.id,label:t.name,children:t.sort_list}}},user_name:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"}},d={date_type:{type:"select",label:"",value:1,maxWidth:"130px",placeholder:"请选择",dataList:[{label:"创建时间",value:1},{label:"修改时间",value:2}]},select_time:{type:"datetimerange",label:"",format:"yyyy-MM-dd HH:mm:ss",value:[]},food_name:{type:"input",label:"菜品/商品名称",value:"",placeholder:"请输入菜品/商品名称"},user_name:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"},attributes:{type:"select",label:"属性",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"商品",value:"goods"},{label:"菜品",value:"foods"}]},label_filter:{type:"select",label:"",value:"",placeholder:"",dataList:[{label:"请选择",value:"select",disabled:!0},{label:"包含",value:"Include"},{label:"不包含",value:"Exclude"}]},label_list:{type:"treeselect",label:"",value:[],placeholder:"选择标签",multiple:!0,limit:1,level:1,valueConsistsOf:"LEAF_PRIORITY",normalizer:function(t){return{id:t.id,label:t.name,children:t.label_list}},dataList:[]}},f={select_time:{type:"datetimerange",label:"创建时间",format:"yyyy-MM-dd",value:Object(n["y"])(7)},food_name:{type:"input",label:"菜品/商品名称",value:"",placeholder:"请输入菜品/商品名称"},is_repeat:{type:"select",label:"已有菜品/商品",value:"",placeholder:"请选择",dataList:[{label:"是",value:1},{label:"否",value:0}]},attributes:{type:"select",label:"属性",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"商品",value:"goods"},{label:"菜品",value:"foods"}]}}},"1a24":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("dialog-message",{attrs:{show:t.visible,title:t.title,loading:t.isLoading,customClass:"ps-dialog",width:t.width},on:{"update:show":function(e){t.visible=e},"update:loading":function(e){t.isLoading=e},close:t.handleClose}},[e("div",{staticClass:"healthTagDialog"},[e("el-input",{staticClass:"ps-input",attrs:{placeholder:"请输入要查询的标签组/标签名称名称"},on:{input:t.searchHandle},model:{value:t.name,callback:function(e){t.name=e},expression:"name"}}),e("el-card",{staticClass:"m-t-20",attrs:{shadow:"never"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v(" 已选 "),e("span",[t._v(t._s(t.selectLabelIdList.length))]),t._v(" 个标签 ")])]),t._l(t.tableData,(function(a,n){return e("div",{key:n},[e("el-collapse",{model:{value:t.activeLaberList,callback:function(e){t.activeLaberList=e},expression:"activeLaberList"}},[e("el-collapse-item",{attrs:{name:a.id}},[e("template",{slot:"title"},[e("span",[t._v(" "+t._s(a.name)+" "),e("span",[t._v("（"+t._s(a.label_list.length)+"）")])]),e("span",{staticClass:"tips-r"},[e("span",{staticClass:"open"},[t._v("展开")]),e("span",{staticClass:"close"},[t._v("收起")])])]),e("div",{staticClass:"ps-flex-align-c flex-align-c flex-wrap"},[a.inputVisible?e("el-input",{ref:"saveTagInput"+a.id,refInFor:!0,staticClass:"ps-input w-100 p-r-10 p-t-5",attrs:{size:"mini",autofocus:"",maxlength:"15"},on:{blur:function(e){return t.handleInputConfirm(a)}},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleInputConfirm(a)}},model:{value:a.inputValue,callback:function(e){t.$set(a,"inputValue",e)},expression:"item.inputValue"}}):e("button-icon",{staticClass:"p-t-5",attrs:{color:"origin",type:"add"},on:{click:function(e){return t.showInput(a)}}},[t._v(" 添加标签 ")]),e("div",{staticStyle:{flex:"1"}},[e("el-checkbox-group",{attrs:{size:"mini"},model:{value:t.selectLabelIdList,callback:function(e){t.selectLabelIdList=e},expression:"selectLabelIdList"}},t._l(a.label_list,(function(n,r){return e("el-checkbox-button",{key:r,attrs:{label:n.id,disabled:n.disabled},on:{change:function(e){return t.checkboxChangge(n,a)}}},[t._v(" "+t._s(n.name)+" ")])})),1)],1)],1)],2)],1)],1)}))],2)],1),e("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[e("el-pagination",{staticClass:"ps-text",attrs:{"page-size":t.pageSize,"current-page":t.currentPage,layout:"total, prev, pager, next",total:t.totalCount,background:"","popper-class":"ps-popper-select"},on:{"current-change":t.handleCurrentChange,"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e}}})],1),e("template",{slot:"tool"},[e("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:t.isLoading},on:{click:t.clickCancleHandle}},[t._v(" 取消 ")]),e("el-button",{staticClass:"ps-btn",attrs:{disabled:t.isLoading,type:"primary"},on:{click:t.clickConfirmHandle}},[t._v(" 保存 ")])],1)])],2)},r=[],i=a("ed08");function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function l(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */l=function(){return e};var t,e={},a=Object.prototype,n=a.hasOwnProperty,r=Object.defineProperty||function(t,e,a){t[e]=a.value},i="function"==typeof Symbol?Symbol:{},s=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function d(t,e,a){return Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{d({},"")}catch(t){d=function(t,e,a){return t[e]=a}}function f(t,e,a,n){var i=e&&e.prototype instanceof v?e:v,o=Object.create(i.prototype),l=new T(n||[]);return r(o,"_invoke",{value:S(t,a,l)}),o}function p(t,e,a){try{return{type:"normal",arg:t.call(e,a)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var m="suspendedStart",h="suspendedYield",g="executing",y="completed",b={};function v(){}function L(){}function _(){}var I={};d(I,s,(function(){return this}));var w=Object.getPrototypeOf,x=w&&w(w($([])));x&&x!==a&&n.call(x,s)&&(I=x);var k=_.prototype=v.prototype=Object.create(I);function D(t){["next","throw","return"].forEach((function(e){d(t,e,(function(t){return this._invoke(e,t)}))}))}function C(t,e){function a(r,i,l,s){var c=p(t[r],t,i);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==o(d)&&n.call(d,"__await")?e.resolve(d.__await).then((function(t){a("next",t,l,s)}),(function(t){a("throw",t,l,s)})):e.resolve(d).then((function(t){u.value=t,l(u)}),(function(t){return a("throw",t,l,s)}))}s(c.arg)}var i;r(this,"_invoke",{value:function(t,n){function r(){return new e((function(e,r){a(t,n,e,r)}))}return i=i?i.then(r,r):r()}})}function S(e,a,n){var r=m;return function(i,o){if(r===g)throw Error("Generator is already running");if(r===y){if("throw"===i)throw o;return{value:t,done:!0}}for(n.method=i,n.arg=o;;){var l=n.delegate;if(l){var s=E(l,n);if(s){if(s===b)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(r===m)throw r=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r=g;var c=p(e,a,n);if("normal"===c.type){if(r=n.done?y:h,c.arg===b)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r=y,n.method="throw",n.arg=c.arg)}}}function E(e,a){var n=a.method,r=e.iterator[n];if(r===t)return a.delegate=null,"throw"===n&&e.iterator.return&&(a.method="return",a.arg=t,E(e,a),"throw"===a.method)||"return"!==n&&(a.method="throw",a.arg=new TypeError("The iterator does not provide a '"+n+"' method")),b;var i=p(r,e.iterator,a.arg);if("throw"===i.type)return a.method="throw",a.arg=i.arg,a.delegate=null,b;var o=i.arg;return o?o.done?(a[e.resultName]=o.value,a.next=e.nextLoc,"return"!==a.method&&(a.method="next",a.arg=t),a.delegate=null,b):o:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,b)}function O(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function N(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(O,this),this.reset(!0)}function $(e){if(e||""===e){var a=e[s];if(a)return a.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,i=function a(){for(;++r<e.length;)if(n.call(e,r))return a.value=e[r],a.done=!1,a;return a.value=t,a.done=!0,a};return i.next=i}}throw new TypeError(o(e)+" is not iterable")}return L.prototype=_,r(k,"constructor",{value:_,configurable:!0}),r(_,"constructor",{value:L,configurable:!0}),L.displayName=d(_,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===L||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,d(t,u,"GeneratorFunction")),t.prototype=Object.create(k),t},e.awrap=function(t){return{__await:t}},D(C.prototype),d(C.prototype,c,(function(){return this})),e.AsyncIterator=C,e.async=function(t,a,n,r,i){void 0===i&&(i=Promise);var o=new C(f(t,a,n,r),i);return e.isGeneratorFunction(a)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},D(k),d(k,u,"Generator"),d(k,s,(function(){return this})),d(k,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),a=[];for(var n in e)a.push(n);return a.reverse(),function t(){for(;a.length;){var n=a.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=$,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(N),!e)for(var a in this)"t"===a.charAt(0)&&n.call(this,a)&&!isNaN(+a.slice(1))&&(this[a]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var a=this;function r(n,r){return l.type="throw",l.arg=e,a.next=n,r&&(a.method="next",a.arg=t),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],l=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var s=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(s&&c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(t,e){for(var a=this.tryEntries.length-1;a>=0;--a){var r=this.tryEntries[a];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,b):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),b},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.finallyLoc===t)return this.complete(a.completion,a.afterLoc),N(a),b}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.tryLoc===t){var n=a.completion;if("throw"===n.type){var r=n.arg;N(a)}return r}}throw Error("illegal catch attempt")},delegateYield:function(e,a,n){return this.delegate={iterator:$(e),resultName:a,nextLoc:n},"next"===this.method&&(this.arg=t),b}},e}function s(t,e){return p(t)||f(t,e)||u(t,e)||c()}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(t,e){if(t){if("string"==typeof t)return d(t,e);var a={}.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?d(t,e):void 0}}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,n=Array(e);a<e;a++)n[a]=t[a];return n}function f(t,e){var a=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=a){var n,r,i,o,l=[],s=!0,c=!1;try{if(i=(a=a.call(t)).next,0===e){if(Object(a)!==a)return;s=!1}else for(;!(s=(n=i.call(a)).done)&&(l.push(n.value),l.length!==e);s=!0);}catch(t){c=!0,r=t}finally{try{if(!s&&null!=a.return&&(o=a.return(),Object(o)!==o))return}finally{if(c)throw r}}return l}}function p(t){if(Array.isArray(t))return t}function m(t,e,a,n,r,i,o){try{var l=t[i](o),s=l.value}catch(t){return void a(t)}l.done?e(s):Promise.resolve(s).then(n,r)}function h(t){return function(){var e=this,a=arguments;return new Promise((function(n,r){var i=t.apply(e,a);function o(t){m(i,n,r,o,l,"next",t)}function l(t){m(i,n,r,o,l,"throw",t)}o(void 0)}))}}var g={name:"selectLaber",props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:"选择标签"},width:{type:String,default:"600px"},isshow:Boolean,ruleSingleInfo:{type:Object,default:function(){return{}}}},data:function(){return{isLoading:!1,name:"",activeLaberList:[],selectLabelIdList:[],selectLabelListData:[],tableData:[],labelType:"",pageSize:3,totalCount:0,currentPage:1}},computed:{visible:{get:function(){return this.isshow},set:function(t){this.$emit("update:isshow",t)}}},created:function(){this.ruleSingleInfo.selectLabelIdList&&this.ruleSingleInfo.selectLabelIdList.length&&(this.selectLabelIdList=this.ruleSingleInfo.selectLabelIdList),this.ruleSingleInfo.selectLabelListData&&this.ruleSingleInfo.selectLabelListData.length&&(this.selectLabelListData=this.ruleSingleInfo.selectLabelListData),this.labelType=this.ruleSingleInfo.labelType,this.getLabelGroupList()},mounted:function(){},methods:{searchHandle:Object(i["d"])((function(){this.currentPage=1,this.getLabelGroupList()}),300),getLabelGroupList:function(){var t=this;return h(l().mark((function e(){var a,n,r,o,c;return l().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,a={type:t.labelType,page:t.currentPage,page_size:t.pageSize},t.name&&(a.name=t.name),e.next=5,Object(i["Z"])(t.$apis.apiBackgroundHealthyAdminLabelGroupListPost(a));case 5:if(n=e.sent,r=s(n,2),o=r[0],c=r[1],t.isLoading=!1,!o){e.next=13;break}return t.$message.error(o.message),e.abrupt("return");case 13:0===c.code?(t.totalCount=c.data.count,t.totalPageSize=t.$computedTotalPageSize(t.totalCount,t.pageSize),t.tableData=c.data.results.map((function(e){return e.inputVisible=!1,e.inputValue="",e.label_list.forEach((function(a){a.label_group_name=e.name,t.ruleSingleInfo.selectLabelAllIds&&t.ruleSingleInfo.selectLabelAllIds.length&&t.ruleSingleInfo.selectLabelAllIds.includes(a.id)&&!t.selectLabelIdList.includes(a.id)?a.disabled=!0:a.disabled=!1})),t.activeLaberList.push(e.id),e}))):t.$message.error(c.msg);case 14:case"end":return e.stop()}}),e)})))()},handleChange:function(){},checkboxChangge:function(t,e){var a=this,n=this.selectLabelIdList.indexOf(t.id);-1!==n?this.selectLabelListData.push(t):this.selectLabelListData.map((function(e,n){t.id===e.id&&a.selectLabelListData.splice(n,1)}))},handleInputConfirm:function(t){t.inputValue?this.getLabelGroupAddLabel(t):t.inputVisible=!1},showInput:function(t){var e=this;t.inputVisible=!0,this.$nextTick((function(a){e.$refs["saveTagInput"+t.id][0].$refs.input.focus()}))},getLabelGroupAddLabel:function(t){var e=this;return h(l().mark((function a(){var n,r,o,c;return l().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return e.isLoading=!0,a.next=3,Object(i["Z"])(e.$apis.apiBackgroundHealthyAdminLabelGroupAddLabelPost({name:t.inputValue,label_group_id:t.id}));case 3:if(n=a.sent,r=s(n,2),o=r[0],c=r[1],e.isLoading=!1,!o){a.next=11;break}return e.$message.error(o.message),a.abrupt("return");case 11:0===c.code?(t.inputValue="",t.inputVisible=!1,e.getLabelGroupList()):e.$message.error(c.msg);case 12:case"end":return a.stop()}}),a)})))()},clickConfirmHandle:function(){var t={selectLabelIdList:this.selectLabelIdList,selectLabelListData:this.selectLabelListData};this.$emit("selectLaberData",t),this.visible=!1},clickCancleHandle:function(){this.visible=!1},handleClose:function(t){this.visible=!1},handleCurrentChange:function(t){this.currentPage=t,this.getLabelGroupList()}}},y=g,b=(a("27c8"),a("2877")),v=Object(b["a"])(y,n,r,!1,null,null,null);e["default"]=v.exports},"27c8":function(t,e,a){"use strict";a("c4a9")},"3fa5":function(t,e,a){"use strict";a.d(e,"a",(function(){return i}));a("9e1f"),a("450d");var n=a("6ed5"),r=a.n(n);function i(t,e){return new Promise((function(a,n){r.a.confirm(t.content?t.content:"",t.title?t.title:"提示",{dangerouslyUseHTMLString:!0|t.useHTML,distinguishCancelAndClose:!0,closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:t.cancel_class?t.cancel_class:"ps-cancel-btn",confirmButtonClass:t.confirm_class?t.confirm_class:"ps-btn",confirmButtonText:t.confirmButtonText,cancelButtonText:t.cancelButtonText,center:""===t.center||t.center}).then((function(t){e?a(e()):a()})).catch((function(t){n(t)}))}))}},a614:function(t,e,a){},c4a9:function(t,e,a){},cbc0:function(t,e,a){"use strict";a("a614")},d7de:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"super-add-commodity container-wrapper"},[e("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"foodRef",attrs:{rules:t.formRuls,model:t.formData,size:"small"}},[e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("基本信息")])]),e("div",{staticStyle:{"max-width":"50%",padding:"0 20px"}},[e("el-form-item",{staticClass:"block-label form-content-flex",attrs:{label:"菜品/商品名称",prop:"name"}},[e("el-input",{staticClass:"ps-input",staticStyle:{width:"80%"},attrs:{placeholder:"请输入菜品/商品名称"},model:{value:t.formData.name,callback:function(e){t.$set(t.formData,"name",e)},expression:"formData.name"}}),e("el-tooltip",{attrs:{effect:"dark",content:"增加菜品别名",placement:"top"}},[e("img",{staticClass:"add-btn-img",attrs:{src:a("a851"),alt:""},on:{click:t.addFoodAliasName}})])],1),t.formData.aliasName.length?e("div",[e("el-form-item",{staticClass:"block-label",attrs:{label:"菜品别名："}},t._l(t.formData.aliasName,(function(n,r){return e("el-form-item",{key:r,class:[r>0?"m-t-10":"","food-alias-name-form"],attrs:{rules:t.formRuls.aliasName,prop:"aliasName[".concat(r,"]")}},[e("el-input",{staticClass:"ps-input",staticStyle:{width:"80%"},attrs:{maxlength:"20",placeholder:"请输入菜品别名"},model:{value:t.formData.aliasName[r],callback:function(e){t.$set(t.formData.aliasName,r,e)},expression:"formData.aliasName[index]"}}),e("img",{attrs:{src:a("a851"),alt:""},on:{click:t.addFoodAliasName}}),e("img",{attrs:{src:a("1597"),alt:""},on:{click:function(e){return t.delFoodAliasName(r)}}})],1)})),1)],1):t._e(),e("el-form-item",{staticClass:"block-label",attrs:{label:"属性",prop:"attributes"}},[e("el-radio-group",{staticClass:"ps-radio",model:{value:t.formData.attributes,callback:function(e){t.$set(t.formData,"attributes",e)},expression:"formData.attributes"}},[e("el-radio",{attrs:{label:"goods"}},[t._v("商品")]),e("el-radio",{attrs:{label:"foods"}},[t._v("菜品")])],1)],1),e("el-form-item",{attrs:{label:"分类：",prop:"categoryId"}},[e("el-select",{staticClass:"ps-select",staticStyle:{width:"190px"},attrs:{placeholder:"请下拉选择分类","popper-class":"ps-popper-select","collapse-tags":"",clearable:"","popper-append-to-body":!1},model:{value:t.formData.categoryId,callback:function(e){t.$set(t.formData,"categoryId",e)},expression:"formData.categoryId"}},t._l(t.foodCategoryList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),e("el-form-item",{attrs:{label:"标签",prop:""}},[e("el-button",{staticClass:"ps-origin-btn",attrs:{type:"primary",size:"small"},on:{click:t.labelClick}},[t._v(" 选择标签 ")])],1),t._l(t.formData.labelGroupInfoList,(function(a,n,r){return e("el-form-item",{key:r,attrs:{label:"".concat(n,":"),prop:""}},t._l(a,(function(a,r){return e("el-tag",{key:r,staticClass:"m-r-5 collapse-data",attrs:{size:"medium",effect:"plain",type:"info",color:"#fff",closable:""},on:{close:function(e){return t.closeTag(n,r,a)}}},[t._v(" "+t._s(a.name)+" ")])})),1)}))],2),e("div",{staticStyle:{"max-width":"900px","padding-left":"20px"}},[e("el-form-item",{staticClass:"upload-block-label upload-hidden",attrs:{label:"菜品/商品图片"}},[e("div",{staticClass:"inline-block upload-w"},[e("el-upload",{ref:"fileUpload",class:{"file-upload":!0,"hide-upload":t.formData.foodImagesList.length>0},attrs:{drag:"",action:t.serverUrl,data:t.uploadParams,"file-list":t.formData.foodImagesList,"on-success":t.handleFoodImgSuccess,"on-change":t.handelChange,"before-upload":t.beforeFoodImgUpload,limit:1,"list-type":"picture-card",multiple:!1,headers:t.headersOpts,accept:".jpeg,.jpg,.png,.bmp"},scopedSlots:t._u([{key:"file",fn:function(a){var n=a.file;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:"uploading"===n.status,expression:"file.status==='uploading'"}],attrs:{"element-loading-text":"上传中"}},[e("div",{staticClass:"upload-food-img"},[e("el-image",{staticClass:"el-upload-dragger",attrs:{src:n.url,fit:"contain"}})],1),e("span",{staticClass:"el-upload-list__item-actions"},[e("span",{staticClass:"el-upload-list__item-preview",on:{click:function(e){return t.handlePictureCardPreview(n)}}},[e("i",{staticClass:"el-icon-zoom-in"})]),e("span",{staticClass:"el-upload-list__item-delete",on:{click:function(e){return t.handleImgRemove(n,"foodImages")}}},[e("i",{staticClass:"el-icon-delete"})])])])}}])},[t.fileLists.length<1?e("div",{staticClass:"upload-t"},[e("i",{staticClass:"el-icon-circle-plus"}),e("div",{staticClass:"el-upload__text"},[t._v(" 上传菜品/商品图片 ")])]):t._e()])],1),e("div",{staticClass:"inline-block upload-tips"},[t._v(" 上传：菜品/商品图片。"),e("br"),t._v(" 建议图片需清晰，图片内容与名称相符。"),e("br"),t._v(" 仅支持jpg、png、bmp格式，大小不超过5M ")])]),e("el-form-item",{staticClass:"upload-block-label",attrs:{label:"识别图片"}},[e("div",{staticClass:"inline-block upload-w"},[e("el-upload",{directives:[{name:"loading",rawName:"v-loading",value:t.uploadingExtra,expression:"uploadingExtra"}],ref:"fileUpload",staticClass:"file-upload",attrs:{"element-loading-text":"上传中",drag:"",action:t.serverUrl,data:t.uploadParams,"file-list":t.formData.extraImagesList,"on-success":t.handleExtraImgSuccess,"on-change":t.handelChange,"before-upload":t.beforeExtraImgUpload,limit:25,multiple:!0,"show-file-list":!1,headers:t.headersOpts,accept:".jpeg,.jpg,.png,.bmp"}},[e("div",{staticClass:"upload-t"},[e("i",{staticClass:"el-icon-circle-plus"}),e("div",{staticClass:"el-upload__text"},[t._v(" 上传识别图片 ")])])])],1),e("div",{staticClass:"inline-block upload-tips"},[t._v(" 上传：识别图片。最多25张"),e("br"),t._v(" 建议图片需清晰，图片内容与名称相符。"),e("br"),t._v(" 仅支持jpg、png、bmp格式，大小不超过5M ")]),e("div",{directives:[{name:"show",rawName:"v-show",value:t.showFoodImg,expression:"showFoodImg"}],staticStyle:{cursor:"pointer"},on:{click:function(e){t.showFoodImg=!1}}},[t._v(" 查看已上传的图片（"+t._s(t.formData.extraImages.length)+"张） "),e("i",{staticClass:"el-icon-arrow-up"})]),e("div",{directives:[{name:"show",rawName:"v-show",value:!t.showFoodImg,expression:"!showFoodImg"}],staticStyle:{cursor:"pointer"},on:{click:function(e){t.showFoodImg=!0}}},[t._v(" 查看已上传的图片（"+t._s(t.formData.extraImages.length)+"张） "),e("i",{staticClass:"el-icon-arrow-down"})]),e("div",{directives:[{name:"show",rawName:"v-show",value:t.showFoodImg,expression:"showFoodImg"}],staticClass:"food-img-wrap"},t._l(t.formData.extraImages,(function(a,n){return e("div",{key:n,staticClass:"food-img-item"},[e("img",{attrs:{src:a,alt:"",srcset:""}}),e("div",{staticClass:"food-img-mask"},[e("i",{staticClass:"el-icon-zoom-in",on:{click:function(e){return t.perviewFoodImg(a)}}}),e("i",{staticClass:"el-icon-delete",on:{click:function(e){return t.handleImgRemove({url:a},"extraImages")}}})])])})),0)])],1)]),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v(" 食材占比 "),e("span",{staticClass:"tip-o-7"},[t._v("（菜品每100g所含食材占比，相加必须等于100%）")]),e("el-button",{staticClass:"ps-btn float-r",staticStyle:{"margin-right":"15px"},attrs:{type:"primary",size:"small",icon:"el-icon-plus"},on:{click:t.addIngredients}},[t._v("添加")])],1)]),e("div",{class:["table-content",t.errorMsg.percentageError?"error-border":""]},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:t.formData.ingredientList,"header-row-class-name":"ps-table-header-row"}},[e("el-table-column",{attrs:{prop:"no",label:"食材",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-select",{staticClass:"ps-select margin-right",attrs:{placeholder:"请下拉选择","collapse-tags":"",clearable:"",filterable:""},on:{change:t.changeIngredient},model:{value:a.row.selectId,callback:function(e){t.$set(a.row,"selectId",e)},expression:"scope.row.selectId"}},t._l(a.row.selectFoodIngredient,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id,disabled:t.disabled}})})),1)]}}])}),e("el-table-column",{attrs:{prop:"id",label:"占比",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",{staticClass:"cantent ps-flex-align-c flex-align-c"},[e("el-slider",{staticClass:"cantent",attrs:{"show-input":""},on:{change:t.changePercentage},model:{value:a.row.percentage,callback:function(e){t.$set(a.row,"percentage",e)},expression:"scope.row.percentage"}}),t._v("% ")],1)]}}])}),e("el-table-column",{attrs:{prop:"xx",label:"操作",align:"center",width:"180px"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteIngredientHandle(a.row.index)}}},[t._v("删除")])]}}])})],1)],1),t.errorMsg.percentageError?e("div",{staticStyle:{color:"red",padding:"20px"}},[t._v(t._s(t.errorMsg.percentageError))]):t._e()]),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v(" 营养信息 ")])]),e("div",{staticClass:"table-content"},[t._l(t.currentNutritionList,(function(a){return[e("div",{key:a.key,staticClass:"nutrition-item"},[e("div",{staticClass:"nutrition-label"},[t._v(t._s(a.name+"："))]),e("el-form-item",{attrs:{prop:a.key}},[e("el-input",{staticClass:"ps-input",staticStyle:{width:"120px"},attrs:{readonly:"",disabled:""},model:{value:t.formData[a.key],callback:function(e){t.$set(t.formData,a.key,e)},expression:"formData[nutrition.key]"}}),e("span",{staticStyle:{"margin-left":"10px"}},[t._v(t._s(a.unit))])],1)],1)]})),e("div",{staticClass:"text-center pointer"},[e("span",{staticStyle:{color:"#027DB4"},on:{click:function(e){t.showAll=!t.showAll}}},[t._v(t._s(t.showAll?"收起":"查看更多营养信息"))])])],2)]),e("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[e("el-button",{staticStyle:{width:"120px"},attrs:{disabled:t.isLoading},on:{click:t.closeHandler}},[t._v("取消")]),e("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary",disabled:t.isLoading},on:{click:t.submitHandler}},[t._v(t._s("add"===t.type?"添加":"编辑"))])],1)]),e("el-dialog",{attrs:{visible:t.dialogVisible},on:{"update:visible":function(e){t.dialogVisible=e}}},[e("img",{attrs:{width:"100%",src:t.dialogImageUrl,alt:""}})]),t.selectLaberDialogVisible?e("select-laber",{attrs:{isshow:t.selectLaberDialogVisible,width:"600px",ruleSingleInfo:t.ruleSingleInfo},on:{"update:isshow":function(e){t.selectLaberDialogVisible=e},selectLaberData:t.selectLaberData}}):t._e()],1)},r=[],i=a("ed08"),o=a("015b"),l=a("da92"),s=a("3fa5"),c=a("1a24"),u=a("dfd5");function d(t){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},d(t)}function f(t,e){return y(t)||g(t,e)||m(t,e)||p()}function p(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function m(t,e){if(t){if("string"==typeof t)return h(t,e);var a={}.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?h(t,e):void 0}}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,n=Array(e);a<e;a++)n[a]=t[a];return n}function g(t,e){var a=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=a){var n,r,i,o,l=[],s=!0,c=!1;try{if(i=(a=a.call(t)).next,0===e){if(Object(a)!==a)return;s=!1}else for(;!(s=(n=i.call(a)).done)&&(l.push(n.value),l.length!==e);s=!0);}catch(t){c=!0,r=t}finally{try{if(!s&&null!=a.return&&(o=a.return(),Object(o)!==o))return}finally{if(c)throw r}}return l}}function y(t){if(Array.isArray(t))return t}function b(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */b=function(){return e};var t,e={},a=Object.prototype,n=a.hasOwnProperty,r=Object.defineProperty||function(t,e,a){t[e]=a.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function c(t,e,a){return Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,a){return t[e]=a}}function u(t,e,a,n){var i=e&&e.prototype instanceof v?e:v,o=Object.create(i.prototype),l=new T(n||[]);return r(o,"_invoke",{value:S(t,a,l)}),o}function f(t,e,a){try{return{type:"normal",arg:t.call(e,a)}}catch(t){return{type:"throw",arg:t}}}e.wrap=u;var p="suspendedStart",m="suspendedYield",h="executing",g="completed",y={};function v(){}function L(){}function _(){}var I={};c(I,o,(function(){return this}));var w=Object.getPrototypeOf,x=w&&w(w($([])));x&&x!==a&&n.call(x,o)&&(I=x);var k=_.prototype=v.prototype=Object.create(I);function D(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function C(t,e){function a(r,i,o,l){var s=f(t[r],t,i);if("throw"!==s.type){var c=s.arg,u=c.value;return u&&"object"==d(u)&&n.call(u,"__await")?e.resolve(u.__await).then((function(t){a("next",t,o,l)}),(function(t){a("throw",t,o,l)})):e.resolve(u).then((function(t){c.value=t,o(c)}),(function(t){return a("throw",t,o,l)}))}l(s.arg)}var i;r(this,"_invoke",{value:function(t,n){function r(){return new e((function(e,r){a(t,n,e,r)}))}return i=i?i.then(r,r):r()}})}function S(e,a,n){var r=p;return function(i,o){if(r===h)throw Error("Generator is already running");if(r===g){if("throw"===i)throw o;return{value:t,done:!0}}for(n.method=i,n.arg=o;;){var l=n.delegate;if(l){var s=E(l,n);if(s){if(s===y)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(r===p)throw r=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r=h;var c=f(e,a,n);if("normal"===c.type){if(r=n.done?g:m,c.arg===y)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r=g,n.method="throw",n.arg=c.arg)}}}function E(e,a){var n=a.method,r=e.iterator[n];if(r===t)return a.delegate=null,"throw"===n&&e.iterator.return&&(a.method="return",a.arg=t,E(e,a),"throw"===a.method)||"return"!==n&&(a.method="throw",a.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var i=f(r,e.iterator,a.arg);if("throw"===i.type)return a.method="throw",a.arg=i.arg,a.delegate=null,y;var o=i.arg;return o?o.done?(a[e.resultName]=o.value,a.next=e.nextLoc,"return"!==a.method&&(a.method="next",a.arg=t),a.delegate=null,y):o:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,y)}function O(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function N(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(O,this),this.reset(!0)}function $(e){if(e||""===e){var a=e[o];if(a)return a.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,i=function a(){for(;++r<e.length;)if(n.call(e,r))return a.value=e[r],a.done=!1,a;return a.value=t,a.done=!0,a};return i.next=i}}throw new TypeError(d(e)+" is not iterable")}return L.prototype=_,r(k,"constructor",{value:_,configurable:!0}),r(_,"constructor",{value:L,configurable:!0}),L.displayName=c(_,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===L||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,c(t,s,"GeneratorFunction")),t.prototype=Object.create(k),t},e.awrap=function(t){return{__await:t}},D(C.prototype),c(C.prototype,l,(function(){return this})),e.AsyncIterator=C,e.async=function(t,a,n,r,i){void 0===i&&(i=Promise);var o=new C(u(t,a,n,r),i);return e.isGeneratorFunction(a)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},D(k),c(k,s,"Generator"),c(k,o,(function(){return this})),c(k,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),a=[];for(var n in e)a.push(n);return a.reverse(),function t(){for(;a.length;){var n=a.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=$,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(N),!e)for(var a in this)"t"===a.charAt(0)&&n.call(this,a)&&!isNaN(+a.slice(1))&&(this[a]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var a=this;function r(n,r){return l.type="throw",l.arg=e,a.next=n,r&&(a.method="next",a.arg=t),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],l=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var s=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(s&&c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(t,e){for(var a=this.tryEntries.length-1;a>=0;--a){var r=this.tryEntries[a];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.finallyLoc===t)return this.complete(a.completion,a.afterLoc),N(a),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.tryLoc===t){var n=a.completion;if("throw"===n.type){var r=n.arg;N(a)}return r}}throw Error("illegal catch attempt")},delegateYield:function(e,a,n){return this.delegate={iterator:$(e),resultName:a,nextLoc:n},"next"===this.method&&(this.arg=t),y}},e}function v(t,e,a,n,r,i,o){try{var l=t[i](o),s=l.value}catch(t){return void a(t)}l.done?e(s):Promise.resolve(s).then(n,r)}function L(t){return function(){var e=this,a=arguments;return new Promise((function(n,r){var i=t.apply(e,a);function o(t){v(i,n,r,o,l,"next",t)}function l(t){v(i,n,r,o,l,"throw",t)}o(void 0)}))}}var _={name:"SuperAddIngredients",data:function(){var t=this,e=function(t,e,a){if(e){var n=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;n.test(e)?a():a(new Error("营养数据有误，仅支持保留两位小数"))}else a()},a=function(e,a,n){if(!t.formData.imageList.length)return n(new Error("请上传菜品图片"));n()};return{type:"add",isLoading:!1,formData:{name:"",aliasName:[],attributes:"foods",tasteList:[],foodImages:[],foodImagesList:[],extraImages:[],extraImagesList:[],ingredientList:[],food_id:"",categoryId:"",selectLabelListData:[],selectLabelIdList:[],labelGroupInfoList:{}},fileLists:[],serverUrl:"/api/background/file/upload",headersOpts:{TOKEN:Object(i["B"])()},formRuls:{name:[{required:!0,message:"食材名称不能为空",trigger:"blur"}],aliasName:[{required:!0,message:"请输入菜品别名",trigger:"blur"}],attributes:[{required:!0,message:"请选择属性",trigger:"blur"}],nutrition:[{validator:e,trigger:"change"}],imageList:[{required:!0,validator:a,trigger:"blur"}],categoryId:[{required:!0,message:"请选择分类",trigger:"blur"}]},nutritionList:o["NUTRITION_LIST"],inputVisible:!1,inputValue:"",limit:25,actionUrl:"",uploadParams:{prefix:"super_food_img"},uploadUrl:"",tableData:[{}],ingredientList:[],allSelectIngredient:[],errorMsg:{percentageError:""},selectLaberDialogVisible:!1,ruleSingleInfo:{},dialogImageUrl:"",dialogVisible:!1,showFoodImg:!0,foodCategoryList:[],showAll:!1,uploading:!1,uploadingExtra:!1}},computed:{currentNutritionList:function(){var t=[];return t=this.showAll?this.nutritionList:this.nutritionList.slice(0,4),t}},components:{selectLaber:c["default"]},created:function(){this.type=this.$route.query.type,this.initLoad()},mounted:function(){},methods:{initLoad:function(){var t=this;return L(b().mark((function e(){var a;return b().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.getIngredientslist();case 3:return e.next=5,t.foodFoodCategoryList();case 5:t.isLoading=!1,"modify"===t.type?(a=t.$decodeQuery(t.$route.query.data),t.formData.id=a.id,t.formData.aliasName=a.alias_name,t.formData.name=a.name,t.formData.attributes=a.attributes,t.formData.categoryId=a.category,a.label.length&&t.initLabelGroup(a.label),t.formData.selectLabelListData=a.label,t.formData.selectLabelIdList=a.label.map((function(t){return t.id})),a.image&&(t.formData.foodImages=[a.image],t.formData.foodImagesList=[{url:a.image,name:a.image,status:"success",uid:a.image}]),a.extra_image&&(a.extra_image.forEach((function(e){t.formData.extraImagesList.push({url:e,name:e,status:"success",uid:e})})),t.formData.extraImages=a.extra_image),t.initIngredient(a),t.setNutritonData(a),t.isDisabledOtherIngredients()):(t.initIngredient(),t.setNutritonData({}));case 7:case"end":return e.stop()}}),e)})))()},searchHandle:Object(i["d"])((function(){this.currentPage=1}),300),setNutritonData:function(t){var e=this;t.nutrition||(t.nutrition={});var a=t.nutrition.element?JSON.parse(Object(i["R"])(t.nutrition.element)):{},n=t.nutrition.vitamin?JSON.parse(Object(i["R"])(t.nutrition.vitamin)):{};o["NUTRITION_LIST"].forEach((function(r){"default"===r.type&&e.$set(e.formData,r.key,t.nutrition[r.key]?t.nutrition[r.key]:0),"element"===r.type&&e.$set(e.formData,r.key,a[r.key]?a[r.key]:0),"vitamin"===r.type&&e.$set(e.formData,r.key,n[r.key]?n[r.key]:0)}))},getIngredientslist:function(){var t=this;return L(b().mark((function e(){var a,n,r,o;return b().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(i["Z"])(t.$apis.apiBackgroundAdminIngredientIngredientNamePost({page:1,page_size:999999}));case 2:if(a=e.sent,n=f(a,2),r=n[0],o=n[1],t.isLoading=!1,!r){e.next=10;break}return t.$message.error(r.message),e.abrupt("return");case 10:0===o.code?t.ingredientList=o.data:t.$message.error(o.msg);case 11:case"end":return e.stop()}}),e)})))()},initIngredient:function(t){var e=this;this.formData.ingredientList=[],"add"===this.type?this.formData.ingredientList.push({index:this.formData.ingredientList.length,selectId:"",percentage:0,selectFoodIngredient:Object(i["f"])(this.ingredientList)}):t&&(this.formData.ingredientList=t.ingredients_list.map((function(t,a){return t.index=a,t.selectId=Number(t.ingredient_id),t.percentage=t.ingredient_scale,t.selectFoodIngredient=Object(i["f"])(e.ingredientList),e.ingredientList.map((function(e){e.id===t.selectId&&(t.nutrition=e.nutrition_info)})),t})),this.isDisabledOtherIngredients())},formatParams:function(){var t=this,e={name:this.formData.name,alias_name:this.formData.aliasName,attributes:this.formData.attributes,image:this.formData.foodImages[0],extra_image:this.formData.extraImages,label_list:this.formData.selectLabelIdList,ingredient_list:[],nutrition_info:{},category_id:this.formData.categoryId};"modify"===this.type&&(e.id=this.formData.id),this.formData.food_id&&"add"===this.type&&(e.food_id=this.formData.food_id),this.formData.ingredientList.map((function(t){if(t.selectId){var a={ingredient_id:t.selectId,ingredient_scale:t.percentage};e.ingredient_list.push(a)}}));var a={},n={};return o["NUTRITION_LIST"].forEach((function(r){"default"===r.type&&(e.nutrition_info[r.key]=t.formData[r.key]),"element"===r.type&&(a[r.key]=t.formData[r.key]),"vitamin"===r.type&&(n[r.key]=t.formData[r.key])})),e.nutrition_info.element=JSON.stringify(a),e.nutrition_info.vitamin=JSON.stringify(n),e},addIngredients:function(){this.formData.ingredientList.push({index:this.formData.ingredientList.length,selectId:"",percentage:0,selectFoodIngredient:Object(i["f"])(this.ingredientList)}),this.isDisabledOtherIngredients()},deleteIngredientHandle:function(t){this.formData.ingredientList.splice(t,1),this.isDisabledOtherIngredients(),this.computedNutritionAndPercentage(),this.changePercentage()},changeIngredient:function(t){var e={};this.ingredientList.map((function(a){a.id===t&&(e=a)})),this.formData.ingredientList.forEach((function(t){t.selectId===e.id&&(t.nutrition=e.nutrition_info)})),this.errorMsg.percentageError="",this.isDisabledOtherIngredients(),this.computedNutritionAndPercentage()},isDisabledOtherIngredients:function(){var t=this;this.allSelectIngredient=[],this.formData.ingredientList.map((function(e,a){e.selectId&&t.allSelectIngredient.push(e.selectId)})),this.formData.ingredientList.forEach((function(e,a){e.selectFoodIngredient.forEach((function(a){t.allSelectIngredient.includes(a.id)&&e.selectId!==a.id?a.disabled=!0:a.disabled=!1}))}))},computedNutritionAndPercentage:function(){var t=this,e={};o["NUTRITION_LIST"].forEach((function(t){e[t.key]=0}));var a=0;this.formData.ingredientList.map((function(n,r){if(n.selectId){r<t.allSelectIngredient.length-1?(n.percentage=parseInt(l["a"].divide(100,t.allSelectIngredient.length)),a=l["a"].plus(n.percentage,a)):n.percentage=parseInt(l["a"].minus(100,a));var o=n.percentage/100;if(n.nutrition||(n.nutrition={}),e.energy_kcal=+n.nutrition.energy_kcal?l["a"].plus(e.energy_kcal,n.nutrition.energy_kcal*o):e.energy_kcal?e.energy_kcal:0,e.protein=+n.nutrition.protein?l["a"].plus(e.protein,n.nutrition.protein*o):e.protein?e.protein:0,e.axunge=+n.nutrition.axunge?l["a"].plus(e.axunge,n.nutrition.axunge*o):e.axunge?e.axunge:0,e.carbohydrate=+n.nutrition.carbohydrate?l["a"].plus(e.carbohydrate,n.nutrition.carbohydrate*o):e.carbohydrate?e.carbohydrate:0,e.cholesterol=+n.nutrition.cholesterol?l["a"].plus(e.cholesterol,n.nutrition.cholesterol*o):e.cholesterol?e.cholesterol:0,e.dietary_fiber=+n.nutrition.dietary_fiber?l["a"].plus(e.dietary_fiber,n.nutrition.dietary_fiber*o):e.dietary_fiber?e.dietary_fiber:0,n.nutrition.element&&n.nutrition.vitamin)try{var s=JSON.parse(Object(i["R"])(n.nutrition.element)),c=JSON.parse(Object(i["R"])(n.nutrition.vitamin));for(var u in s)e[u]=l["a"].plus(e[u],+s[u]?s[u]*o:0);for(var d in c)e[d]=l["a"].plus(e[d],+c[d]?c[d]*o:0)}catch(f){}t.deepFormIngredients&&t.deepFormIngredients.length&&t.deepFormIngredients.forEach((function(t){t.id===n.id&&(n.status=!0)}))}})),this.nutritionList.forEach((function(a){var n=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;n.test(e[a.key])?t.$set(t.formData,a.key,e[a.key]):t.$set(t.formData,a.key,e[a.key].toFixed(2))}))},setNutritionAndPercentage:function(){var t=this,e={};o["NUTRITION_LIST"].forEach((function(t){e[t.key]=0})),this.formData.ingredientList.map((function(t,a){if(t.selectId){t.nutrition||(t.nutrition={});var n=t.percentage/100;if(e.energy_kcal=+t.nutrition.energy_kcal?l["a"].plus(e.energy_kcal,t.nutrition.energy_kcal*n):e.energy_kcal?e.energy_kcal:0,e.protein=+t.nutrition.protein?l["a"].plus(e.protein,t.nutrition.protein*n):e.protein?e.protein:0,e.axunge=+t.nutrition.axunge?l["a"].plus(e.axunge,t.nutrition.axunge*n):e.axunge?e.axunge:0,e.carbohydrate=+t.nutrition.carbohydrate?l["a"].plus(e.carbohydrate,t.nutrition.carbohydrate*n):e.carbohydrate?e.carbohydrate:0,e.cholesterol=+t.nutrition.cholesterol?l["a"].plus(e.cholesterol,t.nutrition.cholesterol*n):e.cholesterol?e.cholesterol:0,e.dietary_fiber=+t.nutrition.dietary_fiber?l["a"].plus(e.dietary_fiber,t.nutrition.dietary_fiber*n):e.dietary_fiber?e.dietary_fiber:0,t.nutrition.element&&t.nutrition.vitamin)try{var r=JSON.parse(Object(i["R"])(t.nutrition.element)),o=JSON.parse(Object(i["R"])(t.nutrition.vitamin));for(var s in r)e[s]=l["a"].plus(e[s],+r[s]?r[s]*n:0);for(var c in o)e[c]=l["a"].plus(e[c],+o[c]?o[c]*n:0)}catch(u){}}})),this.nutritionList.forEach((function(a){var n=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;n.test(e[a.key])?t.$set(t.formData,a.key,e[a.key]):t.$set(t.formData,a.key,e[a.key].toFixed(2))}))},changePercentage:function(t){this.setNutritionAndPercentage();var e=this.formData.ingredientList.reduce((function(t,e){return l["a"].plus(e.percentage,t)}),0);this.errorMsg.percentageError=e>100||e<100?"菜品每100g所含食材占比，相加必须等于100%":"",this.formData.ingredientList.length||(this.errorMsg.percentageError="")},closeTasteHandle:function(t){this.formData.tasteList.splice(this.formData.tasteList.indexOf(t),1)},showTasteInput:function(){var t=this;this.inputVisible=!0,this.$nextTick((function(e){t.$refs.saveTagInput.$refs.input.focus()}))},inputTasteConfirm:function(){var t=this.inputValue;t&&this.formData.tasteList.push(t),this.inputVisible=!1,this.inputValue=""},labelClick:function(){this.ruleSingleInfo={labelType:"food",selectLabelIdList:this.formData.selectLabelIdList,selectLabelListData:this.formData.selectLabelListData},this.selectLaberDialogVisible=!0},closeTag:function(t,e,a){var n=this.formData.selectLabelIdList.indexOf(a.id),r=this.formData.selectLabelListData.indexOf(a);this.formData.selectLabelIdList.splice(n,1),this.formData.selectLabelListData.splice(r,1),this.formData.labelGroupInfoList={},this.initLabelGroup(this.formData.selectLabelListData)},selectLaberData:function(t){this.formData.selectLabelIdList=t.selectLabelIdList,this.formData.selectLabelListData=t.selectLabelListData,this.formData.labelGroupInfoList={},this.initLabelGroup(this.formData.selectLabelListData)},initLabelGroup:function(t){var e=this;t.forEach((function(t){e.formData.labelGroupInfoList[t.label_group_name]||(e.formData.labelGroupInfoList[t.label_group_name]=[]),e.formData.labelGroupInfoList[t.label_group_name]&&!e.formData.labelGroupInfoList[t.label_group_name].includes(t)&&e.formData.labelGroupInfoList[t.label_group_name].push(t)}))},handelChange:function(t,e){this.uploadParams.key=this.uploadParams.prefix+(new Date).getTime()+Math.floor(150*Math.random())+".png"},perviewFoodImg:function(t){this.dialogImageUrl=t,this.dialogVisible=!0},handleFoodImgSuccess:function(t,e,a){this.uploading=!1,0===t.code?(this.formData.foodImagesList=a,this.formData.foodImages.push(t.data.public_url)):this.$message.error(t.msg)},handleExtraImgSuccess:function(t,e,a){this.uploadingExtra=!1,0===t.code?(this.formData.extraImagesList=a,this.formData.extraImages.push(t.data.public_url)):this.$message.error(t.msg)},handleImgRemove:function(t,e){var a=this.formData[e+"List"].findIndex((function(e){return e.url===t.url}));this.formData[e].splice(a,1),this.formData[e+"List"].splice(a,1)},beforeFoodImgUpload:function(t){return this.beforeImgUpload(t,"uploading")},beforeExtraImgUpload:function(t){return this.beforeImgUpload(t,"uploadingExtra")},beforeImgUpload:function(t,e){var a=[".jpeg",".jpg",".png",".bmp"],n=t.size/1024/1024<5;return a.includes(Object(i["A"])(t.name))?n?void(e&&(this[e]=!0)):(this.$message.error("上传图片大小不能超过 5MB!"),!1):(this.$message.error("上传图片只能是 JPG 格式或者PNG格式!"),!1)},handlePictureCardPreview:function(t){this.dialogImageUrl=t.url,this.dialogVisible=!0},submitHandler:function(){var t=this;this.$refs.foodRef.validate((function(e){if(e&&!t.errorMsg.percentageError){if(t.isLoading)return t.$message.error("请勿重复提交！");"modify"===t.type?t.modifyFoodList():Object(s["a"])({content:"是否确定创建该菜品？"},t.addFoodList)}else t.$message.error("请认真检查数据格式！")}))},addFoodList:function(){var t=this;return L(b().mark((function e(){var a,n,r,o;return b().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(i["Z"])(t.$apis.apiBackgroundAdminFoodAddPost(t.formatParams()));case 3:if(a=e.sent,n=f(a,2),r=n[0],o=n[1],t.isLoading=!1,!r){e.next=11;break}return t.$message.error(r.message),e.abrupt("return");case 11:0===o.code?(t.getFoodlist(),t.$message.success(o.msg),t.$closeCurrentTab(t.$route.path)):2===o.code?(t.formData.food_id=o.data.food_id,Object(s["a"])({content:o.msg},t.addFoodList).catch((function(e){t.formData.food_id=""}))):t.$message.error(o.msg);case 12:case"end":return e.stop()}}),e)})))()},modifyFoodList:function(){var t=this;return L(b().mark((function e(){var a,n,r,o;return b().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(i["Z"])(t.$apis.apiBackgroundAdminFoodModifyPost(t.formatParams()));case 3:if(a=e.sent,n=f(a,2),r=n[0],o=n[1],t.isLoading=!1,!r){e.next=11;break}return t.$message.error(r.message),e.abrupt("return");case 11:0===o.code?(t.getFoodlist(),t.$message.success(o.msg),t.$closeCurrentTab(t.$route.path)):t.$message.error(o.msg);case 12:case"end":return e.stop()}}),e)})))()},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,a,n){"confirm"===e?(a.confirmButtonLoading=!0,t.$closeCurrentTab(t.$route.path),a.confirmButtonLoading=!1):a.confirmButtonLoading||n()}}).then((function(t){})).catch((function(t){}))},addFoodAliasName:function(){this.formData.aliasName.push("")},delFoodAliasName:function(t){this.formData.aliasName.splice(t,1)},foodFoodCategoryList:function(){var t=this;return L(b().mark((function e(){var a,n,r,o;return b().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(i["Z"])(t.$apis.apiBackgroundAdminFoodCategoryListPost({page:1,page_size:999999}));case 2:if(a=e.sent,n=f(a,2),r=n[0],o=n[1],!r){e.next=9;break}return t.$message.error(r.message),e.abrupt("return");case 9:0===o.code?t.foodCategoryList=o.data.results:t.$message.error(o.msg);case 10:case"end":return e.stop()}}),e)})))()},getFoodlist:u["getFoodlist"]}},I=_,w=(a("cbc0"),a("2877")),x=Object(w["a"])(I,n,r,!1,null,null,null);e["default"]=x.exports},dfd5:function(t,e,a){"use strict";function n(){var t=this;return new Promise((function(e,a){t.$apis.apiBackgroundAdminFoodListPost({page:1,page_size:999999}).then((function(t){sessionStorage.setItem("allFoodList",t.data.results?JSON.stringify(t.data.results):"[]"),e(t)})).catch((function(t){a(t)}))}))}a.r(e),a.d(e,"getFoodlist",(function(){return n}))}}]);