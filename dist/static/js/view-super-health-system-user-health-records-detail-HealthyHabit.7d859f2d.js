(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-user-health-records-detail-HealthyHabit"],{"480d":function(t,a,s){"use strict";s.r(a);var i=function(){var t=this,a=t._self._c;return a("div",{staticClass:"healthy-habit records-wrapp-bg m-b-20"},[a("div",{staticClass:"p-b-10 ps-flex-bw flex-align-c"},[a("span",{staticStyle:{"font-weight":"bold"}},[t._v("习惯养成")]),a("span",{staticClass:"time"},[t._v("更新时间："+t._s(t.formData.last_update_time))])]),a("div",{staticClass:"m-b-20"},[t._v(" 累计打卡次数： "),a("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.formData.total_count)+"次")])]),a("div",{staticClass:"clock-in"},[t.formData.habit_list&&t.formData.habit_list.length?t._l(t.formData.habit_list,(function(s,i){return a("div",{key:i,staticClass:"ps-flex p-t-10 p-b-10"},[s.image?a("el-image",{staticStyle:{width:"50px",height:"40px","border-radius":"4px"},attrs:{src:s.image}}):a("div",{staticClass:"custom-style",style:"backgroundColor:".concat(s.color)},[t._v(" "+t._s(s.name?s.name.substring(0,1):"")+" ")]),a("div",{staticClass:"clock-in-wrapp p-l-15"},[a("div",{staticClass:"ps-flex-bw"},[a("span",{staticClass:"habit-name"},[t._v(" "+t._s(s.name)+" "),s.is_use?a("span",{staticClass:"clock-in-ing"},[t._v("NOW")]):t._e()]),a("div",{staticClass:"cumulative-clock-in"},[t._v("累计打卡 "+t._s(s.count)+" 次")])]),a("div",{staticClass:"time"},[t._v("最近一次："+t._s(s.update_time))])])],1)})):a("el-empty",{attrs:{description:"暂无数据"}})],2)])},e=[],c={props:{formInfoData:{type:Object,default:function(){return{}}}},data:function(){return{formData:{}}},watch:{formInfoData:function(t){this.formData=t}},mounted:function(){},methods:{}},n=c,l=(s("ec8b"),s("2877")),o=Object(l["a"])(n,i,e,!1,null,"b005ab04",null);a["default"]=o.exports},d365:function(t,a,s){},ec8b:function(t,a,s){"use strict";s("d365")}}]);