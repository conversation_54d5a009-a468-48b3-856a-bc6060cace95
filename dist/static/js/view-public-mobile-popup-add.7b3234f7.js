(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-public-mobile-popup-add"],{3071:function(t,e,r){"use strict";r.r(e);var a=function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"add-banner"},[e("el-form",{ref:"formRef",attrs:{model:t.formData,rules:t.formRules,"label-width":"140px"}},[e("el-form-item",{attrs:{label:"名称：",prop:"name"}},[e("el-input",{staticClass:"w-300 ps-input",attrs:{placeholder:"不超过20字",maxlength:"20"},model:{value:t.formData.name,callback:function(e){t.$set(t.formData,"name",e)},expression:"formData.name"}})],1),"super"===t.type?e("el-form-item",{attrs:{label:"类别：",prop:"window_type"}},[e("el-select",{staticClass:"ps-select",attrs:{placeholder:"请选择弹窗类型",clearable:""},on:{change:t.clearForm},model:{value:t.formData.window_type,callback:function(e){t.$set(t.formData,"window_type",e)},expression:"formData.window_type"}},t._l(t.windowList,(function(t){return e("el-option",{key:t.value,attrs:{label:t.name,value:t.value}})})),1)],1):t._e(),"fab_popup"===t.formData.window_type&&"super"===t.type?e("el-form-item",{attrs:{label:"图片：",prop:"fileLists"}},[e("file-upload",{ref:"faceFileRef",staticClass:"avatar-uploader",attrs:{fileList:t.formData.fileLists,type:"enclosure","before-upload":t.beforeUpload,"show-file-list":!1},on:{fileLists:t.getFileLists}},[t.formData.fileLists.length?e("img",{staticClass:"avatar",attrs:{src:t.formData.fileLists[0].url},on:{click:t.clearFileHandle}}):e("i",{staticClass:"el-icon-plus avatar-uploader-icon"})]),e("span",{staticClass:"tips m-l-20"},[t._v("仅支持.png .jpg 格式，且不超过5M")])],1):t._e(),"fab_popup"!==t.formData.window_type?e("el-form-item",{attrs:{label:"图片："}},[e("el-form-item",{attrs:{"label-width":"0"}},[e("el-radio",{staticClass:"ps-radio",attrs:{label:1},model:{value:t.formData.img_type,callback:function(e){t.$set(t.formData,"img_type",e)},expression:"formData.img_type"}},[t._v("标准模板")]),1===t.formData.img_type?e("div",{staticClass:"m-l-24 m-b-20"},[e("el-form-item",{attrs:{"label-width":"0",prop:"template_text"}},[e("el-input",{staticClass:"w-300 ps-input",attrs:{type:"textarea",placeholder:"编辑弹窗内容, 不超过15字",rows:"3",maxlength:"15","show-word-limit":""},model:{value:t.formData.template_text,callback:function(e){t.$set(t.formData,"template_text",e)},expression:"formData.template_text"}})],1)],1):t._e()],1),e("el-form-item",{attrs:{prop:""}},[e("el-radio",{staticClass:"ps-radio",attrs:{label:2},model:{value:t.formData.img_type,callback:function(e){t.$set(t.formData,"img_type",e)},expression:"formData.img_type"}},[t._v("自行上传")]),2===t.formData.img_type?e("span",[e("el-form-item",{staticClass:"inline-form-item",attrs:{prop:"fileLists"}},[e("file-upload",{ref:"faceFileRef",staticClass:"avatar-uploader",attrs:{fileList:t.formData.fileLists,type:"enclosure","before-upload":t.beforeUpload,"show-file-list":!1},on:{fileLists:t.getFileLists}},[t.formData.fileLists.length?e("img",{staticClass:"avatar",attrs:{src:t.formData.fileLists[0].url},on:{click:t.clearFileHandle}}):e("i",{staticClass:"el-icon-plus avatar-uploader-icon"})]),e("span",{staticClass:"tips m-l-20"},[t._v("仅支持.png .jpg 格式，且不超过5M")])],1)],1):t._e()],1)],1):t._e(),e("el-form-item",{attrs:{label:"有效日期：",prop:"effective_time"}},[e("el-date-picker",{attrs:{type:"datetime",placeholder:"选择日期","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:t.formData.effective_time,callback:function(e){t.$set(t.formData,"effective_time",e)},expression:"formData.effective_time"}}),e("el-checkbox",{staticClass:"m-l-40",model:{value:t.isForever,callback:function(e){t.isForever=e},expression:"isForever"}},[t._v("永久")])],1),e("el-form-item",{staticClass:"p-b-20",attrs:{label:"显示页面：",prop:"show_page_url"}},[e("el-select",{staticClass:"ps-select",attrs:{placeholder:"请选择跳转地址",clearable:""},model:{value:t.formData.show_page_url,callback:function(e){t.$set(t.formData,"show_page_url",e)},expression:"formData.show_page_url"}},t._l(t.innerList,(function(t){return e("el-option",{key:t.value,attrs:{label:t.name,value:t.value}})})),1)],1),e("el-form-item",{attrs:{label:"跳转地址：",prop:"jump_type"}},[e("el-radio-group",{staticClass:"ps-radio",model:{value:t.formData.jump_type,callback:function(e){t.$set(t.formData,"jump_type",e)},expression:"formData.jump_type"}},[e("el-radio",{attrs:{label:"inner",value:"inner"}},[t._v("内部界面")]),"super"===t.type?e("el-radio",{attrs:{label:"outsite",value:"outsite"}},[t._v("外部链接")]):t._e()],1),"inner"===t.formData.jump_type?e("el-form-item",{staticClass:"p-b-20",attrs:{label:"",prop:"jump_innerselect_type"}},[e("el-select",{staticClass:"ps-select",attrs:{placeholder:"请选择跳转地址",clearable:""},model:{value:t.formData.jump_innerselect_type,callback:function(e){t.$set(t.formData,"jump_innerselect_type",e)},expression:"formData.jump_innerselect_type"}},t._l(t.innerList,(function(t){return e("el-option",{key:t.value,attrs:{label:t.name,value:t.value}})})),1)],1):"outsite"===t.formData.jump_type&&"super"===t.type?e("el-form-item",{staticClass:"p-b-20",attrs:{"label-width":"80px",label:"地址：",prop:"jump_url"}},[e("el-input",{staticClass:"w-300 ps-input",model:{value:t.formData.jump_url,callback:function(e){t.$set(t.formData,"jump_url",e)},expression:"formData.jump_url"}})],1):t._e()],1),"super"===t.type?e("el-form-item",{attrs:{label:"显示项目点：",prop:"show_orgs"}},[e("el-select",{staticClass:"ps-select",attrs:{placeholder:"请选择",filterable:"",multiple:"",clearable:"","collapse-tags":""},model:{value:t.formData.show_orgs,callback:function(e){t.$set(t.formData,"show_orgs",e)},expression:"formData.show_orgs"}},t._l(t.orgslist,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1),e("div",{staticClass:"check-style m-l-10"},[e("el-checkbox",{staticClass:"ps-checkbox",on:{change:t.checkOrgsChange},model:{value:t.chooseAllOrgs,callback:function(e){t.chooseAllOrgs=e},expression:"chooseAllOrgs"}},[t._v("全选")])],1)],1):t._e(),"fab_popup"!==t.formData.window_type?e("el-form-item",{attrs:{label:"弹窗提示：",prop:"popup_type"}},[e("el-select",{staticClass:"ps-select",attrs:{placeholder:"请选择",clearable:""},model:{value:t.formData.popup_type,callback:function(e){t.$set(t.formData,"popup_type",e)},expression:"formData.popup_type"}},t._l(t.popupTypeList,(function(t){return e("el-option",{key:t.value,attrs:{label:t.name,value:t.value}})})),1)],1):t._e(),"fab_popup"!==t.formData.window_type?e("div",{staticClass:"tips m-b-20",staticStyle:{"margin-left":"140px","font-size":"13px"}},[t._v("说明：创建新弹窗后，①若设置弹出一次，则用户第一次登录时弹窗显示，后续登录不弹出②若设置每次弹出，则用户每次登录时均弹窗显示")]):t._e(),"fab_popup"!==t.formData.window_type?e("el-form-item",{attrs:{label:"弹窗类型：",prop:"popup_crowd_type"}},[e("el-select",{staticClass:"ps-select",attrs:{placeholder:"请选择",clearable:""},model:{value:t.formData.popup_crowd_type,callback:function(e){t.$set(t.formData,"popup_crowd_type",e)},expression:"formData.popup_crowd_type"}},t._l(t.memberTypeList,(function(t){return e("el-option",{key:t.value,attrs:{label:t.name,value:t.value}})})),1)],1):t._e(),"fab_popup"!==t.formData.window_type?e("el-form-item",{attrs:{label:"优先级：",prop:"priority"}},[e("el-input",{staticClass:"w-300 ps-input",attrs:{placeholder:""},model:{value:t.formData.priority,callback:function(e){t.$set(t.formData,"priority",e)},expression:"formData.priority"}})],1):t._e(),"fab_popup"!==t.formData.window_type?e("div",{staticClass:"error-text m-t-20",staticStyle:{"margin-left":"140px"}},[t._v("优先级数值小的优先显示")]):t._e(),e("el-form-item",[e("div",{staticClass:"m-t-50"},[e("el-button",{attrs:{plain:""},on:{click:t.closeHandler}},[t._v("取消")]),e("el-button",{staticClass:"ps-origin-btn",attrs:{type:"primary"},on:{click:function(e){return t.submitHandler("publish")}}},[t._v("确认发布")])],1)])],1)],1)},o=[],i=r("ed08");function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return e};var t,e={},r=Object.prototype,a=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},l=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",p=i.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,r){return t[e]=r}}function f(t,e,r,a){var i=e&&e.prototype instanceof g?e:g,n=Object.create(i.prototype),s=new P(a||[]);return o(n,"_invoke",{value:C(t,r,s)}),n}function m(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var h="suspendedStart",d="suspendedYield",y="executing",v="completed",_={};function g(){}function b(){}function w(){}var D={};c(D,l,(function(){return this}));var x=Object.getPrototypeOf,L=x&&x(x(A([])));L&&L!==r&&a.call(L,l)&&(D=L);var k=w.prototype=g.prototype=Object.create(D);function j(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function $(t,e){function r(o,i,s,l){var u=m(t[o],t,i);if("throw"!==u.type){var p=u.arg,c=p.value;return c&&"object"==n(c)&&a.call(c,"__await")?e.resolve(c.__await).then((function(t){r("next",t,s,l)}),(function(t){r("throw",t,s,l)})):e.resolve(c).then((function(t){p.value=t,s(p)}),(function(t){return r("throw",t,s,l)}))}l(u.arg)}var i;o(this,"_invoke",{value:function(t,a){function o(){return new e((function(e,o){r(t,a,e,o)}))}return i=i?i.then(o,o):o()}})}function C(e,r,a){var o=h;return function(i,n){if(o===y)throw Error("Generator is already running");if(o===v){if("throw"===i)throw n;return{value:t,done:!0}}for(a.method=i,a.arg=n;;){var s=a.delegate;if(s){var l=E(s,a);if(l){if(l===_)continue;return l}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(o===h)throw o=v,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);o=y;var u=m(e,r,a);if("normal"===u.type){if(o=a.done?v:d,u.arg===_)continue;return{value:u.arg,done:a.done}}"throw"===u.type&&(o=v,a.method="throw",a.arg=u.arg)}}}function E(e,r){var a=r.method,o=e.iterator[a];if(o===t)return r.delegate=null,"throw"===a&&e.iterator.return&&(r.method="return",r.arg=t,E(e,r),"throw"===r.method)||"return"!==a&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+a+"' method")),_;var i=m(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,_;var n=i.arg;return n?n.done?(r[e.resultName]=n.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,_):n:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,_)}function O(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function F(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function P(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(O,this),this.reset(!0)}function A(e){if(e||""===e){var r=e[l];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(a.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(n(e)+" is not iterable")}return b.prototype=w,o(k,"constructor",{value:w,configurable:!0}),o(w,"constructor",{value:b,configurable:!0}),b.displayName=c(w,p,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,c(t,p,"GeneratorFunction")),t.prototype=Object.create(k),t},e.awrap=function(t){return{__await:t}},j($.prototype),c($.prototype,u,(function(){return this})),e.AsyncIterator=$,e.async=function(t,r,a,o,i){void 0===i&&(i=Promise);var n=new $(f(t,r,a,o),i);return e.isGeneratorFunction(r)?n:n.next().then((function(t){return t.done?t.value:n.next()}))},j(k),c(k,p,"Generator"),c(k,l,(function(){return this})),c(k,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var a in e)r.push(a);return r.reverse(),function t(){for(;r.length;){var a=r.pop();if(a in e)return t.value=a,t.done=!1,t}return t.done=!0,t}},e.values=A,P.prototype={constructor:P,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(F),!e)for(var r in this)"t"===r.charAt(0)&&a.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(a,o){return s.type="throw",s.arg=e,r.next=a,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var n=this.tryEntries[i],s=n.completion;if("root"===n.tryLoc)return o("end");if(n.tryLoc<=this.prev){var l=a.call(n,"catchLoc"),u=a.call(n,"finallyLoc");if(l&&u){if(this.prev<n.catchLoc)return o(n.catchLoc,!0);if(this.prev<n.finallyLoc)return o(n.finallyLoc)}else if(l){if(this.prev<n.catchLoc)return o(n.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<n.finallyLoc)return o(n.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&a.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var n=i?i.completion:{};return n.type=t,n.arg=e,i?(this.method="next",this.next=i.finallyLoc,_):this.complete(n)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),_},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),F(r),_}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var a=r.completion;if("throw"===a.type){var o=a.arg;F(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,a){return this.delegate={iterator:A(e),resultName:r,nextLoc:a},"next"===this.method&&(this.arg=t),_}},e}function l(t,e){return m(t)||f(t,e)||p(t,e)||u()}function u(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function p(t,e){if(t){if("string"==typeof t)return c(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?c(t,e):void 0}}function c(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,a=Array(e);r<e;r++)a[r]=t[r];return a}function f(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var a,o,i,n,s=[],l=!0,u=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(a=i.call(r)).done)&&(s.push(a.value),s.length!==e);l=!0);}catch(t){u=!0,o=t}finally{try{if(!l&&null!=r.return&&(n=r.return(),Object(n)!==n))return}finally{if(u)throw o}}return s}}function m(t){if(Array.isArray(t))return t}function h(t,e,r,a,o,i,n){try{var s=t[i](n),l=s.value}catch(t){return void r(t)}s.done?e(l):Promise.resolve(l).then(a,o)}function d(t){return function(){var e=this,r=arguments;return new Promise((function(a,o){var i=t.apply(e,r);function n(t){h(i,a,o,n,s,"next",t)}function s(t){h(i,a,o,n,s,"throw",t)}n(void 0)}))}}var y={name:"PopupAdd",components:{},data:function(){var t=this,e=function(t,e,r){var a=/^\d+$/;if(a.test(e)){if(e>100)return r(new Error("请输入小于100的数字"));r()}else r(new Error("请输入小于100的数字"))},r=function(e,r,a){if(t.isForever)a();else{if(!r)return a(new Error("请输入有效日期或勾选永久"));a()}},a=function(e,r,a){if("popup"===t.formData.window_type){if(!r)return a(new Error("请选择弹窗提示类型"));a()}else a()},o=function(e,r,a){if("popup"===t.formData.window_type){if(!r)return a(new Error("请选择弹窗类型"));a()}else a()},i=function(e,r,a){if("popup"===t.formData.window_type){if(!r)return a(new Error("请输入弹窗文字"));a()}else a()};return{type:"",operate:"",isLoading:!1,apiList:{super:{add:"apiBackgroundAdminMarketingPopupAddPost",modify:"apiBackgroundAdminMarketingPopupModifyPost"},merchant:{add:"apiBackgroundMarketingMarketingPopupAddPost",modify:"apiBackgroundMarketingMarketingPopupModifyPost"}},formData:{id:"",name:"",fileLists:[],img_type:1,img_url:"",template_text:"",jump_type:"inner",jump_url:"",jump_innerselect_type:"",show_orgs:"",priority:"",show_page_url:"",popup_type:1,popup_crowd_type:"",effective_time:"",window_type:""},isForever:!0,formRules:{name:[{required:!0,message:"名称不能为空",trigger:"blur"}],fileLists:[{required:!0,message:"图片不能为空",trigger:"blur"}],jump_type:[{required:!0,message:"请选择跳转类型",trigger:"blur"}],effective_time:[{validator:r,trigger:"blur"}],jump_url:[{required:!0,message:"请选择输入跳转地址",trigger:"blur"}],show_orgs:[{required:!0,message:"请选择项目点",trigger:"blur"}],popup_type:[{required:!0,validator:a,trigger:"blur"}],popup_crowd_type:[{validator:o,trigger:"blur"}],template_text:[{validator:i,trigger:"blur"}],priority:[{validator:e,trigger:"blur"}]},windowList:[{value:"popup",name:"弹窗"},{value:"fab_popup",name:"浮窗"}],innerList:[{value:"",name:"无"},{value:"app_management",name:"移动端管理"},{value:"charge",name:"充值"},{value:"order",name:"订单"},{value:"reservation",name:"预约点餐/我的预约"},{value:"report",name:"报餐"},{value:"account_info",name:"账户信息"},{value:"tray_bind",name:"托盘绑定"},{value:"jiaofei",name:"缴费中心"},{value:"intent_food",name:"意向菜谱"},{value:"attendance",name:"门禁考勤"},{value:"free_payment_setting",name:"免密代扣"},{value:"shop_feedback",name:"食堂建议"},{value:"order_review",name:"审核查询"},{value:"alipay_qycode",name:"企业码"},{value:"member",name:"会员中心"}],popupTypeList:[{value:1,name:"弹出一次"},{value:2,name:"每次弹出"},{value:5,name:"每天一次"},{value:3,name:"每周一次"},{value:4,name:"每月一次"}],memberTypeList:[{value:"all",name:"所有人弹窗"},{value:"member",name:"会员弹窗"},{value:"not_member",name:"非会员弹窗"}],defaultImg:"https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/v4_h5/banner/71b0cd57f3714b1eed4c207942f0c64d1666144275981.png",orgslist:[],chooseAllOrgs:!1}},created:function(){this.type=this.$route.query.role,this.operate=this.$route.params.type,this.initLoad()},watch:{"formData.effective_time":{handler:function(t){this.isForever=!t}},isForever:function(t){t&&(this.formData.effective_time="")}},mounted:function(){},computed:{},methods:{initLoad:function(){if("super"===this.type&&this.getOrgList(),this.$route.query.data){var t=this.$decodeQuery(this.$route.query.data);if(this.formData.id=t.id,this.formData.name=t.name,""===t.img_url)this.formData.img_type=1,this.formData.template_text=t.template_text;else{this.formData.img_type=2;var e=t.img_url.split("/");e=e[e.length-1],this.formData.fileLists=[{name:e,status:"success",uid:(new Date).getTime(),url:t.img_url}]}this.formData.jump_type=t.jump_type,"inner"===t.jump_type?this.formData.jump_innerselect_type=t.jump_url:this.formData.jump_url=t.jump_url,this.formData.show_orgs=t.show_orgs,this.formData.popup_type=t.popup_type,this.formData.show_page_url=t.show_page_url,this.formData.priority=t.priority,this.formData.effective_time=t.effective_time||"",this.formData.effective_time||(this.isForever=!0),this.formData.popup_crowd_type=t.popup_crowd_type||"",this.formData.window_type=t.window_type}},formatQueryParams:function(t){var e={};for(var r in t){var a=Object(i["b"])(r);""!==t[r].value&&null!==t[r].value&&("select_time"!==a?e[a]=t[r].value:t[r].value&&t[r].value.length>0&&(e.start_time=t[r].value[0],e.end_time=t[r].value[1]))}return e},getFileLists:function(t){this.formData.fileLists=t},beforeUpload:function(t){var e=[".jpg",".png"];if(!e.includes(this.getSuffix(t.name)))return this.$message.error("上传图片只能是 jpg/png 格式"),!1;var r=t.size/1024/1024<5;return r?void 0:(this.$message.error("上传图片大小不能超过 5MB!"),!1)},getSuffix:function(t){var e=t.lastIndexOf("."),r="";return-1!==e&&(r=t.substring(e)),r},clearFileHandle:function(){this.$refs.faceFileRef.clearHandle(),this.formData.fileLists=[]},modifyHandle:function(t){var e=this;return d(s().mark((function r(){var a,o,i,n;return s().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return e.isLoading=!0,r.next=3,e.$to(e.$apis[e.apiList[e.type].modify](t));case 3:if(a=r.sent,o=l(a,2),i=o[0],n=o[1],e.isLoading=!1,!i){r.next=11;break}return e.$message.error(i.message),r.abrupt("return");case 11:0===n.code?(e.$message.success(n.msg),e.closeTab()):e.$message.error(n.msg);case 12:case"end":return r.stop()}}),r)})))()},addHandle:function(t){var e=this;return d(s().mark((function r(){var a,o,i,n;return s().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return e.isLoading=!0,r.next=3,e.$to(e.$apis[e.apiList[e.type].add](t));case 3:if(a=r.sent,o=l(a,2),i=o[0],n=o[1],e.isLoading=!1,!i){r.next=11;break}return e.$message.error(i.message),r.abrupt("return");case 11:0===n.code?(e.$message.success(n.msg),e.closeTab()):e.$message.error(n.msg);case 12:case"end":return r.stop()}}),r)})))()},getParams:function(){var t,e={name:this.formData.name,jump_type:this.formData.jump_type,popup_type:this.formData.popup_type,priority:this.formData.priority,show_page_url:this.formData.show_page_url};("super"===this.type&&"outsite"===this.formData.jump_type&&(e.jump_url=this.formData.jump_url),"inner"===this.formData.jump_type&&(e.jump_url=this.formData.jump_innerselect_type),"popup"===this.formData.window_type)?1===this.formData.img_type?(e.img_url="",e.template_text=this.formData.template_text):e.img_url=this.formData.fileLists[0].url:e.img_url=(null===(t=this.formData.fileLists[0])||void 0===t?void 0:t.url)||"";return"super"===this.type&&(e.show_orgs=this.formData.show_orgs,"popup"===this.formData.window_type&&(e.popup_crowd_type=this.formData.popup_crowd_type),"fab_popup"===this.formData.window_type&&(e.priority=0),this.isForever?e.effective_time=null:e.effective_time=this.formData.effective_time,e.window_type=this.formData.window_type),"modify"===this.operate&&(e.id=this.formData.id),e},submitHandler:function(t){var e=this;this.$refs.formRef.validate((function(r){if(r){if(e.isLoading)return e.$message.error("请勿重复提交！");var a=e.getParams();a.status="modify"===e.operate?t:"unpublished","modify"===e.operate?e.modifyHandle(a):e.addHandle(a)}}))},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,r,a){"confirm"===e?(r.confirmButtonLoading=!0,t.closeTab(),a(),r.confirmButtonLoading=!1):r.confirmButtonLoading||a()}}).then((function(t){})).catch((function(t){}))},closeTab:function(){this.$closeCurrentTab(this.$route.path)},getOrgList:function(){var t=this;return d(s().mark((function e(){var r,a,o,i;return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$to(t.$apis.apiBackgroundAdminMarketingPopupGetOrgsPost());case 2:if(r=e.sent,a=l(r,2),o=a[0],i=a[1],!o){e.next=9;break}return t.$message.error(o.message),e.abrupt("return");case 9:0===i.code?t.orgslist=i.data:t.$message.error(i.msg);case 10:case"end":return e.stop()}}),e)})))()},checkOrgsChange:function(t){if(t){var e=[];this.orgslist&&Array.isArray(this.orgslist)&&this.orgslist.forEach((function(t){e.push(t.id)})),e&&this.$set(this.formData,"show_orgs",e)}else this.$set(this.formData,"show_orgs",[])},clearForm:function(){this.$refs.formRef.clearValidate()}}},v=y,_=(r("e22c"),r("2877")),g=Object(_["a"])(v,a,o,!1,null,null,null);e["default"]=g.exports},"68ae":function(t,e,r){},e22c:function(t,e,r){"use strict";r("68ae")}}]);