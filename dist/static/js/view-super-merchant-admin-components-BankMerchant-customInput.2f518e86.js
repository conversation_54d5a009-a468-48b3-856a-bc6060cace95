(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-merchant-admin-components-BankMerchant-customInput"],{c3cc:function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"ps-flex"},[e("el-input",{class:"textarea"!=t.inputType?"w-180":"w-350 h-100",attrs:{placeholder:t.inputPlaceHolder,autocomplete:"off",type:t.inputType,disabled:t.inputDisabled,clearable:"",rows:t.inputRows,maxlength:t.inputMaxLength,autosize:{minRows:t.inputRows,maxRows:t.inputRows}},on:{input:t.handlerInputChange},model:{value:t.inputContent,callback:function(e){t.inputContent=e},expression:"inputContent"}}),e("div",{staticClass:"m-l-10 m-t-10"},[t._v(t._s(t.currentLength)+"/"+t._s(t.inputMaxLength))])],1)},a=[],s={name:"customInput",props:{value:{type:String,default:""},type:{type:String,default:"text"},disabled:{type:Boolean,default:!1},maxLength:{type:Number,default:50},placeholder:{type:String,default:"请输入"},natureType:{type:String,default:""},rows:{type:Number,default:2}},data:function(){return{inputContent:this.value,inputType:this.type,inputDisabled:this.disabled,inputRows:this.rows,currentLength:this.value?this.value.length:0,inputMaxLength:this.maxLength,inputPlaceHolder:this.placeholder}},watch:{value:function(t){this.inputContent=t},disabled:function(t){this.inputDisabled=t}},methods:{handlerInputChange:function(t){var e=t?t.length:0;e>this.maxLength&&(this.$message.error("超出最大字符"+this.maxLength+"限制"),t=t.slice(0,this.maxLength),this.inputContent=t,e=t.length),this.$set(this,"currentLength",e),this.$emit("inputChange",t,this.natureType)}}},u=s,p=n("2877"),l=Object(p["a"])(u,i,a,!1,null,"7537d78b",null);e["default"]=l.exports}}]);