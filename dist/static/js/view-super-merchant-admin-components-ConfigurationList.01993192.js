(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-merchant-admin-components-ConfigurationList","view-super-merchant-admin-components-addRootOrganization~view-super-merchant-admin-components-appidSetting"],{b164:function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"ps-el-drawer"},[t("el-drawer",{attrs:{visible:e.visible,"show-close":!1,size:"75%"},scopedSlots:e._u([{key:"title",fn:function(){return[t("div",{staticClass:"ps-flex-align-c flex-align-c"},[t("div",{staticClass:"m-r-20"},[e._v(e._s("merchant"===e.type?"食堂管理系统-功能配置":"移动端菜单配置："))]),"merchant"===e.type?t("div",{staticClass:"ps-el-popover"},[t("el-popover",{attrs:{placement:"bottom",title:"部分特殊权限控制说明：",width:"710",trigger:"hover"}},[t("div",{staticClass:"popover"},[t("div",{staticClass:"m-b-5"},[e._v("1、售货柜订单-消费订单 "),t("span",{staticClass:"f-w-700 red"},[e._v("[ 退款 ]")]),e._v(" 权限跟随消费订单-堂食订单 "),t("span",{staticClass:"f-w-700 red"},[e._v("[ 退款 ]")]),e._v(" 权限控制；")]),t("div",{staticClass:"m-b-5"},[e._v("2、食材标签 "),t("span",{staticClass:"f-w-700 red"},[e._v("[ 添加标签组 ]")]),e._v(" "),t("span",{staticClass:"f-w-700 red"},[e._v("[ 添加标签 ]")]),e._v(" "),t("span",{staticClass:"f-w-700 red"},[e._v("[ 编辑 ]")]),e._v(" "),t("span",{staticClass:"f-w-700 red"},[e._v("[ 删除 ]")]),e._v(" 权限跟随菜品标签 "),t("span",{staticClass:"f-w-700 red"},[e._v("[ 添加标签组 ]")]),e._v(" "),t("span",{staticClass:"f-w-700 red"},[e._v("[ 添加标签 ]")]),e._v(" "),t("span",{staticClass:"f-w-700 red"},[e._v("[ 编辑 ]")]),e._v(" "),t("span",{staticClass:"f-w-700 red"},[e._v("[ 删除 ]")]),e._v(" 对应权限控制；")]),t("div",{staticClass:"m-b-5"},[e._v("3、商品管理-商品分类 "),t("span",{staticClass:"f-w-700 red"},[e._v("[ 新增商品 ]")]),e._v(" 权限跟随商品管理-商品信息"),t("span",{staticClass:"f-w-700 red"},[e._v("[ 新增 ]")]),e._v("权限控制；")]),t("div",{staticClass:"m-b-5"},[e._v("4、挂失卡管理 "),t("span",{staticClass:"f-w-700 red"},[e._v("[ 取消挂失 ]")]),e._v(" "),t("span",{staticClass:"f-w-700 red"},[e._v("[ 补卡 ]")]),e._v(" 权限跟随用户列表 "),t("span",{staticClass:"f-w-700 red"},[e._v("[ 取消挂失 ]")]),e._v(" "),t("span",{staticClass:"f-w-700 red"},[e._v("[ 补卡 ]")]),e._v(" 对应权限控制；")])]),t("i",{staticClass:"el-icon-question",attrs:{slot:"reference"},slot:"reference"})])],1):e._e()])]},proxy:!0}])},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],staticClass:"p-20"},["app"!==e.type?t("el-tabs",{staticClass:"version-configuration-content-box",attrs:{type:"card","tab-position":"left"}},e._l(e.merchantFeatureList,(function(n,r){return t("el-tab-pane",{key:r,attrs:{label:n.verbose_name}},[t("div",{staticClass:"p-20"},[t("div",{staticClass:"m-b-10 w-150"},[t("el-checkbox",{staticClass:"ps-flex-align-c flex-align-c",attrs:{indeterminate:n.isIndeterminate,disabled:n.canNotSelect},on:{change:function(t){return e.dataListHandle(n.isSelect,n,r,!0)}},model:{value:n.isSelect,callback:function(t){e.$set(n,"isSelect",t)},expression:"item.isSelect"}},[t("span",{staticClass:"font-size-18 f-w-500"},[e._v("全选当前页")])])],1),e._l(n.children,(function(n,i){return t("div",{key:i,staticClass:"m-b-20"},[t("div",{staticClass:"w-150"},[t("el-checkbox",{staticClass:"ps-flex-align-c flex-align-c m-b-10",attrs:{indeterminate:n.isIndeterminate,disabled:n.canNotSelect},on:{change:function(t){return e.dataListHandle(n.isSelect,n,r,!1)}},model:{value:n.isSelect,callback:function(t){e.$set(n,"isSelect",t)},expression:"item1.isSelect"}},[t("span",{staticClass:"font-size-18 f-w-700"},[e._v(e._s(n.verbose_name))])])],1),t("div",{staticStyle:{"border-top":"1px solid #e5e7ea"}},e._l(n.children,(function(n,i){return t("div",{key:i,staticClass:"box-item flex-start",style:i%2===0?{backgroundColor:"#ffffff"}:{backgroundColor:"#f8f9fa"}},[t("div",{class:[n.children.length?"":"box-item-left","p-20"]},[t("el-checkbox",{attrs:{indeterminate:n.isIndeterminate,disabled:n.canNotSelect},on:{change:function(t){return e.dataListHandle(n.isSelect,n,r,!1)}},model:{value:n.isSelect,callback:function(t){e.$set(n,"isSelect",t)},expression:"item2.isSelect"}},[t("span",{staticClass:"ellipsis w-100"},[e._v(e._s(n.verbose_name))])])],1),n.children.length&&!e.hasChildren(n.children)?t("div",{class:[n.children.length?"box-item-right1":"","p-20","w-100-p"]},e._l(n.children,(function(n,i){return t("div",{key:i},[t("el-checkbox",{attrs:{disabled:n.canNotSelect},on:{change:function(t){return e.dataListHandle(n.isSelect,n,r,!1)}},model:{value:n.isSelect,callback:function(t){e.$set(n,"isSelect",t)},expression:"item3.isSelect"}},[t("span",{staticClass:"ellipsis w-150"},[e._v(e._s(n.verbose_name))])])],1)})),0):e._e(),n.children.length&&e.hasChildren(n.children)?t("div",{class:[n.children.length?"box-item-right2":"","w-100-p"]},e._l(n.children,(function(i,s){return t("div",{key:s,staticClass:"three-level flex-start",style:s<n.children.length-1?{borderBottom:"1px solid #e5e7ea"}:{}},[t("el-checkbox",{staticClass:"p-20",attrs:{disabled:i.canNotSelect},on:{change:function(t){return e.dataListHandle(i.isSelect,i,r,!1)}},model:{value:i.isSelect,callback:function(t){e.$set(i,"isSelect",t)},expression:"item3.isSelect"}},[t("span",{staticClass:"ellipsis w-150"},[e._v(e._s(i.verbose_name))])]),i.children.length?t("div",{staticClass:"three-level-right p-20 w-100-p"},e._l(i.children,(function(n,i){return t("div",{key:i},[t("el-checkbox",{attrs:{disabled:n.canNotSelect},on:{change:function(t){return e.dataListHandle(n.isSelect,n,r,!1)}},model:{value:n.isSelect,callback:function(t){e.$set(n,"isSelect",t)},expression:"item4.isSelect"}},[t("span",{staticClass:"ellipsis w-150"},[e._v(e._s(n.verbose_name))])])],1)})),0):e._e()],1)})),0):e._e()])})),0)])}))],2)])})),1):t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],staticClass:"version-configuration-content-box2"},[t("div",{staticClass:"p-20"},[t("div",{staticClass:"box-item p-t-20 p-l-20 p-r-20"},[t("el-row",{attrs:{gutter:20}},e._l(e.merchantFeatureList,(function(n,r){return t("el-col",{key:r,attrs:{span:6}},[t("el-checkbox",{staticClass:"m-b-20",attrs:{disabled:n.canNotSelect},model:{value:n.isSelect,callback:function(t){e.$set(n,"isSelect",t)},expression:"item.isSelect"}},[t("span",{staticClass:"ellipsis w-180"},[e._v(e._s(n.verbose_name))])])],1)})),1)],1)])]),t("div",{staticClass:"version-configuration-content-footer"},[t("div",{staticClass:"button-area m-r-40"},[t("el-button",{staticClass:"w-100",attrs:{size:"small"},on:{click:e.cancel}},[e._v("取消")]),t("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:e.save}},[e._v("保存")])],1),t("div",{staticClass:"checkbox-area m-r-40"},[t("el-checkbox",{on:{change:function(t){return e.isSelectAll("selectAll",!0)}},model:{value:e.selectAll,callback:function(t){e.selectAll=t},expression:"selectAll"}},[t("span",{staticClass:"font-size-16"},[e._v("全选")])]),t("el-checkbox",{on:{change:function(t){return e.isSelectAll("selectNone",!1)}},model:{value:e.selectNone,callback:function(t){e.selectNone=t},expression:"selectNone"}},[t("span",{staticClass:"font-size-16"},[e._v("全不选")])])],1),t("div",[e._v("定制数量："+e._s(e.computedSelectCount)+"/"+e._s(e.computedTotalCount))])])],1)])],1)},i=[],s=n("ed08"),a=n("2f62");function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function o(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */o=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(e,t,n){e[t]=n.value},s="function"==typeof Symbol?Symbol:{},a=s.iterator||"@@iterator",l=s.asyncIterator||"@@asyncIterator",u=s.toStringTag||"@@toStringTag";function h(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{h({},"")}catch(e){h=function(e,t,n){return e[t]=n}}function f(e,t,n,r){var s=t&&t.prototype instanceof y?t:y,a=Object.create(s.prototype),c=new I(r||[]);return i(a,"_invoke",{value:j(e,n,c)}),a}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var p="suspendedStart",v="suspendedYield",m="executing",b="completed",g={};function y(){}function S(){}function w(){}var _={};h(_,a,(function(){return this}));var C=Object.getPrototypeOf,L=C&&C(C(A([])));L&&L!==n&&r.call(L,a)&&(_=L);var x=w.prototype=y.prototype=Object.create(_);function k(e){["next","throw","return"].forEach((function(t){h(e,t,(function(e){return this._invoke(t,e)}))}))}function O(e,t){function n(i,s,a,o){var l=d(e[i],e,s);if("throw"!==l.type){var u=l.arg,h=u.value;return h&&"object"==c(h)&&r.call(h,"__await")?t.resolve(h.__await).then((function(e){n("next",e,a,o)}),(function(e){n("throw",e,a,o)})):t.resolve(h).then((function(e){u.value=e,a(u)}),(function(e){return n("throw",e,a,o)}))}o(l.arg)}var s;i(this,"_invoke",{value:function(e,r){function i(){return new t((function(t,i){n(e,r,t,i)}))}return s=s?s.then(i,i):i()}})}function j(t,n,r){var i=p;return function(s,a){if(i===m)throw Error("Generator is already running");if(i===b){if("throw"===s)throw a;return{value:e,done:!0}}for(r.method=s,r.arg=a;;){var c=r.delegate;if(c){var o=E(c,r);if(o){if(o===g)continue;return o}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===p)throw i=b,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=m;var l=d(t,n,r);if("normal"===l.type){if(i=r.done?b:v,l.arg===g)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(i=b,r.method="throw",r.arg=l.arg)}}}function E(t,n){var r=n.method,i=t.iterator[r];if(i===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,E(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var s=d(i,t.iterator,n.arg);if("throw"===s.type)return n.method="throw",n.arg=s.arg,n.delegate=null,g;var a=s.arg;return a?a.done?(n[t.resultName]=a.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,g):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,g)}function P(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function N(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function I(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(P,this),this.reset(!0)}function A(t){if(t||""===t){var n=t[a];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,s=function n(){for(;++i<t.length;)if(r.call(t,i))return n.value=t[i],n.done=!1,n;return n.value=e,n.done=!0,n};return s.next=s}}throw new TypeError(c(t)+" is not iterable")}return S.prototype=w,i(x,"constructor",{value:w,configurable:!0}),i(w,"constructor",{value:S,configurable:!0}),S.displayName=h(w,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===S||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,h(e,u,"GeneratorFunction")),e.prototype=Object.create(x),e},t.awrap=function(e){return{__await:e}},k(O.prototype),h(O.prototype,l,(function(){return this})),t.AsyncIterator=O,t.async=function(e,n,r,i,s){void 0===s&&(s=Promise);var a=new O(f(e,n,r,i),s);return t.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},k(x),h(x,u,"Generator"),h(x,a,(function(){return this})),h(x,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=A,I.prototype={constructor:I,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(N),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function i(r,i){return c.type="throw",c.arg=t,n.next=r,i&&(n.method="next",n.arg=e),!!i}for(var s=this.tryEntries.length-1;s>=0;--s){var a=this.tryEntries[s],c=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var o=r.call(a,"catchLoc"),l=r.call(a,"finallyLoc");if(o&&l){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(o){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var s=i;break}}s&&("break"===e||"continue"===e)&&s.tryLoc<=t&&t<=s.finallyLoc&&(s=null);var a=s?s.completion:{};return a.type=e,a.arg=t,s?(this.method="next",this.next=s.finallyLoc,g):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),N(n),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;N(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:A(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),g}},t}function l(e,t){return p(e)||d(e,t)||h(e,t)||u()}function u(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function h(e,t){if(e){if("string"==typeof e)return f(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?f(e,t):void 0}}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function d(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,s,a,c=[],o=!0,l=!1;try{if(s=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;o=!1}else for(;!(o=(r=s.call(n)).done)&&(c.push(r.value),c.length!==t);o=!0);}catch(e){l=!0,i=e}finally{try{if(!o&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}function p(e){if(Array.isArray(e))return e}function v(e,t,n,r,i,s,a){try{var c=e[s](a),o=c.value}catch(e){return void n(e)}c.done?t(o):Promise.resolve(o).then(r,i)}function m(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var s=e.apply(t,n);function a(e){v(s,r,i,a,c,"next",e)}function c(e){v(s,r,i,a,c,"throw",e)}a(void 0)}))}}function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){y(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function y(e,t,n){return(t=S(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function S(e){var t=w(e,"string");return"symbol"==c(t)?t:t+""}function w(e,t){if("object"!=c(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=c(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var _={props:{isShow:Boolean,type:String},data:function(){return{isLoading:!1,merchantFeatureList:[],selectNone:!1,selectAll:!1,haveBeenSelectKey:[],versionList:[],selectedVersionData:{}}},computed:g(g({},Object(a["c"])(["permissionData","versionPermissionData"])),{},{visible:{get:function(){return this.isShow},set:function(e){this.$emit("update:isShow",e)}},hasChildren:function(){return function(e){var t=!1;return e.forEach((function(e){e.children.length&&(t=!0)})),t}},computedSelectCount:function(){var e=0;return"app"===this.type?this.merchantFeatureList.forEach((function(t){t.isSelect&&e++})):this.merchantFeatureList.forEach((function(t){e+=t.tabSelectCount})),e},computedTotalCount:function(){var e=0;return"app"===this.type?this.merchantFeatureList.forEach((function(t){e++})):this.merchantFeatureList.forEach((function(t){e+=t.tabTotalCount})),e}}),watch:{selectAll:function(e,t){e&&this.selectNone&&(this.selectNone=!1)},selectNone:function(e,t){e&&this.selectAll&&(this.selectAll=!1)},visible:function(e){if(e){switch(this.haveBeenSelectKey=[],this.type){case"merchant":this.haveBeenSelectKey=Object(s["f"])(this.permissionData.permission);break;case"merchant_app":this.haveBeenSelectKey=Object(s["f"])(this.permissionData.merchant_app_permission);break;case"app":this.haveBeenSelectKey=Object(s["f"])(this.permissionData.app_permission);break}-1===this.permissionData.tollVersion?this.selectedVersionData={permission:[],merchant_app_permission:[],app_permission:[]}:this.selectedVersionData={permission:this.versionPermissionData.permission,merchant_app_permission:this.versionPermissionData.merchant_app_permission,app_permission:this.versionPermissionData.app_permission},"app"===this.type?this.getAppPermission():this.getPermissionList()}}},created:function(){},methods:{getPermissionList:function(){var e=this;return m(o().mark((function t(){var n,r,i,a,c;return o().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.merchantFeatureList=[],e.isLoading=!0,t.next=4,Object(s["Z"])("merchant"===e.type?e.$apis.apiBackgroundAdminOrganizationGetMerchantPermissionsPost():e.$apis.apiBackgroundAdminOrganizationGetMerchantMobilePermissions());case 4:if(n=t.sent,r=l(n,2),i=r[0],a=r[1],!i){t.next=11;break}return e.$message.error(i.message),t.abrupt("return");case 11:0===a.code?(e.merchantFeatureList=a.data.map((function(t,n){return Object.assign(t,{tabSelectCount:0,tabTotalCount:0}),e.switchStatus(t),e.switchIndeterminate(!0,t),t})),c=e.merchantFeatureList.map((function(t,n){return e.setCount(t,n),t})),e.merchantFeatureList=Object(s["f"])(c),e.isLoading=!1):e.$message.error(a.msg);case 12:case"end":return t.stop()}}),t)})))()},getAppPermission:function(){var e=this;return m(o().mark((function t(){var n,r,i,a;return o().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.merchantFeatureList=[],e.isLoading=!0,t.next=4,Object(s["Z"])(e.$apis.apiBackgroundAdminOrganizationGetAppPermissionsPost());case 4:if(n=t.sent,r=l(n,2),i=r[0],a=r[1],e.isLoading=!1,!i){t.next=12;break}return e.$message.error(i.message),t.abrupt("return");case 12:0===a.code?(e.merchantFeatureList=a.data[0].children.map((function(t,n){return e.switchStatus(t),t})),e.isLoading=!1):e.$message.error(a.msg);case 13:case"end":return t.stop()}}),t)})))()},switchIndeterminate:function(e,t){var n=this;this.changeSelfIndeterminate(e,t),t.children&&t.children.length&&t.children.map((function(t){return t.isSelect&&n.switchIndeterminate(e,t),t}))},dataListHandle:function(e,t,n,r){var i=this;e?this.selectNone=!1:this.selectAll=!1,this.selectAllItem(e,t,n),this.changeSelfIndeterminate(e,t),r||this.merchantFeatureList.forEach((function(n){i.changeParentIndeterminate(e,n,t)})),"app"!==this.type&&(this.merchantFeatureList[n].tabSelectCount=0,this.merchantFeatureList[n].isSelect&&this.merchantFeatureList[n].tabSelectCount++,this.merchantFeatureList[n].children&&this.merchantFeatureList[n].children.length&&this.resetTabSelectCount(this.merchantFeatureList[n].children,n))},changeSelfIndeterminate:function(e,t){var n=this;e?t.children.some((function(e){return e.isSelect}))&&t.children.some((function(e){return!e.isSelect}))?t.isIndeterminate=!0:t.isIndeterminate=!1:t.children.some((function(e){return e.isSelect}))?t.isIndeterminate=!0:(t.children.some((function(e){return e.isSelect})),t.isIndeterminate=!1),t.children&&t.children.length&&t.children.forEach((function(t){n.changeSelfIndeterminate(e,t)}))},changeParentIndeterminate:function(e,t,n){var r=this;t.index===n.parent?(e?t.children.some((function(e){return e.isSelect}))?t.children.some((function(e){return!e.isSelect}))?(t.isIndeterminate=!0,t.isSelect=!0):(t.isIndeterminate=!1,t.isSelect=!0):(t.isIndeterminate=!1,t.isSelect=!1):t.children.some((function(e){return e.isSelect}))?(t.isIndeterminate=!0,t.isSelect=!0):t.children.some((function(e){return e.isSelect}))?(t.isIndeterminate=!1,t.isSelect=!0):(t.isIndeterminate=!1,t.isSelect=!1),0!==t.level&&this.merchantFeatureList.forEach((function(n){r.changeParentIndeterminate(e,n,t)}))):t.children&&t.children.length&&t.children.forEach((function(t){r.changeParentIndeterminate(e,t,n)}))},resetTabSelectCount:function(e,t){var n=this;e.length&&e.forEach((function(e){e.isSelect&&n.merchantFeatureList[t].tabSelectCount++,e.children&&e.children.length&&n.resetTabSelectCount(e.children,t)}))},setCount:function(e,t){var n=this;Object.keys(e).includes("isSelect")&&this.merchantFeatureList[t].tabTotalCount++,e.isSelect&&this.merchantFeatureList[t].tabSelectCount++,e.children&&e.children.length&&e.children.forEach((function(e){n.setCount(e,t)}))},switchStatus:function(e){var t=this;if(Object.assign(e,{isSelect:!1,isIndeterminate:!1,canNotSelect:!1}),this.haveBeenSelectKey.length&&this.haveBeenSelectKey.includes(e.key)){e.isSelect=!0;var n=[];n="app"!==this.type?this.getDifference("merchant"===this.type?this.selectedVersionData.permission:this.selectedVersionData.merchant_app_permission,this.haveBeenSelectKey):this.getDifference(this.selectedVersionData.app_permission,this.haveBeenSelectKey),-1===this.permissionData.tollVersion||n.includes(e.key)||(e.canNotSelect=!0)}else e.isSelect=!1;e.children&&e.children.length&&e.children.forEach((function(e){t.switchStatus(e)}))},getDifference:function(e,t){var n=e.filter((function(e){return!t.includes(e)})),r=t.filter((function(t){return!e.includes(t)}));return n.concat(r)},selectAllItem:function(e,t,n){var r=this;t.isSelect=e,t.children&&t.children.length&&t.children.forEach((function(t){r.selectAllItem(e,t,n)}))},isSelectAll:function(e,t){var n=this;("selectAll"===e&&t||"selectNone"===e&&!t)&&(this.isLoading=!0,this.merchantFeatureList.forEach((function(e,r){e.canNotSelect||n.searchCanNoSelect(e,t,r),n.isLoading=!1,e.tabSelectCount=0,t?e.tabSelectCount=e.tabTotalCount:(e.isSelect&&n.merchantFeatureList[r].tabSelectCount++,e.children&&e.children.length&&n.resetTabSelectCount(e.children,r))})))},searchCanNoSelect:function(e,t,n){var r=this;e.canNotSelect?e.children&&e.children.length&&e.children.forEach((function(e){r.searchCanNoSelect(e,t)})):(e.isSelect=t,this.dataListHandle(t,e,n,!0))},save:function(){this.haveBeenSelectKey=Object(s["f"])(this.traverseGroups(this.merchantFeatureList)),this.$emit("refreshPermission",this.haveBeenSelectKey),this.selectAll=!1,this.selectNone=!1,this.visible=!1},cancel:function(){this.selectAll=!1,this.selectNone=!1,this.visible=!1},traverseGroups:function(e){var t=[];return this.traverseGroupsDetail(e,t),t},traverseGroupsDetail:function(e,t){var n=this;e.forEach((function(e){e.isSelect&&t.push(e.key),e.children&&e.children.length&&n.traverseGroupsDetail(e.children,t)}))}}},C=_,L=(n("d144"),n("2877")),x=Object(L["a"])(C,r,i,!1,null,"703abf34",null);t["default"]=x.exports},d144:function(e,t,n){"use strict";n("dd9b")},dd9b:function(e,t,n){}}]);