(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-supplier-admin-SupplierAdmin"],{7472:function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"meal-details container-wrapper"},[t("refresh-tool",{on:{refreshPage:e.refreshHandle}}),t("search-form",{ref:"searchRef",attrs:{"form-setting":e.searchFormSetting,autoSearch:!1,"label-width":"100px"},on:{search:e.searchHandle}}),t("div",{staticClass:"table-wrapper"},[t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[e._v("数据列表")]),t("div",{staticClass:"align-r"},[t("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_food.ingredient_supplier.add"],expression:"['background_food.ingredient_supplier.add']"}],attrs:{color:"origin",type:"add"},on:{click:function(t){return e.addOrEditSupplier("add")}}},[e._v("新建")])],1)]),t("div",{staticClass:"table-content"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"",border:"","header-row-class-name":"ps-table-header-row"},on:{"selection-change":e.handleSelectionChange}},[t("el-table-column",{attrs:{prop:"no",label:"供应商编号",align:"center"}}),t("el-table-column",{attrs:{prop:"name",label:"供应商名称",align:"center"}}),t("el-table-column",{attrs:{prop:"credit_code",label:"",align:"center",width:"190"}},[t("template",{slot:"header"},[t("div",[e._v("工商营业执照")]),t("div",[e._v("（社会统一信用代码）")])])],2),t("el-table-column",{attrs:{prop:"tax_registration_license",label:"税务登记证",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-image",{staticClass:"w-100-p",attrs:{src:r.row.tax_registration_license,"preview-src-list":e.srcList,alt:"暂无图片"},on:{click:function(t){return e.imgRegistrationClick(r.row)}}})]}}])}),t("el-table-column",{attrs:{prop:"business_license",label:"食品经营许可证",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-image",{staticClass:"w-100-p",attrs:{src:r.row.business_license,"preview-src-list":e.srcList,alt:"暂无图片"},on:{click:function(t){return e.imgBusinessClick(r.row)}}})]}}])}),t("el-table-column",{attrs:{prop:"circulation_license",label:"食品流通许可证",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-image",{staticClass:"w-100-p",attrs:{src:r.row.circulation_license,"preview-src-list":e.srcList,alt:"暂无图片"},on:{click:function(t){return e.imgCirculationClick(r.row)}}})]}}])}),t("el-table-column",{attrs:{prop:"person_name",label:"联系人",align:"center"}}),t("el-table-column",{attrs:{prop:"phone",label:"联系人电话",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",[e._v(e._s(e.sensitiveSetting.phone?r.row.phone:"****"))])]}}])}),t("el-table-column",{attrs:{prop:"address",label:"供应商地址",align:"center"}}),t("el-table-column",{attrs:{prop:"create_time",label:"创建时间",align:"center"}}),t("el-table-column",{attrs:{fixed:"right",label:"操作",width:"180",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_food.ingredient_supplier.add_supplier_ingredient"],expression:"['background_food.ingredient_supplier.add_supplier_ingredient']"}],staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.supplierIngredient(r.row)}}},[e._v(" 关联食材 ")]),t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_food.ingredient_supplier.modify"],expression:"['background_food.ingredient_supplier.modify']"}],staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.addOrEditSupplier("modify",r.row)}}},[e._v(" 编辑 ")]),t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_food.ingredient_supplier.delete"],expression:"['background_food.ingredient_supplier.delete']"}],attrs:{type:"text",size:"small"},on:{click:function(t){return e.mulOperation("del",r.row)}}},[e._v(" 删除 ")])]}}])})],1)],1),t("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[t("el-pagination",{staticClass:"ps-text",attrs:{"current-page":e.currentPage,"page-sizes":[10,20,50,100,500],"page-size":e.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)])],1)},i=[],o=r("ed08");function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,i=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},c=o.iterator||"@@iterator",l=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function p(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{p({},"")}catch(e){p=function(e,t,r){return e[t]=r}}function f(e,t,r,n){var o=t&&t.prototype instanceof b?t:b,a=Object.create(o.prototype),s=new I(n||[]);return i(a,"_invoke",{value:j(e,r,s)}),a}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var h="suspendedStart",g="suspendedYield",m="executing",v="completed",y={};function b(){}function w(){}function _(){}var S={};p(S,c,(function(){return this}));var x=Object.getPrototypeOf,L=x&&x(x(N([])));L&&L!==r&&n.call(L,c)&&(S=L);var k=_.prototype=b.prototype=Object.create(S);function O(e){["next","throw","return"].forEach((function(t){p(e,t,(function(e){return this._invoke(t,e)}))}))}function C(e,t){function r(i,o,s,c){var l=d(e[i],e,o);if("throw"!==l.type){var u=l.arg,p=u.value;return p&&"object"==a(p)&&n.call(p,"__await")?t.resolve(p.__await).then((function(e){r("next",e,s,c)}),(function(e){r("throw",e,s,c)})):t.resolve(p).then((function(e){u.value=e,s(u)}),(function(e){return r("throw",e,s,c)}))}c(l.arg)}var o;i(this,"_invoke",{value:function(e,n){function i(){return new t((function(t,i){r(e,n,t,i)}))}return o=o?o.then(i,i):i()}})}function j(t,r,n){var i=h;return function(o,a){if(i===m)throw Error("Generator is already running");if(i===v){if("throw"===o)throw a;return{value:e,done:!0}}for(n.method=o,n.arg=a;;){var s=n.delegate;if(s){var c=P(s,n);if(c){if(c===y)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===h)throw i=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=m;var l=d(t,r,n);if("normal"===l.type){if(i=n.done?v:g,l.arg===y)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(i=v,n.method="throw",n.arg=l.arg)}}}function P(t,r){var n=r.method,i=t.iterator[n];if(i===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,P(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var o=d(i,t.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,y;var a=o.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,y):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function F(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function I(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function N(t){if(t||""===t){var r=t[c];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,o=function r(){for(;++i<t.length;)if(n.call(t,i))return r.value=t[i],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(a(t)+" is not iterable")}return w.prototype=_,i(k,"constructor",{value:_,configurable:!0}),i(_,"constructor",{value:w,configurable:!0}),w.displayName=p(_,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,p(e,u,"GeneratorFunction")),e.prototype=Object.create(k),e},t.awrap=function(e){return{__await:e}},O(C.prototype),p(C.prototype,l,(function(){return this})),t.AsyncIterator=C,t.async=function(e,r,n,i,o){void 0===o&&(o=Promise);var a=new C(f(e,r,n,i),o);return t.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},O(k),p(k,u,"Generator"),p(k,c,(function(){return this})),p(k,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=N,I.prototype={constructor:I,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(F),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function i(n,i){return s.type="throw",s.arg=t,r.next=n,i&&(r.method="next",r.arg=e),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),l=n.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,y):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),F(r),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;F(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:N(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),y}},t}function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach((function(t){u(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function u(e,t,r){return(t=p(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function p(e){var t=f(e,"string");return"symbol"==a(t)?t:t+""}function f(e,t){if("object"!=a(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=a(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function d(e,t,r,n,i,o,a){try{var s=e[o](a),c=s.value}catch(e){return void r(e)}s.done?t(c):Promise.resolve(c).then(n,i)}function h(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var o=e.apply(t,r);function a(e){d(o,n,i,a,s,"next",e)}function s(e){d(o,n,i,a,s,"throw",e)}a(void 0)}))}}var g={name:"MealReportDetail",data:function(){return{tableData:[],srcList:["https://fuss10.elemecdn.com/8/27/f01c15bb73e1ef3793e64e6b7bbccjpeg.jpeg"],isLoading:!1,searchFormSetting:{no:{type:"input",value:"",label:"供应商编号",placeholder:"请输入供应商编号",maxlength:30},name:{type:"input",value:"",label:"供应商名称",placeholder:"请输入供应商名称",maxlength:30},phone:{type:"input",value:"",label:"联系人电话",placeholder:"请输入联系人电话",maxlength:11},person_name:{type:"input",value:"",label:"联系人",placeholder:"请输入联系人",maxlength:10},credit_code:{type:"input",value:"",label:"工商营业执照",placeholder:"请输入工商营业执照",maxlength:30}},sensitiveSetting:{},pageSize:10,totalCount:0,currentPage:1}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getSensitiveSetting(),this.getFoodIngredientSupplierList()},searchHandle:Object(o["d"])((function(e){e&&"search"===e&&(this.currentPage=1,this.getFoodIngredientSupplierList())}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.currentPage=1,this.initLoad()},formatQueryParams:function(e){var t={};for(var r in e)""!==e[r].value&&null!==e[r].value&&0!==e[r].value.length&&("select_date"!==r?t[r]=e[r].value:e[r].value.length>0&&(t.start_date=e[r].value[0],t.end_date=e[r].value[1]));return t},getFoodIngredientSupplierList:function(){var e=this;return h(s().mark((function t(){var r;return s().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,e.$apis.apiBackgroundFoodIngredientSupplierListPost(l(l({},e.formatQueryParams(e.searchFormSetting)),{},{page:e.currentPage,page_size:e.pageSize}));case 3:r=t.sent,e.isLoading=!1,0===r.code?(e.tableData=r.data.results,e.totalCount=r.data.count):e.$message.error(r.msg);case 6:case"end":return t.stop()}}),t)})))()},mulOperation:function(e,t){var r={id:t.id,is_verify:0};this.deleteSupplierDelete(r)},deleteSupplierDelete:function(e){var t=this;return h(s().mark((function r(){var n;return s().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,t.$apis.apiBackgroundFoodIngredientSupplierDeletePost(e);case 2:n=r.sent,0===n.code?(t.$message.success(n.msg),t.getFoodIngredientSupplierList()):2===n.code?t.$confirm(n.msg,"提示",{confirmButtonText:t.$t("dialog.confirm_btn"),cancelButtonText:t.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var r=h(s().mark((function r(n,i,o){return s().wrap((function(r){while(1)switch(r.prev=r.next){case 0:"confirm"===n?(i.confirmButtonLoading=!0,t.deleteSupplierDelete({id:e.id,is_verify:1}),o(),i.confirmButtonLoading=!1):i.confirmButtonLoading||o();case 1:case"end":return r.stop()}}),r)})));function n(e,t,n){return r.apply(this,arguments)}return n}()}):t.$message.error(n.msg);case 4:case"end":return r.stop()}}),r)})))()},getSensitiveSetting:function(){var e=this;return h(s().mark((function t(){var r;return s().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,e.$apis.apiBackgroundReportCenterDataReportGetSensitiveSettingPost();case 3:r=t.sent,e.isLoading=!1,0===r.code?e.sensitiveSetting=r.data:e.$message.error(r.msg);case 6:case"end":return t.stop()}}),t)})))()},imgRegistrationClick:function(e){this.srcList[0]=e.tax_registration_license},imgBusinessClick:function(e){this.srcList[0]=e.business_license},imgCirculationClick:function(e){this.srcList[0]=e.circulation_license},handleSizeChange:function(e){this.pageSize=e,this.getFoodIngredientSupplierList()},handleCurrentChange:function(e){this.currentPage=e,this.getFoodIngredientSupplierList()},handleSelectionChange:function(e){},supplierIngredient:function(e){this.$router.push({name:"MerchantRelationSupplierIngredient",query:{supplier_id:e.id}})},addOrEditSupplier:function(e,t){this.$router.push({name:"MerchantAddEditSupplier",query:{type:e,data:encodeURIComponent(JSON.stringify(t))}})}}},m=g,v=r("2877"),y=Object(v["a"])(m,n,i,!1,null,"1fea6f51",null);t["default"]=y.exports}}]);