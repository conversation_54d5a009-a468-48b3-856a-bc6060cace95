(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-report-financial-statements-constantsConfig","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-meal-report-MealPackageRule","view-merchant-report-financial-statements-BusinessList"],{"3eb4":function(e,l,a){"use strict";a.r(l),a.d(l,"payMethods",(function(){return y})),a.d(l,"paymentOrderType",(function(){return v})),a.d(l,"DEVICE_STATUS",(function(){return m})),a.d(l,"ORDER_TYPE",(function(){return _})),a.d(l,"RECONCILIATION_STATUS_LIST",(function(){return g})),a.d(l,"PROCESSING_STATE",(function(){return h})),a.d(l,"PAYWAYLIST",(function(){return f})),a.d(l,"SUBPAYWAYLIST",(function(){return S})),a.d(l,"recentSevenDay",(function(){return T})),a.d(l,"DetailTotalSearchForm",(function(){return L})),a.d(l,"DetailTotalSearchForm2",(function(){return k})),a.d(l,"TopUpDetailSearchForm",(function(){return P})),a.d(l,"DeviceCodeSearchForm",(function(){return A})),a.d(l,"WithdrawSearchForm",(function(){return D})),a.d(l,"PERSONAL_RECHARGE_SUMMARY",(function(){return N})),a.d(l,"DEPARTMENTAL_CONSUMPTION_SUMMARY",(function(){return C})),a.d(l,"PERSONAL_CONSUMPTION_SUMMARY",(function(){return Y})),a.d(l,"ACCOUNT_WALLET_DAILY",(function(){return E})),a.d(l,"PersonalWalletDailySearchForm",(function(){return O})),a.d(l,"CollectionlCodeReportSearchForm",(function(){return M})),a.d(l,"THIRD_RECONCILIATION",(function(){return R})),a.d(l,"DeductionServiceReportSetting",(function(){return I})),a.d(l,"RechargeServiceReportSetting",(function(){return w})),a.d(l,"RECEIPTSTATUS",(function(){return U})),a.d(l,"RECEIPT_LIST_SEARCH",(function(){return K})),a.d(l,"WRITE_OFF_DETAIL",(function(){return x})),a.d(l,"PersonMealReportSearchForm",(function(){return V})),a.d(l,"SUBSIDY_CLEAR_DETAIL",(function(){return B})),a.d(l,"GROUP_PREPAID_SUMMARY",(function(){return z})),a.d(l,"COUPON_DETAILS",(function(){return F}));var t=a("5a0c"),i=a("c9d9"),u=a("e9c7"),r=a("ed08");function n(e){return b(e)||o(e)||p(e)||c()}function c(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function p(e,l){if(e){if("string"==typeof e)return d(e,l);var a={}.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?d(e,l):void 0}}function o(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function b(e){if(Array.isArray(e))return d(e)}function d(e,l){(null==l||l>e.length)&&(l=e.length);for(var a=0,t=Array(l);a<l;a++)t[a]=e[a];return t}var s=[{value:"ORDER_PART_REFUND",label:"部分退款"},{value:"ORDER_REFUND",label:"退款"},{value:"JC_CONSUME",label:"计次消费"},{value:"KF_CONSUME",label:"扣费消费"},{value:"RESERVATION_CONSUME",label:"预约消费"},{value:"REPORT_MEAL_CONSUME",label:"报餐消费"},{value:"BUFFET_CONSUME",label:"称重消费"},{value:"DRAW",label:"后台提现"},{value:"RECHARGE",label:"充值"},{value:"SUBSIDY_PUBLISH",label:"补贴发放"},{value:"SUBSIDY_CLEAR",label:"补贴清零"},{value:"SUBSIDY_CHARGE",label:"补贴冲销"},{value:"FLAT_COST_REFUND",label:"退费"},{value:"SUPPLEMENTARY",label:"补卡"},{value:"PUBLISH",label:"发卡"},{value:"ORDERING_FOOD_PAY",label:"订餐扣费"},{value:"UNKNOWN",label:"未知"},{value:"THIRD_PARTY_CONSUMPTION",label:"第三方消费"},{value:"ORDER_APPROVE_VISITOR_CONSUME",label:"访客消费"}],y=[{value:"PushiPay",label:"朴食储值支付"},{value:"OCOMPAY",label:"一卡通-鑫澳康支付"},{value:"SHIYAOPAY",label:"一卡通-石药支付"},{value:"ABCPay",label:"农行支付"},{value:"CCBPAY",label:"建行支付"},{value:"BOCPAY",label:"中行支付"},{value:"ICBCPAY",label:"工行支付"},{value:"MEITUANPAY",label:"美团支付"},{value:"ShouqianbaPay",label:"收钱吧支付"},{value:"WechatPay",label:"微信支付"},{value:"CashPay",label:"现金支付"}],v=[{value:"reservation",label:"预约订单"},{value:"report_meal",label:"报餐"},{value:"buffet",label:"称重"},{value:"instore",label:"到店就餐"},{value:"FLAT_COST_REFUND",label:"退费"},{value:"SUPPLEMENTARY",label:"补卡"},{value:"PUBLISH",label:"发卡"},{value:"other",label:"其他"},{value:"THIRD_PARTY_CONSUMPTION",label:"第三方消费"},{value:"zdshg",label:"自动售货柜"},{value:"order_approve_visitor",label:"访客消费"}],m=[{label:"全部",value:""},{label:"在线",value:"ONLINE"},{label:"离线",value:"OFFLINE"}],_=[{value:0,label:"消费类"},{value:1,label:"退款类"},{value:2,label:"提现类"},{value:3,label:"充值类"},{value:4,label:"补贴类"},{value:5,label:"工本费类"},{value:6,label:"缴费类"}],g=[{label:"全部",value:""},{label:"对账成功",value:"success"},{label:"对账失败",value:"failed"}],h=[{label:"全部",value:""},{label:"已处理",value:""},{label:"未处理",value:""}],f=[{value:"PushiPay",label:"储值支付"},{value:"OCOMPAY",label:"一卡通-鑫澳康支付"},{value:"SHIYAOPAY",label:"一卡通-石药支付"},{value:"ZhengYuanPay",label:"正元支付"},{value:"YTYPay",label:"正元易通云支付"},{value:"ABCPay",label:"农行支付"},{value:"CCBPay",label:"建行支付"},{value:"BOCPAY",label:"中行支付"},{value:"ICBCPAY",label:"工行支付"},{value:"PsbcPay",label:"邮储银行支付"},{value:"CMBPAY",label:"招商银行"},{value:"NingXiaPay",label:"宁夏银行"},{value:"NingXiaWxPay",label:"宁夏银行-微信"},{value:"UnionPay",label:"银联支付"},{value:"SuNingPay",label:"苏宁支付"},{value:"WXYFPay",label:"微邮付"},{value:"MEITUANPAY",label:"美团支付"},{value:"ShouqianbaPay",label:"收钱吧支付"},{value:"WechatPay",label:"微信支付"},{value:"AliPay",label:"支付宝"},{value:"YunShanFuPay",label:"云闪付支付"},{value:"WingPay",label:"翼支付"},{value:"QyWechatPay",label:"企业微信支付"},{value:"ZiTengPay",label:"紫藤支付"},{value:"UNKNOWN",label:"未知"},{value:"CashPay",label:"现金支付"},{value:"ThridPartyMachine",label:"第三方卡机支付"},{value:"UnionBankPay",label:"联合银行支付"},{value:"GZCBPay",label:"广州银行支付"},{value:"AliQyCodePay",label:"支付宝企业码支付"},{value:"QiZhiPay",label:"企智支付"},{value:"ELingPay",label:"壹零后支付"},{value:"ZhongHePay",label:"中核支付"},{value:"SodexoPay",label:"索迪斯支付"}],S=[{value:"jf",label:"缴费方式支付"},{value:"fastepay",label:"快e付支付"},{value:"daikou",label:"授权代扣支付"},{value:"ermb",label:"数字人民币"},{value:"jsapi",label:"JSAPI支付"},{value:"h5",label:"H5支付"},{value:"wap",label:"WAP支付"},{value:"miniapp",label:"小程序支付"},{value:"cash",label:"现金支付"},{value:"yhf",label:"邮惠付"},{value:"ermb_daikou",label:"数币授权代扣"},{value:"ermb_b2c",label:"数币网关支付"},{value:"ermb_scanpay",label:"数币C扫B支付"}],T=[t().subtract(7,"day").format("YYYY-MM-DD"),t().format("YYYY-MM-DD")],L={date_type:{type:"select",value:"create_time",dataList:[{label:"创建时间",value:"create_time"},{label:"支付时间",value:"pay_time"},{label:"扣款时间",value:"deduction_time"}]},select_time:{type:"daterange",value:T,format:"yyyy-MM-dd",clearable:!1},trade_no:{type:"input",value:"",label:"订单号",placeholder:"请输入订单号"},name:{type:"input",value:"",label:"姓名",placeholder:"请输入姓名"},phone:{type:"input",value:"",label:"手机号",placeholder:"请输入手机号"},person_no:{type:"input",value:"",label:"人员编号",placeholder:"请输入人员编号"},card_no:{type:"input",value:"",label:"卡号",placeholder:"请输入卡号"},payer_group_ids:{type:"groupSelect",label:"分组",value:[],placeholder:"请选择分组",dataList:[],multiple:!0,collapseTags:!0,clearable:!0},payer_department_group_ids:{type:"organizationDepartmentSelect",multiple:!0,checkStrictly:!0,flat:!1,label:"部门",value:[],placeholder:"请选择部门",dataList:[],limit:1,level:1,clearable:!0},payment_order_type_list:{type:"select",value:[],label:"消费类型",clearable:!0,multiple:!0,collapseTags:!0,dataList:v},payway_list:{type:"select",value:[],label:"支付类型",multiple:!0,collapseTags:!0,dataList:y,clearable:!0},sub_payway_list:{type:"select",value:[],label:"支付方式",dataList:[],multiple:!0,collapseTags:!0,clearable:!0},meal_type_list:{type:"select",label:"餐段",value:[],multiple:!0,placeholder:"请选择",collapseTags:!0,dataList:i["a"]},device_type_list:{type:"select",value:[],label:"设备类型",listNameKey:"name",listValueKey:"key",multiple:!0,collapseTags:!0,dataList:[],clearable:!0},device_name:{type:"input",value:"",label:"交易设备",placeholder:"请输入交易设备"},pay_device_status:{type:"select",value:"",label:"设备状态",dataList:m,clearable:!0},wallet_org:{type:"organizationSelect",value:[],label:"动账钱包",listNameKey:"name",listValueKey:"id",dataList:[],multiple:!0,checkStrictly:!0,collapseTags:!0,clearable:!0},org_ids:{type:"organizationSelect",value:[],label:"消费点",listNameKey:"name",listValueKey:"id",dataList:[],multiple:!0,checkStrictly:!0,collapseTags:!0,clearable:!0},controller:{type:"input",value:"",label:"操作员",placeholder:"请输入要搜索的操作员"},out_trade_no:{type:"input",value:"",label:"第三方订单号",placeholder:"请输入订单号",labelWidth:"100px"},pay_method:{type:"select",label:"访客餐支付方式",value:"",placeholder:"请选择",labelWidth:"110px",dataList:[{label:"全部",value:""},{label:"访客记账",value:"Accounting"},{label:"即付",value:"PayAtSight"}]}},k={date_type:{type:"select",value:"create_time",dataList:[{label:"创建时间",value:"create_time"},{label:"支付时间",value:"pay_time"},{label:"扣款时间",value:"deduction_time"}]},select_time:{type:"daterange",value:T,format:"yyyy-MM-dd",clearable:!1},trade_no:{type:"input",value:"",label:"订单号",placeholder:"请输入订单号"},name:{type:"input",value:"",label:"姓名",placeholder:"请输入姓名"},phone:{type:"input",value:"",label:"手机",placeholder:"请输入手机号"},person_no:{type:"input",value:"",label:"人员编号",placeholder:"请输入人员编号"},card_no:{type:"input",value:"",label:"卡号",placeholder:"请输入卡号"},payer_group_ids:{type:"groupSelect",label:"分组",value:[],placeholder:"请选择分组",dataList:[],multiple:!0,collapseTags:!0,clearable:!0},payer_department_group_ids:{type:"organizationDepartmentSelect",multiple:!0,checkStrictly:!0,flat:!1,label:"部门",value:[],placeholder:"请选择部门",dataList:[],limit:1,level:1,clearable:!0},operate_type_list:{type:"select",value:[],label:"操作类型",multiple:!0,collapseTags:!0,dataList:[].concat(s),clearable:!0},payway_list:{type:"select",value:"",label:"支付类型",multiple:!0,collapseTags:!0,dataList:[],clearable:!0},sub_payway_list:{type:"select",value:[],label:"支付方式",multiple:!0,collapseTags:!0,dataList:[],clearable:!0},meal_type_list:{type:"select",label:"餐段",value:[],multiple:!0,placeholder:"请选择",collapseTags:!0,dataList:i["a"]},wallet_org:{type:"organizationSelect",value:[],label:"动账组织",listNameKey:"name",listValueKey:"id",dataList:[],checkStrictly:!0,multiple:!0,collapseTags:!0,clearable:!0},org_ids:{type:"organizationSelect",value:[],label:"消费点",listNameKey:"name",listValueKey:"id",dataList:[],checkStrictly:!0,multiple:!0,collapseTags:!0,clearable:!0},controller:{type:"input",value:"",label:"操作员",placeholder:"请输入要搜索的操作员"},out_trade_no:{type:"input",value:"",label:"第三方订单号",placeholder:"请输入订单号",labelWidth:"100px"},pay_method:{type:"select",label:"访客餐支付方式",value:"",placeholder:"请选择",labelWidth:"110px",dataList:[{label:"全部",value:""},{label:"访客记账",value:"Accounting"},{label:"即付",value:"PayAtSight"}]},only_discount:{type:"checkbox",label:"",checkboxLabel:"只看优惠",value:!1},only_rate_fee:{type:"checkbox",label:"",checkboxLabel:"只看手续费",value:!1},only_debt_fee:{type:"checkbox",label:"",checkboxLabel:"只看透支",value:!1}},P={select_time:{type:"daterange",label:"充值时间",value:T,format:"yyyy-MM-dd",clearable:!1},name:{type:"input",value:"",label:"姓名",placeholder:"请输入姓名"},phone:{type:"input",value:"",label:"手机号",placeholder:"请输入手机号"},person_no:{type:"input",value:"",label:"人员编号",placeholder:"请输入人员编号"},payer_group_ids:{type:"groupSelect",label:"分组",value:[],placeholder:"请选择分组",dataList:[],listNameKey:"group_name",listValueKey:"id",multiple:!0,collapseTags:!0,clearable:!0},payer_department_group_ids:{type:"organizationDepartmentSelect",multiple:!0,flat:!1,checkStrictly:!0,label:"部门",value:[],placeholder:"请选择部门",dataList:[],limit:1,level:1,clearable:!0},recharge_method:{type:"select",value:"",label:"充值方式",dataList:[],clearable:!0},recharge_type:{type:"select",value:"",label:"充值类型",dataList:[{label:"线上充值",value:"charge"},{label:"线下充值",value:"charge_offline"}],clearable:!0},out_trade_no:{type:"input",value:"",label:"第三方订单号",placeholder:"请输入订单号",labelWidth:"100px"},only_rate_fee:{type:"checkbox",label:"",checkboxLabel:"只看手续费",value:!1}},A={select_time:{type:"daterange",label:"搜索日期",value:T},org_ids:{type:"organizationSelect",value:[],label:"消费点",listNameKey:"name",listValueKey:"id",dataList:[],checkStrictly:!0,multiple:!0,collapseTags:!0,clearable:!0},device_id:{type:"input",value:"",label:"设备号",placeholder:"请输入设备号"},device_type:{type:"select",value:"",label:"设备类型",listNameKey:"name",listValueKey:"key",dataList:[],clearable:!0}},D={select_time:{type:"daterange",label:"申请时间",value:T},trade_no:{type:"input",value:"",label:"订单号",placeholder:"请输入订单号",clearable:!0},name:{type:"input",value:"",label:"姓名",placeholder:"请输入姓名",clearable:!0},phone:{type:"input",value:"",label:"手机号",placeholder:"请输入手机号",clearable:!0},person_no:{type:"input",value:"",label:"人员编号",placeholder:"请输入人员编号",clearable:!0},card_no:{type:"input",value:"",label:"卡号",placeholder:"请输入卡号",clearable:!0},payer_group_ids:{type:"groupSelect",label:"分组",value:[],placeholder:"请选择分组",multiple:!0,collapseTags:!0,clearable:!0},payer_department_group_ids:{type:"organizationDepartmentSelect",multiple:!0,checkStrictly:!0,flat:!1,label:"部门",value:[],placeholder:"请选择部门",dataList:[],limit:1,level:1,clearable:!0},payway:{type:"select",value:"",label:"提现方式",dataList:y,clearable:!0}},N={select_time:{type:"daterange",label:"搜索时间",value:T,clearable:!1},name:{type:"input",value:"",label:"姓名",placeholder:"请输入姓名"},phone:{type:"input",value:"",label:"手机",placeholder:"请输入手机"},person_no:{type:"input",value:"",label:"人员编号",placeholder:"请输入人员编号"},payer_group_ids:{type:"groupSelect",label:"分组",value:[],placeholder:"请选择分组",dataList:[],multiple:!0,collapseTags:!0,clearable:!0},payer_department_group_ids:{type:"organizationDepartmentSelect",multiple:!0,checkStrictly:!0,flat:!1,label:"部门",value:[],placeholder:"请选择部门",dataList:[],limit:1,level:1,clearable:!0},payway:{type:"select",value:"",label:"充值方式",dataList:[],clearable:!0}},C={select_time:{labelWidth:"110px",type:"daterange",label:"消费时间",value:u["a"],pickerOptions:u["b"],clearable:!1},payer_department_group_ids:{type:"organizationDepartmentSelect",multiple:!0,checkStrictly:!0,flat:!1,label:"部门",value:[],placeholder:"请选择部门",dataList:[],limit:1,level:1,clearable:!0},org_ids:{type:"organizationSelect",value:[],label:"消费点",listNameKey:"name",listValueKey:"id",dataList:[],checkStrictly:!0,multiple:!0,collapseTags:!0,clearable:!0}},Y={select_time:{labelWidth:"110px",type:"daterange",label:"消费时间",value:u["a"],pickerOptions:u["b"],clearable:!1},name:{type:"input",value:"",label:"姓名",placeholder:"请输入姓名"},phone:{type:"input",value:"",label:"手机",placeholder:"请输入手机"},person_no:{type:"input",value:"",label:"人员编号",placeholder:"请输入人员编号"},payer_group_ids:{type:"groupSelect",label:"分组",value:[],placeholder:"请选择分组",dataList:[],multiple:!0,collapseTags:!0,clearable:!0},payer_department_group_ids:{type:"organizationDepartmentSelect",multiple:!0,checkStrictly:!0,flat:!1,label:"部门",value:[],placeholder:"请选择部门",dataList:[],limit:1,level:1,clearable:!0},org_ids:{type:"organizationSelect",value:[],label:"消费点",listNameKey:"name",listValueKey:"id",dataList:[],checkStrictly:!0,multiple:!0,collapseTags:!0,clearable:!0}},E={select_time:{type:"daterange",label:"搜索日期",value:u["a"],pickerOptions:u["b"],clearable:!1},org_ids:{type:"organizationSelect",value:[],label:"消费点",listNameKey:"name",listValueKey:"id",dataList:[],checkStrictly:!0,multiple:!0,collapseTags:!0,clearable:!0}},O={select_time:{type:"daterange",label:"搜索日期",value:u["a"],pickerOptions:u["b"],clearable:!1},org_ids:{type:"organizationSelect",value:[],label:"消费点",listNameKey:"name",listValueKey:"id",dataList:[],checkStrictly:!0,multiple:!0,collapseTags:!0,clearable:!0},name:{type:"input",value:"",label:"姓名",maxlength:20,placeholder:"请输入姓名"},person_no:{type:"input",value:"",label:"人员编号",placeholder:"请输入人员编号"},payer_group_ids:{type:"groupSelect",label:"分组",value:[],placeholder:"请选择分组",dataList:[],multiple:!0,collapseTags:!0,clearable:!0},payer_department_group_ids:{type:"organizationDepartmentSelect",multiple:!0,checkStrictly:!0,flat:!1,label:"部门",value:[],placeholder:"请选择部门",dataList:[],limit:1,level:1,clearable:!0}},M={select_time:{type:"daterange",label:"搜索日期",value:T,clearable:!1},org_ids:{type:"organizationSelect",value:[],label:"消费点",listNameKey:"name",listValueKey:"id",dataList:[],checkStrictly:!0,multiple:!0,collapseTags:!0,clearable:!0}},R={select_time:{type:"daterange",label:"时间",value:T},order_type:{type:"select",value:"",label:"订单类型",dataList:_,clearable:!0},trade_no:{type:"input",value:"",label:"总单号",placeholder:"请输入总单号",clearable:!0},business_no:{type:"input",value:"",label:"商户号",placeholder:"请输入总单号",clearable:!0},out_trade_no:{type:"input",value:"",label:"第三方订单号",placeholder:"请输入第三方订单号",clearable:!0},reconciliation_status:{type:"select",value:"",label:"对账状态",dataList:g,clearable:!0},processing_state:{type:"select",value:"",label:"处理状态",dataList:h,clearable:!0},payway_list:{type:"select",value:[],label:"支付类型",multiple:!0,collapseTags:!0,dataList:y,clearable:!0},sub_payway_list:{type:"select",value:[],label:"支付方式",dataList:[],multiple:!0,collapseTags:!0,clearable:!0}},I={date_type:{type:"select",value:"create_time",dataList:[{label:"创建时间",value:"create_time"},{label:"支付时间",value:"pay_time"},{label:"预约时间",value:"reservation_date"}]},select_date:{type:"daterange",value:T,format:"yyyy-MM-dd",clearable:!1},payway:{type:"select",listNameKey:"name",listValueKey:"payway",value:[],label:"支付渠道",dataList:[],clearable:!0,multiple:!0,collapseTags:!0},take_type:{type:"select",value:"",label:"就餐方式",dataList:[{label:"到店就餐",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐-堂食",value:"report_on_scene"},{label:"报餐-堂食自提",value:"report_bale"}],clearable:!0},trade_no:{type:"input",value:"",label:"涉及订单号",labelWidth:"100px",placeholder:"请输入涉及订单号",clearable:!0},org_ids:{type:"organizationSelect",value:[],label:"所属组织",listNameKey:"name",listValueKey:"id",dataList:[],checkStrictly:!0,multiple:!0,collapseTags:!0,clearable:!0},meal_time:{type:"select",value:null,label:"餐段",clearable:!0,dataList:[{label:"全部",value:""}].concat(n(i["a"]))}},w={date_type:{type:"select",value:"create_time",dataList:[{label:"创建时间",value:"create_time"},{label:"支付时间",value:"pay_time"}]},select_date:{type:"daterange",value:T,format:"yyyy-MM-dd",clearable:!1},payway:{type:"select",listNameKey:"name",listValueKey:"payway",value:[],label:"支付渠道",dataList:[],clearable:!0,multiple:!0,collapseTags:!0},org_ids:{type:"organizationSelect",value:[],label:"所属组织",listNameKey:"name",listValueKey:"id",dataList:[],checkStrictly:!0,multiple:!0,collapseTags:!0,clearable:!0},trade_no:{type:"input",value:"",label:"涉及订单号",labelWidth:"100px",placeholder:"请输入涉及订单号",clearable:!0}},U=[{label:"全部",value:""},{label:"已开票",value:"success"},{label:"未开票",value:"non_open"},{label:"开票失败",value:"fail"},{label:"红冲",value:"red"}],K={date_type:{type:"select",value:2,maxWidth:"130px",dataList:[{label:"创建时间",value:1},{label:"申请时间",value:2}]},select_time:{type:"daterange",label:"",clearable:!0,value:T},trade_no:{type:"input",value:"",label:"总单号/订单号",placeholder:""},invoice_status:{type:"select",label:"开票状态",value:"",placeholder:"",dataList:U},organization_ids:{type:"organizationSelect",value:[],label:"所属组织",checkStrictly:!0,isLazy:!1,multiple:!0},name:{type:"input",value:"",label:"开票人"}},x={select_time:{type:"daterange",label:"冲销时间",value:T,clearable:!1},name:{type:"input",value:"",label:"姓名",placeholder:"请输入姓名"},phone:{type:"input",value:"",label:"手机",placeholder:"请输入手机"},person_no:{type:"input",value:"",label:"人员编号",placeholder:"请输入人员编号"},payer_group_ids:{type:"groupSelect",label:"分组",value:[],placeholder:"请选择分组",dataList:[],multiple:!0,collapseTags:!0,clearable:!0},payer_department_group_ids:{type:"organizationDepartmentSelect",multiple:!0,checkStrictly:!0,flat:!1,label:"部门",value:[],placeholder:"请选择部门",dataList:[],limit:1,level:1,clearable:!0},order_no:{type:"input",value:"",label:"订单编号",placeholder:"请输入订单编号"}},V={date:{type:"date",label:"时间",clearable:!1,value:Object(r["M"])(new Date,"{y}-{m}-{d}")},org_id:{type:"organizationSelect",value:"",label:"消费点",listNameKey:"name",listValueKey:"id",dataList:[],multiple:!1,checkStrictly:!0,collapseTags:!0,clearable:!0},payer_group_id:{type:"groupSelect",label:"分组",value:"",placeholder:"请选择分组",dataList:[],clearable:!0},meal_status:{type:"select",value:"",label:"点餐情况",dataList:[{label:"已点餐",value:1},{label:"未点餐",value:0}],clearable:!0},meal_type:{type:"select",value:null,label:"餐段",clearable:!0,dataList:[{label:"全部",value:""}].concat(n(i["a"]))}},B={select_time:{type:"daterange",label:"清零时间",value:T,clearable:!1},name:{type:"input",value:"",label:"姓名",placeholder:"请输入姓名"},phone:{type:"input",value:"",label:"手机",placeholder:"请输入手机"},person_no:{type:"input",value:"",label:"人员编号",placeholder:"请输入人员编号"},payer_group_ids:{type:"groupSelect",label:"分组",value:[],placeholder:"请选择分组",dataList:[],multiple:!0,collapseTags:!0,clearable:!0},payer_department_group_ids:{type:"organizationDepartmentSelect",multiple:!0,checkStrictly:!0,flat:!1,label:"部门",value:[],placeholder:"请选择部门",dataList:[],limit:1,level:1,clearable:!0},order_no:{type:"input",value:"",labelWidth:"220px",label:"订单号/补贴编号/补贴批次号",placeholder:"请输入"}},z={select_time:{labelWidth:"110px",type:"daterange",label:"消费时间",value:u["a"],pickerOptions:u["b"],clearable:!1},org_ids:{type:"organizationSelect",value:[],label:"消费点",listNameKey:"name",listValueKey:"id",dataList:[],checkStrictly:!0,multiple:!0,collapseTags:!0,clearable:!0},payer_group_ids:{type:"groupSelect",label:"分组",value:[],placeholder:"请选择分组",dataList:[],multiple:!0,collapseTags:!0,clearable:!0}},F={trade_no:{type:"input",value:"",label:"订单号",placeholder:"请输入订单号"},phone:{type:"input",value:"",label:"手机号",placeholder:"请输入手机号"},person_no:{type:"input",value:"",label:"人员编号",placeholder:"请输入人员编号"},payer_group_ids:{type:"groupSelect",label:"分组",value:[],placeholder:"请选择分组",dataList:[],multiple:!0,collapseTags:!0,clearable:!0},coupon_type_list:{type:"select",value:[],label:"券类型",placeholder:"请选择券类型",multiple:!0,collapseTags:!0,dataList:[{label:"满减券",value:"FULL_DISCOUNT"},{label:"立减券",value:"INSTANT_DISCOUNT"},{label:"折扣券",value:"DISCOUNT"},{label:"兑换券",value:"EXCHANGE"}]},consume_organization_ids:{type:"organizationSelect",value:[],label:"所属组织",listNameKey:"name",listValueKey:"id",dataList:[],checkStrictly:!0,multiple:!0,collapseTags:!0,clearable:!0},meal_type_list:{type:"select",label:"餐段",value:[],multiple:!0,placeholder:"请选择",collapseTags:!0,dataList:i["a"]},name:{type:"input",value:"",label:"姓名",placeholder:"请输入姓名"}}},c9d9:function(e,l,a){"use strict";a.d(l,"a",(function(){return u})),a.d(l,"d",(function(){return r})),a.d(l,"b",(function(){return n})),a.d(l,"c",(function(){return c})),a.d(l,"e",(function(){return p})),a.d(l,"f",(function(){return o})),a.d(l,"g",(function(){return b}));var t=a("5a0c"),i=a("da92"),u=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],r=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],n={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},c=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],p=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],o=(t().subtract(7,"day").format("YYYY-MM-DD"),t().format("YYYY-MM-DD"),function(e){return e?"number"===typeof e?i["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:i["a"].divide(e,100).toFixed(2):"0.00"}),b=function(e){return i["a"].times(e,100)}},e9c7:function(e,l,a){"use strict";a.d(l,"a",(function(){return u})),a.d(l,"c",(function(){return r})),a.d(l,"b",(function(){return n}));var t=a("5a0c"),i=t().subtract(1,"day").format("YYYY/MM/DD"),u=[t().subtract(7,"day").format("YYYY-MM-DD"),t(i).format("YYYY-MM-DD")],r={disabledDate:function(e){return e.getTime()>Date.now()},shortcuts:[{text:"最近一周",onClick:function(e){var l=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,l])}},{text:"最近一个月",onClick:function(e){var l=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,l])}},{text:"最近三个月",onClick:function(e){var l=new Date,a=new Date;a.setTime(a.getTime()-7776e6),e.$emit("pick",[a,l])}}]},n={disabledDate:function(e){return e.getTime()>new Date(i+" 23:59:59")},shortcuts:[{text:"最近一周",onClick:function(e){var l=new Date(i),a=new Date(i);a.setTime(a.getTime()-5184e5),e.$emit("pick",[a,l])}},{text:"最近一个月",onClick:function(e){var l=new Date(i),a=new Date(i);a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,l])}},{text:"最近三个月",onClick:function(e){var l=new Date(i),a=new Date(i);a.setTime(a.getTime()-7776e6),e.$emit("pick",[a,l])}}]}}}]);