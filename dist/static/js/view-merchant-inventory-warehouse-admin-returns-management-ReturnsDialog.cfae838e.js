(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-inventory-warehouse-admin-returns-management-ReturnsDialog"],{"25d3":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("dialog-message",{staticClass:"returns-dialog",attrs:{show:t.visible,title:t.dialogTitle,showFooter:t.showFooter,loading:t.isLoading,width:t.dialogWidth,top:"200px"},on:{"update:show":function(e){t.visible=e},"update:loading":function(e){t.isLoading=e},closed:t.handlerClose,cancel:t.clickCancleHandle,confirm:t.clickConfirmHandle}},[e("transition-group",{attrs:{name:t.slideTransition},on:{"after-enter":t.afterEnter}},["returnType"===t.dialogType?e("div",{key:"returnType",staticClass:"return-type"},[e("div",{staticClass:"text-center m-b-20"},[e("el-button",{staticClass:"ps-btn",attrs:{size:"medium"},on:{click:function(e){return t.changeDialogType("returnAll")}}},[t._v("整批退货")])],1),e("div",{staticClass:"text-center"},[e("el-button",{staticClass:"ps-btn",attrs:{size:"medium"},on:{click:function(e){return t.changeDialogType("returnMul")}}},[t._v("批量退货")])],1)]):t._e(),"returnAll"===t.dialogType||"returnMul"===t.dialogType?e("div",{key:"tableForm",staticClass:"return-table"},[e("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formRef",attrs:{model:t.formData,rules:t.formRules,"label-width":"0","label-position":"top",size:"small"}},["returnMul"===t.dialogType?e("div",{staticClass:"m-b-20"},[e("el-table",{ref:"tableData",staticStyle:{width:"100%"},attrs:{data:t.formData.tableData,size:"small",stripe:"","header-row-class-name":"ps-table-header-row","max-height":"400","row-key":t.rowKey,"reserve-selection":""},on:{"selection-change":t.handleSelectionChange}},t._l(t.tableSettings,(function(r){return e("table-column",{key:r.key,attrs:{col:r},scopedSlots:t._u([{key:"count",fn:function(n){var i=n.row,a=n.index;return[e("el-form-item",{key:r.key+a+i.select,staticClass:"m-b-0 max-w-220",attrs:{label:"",prop:"tableData."+a+".returnCount",rules:i.select?t.formRules.returnCount:[],width:r.width,"show-message":!1}},[e("el-input",{attrs:{disabled:!i.select},model:{value:i.returnCount,callback:function(e){t.$set(i,"returnCount",e)},expression:"row.returnCount"}})],1)]}},{key:"money",fn:function(n){var i=n.row,a=n.index;return[e("el-form-item",{key:r.key+a+i.select,staticClass:"m-b-0 max-w-220",attrs:{label:"",prop:"tableData."+a+".returnMoney",rules:i.select?t.formRules.returnMoney:[],width:r.width,"show-message":!1}},[e("el-input",{attrs:{disabled:!i.select},model:{value:i.returnMoney,callback:function(e){t.$set(i,"returnMoney",e)},expression:"row.returnMoney"}})],1)]}}],null,!0)})})),1)],1):t._e(),e("div",{},[e("el-form-item",{attrs:{label:"退货说明",prop:"remark"}},[e("el-input",{staticClass:"ps-textarea w-280",attrs:{type:"textarea",rows:4,maxlength:50},model:{value:t.formData.remark,callback:function(e){t.$set(t.formData,"remark",e)},expression:"formData.remark"}})],1),e("el-form-item",{attrs:{label:"上传附件",prop:"imageFile"}},[e("el-upload",{ref:"uploadFoodImage",class:{"upload-food":!0,"hide-upload":t.formData.imageFileList.length>8},attrs:{drag:"",data:t.uploadParams,action:t.actionUrl,multiple:!1,"file-list":t.formData.imageFileList,"list-type":"picture-card","on-change":t.handelChange,"on-success":t.handleImgSuccess,"before-upload":t.beforeImgUpload,limit:3,headers:t.headersOpts},scopedSlots:t._u([{key:"file",fn:function(r){var n=r.file;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:"uploading"===n.status,expression:"file.status==='uploading'"}],attrs:{"element-loading-text":"上传中"}},[e("div",{staticClass:"upload-food-img"},[e("img",{attrs:{src:n.url,alt:""}})]),e("span",{staticClass:"el-upload-list__item-actions"},[e("span",{staticClass:"el-upload-list__item-preview",on:{click:function(e){return t.handlePictureCardPreview(n)}}},[e("i",{staticClass:"el-icon-zoom-in"})]),e("span",{staticClass:"el-upload-list__item-delete",on:{click:function(e){return t.handleImgRemove(n,"imageFile")}}},[e("i",{staticClass:"el-icon-delete"})])])])}}],null,!1,2702688443)},[t.formData.imageFileList.length<3?e("i",{staticClass:"el-icon-plus"}):t._e()])],1)],1)])],1):t._e()]),e("el-dialog",{attrs:{visible:t.dialogVisible},on:{"update:visible":function(e){t.dialogVisible=e}}},[e("img",{attrs:{width:"100%",src:t.dialogImageUrl,alt:""}})])],1)},i=[],a=r("ed08"),o=r("e925");function l(t){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},l(t)}function u(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */u=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},o=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",c=a.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function d(t,e,r,n){var a=e&&e.prototype instanceof b?e:b,o=Object.create(a.prototype),l=new T(n||[]);return i(o,"_invoke",{value:C(t,r,l)}),o}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=d;var p="suspendedStart",m="suspendedYield",g="executing",y="completed",v={};function b(){}function w(){}function _(){}var k={};f(k,o,(function(){return this}));var x=Object.getPrototypeOf,D=x&&x(x($([])));D&&D!==r&&n.call(D,o)&&(k=D);var L=_.prototype=b.prototype=Object.create(k);function O(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function j(t,e){function r(i,a,o,u){var s=h(t[i],t,a);if("throw"!==s.type){var c=s.arg,f=c.value;return f&&"object"==l(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,o,u)}),(function(t){r("throw",t,o,u)})):e.resolve(f).then((function(t){c.value=t,o(c)}),(function(t){return r("throw",t,o,u)}))}u(s.arg)}var a;i(this,"_invoke",{value:function(t,n){function i(){return new e((function(e,i){r(t,n,e,i)}))}return a=a?a.then(i,i):i()}})}function C(e,r,n){var i=p;return function(a,o){if(i===g)throw Error("Generator is already running");if(i===y){if("throw"===a)throw o;return{value:t,done:!0}}for(n.method=a,n.arg=o;;){var l=n.delegate;if(l){var u=S(l,n);if(u){if(u===v)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===p)throw i=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=g;var s=h(e,r,n);if("normal"===s.type){if(i=n.done?y:m,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(i=y,n.method="throw",n.arg=s.arg)}}}function S(e,r){var n=r.method,i=e.iterator[n];if(i===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,S(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var a=h(i,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,v;var o=a.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function P(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function $(e){if(e||""===e){var r=e[o];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,a=function r(){for(;++i<e.length;)if(n.call(e,i))return r.value=e[i],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(l(e)+" is not iterable")}return w.prototype=_,i(L,"constructor",{value:_,configurable:!0}),i(_,"constructor",{value:w,configurable:!0}),w.displayName=f(_,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,f(t,c,"GeneratorFunction")),t.prototype=Object.create(L),t},e.awrap=function(t){return{__await:t}},O(j.prototype),f(j.prototype,s,(function(){return this})),e.AsyncIterator=j,e.async=function(t,r,n,i,a){void 0===a&&(a=Promise);var o=new j(d(t,r,n,i),a);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},O(L),f(L,c,"Generator"),f(L,o,(function(){return this})),f(L,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=$,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(P),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function i(n,i){return l.type="throw",l.arg=e,r.next=n,i&&(r.method="next",r.arg=t),!!i}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],l=o.completion;if("root"===o.tryLoc)return i("end");if(o.tryLoc<=this.prev){var u=n.call(o,"catchLoc"),s=n.call(o,"finallyLoc");if(u&&s){if(this.prev<o.catchLoc)return i(o.catchLoc,!0);if(this.prev<o.finallyLoc)return i(o.finallyLoc)}else if(u){if(this.prev<o.catchLoc)return i(o.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return i(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=t,o.arg=e,a?(this.method="next",this.next=a.finallyLoc,v):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),P(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var i=n.arg;P(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:$(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function s(t,e){return p(t)||h(t,e)||f(t,e)||c()}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(t,e){if(t){if("string"==typeof t)return d(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?d(t,e):void 0}}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function h(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,a,o,l=[],u=!0,s=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=a.call(r)).done)&&(l.push(n.value),l.length!==e);u=!0);}catch(t){s=!0,i=t}finally{try{if(!u&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return l}}function p(t){if(Array.isArray(t))return t}function m(t,e,r,n,i,a,o){try{var l=t[a](o),u=l.value}catch(t){return void r(t)}l.done?e(u):Promise.resolve(u).then(n,i)}function g(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var a=t.apply(e,r);function o(t){m(a,n,i,o,l,"next",t)}function l(t){m(a,n,i,o,l,"throw",t)}o(void 0)}))}}function y(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function v(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?y(Object(r),!0).forEach((function(e){b(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function b(t,e,r){return(e=w(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function w(t){var e=_(t,"string");return"symbol"==l(e)?e:e+""}function _(t,e){if("object"!=l(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=l(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var k={name:"ReturnsDialog",props:{showreturns:Boolean,loading:Boolean,type:{type:String,default:"static"},api:{type:String,default:"apiBackgroundDrpExitInfoMaterialsRefundPost"},rowKey:{type:String,default:"custon_id"},params:{type:Object,default:function(){return{}}},tableSettings:{type:Array,default:function(){return[{label:"",key:"selection",type:"selection",reserveSelection:!0},{label:"物资名称",key:"materials_name"},{label:"入库数量",key:"entry_count"},{label:"当前数量",key:"current_num"},{label:"单位",key:"unit_name"},{label:"入库价",key:"entry_fee",type:"money"},{label:"合计金额",key:"total_fee",type:"money"},{label:"退货数量",key:"returnCount",type:"slot",slotName:"count"},{label:"退货金额",key:"returnMoney",type:"slot",slotName:"money"},{label:"供应商",key:"supplier_manage_name"}]}},staticList:{type:Array,default:function(){return[]}},formatResult:Function},data:function(){return{isLoading:!1,dialogTitle:"选择退货类型",dialogType:"returnType",dialogWidth:"400px",dateRange:[],tableData:[],pageSize:10,totalCount:0,currentPage:1,slideTransition:"slide-left",formData:{tableData:[],imageFile:[],imageFileList:[],remark:""},formRules:{remark:[{required:!0,message:"请填写退货说明",trigger:"change"}],returnCount:[{validator:this.validateCount,trigger:"change"}],returnMoney:[{validator:this.validataPrice,trigger:"change"}]},uploadParams:{prefix:"inventoryImage"},actionUrl:"/api/background/file/upload",headersOpts:{TOKEN:Object(a["B"])()},dialogImageUrl:[],dialogVisible:!1,showFooter:!1,selectList:[]}},computed:{visible:{get:function(){return this.showreturns},set:function(t){this.$emit("update:showreturns",t)}},total:function(){return"static"===this.type?this.staticList.length:this.totalCount}},watch:{showreturns:function(t){t&&this.init()}},created:function(){},mounted:function(){},methods:{init:function(){this.slideTransition="slide-left",this.currentPage=1;var t=Object(a["f"])(this.staticList);this.formData.tableData=t.map((function(t){return t.custon_id="".concat(t.id,"_").concat(t.supplier_manage_id),t.returnCount=t.entry_count,t.returnMoney=Object(a["i"])(t.total_fee),t}))},setDefaultSelectHandle:function(){var t=this;this.$nextTick((function(e){t.formData.tableData.forEach((function(e){t.$refs.tableData.toggleRowSelection(e,!0)}))}))},onPaginationChange:function(t){this.currentPage=t.current,this.pageSize=t.pageSize},changeDialogType:function(t){this.dialogType=t,this.showFooter=!0,this.dialogWidth="860px","returnAll"===t&&(this.dialogTitle="整批退货"),"returnMul"===t&&(this.dialogTitle="批量退货",this.setDefaultSelectHandle())},afterEnter:function(){},handelChange:function(t,e){this.uploadParams.key=this.uploadParams.prefix+"_"+(new Date).getTime()+Math.floor(150*Math.random())+".png"},handleImgSuccess:function(t,e,r){var n=this;0===t.code?(this.formData.imageFileList=r,this.formData.imageFile.push(t.data.public_url),this.$nextTick((function(){n.$refs.formRef.clearValidate("imageFile")}))):this.$message.error(t.msg)},handleImgRemove:function(t,e){var r=this.formData[e+"List"].findIndex((function(e){return e.url===t.url}));this.formData[e].splice(r,1),this.formData[e+"List"].splice(r,1)},beforeImgUpload:function(t){var e=[".jpeg",".jpg",".png",".svg"],r=t.size/1024/1024>5;return e.includes(Object(a["A"])(t.name))?r?(this.$message.error("上传图片大小不能超过 5MB!"),!1):void 0:(this.$message.error("上传图片只能是PNG/JPG/SVG格式!"),!1)},handlePictureCardPreview:function(t){this.dialogImageUrl=t.url,this.dialogVisible=!0},handleSelectionChange:function(t){var e=this,r=[];t&&(r=t.map((function(t){return t[e.rowKey]}))),this.formData.tableData.forEach((function(t){e.$set(t,"select",r.includes(t[e.rowKey]))})),this.selectList=t},validateCount:function(t,e,r,n,i){try{var a=t.field.split("."),l=this.formData[a[0]][Number(a[1])];e?Object(o["k"])(e)?l&&Number(l.current_num)<Number(e)?r(new Error("不能大于当前库存")):r():r(new Error("格式错误")):r(new Error("退货数量不能为空"))}catch(u){r(new Error("校验出错了"))}},validataPrice:function(t,e,r,n,i){try{var l=t.field.split("."),u=this.formData[l[0]][Number(l[1])];e?Object(o["m"])(e)?u&&Object(a["Y"])(e)>u.total_fee?r(new Error("退货金额不能大于当前合计金额")):r():r(new Error("格式错误")):r(new Error("退货金额不能为空"))}catch(s){r(new Error("校验出错了"))}},handlerClose:function(t){this.slideTransition="slide-right",this.isLoading=!1,this.showFooter=!1,this.dialogTitle="选择退货类型",this.dialogType="returnType",this.dialogWidth="400px",this.formData={tableData:[],imageFile:[],imageFileList:[],remark:""}},clickCancleHandle:function(){this.visible=!1},checkedFormHandle:function(){var t=!0;this.formData.remark||(t=!1);for(var e=0;e<this.formData.tableData.length;e++){var r=this.formData.tableData[e];+r.returnCount>r.current_num&&(t=!1),Object(a["Y"])(r.returnMoney||0)>r.total_fee&&(t=!1)}return t},formatParams:function(){var t=v(v({},this.params),{},{refund_remark:this.formData.remark,image_json:this.formData.imageFile});return"returnAll"===this.dialogType?t.refund_type="all_refund":(t.refund_type="bulk_refund",t.refund_info=this.selectList.map((function(t){return{materials_id:t.id,refund_count:+t.returnCount,refund_fee:Object(a["Y"])(t.returnMoney),supplier_manage_id:t.supplier_manage_id}}))),t},clickConfirmHandle:function(){var t=this;this.$refs.formRef.validate((function(e){e&&t.submitHandle(t.formatParams())}))},submitHandle:function(t){var e=this;return g(u().mark((function r(){var n,i,a,o;return u().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return e.isLoading=!0,r.next=3,e.$to(e.$apis.apiBackgroundDrpExitInfoMaterialsRefundPost(t));case 3:if(n=r.sent,i=s(n,2),a=i[0],o=i[1],e.isLoading=!1,!a){r.next=11;break}return e.$message.error(a.message||"出错了"),r.abrupt("return");case 11:0===o.code?(e.visible=!1,e.$emit("confirmReturn")):e.$message.error(o.msg);case 12:case"end":return r.stop()}}),r)})))()}}},x=k,D=(r("59243"),r("2877")),L=Object(D["a"])(x,n,i,!1,null,null,null);e["default"]=L.exports},59243:function(t,e,r){"use strict";r("7ea0")},"7ea0":function(t,e,r){},e925:function(t,e,r){"use strict";r.d(e,"c",(function(){return n})),r.d(e,"g",(function(){return i})),r.d(e,"i",(function(){return a})),r.d(e,"e",(function(){return o})),r.d(e,"h",(function(){return l})),r.d(e,"f",(function(){return u})),r.d(e,"d",(function(){return s})),r.d(e,"m",(function(){return c})),r.d(e,"l",(function(){return f})),r.d(e,"n",(function(){return d})),r.d(e,"j",(function(){return h})),r.d(e,"b",(function(){return p})),r.d(e,"k",(function(){return m})),r.d(e,"a",(function(){return g}));var n=function(t){return/^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(t)},i=function(t){return/^([0-9]{3,4}-)?[0-9]{7,8}$/.test(t.toString())},a=function(t){return/^\w{5,20}$/.test(t)},o=function(t){return/^(?=.*[0-9])(?=.*[a-zA-Z])(.{8,20})$/.test(t)},l=function(t){return/(^([0-9]{3,4}-)?[0-9]{7,8}$)|(^1[3,4,5,6,7,8,9][0-9]{9}$)/.test(t.toString())},u=function(t){return/^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(t.toString())},s=function(t){return/\d/.test(t)},c=function(t){return/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(t)},f=function(t){return/^(([1-9]?[0-9])|([1-9]?[0-9]\.[1-9])|([1-9]?[1-9]\.[0]))$/.test(t)},d=function(t){return/^(([0-9])|([0-9]\.\d{1,3})|([1-9]\.[0]))$/.test(t)},h=function(t){return/^-?(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(t)},p=function(t){return/^[0-9]+$/.test(t)},m=function(t){return/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1}|[1-9][0-9]*\.\d{1})))$/.test(t)},g=function(t){return/^[a-zA-Z0-9]+$/.test(t)}}}]);