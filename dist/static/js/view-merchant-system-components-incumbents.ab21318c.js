(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-system-components-incumbents"],{"5abb":function(e,t,s){"use strict";s("a676")},a676:function(e,t,s){},e145:function(e,t,s){"use strict";s.r(t);var a=function(){var e=this,t=e._self._c;return t("div",[t("div",{staticClass:"table-header"},[t("div",{staticClass:"header-left"},[t("div",{staticClass:"header-left-title"},[e._v("在职人员（"+e._s(e.totalCount)+"人）")]),t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_fund_supervision.job_person.add_job_person"],expression:"['background_fund_supervision.job_person.add_job_person']"}],staticClass:"ps-text m-r-10",attrs:{type:"text",size:"small"},on:{click:function(t){return e.showDrawer("add")}}},[e._v("添加")]),t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_fund_supervision.job_person.batch_import"],expression:"['background_fund_supervision.job_person.batch_import']"}],staticClass:"ps-text m-r-10",attrs:{type:"text",size:"small"},on:{click:function(t){e.importDrawerShow=!0}}},[e._v("导入")]),t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_fund_supervision.job_person.list_export"],expression:"['background_fund_supervision.job_person.list_export']"}],staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:e.gotoExport}},[e._v("导出")])],1)]),t("div",{staticClass:"table-content"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},e._l(e.tableSetting,(function(s,a){return t("table-column",{key:a,attrs:{col:s},scopedSlots:e._u([{key:"healthCertificate",fn:function(s){var a=s.row;return[t("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small",disabled:a.status},on:{click:function(t){return e.handleClick(a)}}},[e._v("查看")])]}},{key:"operation",fn:function(s){var a=s.row;return[t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_fund_supervision.job_person.modify_job_person"],expression:"['background_fund_supervision.job_person.modify_job_person']"}],staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.showDrawer("edit",a)}}},[e._v("编辑")]),t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_fund_supervision.job_person.delete_job_person"],expression:"['background_fund_supervision.job_person.delete_job_person']"}],staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.deleteHandle(a)}}},[e._v("删除")])]}}],null,!0)})})),1),t("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[t("el-pagination",{staticClass:"ps-text",attrs:{"current-page":e.page,"page-sizes":[10,20,50,100,500],"page-size":e.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),t("div",{staticClass:"ps-el-drawer"},[t("el-drawer",{attrs:{title:"add"===e.selectType?"添加人员":"编辑人员",visible:e.drawerShow,"show-close":!1,size:"40%"}},[t("div",{staticClass:"p-20"},[t("el-form",{ref:"drawerFormRef",attrs:{model:e.drawerForm,"label-width":"100px","label-position":"right"}},[t("el-form-item",{attrs:{label:"姓名",prop:"name",rules:[{required:!0,message:"请输入姓名",trigger:["change","blur"]}]}},[t("el-input",{staticClass:"w-300",attrs:{placeholder:"请输入姓名，不超过20个字",maxlength:"20"},model:{value:e.drawerForm.name,callback:function(t){e.$set(e.drawerForm,"name",t)},expression:"drawerForm.name"}})],1),t("el-form-item",{attrs:{label:"联系电话",prop:"phone",rules:[{required:!0,message:"请输入联系电话",trigger:["change","blur"]}]}},[t("el-input",{staticClass:"w-300",attrs:{placeholder:"请输入联系电话，不超过11位",maxlength:"11"},model:{value:e.drawerForm.phone,callback:function(t){e.$set(e.drawerForm,"phone",t)},expression:"drawerForm.phone"}})],1),t("el-form-item",{attrs:{label:"所属岗位",prop:"post",rules:[{required:!0,message:"请输入岗位信息",trigger:["change","blur"]}]}},[t("el-input",{staticClass:"w-300",attrs:{placeholder:"请输入岗位信息，不超过20个字",maxlength:"20"},model:{value:e.drawerForm.post,callback:function(t){e.$set(e.drawerForm,"post",t)},expression:"drawerForm.post"}})],1),t("el-form-item",{attrs:{label:"健康证件",prop:"img",rules:[{required:!0,message:"请上传有效证件",trigger:["change","blur"]}]}},[t("div",{staticClass:"certification-info-show-tips"},[e._v(" 图片最大不超过2MB，仅支持jpg,png格式 ")]),t("el-upload",{directives:[{name:"loading",rawName:"v-loading",value:e.uploading,expression:"uploading"}],ref:"fileUpload",staticClass:"upload-w",attrs:{"element-loading-text":"上传中",action:e.serverUrl,"file-list":e.fileLists,"on-success":e.uploadSuccess,"before-upload":e.beforeFoodImgUpload,limit:1,multiple:!1,"show-file-list":!1,headers:e.headersOpts,accept:".jpeg,.jpg,.png,.bmp"}},[e.drawerForm.img?t("img",{staticStyle:{width:"100px",height:"100px"},attrs:{src:e.drawerForm.img}}):t("div",{staticStyle:{width:"100px",height:"100px",border:"1px dashed #C0C4CC","text-align":"center","line-height":"100px"}},[t("i",{staticClass:"el-icon-plus"})])])],1),t("el-form-item",{attrs:{label:"证件有效期",prop:"time",rules:[{required:!0,message:"请选择证件的过期日期",trigger:["change","blur"]}]}},[t("el-date-picker",{attrs:{"picker-options":e.pickerOptions,type:"date","value-format":"yyyy-MM-dd",placeholder:"选择日期"},model:{value:e.drawerForm.time,callback:function(t){e.$set(e.drawerForm,"time",t)},expression:"drawerForm.time"}})],1)],1),t("div",{staticClass:"ps-el-drawer-footer"},[t("el-button",{staticClass:"w-100",attrs:{size:"small"},on:{click:e.cancelHandle}},[e._v("取消")]),t("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:e.saveHandle}},[e._v("保存")])],1)],1)]),t("el-drawer",{attrs:{title:"导入在职人员",visible:e.importDrawerShow,"show-close":!1,size:"40%"}},[t("div",[t("import-upload-file",{staticClass:"m-t-20",attrs:{uploadFormItemLabel:"批量添加",fileType:"zip",uploadParams:e.uploadParams,link:e.downloadUrl},on:{publicUrl:e.publicUrl}}),t("div",{staticClass:"p-l-20 ps-el-drawer-footer"},[t("el-button",{staticClass:"w-100",attrs:{size:"small"},on:{click:function(t){e.importDrawerShow=!1}}},[e._v("取消")]),t("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:e.importFile}},[e._v("保存")])],1)],1)])],1),e.showImagePreview?t("el-image-viewer",{staticStyle:{"z-index":"3000"},attrs:{"url-list":e.previewList,"hide-on-click-modal":"",teleported:"","on-close":e.closePreview}}):e._e()],1)},r=[],i=s("ed08"),o=s("a1d6"),n=s("08a9"),l=s("f63a"),p={mixins:[l["a"]],components:{ElImageViewer:n["a"],ImportUploadFile:o["a"]},data:function(){return{isLoading:!1,tableData:[],tableSetting:[{label:"姓名",key:"name"},{label:"联系电话",key:"phone"},{label:"所属岗位",key:"job_title"},{label:"健康证",key:"img_url",type:"slot",slotName:"healthCertificate"},{label:"证件有效期",key:"effective_time"},{label:"修改时间",key:"update_time"},{label:"操作人",key:"operator"},{label:"操作",key:"operation",type:"slot",slotName:"operation",width:"200"}],page:1,pageSize:10,totalCount:0,selectType:"",selectId:"",drawerShow:!1,drawerForm:{name:"",phone:"",post:"",img:"",time:""},uploading:!1,serverUrl:"/api/background/file/upload",uploadParams:{prefix:"incumbents",key:"incumbents"+(new Date).getTime()+Math.floor(150*Math.random())},fileLists:[],headersOpts:{TOKEN:Object(i["B"])()},pickerOptions:{disabledDate:function(e){return e.getTime()<Date.now()}},importDrawerShow:!1,showImagePreview:!1,previewList:[],downloadUrl:"/api/temporary/template_excel/batch_import_job_person.zip",importUrl:""}},created:function(){this.getDataList()},methods:{publicUrl:function(e){this.importUrl=e},importFile:function(){var e=this;this.$apis.apiBackgroundFundSupervisionJobPersonBatchImportPost({face_zip_url:this.importUrl}).then((function(t){0===t.code?e.$message.success("导入成功"):e.$message.error(t.msg),e.importDrawerShow=!1,e.isLoading=!0,setTimeout((function(){e.getDataList()}),500)}))},gotoExport:function(){var e={url:"apiBackgroundFundSupervisionJobPersonListExportPost",params:{page:this.currentPage,page_size:this.totalCount}};this.exportHandle(e)},uploadSuccess:function(e,t,s){this.uploading=!1,e&&0===e.code?(this.fileLists=[],this.drawerForm.img=e.data.public_url):(this.drawerForm.img="",this.$message.error(e.msg))},beforeFoodImgUpload:function(e){var t=[".jpeg",".jpg",".png",".bmp"],s=e.size/1024/1024<=2;return t.includes(Object(i["A"])(e.name))?s?void(this.uploading=!0):(this.$message.error("上传图片大小不能超过 2MB!"),!1):(this.$message.error("上传图片只能是 JPG 格式或者PNG格式!"),!1)},handleSizeChange:function(e){this.pageSize=e,this.getDataList()},handleCurrentChange:function(e){this.page=e,this.getDataList()},getDataList:function(){var e=this;this.isLoading=!0;var t={page:this.page,page_size:this.pageSize};this.$apis.apiBackgroundFundSupervisionJobPersonJobPersonListPost(t).then((function(t){0===t.code?(e.isLoading=!1,e.tableData=Object(i["f"])(t.data.results||[]),e.totalCount=t.data.count):e.$message.error(t.msg)}))},handleClick:function(e){this.previewList=[e.img_url],document.body.style.overflow="hidden",this.showImagePreview=!0},closePreview:function(){this.previewList=[],this.showImagePreview=!1,document.body.style.overflow="auto"},showDrawer:function(e,t){this.selectType=e,"edit"===e&&(this.selectId=t.id,this.drawerForm.phone=t.phone,this.drawerForm.img=t.img_url,this.drawerForm.name=t.name,this.drawerForm.post=t.job_title,this.drawerForm.time=t.effective_time),this.drawerShow=!0},cancelHandle:function(){this.$refs.drawerFormRef.resetFields(),this.drawerShow=!1},saveHandle:function(){var e={id:"add"===this.selectType?void 0:this.selectId,phone:this.drawerForm.phone,img_url:this.drawerForm.img,name:this.drawerForm.name,job_title:this.drawerForm.post,effective_time:this.drawerForm.time};"add"===this.selectType?this.addJobPerson(e):this.editJobPerson(e)},addJobPerson:function(e){var t=this;this.$apis.apiBackgroundFundSupervisionJobPersonAddJobPersonPost(e).then((function(e){0===e.code?t.$message.success("新增成功"):t.$message.error(e.msg),t.$refs.drawerFormRef.resetFields(),t.drawerShow=!1,t.getDataList()}))},editJobPerson:function(e){var t=this;this.$apis.apiBackgroundFundSupervisionJobPersonModifyJobPersonPost(e).then((function(e){0===e.code?t.$message.success("编辑成功"):t.$message.error(e.msg),t.$refs.drawerFormRef.resetFields(),t.drawerShow=!1,t.getDataList()}))},deleteHandle:function(e){var t=this;this.$confirm("确定要删除 ".concat(e.name," 的人员信息？删除后不可恢复，请谨慎操作。"),"提示",{distinguishCancelAndClose:!0,confirmButtonText:"确定",cancelButtonText:"取消"}).then((function(){t.$apis.apiBackgroundFundSupervisionJobPersonDeleteJobPersonPost({id:e.id}).then((function(e){0===e.code?t.$message.success("删除成功"):t.$message.error(e.msg),t.getDataList()}))})).catch((function(e){t.$message("已取消删除")}))}}},d=p,c=(s("5abb"),s("2877")),u=Object(c["a"])(d,a,r,!1,null,"f3968a0e",null);t["default"]=u.exports}}]);