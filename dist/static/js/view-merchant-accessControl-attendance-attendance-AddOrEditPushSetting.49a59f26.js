(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-accessControl-attendance-attendance-AddOrEditPushSetting"],{"01564":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"AddOrEditPushSetting container-wrapper"},[e("div",{staticClass:"table-wrapper"},[t._m(0),e("div",[e("el-form",{ref:"settingFormRef",attrs:{model:t.settingForm,rules:t.settingFormRules,"label-width":"120px"}},[e("el-form-item",{attrs:{label:"考勤推送时间"}},[e("el-radio-group",{model:{value:t.settingForm.pushTime,callback:function(e){t.$set(t.settingForm,"pushTime",e)},expression:"settingForm.pushTime"}},[e("el-radio",{staticClass:"ps-radio",attrs:{label:"day"}},[t._v("按天")]),e("el-radio",{staticClass:"ps-radio",attrs:{label:"week"}},[t._v("按周")]),e("el-radio",{staticClass:"ps-radio",attrs:{label:"month"}},[t._v("按月")])],1),e("div",{staticClass:"tips"},[e("p",[t._v("1.按天将在次日收到发送的考勤情况统计日报；")]),e("p",[t._v("2.按周将在每周一收到发送的考勤情况统计周报；")]),e("p",[t._v("3.按月将在次月第一天收到发送的考勤情况统计月报")])])],1),e("el-form-item",{attrs:{label:"考勤推送对象",prop:"pushPerson"}},[e("el-select",{staticClass:"ps-select w-180",attrs:{multiple:!0,placeholder:"请下拉选择","popper-class":"ps-popper-select"},model:{value:t.settingForm.pushPerson,callback:function(e){t.$set(t.settingForm,"pushPerson",e)},expression:"settingForm.pushPerson"}},t._l(t.administratorsList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),e("el-form-item",[e("el-button",{staticClass:"ps-origin-btn w-150",attrs:{size:"small",type:"primary"},on:{click:t.saveSetting}},[t._v("保存")])],1)],1)],1)])])},o=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("基础设置")])])}];function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function a(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */a=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},s="function"==typeof Symbol?Symbol:{},c=s.iterator||"@@iterator",u=s.asyncIterator||"@@asyncIterator",l=s.toStringTag||"@@toStringTag";function h(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{h({},"")}catch(t){h=function(t,e,r){return t[e]=r}}function p(t,e,r,n){var i=e&&e.prototype instanceof w?e:w,a=Object.create(i.prototype),s=new j(n||[]);return o(a,"_invoke",{value:O(t,r,s)}),a}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=p;var d="suspendedStart",m="suspendedYield",g="executing",v="completed",y={};function w(){}function b(){}function L(){}var _={};h(_,c,(function(){return this}));var x=Object.getPrototypeOf,E=x&&x(x(T([])));E&&E!==r&&n.call(E,c)&&(_=E);var P=L.prototype=w.prototype=Object.create(_);function k(t){["next","throw","return"].forEach((function(e){h(t,e,(function(t){return this._invoke(e,t)}))}))}function F(t,e){function r(o,a,s,c){var u=f(t[o],t,a);if("throw"!==u.type){var l=u.arg,h=l.value;return h&&"object"==i(h)&&n.call(h,"__await")?e.resolve(h.__await).then((function(t){r("next",t,s,c)}),(function(t){r("throw",t,s,c)})):e.resolve(h).then((function(t){l.value=t,s(l)}),(function(t){return r("throw",t,s,c)}))}c(u.arg)}var a;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return a=a?a.then(o,o):o()}})}function O(e,r,n){var o=d;return function(i,a){if(o===g)throw Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var s=n.delegate;if(s){var c=S(s,n);if(c){if(c===y)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===d)throw o=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=g;var u=f(e,r,n);if("normal"===u.type){if(o=n.done?v:m,u.arg===y)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(o=v,n.method="throw",n.arg=u.arg)}}}function S(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,S(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,y;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function A(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function $(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function j(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(A,this),this.reset(!0)}function T(e){if(e||""===e){var r=e[c];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(i(e)+" is not iterable")}return b.prototype=L,o(P,"constructor",{value:L,configurable:!0}),o(L,"constructor",{value:b,configurable:!0}),b.displayName=h(L,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,L):(t.__proto__=L,h(t,l,"GeneratorFunction")),t.prototype=Object.create(P),t},e.awrap=function(t){return{__await:t}},k(F.prototype),h(F.prototype,u,(function(){return this})),e.AsyncIterator=F,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new F(p(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},k(P),h(P,l,"Generator"),h(P,c,(function(){return this})),h(P,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=T,j.prototype={constructor:j,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach($),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return s.type="throw",s.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),u=n.call(a,"finallyLoc");if(c&&u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),$(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;$(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:T(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),y}},e}function s(t,e,r,n,o,i,a){try{var s=t[i](a),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,o)}function c(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){s(i,n,o,a,c,"next",t)}function c(t){s(i,n,o,a,c,"throw",t)}a(void 0)}))}}var u={name:"AddOrEditPushSetting",components:{},props:{},data:function(){return{isLoading:!1,type:"",settingData:{},settingForm:{pushTime:"day",pushPerson:[]},settingFormRules:{pushPerson:[{required:!0,message:"请选择考勤推送对象",trigger:"change"}]},administratorsList:[]}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.$route.query.data&&(this.settingData=JSON.parse(decodeURIComponent(this.$route.query.data)),this.settingForm.pushTime=this.settingData.punch_status,this.settingForm.pushPerson=this.settingData.attendance_admins_ids),this.$route.params.type&&(this.type=this.$route.params.type),this.getGroupAdministrators()},saveSetting:function(){var t=this;this.$refs.settingFormRef.validate((function(e){if(e){var r,n;switch(t.type){case"add":r={attendance_group_admin:t.settingForm.pushPerson,punch_status:t.settingForm.pushTime},n=t.$apis.apiBackgroundAttendanceAttendancePushSettingsAddPost(r);break;case"edit":r={id:Number(t.settingData.id),attendance_group_admin:t.settingForm.pushPerson,punch_status:t.settingForm.pushTime},n=t.$apis.apiBackgroundAttendanceAttendancePushSettingsModifyPost(r);break}t.confirmOperation(n)}}))},confirmOperation:function(t){var e=this;return c(a().mark((function r(){var n;return a().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!e.isLoading){r.next=2;break}return r.abrupt("return");case 2:return e.isLoading=!0,r.next=5,t;case 5:n=r.sent,e.isLoading=!1,0===n.code?(e.$message.success("成功"),e.$closeCurrentTab(e.$route.path)):e.$message.error(n.msg);case 8:case"end":return r.stop()}}),r)})))()},getGroupAdministrators:function(){var t=this;return c(a().mark((function e(){var r;return a().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.$apis.apiBackgroundAttendanceAttendanceGroupAdminListPost({page:1,page_size:99999});case 3:r=e.sent,t.isLoading=!1,0===r.code?t.administratorsList=r.data.results:t.$message.error(r.msg);case 6:case"end":return e.stop()}}),e)})))()}}},l=u,h=(r("4f81"),r("2877")),p=Object(h["a"])(l,n,o,!1,null,"00d8c384",null);e["default"]=p.exports},"4f81":function(t,e,r){"use strict";r("880c")},"880c":function(t,e,r){}}]);