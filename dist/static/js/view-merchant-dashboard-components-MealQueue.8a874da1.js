(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-dashboard-components-MealQueue"],{ab15:function(t,a,s){},d2a9:function(t,a,s){"use strict";s.r(a);var i=function(){var t=this,a=t._self._c;return a("div",{staticClass:"MealQueue"},[a("div",{staticClass:"header"},[a("div",{staticClass:"time"},[t._v("2022-08-30 08:36:50")]),a("div",{staticClass:"title"},[t._v("叫号屏")]),a("div",{staticClass:"org-title"},[t._v(t._s(t.templateInfo.orgName?t.templateInfo.orgName:"请选择消费点")),a("span",{staticClass:"btn",on:{click:function(a){return t.openDialog("stock")}}},[t._v("编辑")])])]),a("div",{staticClass:"queue"},[t._m(0),a("div",{staticClass:"queue-list"},t._l(8,(function(s,i){return a("div",{key:i,staticClass:"queue-list-item"},[t._v("000"+t._s(s))])})),0)]),a("div",{staticClass:"queue"},[t._m(1),a("div",{staticClass:"queue-list"},t._l(8,(function(s,i){return a("div",{key:i,staticClass:"queue-list-item"},[t._v("000"+t._s(s))])})),0)]),a("el-dialog",{attrs:{title:"选择组织",visible:t.showDialog,width:"500px","custom-class":"ps-dialog"},on:{"update:visible":function(a){t.showDialog=a}}},[a("el-form",{ref:"dialogFormRef",staticClass:"dialog-form",attrs:{model:t.dialogForm,"label-width":"160px"}},[a("el-form-item",{attrs:{label:"显示组织："}},[a("organization-select",{staticClass:"search-item-w ps-input w-180",attrs:{placeholder:"请选择所属组织",isLazy:!1,multiple:!1,"check-strictly":!0,"append-to-body":!0},on:{change:t.changeHandle},model:{value:t.dialogForm.orgId,callback:function(a){t.$set(t.dialogForm,"orgId",a)},expression:"dialogForm.orgId"}})],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{staticClass:"ps-cancel-btn",on:{click:function(a){t.showDialog=!1}}},[t._v("取 消")]),a("el-button",{staticClass:"ps-btn",attrs:{type:"primary"},on:{click:t.confirmDialog}},[t._v("确 定")])],1)],1)],1)},e=[function(){var t=this,a=t._self._c;return a("div",{staticClass:"queue-title"},[a("img",{staticClass:"icon-take",attrs:{src:s("0d54"),alt:""}}),a("div",{staticClass:"text"},[t._v("可取餐")])])},function(){var t=this,a=t._self._c;return a("div",{staticClass:"queue-title"},[a("img",{staticClass:"icon-making",attrs:{src:s("57e2"),alt:""}}),a("div",{staticClass:"text"},[t._v("制作中")])])}],o=s("cbfb"),l={name:"MealQueue",components:{OrganizationSelect:o["a"]},props:{type:String,templateInfo:Object},data:function(){return{dialogType:"",showDialog:!1,dialogForm:{orgId:"",orgName:""}}},methods:{changeHandle:function(t){this.dialogForm.orgName=t.name},openDialog:function(t){this.dialogType=t,this.showDialog=!0,this.dialogForm.orgId=this.templateInfo.orgId},confirmDialog:function(){this.showDialog=!1,this.templateInfo.orgId=this.dialogForm.orgId,this.templateInfo.orgName=this.dialogForm.orgName,this.$emit("comfirm",this.templateInfo)}}},n=l,c=(s("e58f"),s("2877")),r=Object(c["a"])(n,i,e,!1,null,null,null);a["default"]=r.exports},e58f:function(t,a,s){"use strict";s("ab15")}}]);