(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-member-center-components-MemberListDialog"],{"5c4a":function(t,e,r){"use strict";r("8f1a")},"8f1a":function(t,e,r){},d337:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("dialog-message",{attrs:{show:t.visible,title:t.title,loading:t.isLoading,customClass:"ps-dialog",width:t.width},on:{"update:show":function(e){t.visible=e},"update:loading":function(e){t.isLoading=e},close:t.handleClose}},[e("el-form",{ref:"memberFormRef",staticClass:"member-form",attrs:{model:t.dialogForm,"status-icon":"",rules:t.dialogFormRules,"label-width":"120px"},nativeOn:{submit:function(t){t.preventDefault()}}},["label"===t.type?e("div",[e("el-form-item",{attrs:{label:"自动标签："}},[t._v(" "+t._s(t.autoLabelNameList.join("，"))+" ")]),e("el-form-item",{attrs:{label:"手动标签："}},[e("div",{staticClass:"label-list"},t._l(t.labelNameList,(function(r,n){return e("div",{key:r,staticClass:"label-list-item"},[e("span",{staticClass:"m-r-5"},[t._v(t._s(r))]),e("i",{staticClass:"el-icon-close del-icon",on:{click:function(e){return t.delLabel(n)}}})])})),0)]),e("el-form-item",{attrs:{label:"新增手动标签："}},[e("el-select",{staticClass:"ps-input",attrs:{placeholder:"请选择手动标签",multiple:"","collapse-tags":""},on:{change:t.changeSelectLabel},model:{value:t.dialogForm.selectLabelList,callback:function(e){t.$set(t.dialogForm,"selectLabelList",e)},expression:"dialogForm.selectLabelList"}},t._l(t.labelList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)],1):t._e(),"integral"===t.type?e("div",[e("el-form-item",{attrs:{label:"当前积分："}},[t._v(t._s(t.selectInfo.integral))]),e("el-form-item",{attrs:{label:"修改后积分：",prop:"score"}},[e("el-input",{staticClass:"ps-input w-180",attrs:{maxlength:"7"},model:{value:t.dialogForm.score,callback:function(e){t.$set(t.dialogForm,"score",e)},expression:"dialogForm.score"}})],1)],1):t._e(),"growthScore"===t.type?e("div",[e("el-form-item",{attrs:{label:"当前成长分："}},[t._v(t._s(t.selectInfo.growth_points))]),e("el-form-item",{attrs:{label:"添加成长分：",prop:"score"}},[e("el-input",{staticClass:"ps-input w-180",attrs:{maxlength:"7"},model:{value:t.dialogForm.score,callback:function(e){t.$set(t.dialogForm,"score",e)},expression:"dialogForm.score"}})],1),e("el-form-item",{attrs:{label:"修改后成长分："}},[t._v(t._s(Number(t.selectInfo.growth_points)+Number(t.dialogForm.score)))])],1):t._e()]),e("template",{slot:"tool"},[e("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:t.isLoading},on:{click:t.clickCancleHandle}},[t._v(" 取消 ")]),e("el-button",{staticClass:"ps-btn",attrs:{disabled:t.isLoading,type:"primary"},on:{click:t.clickConfirmHandle}},[t._v(" 确定 ")])],1)])],2)},o=[];function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function a(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */a=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},s="function"==typeof Symbol?Symbol:{},l=s.iterator||"@@iterator",c=s.asyncIterator||"@@asyncIterator",u=s.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function h(t,e,r,n){var i=e&&e.prototype instanceof y?e:y,a=Object.create(i.prototype),s=new $(n||[]);return o(a,"_invoke",{value:O(t,r,s)}),a}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var p="suspendedStart",m="suspendedYield",b="executing",g="completed",v={};function y(){}function L(){}function w(){}var _={};f(_,l,(function(){return this}));var x=Object.getPrototypeOf,F=x&&x(x(I([])));F&&F!==r&&n.call(F,l)&&(_=F);var k=w.prototype=y.prototype=Object.create(_);function E(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function N(t,e){function r(o,a,s,l){var c=d(t[o],t,a);if("throw"!==c.type){var u=c.arg,f=u.value;return f&&"object"==i(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,s,l)}),(function(t){r("throw",t,s,l)})):e.resolve(f).then((function(t){u.value=t,s(u)}),(function(t){return r("throw",t,s,l)}))}l(c.arg)}var a;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return a=a?a.then(o,o):o()}})}function O(e,r,n){var o=p;return function(i,a){if(o===b)throw Error("Generator is already running");if(o===g){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var s=n.delegate;if(s){var l=S(s,n);if(l){if(l===v)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=b;var c=d(e,r,n);if("normal"===c.type){if(o=n.done?g:m,c.arg===v)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o=g,n.method="throw",n.arg=c.arg)}}}function S(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,S(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=d(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function C(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function $(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(C,this),this.reset(!0)}function I(e){if(e||""===e){var r=e[l];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(i(e)+" is not iterable")}return L.prototype=w,o(k,"constructor",{value:w,configurable:!0}),o(w,"constructor",{value:L,configurable:!0}),L.displayName=f(w,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===L||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,f(t,u,"GeneratorFunction")),t.prototype=Object.create(k),t},e.awrap=function(t){return{__await:t}},E(N.prototype),f(N.prototype,c,(function(){return this})),e.AsyncIterator=N,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new N(h(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},E(k),f(k,u,"Generator"),f(k,l,(function(){return this})),f(k,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=I,$.prototype={constructor:$,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return s.type="throw",s.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=n.call(a,"catchLoc"),c=n.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),j(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;j(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:I(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function s(t,e,r,n,o,i,a){try{var s=t[i](a),l=s.value}catch(t){return void r(t)}s.done?e(l):Promise.resolve(l).then(n,o)}function l(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){s(i,n,o,a,l,"next",t)}function l(t){s(i,n,o,a,l,"throw",t)}a(void 0)}))}}var c={name:"ForLeaveRuleDialog",props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:""},width:{type:String,default:"450px"},selectInfo:{type:Object,default:function(){return{}}},isshow:Boolean,confirm:Function},data:function(){var t=function(t,e,r){if(""===e)return r(new Error("不能为空"));var n=/^\d+$/;n.test(e)?r():r(new Error("请输入正整数"))};return{isLoading:!1,dialogForm:{selectLabelList:[],score:""},dialogFormRules:{score:[{required:!0,validator:t,trigger:"blur"}]},autoLabelNameList:[],autoLabelIdList:[],labelNameList:[],labelList:[]}},computed:{visible:{get:function(){return this.isshow},set:function(t){this.$emit("update:isshow",t)}}},watch:{visible:function(){var t=this;this.visible?(this.labelNameList=[],this.autoLabelNameList=[],this.autoLabelIdList=[],this.dialogForm.selectLabelList=[],this.selectInfo.member_labels_list.map((function(e){"auto"===e.type?(t.autoLabelNameList.push(e.name),t.autoLabelIdList.push(e.id)):t.dialogForm.selectLabelList.push(e.id)})),this.getMemberLabel()):this.$refs.memberFormRef.resetFields()}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){},clickConfirmHandle:function(){var t=this;this.$refs.memberFormRef.validate((function(e){if(e){var r,n={};switch(t.type){case"growthScore":n={user_id:t.selectInfo.id,add_growth_value:t.dialogForm.score,obtain_type:"background_add"},r=t.$apis.apiBackgroundMemberMemberGradeGrowthAddPost(n);break;case"label":n={id:t.selectInfo.id,member_labels:t.autoLabelIdList.concat(t.dialogForm.selectLabelList)},r=t.$apis.apiBackgroundMemberMemberUserModifyPost(n);break}t.confirmOperation(r)}}))},confirmOperation:function(t){var e=this;return l(a().mark((function r(){var n;return a().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!e.isLoading){r.next=2;break}return r.abrupt("return");case 2:return e.isLoading=!0,r.next=5,t;case 5:n=r.sent,e.isLoading=!1,0===n.code?(e.$message.success("成功"),e.$emit("confirm","search")):e.$message.error(n.msg);case 8:case"end":return r.stop()}}),r)})))()},clickCancleHandle:function(){this.visible=!1},handleClose:function(t){this.isLoading=!1,this.visible=!1,this.$refs.memberFormRef.resetFields()},delLabel:function(t){this.dialogForm.selectLabelList.splice(t,1),this.labelNameList.splice(t,1)},getMemberLabel:function(){var t=this;return l(a().mark((function e(){var r;return a().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.apiBackgroundMemberMemberLabelListPost({page:1,page_size:99999,type:"manual"});case 2:r=e.sent,0===r.code?(t.labelList=r.data.results,t.labelList.map((function(e){-1!==t.dialogForm.selectLabelList.indexOf(e.id)&&t.labelNameList.push(e.name)}))):t.$message.error(r.msg);case 4:case"end":return e.stop()}}),e)})))()},changeSelectLabel:function(){var t=this;this.labelNameList=[],this.labelList.map((function(e){-1!==t.dialogForm.selectLabelList.indexOf(e.id)&&t.labelNameList.push(e.name)}))}}},u=c,f=(r("5c4a"),r("2877")),h=Object(f["a"])(u,n,o,!1,null,"a2e287b8",null);e["default"]=h.exports}}]);