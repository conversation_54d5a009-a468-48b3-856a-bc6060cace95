(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-meal-apply-constants","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-meal-report-MealPackageRule"],{"794d":function(e,l,a){"use strict";a.r(l),a.d(l,"PAYMENTSTATE",(function(){return b})),a.d(l,"PROCESSIMG_SEARCH",(function(){return _})),a.d(l,"PROCESSED_SEARCH",(function(){return c})),a.d(l,"REVOKE_SEARCH",(function(){return m})),a.d(l,"ACCOUNTIMG_SEARCH",(function(){return d})),a.d(l,"PAYABLE_SEARCH",(function(){return s})),a.d(l,"ORDER_TOTAL_SEARCH",(function(){return k})),a.d(l,"ORDER_PERPARE_SEARCH",(function(){return v})),a.d(l,"PROCESSIMG_TABLE",(function(){return g})),a.d(l,"PROCESSED_TABLE",(function(){return f})),a.d(l,"REVOKE_TABLE",(function(){return h})),a.d(l,"ACCOUNTIMG_TABLE",(function(){return E})),a.d(l,"PAYABLE_TABLE",(function(){return R})),a.d(l,"ORDER_TOTAL_TABLE",(function(){return O})),a.d(l,"ORDER_PERPARE_TABLE",(function(){return S})),a.d(l,"ACCOUNTIMG_COLLECT",(function(){return A})),a.d(l,"PAYABLE_COLLECT",(function(){return L})),a.d(l,"getRequestParams",(function(){return T}));var t=a("ed08"),n=a("c9d9");function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function o(e,l){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);l&&(t=t.filter((function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable}))),a.push.apply(a,t)}return a}function r(e){for(var l=1;l<arguments.length;l++){var a=null!=arguments[l]?arguments[l]:{};l%2?o(Object(a),!0).forEach((function(l){u(e,l,a[l])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):o(Object(a)).forEach((function(l){Object.defineProperty(e,l,Object.getOwnPropertyDescriptor(a,l))}))}return e}function u(e,l,a){return(l=y(l))in e?Object.defineProperty(e,l,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[l]=a,e}function y(e){var l=p(e,"string");return"symbol"==i(l)?l:l+""}function p(e,l){if("object"!=i(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var t=a.call(e,l||"default");if("object"!=i(t))return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===l?String:Number)(e)}var b=[{label:"全部",value:""},{label:"待支付",value:"ORDER_PAYING"},{label:"支付成功",value:"ORDER_SUCCESS"},{label:"支付失败",value:"ORDER_FAILED"},{label:"交易冲正中",value:"ORDER_REVERSALING"},{label:"交易冲正",value:"ORDER_REVERSAL"},{label:"退款中",value:"ORDER_REFUNDING"},{label:"已退款",value:"ORDER_REFUND_SUCCESS"},{label:"关闭(用户未支付)",value:"ORDER_CLOSE"},{label:"过期",value:"ORDER_TIME_OUT"},{label:"未知",value:"ORDER_UNKNOWN"}],_={create_time:{timeRange:!0,type:"daterange",label:"申请时间",clearable:!1,value:Object(t["t"])(-7,{format:"{y}-{m}-{d}"})},approve_no:{type:"input",label:"单号",value:"",placeholder:"请输入单号"},name:{type:"input",label:"申请人",value:"",placeholder:"请输入申请人"},payer_group_ids:{type:"groupSelect",label:"所属分组",value:[],multiple:!0,collapseTags:!0,placeholder:"请选择所属分组"},phone:{type:"input",label:"手机号码",value:"",placeholder:"请输入手机号码"},pay_method:{type:"select",label:"访客餐支付方式",labelWidth:"110px",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"访客记账",value:"Accounting"},{label:"即付",value:"PayAtSight"}]},user_type:{type:"select",label:"适用用户",value:"",placeholder:"请选择适用用户",dataList:[{label:"全部",value:""},{label:"内部用户",value:"insider"},{label:"外部用户",value:"outsiders"}]},apply_type_list:{type:"select",label:"申请类型",value:[],multiple:!0,collapseTags:!0,placeholder:"请选择",dataList:[]}},c={deal_time:{timeRange:!0,type:"daterange",label:"审批时间",clearable:!1,value:Object(t["t"])(-7,{format:"{y}-{m}-{d}"})},create_time:{timeRange:!0,type:"daterange",label:"申请时间",clearable:!1,value:Object(t["t"])(-7,{format:"{y}-{m}-{d}"})},approve_no:{type:"input",label:"单号",value:"",placeholder:"请输入单号"},name:{type:"input",label:"申请人",value:"",placeholder:"请输入申请人"},payer_group_ids:{type:"groupSelect",label:"所属分组",value:[],multiple:!0,collapseTags:!0,placeholder:"请选择所属分组"},phone:{type:"input",label:"手机号码",value:"",placeholder:"请输入手机号码"},pay_method:{type:"select",label:"访客餐支付方式",labelWidth:"110px",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"访客记账",value:"Accounting"},{label:"即付",value:"PayAtSight"}]},user_type:{type:"select",label:"适用用户",value:"",placeholder:"请选择适用用户",dataList:[{label:"全部",value:""},{label:"内部用户",value:"insider"},{label:"外部用户",value:"outsiders"}]},apply_type_list:{type:"select",label:"申请类型",value:[],multiple:!0,collapseTags:!0,placeholder:"请选择",dataList:[]},approve_status_list:{type:"select",label:"审批状态",value:[],multiple:!0,collapseTags:!0,placeholder:"请选择审批状态",dataList:[{label:"审批中",value:"PROCESSING"},{label:"审批通过",value:"AGREE"},{label:"拒绝申请",value:"REJECT"}]}},m={deal_time:{timeRange:!0,type:"daterange",label:"撤销时间",clearable:!1,value:Object(t["t"])(-7,{format:"{y}-{m}-{d}"})},create_time:{timeRange:!0,type:"daterange",label:"申请时间",clearable:!1,value:Object(t["t"])(-7,{format:"{y}-{m}-{d}"})},approve_no:{type:"input",label:"单号",value:"",placeholder:"请输入单号"},name:{type:"input",label:"申请人",value:"",placeholder:"请输入申请人"},payer_group_ids:{type:"groupSelect",label:"所属分组",value:[],multiple:!0,collapseTags:!0,placeholder:"请选择所属分组"},phone:{type:"input",label:"手机号码",value:"",placeholder:"请输入手机号码"}},d={date_type:{type:"select",value:"create_time",label:"时间",dataList:[{label:"创建时间",value:"create_time"},{label:"支付时间",value:"pay_time"},{label:"扣款时间",value:"deduction_time"},{label:"就餐时间",value:"date"}]},create_time:{timeRange:!0,type:"daterange",clearable:!1,value:Object(t["t"])(-7,{format:"{y}-{m}-{d}"})},name:{type:"input",label:"用户姓名",value:"",placeholder:"请输入申请人"},phone:{type:"input",label:"手机号码",value:"",placeholder:"请输入手机号码"},payer_group_ids:{type:"groupSelect",label:"分组",value:[],multiple:!0,collapseTags:!0,placeholder:"请选择分组"},payer_department_group_ids:{type:"organizationDepartmentSelect",value:[],label:"部门"},organization_ids:{type:"organizationSelect",value:[],label:"消费点",checkStrictly:!0,isLazy:!1,multiple:!0},source_organization_ids:{type:"organizationSelect",value:[],label:"所属组织",checkStrictly:!0,isLazy:!1,multiple:!0},trade_no:{type:"input",label:"订单编号",value:"",placeholder:"请输入手机号码"},consume_type:{type:"select",label:"记账类型",value:"",placeholder:"请选择记账类型",dataList:[{label:"全部",value:""},{label:"固定金额",value:"GD"},{label:"按实际记账",value:"JZ"}]},settle_status:{type:"select",label:"对账状态",value:"",placeholder:"请选择对账状态",dataList:[{label:"全部",value:""},{label:"已对账",value:1},{label:"未对账",value:0}]},meal_type_list:{type:"select",label:"餐段",value:[],multiple:!0,placeholder:"请选择",collapseTags:!0,dataList:n["a"]}},s={date_type:{type:"select",value:"create_time",label:"时间",dataList:[{label:"创建时间",value:"create_time"},{label:"支付时间",value:"pay_time"},{label:"扣款时间",value:"deduction_time"},{label:"就餐时间",value:"date"}]},create_time:{timeRange:!0,type:"daterange",clearable:!1,value:Object(t["t"])(-7,{format:"{y}-{m}-{d}"})},order_status:{type:"select",label:"支付状态",value:"",placeholder:"请选择",dataList:b},payer_group_ids:{type:"groupSelect",label:"分组",value:[],multiple:!0,collapseTags:!0,placeholder:"请选择分组"},name:{type:"input",label:"用户姓名",value:"",placeholder:"请输入申请人"},phone:{type:"input",label:"手机号码",value:"",placeholder:"请输入手机号码"},payer_department_group_ids:{type:"organizationDepartmentSelect",value:[],label:"部门"},organization_ids:{type:"organizationSelect",value:[],label:"消费点",checkStrictly:!0,isLazy:!1,multiple:!0},source_organization_ids:{type:"organizationSelect",value:[],label:"所属组织",checkStrictly:!0,isLazy:!1,multiple:!0},trade_no:{type:"input",label:"订单编号",value:"",placeholder:"请输入手机号码"},meal_type_list:{type:"select",label:"餐段",value:[],multiple:!0,placeholder:"请选择",collapseTags:!0,dataList:n["a"]},is_use:{type:"select",label:"状态",value:"",placeholder:"请选择对账状态",dataList:[{label:"全部",value:""},{label:"已就餐",value:1},{label:"未使用",value:0},{label:"已过期",value:2}]}},k={create_time:{timeRange:!0,type:"daterange",label:"就餐日期",clearable:!1,value:Object(t["t"])(-7,{format:"{y}-{m}-{d}"})},organization_ids:{type:"organizationSelect",value:[],label:"消费点",checkStrictly:!0,isLazy:!1,multiple:!0},payer_group_ids:{type:"groupSelect",label:"分组",value:[],multiple:!0,collapseTags:!0,placeholder:"请选择分组"},is_visitor:{type:"checkbox",label:"",checkboxLabel:"只看游客",value:!1}},v={create_time:{timeRange:!0,type:"daterange",label:"就餐日期",clearable:!1,value:Object(t["t"])(-7,{format:"{y}-{m}-{d}"})},payer_group_ids:{type:"groupSelect",label:"分组",value:[],multiple:!0,collapseTags:!0,placeholder:"请选择分组"},is_visitor:{type:"checkbox",label:"",checkboxLabel:"只看游客",value:!1}},g=[{label:"单号",key:"approve_no"},{label:"申请时间",key:"create_time"},{label:"所属分组",key:"payer_group_name"},{label:"消费点",key:"org_name",type:"slot",slotName:"tooltip"},{label:"申请人",key:"name"},{label:"手机号码",key:"phone"},{label:"餐段",key:"meal_type_list",type:"slot",slotName:"tooltip"},{label:"就餐日期",key:"date_range_str"},{label:"就餐人数",key:"meal_num"},{label:"访客餐支付方式",key:"pay_method_alias"},{label:"记账方式",key:"consume_type_alias"},{label:"次数限制",key:"num_limit_alias"},{label:"申请类型",key:"apply_type"},{label:"备注",key:"remark",type:"slot",slotName:"tooltip"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"120"}],f=[{label:"单号",key:"approve_no"},{label:"审批时间",key:"approve_time"},{label:"审批状态",key:"approve_status"},{label:"所属分组",key:"payer_group_name"},{label:"消费点",key:"org_name",type:"slot",slotName:"tooltip"},{label:"申请人",key:"name"},{label:"手机号码",key:"phone"},{label:"餐段",key:"meal_type_list",type:"slot",slotName:"tooltip"},{label:"就餐日期",key:"date_range_str"},{label:"访客餐支付方式",key:"pay_method_alias"},{label:"记账方式",key:"consume_type_alias"},{label:"次数限制",key:"num_limit_alias"},{label:"就餐人数",key:"meal_num"},{label:"申请类型",key:"apply_type"},{label:"申请时间",key:"create_time"},{label:"备注",key:"remark",type:"slot",slotName:"tooltip"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"80"}],h=[{label:"单号",key:"approve_no"},{label:"撤销时间",key:"revoke_time"},{label:"所属分组",key:"payer_group_name"},{label:"消费点",key:"org_name",type:"slot",slotName:"tooltip"},{label:"申请人",key:"name"},{label:"手机号码",key:"phone"},{label:"餐段",key:"meal_type_list",type:"slot",slotName:"tooltip"},{label:"就餐日期",key:"date_range_str"},{label:"就餐人数",key:"meal_num"},{label:"访客餐支付方式",key:"pay_method_alias"},{label:"记账方式",key:"consume_type_alias"},{label:"次数限制",key:"num_limit_alias"},{label:"申请类型",key:"apply_type"},{label:"申请时间",key:"create_time"},{label:"备注",key:"remark",type:"slot",slotName:"tooltip"}],E=[{label:"审批单号",key:"approve_no"},{label:"订单号",key:"trade_no"},{label:"创建时间",key:"create_time"},{label:"支付时间",key:"pay_time"},{label:"扣款时间",key:"deduction_time"},{label:"就餐时间",key:"date"},{label:"订单金额",key:"origin_fee",type:"money"},{label:"优惠金额",key:"discount_fee",type:"money"},{label:"实付金额",key:"pay_fee",type:"money"},{label:"餐段",key:"meal_type_alias"},{label:"记账类型",key:"consume_type_alias"},{label:"所属组织",key:"source_organization_name"},{label:"用户姓名",key:"name"},{label:"手机号",key:"phone"},{label:"分组",key:"payer_group_name"},{label:"部门",key:"payer_department_group_name"},{label:"支付状态",key:"order_status_alias"},{label:"对账状态",key:"settle_status_alias"},{label:"消费点",key:"stall"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"220"}],R=[{label:"审批单号",key:"approve_no"},{label:"订单号",key:"trade_no"},{label:"创建时间",key:"create_time"},{label:"支付时间",key:"pay_time"},{label:"扣款时间",key:"deduction_time"},{label:"就餐时间",key:"date"},{label:"订单金额",key:"origin_fee",type:"money"},{label:"优惠金额",key:"discount_fee",type:"money"},{label:"实付金额",key:"pay_fee",type:"money"},{label:"餐段",key:"meal_type_alias"},{label:"所属组织",key:"source_organization_name"},{label:"用户姓名",key:"name"},{label:"手机号",key:"phone"},{label:"分组",key:"payer_group_name"},{label:"部门",key:"payer_department_group_name"},{label:"访客餐支付方式",key:"sub_payway_alias"},{label:"支付类型",key:"payway_alias"},{label:"支付状态",key:"order_status_alias"},{label:"状态",key:"is_use_alias"},{label:"消费点",key:"stall"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"220"}],O=[{label:"分组/游客",key:"group_name"},{label:"访客就餐次数",key:"meal_count"},{label:"即付金额",key:"pay_at_sight_fee",type:"money"},{label:"记账金额",key:"accounting_fee",type:"money"},{label:"对账次数",key:"settle_count"},{label:"已对账金额",key:"settle_fee",type:"money"},{label:"未对账金额",key:"settle_fail_fee",type:"money"}],S=[{label:"消费点",key:"organization_name"},{label:"早餐",key:"breakfast",children:[{label:"记账/单",key:"breakfast_accounting_count"},{label:"即付/单",key:"breakfast_pay_at_sight_count"}]},{label:"午餐",key:"lunch",children:[{label:"记账/单",key:"lunch_accounting_count"},{label:"即付/单",key:"lunch_pay_at_sight_count"}]},{label:"下午茶",key:"afternoon",children:[{label:"记账/单",key:"afternoon_accounting_count"},{label:"即付/单",key:"afternoon_pay_at_sight_count"}]},{label:"晚餐",key:"dinner",children:[{label:"记账/单",key:"dinner_accounting_count"},{label:"即付/单",key:"dinner_pay_at_sight_count"}]},{label:"夜宵",key:"supper",children:[{label:"记账/单",key:"supper_accounting_count"},{label:"即付/单",key:"supper_pay_at_sight_count"}]},{label:"凌晨餐",key:"morning",children:[{label:"记账/单",key:"morning_accounting_count"},{label:"即付/单",key:"morning_pay_at_sight_count"}]}],A=[{key:"total_count",value:"",label:"合计笔数",class:"origin"},{key:"total_origin_fee",value:"",label:"合计订单金额",class:"origin",type:"money"},{key:"total_pay_fee",value:"",label:"合计实收金额",class:"origin",type:"money"},{key:"settle_origin_fee",value:"",label:"已对账金额（订单金额）",class:"origin",type:"money"},{key:"settle_pay_fee",value:"",label:"已对账金额（实收）",class:"origin",type:"money"}],L=[{key:"total_count",value:"",label:"合计笔数",class:"origin"},{key:"total_origin_fee",value:"",label:"合计订单金额",class:"origin",type:"money"},{key:"total_pay_fee",value:"",label:"合计实收金额",class:"origin",type:"money"}],T=function(e,l,a){var t={};Object.keys(e).forEach((function(l){var a,n,i;"create_time"===l&&(null===(a=e[l].value)||void 0===a?void 0:a.length)>0?(t.start_date=e[l].value[0],t.end_date=e[l].value[1]):"deal_time"===l&&(null===(n=e[l].value)||void 0===n?void 0:n.length)>0?(t.deal_start_date=e[l].value[0],t.deal_end_date=e[l].value[1]):(""!==e[l].value&&e[l].value&&null!==(i=e[l].value)&&void 0!==i&&i.length||"number"===typeof e[l].value||"boolean"===typeof e[l].value)&&(t[l]=e[l].value)}));var n=r({page:l,page_size:a},t);return n}},c9d9:function(e,l,a){"use strict";a.d(l,"a",(function(){return i})),a.d(l,"d",(function(){return o})),a.d(l,"b",(function(){return r})),a.d(l,"c",(function(){return u})),a.d(l,"e",(function(){return y})),a.d(l,"f",(function(){return p})),a.d(l,"g",(function(){return b}));var t=a("5a0c"),n=a("da92"),i=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],o=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],r={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},u=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],y=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],p=(t().subtract(7,"day").format("YYYY-MM-DD"),t().format("YYYY-MM-DD"),function(e){return e?"number"===typeof e?n["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:n["a"].divide(e,100).toFixed(2):"0.00"}),b=function(e){return n["a"].times(e,100)}}}]);