(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-health-assessment-AddEditQuestionnaire"],{"14df":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"super-add-ingredients container-wrapper"},[e("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formIngredients",attrs:{rules:t.formRuls,model:t.formData,size:"small"}},[e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("基本信息")])]),e("div",{staticStyle:{"max-width":"300px",padding:"0 20px"}},[e("el-form-item",{staticClass:"block-label",attrs:{label:"测评名称",prop:"name"}},[e("el-input",{staticClass:"p-r-48 p-b-10",attrs:{placeholder:"请输入测评名称",type:"textarea",autosize:{minRows:0,maxRows:2},maxlength:"25","show-word-limit":""},model:{value:t.formData.name,callback:function(e){t.$set(t.formData,"name",e)},expression:"formData.name"}})],1)],1),e("div",{staticStyle:{"max-width":"350px",padding:"0 20px"}},[e("el-form-item",{attrs:{label:"测评介绍",prop:"content"}},[e("el-input",{staticClass:"p-r-48 p-b-10",attrs:{type:"textarea",placeholder:"请输入测评介绍",autosize:{minRows:4,maxRows:10},maxlength:"200","show-word-limit":""},model:{value:t.formData.content,callback:function(e){t.$set(t.formData,"content",e)},expression:"formData.content"}})],1)],1)]),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v(" 测评题编写 "),e("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(e){return t.addQuestionnaire()}}},[t._v(" 添加题目 ")])],1)]),e("div",{staticClass:"table-content"},t._l(t.formData.topic_list,(function(r,n){return e("div",{key:n,staticStyle:{"max-width":"600px",padding:"0 20px"}},[e("el-form-item",{attrs:{label:n+1+"、",prop:"topic_list."+n+".content",rules:{required:!0,message:"请输入问题",trigger:"blur"},"label-width":"30px"}},[e("el-input",{staticClass:"p-r-48 p-b-10",attrs:{type:"textarea",placeholder:"请输入问题",autosize:{minRows:4,maxRows:10},maxlength:"200","show-word-limit":""},model:{value:r.content,callback:function(e){t.$set(r,"content",e)},expression:"questionnaireItem.content"}})],1),e("div",{staticClass:"ps-flex-align-c flex-align-c p-l-20"},[e("el-radio-group",{staticClass:"ps-radio",model:{value:r.choice_type,callback:function(e){t.$set(r,"choice_type",e)},expression:"questionnaireItem.choice_type"}},[e("el-radio",{attrs:{label:"single"}},[t._v("单选")]),e("el-radio",{attrs:{label:"multiple"}},[t._v("多选")])],1),e("el-select",{staticStyle:{"margin-left":"20px"},attrs:{size:"small"},model:{value:r.is_required,callback:function(e){t.$set(r,"is_required",e)},expression:"questionnaireItem.is_required"}},[e("el-option",{attrs:{label:"必填",value:"required"}}),e("el-option",{attrs:{label:"非必填",value:"not_required"}})],1),t.formData.topic_list.length>1?e("div",{staticClass:"ps-flex-align-c flex-align-c cursor: pointer"},[0!=n?e("div",{staticClass:"p-l-20",on:{click:function(e){return t.handleMove(n,"up",t.formData.topic_list)}}},[t._v(" 上移 "),e("i",{staticClass:"el-icon-top"})]):t._e(),e("div",{staticClass:"p-l-20",on:{click:function(e){return t.handleMove(n,"down",t.formData.topic_list)}}},[t._v(" 下移 "),e("i",{staticClass:"el-icon-bottom"})]),e("i",{staticClass:"el-icon-circle-close p-l-20 font-size-16",on:{click:function(e){return t.questionnaireRemove(n)}}})]):t._e()],1),t._l(r.options_text,(function(i,o){return e("div",{key:o,staticClass:"ps-flex-align-c p-t-20"},[e("el-form-item",{attrs:{label:t.chooseNumStr(o)+"、",prop:"topic_list."+n+".options_text."+o+".options_text",rules:{required:!0,message:"请输入选项",trigger:"blur"},"label-width":"30px"}},[e("el-input",{staticClass:"p-r-10",staticStyle:{width:"300px"},attrs:{type:"textarea",autosize:{minRows:0,maxRows:2},maxlength:"32"},model:{value:i.options_text,callback:function(e){t.$set(i,"options_text",e)},expression:"optionItem.options_text"}})],1),e("el-form-item",{attrs:{"label-width":"20px",prop:"topic_list."+n+".options_text."+o+".options_number",rules:{required:!0,message:"请输入分数",trigger:"blur"}}},[e("el-input",{staticClass:"p-r-10",staticStyle:{width:"120px"},model:{value:i.options_number,callback:function(e){t.$set(i,"options_number",e)},expression:"optionItem.options_number"}}),e("span",[t._v("分")])],1),e("div",{staticClass:"p-l-20"},[r.options_text.length>1?e("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.optionRemove(n,o)}}},[t._v(" 删除 ")]):t._e()],1)],1)})),e("div",{staticClass:"p-b-20 p-l-20",staticStyle:{color:"#fda04d",cursor:"pointer",width:"110px"},on:{click:function(e){return t.addOption(n,r.options_text)}}},[e("i",{staticClass:"el-icon-plus"}),t._v(" 添加选项 ")])],2)})),0)]),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v(" 评价编写 "),e("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(e){return t.addEvaluate()}}},[t._v("添加评价")])],1)]),e("div",{staticClass:"p-l-20",staticStyle:{color:"#a5a5a5"}},[t._v(' 注：填写分数时，符号表示：“小于”=“>”、"大于"=“>”、“小于等于”=“>=”、“大于等于”=“>=”\' ')]),e("div",{staticClass:"table-content"},t._l(t.formData.assess_list,(function(r,n){return e("div",{key:n,staticStyle:{"max-width":"600px",padding:"0 20px"}},[e("div",{staticClass:"ps-flex-align-c p-t-20"},[e("el-form-item",{attrs:{label:n+1+"、","label-width":"30px"}},[e("span",{staticClass:"p-r-10"},[t._v("分数")]),e("el-select",{staticStyle:{width:"120px"},attrs:{size:"small"},model:{value:r.comparison_operator,callback:function(e){t.$set(r,"comparison_operator",e)},expression:"evaluateItem.comparison_operator"}},[e("el-option",{attrs:{label:"等于",value:"="}}),e("el-option",{attrs:{label:"大于",value:">"}}),e("el-option",{attrs:{label:"小于",value:"<"}}),e("el-option",{attrs:{label:"大于等于",value:">="}}),e("el-option",{attrs:{label:"小于等于",value:"<="}})],1)],1),e("el-form-item",{attrs:{prop:"assess_list."+n+".assess_number",rules:{required:!0,message:"请输入分数",trigger:"blur"},"label-width":"30px"}},[e("el-input",{staticStyle:{width:"120px"},attrs:{placeholder:"分数"},model:{value:r.assess_number,callback:function(e){t.$set(r,"assess_number",e)},expression:"evaluateItem.assess_number"}})],1)],1),e("el-form-item",{attrs:{"label-width":"30px",prop:"assess_list."+n+".assess_name",rules:{required:!0,message:"请输入标题",trigger:"blur"}}},[e("el-input",{staticStyle:{width:"250px"},attrs:{placeholder:"请输入标题",type:"textarea",autosize:{minRows:0,maxRows:2},maxlength:"32"},model:{value:r.assess_name,callback:function(e){t.$set(r,"assess_name",e)},expression:"evaluateItem.assess_name"}}),e("span",{staticClass:"p-l-10"},[t._v("标题")])],1),e("el-form-item",{attrs:{"label-width":"30px",prop:"assess_list."+n+".assess_content",rules:{required:!0,message:"请输入问题",trigger:"blur"}}},[e("div",{staticClass:"ps-flex-align-c flex-align-c"},[e("el-input",{staticClass:"p-r-48 p-b-10",attrs:{type:"textarea",placeholder:"请输入问题",autosize:{minRows:4,maxRows:10},maxlength:"200","show-word-limit":""},model:{value:r.assess_content,callback:function(e){t.$set(r,"assess_content",e)},expression:"evaluateItem.assess_content"}}),t.formData.assess_list.length>1?e("div",{staticClass:"p-l-20"},[e("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.evaluateRemove(n)}}},[t._v(" 删除 ")])],1):t._e()],1)])],1)})),0)]),e("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[e("el-button",{staticStyle:{width:"120px"},on:{click:t.closeHandler}},[t._v("取消")]),e("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:t.submitHandler}},[t._v(" "+t._s("add"===t.type?"添加":"编辑")+" ")])],1)])],1)},i=[],o=r("ed08");function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},l=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function p(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{p({},"")}catch(t){p=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var o=e&&e.prototype instanceof b?e:b,a=Object.create(o.prototype),s=new j(n||[]);return i(a,"_invoke",{value:E(t,r,s)}),a}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var h="suspendedStart",m="suspendedYield",v="executing",y="completed",g={};function b(){}function _(){}function x(){}var w={};p(w,l,(function(){return this}));var C=Object.getPrototypeOf,k=C&&C(C(R([])));k&&k!==r&&n.call(k,l)&&(w=k);var L=x.prototype=b.prototype=Object.create(w);function S(t){["next","throw","return"].forEach((function(e){p(t,e,(function(t){return this._invoke(e,t)}))}))}function $(t,e){function r(i,o,s,l){var c=d(t[i],t,o);if("throw"!==c.type){var u=c.arg,p=u.value;return p&&"object"==a(p)&&n.call(p,"__await")?e.resolve(p.__await).then((function(t){r("next",t,s,l)}),(function(t){r("throw",t,s,l)})):e.resolve(p).then((function(t){u.value=t,s(u)}),(function(t){return r("throw",t,s,l)}))}l(c.arg)}var o;i(this,"_invoke",{value:function(t,n){function i(){return new e((function(e,i){r(t,n,e,i)}))}return o=o?o.then(i,i):i()}})}function E(e,r,n){var i=h;return function(o,a){if(i===v)throw Error("Generator is already running");if(i===y){if("throw"===o)throw a;return{value:t,done:!0}}for(n.method=o,n.arg=a;;){var s=n.delegate;if(s){var l=O(s,n);if(l){if(l===g)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===h)throw i=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=v;var c=d(e,r,n);if("normal"===c.type){if(i=n.done?y:m,c.arg===g)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(i=y,n.method="throw",n.arg=c.arg)}}}function O(e,r){var n=r.method,i=e.iterator[n];if(i===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,O(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var o=d(i,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,g;var a=o.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,g):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function q(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function D(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function j(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(q,this),this.reset(!0)}function R(e){if(e||""===e){var r=e[l];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function r(){for(;++i<e.length;)if(n.call(e,i))return r.value=e[i],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(a(e)+" is not iterable")}return _.prototype=x,i(L,"constructor",{value:x,configurable:!0}),i(x,"constructor",{value:_,configurable:!0}),_.displayName=p(x,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===_||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,x):(t.__proto__=x,p(t,u,"GeneratorFunction")),t.prototype=Object.create(L),t},e.awrap=function(t){return{__await:t}},S($.prototype),p($.prototype,c,(function(){return this})),e.AsyncIterator=$,e.async=function(t,r,n,i,o){void 0===o&&(o=Promise);var a=new $(f(t,r,n,i),o);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},S(L),p(L,u,"Generator"),p(L,l,(function(){return this})),p(L,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=R,j.prototype={constructor:j,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(D),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function i(n,i){return s.type="throw",s.arg=e,r.next=n,i&&(r.method="next",r.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var l=n.call(a,"catchLoc"),c=n.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),D(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var i=n.arg;D(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:R(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),g}},e}function l(t,e){return d(t)||f(t,e)||u(t,e)||c()}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(t,e){if(t){if("string"==typeof t)return p(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?p(t,e):void 0}}function p(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function f(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,s=[],l=!0,c=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=o.call(r)).done)&&(s.push(n.value),s.length!==e);l=!0);}catch(t){c=!0,i=t}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw i}}return s}}function d(t){if(Array.isArray(t))return t}function h(t,e,r,n,i,o,a){try{var s=t[o](a),l=s.value}catch(t){return void r(t)}s.done?e(l):Promise.resolve(l).then(n,i)}function m(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var o=t.apply(e,r);function a(t){h(o,n,i,a,s,"next",t)}function s(t){h(o,n,i,a,s,"throw",t)}a(void 0)}))}}var v={name:"SuperAddIngredients",data:function(){return{isLoading:!1,type:"add",formData:{name:"",content:"",topic_list:[{content:"",choice_type:"single",is_required:"not_required",options_text:[{options_text:"",options_number:""}]}],assess_list:[{comparison_operator:"=",assess_number:"",assess_name:"",assess_content:""}]},formRuls:{name:[{required:!0,message:"请输入测评名称",trigger:"blur"}],content:[{required:!0,message:"请输入测评介绍",trigger:"blur"}]},categoryList:[]}},created:function(){this.type=this.$route.query.type,this.initLoad()},mounted:function(){},methods:{initLoad:function(){if("modify"===this.type){var t=this.$decodeQuery(this.$route.query.data);this.formData={id:t.id,name:t.name,content:t.content,topic_list:t.topic,assess_list:t.assess}}},searchHandle:Object(o["d"])((function(){this.currentPage=1}),300),chooseNumStr:function(t){var e=["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"];return e[t]},addQuestionnaire:function(){this.formData.topic_list.push({content:"",choice_type:"single",is_required:"not_required",options_text:[{options_text:"",options_number:""}]})},questionnaireRemove:function(t){this.formData.topic_list.splice(t,1)},addOption:function(t,e){this.formData.topic_list[t].options_text.push({options_text:"",options_number:""})},optionRemove:function(t,e){this.formData.topic_list[t].options_text.splice(e,1)},handleMove:function(t,e,r){if("up"===e){if(0===t)return;var n=r[t-1];r.splice(t-1,1),r.splice(t,0,n)}else{if(t===r.length-1)return;var i=r[t+1];r.splice(t+1,1),r.splice(t,0,i)}},addEvaluate:function(){this.formData.assess_list.push({comparison_operator:"=",assess_number:"",assess_name:"",assess_content:""})},evaluateRemove:function(t){this.formData.assess_list.splice(t,1)},addModifyHealthyQuestion:function(t){var e=this;return m(s().mark((function r(){var n,i,a,c,u,p,f,d;return s().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(e.isLoading=!0,n="",i=l(n,2),a=i[0],c=i[1],"add"!==e.type){r.next=12;break}return r.next=6,Object(o["Z"])(e.$apis.apiBackgroundHealthyQuestionAddPost(t));case 6:u=r.sent,p=l(u,2),a=p[0],c=p[1],r.next=19;break;case 12:return r.next=15,Object(o["Z"])(e.$apis.apiBackgroundHealthyQuestionModifyPost(t));case 15:f=r.sent,d=l(f,2),a=d[0],c=d[1];case 19:if(e.isLoading=!1,!a){r.next=23;break}return e.$message.error(a.message),r.abrupt("return");case 23:0===c.code?(e.$message.success(c.msg),e.$closeCurrentTab(e.$route.path)):e.$message.error(c.msg);case 24:case"end":return r.stop()}}),r)})))()},submitHandler:function(){var t=this;this.$refs.formIngredients.validate((function(e){if(e){if(t.isLoading)return t.$message.error("请勿重复提交！");t.addModifyHealthyQuestion(t.formData)}}))},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,r,n){"confirm"===e?t.$closeCurrentTab(t.$route.path):r.confirmButtonLoading||n()}}).then((function(t){})).catch((function(t){}))}}},y=v,g=(r("5d81"),r("2877")),b=Object(g["a"])(y,n,i,!1,null,null,null);e["default"]=b.exports},"5d81":function(t,e,r){"use strict";r("b6e0")},b6e0:function(t,e,r){}}]);