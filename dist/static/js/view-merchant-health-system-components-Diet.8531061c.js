(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-health-system-components-Diet","view-merchant-health-system-components-ArchivesDetail","view-merchant-health-system-constants"],{"0919":function(t,e,a){"use strict";a("f7ad")},"9c61":function(t,e,a){"use strict";a.r(e),a.d(e,"MEALTIME_SETTING",(function(){return i})),a.d(e,"JOB_LIST",(function(){return r}));var i={tooltip:{trigger:"item",borderColor:"#FCA155",textStyle:{color:"#000",fontWeight:500},backgroundColor:"#fff",extraCssText:"box-shadow: 0 0 10px rgba(0,0,0,0.2);font-weight: 540;",formatter:function(t){var e=t.marker,a=t.percent;return e+t.name+"&nbsp;&nbsp;&nbsp;"+a+"%"}},title:{text:"0",x:"center",y:"center",top:"25%",textStyle:{color:"#fd953c",fontSize:18}},legend:{top:"62%",orient:"vertical",y:"bottom",padding:[0,0,0,0]},series:[{type:"pie",radius:["45%","60%"],avoidLabelOverlap:!1,top:"-10%",height:200,itemStyle:{borderRadius:10,borderColor:"#f1f2f5",borderWidth:3},hoverAnimation:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!1,fontSize:"20",fontWeight:"bold"}},labelLine:{show:!1},data:[]}],color:["#07DED0","#FE985F","#e98397","#F97C95","#58AFFE","#F8C345"]},r=[{label:"学生",value:"student",status:!1,type:"0"},{label:"在职教学人员",value:"teacher",status:!1,type:"1"},{label:"军人",value:"soldier",status:!1,type:"2"},{label:"国家机关、党群组织、企业、事业单位负责人",value:"org_leaders",status:!1,type:"3"},{label:"其他专业技术和管理人员",value:"manage",status:!1,type:"4"},{label:"商业、服务业人员",value:"service",status:!1,type:"5"},{label:"办事人员和有关人员（含公务员）",value:"clerks",status:!1,type:"6"},{label:"农、林、牧、渔、水利生产人员",value:"farmers",status:!1,type:"7"},{label:"生产、运输设备操作人员及其有关人员",value:"production",status:!1,type:"8"},{label:"不便分类的其他从业人员",value:"other",status:!1,type:"9"},{label:"待业人员",value:"unemployed",status:!1,type:"10"}]},bd1e:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"diet-wrapp"},[e("div",{staticClass:"ps-flex flex-wrap m-t-10"},[e("div",[e("div",{staticClass:"diet-title m-r-10 m-b-10"},[t._m(0),e("div",{ref:"intake_record",staticStyle:{height:"240px"},attrs:{id:"circular_chart"}})])]),e("div",[e("div",{staticClass:"diet-title m-r-10 m-b-10"},[t._m(1),e("div",{ref:"source",staticStyle:{height:"240px"},attrs:{id:"circular_chart"}})])]),e("div",[e("div",{staticClass:"diet-title m-r-10 m-b-10"},[t._m(2),e("div",{ref:"intake_exceed",staticStyle:{height:"240px"},attrs:{id:"circular_chart"}})])]),e("div",[e("div",{staticClass:"diet-title m-r-10 m-b-10"},[t._m(3),e("div",{ref:"food_category",staticStyle:{height:"240px"},attrs:{id:"circular_chart"}})])])])])},r=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"ps-flex flex-align-c"},[e("span",{staticClass:"text p-l-10"},[t._v("饮食记录")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"ps-flex flex-align-c"},[e("span",{staticClass:"text p-l-10"},[t._v("记录来源")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"ps-flex flex-align-c"},[e("span",{staticClass:"text p-l-10"},[t._v("摄入超标")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"ps-flex flex-align-c"},[e("span",{staticClass:"text p-l-10"},[t._v("食物种类")])])}],o=a("9c61"),s=a("ed08"),n=a("da92"),l={props:{formInfoData:{type:Object,default:function(){return{}}}},data:function(){return{isLoading:!1,formData:{},circularChartRefList:[{key:"intake_record",data:[{value:0,name:"早餐",key:"breakfast",current:0,unit:"次"},{value:0,name:"午餐",key:"lunch",current:0,unit:"次"},{value:0,name:"晚餐",key:"dinner",current:0,unit:"次"}],color:["#07DED0","#FE985F","#e98397"]},{key:"source",data:[{value:0,name:"系统",key:"system",current:0,unit:"次"},{value:0,name:"手动",key:"user",current:0,unit:"次"}],color:["#FE985F","#f6c80e"]},{key:"intake_exceed",data:[{value:0,name:"早餐",key:"breakfast",current:0,unit:"次"},{value:0,name:"午餐",key:"lunch",current:0,unit:"次"},{value:0,name:"晚餐",key:"dinner",current:0,unit:"次"}],color:["red","#FE985F","#e98397"]},{key:"food_category",data:[{value:0,name:"谷类薯类",key:"cereals_tubers",current:0,unit:"种"},{value:0,name:"鱼禽蛋肉",key:"eggsandmeat",current:0,unit:"种"},{value:0,name:"蔬菜水果",key:"fruit_vegetable",current:0,unit:"种"},{value:0,name:"奶类豆类",key:"dairy",current:0,unit:"种"}],color:["#08d7d7","#4e95fa","#4ad96c","#727aff"]}],pieChart:{intake_record:null,source:null,intake_exceed:null,food_category:null},tabType:"food",tableData:[],dietData:{intake_exceed_total:0,intake_record_total:0,source_total:0,food_category_total:0},currentPage:1,pageSize:6,totalCount:0,foodList:[],ingredientList:[]}},watch:{formInfoData:function(t){var e=this;this.tabType="food",this.formData=t,this.dietData.intake_record_total=n["a"].plus(this.formData.intake_record.breakfast,this.formData.intake_record.lunch,this.formData.intake_record.dinner),this.dietData.source_total=n["a"].plus(this.formData.source.system,this.formData.source.user),this.dietData.intake_exceed_total=n["a"].plus(this.formData.intake_exceed.breakfast,this.formData.intake_exceed.lunch,this.formData.intake_exceed.dinner),this.dietData.food_category_total=n["a"].plus(this.formData.food_category.cereals_tubers,this.formData.food_category.dairy,this.formData.food_category.eggsandmeat,this.formData.food_category.fruit_vegetable),this.tableData=this.formData.food_list,this.foodList=this.formData.food_list,this.ingredientList=this.formData.ingredient_list,this.$nextTick((function(){e.initMealTimeDataPie()}))}},created:function(){},beforeDestroy:function(){window.removeEventListener("resize",this.resizeChartHandle)},mounted:function(){window.addEventListener("resize",this.resizeChartHandle)},methods:{initMealTimeDataPie:function(){var t=this;this.circularChartRefList.forEach((function(e){var a=e.data,i={};a.forEach((function(a){"intake_record"===e.key&&t.formData[e.key][a.key]/t.dietData.intake_record_total&&(a.value=Number((t.formData[e.key][a.key]/t.dietData.intake_record_total*100).toFixed(2))),"source"===e.key&&t.formData[e.key][a.key]/t.dietData.source_total&&(a.value=Number((t.formData[e.key][a.key]/t.dietData.source_total*100).toFixed(2))),"intake_exceed"===e.key&&t.formData[e.key][a.key]/t.dietData.intake_exceed_total&&(a.value=Number((t.formData[e.key][a.key]/t.dietData.intake_exceed_total*100).toFixed(2))),"food_category"===e.key&&t.formData[e.key][a.key]/t.dietData.food_category_total&&(a.value=Number((t.formData[e.key][a.key]/t.dietData.food_category_total*100).toFixed(2))),i[a.name]={value:a.value,current:t.formData[e.key][a.key],unit:a.unit}}));var r=o["MEALTIME_SETTING"];r.legend.formatter=function(t){var e=i[t];return t+"    "+(e.value||0)+"%    "+(e.current||0)+e.unit},r.series[0].data=a,r.title.text="".concat(t.dietData[e.key+"_total"]).concat("food_category"===e.key?"种":"次"),t.pieChart[e.key]||(t.pieChart[e.key]=t.$echarts.init(t.$refs[e.key])),t.pieChart[e.key].setOption(r)}))},tableLoadmore:function(){var t=this;setTimeout((function(){t.pageSize+=10}),100)},tabClick:function(t){this.tabType=t,this.tableData=[],this.pageSize=6,this.tableData="food"===t?this.foodList:this.ingredientList},resizeChartHandle:Object(s["d"])((function(){this.pieChart.intake_record&&this.pieChart.intake_record.resize(),this.pieChart.source&&this.pieChart.source.resize(),this.pieChart.intake_exceed&&this.pieChart.intake_exceed.resize(),this.pieChart.food_category&&this.pieChart.food_category.resize()}),300)}},c=l,u=(a("0919"),a("2877")),d=Object(u["a"])(c,i,r,!1,null,null,null);e["default"]=d.exports},f7ad:function(t,e,a){}}]);