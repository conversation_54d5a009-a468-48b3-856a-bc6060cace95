(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-user-center-constants-autoRegisterApprove"],{"6ad0":function(e,l,a){"use strict";a.r(l),a.d(l,"TABLE_SETTING_PENDING",(function(){return n})),a.d(l,"TABLE_SETTING_FINISH",(function(){return r})),a.d(l,"TABLE_SETTING_REJECT",(function(){return o})),a.d(l,"TABLE_SETTING_LIST",(function(){return i})),a.d(l,"SEARCH_FORM_PENDING",(function(){return p})),a.d(l,"SEARCH_FORM_FINISH",(function(){return c})),a.d(l,"SEARCH_FORM_LIST",(function(){return y}));var t=a("ed08"),n=[{label:"全选",key:"selection",type:"selection"},{label:"申请时间",key:"create_time"},{label:"所属组织",key:"org_name"},{label:"人员编号",key:"person_no"},{label:"姓名",key:"name"},{label:"手机号",key:"phone"},{label:"申请备注",key:"remark"},{label:"操作",key:"operation",type:"slot",slotName:"operation",width:"100"}],r=[{label:"审批时间",key:"approve_time"},{label:"申请时间",key:"create_time"},{label:"所属组织",key:"org_name"},{label:"人员编号",key:"person_no"},{label:"姓名",key:"name"},{label:"手机号",key:"phone"},{label:"申请备注",key:"remark",showTooltip:!0}],o=[{label:"审批时间",key:"approve_time"},{label:"申请时间",key:"create_time"},{label:"所属组织",key:"org_name"},{label:"人员编号",key:"person_no"},{label:"姓名",key:"name"},{label:"手机号",key:"phone"},{label:"申请备注",key:"remark",showTooltip:!0},{label:"拒绝说明",key:"reject_reason",showTooltip:!0}],i={pending:n,finish:r,reject:o},p={select_time:{label:"申请时间",type:"daterange",value:Object(t["y"])(7),format:"yyyy-MM-dd",clearable:!1},phone:{type:"input",value:"",label:"手机号",placeholder:"请输入",clearable:!0},person_no:{type:"input",value:"",label:"人员编号",placeholder:"请输入",clearable:!0},name:{type:"input",value:"",label:"姓名",placeholder:"请输入",clearable:!0},source_organization_id:{type:"organizationSelect",value:[],label:"所属组织",listNameKey:"name",listValueKey:"id",dataList:[],checkStrictly:!0,multiple:!0,collapseTags:!0,clearable:!0}},c={date_type:{type:"select",value:"approve_time",maxWidth:"100px",dataList:[{label:"审批时间",value:"approve_time"},{label:"申请时间",value:"create_time"}]},select_time:{label:"",type:"daterange",value:Object(t["y"])(7),format:"yyyy-MM-dd",clearable:!1},phone:{type:"input",value:"",label:"手机号",placeholder:"请输入",clearable:!0},person_no:{type:"input",value:"",label:"人员编号",placeholder:"请输入",clearable:!0},name:{type:"input",value:"",label:"姓名",placeholder:"请输入",clearable:!0},source_organization_id:{type:"organizationSelect",value:[],label:"所属组织",listNameKey:"name",listValueKey:"id",dataList:[],checkStrictly:!0,multiple:!0,collapseTags:!0,clearable:!0}},y={pending:p,finish:c,reject:c}}}]);