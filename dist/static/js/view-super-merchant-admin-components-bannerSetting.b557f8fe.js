(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-merchant-admin-components-bannerSetting"],{"0691":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"container-wrapper bannersetting"},[e("div",{staticClass:"l-title clearfix"},[e("span",{staticClass:"float-l min-title-h"},[t._v("首页轮播图")]),"detail"===t.formOperate?e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.organization.modify"],expression:"['background.admin.organization.modify']"}],staticClass:"float-r",attrs:{size:"mini"},on:{click:t.changeOperate}},[t._v("编辑")]):t._e()],1),e("div",{staticClass:"appid-box"},[e("el-form",{ref:"appidRef",attrs:{rules:t.formDataRuls,model:t.formData,size:"small","label-width":"100px"}},[e("el-form-item",{attrs:{label:"首页轮播",prop:"banner"}},[e("el-select",{staticClass:"ps-select w-300",attrs:{disabled:!t.checkIsFormStatus,multiple:!0},model:{value:t.formData.banner,callback:function(e){t.$set(t.formData,"banner",e)},expression:"formData.banner"}},t._l(t.bannerList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)],1),t.checkIsFormStatus?e("div",{staticClass:"add-wrapper"},[e("el-button",{staticClass:"ps-origin-btn",attrs:{type:"primary",size:"small"},on:{click:t.saveSettingHandle}},[t._v("保存")])],1):t._e()],1)])},a=[],o=r("ed08");function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},c=o.iterator||"@@iterator",u=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function h(t,e,r,n){var o=e&&e.prototype instanceof b?e:b,i=Object.create(o.prototype),s=new A(n||[]);return a(i,"_invoke",{value:j(t,r,s)}),i}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var d="suspendedStart",m="suspendedYield",g="executing",v="completed",y={};function b(){}function w(){}function L(){}var x={};f(x,c,(function(){return this}));var k=Object.getPrototypeOf,O=k&&k(k(P([])));O&&O!==r&&n.call(O,c)&&(x=O);var S=L.prototype=b.prototype=Object.create(x);function _(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function E(t,e){function r(a,o,s,c){var u=p(t[a],t,o);if("throw"!==u.type){var l=u.arg,f=l.value;return f&&"object"==i(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,s,c)}),(function(t){r("throw",t,s,c)})):e.resolve(f).then((function(t){l.value=t,s(l)}),(function(t){return r("throw",t,s,c)}))}c(u.arg)}var o;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return o=o?o.then(a,a):a()}})}function j(e,r,n){var a=d;return function(o,i){if(a===g)throw Error("Generator is already running");if(a===v){if("throw"===o)throw i;return{value:t,done:!0}}for(n.method=o,n.arg=i;;){var s=n.delegate;if(s){var c=B(s,n);if(c){if(c===y)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===d)throw a=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=g;var u=p(e,r,n);if("normal"===u.type){if(a=n.done?v:m,u.arg===y)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(a=v,n.method="throw",n.arg=u.arg)}}}function B(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,B(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var o=p(a,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,y;var i=o.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function D(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function $(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function A(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(D,this),this.reset(!0)}function P(e){if(e||""===e){var r=e[c];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,o=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(i(e)+" is not iterable")}return w.prototype=L,a(S,"constructor",{value:L,configurable:!0}),a(L,"constructor",{value:w,configurable:!0}),w.displayName=f(L,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,L):(t.__proto__=L,f(t,l,"GeneratorFunction")),t.prototype=Object.create(S),t},e.awrap=function(t){return{__await:t}},_(E.prototype),f(E.prototype,u,(function(){return this})),e.AsyncIterator=E,e.async=function(t,r,n,a,o){void 0===o&&(o=Promise);var i=new E(h(t,r,n,a),o);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},_(S),f(S,l,"Generator"),f(S,c,(function(){return this})),f(S,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=P,A.prototype={constructor:A,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach($),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return s.type="throw",s.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],s=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var c=n.call(i,"catchLoc"),u=n.call(i,"finallyLoc");if(c&&u){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,y):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),$(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;$(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:P(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),y}},e}function c(t,e){return p(t)||h(t,e)||l(t,e)||u()}function u(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function l(t,e){if(t){if("string"==typeof t)return f(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?f(t,e):void 0}}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function h(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,o,i,s=[],c=!0,u=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(s.push(n.value),s.length!==e);c=!0);}catch(t){u=!0,a=t}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw a}}return s}}function p(t){if(Array.isArray(t))return t}function d(t,e,r,n,a,o,i){try{var s=t[o](i),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,a)}function m(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var o=t.apply(e,r);function i(t){d(o,n,a,i,s,"next",t)}function s(t){d(o,n,a,i,s,"throw",t)}i(void 0)}))}}var g={name:"SuperBannerSetting",props:{type:String,infoData:{type:Object,default:function(){return{}}},organizationData:Object,restoreHandle:Function},components:{},data:function(){return{formOperate:"detail",isLoading:!1,formData:{banner:[]},formDataRuls:{banner:[{required:!0,message:"请先选择",trigger:"blur"}]},bannerList:[]}},computed:{checkIsFormStatus:function(){var t=!1;switch(this.formOperate){case"detail":t=!1;break;case"modify":t=!0;break}return t}},watch:{},created:function(){},mounted:function(){this.initLoad()},methods:{initLoad:function(){this.getSelectBannerList()},refreshHandle:function(){this.initLoad()},searchHandle:Object(o["d"])((function(){}),300),changeOperate:function(){"modify"!==this.formOperate?this.formOperate="modify":this.formOperate="detail"},getBannerList:function(){var t=this;return m(s().mark((function e(){var r,n,a,o,i;return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,r={org_id:t.organizationData.id,page:1,page_size:999999},e.next=4,t.$to(t.$apis.apiBackgroundAdminMarketingBannerListPost(r));case 4:if(n=e.sent,a=c(n,2),o=a[0],i=a[1],t.isLoading=!1,!o){e.next=12;break}return t.$message.error(o.message),e.abrupt("return");case 12:0===i.code&&(t.bannerList=i.data.results);case 13:case"end":return e.stop()}}),e)})))()},getSelectBannerList:function(){var t=this;return m(s().mark((function e(){var r,n,a,o,i;return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,r={org_id:t.organizationData.id,page:1,page_size:999999},e.next=4,t.$to(t.$apis.apiBackgroundAdminMarketingBannerGetOrgBannerListPost(r));case 4:if(n=e.sent,a=c(n,2),o=a[0],i=a[1],t.isLoading=!1,!o){e.next=12;break}return t.$message.error(o.message),e.abrupt("return");case 12:0===i.code?(t.bannerList=i.data,i.data.map((function(e){e.is_select&&t.formData.banner.push(e.id)}))):t.$message.error(i.msg);case 13:case"end":return e.stop()}}),e)})))()},saveSettingHandle:function(){var t=this;return m(s().mark((function e(){return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.isLoading){e.next=2;break}return e.abrupt("return");case 2:t.$refs.appidRef.validate((function(e){e&&t.modifySetting()}));case 3:case"end":return e.stop()}}),e)})))()},modifySetting:function(){var t=this;return m(s().mark((function e(){var r,n,a,i,u;return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r={org_id:t.organizationData.id,banner_ids:t.formData.banner},t.isLoading=!0,e.next=4,Object(o["Z"])(t.$apis.apiBackgroundAdminMarketingBannerSetOrgsPost(r));case 4:if(n=e.sent,a=c(n,2),i=a[0],u=a[1],t.isLoading=!1,!i){e.next=12;break}return t.$message.error(i.message),e.abrupt("return");case 12:0===u.code?(t.$message.success("修改成功"),t.formOperate="detail",t.restoreHandle(t.type,t.formOperate)):t.$message.error(u.msg);case 13:case"end":return e.stop()}}),e)})))()}}},v=g,y=(r("814e"),r("2877")),b=Object(y["a"])(v,n,a,!1,null,null,null);e["default"]=b.exports},"814e":function(t,e,r){"use strict";r("8783")},8783:function(t,e,r){t.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}}}]);