(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-menu-rule-setting-MenuRuleSetting","view-merchant-consumption-rules-service-admin-AddChargeServiceRule","view-merchant-meal-management-components-mealFoodList-FoodDiscountDialog","view-merchant-meal-management-menu-rule-setting-constants"],{"024f":function(t,e,r){},"27dd":function(t,e,r){"use strict";r("024f")},"2ecc":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"menu-rule-setting container-wrapper"},[e("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formRules",attrs:{rules:t.formRuls,model:t.formData,size:"small"}},[e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("规则设置")]),e("div",{staticClass:"p-r-20"},[e("span",{staticClass:"p-r-10",staticStyle:{"font-size":"13px"}},[t._v("菜品提醒规则")]),e("el-switch",{attrs:{"active-color":"#ff9b45"},model:{value:t.formData.food_tips,callback:function(e){t.$set(t.formData,"food_tips",e)},expression:"formData.food_tips"}})],1)]),t.formData.food_tips?e("div",{staticStyle:{padding:"0 20px"}},[e("div",{staticClass:"ps-flex"},[e("el-form-item",{attrs:{label:""}},[e("span",{staticClass:"p-r-10"},[t._v("添加的菜品销量在指定时间")])]),e("el-form-item",{attrs:{label:"",prop:"food_tips_time"}},[e("el-select",{staticClass:"ps-select w-100 p-r-10",attrs:{"popper-class":"ps-popper-select"},model:{value:t.formData.food_tips_time,callback:function(e){t.$set(t.formData,"food_tips_time",e)},expression:"formData.food_tips_time"}},[e("el-option",{attrs:{label:"近7天",value:7}}),e("el-option",{attrs:{label:"近30天",value:30}})],1)],1),e("el-form-item",{attrs:{label:""}},[e("span",{staticClass:"m-r-10"},[t._v("内，销售排行位于后")])]),e("el-form-item",{attrs:{label:"",prop:"food_tips_rank"}},[e("el-select",{staticClass:"ps-select m-r-10",staticStyle:{width:"120px"},attrs:{"popper-class":"ps-popper-select"},model:{value:t.formData.food_tips_rank,callback:function(e){t.$set(t.formData,"food_tips_rank",e)},expression:"formData.food_tips_rank"}},[e("el-option",{attrs:{label:1,value:1}}),e("el-option",{attrs:{label:2,value:2}}),e("el-option",{attrs:{label:3,value:3}}),e("el-option",{attrs:{label:4,value:4}}),e("el-option",{attrs:{label:5,value:5}}),e("el-option",{attrs:{label:6,value:6}}),e("el-option",{attrs:{label:7,value:7}}),e("el-option",{attrs:{label:8,value:8}}),e("el-option",{attrs:{label:9,value:9}}),e("el-option",{attrs:{label:10,value:10}})],1),e("span",[t._v("名进行提醒")])],1)],1),e("div",{staticClass:"ps-flex"},[e("el-form-item",{attrs:{label:""}},[e("span",{staticClass:"p-r-10"},[t._v("添加的菜品在")])]),e("el-form-item",{attrs:{label:"",prop:"food_tips_menu_type"}},[e("el-select",{staticClass:"ps-select w-100 p-r-10",attrs:{"popper-class":"ps-popper-select"},model:{value:t.formData.food_tips_menu_type,callback:function(e){t.$set(t.formData,"food_tips_menu_type",e)},expression:"formData.food_tips_menu_type"}},[e("el-option",{attrs:{label:"周菜谱",value:"week"}}),e("el-option",{attrs:{label:"月菜谱",value:"month"}})],1)],1),e("el-form-item",{attrs:{label:""}},[e("span",{staticClass:"m-r-10"},[t._v("菜谱内，重复")])]),e("el-form-item",{attrs:{label:"",prop:"food_tips_repeat_number"}},[e("el-input",{staticClass:"ps-input w-100 m-r-10",attrs:{placeholder:""},model:{value:t.formData.food_tips_repeat_number,callback:function(e){t.$set(t.formData,"food_tips_repeat_number",e)},expression:"formData.food_tips_repeat_number"}}),e("span",[t._v("次，进行提醒")])],1)],1),t._l(t.formData.menu_food_category_limit_rule,(function(r,n){return e("div",{key:n+"_seasonal"},[e("div",{staticClass:"ps-flex flex-align-c"},[e("el-form-item",{attrs:{label:""}},[e("span",{staticClass:"p-r-10"},[t._v(" 智能推荐的菜品属性")])]),e("el-form-item",{staticClass:"m-r-10",attrs:{label:"",prop:"menu_food_category_limit_rule["+n+"].category_id",rules:t.formRuls.category_id}},[e("el-select",{staticClass:"ps-select w-100 p-r-10",attrs:{"popper-class":"ps-popper-select",filterable:""},on:{change:function(e){return t.changeIntAttrId(r)}},model:{value:r.category_id,callback:function(e){t.$set(r,"category_id",e)},expression:"item.category_id"}},[e("el-option",{attrs:{label:"全选",value:0}}),t._l(t.foodAttributeList,(function(t){return e("el-option",{key:t.key,attrs:{label:t.label,value:t.key}})}))],2),e("span",[t._v("在")])],1),e("el-form-item",{attrs:{label:"",prop:"menu_food_category_limit_rule["+n+"].menu_type",rules:t.formRuls.menu_type}},[e("el-select",{staticClass:"ps-select w-100 p-r-10",attrs:{"popper-class":"ps-popper-select",disabled:""===r.category_id},model:{value:r.menu_type,callback:function(e){t.$set(r,"menu_type",e)},expression:"item.menu_type"}},t._l(t.menuTypeListData,(function(n){return e("el-option",{key:n.value,attrs:{label:n.name,value:n.value,disabled:t.disabledMenuType(r.category_id,n.value)}})})),1)],1),e("el-form-item",{attrs:{label:"",prop:"menu_food_category_limit_rule["+n+"].days",rules:t.formRuls.days}},[e("el-input",{staticClass:"ps-input w-100 m-r-10",attrs:{placeholder:""},model:{value:r.days,callback:function(e){t.$set(r,"days",e)},expression:"item.days"}}),e("span",[t._v("天内，")])],1),e("el-form-item",{attrs:{label:"",prop:"menu_food_category_limit_rule["+n+"].food_tips_repeat_number",rules:t.formRuls.food_tips_repeat_number}},[e("span",{staticClass:"p-r-10"},[t._v("允许重复")]),e("el-input",{staticClass:"ps-input w-100 m-r-10",attrs:{placeholder:""},model:{value:r.food_tips_repeat_number,callback:function(e){t.$set(r,"food_tips_repeat_number",e)},expression:"item.food_tips_repeat_number"}}),e("span",[t._v("次")])],1),e("el-form-item",{attrs:{label:"",prop:""}},[e("el-button",{staticClass:"p-l-20",staticStyle:{"font-size":"25px"},attrs:{icon:"el-icon-circle-plus",type:"text"},on:{click:function(e){return t.clickAddMenuFoodLimitRule("menu_food_category_limit_rule")}}}),t.formData.menu_food_category_limit_rule.length>1?e("el-button",{staticStyle:{"font-size":"25px"},attrs:{icon:"el-icon-remove",type:"text"},on:{click:function(e){return t.clickDelectMenuFoodLimitRule(n,"menu_food_category_limit_rule")}}}):t._e()],1)],1)])})),t._l(t.ruleAttrMeal,(function(r,n){return e("div",{key:r.key,staticStyle:{"max-width":"900px"}},[e("div",{staticClass:"m-b-20"},[t._v(t._s(r.label)+"：智能推荐的菜品属性数量："),e("span",{staticClass:"red"},[t._v("共"+t._s(r.count?r.count:0)+"道")])]),e("div",{staticClass:"inline-form-item"},t._l(r.data,(function(o){return e("el-form-item",{key:o.key,attrs:{"label-width":"120px",label:t.nameFormat(o.label,5),rules:t.formRuls.arr_number,prop:r.key+"_food_data."+o.key}},[e("el-input",{staticClass:"ps-input w-100 m-l-10 m-r-10",attrs:{placeholder:""},on:{input:function(e){return t.inputChangeRuleHandle(e,r,n)}},model:{value:t.formData[r.key+"_food_data"][o.key],callback:function(e){t.$set(t.formData[r.key+"_food_data"],o.key,e)},expression:"formData[meal.key+'_food_data'][item.key]"}}),e("span",{staticClass:"m-r-10"},[t._v("道")])],1)})),1)])}))],2):t._e()]),e("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[e("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:t.submitHandler}},[t._v(" 保存 ")])],1)])],1)},o=[],a=r("d0dd"),i=r("ed08"),u=r("da92"),s=r("e173"),l=r("ca94"),c=r("e925");function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function d(t,e){return y(t)||h(t,e)||m(t,e)||p()}function p(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function m(t,e){if(t){if("string"==typeof t)return _(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_(t,e):void 0}}function _(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function h(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,u=[],s=!0,l=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){l=!0,o=t}finally{try{if(!s&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(l)throw o}}return u}}function y(t){if(Array.isArray(t))return t}function v(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */v=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function(t,e,r){return t[e]=r}}function c(t,e,r,n){var a=e&&e.prototype instanceof g?e:g,i=Object.create(a.prototype),u=new C(n||[]);return o(i,"_invoke",{value:j(t,r,u)}),i}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=c;var p="suspendedStart",m="suspendedYield",_="executing",h="completed",y={};function g(){}function b(){}function w(){}var k={};l(k,i,(function(){return this}));var E=Object.getPrototypeOf,x=E&&E(E(M([])));x&&x!==r&&n.call(x,i)&&(k=x);var L=w.prototype=g.prototype=Object.create(k);function D(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function $(t,e){function r(o,a,i,u){var s=d(t[o],t,a);if("throw"!==s.type){var l=s.arg,c=l.value;return c&&"object"==f(c)&&n.call(c,"__await")?e.resolve(c.__await).then((function(t){r("next",t,i,u)}),(function(t){r("throw",t,i,u)})):e.resolve(c).then((function(t){l.value=t,i(l)}),(function(t){return r("throw",t,i,u)}))}u(s.arg)}var a;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return a=a?a.then(o,o):o()}})}function j(e,r,n){var o=p;return function(a,i){if(o===_)throw Error("Generator is already running");if(o===h){if("throw"===a)throw i;return{value:t,done:!0}}for(n.method=a,n.arg=i;;){var u=n.delegate;if(u){var s=O(u,n);if(s){if(s===y)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=h,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=_;var l=d(e,r,n);if("normal"===l.type){if(o=n.done?h:m,l.arg===y)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(o=h,n.method="throw",n.arg=l.arg)}}}function O(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,O(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var a=d(o,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,y;var i=a.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function S(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function A(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function C(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(S,this),this.reset(!0)}function M(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(f(e)+" is not iterable")}return b.prototype=w,o(L,"constructor",{value:w,configurable:!0}),o(w,"constructor",{value:b,configurable:!0}),b.displayName=l(w,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,l(t,s,"GeneratorFunction")),t.prototype=Object.create(L),t},e.awrap=function(t){return{__await:t}},D($.prototype),l($.prototype,u,(function(){return this})),e.AsyncIterator=$,e.async=function(t,r,n,o,a){void 0===a&&(a=Promise);var i=new $(c(t,r,n,o),a);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},D(L),l(L,s,"Generator"),l(L,i,(function(){return this})),l(L,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=M,C.prototype={constructor:C,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(A),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return u.type="throw",u.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],u=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var s=n.call(i,"catchLoc"),l=n.call(i,"finallyLoc");if(s&&l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=t,i.arg=e,a?(this.method="next",this.next=a.finallyLoc,y):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),A(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;A(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:M(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),y}},e}function g(t,e,r,n,o,a,i){try{var u=t[a](i),s=u.value}catch(t){return void r(t)}u.done?e(s):Promise.resolve(s).then(n,o)}function b(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var a=t.apply(e,r);function i(t){g(a,n,o,i,u,"next",t)}function u(t){g(a,n,o,i,u,"throw",t)}i(void 0)}))}}var w={data:function(){var t=function(t,e,r){var n=/^\d+$/;!n.test(e)&&e?r(new Error("请输入正确数字")):r()},e=function(t,e,r){e?Object(c["b"])(e)?r():r(new Error("请输入数字")):r()};return{menuTypeListData:Object(i["f"])(l["MENU_TYPE_LIST_DATA"]),isLoading:!1,formData:{food_tips:!1,food_tips_time:"",food_tips_rank:"",food_tips_menu_type:"",food_tips_repeat_number:"",breakfast_food_data:{},lunch_food_data:{},dinner_food_data:{},menu_food_category_limit_rule:[{category_id:"",menu_type:"",days:0,food_tips_repeat_number:0}]},formRuls:{food_tips_time:[{required:!0,message:"请选择",trigger:"change"}],food_tips_rank:[{required:!0,message:"请选择",trigger:"change"}],food_tips_menu_type:[{required:!0,message:"请选择",trigger:"change"}],food_tips_repeat_number:[{required:!0,message:"请输入整数",validator:a["f"],trigger:"blur"}],arr_number:[{validator:e,trigger:"blur"}],food_menu_price:[{validator:s["c"],trigger:"change"}],category_id:[{required:!0,message:"请选择菜品属性",trigger:["blur","change"]}],menu_type:[{required:!0,message:"请选择菜谱",trigger:["blur","change"]}],days:[{required:!1,validator:t,message:"请输入整数",trigger:["blur","change"]}]},ruleAttrMeal:[{label:"早餐",key:"breakfast",count:0,data:[]},{label:"午餐",key:"lunch",count:0,data:[]},{label:"下午茶",key:"afternoon",count:0,data:[]},{label:"晚餐",key:"dinner",count:0,data:[]},{label:"夜宵",key:"supper",count:0,data:[]},{label:"凌晨餐",key:"morning",count:0,data:[]}],foodAttributeList:[]}},created:function(){var t=this;return b(v().mark((function e(){return v().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.getFoodAttributeList();case 2:t.getMenuSettingInfoRuleDetail();case 3:case"end":return e.stop()}}),e)})))()},computed:{disabledMenuType:function(){var t=this;return function(e,r){var n=t.formData.menu_food_category_limit_rule.reduce((function(t,r){return 0===e||e===r.category_id||0===r.category_id?t.concat(r.menu_type):t}),[]),o=!1;return n.includes(r)&&(o=!0),o}},disabledSeasonalMenu:function(){var t=this.formData.menu_seasonal_food_limit_rule.reduce((function(t,e){return t.concat(e.menu_type)}),[]);return function(e){var r=!1;return t.includes(e)&&(r=!0),r}}},mounted:function(){},methods:{getFoodAttributeList:function(){var t=this;return b(v().mark((function e(){var r,n,o,a;return v().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(i["Z"])(t.$apis.apiBackgroundFoodFoodCategoryListPost({page:1,page_size:99999}));case 3:if(r=e.sent,n=d(r,2),o=n[0],a=n[1],t.isLoading=!1,!o){e.next=11;break}return t.$message.error(o.message),e.abrupt("return");case 11:0===a.code?t.foodAttributeList=a.data.results.map((function(t){var e={label:t.name,key:t.id};return e})):t.$message.error(a.msg);case 12:case"end":return e.stop()}}),e)})))()},getMenuSettingInfoRuleDetail:function(){var t=this;return b(v().mark((function e(){var r,n,o,a,u,s,l;return v().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(i["Z"])(t.$apis.apiBackgroundFoodMenuSettingInfoRuleDetailPost());case 3:if(r=e.sent,n=d(r,2),o=n[0],a=n[1],t.isLoading=!1,!o){e.next=11;break}return t.$message.error(o.message),e.abrupt("return");case 11:if(0!==a.code){e.next=32;break}if(u=Object(i["f"])(t.formData),!a.data){e.next=29;break}e.t0=v().keys(a.data);case 15:if((e.t1=e.t0()).done){e.next=29;break}s=e.t1.value,l=a.data[s],e.t2=s,e.next="menu_food_category_limit_rule"===e.t2?21:"menu_seasonal_food_limit_rule"===e.t2?23:25;break;case 21:return l.length>0&&(u[s]=l),e.abrupt("break",27);case 23:return l.length>0&&(u[s]=l),e.abrupt("break",27);case 25:return u[s]=l,e.abrupt("break",27);case 27:e.next=15;break;case 29:t.setFoodRuleAttr(u),e.next=33;break;case 32:t.$message.error(a.msg);case 33:case"end":return e.stop()}}),e)})))()},processFoodData:function(t){var e=this,r={};if(t&&Object.keys(t).length){var n=function(n){e.foodAttributeList.forEach((function(e){Number(e.key)===Number(n)&&(r[n]=t[n]?t[n]:0)}))};for(var o in t)n(o)}else this.foodAttributeList.forEach((function(t){r[t.key]=0}));return r},changeIntAttrId:function(t){t.menu_type=""},setFoodRuleAttr:function(t){var e=this,r=this.foodAttributeList.map((function(t){return t.key}));this.ruleAttrMeal.forEach((function(n,o){e.$set(n,"data",Object(i["f"])(e.foodAttributeList));var a=n.key+"_food_data";if(t[a]){var u={};Object.keys(t[a]).forEach((function(e){r.includes(Number(e))&&(u[e]=t[a][e])})),t[a]=u}})),this.formData=t,this.ruleAttrMeal.forEach((function(t,r){e.changeMealRuleHandle("",t,r)}))},inputChangeRuleHandle:Object(i["d"])((function(t,e,r){this.changeMealRuleHandle(t,e,r)}),300),changeMealRuleHandle:function(t,e,r){var n=this,o=e.key+"_food_data",a=0;Object.keys(this.formData[o]).forEach((function(t){n.formData[o][t]&&(a=u["a"].plus(a,n.formData[o][t]))})),this.$set(this.ruleAttrMeal[r],"count",a)},formatParams:function(){var t=Object(i["f"])(this.formData);return t.food_tips_rank=Number(t.food_tips_rank),this.ruleAttrMeal.forEach((function(e,r){var n=e.key+"_food_data",o=t[n];e.data.forEach((function(t){Number(o[t.key])?o[t.key]=Number(o[t.key]):o[t.key]=0}))})),t.menu_food_category_limit_rule=this.formData.menu_food_category_limit_rule.map((function(t){var e={category_id:t.category_id,menu_type:t.menu_type,days:Number(t.days),food_tips_repeat_number:Number(t.food_tips_repeat_number)};return e})),t},getMenuSettingInfoModify:function(){var t=this;return b(v().mark((function e(){var r,n,o,a;return v().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(i["Z"])(t.$apis.apiBackgroundFoodMenuSettingInfoModifyPost(t.formatParams()));case 3:if(r=e.sent,n=d(r,2),o=n[0],a=n[1],t.isLoading=!1,!o){e.next=11;break}return t.$message.error(o.message),e.abrupt("return");case 11:0===a.code?(t.$message.success(a.msg),t.getMenuSettingInfoRuleDetail()):t.$message.error(a.msg);case 12:case"end":return e.stop()}}),e)})))()},clickAddMenuFoodLimitRule:function(t){this.formData.menu_food_category_limit_rule.push({category_id:"",menu_type:"",days:0,food_tips_repeat_number:0})},clickDelectMenuFoodLimitRule:function(t,e){this.formData.menu_food_category_limit_rule.splice(t,1)},clickAddSeasonalFoodLimitRule:function(t){this.formData.menu_seasonal_food_limit_rule.push({menu_type:"",days:0,food_tips_repeat_number:0})},clickDelectSeasonalFoodLimitRule:function(t,e){this.formData.menu_seasonal_food_limit_rule.splice(t,1)},nameFormat:function(t,e){if(t){var r=t.slice(0,e);return r+=t.length>e?"...":"",r}},submitHandler:function(){var t=this;this.$refs.formRules.validate((function(e){if(e){if(t.isLoading)return t.$message.error("请勿重复提交！");t.getMenuSettingInfoModify()}}))}}},k=w,E=(r("27dd"),r("2877")),x=Object(E["a"])(k,n,o,!1,null,null,null);e["default"]=x.exports},ca94:function(t,e,r){"use strict";r.r(e),r.d(e,"MENU_TYPE_LIST_DATA",(function(){return n}));var n=[{name:"周菜谱",value:"week",disabled:!1},{name:"月菜谱",value:"month",disabled:!1}]},d0dd:function(t,e,r){"use strict";r.d(e,"a",(function(){return n})),r.d(e,"b",(function(){return o})),r.d(e,"g",(function(){return a})),r.d(e,"c",(function(){return i})),r.d(e,"f",(function(){return u})),r.d(e,"d",(function(){return s})),r.d(e,"e",(function(){return l}));var n=function(t,e,r){if(e){var n=/^-?(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;n.test(e)?r():r(new Error("金额格式有误"))}else r(new Error("请输入金额"))},o=function(t,e,r){if(e){var n=/^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;n.test(e)?r():r(new Error("金额格式有误"))}else r()},a=function(t,e,r){if(!e)return r(new Error("手机号不能为空"));var n=/^1[3456789]\d{9}$/;n.test(e)?r():r(new Error("请输入正确手机号"))},i=function(t,e,r){if(!e)return r(new Error("金额有误"));var n=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;n.test(e)?r():r(new Error("金额格式有误"))},u=function(t,e,r){if(""===e)return r(new Error("不能为空"));var n=/^\d+$/;n.test(e)?r():r(new Error("请输入正确数字"))},s=function(t,e,r){if(""!==e){var n=/^(\+|-)?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;n.test(e)?r():r(new Error("金额格式有误"))}else r(new Error("请输入金额"))},l=function(t,e,r){var n=/^[\u4E00-\u9FA5\w-]+$/;n.test(e)?r():r(new Error("格式不正确，不能包含特殊字符"))}},e173:function(t,e,r){"use strict";r.d(e,"e",(function(){return o})),r.d(e,"l",(function(){return a})),r.d(e,"b",(function(){return i})),r.d(e,"a",(function(){return u})),r.d(e,"d",(function(){return s})),r.d(e,"k",(function(){return l})),r.d(e,"c",(function(){return c})),r.d(e,"h",(function(){return f})),r.d(e,"f",(function(){return d})),r.d(e,"g",(function(){return p})),r.d(e,"j",(function(){return m})),r.d(e,"i",(function(){return _}));var n=r("e925"),o=function(t,e,r){if(!e)return r();Object(n["c"])(e)?r():r(new Error("邮箱格式错误！"))},a=function(t,e,r){if(!e)return r();Object(n["g"])(e)?r():r(new Error("电话格式错误！"))},i=function(t,e,r){if(!e)return r();Object(n["i"])(e)?r():r(new Error("长度5到20位，只支持数字、大小写英文或下划线组合"))},u=function(t,e,r){if(!e)return r();Object(n["e"])(e)?r():r(new Error("密码长度8~20位，英文加数字"))},s=function(t,e,r){if(!e||"长期"===e)return r();if(Object(n["d"])(e)){var o=e.toString().trim().replace(" ","");if(8!==o.length)return r();o=o.slice(0,4)+"/"+o.slice(4,6)+"/"+o.slice(6,o.length);var a=new Date(o).getTime();if(isNaN(a))return r(new Error("请输入正确的日期"));var i=(new Date).getTime();a<i&&r(new Error("有效期必须大于当前日期")),r()}r(new Error("请输入yyyyMMdd格式的日期"))},l=function(t,e,r){if(!e)return r();Object(n["h"])(e)?r():r(new Error("电话/座机格式错误！"))},c=function(t,e,r){Object(n["m"])(e)?r():r(new Error("金额格式有误"))},f=function(t,e,r){if(""===e)return r(new Error("不能为空"));Object(n["b"])(e)?0===Number(e)?r(new Error("请输入大于0的数字")):r():r(new Error("请输入数字"))},d=function(t,e,r){e?0===Number(e)?r(new Error("请输入大于0的数字！")):Object(n["l"])(e)?r():r(new Error("最多2位数字可保留一位小数!")):r(new Error("请输入！"))},p=function(t,e,r){e?0===Number(e)?r(new Error("请输入大于0的数字！")):Object(n["n"])(e)?r():r(new Error("最多1位数字可保留3位小数!")):r(new Error("请输入！"))},m=function(t,e,r){e?Object(n["k"])(e)&&0!==Number(e)?r():r(new Error("格式错误")):r(new Error("请输入必填项"))},_=function(t,e,r){e?Object(n["d"])(e)?r():r(new Error("请输入数字")):r()}},e925:function(t,e,r){"use strict";r.d(e,"c",(function(){return n})),r.d(e,"g",(function(){return o})),r.d(e,"i",(function(){return a})),r.d(e,"e",(function(){return i})),r.d(e,"h",(function(){return u})),r.d(e,"f",(function(){return s})),r.d(e,"d",(function(){return l})),r.d(e,"m",(function(){return c})),r.d(e,"l",(function(){return f})),r.d(e,"n",(function(){return d})),r.d(e,"j",(function(){return p})),r.d(e,"b",(function(){return m})),r.d(e,"k",(function(){return _})),r.d(e,"a",(function(){return h}));var n=function(t){return/^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(t)},o=function(t){return/^([0-9]{3,4}-)?[0-9]{7,8}$/.test(t.toString())},a=function(t){return/^\w{5,20}$/.test(t)},i=function(t){return/^(?=.*[0-9])(?=.*[a-zA-Z])(.{8,20})$/.test(t)},u=function(t){return/(^([0-9]{3,4}-)?[0-9]{7,8}$)|(^1[3,4,5,6,7,8,9][0-9]{9}$)/.test(t.toString())},s=function(t){return/^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(t.toString())},l=function(t){return/\d/.test(t)},c=function(t){return/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(t)},f=function(t){return/^(([1-9]?[0-9])|([1-9]?[0-9]\.[1-9])|([1-9]?[1-9]\.[0]))$/.test(t)},d=function(t){return/^(([0-9])|([0-9]\.\d{1,3})|([1-9]\.[0]))$/.test(t)},p=function(t){return/^-?(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(t)},m=function(t){return/^[0-9]+$/.test(t)},_=function(t){return/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1}|[1-9][0-9]*\.\d{1})))$/.test(t)},h=function(t){return/^[a-zA-Z0-9]+$/.test(t)}}}]);