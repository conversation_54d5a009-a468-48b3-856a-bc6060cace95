(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-supervision-and-management-food-safety-management-SampleRecord","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-meal-report-MealPackageRule","view-merchant-order-RechargeOrder","view-merchant-order-RefundOrder","view-merchant-report-report-management-FoodSaleRanking","view-merchant-supervision-and-management-food-safety-management-compontents-EditReasonDialog","view-merchant-supervision-and-management-food-safety-management-constants"],{"1f0ea":function(e,t,r){},"3b5b":function(e,t,r){"use strict";r("1f0ea")},"60e2":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("el-dialog",{attrs:{visible:e.visible,title:e.title,width:e.width},on:{"update:visible":function(t){e.visible=t}},scopedSlots:e._u([{key:"footer",fn:function(){return[t("div",{staticClass:"dialog-footer"},[t("el-button",{on:{click:e.handlerClose}},[e._v(" 取消 ")]),t("el-button",{attrs:{type:"primary"},on:{click:e.handlerConfirm}},[e._v(" 保存 ")])],1)]},proxy:!0}])},[t("div",{staticClass:"dialog-content"},[t("div",{},[t("el-input",{staticStyle:{width:"360px"},attrs:{placeholder:"请输入",clearable:"",maxlength:"50","show-word-limit":"",type:"textarea"},model:{value:e.formData.reason,callback:function(t){e.$set(e.formData,"reason",t)},expression:"formData.reason"}})],1)])])},a=[],i=r("ed08");function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function l(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */l=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},u=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function f(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{f({},"")}catch(e){f=function(e,t,r){return e[t]=r}}function h(e,t,r,n){var i=t&&t.prototype instanceof b?t:b,o=Object.create(i.prototype),l=new T(n||[]);return a(o,"_invoke",{value:P(e,r,l)}),o}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=h;var p="suspendedStart",y="suspendedYield",m="executing",g="completed",v={};function b(){}function w(){}function _(){}var S={};f(S,u,(function(){return this}));var k=Object.getPrototypeOf,x=k&&k(k(N([])));x&&x!==r&&n.call(x,u)&&(S=x);var L=_.prototype=b.prototype=Object.create(S);function E(e){["next","throw","return"].forEach((function(t){f(e,t,(function(e){return this._invoke(t,e)}))}))}function O(e,t){function r(a,i,l,u){var c=d(e[a],e,i);if("throw"!==c.type){var s=c.arg,f=s.value;return f&&"object"==o(f)&&n.call(f,"__await")?t.resolve(f.__await).then((function(e){r("next",e,l,u)}),(function(e){r("throw",e,l,u)})):t.resolve(f).then((function(e){s.value=e,l(s)}),(function(e){return r("throw",e,l,u)}))}u(c.arg)}var i;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){r(e,n,t,a)}))}return i=i?i.then(a,a):a()}})}function P(t,r,n){var a=p;return function(i,o){if(a===m)throw Error("Generator is already running");if(a===g){if("throw"===i)throw o;return{value:e,done:!0}}for(n.method=i,n.arg=o;;){var l=n.delegate;if(l){var u=D(l,n);if(u){if(u===v)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===p)throw a=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=m;var c=d(t,r,n);if("normal"===c.type){if(a=n.done?g:y,c.arg===v)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(a=g,n.method="throw",n.arg=c.arg)}}}function D(t,r){var n=r.method,a=t.iterator[n];if(a===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,D(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=d(a,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var o=i.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,v):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function j(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function R(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function T(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(j,this),this.reset(!0)}function N(t){if(t||""===t){var r=t[u];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,i=function r(){for(;++a<t.length;)if(n.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(o(t)+" is not iterable")}return w.prototype=_,a(L,"constructor",{value:_,configurable:!0}),a(_,"constructor",{value:w,configurable:!0}),w.displayName=f(_,s,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,f(e,s,"GeneratorFunction")),e.prototype=Object.create(L),e},t.awrap=function(e){return{__await:e}},E(O.prototype),f(O.prototype,c,(function(){return this})),t.AsyncIterator=O,t.async=function(e,r,n,a,i){void 0===i&&(i=Promise);var o=new O(h(e,r,n,a),i);return t.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},E(L),f(L,s,"Generator"),f(L,u,(function(){return this})),f(L,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=N,T.prototype={constructor:T,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(R),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(n,a){return l.type="throw",l.arg=t,r.next=n,a&&(r.method="next",r.arg=e),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],l=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var u=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(u&&c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(u){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),R(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;R(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:N(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),v}},t}function u(e,t){return d(e)||h(e,t)||s(e,t)||c()}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function s(e,t){if(e){if("string"==typeof e)return f(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?f(e,t):void 0}}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function h(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,i,o,l=[],u=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);u=!0);}catch(e){c=!0,a=e}finally{try{if(!u&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw a}}return l}}function d(e){if(Array.isArray(e))return e}function p(e,t,r,n,a,i,o){try{var l=e[i](o),u=l.value}catch(e){return void r(e)}l.done?t(u):Promise.resolve(u).then(n,a)}function y(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){p(i,n,a,o,l,"next",e)}function l(e){p(i,n,a,o,l,"throw",e)}o(void 0)}))}}var m={name:"EditReasonDialog",props:{loading:Boolean,isshow:Boolean,title:{type:String,default:"修改原因"},width:{type:String,default:"400px"},dialogType:{type:String,default:"notEntry"},content:{type:String,default:""},id:{type:Number,default:-1}},data:function(){return{isLoading:!1,formData:{reason:""}}},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},watch:{visible:function(){this.visible&&(this.formData.reason=this.content)}},mounted:function(){},methods:{handlerClose:function(){this.visible=!1,this.$emit("close",!1)},handlerConfirm:function(){if(!this.formData.reason||!this.formData.reason.trim())return this.$message.error("请输入原因");this.modifyReason()},modifyReason:function(){var e=this;return y(l().mark((function t(){var r,n,a,o,c;return l().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return r={id:e.id,reason:e.formData.reason},e.isLoading=!0,t.next=4,Object(i["Z"])("notEntry"===e.dialogType?e.$apis.apiBackgroundFundSupervisionChannelCanteenManagementModifyNotEntryReasonPost(r):e.$apis.apiBackgroundFundSupervisionChannelCanteenManagementModifyNotReservedReasonPost(r));case 4:if(n=t.sent,a=u(n,2),o=a[0],c=a[1],e.isLoading=!1,!o){t.next=11;break}return t.abrupt("return");case 11:c&&0===c.code?(e.$message.success("修改成功"),e.$emit("confirm",e.formData),e.visible=!1):e.$message.error(c.msg||"修改失败");case 12:case"end":return t.stop()}}),t)})))()}}},g=m,v=(r("3b5b"),r("2877")),b=Object(v["a"])(g,n,a,!1,null,"6a960022",null);t["default"]=b.exports},"87ac":function(e,t,r){"use strict";var n=r("ed08"),a=r("2f62");function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function o(e,t){return f(e)||s(e,t)||u(e,t)||l()}function l(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(e,t){if(e){if("string"==typeof e)return c(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?c(e,t):void 0}}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function s(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,i,o,l=[],u=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);u=!0);}catch(e){c=!0,a=e}finally{try{if(!u&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw a}}return l}}function f(e){if(Array.isArray(e))return e}function h(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */h=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},l=o.iterator||"@@iterator",u=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function s(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,r){return e[t]=r}}function f(e,t,r,n){var i=t&&t.prototype instanceof b?t:b,o=Object.create(i.prototype),l=new T(n||[]);return a(o,"_invoke",{value:P(e,r,l)}),o}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var p="suspendedStart",y="suspendedYield",m="executing",g="completed",v={};function b(){}function w(){}function _(){}var S={};s(S,l,(function(){return this}));var k=Object.getPrototypeOf,x=k&&k(k(N([])));x&&x!==r&&n.call(x,l)&&(S=x);var L=_.prototype=b.prototype=Object.create(S);function E(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function O(e,t){function r(a,o,l,u){var c=d(e[a],e,o);if("throw"!==c.type){var s=c.arg,f=s.value;return f&&"object"==i(f)&&n.call(f,"__await")?t.resolve(f.__await).then((function(e){r("next",e,l,u)}),(function(e){r("throw",e,l,u)})):t.resolve(f).then((function(e){s.value=e,l(s)}),(function(e){return r("throw",e,l,u)}))}u(c.arg)}var o;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){r(e,n,t,a)}))}return o=o?o.then(a,a):a()}})}function P(t,r,n){var a=p;return function(i,o){if(a===m)throw Error("Generator is already running");if(a===g){if("throw"===i)throw o;return{value:e,done:!0}}for(n.method=i,n.arg=o;;){var l=n.delegate;if(l){var u=D(l,n);if(u){if(u===v)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===p)throw a=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=m;var c=d(t,r,n);if("normal"===c.type){if(a=n.done?g:y,c.arg===v)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(a=g,n.method="throw",n.arg=c.arg)}}}function D(t,r){var n=r.method,a=t.iterator[n];if(a===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,D(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=d(a,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var o=i.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,v):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function j(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function R(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function T(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(j,this),this.reset(!0)}function N(t){if(t||""===t){var r=t[l];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,o=function r(){for(;++a<t.length;)if(n.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(i(t)+" is not iterable")}return w.prototype=_,a(L,"constructor",{value:_,configurable:!0}),a(_,"constructor",{value:w,configurable:!0}),w.displayName=s(_,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,s(e,c,"GeneratorFunction")),e.prototype=Object.create(L),e},t.awrap=function(e){return{__await:e}},E(O.prototype),s(O.prototype,u,(function(){return this})),t.AsyncIterator=O,t.async=function(e,r,n,a,i){void 0===i&&(i=Promise);var o=new O(f(e,r,n,a),i);return t.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},E(L),s(L,c,"Generator"),s(L,l,(function(){return this})),s(L,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=N,T.prototype={constructor:T,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(R),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(n,a){return l.type="throw",l.arg=t,r.next=n,a&&(r.method="next",r.arg=e),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],l=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var u=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(u&&c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(u){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),R(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;R(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:N(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),v}},t}function d(e,t,r,n,a,i,o){try{var l=e[i](o),u=l.value}catch(e){return void r(e)}l.done?t(u):Promise.resolve(u).then(n,a)}function p(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){d(i,n,a,o,l,"next",e)}function l(e){d(i,n,a,o,l,"throw",e)}o(void 0)}))}}function y(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y(Object(r),!0).forEach((function(t){g(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function g(e,t,r){return(t=v(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function v(e){var t=b(e,"string");return"symbol"==i(t)?t:t+""}function b(e,t){if("object"!=i(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}t["a"]={data:function(){return{currentTableSetting:[],dialogPrintVisible:!1}},computed:m({},Object(a["c"])(["userInfo"])),mounted:function(){},methods:{initPrintSetting:function(){var e=this;return p(h().mark((function t(){var r,a;return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return r=[],t.next=3,e.getPrintSettingInfo();case 3:r=t.sent;try{r=r?Object(n["E"])(r):Object(n["E"])(e.tableSetting)}catch(i){r=Object(n["E"])(e.tableSetting)}r.length<12?(a=Object(n["m"])(e.tableSetting,r),a=e.deleteWidthKey(a),e.currentTableSetting=a):e.currentTableSetting=Object(n["m"])(e.tableSetting,r);case 6:case"end":return t.stop()}}),t)})))()},getPrintSettingInfo:function(){var e=this;return p(h().mark((function t(){var r,a,i,l,u;return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return r=null,t.next=3,Object(n["Z"])(e.$apis.apiBackgroundAdminAccountGetAccountPrintInfoPost({id:e.userInfo.account_id,print_key:e.printType}));case 3:if(a=t.sent,i=o(a,2),l=i[0],u=i[1],!l){t.next=10;break}return e.$message.error(l.message),t.abrupt("return",r);case 10:return 0===u.code?r=u.data:e.$message.error(u.msg),t.abrupt("return",r);case 12:case"end":return t.stop()}}),t)})))()},setPrintSettingInfo:function(e,t){var r=this;return p(h().mark((function a(){var i,l,u,c;return h().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,Object(n["Z"])(r.$apis.apiBackgroundAdminAccountSetAccountPrintInfoPost(m({id:r.userInfo.account_id,print_key:r.printType,print_list:e},t)));case 2:if(i=a.sent,l=o(i,2),u=l[0],c=l[1],!u){a.next=9;break}return r.$message.error(u.message),a.abrupt("return");case 9:0===c.code?r.$message.success("设置成功"):r.$message.error(c.msg);case 10:case"end":return a.stop()}}),a)})))()},openPrintSetting:function(){this.dialogPrintVisible=!0},confirmPrintDialog:function(e,t){var r=this;return p(h().mark((function a(){var i;return h().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(!e){a.next=6;break}return i=Object(n["f"])(e),i.length<12&&(i=r.deleteWidthKey(i)),a.next=5,r.setPrintSettingInfo(i,t);case 5:r.currentTableSetting=i;case 6:case"end":return a.stop()}}),a)})))()},deleteWidthKey:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"children";function r(e){e.map((function(e){e.width&&delete e.width,e.minWidth&&delete e.minWidth,e[t]&&e[t].length>0&&r(e[t])}))}return r(e),e},indexMethod:function(e){return(this.page-1)*this.pageSize+(e+1)},setCollectData:function(e){var t=this;this.collect.forEach((function(r){e.data.collect&&void 0!==e.data.collect[r.key]&&t.$set(r,"value",e.data.collect[r.key])}))},setSummaryData:function(e){var t=e.data.collect;t[this.tableSetting[0].key]="合计",this.tableData.length&&this.tableData.push(t)}},beforeDestroy:function(){}}},a1d9:function(e,t,r){},a7bb:function(e,t,r){"use strict";r("a1d9")},c9d9:function(e,t,r){"use strict";r.d(t,"a",(function(){return i})),r.d(t,"d",(function(){return o})),r.d(t,"b",(function(){return l})),r.d(t,"c",(function(){return u})),r.d(t,"e",(function(){return c})),r.d(t,"f",(function(){return s})),r.d(t,"g",(function(){return f}));var n=r("5a0c"),a=r("da92"),i=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],o=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],l={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},u=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],c=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],s=(n().subtract(7,"day").format("YYYY-MM-DD"),n().format("YYYY-MM-DD"),function(e){return e?"number"===typeof e?a["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:a["a"].divide(e,100).toFixed(2):"0.00"}),f=function(e){return a["a"].times(e,100)}},e49c:function(e,t,r){"use strict";r.r(t),r.d(t,"recentSevenDayTime",(function(){return f})),r.d(t,"recentSevenDay",(function(){return h})),r.d(t,"recentThreeDay",(function(){return d})),r.d(t,"recentCurrentDay",(function(){return p})),r.d(t,"SEARCH_SETTING_MORNING_INSPECTION_DETAILS",(function(){return y})),r.d(t,"SEARCH_SETTING_MORNING_INSPECTION_SUMMARY",(function(){return m})),r.d(t,"TABLE_HEAD_DATA_MORNING_INSPECTION_DETAILS",(function(){return g})),r.d(t,"TABLE_HEAD_DATA_MORNING_INSPECTION_SUMMARY",(function(){return v})),r.d(t,"SEARCH_SETTING_MORNING_SAMPLE_RECORD",(function(){return b})),r.d(t,"TABLE_HEAD_DATA_SAMPLE_RECORD",(function(){return w}));var n=r("5a0c"),a=r("c9d9");function i(e){return c(e)||u(e)||l(e)||o()}function o(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function l(e,t){if(e){if("string"==typeof e)return s(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?s(e,t):void 0}}function u(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function c(e){if(Array.isArray(e))return s(e)}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var f=[n().subtract(7,"day").format("YYYY-MM-DD HH:mm:ss"),n().format("YYYY-MM-DD HH:mm:ss")],h=[n().subtract(7,"day").format("YYYY-MM-DD"),n().format("YYYY-MM-DD")],d=[n().subtract(3,"day").format("YYYY-MM-DD"),n().format("YYYY-MM-DD")],p=[n().subtract(0,"day").format("YYYY-MM-DD"),n().format("YYYY-MM-DD")],y={select_time:{type:"daterange",label:"晨检时间",value:h,clearable:!1},org_id:{type:"organizationSelect",value:"",label:"组织名称",listNameKey:"name",listValueKey:"id",checkStrictly:!0,multiple:!1,collapseTags:!0,clearable:!0},name:{type:"input",value:"",label:"晨检人",dataList:[],placeholder:"请输入",maxlength:20,clearable:!0},check_result:{type:"select",value:"全部",label:"晨检结果",dataList:[{label:"全部",value:"全部"},{label:"合格",value:"0"},{label:"不合格",value:"1"}],clearable:!0}},m={select_time:{type:"daterange",label:"晨检时间",value:p,clearable:!1},org_id:{type:"organizationSelect",value:"",label:"组织名称",listNameKey:"name",listValueKey:"id",checkStrictly:!0,multiple:!1,collapseTags:!0,clearable:!0},name:{type:"input",value:"",label:"晨检人",dataList:[],placeholder:"请输入",maxlength:20,clearable:!0},is_check:{type:"select",value:"全部",label:"晨检状态",dataList:[{label:"全部",value:"全部"},{label:"已晨检",value:!0},{label:"未晨检",value:!1}],clearable:!0}},g=[{label:"晨检时间",key:"check_time"},{label:"组织名称",key:"org_name"},{label:"晨检人姓名",key:"name"},{label:"晨检结果",key:"check_result_alias",type:"slot",slotName:"checkResult"},{label:"体温",key:"temperature",type:"slot",slotName:"temperature"},{label:"手部识别结果",key:"hand_result"},{label:"是否有腹泻和咽喉炎症",key:"risk_type_one_alias"},{label:"健康证是否有效",key:"health_certificate_status_alias"},{label:"不合格原因",key:"remark"},{label:"图片",key:"images",type:"slot",slotName:"images"}],v=[{label:"晨检时间",key:"check_time"},{label:"晨检人姓名",key:"name"},{label:"晨检状态",key:"check_status",type:"slot",slotName:"checkStatus"},{label:"晨检次数",key:"count"},{label:"合格数",key:"qualified_count"},{label:"不合格数",key:"unqualified_count"}],b={date_type:{type:"select",value:"reserved_time",label:"",width:"120px",dataList:[{label:"留样时间",value:"reserved_time"},{label:"入柜时间",value:"entry_date"},{label:"离柜时间",value:"exit_time"}],clearable:!1},selecttime:{type:"daterange",filterable:!0,defaultExpandAll:!0,clearable:!1,label:"",value:d,placeholder:"请选择",dataList:[]},org_id:{type:"organizationSelect",value:"",label:"组织名称",listNameKey:"name",listValueKey:"id",checkStrictly:!0,multiple:!1,collapseTags:!0},meal_type:{type:"select",value:[],clearable:!0,label:"留样餐段",multiple:!0,collapseTags:!0,dataList:i(a["a"])},menu_type:{type:"select",value:"",label:"留样菜谱",placeholder:"请选择",dataList:[],clearable:!0},food_name:{type:"input",label:"菜品",clearable:!0,value:"",placeholder:"请输入",maxlength:20},reserved_user:{type:"input",value:"",label:"留样员",placeholder:"请输入"},sample_exit_user:{type:"input",value:"",label:"取样员",placeholder:"请输入"},sample_entry_user:{type:"input",value:"",label:"入柜员",placeholder:"请输入"},reserved_status:{type:"select",value:"all",label:"留样状态",dataList:[{label:"全部",value:"all"},{label:"已留样",value:"reserved"},{label:"未留样",value:"not_reserved"}]},entry_cupboard:{type:"select",value:"all",label:"入柜状态",dataList:[{label:"全部",value:"all"},{label:"是",value:!0},{label:"否",value:!1}]},entry_device_ids:{type:"select",value:[],label:"入柜设备",dataList:[],multiple:!0,placeholder:"请选择",clearable:!0,listNameKey:"device_name",listValueKey:"device_no"}},w=[{label:"留样时间",key:"reserved_time",width:"180px"},{label:"所属组织",key:"org_name"},{label:"所属菜谱",key:"menu_name"},{label:"餐段",key:"meal_type_alias"},{label:"菜品",key:"food_name",showTooltip:!0,width:"130px"},{label:"留样状态",key:"reserved_status_alias"},{label:"留样数量",key:"food_count"},{label:"留样重量",key:"food_weight",type:"slot",slotName:"foodWeight"},{label:"是否入柜",key:"entry_cupboard",type:"slot",slotName:"entryCupboard"},{label:"入柜时间",key:"entry_cupboard_time",width:"180px"},{label:"存放时长",key:"store_time",width:"180px"},{label:"离柜时间",key:"exit_cupboard_time",width:"180px"},{label:"入柜设备",key:"entry_device"},{label:"当前柜内温度",key:"temperature",type:"slot",slotName:"temperature"},{label:"留样员",key:"reserved_user_name",type:"slot",slotName:"reservedUserName",width:"180px",showTooltip:!0},{label:"入柜员",key:"sample_entry_user",type:"slot",slotName:"sampleEntryUser",width:"180px",showTooltip:!0},{label:"取样员",key:"sample_exit_user",type:"slot",slotName:"sampleExitUser",width:"180px",showTooltip:!0},{label:"未入柜原因",key:"not_entry_reason",type:"slot",slotName:"notEntryReason",showTooltip:!0,width:"180px"},{label:"留样照片",key:"operation",type:"slot",slotName:"operation",fixed:"right"}]},ea82:function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"AiRetentionInstrument-list container-wrapper"},[t("refresh-tool",{on:{refreshPage:e.refreshHandle}}),t("search-form",{ref:"searchRef",attrs:{"form-setting":e.searchFormSetting,"label-width":"105px",autoSearch:!1},on:{search:e.searchHandle}}),t("div",{staticClass:"table-wrapper"},[t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[e._v("数据列表")]),t("div",{staticClass:"align-r"},[t("button-icon",{attrs:{color:"origin",type:"export"},on:{click:e.gotoExport}},[e._v("导出")]),t("button-icon",{attrs:{color:"plain"},on:{click:e.gotoPrint}},[e._v("打印")]),t("button-icon",{attrs:{color:"plain"},on:{click:e.openPrintSetting}},[e._v("报表设置")])],1)]),t("div",{staticClass:"table-content"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","header-row-class-name":"ps-table-header-row"},on:{"selection-change":e.handleSelectionChange}},e._l(e.currentTableSetting,(function(r){return t("table-column",{key:r.key,attrs:{col:r},scopedSlots:e._u([{key:"entryCupboard",fn:function(t){var r=t.row;return[e._v(" "+e._s(r.entry_cupboard?"是":"否")+" ")]}},{key:"operation",fn:function(r){var n=r.row;return[t("div",{staticClass:"ps-origin pointer",on:{click:function(t){return e.viewPicDetail(n.food_image)}}},[e._v(" 查看 ")])]}},{key:"reservedUserName",fn:function(r){var n=r.row;return[t("div",{staticClass:"person-tag line-1",on:{click:function(t){return e.viewPicDetail(n.reserved_user_image)}}},[e._v(" "+e._s(n.reserved_user_name)+" ")])]}},{key:"sampleEntryUser",fn:function(r){var n=r.row;return[t("div",{staticClass:"person-tag line-1",on:{click:function(t){return e.handlerShowUser(n.sample_entry_user)}}},[e._v(" "+e._s(e.getNameByList(n.sample_entry_user))+" ")])]}},{key:"sampleExitUser",fn:function(r){var n=r.row;return[t("div",{staticClass:"person-tag line-1",on:{click:function(t){return e.handlerShowUser(n.sample_exit_user)}}},[e._v(" "+e._s(e.getNameByList(n.sample_exit_user))+" ")])]}},{key:"notReservedReason",fn:function(r){var n=r.row;return[t("div",{staticClass:"person-tag color-red line-1",on:{click:function(t){return e.handlerShowModifyReason(n.not_reserved_reason,"notReserved",n.id)}}},[e._v(" "+e._s(n.not_reserved_reason?n.not_reserved_reason:"编辑")+" ")])]}},{key:"notEntryReason",fn:function(r){var n=r.row;return[n.entry_cupboard?e._e():t("div",{staticClass:"person-tag color-red line-1",on:{click:function(t){return e.handlerShowModifyReason(n.not_entry_reason,"notEntry",n.id)}}},[e._v(" "+e._s(n.not_entry_reason?n.not_entry_reason:"编辑")+" ")])]}},{key:"temperature",fn:function(t){var r=t.row;return[e._v(" "+e._s(r.temperature?r.temperature+"°C":"")+" ")]}},{key:"foodWeight",fn:function(t){var r=t.row;return[e._v(" "+e._s(r.food_weight?r.food_weight+"g":"")+" ")]}}],null,!0)})})),1)],1),t("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[t("pagination",{attrs:{onPaginationChange:e.onPaginationChange,"current-page":e.currentPage,"page-size":e.pageSize,"page-sizes":[10,20,50,100,500],layout:"total, prev, pager, next, sizes, jumper",total:e.totalCount},on:{"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t},"update:pageSize":function(t){e.pageSize=t},"update:page-size":function(t){e.pageSize=t}}})],1)]),e.dialogPrintVisible?t("print-setting",{attrs:{extraParams:{printType:e.printType},tableSetting:e.tableSetting,defaultCheckedSetting:e.currentTableSetting,show:e.dialogPrintVisible},on:{"update:show":function(t){e.dialogPrintVisible=t},confirm:e.confirmPrintDialog}}):e._e(),t("el-image",{ref:"imagePre",attrs:{src:"","preview-src-list":e.imgUrlList}},[t("div",{attrs:{slot:"error"},slot:"error"})]),t("image-view-preview",{attrs:{isshow:e.isShowPreViewDialog,title:e.dialogTitle,picList:e.imgUrlList},on:{"update:isshow":function(t){e.isShowPreViewDialog=t},close:e.closePreviewDialog}}),t("edit-reason-dialog",{attrs:{isshow:e.editReasonDialogVisible,title:e.editReasonDialogTitle,dialogType:e.editReasonDialogType,content:e.editReasonDialogContent,id:e.editReasonDialogId},on:{"update:isshow":function(t){e.editReasonDialogVisible=t},close:e.closeEditReasonDialog,confirm:e.confirmEditReasonDialog}})],1)},a=[],i=r("ed08"),o=r("f63a"),l=r("87ac"),u=r("e49c"),c=r("74d4"),s=r("60e2");function f(e){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},f(e)}function h(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */h=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function c(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,r){return e[t]=r}}function s(e,t,r,n){var i=t&&t.prototype instanceof b?t:b,o=Object.create(i.prototype),l=new T(n||[]);return a(o,"_invoke",{value:P(e,r,l)}),o}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=s;var p="suspendedStart",y="suspendedYield",m="executing",g="completed",v={};function b(){}function w(){}function _(){}var S={};c(S,o,(function(){return this}));var k=Object.getPrototypeOf,x=k&&k(k(N([])));x&&x!==r&&n.call(x,o)&&(S=x);var L=_.prototype=b.prototype=Object.create(S);function E(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function O(e,t){function r(a,i,o,l){var u=d(e[a],e,i);if("throw"!==u.type){var c=u.arg,s=c.value;return s&&"object"==f(s)&&n.call(s,"__await")?t.resolve(s.__await).then((function(e){r("next",e,o,l)}),(function(e){r("throw",e,o,l)})):t.resolve(s).then((function(e){c.value=e,o(c)}),(function(e){return r("throw",e,o,l)}))}l(u.arg)}var i;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){r(e,n,t,a)}))}return i=i?i.then(a,a):a()}})}function P(t,r,n){var a=p;return function(i,o){if(a===m)throw Error("Generator is already running");if(a===g){if("throw"===i)throw o;return{value:e,done:!0}}for(n.method=i,n.arg=o;;){var l=n.delegate;if(l){var u=D(l,n);if(u){if(u===v)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===p)throw a=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=m;var c=d(t,r,n);if("normal"===c.type){if(a=n.done?g:y,c.arg===v)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(a=g,n.method="throw",n.arg=c.arg)}}}function D(t,r){var n=r.method,a=t.iterator[n];if(a===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,D(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=d(a,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var o=i.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,v):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function j(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function R(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function T(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(j,this),this.reset(!0)}function N(t){if(t||""===t){var r=t[o];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,i=function r(){for(;++a<t.length;)if(n.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(f(t)+" is not iterable")}return w.prototype=_,a(L,"constructor",{value:_,configurable:!0}),a(_,"constructor",{value:w,configurable:!0}),w.displayName=c(_,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,c(e,u,"GeneratorFunction")),e.prototype=Object.create(L),e},t.awrap=function(e){return{__await:e}},E(O.prototype),c(O.prototype,l,(function(){return this})),t.AsyncIterator=O,t.async=function(e,r,n,a,i){void 0===i&&(i=Promise);var o=new O(s(e,r,n,a),i);return t.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},E(L),c(L,u,"Generator"),c(L,o,(function(){return this})),c(L,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=N,T.prototype={constructor:T,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(R),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(n,a){return l.type="throw",l.arg=t,r.next=n,a&&(r.method="next",r.arg=e),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],l=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var u=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(u&&c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(u){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),R(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;R(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:N(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),v}},t}function d(e,t){return v(e)||g(e,t)||y(e,t)||p()}function p(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function y(e,t){if(e){if("string"==typeof e)return m(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?m(e,t):void 0}}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function g(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,i,o,l=[],u=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);u=!0);}catch(e){c=!0,a=e}finally{try{if(!u&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw a}}return l}}function v(e){if(Array.isArray(e))return e}function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function w(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach((function(t){_(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function _(e,t,r){return(t=S(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function S(e){var t=k(e,"string");return"symbol"==f(t)?t:t+""}function k(e,t){if("object"!=f(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=f(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function x(e,t,r,n,a,i,o){try{var l=e[i](o),u=l.value}catch(e){return void r(e)}l.done?t(u):Promise.resolve(u).then(n,a)}function L(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){x(i,n,a,o,l,"next",e)}function l(e){x(i,n,a,o,l,"throw",e)}o(void 0)}))}}var E={name:"SampleRecord",mixins:[o["a"],l["a"]],data:function(){return{isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[],tableSetting:Object(i["f"])(u["TABLE_HEAD_DATA_SAMPLE_RECORD"]),currentTableSetting:[],searchFormSetting:Object(i["f"])(u["SEARCH_SETTING_MORNING_SAMPLE_RECORD"]),printType:"SupervisionCanteenSampleRecord",imgUrlList:[],isShowPreViewDialog:!1,dialogTitle:"",editReasonDialogVisible:!1,editReasonDialogTitle:"原因",editReasonDialogType:"",editReasonDialogContent:"",editReasonDialogId:-1,menuList:[]}},created:function(){this.initLoad()},components:{ImageViewPreview:c["a"],EditReasonDialog:s["default"]},mounted:function(){},watch:{"searchFormSetting.selecttime":{handler:function(e){this.getMenuList()},deep:!0},"searchFormSetting.org_id":{handler:function(e){this.getMenuList()},deep:!0}},methods:{initLoad:function(){this.getSupervisionCanteenSampleRecord(),this.initPrintSetting(),this.getDevicelist(),this.getMenuList()},searchHandle:Object(i["d"])((function(e){e&&"search"===e&&(this.currentPage=1,this.getSupervisionCanteenSampleRecord())}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.currentPage=1,this.initLoad()},formatQueryParams:function(e){var t=this,r={},n=function(n){if(""!==e[n].value&&null!==e[n].value&&0!==e[n].value.length&&"all"!==e[n].value)if("entry_cupboard"===n)r.entry_cupboard=e[n].value;else if("meal_type"===n)r.meal_type=e[n].value;else if("menu_type"===n&&e[n].value){r.menu_id=e[n].value;var a=t.menuList.find((function(t){return t.id===e[n].value}));r.menu_type=a?a.menu_type:""}else if("selecttime"!==n){var i=e[n].value;i&&(r[n]=e[n].value)}else e[n].value&&e[n].value.length>0&&(r.start_date=e[n].value[0],r.end_date=e[n].value[1])};for(var a in e)n(a);return r},getSupervisionCanteenSampleRecord:function(){var e=this;return L(h().mark((function t(){var r,n,a,o,l;return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.isLoading){t.next=2;break}return t.abrupt("return");case 2:return e.isLoading=!0,r=w(w({},e.formatQueryParams(e.searchFormSetting)),{},{page:e.currentPage,page_size:e.pageSize}),t.next=6,Object(i["Z"])(e.$apis.apiBackgroundFundSupervisionChannelCanteenManagementFoodReservedSampleRecordPost(r));case 6:if(n=t.sent,a=d(n,2),o=a[0],l=a[1],e.isLoading=!1,!o){t.next=14;break}return e.$message.error(o.message),t.abrupt("return");case 14:0===l.code?(e.totalCount=l.data.count,e.tableData=l.data.results):e.$message.error(l.msg);case 15:case"end":return t.stop()}}),t)})))()},onPaginationChange:function(e){this.currentPage=e.current,this.pageSize=e.pageSize,this.getSupervisionCanteenSampleRecord()},handleSelectionChange:function(e){},gotoDetail:function(e){this.$router.push({name:"MerchantAiRetentionInstrumentDetail",query:{id:e.id,data:this.$encodeQuery(e)}})},gotoExport:function(){var e=w(w({},this.formatQueryParams(this.searchFormSetting)),{},{page:this.currentPage,page_size:this.totalCount});e.agreement_type&&(e.agreement_type=[e.agreement_type]);var t={type:this.printType,url:"apiBackgroundFundSupervisionChannelCanteenManagementFoodReservedSampleRecordExportPost",params:e};this.exportHandle(t)},gotoPrint:function(){var e=this.formatQueryParams(this.searchFormSetting),t=Object(i["f"])(this.tableSetting);t=t.filter((function(e){return"operation"!==e.key}));var r=this.$router.resolve({name:"Print",query:{print_date_state:!0,print_type:this.printType,print_title:"留样记录",result_key:"results",api:"apiBackgroundFundSupervisionChannelCanteenManagementFoodReservedSampleRecordPost",show_summary:!1,show_print_header_and_footer:!0,table_setting:JSON.stringify(t),current_table_setting:JSON.stringify(t),push_summary:!1,params:JSON.stringify(w(w({},e),{},{page:1,page_size:this.totalCount||10}))}}),n=r.href;window.open(n,"_blank")},viewPicDetail:function(e){if(this.dialogTitle="查看详情",e&&e.length>0){var t=e;this.$set(this,"imgUrlList",[t]),this.isShowPreViewDialog=!0}else this.$message.error("亲，没有图片喔！")},closePreviewDialog:function(){this.isShowPreViewDialog=!1},handlerShowModifyReason:function(e,t,r){this.editReasonDialogTitle="notEntry"===t?"未入柜原因":"未留样原因",this.editReasonDialogContent=e,this.editReasonDialogType=t,this.editReasonDialogId=r||-1,this.editReasonDialogVisible=!0},closeEditReasonDialog:function(){this.editReasonDialogVisible=!1},confirmEditReasonDialog:function(){this.editReasonDialogVisible=!1,this.getSupervisionCanteenSampleRecord()},getNameByList:function(e){if(!e||0===e.length)return"";if("object"===f(e)){var t=[];for(var r in e)t.push(e[r]);return t.join("、")}if(Array.isArray(e)&&e.length>0){var n=e.map((function(e){return e.name}));return n.join("、")}},handlerShowUser:function(e){var t=this;return L(h().mark((function r(){var n,a,i,o;return h().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(e&&0!==e.length){r.next=2;break}return r.abrupt("return","暂未图片");case 2:if("object"===f(e))for(o in n=[],a=0,i=Object.keys(e).length,e)t.getUserPicInfo(o,(function(e){if(e&&n.push(e),a++,a===i){if(!n||0===n.length)return t.$message.error("图片不存在");t.$set(t,"imgUrlList",n),t.isShowPreViewDialog=!0}}));case 3:case"end":return r.stop()}}),r)})))()},getUserPicInfo:function(e,t){var r=this;this.isLoading=!0,this.$apis.apiBackgroundFundSupervisionChannelCanteenManagementGetAccountFaceUrlPost({id:e}).then((function(e){r.isLoading=!1,e&&0===e.code&&t(e.data)})).catch((function(e){r.isLoading=!1}))},getDevicelist:function(){var e=this;this.isLoading=!0,this.$apis.apiBackgroundDeviceDeviceListPost({device_type:"LYG",page:1,page_size:9999}).then((function(t){if(e.isLoading=!1,t&&0===t.code){var r=t.data||{},n=r.results||[];e.$set(e.searchFormSetting.entry_device_ids,"dataList",n)}})).catch((function(t){e.isLoading=!1}))},getMenuList:function(){var e=this;this.isLoading=!0;var t={start_date:this.searchFormSetting.selecttime.value[0],end_date:this.searchFormSetting.selecttime.value[1]};this.searchFormSetting.org_id.value&&Reflect.set(t,"organization_ids",[this.searchFormSetting.org_id.value]),this.$apis.apiBackgroundStoreRetentionRecordMenuList(t).then((function(t){if(e.isLoading=!1,t&&0===t.code){var r=t.data||[];e.menuList=Object(i["f"])(r),r=r.map((function(e){return{label:e.name,value:e.id}})),e.$set(e.searchFormSetting.menu_type,"value",""),e.$set(e.searchFormSetting.menu_type,"dataList",r)}})).catch((function(t){e.isLoading=!1}))}}},O=E,P=(r("a7bb"),r("2877")),D=Object(P["a"])(O,n,a,!1,null,null,null);t["default"]=D.exports}}]);