(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-nutrition-rules-core-ModifyGuidanceRules"],{7587:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mdify-guidance-rules container-wrapper"},[e("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formGuidanceRules",attrs:{rules:t.formRuls,model:t.formData,size:"small"}},[e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("基本信息")])]),e("div",{staticClass:"ps-flex"},[e("div",{staticStyle:{padding:"0 20px"}},[e("el-form-item",{staticClass:"block-label",attrs:{label:"模型规则名称：",prop:"name"}},[e("el-input",{staticClass:"ps-input w-250",attrs:{placeholder:"模型规则名称"},model:{value:t.formData.name,callback:function(e){t.$set(t.formData,"name",e)},expression:"formData.name"}})],1)],1),e("div",{staticStyle:{padding:"0 20px"}},[e("el-form-item",{attrs:{label:"适用范围：",prop:"content"}},[e("organization-select",{staticClass:"search-item-w ps-input w-250",attrs:{placeholder:"请选择所属组织",isLazy:!1,multiple:!0,"check-strictly":!0,role:"super","append-to-body":!0,filterable:!1},model:{value:t.formData.visible_organization,callback:function(e){t.$set(t.formData,"visible_organization",e)},expression:"formData.visible_organization"}})],1)],1)]),e("div",{staticStyle:{"max-width":"510px",padding:"0 20px"}},[e("el-form-item",{attrs:{label:"说明：",prop:"remark"}},[e("el-input",{staticClass:"ps-input p-b-10",attrs:{type:"textarea",placeholder:"请输入说明内容",autosize:{minRows:4,maxRows:10},maxlength:"60","show-word-limit":""},model:{value:t.formData.remark,callback:function(e){t.$set(t.formData,"remark",e)},expression:"formData.remark"}})],1)],1)]),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("性别指导规则")])]),e("div",{staticStyle:{padding:"0 20px"}},[e("div",{staticClass:"ps-flex"},[e("el-form-item",{attrs:{label:"",prop:"comparison"}},[e("span",{staticClass:"p-r-10"},[t._v("男性人数占比")]),e("el-select",{staticClass:"ps-select w-100 p-r-10",attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select"},model:{value:t.formData.comparison,callback:function(e){t.$set(t.formData,"comparison",e)},expression:"formData.comparison"}},[e("el-option",{attrs:{label:"等于",value:"=="}}),e("el-option",{attrs:{label:"大于",value:">"}}),e("el-option",{attrs:{label:"小于",value:"<"}})],1),e("span",{staticClass:"m-r-10"},[t._v("女性人数占比，且超过")])],1),e("el-form-item",{attrs:{label:"",prop:"comparison_score"}},[e("el-input",{staticClass:"ps-input w-100 m-r-10",attrs:{placeholder:""},model:{value:t.formData.comparison_score,callback:function(e){t.$set(t.formData,"comparison_score",e)},expression:"formData.comparison_score"}}),e("span",[t._v("%")]),e("span",[t._v("，")])],1),e("el-form-item",{attrs:{label:"",prop:"right_gender"}},[e("span",{staticClass:"m-r-10"},[t._v("以")]),e("el-select",{staticClass:"ps-select m-r-10",staticStyle:{width:"120px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select"},model:{value:t.formData.right_gender,callback:function(e){t.$set(t.formData,"right_gender",e)},expression:"formData.right_gender"}},[e("el-option",{attrs:{label:"女性摄入量",value:"WOMEN"}}),e("el-option",{attrs:{label:"男性摄入量",value:"MAN"}})],1),e("span",[t._v("为准，")])],1),e("el-form-item",{attrs:{label:"",prop:"second_gender"}},[e("span",{staticClass:"m-r-10"},[t._v("反之以")]),e("el-select",{staticClass:"ps-select m-r-10",staticStyle:{width:"120px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select"},model:{value:t.formData.second_gender,callback:function(e){t.$set(t.formData,"second_gender",e)},expression:"formData.second_gender"}},[e("el-option",{attrs:{label:"女性摄入量",value:"WOMEN"}}),e("el-option",{attrs:{label:"男性摄入量",value:"MAN"}})],1),e("span",[t._v("为依据。")])],1)],1)])]),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("年龄指导计算规则")])]),e("div",{staticStyle:{padding:"0 20px"}},[e("el-form-item",{attrs:{label:"",prop:"preferred_calculation_method"}},[e("span",{staticClass:"p-r-10"},[t._v(" 根据DRIs 表(中国营养学会 2013)查年龄范围所对应的营养摄入量，通过 ")]),e("el-select",{staticClass:"ps-select w-100 p-r-10",attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select"},model:{value:t.formData.preferred_calculation_method,callback:function(e){t.$set(t.formData,"preferred_calculation_method",e)},expression:"formData.preferred_calculation_method"}},[e("el-option",{attrs:{label:"中间值",value:"median"}}),e("el-option",{attrs:{label:"均值",value:"average"}})],1),e("span",[t._v("的计算方式，计算实际营养建议摄入量。")])],1)],1)]),e("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[e("el-button",{staticStyle:{width:"120px"},on:{click:t.closeHandler}},[t._v("取消")]),e("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:t.submitHandler}},[t._v(" 保存 ")])],1)])],1)},a=[],o=r("cbfb"),i=r("ed08");function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function l(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */l=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function p(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{p({},"")}catch(t){p=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var o=e&&e.prototype instanceof b?e:b,i=Object.create(o.prototype),s=new N(n||[]);return a(i,"_invoke",{value:S(t,r,s)}),i}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var h="suspendedStart",m="suspendedYield",v="executing",y="completed",g={};function b(){}function _(){}function w(){}var x={};p(x,i,(function(){return this}));var L=Object.getPrototypeOf,C=L&&L(L(z([])));C&&C!==r&&n.call(C,i)&&(x=C);var k=w.prototype=b.prototype=Object.create(x);function D(t){["next","throw","return"].forEach((function(e){p(t,e,(function(t){return this._invoke(e,t)}))}))}function E(t,e){function r(a,o,i,l){var c=d(t[a],t,o);if("throw"!==c.type){var u=c.arg,p=u.value;return p&&"object"==s(p)&&n.call(p,"__await")?e.resolve(p.__await).then((function(t){r("next",t,i,l)}),(function(t){r("throw",t,i,l)})):e.resolve(p).then((function(t){u.value=t,i(u)}),(function(t){return r("throw",t,i,l)}))}l(c.arg)}var o;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return o=o?o.then(a,a):a()}})}function S(e,r,n){var a=h;return function(o,i){if(a===v)throw Error("Generator is already running");if(a===y){if("throw"===o)throw i;return{value:t,done:!0}}for(n.method=o,n.arg=i;;){var s=n.delegate;if(s){var l=$(s,n);if(l){if(l===g)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===h)throw a=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=v;var c=d(e,r,n);if("normal"===c.type){if(a=n.done?y:m,c.arg===g)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(a=y,n.method="throw",n.arg=c.arg)}}}function $(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,$(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var o=d(a,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,g;var i=o.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,g):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function j(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function N(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(j,this),this.reset(!0)}function z(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,o=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(s(e)+" is not iterable")}return _.prototype=w,a(k,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:_,configurable:!0}),_.displayName=p(w,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===_||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,p(t,u,"GeneratorFunction")),t.prototype=Object.create(k),t},e.awrap=function(t){return{__await:t}},D(E.prototype),p(E.prototype,c,(function(){return this})),e.AsyncIterator=E,e.async=function(t,r,n,a,o){void 0===o&&(o=Promise);var i=new E(f(t,r,n,a),o);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},D(k),p(k,u,"Generator"),p(k,i,(function(){return this})),p(k,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=z,N.prototype={constructor:N,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(O),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return s.type="throw",s.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],s=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),O(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;O(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:z(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),g}},e}function c(t,e){return h(t)||d(t,e)||p(t,e)||u()}function u(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function p(t,e){if(t){if("string"==typeof t)return f(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?f(t,e):void 0}}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function d(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,o,i,s=[],l=!0,c=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=o.call(r)).done)&&(s.push(n.value),s.length!==e);l=!0);}catch(t){c=!0,a=t}finally{try{if(!l&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(c)throw a}}return s}}function h(t){if(Array.isArray(t))return t}function m(t,e,r,n,a,o,i){try{var s=t[o](i),l=s.value}catch(t){return void r(t)}s.done?e(l):Promise.resolve(l).then(n,a)}function v(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var o=t.apply(e,r);function i(t){m(o,n,a,i,s,"next",t)}function s(t){m(o,n,a,i,s,"throw",t)}i(void 0)}))}}var y={data:function(){var t=function(t,e,r){if(""===e)return r(new Error("不能为空"));var n=/^\d+$/;e>100?r(new Error("比例不能大于100")):n.test(e)?r():r(new Error("请输入整数比例"))};return{type:"",isLoading:!1,formData:{name:"",remark:"",visible:"all",visible_organization:[],comparison_score:"",comparison:"==",right_gender:"MAN",second_gender:"WOMEN",preferred_calculation_method:"median"},formRuls:{comparison_score:[{required:!0,validator:t,trigger:"blur"}]}}},components:{OrganizationSelect:o["a"]},created:function(){this.type=this.$route.query.type,this.initLoad()},mounted:function(){},methods:{initLoad:function(){if("modify"===this.type){var t=this.$decodeQuery(this.$route.query.data);this.formData={id:t.id,name:t.name,remark:t.remark,visible:t.visible,visible_organization:t.visible_organization,comparison_score:t.setting_json.comparison_score,comparison:t.setting_json.comparison,right_gender:t.setting_json.right_gender,second_gender:t.setting_json.second_gender,preferred_calculation_method:t.setting_json.preferred_calculation_method}}},getRuleModifyDris:function(t){var e=this;return v(l().mark((function r(){var n,a,o,s;return l().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return t.visible=t.visible_organization.length?"part":"all",e.isLoading=!0,r.next=4,Object(i["Z"])(e.$apis.apiBackgroundHealthyAdminNutritionRuleModifyDrisPost(t));case 4:if(n=r.sent,a=c(n,2),o=a[0],s=a[1],e.isLoading=!1,!o){r.next=12;break}return e.$message.error(o.message),r.abrupt("return");case 12:0===s.code?(e.$message.success("修改成功"),e.$closeCurrentTab(e.$route.path)):e.$message.error(s.msg);case 13:case"end":return r.stop()}}),r)})))()},submitHandler:function(){var t=this;this.$refs.formGuidanceRules.validate((function(e){if(e){if(t.isLoading)return t.$message.error("请勿重复提交！");t.getRuleModifyDris(t.formData)}}))},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,r,n){"confirm"===e?t.$closeCurrentTab(t.$route.path):r.confirmButtonLoading||n()}}).then((function(t){})).catch((function(t){}))}}},g=y,b=(r("9e24"),r("2877")),_=Object(b["a"])(g,n,a,!1,null,null,null);e["default"]=_.exports},"96e9":function(t,e,r){},"9e24":function(t,e,r){"use strict";r("96e9")}}]);