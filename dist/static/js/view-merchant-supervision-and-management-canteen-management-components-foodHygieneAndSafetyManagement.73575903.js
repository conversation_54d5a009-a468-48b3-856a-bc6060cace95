(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-supervision-and-management-canteen-management-components-foodHygieneAndSafetyManagement"],{"22e5":function(e,t,a){"use strict";a("9847")},"33df":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e._self._c;return t("div",[t("div",{staticClass:"table-header"},[t("div",{staticClass:"header-left"},[t("div",{staticClass:"header-left-title"},[e._v("食品卫生安全管理")]),t("el-button",{staticClass:"ps-text m-r-10",attrs:{type:"text",size:"small"},on:{click:function(t){return e.showDrawer("add")}}},[e._v("添加")])],1)]),t("div",{staticClass:"table-content"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},e._l(e.tableSetting,(function(a,s){return t("table-column",{key:s,attrs:{col:a},scopedSlots:e._u([{key:"phone",fn:function(a){var s=a.row;return[t("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:s.phone,placement:"top"}},[t("div",[e._v(e._s(e.computedPhone(s.phone)))])])]}},{key:"img",fn:function(a){var s=a.row;return[t("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small",disabled:!s.face_url},on:{click:function(t){return e.handleClick(s)}}},[e._v("查看")])]}},{key:"operation",fn:function(a){var s=a.row;return[t("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.showDrawer("edit",s)}}},[e._v("编辑")]),t("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.deleteHandle(s)}}},[e._v("删除")])]}}],null,!0)})})),1),t("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[t("el-pagination",{staticClass:"ps-text",attrs:{"current-page":e.page,"page-sizes":[10,20,50,100,500],"page-size":e.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),t("div",{staticClass:"ps-el-drawer"},[t("el-drawer",{attrs:{title:"add"===e.selectType?"新建信息":"编辑信息",visible:e.drawerShow,"show-close":!1,size:"40%"}},[t("div",{staticClass:"p-20"},[t("el-form",{ref:"drawerFormRef",attrs:{model:e.drawerForm,"label-width":"100px","label-position":"right"}},[t("el-form-item",{attrs:{label:"姓名",prop:"name",rules:[{required:!0,message:"请输入姓名",trigger:["change","blur"]}]}},[t("el-input",{staticClass:"w-300",attrs:{placeholder:"请输入姓名，不超过20个字",maxlength:"20"},model:{value:e.drawerForm.name,callback:function(t){e.$set(e.drawerForm,"name",t)},expression:"drawerForm.name"}})],1),t("el-form-item",{attrs:{label:"联系电话",prop:"phone",rules:[{required:!0,message:"请输入联系电话",trigger:["change","blur"]},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:["change","blur"]}]}},[t("el-input",{staticClass:"w-300",attrs:{placeholder:"请输入联系电话，不超过11位",maxlength:"11"},model:{value:e.drawerForm.phone,callback:function(t){e.$set(e.drawerForm,"phone",t)},expression:"drawerForm.phone"}})],1),t("el-form-item",{attrs:{label:"所属岗位",prop:"post",rules:[{required:!0,message:"请输入岗位信息",trigger:["change","blur"]}]}},[t("el-input",{staticClass:"w-300",attrs:{placeholder:"请输入岗位信息，不超过20个字",maxlength:"20"},model:{value:e.drawerForm.post,callback:function(t){e.$set(e.drawerForm,"post",t)},expression:"drawerForm.post"}})],1),t("el-form-item",{attrs:{label:"个人照片",prop:"img",rules:[{required:!0,message:"请上传有效照片",trigger:["change","blur"]}]}},[t("div",{staticClass:"certification-info-show-tips"},[e._v(" 图片最大不超过2MB，仅支持jpg,png格式 ")]),t("el-upload",{directives:[{name:"loading",rawName:"v-loading",value:e.uploading,expression:"uploading"}],ref:"fileUpload",staticClass:"upload-w",attrs:{"element-loading-text":"上传中",action:e.serverUrl,"file-list":e.fileLists,"on-success":e.uploadSuccess,"before-upload":e.beforeFoodImgUpload,limit:1,multiple:!1,"show-file-list":!1,headers:e.headersOpts,accept:".jpeg,.jpg,.png,.bmp"}},[e.drawerForm.img?t("img",{staticStyle:{width:"100px",height:"100px"},attrs:{src:e.drawerForm.img}}):t("div",{staticStyle:{width:"100px",height:"100px",border:"1px dashed #C0C4CC","text-align":"center","line-height":"100px"}},[t("i",{staticClass:"el-icon-plus"})])])],1)],1),t("div",{staticClass:"ps-el-drawer-footer"},[t("el-button",{staticClass:"w-100",attrs:{size:"small"},on:{click:e.cancelHandle}},[e._v("取消")]),t("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:e.saveHandle}},[e._v("保存")])],1)],1)])],1),e.showImagePreview?t("el-image-viewer",{staticStyle:{"z-index":"3000"},attrs:{"url-list":e.previewList,"hide-on-click-modal":"",teleported:"","on-close":e.closePreview}}):e._e()],1)},r=[],i=a("ed08"),n=a("08a9"),o={components:{ElImageViewer:n["a"]},data:function(){return{isLoading:!1,tableData:[],tableSetting:[{label:"姓名",key:"name"},{label:"联系电话",key:"phone",type:"slot",slotName:"phone"},{label:"所属岗位",key:"job_title"},{label:"个人照片",key:"face_url",type:"slot",slotName:"img"},{label:"修改时间",key:"update_time"},{label:"操作人",key:"operator"},{label:"操作",key:"operation",type:"slot",slotName:"operation",width:"200"}],page:1,pageSize:10,totalCount:0,selectType:"",drawerShow:!1,drawerForm:{name:"",phone:"",post:"",img:""},uploading:!1,serverUrl:"/api/background/file/upload",uploadParams:{prefix:"incumbents",key:"incumbents"+(new Date).getTime()+Math.floor(150*Math.random())},fileLists:[],headersOpts:{TOKEN:Object(i["B"])()},showImagePreview:!1,previewList:[]}},computed:{computedPhone:function(){return function(e){return e.replace(/^(\d{3})\d{4}(\d{4})$/,"$1****$2")}}},created:function(){this.getDataList()},methods:{handleSizeChange:function(e){this.pageSize=e,this.getDataList()},handleCurrentChange:function(e){this.page=e,this.getDataList()},uploadSuccess:function(e,t,a){this.uploading=!1,e&&0===e.code?(this.fileLists=[],this.drawerForm.img=e.data.public_url):(this.drawerForm.img="",this.$message.error(e.msg))},beforeFoodImgUpload:function(e){var t=[".jpeg",".jpg",".png",".bmp"],a=e.size/1024/1024<=2;return t.includes(Object(i["A"])(e.name))?a?void(this.uploading=!0):(this.$message.error("上传图片大小不能超过 2MB!"),!1):(this.$message.error("上传图片只能是 JPG 格式或者PNG格式!"),!1)},getDataList:function(){var e=this;this.isLoading=!0;var t={page:this.page,page_size:this.pageSize};this.$apis.apiBackgroundFundSupervisionPublicityInfoSecurityAdminListPost(t).then((function(t){e.isLoading=!1,0===t.code?(e.tableData=Object(i["f"])(t.data.results),e.totalCount=t.data.count):e.$message.error(t.msg)}))},showDrawer:function(e,t){var a=this;this.selectType=e,"edit"===e?(this.selectId=t.id,this.drawerForm.phone=t.phone,this.drawerForm.img=t.face_url,this.drawerForm.name=t.name,this.drawerForm.post=t.job_title):(this.selectId="",this.drawerForm.phone="",this.drawerForm.img="",this.drawerForm.name="",this.drawerForm.post=""),this.drawerShow=!0,setTimeout((function(){a.$refs.drawerFormRef.clearValidate()}),10)},handleClick:function(e){this.previewList=[e.face_url],document.body.style.overflow="hidden",this.showImagePreview=!0},closePreview:function(){this.previewList=[],this.showImagePreview=!1,document.body.style.overflow="auto"},cancelHandle:function(){this.$refs.drawerFormRef.resetFields(),this.drawerShow=!1},saveHandle:function(){var e=this;this.$refs.drawerFormRef.validate((function(t){if(t){var a={id:"add"===e.selectType?void 0:e.selectId,phone:e.drawerForm.phone,face_url:e.drawerForm.img,name:e.drawerForm.name,job_title:e.drawerForm.post};"add"===e.selectType?e.addFoodHygieneAndSafetyManagement(a):e.editFoodHygieneAndSafetyManagement(a)}else e.$message.error("请确认表单内容填写是否正确")}))},addFoodHygieneAndSafetyManagement:function(e){var t=this;this.$apis.apiBackgroundFundSupervisionPublicityInfoAddSecurityAdminPost(e).then((function(e){0===e.code?t.$message.success("新增成功"):t.$message.error(e.msg),t.$refs.drawerFormRef.resetFields(),t.drawerShow=!1,t.getDataList()}))},editFoodHygieneAndSafetyManagement:function(e){var t=this;this.$apis.apiBackgroundFundSupervisionPublicityInfoModifySecurityAdminPost(e).then((function(e){0===e.code?t.$message.success("编辑成功"):t.$message.error(e.msg),t.$refs.drawerFormRef.resetFields(),t.drawerShow=!1,t.getDataList()}))},deleteHandle:function(e){var t=this;this.$confirm("确定要删除 ".concat(e.name," 的人员信息？删除后不可恢复，请谨慎操作。"),"提示",{distinguishCancelAndClose:!0,confirmButtonText:"确定",cancelButtonText:"取消"}).then((function(){t.$apis.apiBackgroundFundSupervisionPublicityInfoDeleteSecurityAdminPost({id:e.id}).then((function(e){0===e.code?t.$message.success("删除成功"):t.$message.error(e.msg),t.getDataList()}))})).catch((function(e){t.$message("已取消删除")}))}}},l=o,d=(a("22e5"),a("2877")),c=Object(d["a"])(l,s,r,!1,null,"2aad4385",null);t["default"]=c.exports},9847:function(e,t,a){}}]);