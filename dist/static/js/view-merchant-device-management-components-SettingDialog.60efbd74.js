(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-device-management-components-SettingDialog"],{"6abc":function(t,i,o){"use strict";o("f58d")},c1b7:function(t,i,o){"use strict";o.r(i);var e=function(){var t=this,i=t._self._c;return i("dialog-message",{attrs:{show:t.visible,title:t.title,loading:t.isLoading,customClass:"ps-dialog",width:"500px"},on:{"update:show":function(i){t.visible=i},"update:loading":function(i){t.isLoading=i},close:t.handleClose}},[i("el-form",{ref:"dialogForm",staticClass:"dialog-form",attrs:{model:t.dialogForm,"status-icon":"",rules:t.dialogFormRules,"label-width":"120px"},nativeOn:{submit:function(t){t.preventDefault()}}},["other"===t.type?i("div",[i("el-form-item",{attrs:{label:"适用组织",prop:"organizationIds"}},[i("organization-select",{staticClass:"search-item-w ps-input w-250",attrs:{placeholder:"请选择适用组织",isLazy:!1,multiple:!0,"check-strictly":!0,"append-to-body":!0},model:{value:t.dialogForm.organizationIds,callback:function(i){t.$set(t.dialogForm,"organizationIds",i)},expression:"dialogForm.organizationIds"}})],1)],1):t._e()]),i("template",{slot:"tool"},[i("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[i("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:t.isLoading},on:{click:t.clickCancleHandle}},[t._v(" 取消 ")]),i("el-button",{staticClass:"ps-btn",attrs:{disabled:t.isLoading,type:"primary"},on:{click:t.clickConfirmHandle}},[t._v(" 确定 ")])],1)])],2)},a=[],n=o("cbfb"),s={name:"SettingDialog",components:{OrganizationSelect:n["a"]},props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:""},isshow:Boolean,confirm:Function},data:function(){return{isLoading:!1,dialogForm:{organizationIds:[]},dialogFormRules:{organizationIds:[{required:!0,message:"请选择适用组织",trigger:"blur"}]}}},computed:{visible:{get:function(){return this.isshow},set:function(t){this.$emit("update:isshow",t)}}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){},clickConfirmHandle:function(){var t=this;this.$refs.dialogForm.validate((function(i){i&&"other"===t.type&&(t.$refs.dialogForm.clearValidate(),t.$emit("otherOrgConfirm",t.dialogForm.organizationIds))}))},clickCancleHandle:function(){this.visible=!1},handleClose:function(t){this.isLoading=!1,this.visible=!1,this.$refs.dialogForm.resetFields()}}},l=s,r=(o("6abc"),o("2877")),d=Object(r["a"])(l,e,a,!1,null,"4d41bf55",null);i["default"]=d.exports},f58d:function(t,i,o){}}]);