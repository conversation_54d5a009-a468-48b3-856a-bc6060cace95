(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-health-system-HealthSystem","view-merchant-health-system-components-AnalysisResult"],{2157:function(t,e,r){"use strict";r("c2fb")},"35aa":function(t,e,r){"use strict";r.r(e);var a=function(){var t=this,e=t._self._c;return e("div",{attrs:{id:"HealthSystem"}},[e("refresh-tool",{on:{refreshPage:t.refreshHandle}}),e("search-form",{ref:"searchRef",attrs:{loading:t.isLoading,"form-setting":t.searchForm},on:{search:t.searchHandle}}),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("数据列表")]),e("div",{staticClass:"align-r"},[e("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_healthy.healthy_info.follow_user"],expression:"['background_healthy.healthy_info.follow_user']"}],attrs:{color:"origin"},on:{click:function(e){return t.followAll(!0)}}},[t._v("批量关注")]),e("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_healthy.healthy_info.follow_user"],expression:"['background_healthy.healthy_info.follow_user']"}],attrs:{color:"origin"},on:{click:function(e){return t.followAll(!1)}}},[t._v("批量取关")]),e("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_healthy.healthy_info.add"],expression:"['background_healthy.healthy_info.add']"}],attrs:{color:"origin"},on:{click:function(e){return t.addOrEditHandle("add","")}}},[t._v("创建档案")])],1)]),e("div",{staticClass:"table-content"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableView",attrs:{data:t.tableData,stripe:"","header-row-class-name":"ps-table-header-row","row-key":"key"},on:{"selection-change":t.setSelectList}},t._l(t.currentTableSetting,(function(r){return e("table-column",{key:r.key,attrs:{col:r},scopedSlots:t._u([{key:"isFollow",fn:function(e){var r=e.row;return[t._v(" "+t._s(r.is_follow?"是":"否")+" ")]}},{key:"diseaseList",fn:function(r){var a=r.row;return t._l(a.disease_list,(function(r,a){return e("div",{key:a},[t._v(" "+t._s(r.name)+" ")])}))}},{key:"creatorName",fn:function(e){var r=e.row;return[t._v(" "+t._s(r.creator_name||"--")+" ")]}},{key:"operation",fn:function(r){var a=r.row;return[e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_healthy.healthy_info.modify"],expression:"['background_healthy.healthy_info.modify']"}],attrs:{type:"text",size:"small"},on:{click:function(e){return t.addOrEditHandle("edit",a)}}},[t._v("编辑")]),e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_healthy.healthy_info.follow_user"],expression:"['background_healthy.healthy_info.follow_user']"}],attrs:{type:"text",size:"small"},on:{click:function(e){return t.isFollow([a.user_id],!a.is_follow)}}},[t._v(t._s(a.is_follow?"取消关注":"关注"))]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.showDetail(a)}}},[t._v("详情")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.showAnalysisResult(a)}}},[t._v("营养分析")])]}}],null,!0)})})),1),e("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[e("el-pagination",{staticClass:"ps-text",attrs:{"current-page":t.page,"page-sizes":[10,20,50,100,500],"page-size":t.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:t.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1),e("CreateOrEditArchives",{attrs:{drawerType:t.createOrEditDrawerType,isShow:t.createOrEditDrawerShow,fileData:t.fileData,foodList:t.foodList,diseaseList:t.diseaseList},on:{"update:isShow":function(e){t.createOrEditDrawerShow=e},"update:is-show":function(e){t.createOrEditDrawerShow=e},refresh:t.refreshHandle}}),e("ArchivesDetail",{attrs:{isShow:t.archivesDetailShow,archivesDetailParams:t.archivesDetailParams},on:{"update:isShow":function(e){t.archivesDetailShow=e},"update:is-show":function(e){t.archivesDetailShow=e}}}),e("AnalysisResult",{attrs:{isShow:t.analysisResultShow,selectedId:t.selectedId},on:{"update:isShow":function(e){t.analysisResultShow=e},"update:is-show":function(e){t.analysisResultShow=e}}})],1)],1)},n=[],i=r("ed08"),o=r("9af0"),s=r("782d"),l=r("d85e");function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function u(t){return p(t)||h(t)||d(t)||f()}function f(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(t,e){if(t){if("string"==typeof t)return y(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?y(t,e):void 0}}function h(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function p(t){if(Array.isArray(t))return y(t)}function y(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,a=Array(e);r<e;r++)a[r]=t[r];return a}function v(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,a)}return r}function g(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?v(Object(r),!0).forEach((function(e){m(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function m(t,e,r){return(e=b(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function b(t){var e=w(t,"string");return"symbol"==c(e)?e:e+""}function w(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var a=r.call(t,e||"default");if("object"!=c(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function _(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */_=function(){return e};var t,e={},r=Object.prototype,a=r.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function f(t,e,r,a){var i=e&&e.prototype instanceof m?e:m,o=Object.create(i.prototype),s=new z(a||[]);return n(o,"_invoke",{value:C(t,r,s)}),o}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var h="suspendedStart",p="suspendedYield",y="executing",v="completed",g={};function m(){}function b(){}function w(){}var k={};u(k,o,(function(){return this}));var x=Object.getPrototypeOf,S=x&&x(x(A([])));S&&S!==r&&a.call(S,o)&&(k=S);var D=w.prototype=m.prototype=Object.create(k);function L(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function O(t,e){function r(n,i,o,s){var l=d(t[n],t,i);if("throw"!==l.type){var u=l.arg,f=u.value;return f&&"object"==c(f)&&a.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,o,s)}),(function(t){r("throw",t,o,s)})):e.resolve(f).then((function(t){u.value=t,o(u)}),(function(t){return r("throw",t,o,s)}))}s(l.arg)}var i;n(this,"_invoke",{value:function(t,a){function n(){return new e((function(e,n){r(t,a,e,n)}))}return i=i?i.then(n,n):n()}})}function C(e,r,a){var n=h;return function(i,o){if(n===y)throw Error("Generator is already running");if(n===v){if("throw"===i)throw o;return{value:t,done:!0}}for(a.method=i,a.arg=o;;){var s=a.delegate;if(s){var l=j(s,a);if(l){if(l===g)continue;return l}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(n===h)throw n=v,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);n=y;var c=d(e,r,a);if("normal"===c.type){if(n=a.done?v:p,c.arg===g)continue;return{value:c.arg,done:a.done}}"throw"===c.type&&(n=v,a.method="throw",a.arg=c.arg)}}}function j(e,r){var a=r.method,n=e.iterator[a];if(n===t)return r.delegate=null,"throw"===a&&e.iterator.return&&(r.method="return",r.arg=t,j(e,r),"throw"===r.method)||"return"!==a&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+a+"' method")),g;var i=d(n,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,g;var o=i.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,g):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function T(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function z(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(T,this),this.reset(!0)}function A(e){if(e||""===e){var r=e[o];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,i=function r(){for(;++n<e.length;)if(a.call(e,n))return r.value=e[n],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(c(e)+" is not iterable")}return b.prototype=w,n(D,"constructor",{value:w,configurable:!0}),n(w,"constructor",{value:b,configurable:!0}),b.displayName=u(w,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,u(t,l,"GeneratorFunction")),t.prototype=Object.create(D),t},e.awrap=function(t){return{__await:t}},L(O.prototype),u(O.prototype,s,(function(){return this})),e.AsyncIterator=O,e.async=function(t,r,a,n,i){void 0===i&&(i=Promise);var o=new O(f(t,r,a,n),i);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},L(D),u(D,l,"Generator"),u(D,o,(function(){return this})),u(D,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var a in e)r.push(a);return r.reverse(),function t(){for(;r.length;){var a=r.pop();if(a in e)return t.value=a,t.done=!1,t}return t.done=!0,t}},e.values=A,z.prototype={constructor:z,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(E),!e)for(var r in this)"t"===r.charAt(0)&&a.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function n(a,n){return s.type="throw",s.arg=e,r.next=a,n&&(r.method="next",r.arg=t),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var l=a.call(o,"catchLoc"),c=a.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,g):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),E(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var a=r.completion;if("throw"===a.type){var n=a.arg;E(r)}return n}}throw Error("illegal catch attempt")},delegateYield:function(e,r,a){return this.delegate={iterator:A(e),resultName:r,nextLoc:a},"next"===this.method&&(this.arg=t),g}},e}function k(t,e,r,a,n,i,o){try{var s=t[i](o),l=s.value}catch(t){return void r(t)}s.done?e(l):Promise.resolve(l).then(a,n)}function x(t){return function(){var e=this,r=arguments;return new Promise((function(a,n){var i=t.apply(e,r);function o(t){k(i,a,n,o,s,"next",t)}function s(t){k(i,a,n,o,s,"throw",t)}o(void 0)}))}}var S={name:"HealthSystem",components:{CreateOrEditArchives:o["default"],ArchivesDetail:s["default"],AnalysisResult:l["default"]},data:function(){return{isLoading:!1,searchForm:{select_time:{type:"datetimerange",label:"创建时间",value:[],clearable:!1},name:{label:"姓名",type:"input",value:"",placeholder:"请输入",labelWidth:"100px"},person_no:{label:"人员编号",type:"input",value:"",placeholder:"请输入",labelWidth:"100px"},phone:{label:"手机号",type:"input",value:"",placeholder:"请输入",labelWidth:"100px"},is_follow:{label:"重点关注",type:"select",value:"",placeholder:"全部",clearable:!0,dataList:[{value:"",label:"全部"},{value:1,label:"是"},{value:0,label:"否"}]}},tableData:[],currentTableSetting:[{label:"",type:"selection",reserveSelection:!0},{label:"人员编号",key:"person_no"},{label:"姓名",key:"name"},{label:"手机号",key:"phone"},{label:"性别",key:"gender"},{label:"个人特征",key:"disease_list",type:"slot",slotName:"diseaseList"},{label:"重点关注",key:"is_follow",type:"slot",slotName:"isFollow"},{label:"创建时间",key:"create_time"},{label:"操作人",key:"creator_name ",type:"slot",slotName:"creatorName"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"240"}],createOrEditDrawerType:"",createOrEditDrawerShow:!1,archivesDetailShow:!1,analysisResultShow:!1,selectedList:[],fileData:{},archivesDetailParams:{},selectedId:0,diseaseList:[],foodList:[],page:1,pageSize:10,totalCount:0}},created:function(){this.initLoad()},methods:{initLoad:function(){var t=this;return x(_().mark((function e(){return _().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.getDataList();case 3:return e.next=5,t.getDiseaseList();case 5:return e.next=7,t.getFoodList();case 7:t.isLoading=!1;case 8:case"end":return e.stop()}}),e)})))()},searchHandle:Object(i["d"])((function(t){t&&"search"===t&&(this.page=1,this.isLoading=!0,this.initLoad())}),300),refreshHandle:function(){this.page=1,this.initLoad()},getDiseaseList:function(){var t=this;this.$apis.apiBackgroundHealthyDiseaseListPost({page_size:9999}).then((function(e){0===e.code?t.diseaseList=Object(i["f"])(e.data.results)||[]:t.$message.error(e.msg)}))},getFoodList:function(){var t=this,e={page:1,page_size:9999};this.$apis.apiBackgroundHealthyHealthyInfoIngredientTabooListPost(e).then((function(e){0===e.code?t.foodList=Object(i["f"])(e.data)||[]:t.$message.error(e.msg)}))},getDataList:function(){var t=this,e=g({page:this.page,page_size:this.pageSize},this.formatQueryParams(this.searchForm));this.$apis.apiBackgroundHealthyHealthyInfoListPost(e).then((function(e){0===e.code?(t.tableData=e.data.results,t.totalCount=e.data.count,t.isLoading=!1):t.$message.error(e.msg)}))},formatQueryParams:function(t){var e={};for(var r in t)"select_time"===r&&t[r].value.length?(e.start_time=t[r].value[0],e.end_time=t[r].value[1]):e[r]=t[r].value||void 0;return e},setSelectList:function(t){var e=t.map((function(t){return t.user_id}));this.selectedList=this.removeDuplicates(e)},removeDuplicates:function(t){return u(new Set(t))},followAll:function(t){if(!this.selectedList.length)return this.$message.error("请先选择档案");this.isFollow(this.selectedList,t)},isFollow:function(t,e){var r=this;this.$apis.apiBackgroundHealthyHealthyInfoFollowUserPost({user_ids:t,is_follow:e}).then((function(t){0===t.code?(r.isLoading=!0,r.$message.success(e?"关注成功":"取关成功"),r.$refs.tableView.clearSelection(),r.getDataList(),r.isLoading=!1):r.$message.error(t.msg)}))},showDetail:function(t){this.archivesDetailParams={id:t.id,user_id:t.user_id},this.archivesDetailShow=!0},addOrEditHandle:function(t,e){if(this.createOrEditDrawerType=t,"add"===t){var r={name:"",person_no:"",phone:"",card_info_id:"",gender:"",height:"",weight:"",targe_weight:"",birthday:"",job:"",disease:"",taboo_food:"",is_follow:""};this.fileData=Object(i["f"])(r)}else this.fileData=Object(i["f"])(e);this.createOrEditDrawerShow=!0},showAnalysisResult:function(t){this.selectedId=t.id,this.analysisResultShow=!0},handleSizeChange:function(t){this.pageSize=t,this.isLoading=!0,this.getDataList()},handleCurrentChange:function(t){this.page=t,this.isLoading=!0,this.getDataList()}}},D=S,L=r("2877"),O=Object(L["a"])(D,a,n,!1,null,"322ed38b",null);e["default"]=O.exports},c2fb:function(t,e,r){},d85e:function(t,e,r){"use strict";r.r(e);var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"ps-el-drawer"},[e("el-drawer",{attrs:{title:"营养分析",visible:t.visible,"show-close":!1,size:"70%"}},[e("div",{staticClass:"drawer-content p-20 flex-col"},[e("div",{staticClass:"flex-b-c m-b-20"},[e("div",{staticClass:"flex-b-c"},[e("el-button",{class:["w-100","day"===t.type?"ps-origin-btn":""],attrs:{size:"small"},on:{click:function(e){t.type="day"}}},[t._v("按天")]),e("el-button",{class:["w-100","week"===t.type?"ps-origin-btn":""],attrs:{size:"small"},on:{click:function(e){t.type="week"}}},[t._v("按自然周")])],1),e("special-date-picker",{attrs:{selectType:t.type,selectedId:t.selectedId,showRecord:t.visible},on:{setHealthyInfo:t.setHealthyInfo}})],1),"day"===t.type?e("div",{staticClass:"drawer-content-list"},[e("div",{staticClass:"f-w-700 font-size-18 m-b-10"},[t._v("食物清单（"+t._s(this.foodList.length)+"）")]),e("div",{staticClass:"bg-grey flex-center"},[e("div",{staticClass:"drawer-content-list-box w-100-p"},t._l(t.foodList,(function(r,a){return e("div",{key:a,staticClass:"drawer-content-list-box-item flex-b-c"},[e("div",[t._v(t._s(r.name))]),e("div",[t._v(t._s(r.weight)+"g")])])})),0)])]):t._e(),e("div",{staticClass:"drawer-content-layout"},[e("div",{staticClass:"f-w-700 font-size-18 m-b-10"},[t._v("饮食结构")]),e("div",{staticClass:"bg-grey flex-center"},[e("div",{staticClass:"drawer-content-layout-box w-100-p"},t._l(t.dietData,(function(r,a){return e("div",{key:a,staticClass:"drawer-content-layout-box-item flex-b-c"},[e("div",[t._v(t._s(r.text)+"："+t._s(r.value)+"g / "+t._s(r.range[0])+"g - "+t._s(r.range[1])+"g")]),"none"!==t.computedStatus(r)?e("div",[e("i",{class:["up"===t.computedStatus(r)?"el-icon-top":"el-icon-bottom"],style:"up"===t.computedStatus(r)?{color:"#D9001B"}:{color:"#F59A23"}})]):t._e()])})),0)])]),e("div",{staticClass:"drawer-content-analysis"},[e("div",{staticClass:"f-w-700 font-size-18 m-b-10"},[t._v("总能量分析")]),e("div",{staticClass:"bg-grey flex-center"},[e("div",{staticClass:"drawer-content-analysis-box w-100-p"},t._l(t.analysisData,(function(r,a){return e("div",{key:a,staticClass:"drawer-content-analysis-box-item flex-b-c"},[e("div",[t._v(t._s(r.text)+"："+t._s(r.value)+"kcal / "+t._s(r.range[0])+"kcal - "+t._s(r.range[1])+"kcal")]),"none"!==t.computedStatus(r)?e("div",[e("i",{class:["up"===t.computedStatus(r)?"el-icon-top":"el-icon-bottom"],style:"up"===t.computedStatus(r)?{color:"#D9001B"}:{color:"#F59A23"}})]):t._e()])})),0)])]),e("div",{staticClass:"m-b-20"},[e("div",{staticClass:"f-w-700 font-size-18 m-b-10"},[t._v("能量来源")]),e("div",{staticClass:"bg-grey"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableView",attrs:{data:t.energyTableData,stripe:"","header-row-class-name":"ps-table-header-row"}},t._l(t.energyTableSetting,(function(r){return e("table-column",{key:r.key,attrs:{col:r},scopedSlots:t._u([{key:"value",fn:function(r){var a=r.row;return[e("div",{staticClass:"text-right"},[e("span",{staticClass:"m-r-10"},[t._v(t._s(a.value)+t._s(a.unit))]),e("i",{class:["up"===t.computedStatus(a)?"el-icon-top":"el-icon-bottom"],style:"up"===t.computedStatus(a)?{color:"#D9001B"}:{color:"#F59A23"}})])]}},{key:"value_rate",fn:function(e){var r=e.row;return[t._v(" "+t._s(r.range[0])+" - "+t._s(r.range[1])+"g ")]}},{key:"range",fn:function(e){var r=e.row;return[t._v(" "+t._s(r.value_rate)+"% ")]}},{key:"range_rate",fn:function(e){var r=e.row;return[t._v(" "+t._s(r.range_rate[0])+" - "+t._s(r.range_rate[1])+"% ")]}}],null,!0)})})),1)],1)]),e("div",{staticClass:"m-b-20"},[e("div",{staticClass:"f-w-700 font-size-18 m-b-10"},[t._v("营养素分析")]),e("div",{staticClass:"bg-grey drawer-content-table"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableView",attrs:{data:t.nutrientTableData[0],stripe:"","header-row-class-name":"ps-table-header-row"}},t._l(t.nutrientTableSetting,(function(r,a){return e("table-column",{key:a,attrs:{col:r},scopedSlots:t._u([{key:"value",fn:function(r){var a=r.row;return[e("div",{staticClass:"text-right"},[e("span",{staticClass:"m-r-10"},[t._v(t._s(a.value)+t._s(a.unit))]),e("i",{class:["up"===t.computedStatus(a)?"el-icon-top":"el-icon-bottom"],style:"up"===t.computedStatus(a)?{color:"#D9001B"}:{color:"#F59A23"}})])]}},{key:"range",fn:function(e){var r=e.row;return[t._v(" "+t._s(r.range[0])+" - "+t._s(r.range[1])+t._s(r.unit)+" ")]}}],null,!0)})})),1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableView",attrs:{data:t.nutrientTableData[1],stripe:"","header-row-class-name":"ps-table-header-row"}},t._l(t.nutrientTableSetting,(function(r,a){return e("table-column",{key:a,attrs:{col:r},scopedSlots:t._u([{key:"value",fn:function(r){var a=r.row;return[e("div",{staticClass:"text-right"},[e("span",{staticClass:"m-r-10"},[t._v(t._s(a.value)+t._s(a.unit))]),e("i",{class:["up"===t.computedStatus(a)?"el-icon-top":"el-icon-bottom"],style:"up"===t.computedStatus(a)?{color:"#D9001B"}:{color:"#F59A23"}})])]}},{key:"range",fn:function(e){var r=e.row;return[t._v(" "+t._s(r.range[0])+" - "+t._s(r.range[1])+t._s(r.unit)+" ")]}}],null,!0)})})),1)],1)]),e("div",{staticClass:"m-b-20"},[e("div",{staticClass:"f-w-700 font-size-18 m-b-10"},[t._v("饮食分析")]),e("div",{staticClass:"bg-grey"},t._l(t.nutritionAnalyzeTextData,(function(r,a){return e("div",{key:a},[t._v(" "+t._s(a+1)+". "+t._s(r)+" ")])})),0)]),"week"===t.type?e("div",{staticClass:"m-b-20"},[e("div",{staticClass:"f-w-700 font-size-18 m-b-10"},[t._v("指导建议")]),e("div",{staticClass:"bg-grey"},t._l(t.suggestionTextData,(function(r,a){return e("div",{key:a},[t._v(" "+t._s(a+1)+". "+t._s(r)+" ")])})),0)]):t._e(),e("div",{staticClass:"ps-el-drawer-footer ps-flex-align-c flex-align-c"},[e("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:t.closeDrawer}},[t._v("关闭")])],1)])])],1)},n=[],i=r("5a0c"),o=r.n(i),s=r("af32"),l=r("ed08");function c(t){return h(t)||d(t)||f(t)||u()}function u(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(t,e){if(t){if("string"==typeof t)return p(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?p(t,e):void 0}}function d(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function h(t){if(Array.isArray(t))return p(t)}function p(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,a=Array(e);r<e;r++)a[r]=t[r];return a}var y={components:{SpecialDatePicker:s["default"]},props:{isShow:Boolean,selectedId:Number},data:function(){return{isLoading:!1,type:"day",foodList:[],dietData:[],analysisData:[],energyTableData:[],nutrientTableData:[],nutritionAnalyzeTextData:[],suggestionTextData:[],energyTableSetting:[{label:"三大供能营养素",align:"right",key:"text"},{label:"摄入量",align:"right",key:"value",type:"slot",slotName:"value"},{label:"推荐量",align:"right",key:"value_rate",type:"slot",slotName:"value_rate"},{label:"摄入供能比",align:"right",key:"range",type:"slot",slotName:"range"},{label:"推荐供能比",align:"right",key:"range_rate",type:"slot",slotName:"range_rate"}],nutrientTableSetting:[{label:"营养素名称",align:"right",key:"text"},{label:"摄入量",align:"right",key:"value",type:"slot",slotName:"value"},{label:"推荐量",align:"right",key:"range",type:"slot",slotName:"range"}],pickerOptions:this.disableDate()}},computed:{visible:{get:function(){return this.isShow},set:function(t){this.$emit("update:isShow",t)}},computedWeek:function(){var t=this;return function(e){var r=o()(e).startOf("week").format("YYYY-MM-DD"),a=o()(e).endOf("week").format("YYYY-MM-DD");return"day"===t.type?o()(e).format("YYYY-MM-DD"):"".concat(r," 至 ").concat(a)}},computedStatus:function(){return function(t){var e=t.value,r=t.range,a="";return a=e<r[0]?"down":e>r[1]?"up":"none",a}}},methods:{disableDate:function(){var t=this;return{disabledDate:function(e){return"week"===t.type?e.getTime()>new Date(o()().endOf("week")).getTime():e.getTime()>Date.now()}}},setHealthyInfo:function(t){if("day"===this.type){if(this.foodList=t?Object(l["f"])(t.food_data):[],this.dietData=t?Object(l["f"])(t.food_diversity.data):[],this.analysisData=t?Object(l["f"])(t.energy_kcal_data):[],this.energyTableData=t?Object(l["f"])(t.three_nutrition):[],t.total_nutrition&&0!==t.total_nutrition.length){var e=t.total_nutrition.filter((function(t,e){return e%2===0})),r=t.total_nutrition.filter((function(t,e){return e%2!==0}));this.nutrientTableData=[e,r]}else this.nutrientTableData=[];var a=[];t&&t.nutrition_analyze_text&&(a=[].concat(c(t.nutrition_analyze_text.meal_type||[]),c(t.nutrition_analyze_text.category||[]),c(t.nutrition_analyze_text.full||[]))),this.nutritionAnalyzeTextData=t?Object(l["f"])(a):[]}else{if(this.dietData=t?Object(l["f"])(t.food_diversity.data):[],this.analysisData=t?Object(l["f"])(t.energy_kcal_data):[],this.energyTableData=t?Object(l["f"])(t.three_nutrition):[],t&&t.total_nutrition&&0!==t.total_nutrition.length){var n=t.total_nutrition.filter((function(t,e){return e%2===0})),i=t.total_nutrition.filter((function(t,e){return e%2!==0}));this.nutrientTableData=[n,i]}else this.nutrientTableData=[];var o=[];t&&t.nutrition_analyze_text&&(o=[].concat(c(t.nutrition_analyze_text.category||[]),c(t.nutrition_analyze_text.full||[]))),this.nutritionAnalyzeTextData=t?Object(l["f"])(o):[],this.suggestionTextData=t?Object(l["f"])(t.nutrition_guid_text):[]}},closeDrawer:function(){this.type="day",this.visible=!1}}},v=y,g=(r("2157"),r("2877")),m=Object(g["a"])(v,a,n,!1,null,"40b6775c",null);e["default"]=m.exports}}]);