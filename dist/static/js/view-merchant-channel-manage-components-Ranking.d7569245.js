(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-channel-manage-components-Ranking"],{"2cb4":function(t,a,e){"use strict";e.r(a);var n=function(){var t=this,a=t._self._c;return a("div",{staticClass:"ranking-container flex"},[a("div",{staticClass:"food-top ranking-item m-t-20 m-b-20"},[a("div",{staticClass:"top flex"},[a("div",{staticClass:"title"},[t._v("登录数排行TOP20")]),a("div",{staticClass:"m-l-10"},[a("el-select",{staticClass:"w-150",attrs:{placeholder:"请选择"},on:{change:function(a){t.changeOrgsChoose("login",a)}},model:{value:t.orgsChooseIdLogin,callback:function(a){t.orgsChooseIdLogin=a},expression:"orgsChooseIdLogin"}},t._l(t.orgsChooseList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.name,value:t.value}})})),1)],1)]),a("div",{staticClass:"m-t-10",staticStyle:{width:"200px"}}),a("div",{staticClass:"ranking-list"},[t._l(t.loginRankingData,(function(e,n){return a("div",{key:n,staticClass:"top-item flex"},[a("div",{staticClass:"left flex"},[a("div",{staticClass:"count",class:t.foodTOPThreeClass(n+1)},[t._v(t._s(n+1))]),a("div",{staticClass:"food"},[t._v(t._s("1"==t.orgsChooseIdLogin?e.org_name:e.channel_name))])]),a("div",{staticClass:"right"},[t._v(t._s(t._f("formatPriceTo3")(e.total_login_count))+"人")])])})),t.loginRankingData.length?t._e():a("div",{staticClass:"flex flex-center empty-text"},[t._v(" 暂无更多 ")])],2)]),a("div",{staticClass:"food-top ranking-item m-l-20 m-t-20 m-b-20"},[a("div",{staticClass:"top flex"},[a("div",{staticClass:"title"},[t._v("注册数排行TOP20")]),a("div",{staticClass:"m-l-10"},[a("el-select",{staticClass:"w-150",attrs:{placeholder:"请选择"},on:{change:function(a){t.changeOrgsChoose("register",a)}},model:{value:t.orgsChooseIdRegister,callback:function(a){t.orgsChooseIdRegister=a},expression:"orgsChooseIdRegister"}},t._l(t.orgsChooseList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.name,value:t.value}})})),1)],1)]),a("div",{staticClass:"ranking-list"},[t._l(t.registerRankingData,(function(e,n){return a("div",{key:n,staticClass:"top-item flex"},[a("div",{staticClass:"left flex"},[a("div",{staticClass:"count",class:t.foodTOPThreeClass(n+1)},[t._v(t._s(n+1))]),a("div",{staticClass:"food"},[t._v(t._s("1"==t.orgsChooseIdRegister?e.org_name:e.channel_name))])]),a("div",{staticClass:"right"},[t._v(t._s(t._f("formatPriceTo3")(e.total_register_count))+"人")])])})),t.registerRankingData.length?t._e():a("div",{staticClass:"flex flex-center empty-text"},[t._v(" 暂无更多 ")])],2)]),a("div",{staticClass:"food-top ranking-item m-l-20 m-t-20 m-b-20"},[a("div",{staticClass:"top flex"},[a("div",{staticClass:"title"},[t._v("交易金额排行TOP20")]),a("div",{staticClass:"m-l-10"},[a("el-select",{staticClass:"w-150",attrs:{placeholder:"请选择"},on:{change:function(a){t.changeOrgsChoose("transaction",a)}},model:{value:t.orgsChooseIdTransaction,callback:function(a){t.orgsChooseIdTransaction=a},expression:"orgsChooseIdTransaction"}},t._l(t.orgsChooseList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.name,value:t.value}})})),1)],1)]),a("div",{staticClass:"ranking-list"},[t._l(t.totalFeeRankingData,(function(e,n){return a("div",{key:n,staticClass:"top-item flex"},[a("div",{staticClass:"left flex"},[a("div",{staticClass:"count",class:t.foodTOPThreeClass(n+1)},[t._v(t._s(n+1))]),a("div",{staticClass:"food"},[t._v(t._s("1"==t.orgsChooseIdTransaction?e.org_name:e.channel_name))])]),a("div",{staticClass:"right"},[t._v(" ￥"+t._s(t._f("formatPriceTo3")(t._f("formatMoney")(e.total_real_fee)))+" ")])])})),t.totalFeeRankingData.length?t._e():a("div",{staticClass:"flex flex-center empty-text"},[t._v(" 暂无更多 ")])],2)])])},s=[],i=e("ed08"),o=e("2232"),l={name:"Ranking",props:{chartData:{type:Object,default:function(){}},channelId:{type:Array,default:function(){return[]}},timeValue:{type:Object,default:function(){return{}}}},data:function(){return{loginRankingData:[],loginProjectRankingData:[],loginChannelRankingData:[],registerRankingData:[],registerProjectRankingData:[],registerChannelRankingData:[],totalFeeRankingData:[],totalFeeProjectRankingData:[],totalFeeChannelRankingData:[],orgsChooseList:Object(i["f"])(o["ORGS_CHOOSE_LIST"]),orgsChooseIdLogin:"1",orgsChooseIdRegister:"1",orgsChooseIdTransaction:"1"}},created:function(){},mounted:function(){window.addEventListener("resize",this.resizeChartHandle)},computed:{},watch:{chartData:{deep:!0,handler:function(t){this.initFoodSalesRanking(Object(i["f"])(t))}},channelId:function(t){},timeValue:{deep:!0,handler:function(t){}}},methods:{initFoodSalesRanking:function(t){var a=t.analysis_data||[];this.loginRankingData=Object(i["f"])(this.sortData(a,"total_login_count")),this.registerRankingData=Object(i["f"])(this.sortData(a,"total_register_count")),this.totalFeeRankingData=Object(i["f"])(this.sortData(a,"total_real_fee")),this.loginProjectRankingData=Object(i["f"])(this.loginRankingData),this.registerProjectRankingData=Object(i["f"])(this.registerRankingData),this.totalFeeProjectRankingData=Object(i["f"])(this.totalFeeRankingData);var e=this.getChannelList(a);e&&(this.loginChannelRankingData=Object(i["f"])(this.sortData(e,"total_login_count")),this.registerChannelRankingData=Object(i["f"])(this.sortData(e,"total_register_count")),this.totalFeeChannelRankingData=Object(i["f"])(this.sortData(e,"total_real_fee")))},getChannelList:function(t){var a=[],e=[];return t.forEach((function(t){e.push(t.channel_name)})),e=new Set(e),e.forEach((function(e){var n=e,s=t.filter((function(t){return t.channel_name===n}));if(s&&1===s.length)a.push(s[0]);else{for(var i=s[0],o=1;o<s.length;o++)i.total_login_count=i.total_login_count+s[o].total_login_count,i.total_register_count=i.total_register_count+s[o].total_register_count,i.total_real_fee=i.total_real_fee+s[o].total_real_fee;a.push(i)}})),a},sortData:function(t,a,e){var n=t;return n.sort((function(t,n){return"up"===e?t[a]-n[a]:n[a]-t[a]})),n},foodTOPThreeClass:function(t){return 1===t?"No1":2===t?"No2":3===t?"No3":void 0},changeOrgsChoose:function(t,a){switch(t){case"login":this.loginRankingData="1"===a?Object(i["f"])(this.loginProjectRankingData):Object(i["f"])(this.loginChannelRankingData);break;case"register":this.registerRankingData="1"===a?Object(i["f"])(this.registerProjectRankingData):Object(i["f"])(this.registerChannelRankingData);break;case"transaction":this.totalFeeRankingData="1"===a?Object(i["f"])(this.totalFeeProjectRankingData):Object(i["f"])(this.totalFeeChannelRankingData);break;default:break}}}},r=l,c=(e("65d7"),e("2877")),g=Object(c["a"])(r,n,s,!1,null,"b5b33532",null);a["default"]=g.exports},"65d7":function(t,a,e){"use strict";e("dd46")},dd46:function(t,a,e){}}]);