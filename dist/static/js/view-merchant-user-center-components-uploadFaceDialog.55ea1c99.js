(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-user-center-components-uploadFaceDialog"],{7115:function(t,e,r){},b4b6:function(t,e,r){"use strict";r("7115")},c46d:function(t,e,r){"use strict";r.r(e);var a=function(){var t=this,e=t._self._c;return e("div",[e("dialog-message",{attrs:{width:"800px","show-close":!1,show:t.showDialog,showFooter:!1,center:"center"},on:{"update:show":function(e){t.showDialog=e}},scopedSlots:t._u([{key:"title",fn:function(){return[t._v("编辑人脸信息")]},proxy:!0},{key:"tool",fn:function(){return[e("div",{staticClass:"footer-btn"},[e("el-button",{staticClass:"unify-btn",attrs:{disabled:t.uploading},on:{click:t.cancelBtn}},[t._v("取消")]),e("el-button",{staticClass:"ps-btn unify-btn save-btn",attrs:{disabled:t.showSave},on:{click:t.save}},[t._v(" 保存 ")]),e("el-button",{staticClass:"ps-warn delete-btn unify-btn",attrs:{disabled:!t.formData.imageList[0]},on:{click:t.deleteFace}},[t._v(" 删除 ")])],1)]},proxy:!0}])},[[e("div",{staticClass:"dialog-content"},[e("div",{staticClass:"left"},[e("el-upload",{directives:[{name:"loading",rawName:"v-loading",value:t.uploading,expression:"uploading"}],ref:"fileUpload",staticClass:"file-upload",attrs:{"element-loading-text":"上传中",drag:!0,action:t.serverUrl,data:t.uploadParams,"file-list":t.fileLists,"on-success":t.uploadSuccess,"before-upload":t.beforeFoodImgUpload,limit:1,multiple:!1,"show-file-list":!1,headers:t.headersOpts}},[t._t("default",(function(){return[t.formData.imageList.length?t._e():e("div",{staticClass:"upload-t"},[e("div",{},[e("i",{staticClass:"el-icon-upload"}),e("div",{staticClass:"el-upload__text"},[t._v(" 将文件拖到此处，或 "),e("em",[t._v("点击上传")])])])]),t.formData.imageList.length?e("el-image",{staticClass:"el-upload-dragger upload-img",attrs:{src:t.formData.imageList[0],fit:"contain"},on:{click:t.changeFace}}):t._e()]}))],2)],1),e("div",{staticClass:"right"},[e("div",{staticClass:"upload-state"},[e("div",{directives:[{name:"show",rawName:"v-show",value:"upload_crossing"===t.currentState,expression:"currentState === 'upload_crossing'"}],staticClass:"upload-crossing"},[e("i",{staticClass:"el-icon-loading icon-loading"}),e("span",[t._v("正在上传图片")])]),e("div",{directives:[{name:"show",rawName:"v-show",value:"upload_success"===t.currentState||"upload_delete"===t.currentState||"upload_delete_save"===t.currentState,expression:"currentState === 'upload_success' || currentState === 'upload_delete' || currentState === 'upload_delete_save'"}],staticClass:"upload-success"},[e("i",{staticClass:"el-icon-check icon-check"}),e("span",{directives:[{name:"show",rawName:"v-show",value:"upload_success"===t.currentState,expression:"currentState === 'upload_success'"}]},[t._v("上传图片成功，请保存")]),e("span",{directives:[{name:"show",rawName:"v-show",value:"upload_delete"===t.currentState,expression:"currentState === 'upload_delete'"}]},[t._v(" 图片信息已删除 ")]),e("span",{directives:[{name:"show",rawName:"v-show",value:"upload_delete_save"===t.currentState&&t.faceDetailInfo.face_url&&"locality"!==t.deleteBehavior,expression:"currentState === 'upload_delete_save' && faceDetailInfo.face_url && deleteBehavior !== 'locality'"}]},[t._v(" 图片信息已删除，请保存 ")])]),e("div",{directives:[{name:"show",rawName:"v-show",value:"upload_fail"===t.currentState,expression:"currentState === 'upload_fail'"}],staticClass:"upload-fail"},[e("i",{staticClass:"el-icon-close icon-close"}),e("span",[t._v("上传失败 + ' ' + "+t._s(t.uploadFailMsg))])])]),e("div",{staticClass:"format-tips unify-font"},[t._v("支持jpg、png、bmp格式, 大小不超过10MB")]),e("div",{staticClass:"unify-font"},[t._v("提示：")]),e("div",{staticClass:"unify-font"},[t._v("1、确保上传的图片只存在一张人脸照片信息；")]),e("div",{staticClass:"unify-font"},[t._v("2、确保上传的图片清晰, 无模糊；")]),e("div",{staticClass:"unify-font"},[t._v("3、确保人脸位于图片正中央位置, 且占据图片超过60%的区域；")]),e("div",{staticClass:"unify-font"},[t._v("4、确保拍摄时光线充足；")]),e("div",{staticClass:"unify-font"},[t._v("5、确保照片为正90度；")]),e("div",{staticClass:"unify-font"},[t._v(" 6、为避免影响人脸功能的正常使用, 建议6个月后更新一次人脸照片。 ")])])])]],2)],1)},n=[],o=r("ed08");function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function s(t,e){return d(t)||f(t,e)||l(t,e)||c()}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function l(t,e){if(t){if("string"==typeof t)return u(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?u(t,e):void 0}}function u(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,a=Array(e);r<e;r++)a[r]=t[r];return a}function f(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var a,n,o,i,s=[],c=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(a=o.call(r)).done)&&(s.push(a.value),s.length!==e);c=!0);}catch(t){l=!0,n=t}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(l)throw n}}return s}}function d(t){if(Array.isArray(t))return t}function h(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */h=function(){return e};var t,e={},r=Object.prototype,a=r.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},s=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function f(t,e,r,a){var o=e&&e.prototype instanceof w?e:w,i=Object.create(o.prototype),s=new E(a||[]);return n(i,"_invoke",{value:F(t,r,s)}),i}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",v="suspendedYield",g="executing",m="completed",y={};function w(){}function _(){}function b(){}var S={};u(S,s,(function(){return this}));var x=Object.getPrototypeOf,L=x&&x(x(P([])));L&&L!==r&&a.call(L,s)&&(S=L);var D=b.prototype=w.prototype=Object.create(S);function C(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function r(n,o,s,c){var l=d(t[n],t,o);if("throw"!==l.type){var u=l.arg,f=u.value;return f&&"object"==i(f)&&a.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,s,c)}),(function(t){r("throw",t,s,c)})):e.resolve(f).then((function(t){u.value=t,s(u)}),(function(t){return r("throw",t,s,c)}))}c(l.arg)}var o;n(this,"_invoke",{value:function(t,a){function n(){return new e((function(e,n){r(t,a,e,n)}))}return o=o?o.then(n,n):n()}})}function F(e,r,a){var n=p;return function(o,i){if(n===g)throw Error("Generator is already running");if(n===m){if("throw"===o)throw i;return{value:t,done:!0}}for(a.method=o,a.arg=i;;){var s=a.delegate;if(s){var c=j(s,a);if(c){if(c===y)continue;return c}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(n===p)throw n=m,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);n=g;var l=d(e,r,a);if("normal"===l.type){if(n=a.done?m:v,l.arg===y)continue;return{value:l.arg,done:a.done}}"throw"===l.type&&(n=m,a.method="throw",a.arg=l.arg)}}}function j(e,r){var a=r.method,n=e.iterator[a];if(n===t)return r.delegate=null,"throw"===a&&e.iterator.return&&(r.method="return",r.arg=t,j(e,r),"throw"===r.method)||"return"!==a&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+a+"' method")),y;var o=d(n,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,y;var i=o.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function I(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(I,this),this.reset(!0)}function P(e){if(e||""===e){var r=e[s];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,o=function r(){for(;++n<e.length;)if(a.call(e,n))return r.value=e[n],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(i(e)+" is not iterable")}return _.prototype=b,n(D,"constructor",{value:b,configurable:!0}),n(b,"constructor",{value:_,configurable:!0}),_.displayName=u(b,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===_||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,u(t,l,"GeneratorFunction")),t.prototype=Object.create(D),t},e.awrap=function(t){return{__await:t}},C(k.prototype),u(k.prototype,c,(function(){return this})),e.AsyncIterator=k,e.async=function(t,r,a,n,o){void 0===o&&(o=Promise);var i=new k(f(t,r,a,n),o);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},C(D),u(D,l,"Generator"),u(D,s,(function(){return this})),u(D,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var a in e)r.push(a);return r.reverse(),function t(){for(;r.length;){var a=r.pop();if(a in e)return t.value=a,t.done=!1,t}return t.done=!0,t}},e.values=P,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(O),!e)for(var r in this)"t"===r.charAt(0)&&a.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function n(a,n){return s.type="throw",s.arg=e,r.next=a,n&&(r.method="next",r.arg=t),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],s=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=a.call(i,"catchLoc"),l=a.call(i,"finallyLoc");if(c&&l){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,y):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),O(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var a=r.completion;if("throw"===a.type){var n=a.arg;O(r)}return n}}throw Error("illegal catch attempt")},delegateYield:function(e,r,a){return this.delegate={iterator:P(e),resultName:r,nextLoc:a},"next"===this.method&&(this.arg=t),y}},e}function p(t,e,r,a,n,o,i){try{var s=t[o](i),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(a,n)}function v(t){return function(){var e=this,r=arguments;return new Promise((function(a,n){var o=t.apply(e,r);function i(t){p(o,a,n,i,s,"next",t)}function s(t){p(o,a,n,i,s,"throw",t)}i(void 0)}))}}var g={props:{faceDetailInfo:{type:Object,default:function(){return{}}}},computed:{showSave:function(){var t=!0;return(this.faceDetailInfo.face_url&&"upload_delete_save"===this.currentState||"upload_success"===this.currentState&&this.formData.imageList[0])&&(t=!1),t}},data:function(){return{showDialog:!1,uploading:!1,formData:{imageList:[]},serverUrl:"/api/background/file/upload",headersOpts:{TOKEN:Object(o["B"])()},fileLists:[],uploadParams:{prefix:"face",key:(new Date).getTime()+Math.floor(150*Math.random())},currentState:"upload_waiting",uploadFailMsg:"",tempFaceUrl:"",userFaceDetailInfo:{},deleteBehavior:""}},methods:{openDialog:function(){this.showDialog=!0,this.userFaceDetailInfo=Object(o["f"])(this.faceDetailInfo),this.faceDetailInfo.face_url&&(this.formData.imageList[0]=this.faceDetailInfo.face_url)},closeDialog:function(){this.currentState="upload_waiting",this.formData.imageList=[],this.$refs.fileUpload.clearFiles(),this.showDialog=!1},uploadSuccess:function(t,e,r){this.uploading=!1,0===t.code?(this.fileLists=r,this.formData.imageList=[t.data.public_url],this.currentState="upload_success",this.deleteBehavior=""):(this.currentState="upload_fail",this.$message.error(t.msg))},beforeFoodImgUpload:function(t){var e=this;this.uploading=!0,this.currentState="upload_crossing";var r=[".jpg",".png",".bmp"],a=t.size/1024/1024<=10;return r.includes(Object(o["A"])(t.name))?a?new Promise((function(r,a){e.uploading=!0;var n=new FileReader;n.onload=function(n){var o=new Image;o.onload=function(){var a=document.createElement("canvas"),n=a.getContext("2d");a.width=o.width,a.height=o.height,n.drawImage(o,0,0),a.toBlob((function(a){var n=new File([a],t.name,{type:"image/jpeg"});r(n),e.currentState="upload_crossing"}),"image/jpeg")},o.onerror=a,o.src=n.target.result},n.readAsDataURL(t)})).catch((function(){e.currentState="upload_fail",e.uploading=!1})):(this.$message.error("上传图片大小不能超过 10MB!"),this.uploading=!1,this.currentState="normal",this.deleteBehavior="",!1):(this.$message.error("上传图片只能是JPG/BMP/PNG格式!"),this.uploading=!1,this.currentState="normal",this.deleteBehavior="",!1)},save:function(){var t=this;return v(h().mark((function e(){return h().wrap((function(e){while(1)switch(e.prev=e.next){case 0:"upload_success"===t.currentState&&t.formData.imageList[0]?t.addFaceInfo():t.faceDetailInfo.face_url&&"upload_delete_save"===t.currentState&&t.deleteFaceAPI();case 1:case"end":return e.stop()}}),e)})))()},addFaceInfo:function(){var t=this;return v(h().mark((function e(){var r,a,n,i;return h().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(o["Z"])(t.$apis.apiCardServiceCardUserUploadUserFacePost({face_url:t.formData.imageList[0],card_info_id:t.faceDetailInfo.id}));case 2:if(r=e.sent,a=s(r,2),n=a[0],i=a[1],!n){e.next=8;break}return e.abrupt("return",t.$message.error(n.msg));case 8:if(0!==i.code){e.next=14;break}t.$emit("uploadSuccess"),t.closeDialog(),t.$message.success(i.msg),e.next=15;break;case 14:return e.abrupt("return",t.$message.error(i.msg));case 15:case"end":return e.stop()}}),e)})))()},deleteFaceAPI:function(){var t=this;return v(h().mark((function e(){var r,a,n,i;return h().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(o["Z"])(t.$apis.apiCardServiceCardUserDeleteFacePost({id:t.faceDetailInfo.id}));case 2:if(r=e.sent,a=s(r,2),n=a[0],i=a[1],!n){e.next=8;break}return e.abrupt("return",t.$message.error(n.msg));case 8:if(0!==i.code){e.next=14;break}t.$emit("uploadSuccess"),t.closeDialog(),t.$message.success(i.msg),e.next=15;break;case 14:return e.abrupt("return",t.$message.error(i.msg));case 15:case"end":return e.stop()}}),e)})))()},cancelBtn:function(){this.deleteBehavior="",this.closeDialog()},deleteFace:function(){this.$refs.fileUpload.clearFiles(),this.formData.imageList=[],this.faceDetailInfo.face_url?this.currentState="upload_delete_save":this.currentState="upload_delete"},changeFace:function(){this.$refs.fileUpload.clearFiles(),this.formData.imageList=[],this.deleteBehavior="locality",this.currentState="normal"}},mounted:function(){}},m=g,y=(r("b4b6"),r("2877")),w=Object(y["a"])(m,a,n,!1,null,"6b099d3c",null);e["default"]=w.exports}}]);