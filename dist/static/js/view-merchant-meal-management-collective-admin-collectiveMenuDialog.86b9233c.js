(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-collective-admin-collectiveMenuDialog"],{"398a":function(e,t,r){"use strict";r("eeca")},"4a9c":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("dialog-message",{attrs:{show:e.visible,title:e.title,loading:e.isLoading,customClass:"ps-dialog",width:e.width},on:{"update:show":function(t){e.visible=t},"update:loading":function(t){e.isLoading=t},close:e.handleClose}},[t("el-form",{ref:"formData",staticClass:"dialog-form collective-mel-dialog",attrs:{model:e.form,rules:e.rules,size:"small"}},[t("el-form-item",{staticClass:"block-label",attrs:{label:"菜谱名称：",prop:"name"}},[t("el-input",{staticClass:"ps-input",attrs:{placeholder:"请输入菜谱名称",maxlength:"15"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),t("el-form-item",{staticClass:"block-label",attrs:{label:"所属集体"}},[t("el-select",{staticClass:"ps-select",staticStyle:{width:"100%"},attrs:{disabled:""},model:{value:e.form.collective_value,callback:function(t){e.$set(e.form,"collective_value",t)},expression:"form.collective_value"}},e._l(e.collective_groups,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),t("el-form-item",{staticClass:"block-label",attrs:{label:"日期"}},[t("el-radio-group",{model:{value:e.form.menu_type,callback:function(t){e.$set(e.form,"menu_type",t)},expression:"form.menu_type"}},[t("el-radio",{staticClass:"ps-radio",attrs:{label:"week"}},[e._v("周菜谱")]),t("el-radio",{staticClass:"ps-radio",attrs:{label:"month"}},[e._v("月菜谱")])],1)],1),"week"===e.form.menu_type?t("div",{staticClass:"ps-flex-align-c"},[t("el-form-item",{attrs:{label:""}},[t("el-date-picker",{staticClass:"w-200 m-r-20",attrs:{type:"month","picker-options":e.pickerOptions,placeholder:"选择月","value-format":"yyyy-MM",clearable:!1},on:{change:e.changeWeekMont},model:{value:e.form.weekMontValue,callback:function(t){e.$set(e.form,"weekMontValue",t)},expression:"form.weekMontValue"}})],1),t("el-form-item",{attrs:{label:""}},[t("el-select",{staticClass:"ps-select",staticStyle:{width:"300px"},attrs:{"popper-class":"ps-popper-select",placeholder:"选择周",multiple:"","collapse-tags":"",size:"small"},model:{value:e.form.weekList,callback:function(t){e.$set(e.form,"weekList",t)},expression:"form.weekList"}},e._l(e.weekListGroups,(function(r){return t("el-option",{key:r.start_end,attrs:{label:r.label,value:r.start_end,disabled:e.weekDisabled(r)}})})),1)],1)],1):e._e(),t("div",["month"===e.form.menu_type?t("el-form-item",{attrs:{label:""}},[t("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"months","picker-options":e.pickerOptions,"value-format":"yyyy-MM",placeholder:"选择月",clearable:!1},model:{value:e.form.monthList,callback:function(t){e.$set(e.form,"monthList",t)},expression:"form.monthList"}})],1):e._e()],1),t("el-form-item",{attrs:{label:"设备类型：",prop:"device_types"}},[t("el-select",{staticClass:"ps-select",staticStyle:{width:"100%"},attrs:{multiple:"",placeholder:"请选择设备类型","popper-class":"ps-popper-select"},on:{change:e.deviceTypeChange},model:{value:e.form.device_types,callback:function(t){e.$set(e.form,"device_types",t)},expression:"form.device_types"}},e._l(e.deviceArr,(function(e){return t("el-option",{key:e.key,attrs:{label:e.name,value:e.key}})})),1)],1),t("el-form-item",{attrs:{label:"设备型号：",prop:e.isDisabledModel?"":"device_model"}},[t("el-select",{staticClass:"ps-select",staticStyle:{width:"100%"},attrs:{multiple:"",placeholder:"请选择设备型号","popper-class":"ps-popper-select",disabled:e.isDisabledModel},model:{value:e.form.device_model,callback:function(t){e.$set(e.form,"device_model",t)},expression:"form.device_model"}},e._l(e.deviceModelList,(function(e){return t("el-option",{key:e.key,attrs:{label:e.key,value:e.key}})})),1)],1),t("el-form-item",{attrs:{label:"可见分组",prop:"use_user_groups"}},[t("user-group-select",{staticStyle:{width:"100%"},attrs:{multiple:"",disabled:e.isDisabledGroup,options:e.groupOptions},model:{value:e.form.use_user_groups,callback:function(t){e.$set(e.form,"use_user_groups",t)},expression:"form.use_user_groups"}})],1)],1),t("template",{slot:"tool"},[t("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:e.clickCancleHandle}},[e._v(" 取消 ")]),t("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickConfirmHandle}},[e._v(" 保存 ")])],1)])],2)},o=[],a=r("5a0c"),i=r("390a"),s=r("ed08");function l(e){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},l(e)}function c(e){return d(e)||f(e)||v(e)||u()}function u(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function d(e){if(Array.isArray(e))return y(e)}function p(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */p=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(e,t,r){e[t]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",c=a.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function f(e,t,r,n){var a=t&&t.prototype instanceof b?t:b,i=Object.create(a.prototype),s=new $(n||[]);return o(i,"_invoke",{value:O(e,r,s)}),i}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var m="suspendedStart",h="suspendedYield",v="executing",y="completed",g={};function b(){}function w(){}function _(){}var k={};u(k,i,(function(){return this}));var D=Object.getPrototypeOf,L=D&&D(D(j([])));L&&L!==r&&n.call(L,i)&&(k=L);var M=_.prototype=b.prototype=Object.create(k);function x(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function Y(e,t){function r(o,a,i,s){var c=d(e[o],e,a);if("throw"!==c.type){var u=c.arg,f=u.value;return f&&"object"==l(f)&&n.call(f,"__await")?t.resolve(f.__await).then((function(e){r("next",e,i,s)}),(function(e){r("throw",e,i,s)})):t.resolve(f).then((function(e){u.value=e,i(u)}),(function(e){return r("throw",e,i,s)}))}s(c.arg)}var a;o(this,"_invoke",{value:function(e,n){function o(){return new t((function(t,o){r(e,n,t,o)}))}return a=a?a.then(o,o):o()}})}function O(t,r,n){var o=m;return function(a,i){if(o===v)throw Error("Generator is already running");if(o===y){if("throw"===a)throw i;return{value:e,done:!0}}for(n.method=a,n.arg=i;;){var s=n.delegate;if(s){var l=C(s,n);if(l){if(l===g)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===m)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var c=d(t,r,n);if("normal"===c.type){if(o=n.done?y:h,c.arg===g)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o=y,n.method="throw",n.arg=c.arg)}}}function C(t,r){var n=r.method,o=t.iterator[n];if(o===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,C(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var a=d(o,t.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,g;var i=a.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,g):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function S(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function $(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(S,this),this.reset(!0)}function j(t){if(t||""===t){var r=t[i];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,a=function r(){for(;++o<t.length;)if(n.call(t,o))return r.value=t[o],r.done=!1,r;return r.value=e,r.done=!0,r};return a.next=a}}throw new TypeError(l(t)+" is not iterable")}return w.prototype=_,o(M,"constructor",{value:_,configurable:!0}),o(_,"constructor",{value:w,configurable:!0}),w.displayName=u(_,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,u(e,c,"GeneratorFunction")),e.prototype=Object.create(M),e},t.awrap=function(e){return{__await:e}},x(Y.prototype),u(Y.prototype,s,(function(){return this})),t.AsyncIterator=Y,t.async=function(e,r,n,o,a){void 0===a&&(a=Promise);var i=new Y(f(e,r,n,o),a);return t.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},x(M),u(M,c,"Generator"),u(M,i,(function(){return this})),u(M,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=j,$.prototype={constructor:$,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(E),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function o(n,o){return s.type="throw",s.arg=t,r.next=n,o&&(r.method="next",r.arg=e),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],s=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,g):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),E(r),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;E(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:j(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),g}},t}function m(e,t){return b(e)||g(e,t)||v(e,t)||h()}function h(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function v(e,t){if(e){if("string"==typeof e)return y(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?y(e,t):void 0}}function y(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function g(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,i,s=[],l=!0,c=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(c)throw o}}return s}}function b(e){if(Array.isArray(e))return e}function w(e,t,r,n,o,a,i){try{var s=e[a](i),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,o)}function _(e){return function(){var t=this,r=arguments;return new Promise((function(n,o){var a=e.apply(t,r);function i(e){w(a,n,o,i,s,"next",e)}function s(e){w(a,n,o,i,s,"throw",e)}i(void 0)}))}}var k={name:"collectiveMelDialog",props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:"创建菜谱"},width:{type:String,default:"600px"},isshow:Boolean,formDataDialog:{type:Object,default:function(){return{}}},confirm:Function},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},components:{UserGroupSelect:i["a"]},data:function(){return{isLoading:!1,rules:{name:[{required:!0,message:"请输入名称",trigger:"blur"}],device_types:[{required:!0,message:"请选择设备类型",trigger:"change"}],device_model:[{required:!0,message:"请选择设备型号",trigger:"change"}]},deviceArr:[],collective_groups:[],deviceModelList:[],form:{id:-1,name:"",collective_value:"",menu_type:"week",device_types:[],device_model:[],use_user_groups:[],weekMontValue:"",monthList:[],weekList:[]},isDisabledModel:!1,isDisabledGroup:!0,groupOptions:{label:"group_name",value:"id"},pickerOptions:{disabledDate:function(e){return e.getTime()<Date.now()-864e5}},weekListGroups:[]}},created:function(){this.initLoad(),this.collective_groups.push({name:this.formDataDialog.name,id:this.formDataDialog.id}),this.form.collective_value=this.formDataDialog.id},mounted:function(){},methods:{getWeekList:function(e){var t=[],r=a(e).startOf("week").add(1,"day"),n={label:r.format("MM.DD")+"-"+a(e).endOf("week").add(1,"day").format("MM.DD"),start:r.format("YYYY-MM-DD"),end:a(e).endOf("week").add(1,"day").format("YYYY-MM-DD"),start_end:r.format("YYYY-MM-DD")+"_"+a(e).endOf("week").add(1,"day").format("YYYY-MM-DD")};t.push(n);for(var o=1;o<4;o++)t.push({label:r.add(o,"week").startOf("week").add(1,"day").format("MM.DD")+"-"+r.add(o,"week").endOf("week").add(1,"day").format("MM.DD"),start:r.add(o,"week").startOf("week").add(1,"day").format("YYYY-MM-DD"),end:r.add(o,"week").endOf("week").add(1,"day").format("YYYY-MM-DD"),start_end:r.add(o,"week").startOf("week").add(1,"day").format("YYYY-MM-DD")+"_"+r.add(o,"week").endOf("week").add(1,"day").format("YYYY-MM-DD")});return t},getCollectiveCreateFoodMenu:function(e){var t=this;return _(p().mark((function r(){var n,o,a,i;return p().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return t.isLoading=!0,r.next=3,Object(s["Z"])(t.$apis.apiBackgroundFoodCollectiveCreateFoodMenuPost(e));case 3:if(n=r.sent,o=m(n,2),a=o[0],i=o[1],t.isLoading=!1,!a){r.next=11;break}return t.$message.error(a.message),r.abrupt("return");case 11:0===i.code?(t.$message.success(i.msg),t.visible=!1,t.confirm()):t.$message.error(i.msg);case 12:case"end":return r.stop()}}),r)})))()},clickConfirmHandle:function(){var e=this;this.$refs.formData.validate((function(t){if(!t)return!1;var r={name:e.form.name,menu_type:e.form.menu_type,menu_weekly_data:[],menu_monthly_data:[],collective_id:e.form.collective_value,device_types:e.form.device_types,device_model:e.form.device_model,use_user_groups:e.form.use_user_groups};return e.form.weekList.length&&e.form.weekList.forEach((function(e){var t=e.split("_");r.menu_weekly_data.push({start_date:t[0],end_date:t[1]})})),e.form.monthList.length&&e.form.monthList.forEach((function(e){r.menu_monthly_data.push({month:a(e).format("YYYYMM")})})),"week"!==e.form.menu_type||e.form.weekList.length?"month"!==e.form.menu_type||e.form.monthList.length?void e.getCollectiveCreateFoodMenu(r):e.$message.error("请选择月"):e.$message.error("请选择周")}))},clickCancleHandle:function(){this.visible=!1},handleClose:function(e){this.visible=!1},initLoad:function(){var e=this;return _(p().mark((function t(){return p().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getOrgDeviceList();case 2:e.getDeviceModel();case 3:case"end":return t.stop()}}),t)})))()},deviceTypeChange:function(){this.form.device_model=[],this.$refs.formData.clearValidate(),this.getDeviceModel()},getDeviceModel:function(){var e=this;return _(p().mark((function t(){var r,n,o;return p().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(r=["H5","MAPP","ZNC"],e.isDisabledGroup=!0,r.map((function(t){e.form.device_types.includes(t)&&(e.isDisabledGroup=!1)})),n=e.form.device_types.filter((function(e){return"H5"!==e&&"MAPP"!==e})),n.length){t.next=9;break}return e.isDisabledModel=!0,t.abrupt("return");case 9:e.isDisabledModel=!1;case 10:return t.next=12,e.$apis.apiBackgroundDeviceDeviceDeviceModelPost({device_types:n});case 12:o=t.sent,0===o.code?e.deviceModelList=o.data:e.$message.error(o.msg);case 14:case"end":return t.stop()}}),t)})))()},getOrgDeviceList:function(){var e=this;return _(p().mark((function t(){var r;return p().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundDeviceDeviceDeviceTypePost({source:"self"});case 2:r=t.sent,0===r.code?e.deviceArr=[{name:"H5",key:"H5"},{name:"小程序",key:"MAPP"}].concat(c(r.data)):e.$message.error(r.msg);case 4:case"end":return t.stop()}}),t)})))()},changeWeekMont:function(e){this.weekListGroups=this.getWeekList(a(e).add(1,"day"))},weekDisabled:function(e){var t=!1;return(new Date).getTime()>new Date(e.end).getTime()&&(t=!0),t}}},D=k,L=(r("398a"),r("2877")),M=Object(L["a"])(D,n,o,!1,null,"53819005",null);t["default"]=M.exports},eeca:function(e,t,r){}}]);