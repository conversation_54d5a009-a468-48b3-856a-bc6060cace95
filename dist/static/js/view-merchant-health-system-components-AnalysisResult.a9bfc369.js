(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-health-system-components-AnalysisResult"],{2157:function(t,e,a){"use strict";a("c2fb")},c2fb:function(t,e,a){},d85e:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"ps-el-drawer"},[e("el-drawer",{attrs:{title:"营养分析",visible:t.visible,"show-close":!1,size:"70%"}},[e("div",{staticClass:"drawer-content p-20 flex-col"},[e("div",{staticClass:"flex-b-c m-b-20"},[e("div",{staticClass:"flex-b-c"},[e("el-button",{class:["w-100","day"===t.type?"ps-origin-btn":""],attrs:{size:"small"},on:{click:function(e){t.type="day"}}},[t._v("按天")]),e("el-button",{class:["w-100","week"===t.type?"ps-origin-btn":""],attrs:{size:"small"},on:{click:function(e){t.type="week"}}},[t._v("按自然周")])],1),e("special-date-picker",{attrs:{selectType:t.type,selectedId:t.selectedId,showRecord:t.visible},on:{setHealthyInfo:t.setHealthyInfo}})],1),"day"===t.type?e("div",{staticClass:"drawer-content-list"},[e("div",{staticClass:"f-w-700 font-size-18 m-b-10"},[t._v("食物清单（"+t._s(this.foodList.length)+"）")]),e("div",{staticClass:"bg-grey flex-center"},[e("div",{staticClass:"drawer-content-list-box w-100-p"},t._l(t.foodList,(function(a,n){return e("div",{key:n,staticClass:"drawer-content-list-box-item flex-b-c"},[e("div",[t._v(t._s(a.name))]),e("div",[t._v(t._s(a.weight)+"g")])])})),0)])]):t._e(),e("div",{staticClass:"drawer-content-layout"},[e("div",{staticClass:"f-w-700 font-size-18 m-b-10"},[t._v("饮食结构")]),e("div",{staticClass:"bg-grey flex-center"},[e("div",{staticClass:"drawer-content-layout-box w-100-p"},t._l(t.dietData,(function(a,n){return e("div",{key:n,staticClass:"drawer-content-layout-box-item flex-b-c"},[e("div",[t._v(t._s(a.text)+"："+t._s(a.value)+"g / "+t._s(a.range[0])+"g - "+t._s(a.range[1])+"g")]),"none"!==t.computedStatus(a)?e("div",[e("i",{class:["up"===t.computedStatus(a)?"el-icon-top":"el-icon-bottom"],style:"up"===t.computedStatus(a)?{color:"#D9001B"}:{color:"#F59A23"}})]):t._e()])})),0)])]),e("div",{staticClass:"drawer-content-analysis"},[e("div",{staticClass:"f-w-700 font-size-18 m-b-10"},[t._v("总能量分析")]),e("div",{staticClass:"bg-grey flex-center"},[e("div",{staticClass:"drawer-content-analysis-box w-100-p"},t._l(t.analysisData,(function(a,n){return e("div",{key:n,staticClass:"drawer-content-analysis-box-item flex-b-c"},[e("div",[t._v(t._s(a.text)+"："+t._s(a.value)+"kcal / "+t._s(a.range[0])+"kcal - "+t._s(a.range[1])+"kcal")]),"none"!==t.computedStatus(a)?e("div",[e("i",{class:["up"===t.computedStatus(a)?"el-icon-top":"el-icon-bottom"],style:"up"===t.computedStatus(a)?{color:"#D9001B"}:{color:"#F59A23"}})]):t._e()])})),0)])]),e("div",{staticClass:"m-b-20"},[e("div",{staticClass:"f-w-700 font-size-18 m-b-10"},[t._v("能量来源")]),e("div",{staticClass:"bg-grey"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableView",attrs:{data:t.energyTableData,stripe:"","header-row-class-name":"ps-table-header-row"}},t._l(t.energyTableSetting,(function(a){return e("table-column",{key:a.key,attrs:{col:a},scopedSlots:t._u([{key:"value",fn:function(a){var n=a.row;return[e("div",{staticClass:"text-right"},[e("span",{staticClass:"m-r-10"},[t._v(t._s(n.value)+t._s(n.unit))]),e("i",{class:["up"===t.computedStatus(n)?"el-icon-top":"el-icon-bottom"],style:"up"===t.computedStatus(n)?{color:"#D9001B"}:{color:"#F59A23"}})])]}},{key:"value_rate",fn:function(e){var a=e.row;return[t._v(" "+t._s(a.range[0])+" - "+t._s(a.range[1])+"g ")]}},{key:"range",fn:function(e){var a=e.row;return[t._v(" "+t._s(a.value_rate)+"% ")]}},{key:"range_rate",fn:function(e){var a=e.row;return[t._v(" "+t._s(a.range_rate[0])+" - "+t._s(a.range_rate[1])+"% ")]}}],null,!0)})})),1)],1)]),e("div",{staticClass:"m-b-20"},[e("div",{staticClass:"f-w-700 font-size-18 m-b-10"},[t._v("营养素分析")]),e("div",{staticClass:"bg-grey drawer-content-table"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableView",attrs:{data:t.nutrientTableData[0],stripe:"","header-row-class-name":"ps-table-header-row"}},t._l(t.nutrientTableSetting,(function(a,n){return e("table-column",{key:n,attrs:{col:a},scopedSlots:t._u([{key:"value",fn:function(a){var n=a.row;return[e("div",{staticClass:"text-right"},[e("span",{staticClass:"m-r-10"},[t._v(t._s(n.value)+t._s(n.unit))]),e("i",{class:["up"===t.computedStatus(n)?"el-icon-top":"el-icon-bottom"],style:"up"===t.computedStatus(n)?{color:"#D9001B"}:{color:"#F59A23"}})])]}},{key:"range",fn:function(e){var a=e.row;return[t._v(" "+t._s(a.range[0])+" - "+t._s(a.range[1])+t._s(a.unit)+" ")]}}],null,!0)})})),1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableView",attrs:{data:t.nutrientTableData[1],stripe:"","header-row-class-name":"ps-table-header-row"}},t._l(t.nutrientTableSetting,(function(a,n){return e("table-column",{key:n,attrs:{col:a},scopedSlots:t._u([{key:"value",fn:function(a){var n=a.row;return[e("div",{staticClass:"text-right"},[e("span",{staticClass:"m-r-10"},[t._v(t._s(n.value)+t._s(n.unit))]),e("i",{class:["up"===t.computedStatus(n)?"el-icon-top":"el-icon-bottom"],style:"up"===t.computedStatus(n)?{color:"#D9001B"}:{color:"#F59A23"}})])]}},{key:"range",fn:function(e){var a=e.row;return[t._v(" "+t._s(a.range[0])+" - "+t._s(a.range[1])+t._s(a.unit)+" ")]}}],null,!0)})})),1)],1)]),e("div",{staticClass:"m-b-20"},[e("div",{staticClass:"f-w-700 font-size-18 m-b-10"},[t._v("饮食分析")]),e("div",{staticClass:"bg-grey"},t._l(t.nutritionAnalyzeTextData,(function(a,n){return e("div",{key:n},[t._v(" "+t._s(n+1)+". "+t._s(a)+" ")])})),0)]),"week"===t.type?e("div",{staticClass:"m-b-20"},[e("div",{staticClass:"f-w-700 font-size-18 m-b-10"},[t._v("指导建议")]),e("div",{staticClass:"bg-grey"},t._l(t.suggestionTextData,(function(a,n){return e("div",{key:n},[t._v(" "+t._s(n+1)+". "+t._s(a)+" ")])})),0)]):t._e(),e("div",{staticClass:"ps-el-drawer-footer ps-flex-align-c flex-align-c"},[e("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:t.closeDrawer}},[t._v("关闭")])],1)])])],1)},i=[],s=a("5a0c"),r=a.n(s),l=a("af32"),o=a("ed08");function c(t){return v(t)||f(t)||d(t)||u()}function u(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(t,e){if(t){if("string"==typeof t)return y(t,e);var a={}.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?y(t,e):void 0}}function f(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function v(t){if(Array.isArray(t))return y(t)}function y(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,n=Array(e);a<e;a++)n[a]=t[a];return n}var _={components:{SpecialDatePicker:l["default"]},props:{isShow:Boolean,selectedId:Number},data:function(){return{isLoading:!1,type:"day",foodList:[],dietData:[],analysisData:[],energyTableData:[],nutrientTableData:[],nutritionAnalyzeTextData:[],suggestionTextData:[],energyTableSetting:[{label:"三大供能营养素",align:"right",key:"text"},{label:"摄入量",align:"right",key:"value",type:"slot",slotName:"value"},{label:"推荐量",align:"right",key:"value_rate",type:"slot",slotName:"value_rate"},{label:"摄入供能比",align:"right",key:"range",type:"slot",slotName:"range"},{label:"推荐供能比",align:"right",key:"range_rate",type:"slot",slotName:"range_rate"}],nutrientTableSetting:[{label:"营养素名称",align:"right",key:"text"},{label:"摄入量",align:"right",key:"value",type:"slot",slotName:"value"},{label:"推荐量",align:"right",key:"range",type:"slot",slotName:"range"}],pickerOptions:this.disableDate()}},computed:{visible:{get:function(){return this.isShow},set:function(t){this.$emit("update:isShow",t)}},computedWeek:function(){var t=this;return function(e){var a=r()(e).startOf("week").format("YYYY-MM-DD"),n=r()(e).endOf("week").format("YYYY-MM-DD");return"day"===t.type?r()(e).format("YYYY-MM-DD"):"".concat(a," 至 ").concat(n)}},computedStatus:function(){return function(t){var e=t.value,a=t.range,n="";return n=e<a[0]?"down":e>a[1]?"up":"none",n}}},methods:{disableDate:function(){var t=this;return{disabledDate:function(e){return"week"===t.type?e.getTime()>new Date(r()().endOf("week")).getTime():e.getTime()>Date.now()}}},setHealthyInfo:function(t){if("day"===this.type){if(this.foodList=t?Object(o["f"])(t.food_data):[],this.dietData=t?Object(o["f"])(t.food_diversity.data):[],this.analysisData=t?Object(o["f"])(t.energy_kcal_data):[],this.energyTableData=t?Object(o["f"])(t.three_nutrition):[],t.total_nutrition&&0!==t.total_nutrition.length){var e=t.total_nutrition.filter((function(t,e){return e%2===0})),a=t.total_nutrition.filter((function(t,e){return e%2!==0}));this.nutrientTableData=[e,a]}else this.nutrientTableData=[];var n=[];t&&t.nutrition_analyze_text&&(n=[].concat(c(t.nutrition_analyze_text.meal_type||[]),c(t.nutrition_analyze_text.category||[]),c(t.nutrition_analyze_text.full||[]))),this.nutritionAnalyzeTextData=t?Object(o["f"])(n):[]}else{if(this.dietData=t?Object(o["f"])(t.food_diversity.data):[],this.analysisData=t?Object(o["f"])(t.energy_kcal_data):[],this.energyTableData=t?Object(o["f"])(t.three_nutrition):[],t&&t.total_nutrition&&0!==t.total_nutrition.length){var i=t.total_nutrition.filter((function(t,e){return e%2===0})),s=t.total_nutrition.filter((function(t,e){return e%2!==0}));this.nutrientTableData=[i,s]}else this.nutrientTableData=[];var r=[];t&&t.nutrition_analyze_text&&(r=[].concat(c(t.nutrition_analyze_text.category||[]),c(t.nutrition_analyze_text.full||[]))),this.nutritionAnalyzeTextData=t?Object(o["f"])(r):[],this.suggestionTextData=t?Object(o["f"])(t.nutrition_guid_text):[]}},closeDrawer:function(){this.type="day",this.visible=!1}}},g=_,b=(a("2157"),a("2877")),p=Object(b["a"])(g,n,i,!1,null,"40b6775c",null);e["default"]=p.exports}}]);