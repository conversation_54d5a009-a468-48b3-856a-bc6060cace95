(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-distribution-constantsConfig"],{"8fb5":function(e,a,l){"use strict";l.r(a),l.d(a,"recentSevenDay",(function(){return s})),l.d(a,"GetMealSearchForm",(function(){return c})),l.d(a,"TransverseSearchForm",(function(){return o}));var t=l("5a0c"),s=[t().subtract(7,"day").format("YYYY-MM-DD"),t().format("YYYY-MM-DD")],c={select_time:{type:"daterange",label:"用餐时间",value:s},org_ids:{type:"organizationSelect",multiple:!1,isLazy:!0,label:"来源",value:"",placeholder:"请选择来源"},card_status:{type:"select",label:"配送区域",value:"",placeholder:"请选择配送区域",collapseTags:!0,dataList:[]}},o={select_time:{type:"daterange",label:"用餐时间",value:s},org_ids:{type:"organizationSelect",multiple:!1,isLazy:!0,label:"来源",value:"",placeholder:"请选择来源"},card_status:{type:"select",label:"配送区域",value:"",placeholder:"请选择配送区域",collapseTags:!0,dataList:[]},meal:{type:"select",label:"餐段",value:"",placeholder:"请选择餐段",collapseTags:!0,dataList:[]},one:{type:"select",label:"一级",value:"",placeholder:"请选择一级",collapseTags:!0,dataList:[]},two:{type:"select",label:"二级",value:"",placeholder:"请选择二级",collapseTags:!0,dataList:[]},three:{type:"select",label:"三级",value:"",placeholder:"请选择三级",collapseTags:!0,dataList:[]},four:{type:"select",label:"四级",value:"",placeholder:"请选择四级",collapseTags:!0,dataList:[]},five:{type:"select",label:"五级",value:"",placeholder:"请选择五级",collapseTags:!0,dataList:[]}}}}]);