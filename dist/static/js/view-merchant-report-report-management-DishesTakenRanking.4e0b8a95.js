(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-report-report-management-DishesTakenRanking","view-merchant-home-page-components-FoodCategory","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-meal-report-MealPackageRule","view-merchant-order-RechargeOrder","view-merchant-order-RefundOrder","view-merchant-report-report-management-FoodSaleRanking","view-merchant-report-report-management-constantsConfig"],{"174c":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"carbinding container-wrapper"},[t("refresh-tool",{on:{refreshPage:function(t){return e.refreshHandler(!0)}}}),t("search-form",{ref:"searchRef",attrs:{"form-setting":e.searchFormSetting},on:{search:e.searchHandler,reset:e.resetHandler}},[t("div",{staticClass:"ps-flex",attrs:{slot:"append"},slot:"append"},[t("div",{staticClass:"el-form-item__label w-80 m-b-20"},[e._v("菜品分类")]),t("food-category",{ref:"foodCateGory",staticStyle:{width:"200px"},on:{input:e.changeCategory}})],1)]),t("div",{staticClass:"table-wrapper"},[t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[e._v("数据列表")]),t("div",{staticClass:"align-r"},[t("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_order.manage_report.food_payment_weight_ranking_list_export"],expression:"['background_order.manage_report.food_payment_weight_ranking_list_export']"}],attrs:{color:"origin"},on:{click:function(t){return e.handlerExport()}}},[e._v("导出报表")])],1)]),t("div",{staticClass:"table-content"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData,height:"460",stripe:"","header-row-class-name":"ps-table-header-row","empty-text":"暂无数据，请查询"}},[t("el-table-column",{attrs:{label:"序号",align:"center","header-align":"center",width:"80px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.$index+1+(e.currentPage-1)*e.pageSize)+" ")]}}])}),e._l(e.tableSettings,(function(r,n){return t("table-column",{key:n,attrs:{col:r},scopedSlots:e._u([{key:"foodWeight",fn:function(t){var r=t.row;return[e._v(" "+e._s(e._f("0")(r.food_weight))+" g ")]}},{key:"averageWeight",fn:function(t){var r=t.row;return[e._v(" "+e._s(e._f("0")(r.average_weight))+" g ")]}}],null,!0)})}))],2)],1),t("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[t("el-pagination",{staticClass:"ps-text",attrs:{"current-page":e.currentPage,"page-sizes":[10,20,30,40],"page-size":e.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":e.handleSizeChange,"current-change":e.onPaginationChange}})],1)])],1)},a=[],o=r("20e4"),i=r("ed08"),c=r("f63a"),l=r("87ac"),u=r("d4e4");function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function f(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */f=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function h(e,t,r,n){var o=t&&t.prototype instanceof b?t:b,i=Object.create(o.prototype),c=new C(n||[]);return a(i,"_invoke",{value:j(e,r,c)}),i}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=h;var p="suspendedStart",y="suspendedYield",g="executing",m="completed",v={};function b(){}function w(){}function _(){}var k={};u(k,i,(function(){return this}));var x=Object.getPrototypeOf,S=x&&x(x(A([])));S&&S!==r&&n.call(S,i)&&(k=S);var L=_.prototype=b.prototype=Object.create(k);function O(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function E(e,t){function r(a,o,i,c){var l=d(e[a],e,o);if("throw"!==l.type){var u=l.arg,f=u.value;return f&&"object"==s(f)&&n.call(f,"__await")?t.resolve(f.__await).then((function(e){r("next",e,i,c)}),(function(e){r("throw",e,i,c)})):t.resolve(f).then((function(e){u.value=e,i(u)}),(function(e){return r("throw",e,i,c)}))}c(l.arg)}var o;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){r(e,n,t,a)}))}return o=o?o.then(a,a):a()}})}function j(t,r,n){var a=p;return function(o,i){if(a===g)throw Error("Generator is already running");if(a===m){if("throw"===o)throw i;return{value:e,done:!0}}for(n.method=o,n.arg=i;;){var c=n.delegate;if(c){var l=P(c,n);if(l){if(l===v)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===p)throw a=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=g;var u=d(t,r,n);if("normal"===u.type){if(a=n.done?m:y,u.arg===v)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(a=m,n.method="throw",n.arg=u.arg)}}}function P(t,r){var n=r.method,a=t.iterator[n];if(a===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,P(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var o=d(a,t.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,v;var i=o.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,v):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function D(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function C(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function A(t){if(t||""===t){var r=t[i];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,o=function r(){for(;++a<t.length;)if(n.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(s(t)+" is not iterable")}return w.prototype=_,a(L,"constructor",{value:_,configurable:!0}),a(_,"constructor",{value:w,configurable:!0}),w.displayName=u(_,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,u(e,l,"GeneratorFunction")),e.prototype=Object.create(L),e},t.awrap=function(e){return{__await:e}},O(E.prototype),u(E.prototype,c,(function(){return this})),t.AsyncIterator=E,t.async=function(e,r,n,a,o){void 0===o&&(o=Promise);var i=new E(h(e,r,n,a),o);return t.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},O(L),u(L,l,"Generator"),u(L,i,(function(){return this})),u(L,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=A,C.prototype={constructor:C,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(D),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(n,a){return c.type="throw",c.arg=t,r.next=n,a&&(r.method="next",r.arg=e),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],c=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),u=n.call(i,"finallyLoc");if(l&&u){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),D(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;D(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:A(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),v}},t}function h(e,t){return m(e)||g(e,t)||p(e,t)||d()}function d(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function p(e,t){if(e){if("string"==typeof e)return y(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?y(e,t):void 0}}function y(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function g(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,o,i,c=[],l=!0,u=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=o.call(r)).done)&&(c.push(n.value),c.length!==t);l=!0);}catch(e){u=!0,a=e}finally{try{if(!l&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw a}}return c}}function m(e){if(Array.isArray(e))return e}function v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function b(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v(Object(r),!0).forEach((function(t){w(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function w(e,t,r){return(t=_(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _(e){var t=k(e,"string");return"symbol"==s(t)?t:t+""}function k(e,t){if("object"!=s(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function x(e,t,r,n,a,o,i){try{var c=e[o](i),l=c.value}catch(e){return void r(e)}c.done?t(l):Promise.resolve(l).then(n,a)}function S(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var o=e.apply(t,r);function i(e){x(o,n,a,i,c,"next",e)}function c(e){x(o,n,a,i,c,"throw",e)}i(void 0)}))}}var L={name:"DishesTakenRanking",data:function(){return{isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[],tableSettings:Object(i["f"])(o["TABLE_HEAD_DATA_DISHES_TAKEN"]),searchFormSetting:Object(i["f"])(o["SEARCH_FORM_SET_DATA_DISHES_TAKEN"]),chooseCategory:[]}},components:{FoodCategory:u["default"]},mixins:[c["a"],l["a"]],created:function(){},methods:{refreshHandler:function(e){this.currentPage=1,e&&this.$refs.searchRef.resetForm(),this.$refs.foodCateGory&&(this.chooseCategory=[],this.$refs.foodCateGory.reset()),this.tableData=[],this.initLoad()},initLoad:function(){this.getFoodSaleRanking()},onPaginationChange:function(e){this.currentPage=e,this.getFoodSaleRanking()},handleSizeChange:function(e){this.pageSize=e,this.getFoodSaleRanking()},getFoodSaleRanking:function(){var e=this;return S(f().mark((function t(){var r,n,a,o,c,l;return f().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.isLoading){t.next=2;break}return t.abrupt("return");case 2:return e.isLoading=!0,r=b(b({},e.formatQueryParams(e.searchFormSetting)),{},{page:e.currentPage,page_size:e.pageSize}),t.next=6,Object(i["Z"])(e.$apis.apiBackgroundReportCenterManageReportFoodPaymentWeightRankingListPost(r));case 6:if(n=t.sent,a=h(n,2),o=a[0],c=a[1],e.isLoading=!1,!o){t.next=14;break}return e.$message.error(o.message),t.abrupt("return");case 14:0===c.code?(l=c.data.results||[],e.tableData=Object(i["f"])(l),e.totalCount=c.data.count||-1):e.$message.error(c.msg);case 15:case"end":return t.stop()}}),t)})))()},formatQueryParams:function(e){var t={};for(var r in e)""!==e[r].value&&null!==e[r].value&&0!==e[r].value.length&&("select_time"===r?(t.start_date=e[r].value[0],t.end_date=e[r].value[1]):t[r]=e[r].value);return this.chooseCategory&&this.chooseCategory.length>0&&(t.category=this.chooseCategory),t},searchHandler:Object(i["d"])((function(e){if(e&&"search"===e){if(0===this.searchFormSetting.select_time.value.length)return this.$message.error("请选择就餐时间");this.currentPage=1,this.initLoad()}}),300),resetHandler:function(){this.currentPage=1,this.tableData=[],this.$refs.foodCateGory&&(this.chooseCategory=[],this.$refs.foodCateGory.reset())},handlerExport:function(){if(!this.tableData||0===this.tableData.length)return this.$message.error("没有可导出的数据");var e={type:"ExportDishesTakenRanking",params:b(b(b({payment_order_type:this.currentType},this.sort),this.formatQueryParams(this.searchFormSetting)),{},{page:this.currentPage,page_size:this.pageSize})};this.exportHandle(e)},changeCategory:function(e){this.chooseCategory=e}}},O=L,E=(r("8699"),r("2877")),j=Object(E["a"])(O,n,a,!1,null,"eb891a08",null);t["default"]=j.exports},"20e4":function(e,t,r){"use strict";r.r(t),r.d(t,"recentSevenDay",(function(){return o})),r.d(t,"FoodSaleRanking",(function(){return i})),r.d(t,"SummaryOfSales",(function(){return c})),r.d(t,"TABLE_HEAD_DATA_DISHES_TAKEN",(function(){return l})),r.d(t,"SEARCH_FORM_SET_DATA_DISHES_TAKEN",(function(){return u}));var n=r("5a0c"),a=r("c9d9"),o=[n().subtract(7,"day").format("YYYY-MM-DD"),n().format("YYYY-MM-DD")],i={select_time:{type:"daterange",label:"就餐时间",value:o,format:"yyyy-MM-dd",clearable:!1},meal_type_list:{type:"select",label:"餐段",value:[],multiple:!0,placeholder:"请选择",collapseTags:!0,dataList:a["a"]},org_ids:{type:"organizationSelect",value:[],label:"消费点",listNameKey:"name",listValueKey:"id",dataList:[],checkStrictly:!0,multiple:!0,collapseTags:!0,clearable:!0},food_name:{type:"input",value:"",label:"菜品名称",maxlength:20,placeholder:"请输入菜品名称"}},c={select_time:{type:"daterange",label:"就餐时间",value:o,format:"yyyy-MM-dd",clearable:!1},meal_type_list:{type:"select",label:"餐段",value:[],multiple:!0,placeholder:"请选择",collapseTags:!0,dataList:a["a"]},org_ids:{type:"organizationSelect",value:[],label:"消费点",listNameKey:"name",listValueKey:"id",dataList:[],checkStrictly:!0,multiple:!0,collapseTags:!0,clearable:!0}},l=[{label:"所属组织",key:"org_name"},{label:"菜品一级分类",key:"sort"},{label:"菜品二级分类",key:"category"},{label:"菜品名称",key:"name"},{label:"取用量",key:"food_weight",type:"slot",slotName:"foodWeight"},{label:"取菜人数",key:"use_count"},{label:"平均取用量",key:"average_weight",type:"slot",slotName:"averageWeight"}],u={select_time:{type:"daterange",label:"就餐时间",value:[],format:"yyyy-MM-dd",clearable:!1},food_name:{type:"input",value:"",label:"菜品名称",maxlength:20,placeholder:"请输入菜品名称"},org_ids:{type:"organizationSelect",value:[],label:"所属组织",listNameKey:"name",listValueKey:"id",dataList:[],checkStrictly:!0,multiple:!0,collapseTags:!0,clearable:!0},meal_type:{type:"select",label:"餐段",value:[],multiple:!0,placeholder:"请选择",collapseTags:!0,dataList:a["a"]}}},4960:function(e,t,r){},8699:function(e,t,r){"use strict";r("c569")},"87ac":function(e,t,r){"use strict";var n=r("ed08"),a=r("2f62");function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function i(e,t){return f(e)||s(e,t)||l(e,t)||c()}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function l(e,t){if(e){if("string"==typeof e)return u(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?u(e,t):void 0}}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function s(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,o,i,c=[],l=!0,u=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=o.call(r)).done)&&(c.push(n.value),c.length!==t);l=!0);}catch(e){u=!0,a=e}finally{try{if(!l&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw a}}return c}}function f(e){if(Array.isArray(e))return e}function h(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */h=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},c=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,r){return e[t]=r}}function f(e,t,r,n){var o=t&&t.prototype instanceof b?t:b,i=Object.create(o.prototype),c=new C(n||[]);return a(i,"_invoke",{value:j(e,r,c)}),i}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var p="suspendedStart",y="suspendedYield",g="executing",m="completed",v={};function b(){}function w(){}function _(){}var k={};s(k,c,(function(){return this}));var x=Object.getPrototypeOf,S=x&&x(x(A([])));S&&S!==r&&n.call(S,c)&&(k=S);var L=_.prototype=b.prototype=Object.create(k);function O(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function E(e,t){function r(a,i,c,l){var u=d(e[a],e,i);if("throw"!==u.type){var s=u.arg,f=s.value;return f&&"object"==o(f)&&n.call(f,"__await")?t.resolve(f.__await).then((function(e){r("next",e,c,l)}),(function(e){r("throw",e,c,l)})):t.resolve(f).then((function(e){s.value=e,c(s)}),(function(e){return r("throw",e,c,l)}))}l(u.arg)}var i;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){r(e,n,t,a)}))}return i=i?i.then(a,a):a()}})}function j(t,r,n){var a=p;return function(o,i){if(a===g)throw Error("Generator is already running");if(a===m){if("throw"===o)throw i;return{value:e,done:!0}}for(n.method=o,n.arg=i;;){var c=n.delegate;if(c){var l=P(c,n);if(l){if(l===v)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===p)throw a=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=g;var u=d(t,r,n);if("normal"===u.type){if(a=n.done?m:y,u.arg===v)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(a=m,n.method="throw",n.arg=u.arg)}}}function P(t,r){var n=r.method,a=t.iterator[n];if(a===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,P(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var o=d(a,t.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,v;var i=o.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,v):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function D(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function C(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function A(t){if(t||""===t){var r=t[c];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,i=function r(){for(;++a<t.length;)if(n.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(o(t)+" is not iterable")}return w.prototype=_,a(L,"constructor",{value:_,configurable:!0}),a(_,"constructor",{value:w,configurable:!0}),w.displayName=s(_,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,s(e,u,"GeneratorFunction")),e.prototype=Object.create(L),e},t.awrap=function(e){return{__await:e}},O(E.prototype),s(E.prototype,l,(function(){return this})),t.AsyncIterator=E,t.async=function(e,r,n,a,o){void 0===o&&(o=Promise);var i=new E(f(e,r,n,a),o);return t.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},O(L),s(L,u,"Generator"),s(L,c,(function(){return this})),s(L,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=A,C.prototype={constructor:C,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(D),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(n,a){return c.type="throw",c.arg=t,r.next=n,a&&(r.method="next",r.arg=e),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],c=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),u=n.call(i,"finallyLoc");if(l&&u){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),D(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;D(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:A(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),v}},t}function d(e,t,r,n,a,o,i){try{var c=e[o](i),l=c.value}catch(e){return void r(e)}c.done?t(l):Promise.resolve(l).then(n,a)}function p(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var o=e.apply(t,r);function i(e){d(o,n,a,i,c,"next",e)}function c(e){d(o,n,a,i,c,"throw",e)}i(void 0)}))}}function y(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function g(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y(Object(r),!0).forEach((function(t){m(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function m(e,t,r){return(t=v(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function v(e){var t=b(e,"string");return"symbol"==o(t)?t:t+""}function b(e,t){if("object"!=o(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}t["a"]={data:function(){return{currentTableSetting:[],dialogPrintVisible:!1}},computed:g({},Object(a["c"])(["userInfo"])),mounted:function(){},methods:{initPrintSetting:function(){var e=this;return p(h().mark((function t(){var r,a;return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return r=[],t.next=3,e.getPrintSettingInfo();case 3:r=t.sent;try{r=r?Object(n["E"])(r):Object(n["E"])(e.tableSetting)}catch(o){r=Object(n["E"])(e.tableSetting)}r.length<12?(a=Object(n["m"])(e.tableSetting,r),a=e.deleteWidthKey(a),e.currentTableSetting=a):e.currentTableSetting=Object(n["m"])(e.tableSetting,r);case 6:case"end":return t.stop()}}),t)})))()},getPrintSettingInfo:function(){var e=this;return p(h().mark((function t(){var r,a,o,c,l;return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return r=null,t.next=3,Object(n["Z"])(e.$apis.apiBackgroundAdminAccountGetAccountPrintInfoPost({id:e.userInfo.account_id,print_key:e.printType}));case 3:if(a=t.sent,o=i(a,2),c=o[0],l=o[1],!c){t.next=10;break}return e.$message.error(c.message),t.abrupt("return",r);case 10:return 0===l.code?r=l.data:e.$message.error(l.msg),t.abrupt("return",r);case 12:case"end":return t.stop()}}),t)})))()},setPrintSettingInfo:function(e,t){var r=this;return p(h().mark((function a(){var o,c,l,u;return h().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,Object(n["Z"])(r.$apis.apiBackgroundAdminAccountSetAccountPrintInfoPost(g({id:r.userInfo.account_id,print_key:r.printType,print_list:e},t)));case 2:if(o=a.sent,c=i(o,2),l=c[0],u=c[1],!l){a.next=9;break}return r.$message.error(l.message),a.abrupt("return");case 9:0===u.code?r.$message.success("设置成功"):r.$message.error(u.msg);case 10:case"end":return a.stop()}}),a)})))()},openPrintSetting:function(){this.dialogPrintVisible=!0},confirmPrintDialog:function(e,t){var r=this;return p(h().mark((function a(){var o;return h().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(!e){a.next=6;break}return o=Object(n["f"])(e),o.length<12&&(o=r.deleteWidthKey(o)),a.next=5,r.setPrintSettingInfo(o,t);case 5:r.currentTableSetting=o;case 6:case"end":return a.stop()}}),a)})))()},deleteWidthKey:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"children";function r(e){e.map((function(e){e.width&&delete e.width,e.minWidth&&delete e.minWidth,e[t]&&e[t].length>0&&r(e[t])}))}return r(e),e},indexMethod:function(e){return(this.page-1)*this.pageSize+(e+1)},setCollectData:function(e){var t=this;this.collect.forEach((function(r){e.data.collect&&void 0!==e.data.collect[r.key]&&t.$set(r,"value",e.data.collect[r.key])}))},setSummaryData:function(e){var t=e.data.collect;t[this.tableSetting[0].key]="合计",this.tableData.length&&this.tableData.push(t)}},beforeDestroy:function(){}}},"894e":function(e,t,r){"use strict";r("4960")},c569:function(e,t,r){},c9d9:function(e,t,r){"use strict";r.d(t,"a",(function(){return o})),r.d(t,"d",(function(){return i})),r.d(t,"b",(function(){return c})),r.d(t,"c",(function(){return l})),r.d(t,"e",(function(){return u})),r.d(t,"f",(function(){return s})),r.d(t,"g",(function(){return f}));var n=r("5a0c"),a=r("da92"),o=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],i=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],c={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},l=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],u=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],s=(n().subtract(7,"day").format("YYYY-MM-DD"),n().format("YYYY-MM-DD"),function(e){return e?"number"===typeof e?a["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:a["a"].divide(e,100).toFixed(2):"0.00"}),f=function(e){return a["a"].times(e,100)}},d4e4:function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("div",[t("tree-select",e._g(e._b({attrs:{multiple:!0,options:e.treeList,limit:1,limitText:function(e){return"+"+e},"default-expand-level":1,normalizer:e.normalizer,placeholder:"请选择","no-children-text":"暂无更多",noOptionsText:"暂无分类",noResultsText:"暂无更多","search-nested":"","value-consists-of":"LEAF_PRIORITY",appendToBody:!0},model:{value:e.selectData,callback:function(t){e.selectData=t},expression:"selectData"}},"tree-select",e.$attrs,!1),e.$listeners))],1)},a=[],o=r("ed08"),i={name:"",components:{},props:{onlyFirstList:{type:Boolean,default:!1}},data:function(){return{selectData:[],treeList:[],normalizer:function(e){return{id:e.id,label:e.name,children:e.children}},firstLevelList:[],secondLevelList:[]}},computed:{},watch:{},created:function(){this.getCategory()},mounted:function(){},methods:{getCategory:function(){var e=this,t={page:1,page_size:999999},r=this.$apis.apiBackgroundFoodFoodSortListPost(t),n=this.$apis.apiBackgroundFoodFoodCategoryListPost(t);this.onlyFirstList?r.then((function(t){0===t.code&&(e.firstLevelList=t.data.results),e.treeList=e.arrayToTree(e.firstLevelList,[])})):Promise.all([r,n]).then((function(t){t.forEach((function(t,r){if(0===t.code)switch(r){case 0:e.firstLevelList=t.data.results;break;case 1:e.secondLevelList=t.data.results;break}})),e.treeList=e.arrayToTree(e.firstLevelList,e.secondLevelList)})).catch((function(e){}))},arrayToTree:function(e,t){var r=Object(o["f"])(e);return t.forEach((function(e){for(var t=0;t<r.length;t++){var n=r[t];e.sort==n.id?(n.isDisabled=!1,n.children?n.children.push(e):n.children=[e]):n.children||(n.children=[],n.isDisabled=!0)}})),r},reset:function(){this.selectData=[]}}},c=i,l=(r("894e"),r("2877")),u=Object(l["a"])(c,n,a,!1,null,null,null);t["default"]=u.exports}}]);