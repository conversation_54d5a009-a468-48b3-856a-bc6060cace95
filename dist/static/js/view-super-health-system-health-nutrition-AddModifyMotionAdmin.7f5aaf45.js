(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-health-nutrition-AddModifyMotionAdmin"],{3177:function(t,e,r){},b2a2:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"add_modify_motion_admin container-wrapper"},[e("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formIngredients",attrs:{rules:t.formRuls,model:t.formData,size:"small","label-width":"80px"}},[e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("基本信息")])]),e("div",{staticStyle:{padding:"0 20px"}},[e("el-form-item",{attrs:{label:"封面",prop:"image"}},[e("el-upload",{ref:"uploadFood",staticClass:"avatar-uploader",attrs:{data:t.uploadParams,action:t.actionUrl,"show-file-list":!1,"on-success":t.handleAvatarSuccess,"before-upload":t.beforeAvatarUpload}},[t.formData.image?e("img",{staticClass:"avatar",attrs:{src:t.formData.image}}):e("i",{staticClass:"el-icon-plus avatar-uploader-icon"})])],1),e("el-form-item",{attrs:{label:"运动名称",prop:"name"}},[e("el-input",{staticClass:"ps-input p-r-48",staticStyle:{width:"240px"},attrs:{placeholder:"请输入运动名称","show-word-limit":""},model:{value:t.formData.name,callback:function(e){t.$set(t.formData,"name",e)},expression:"formData.name"}})],1),e("el-form-item",{attrs:{label:"数量",prop:"count"}},[e("el-input",{staticClass:"ps-input p-r-48",staticStyle:{width:"240px"},attrs:{placeholder:"请输入数量","show-word-limit":""},model:{value:t.formData.count,callback:function(e){t.$set(t.formData,"count",e)},expression:"formData.count"}})],1),e("el-form-item",{attrs:{label:"单位",prop:"counting_unit"}},[e("el-select",{staticClass:"ps-select",attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select"},model:{value:t.formData.counting_unit,callback:function(e){t.$set(t.formData,"counting_unit",e)},expression:"formData.counting_unit"}},[e("el-option",{attrs:{label:"分钟",value:"minute"}}),e("el-option",{attrs:{label:"公里",value:"kilometer"}}),e("el-option",{attrs:{label:"次",value:"freq"}}),e("el-option",{attrs:{label:"组",value:"group"}}),e("el-option",{attrs:{label:"套",value:"set"}})],1)],1),e("el-form-item",{attrs:{label:"热量",prop:"energy_kcal"}},[e("el-input",{staticClass:"ps-input p-r-48",staticStyle:{width:"240px"},attrs:{placeholder:"请输入热量",maxlength:"40","show-word-limit":""},model:{value:t.formData.energy_kcal,callback:function(e){t.$set(t.formData,"energy_kcal",e)},expression:"formData.energy_kcal"}},[e("template",{slot:"append"},[t._v("千卡")])],2)],1)],1)]),e("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[e("el-button",{staticStyle:{width:"120px"},on:{click:t.closeHandler}},[t._v("取消")]),e("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:t.submitHandler}},[t._v(" "+t._s("add"===t.type?"添加":"编辑")+" ")])],1)])],1)},a=[],o=r("ed08");function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function c(t,e){return p(t)||f(t,e)||l(t,e)||s()}function s(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function l(t,e){if(t){if("string"==typeof t)return u(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?u(t,e):void 0}}function u(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function f(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,o,i,c=[],s=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);s=!0);}catch(t){l=!0,a=t}finally{try{if(!s&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(l)throw a}}return c}}function p(t){if(Array.isArray(t))return t}function d(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */d=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},c=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var o=e&&e.prototype instanceof b?e:b,i=Object.create(o.prototype),c=new D(n||[]);return a(i,"_invoke",{value:j(t,r,c)}),i}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var h="suspendedStart",m="suspendedYield",y="executing",g="completed",v={};function b(){}function w(){}function x(){}var _={};u(_,c,(function(){return this}));var k=Object.getPrototypeOf,L=k&&k(k(P([])));L&&L!==r&&n.call(L,c)&&(_=L);var S=x.prototype=b.prototype=Object.create(_);function $(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function E(t,e){function r(a,o,c,s){var l=p(t[a],t,o);if("throw"!==l.type){var u=l.arg,f=u.value;return f&&"object"==i(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,c,s)}),(function(t){r("throw",t,c,s)})):e.resolve(f).then((function(t){u.value=t,c(u)}),(function(t){return r("throw",t,c,s)}))}s(l.arg)}var o;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return o=o?o.then(a,a):a()}})}function j(e,r,n){var a=h;return function(o,i){if(a===y)throw Error("Generator is already running");if(a===g){if("throw"===o)throw i;return{value:t,done:!0}}for(n.method=o,n.arg=i;;){var c=n.delegate;if(c){var s=O(c,n);if(s){if(s===v)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===h)throw a=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=y;var l=p(e,r,n);if("normal"===l.type){if(a=n.done?g:m,l.arg===v)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(a=g,n.method="throw",n.arg=l.arg)}}}function O(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,O(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var o=p(a,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,v;var i=o.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function A(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function C(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function D(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(A,this),this.reset(!0)}function P(e){if(e||""===e){var r=e[c];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,o=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(i(e)+" is not iterable")}return w.prototype=x,a(S,"constructor",{value:x,configurable:!0}),a(x,"constructor",{value:w,configurable:!0}),w.displayName=u(x,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,x):(t.__proto__=x,u(t,l,"GeneratorFunction")),t.prototype=Object.create(S),t},e.awrap=function(t){return{__await:t}},$(E.prototype),u(E.prototype,s,(function(){return this})),e.AsyncIterator=E,e.async=function(t,r,n,a,o){void 0===o&&(o=Promise);var i=new E(f(t,r,n,a),o);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},$(S),u(S,l,"Generator"),u(S,c,(function(){return this})),u(S,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=P,D.prototype={constructor:D,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(C),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return c.type="throw",c.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],c=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var s=n.call(i,"catchLoc"),l=n.call(i,"finallyLoc");if(s&&l){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),C(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;C(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:P(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function h(t,e,r,n,a,o,i){try{var c=t[o](i),s=c.value}catch(t){return void r(t)}c.done?e(s):Promise.resolve(s).then(n,a)}function m(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var o=t.apply(e,r);function i(t){h(o,n,a,i,c,"next",t)}function c(t){h(o,n,a,i,c,"throw",t)}i(void 0)}))}}var y={name:"SuperAddEditArticle",data:function(){return{isLoading:!1,type:"add",formData:{image:"",name:"",count:"",counting_unit:"",energy_kcal:""},formRuls:{name:[{required:!0,message:"请输入运动名称",trigger:"blur"}],count:[{required:!0,message:"请输入数量",trigger:"blur"}],counting_unit:[{required:!0,message:"请选择是单位",trigger:"blur"}],energy_kcal:[{required:!0,message:"请输入热量",trigger:"blur"}]},actionUrl:"",uploadParams:{}}},created:function(){this.getUploadToken(),this.type=this.$route.query.type,this.initLoad()},mounted:function(){},methods:{initLoad:function(){if("modify"===this.type){var t=this.$decodeQuery(this.$route.query.data);this.formData={id:t.id,image:t.image,name:t.name,count:t.count,counting_unit:t.counting_unit,energy_kcal:t.energy_kcal}}},searchHandle:Object(o["d"])((function(){this.currentPage=1}),300),getUploadToken:function(){var t=this;return m(d().mark((function e(){var r;return d().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.getUploadToken({prefix:"jpeg/png"});case 2:r=e.sent,0===r.code?(t.actionUrl=r.data.host,t.uploadParams={key:r.data.prefix+(new Date).getTime()+Math.floor(150*Math.random()),prefix:r.data.prefix,policy:r.data.policy,OSSAccessKeyId:r.data.accessid,signature:r.data.signature,callback:r.data.callback,success_action_status:"200"}):t.$message.error(r.msg);case 4:case"end":return e.stop()}}),e)})))()},handleAvatarSuccess:function(t,e){0===t.code?(this.$refs.uploadFood.clearFiles(),this.formData.image=t.data.public_url,this.getUploadToken()):this.$message.error(t.msg)},beforeAvatarUpload:function(t){var e="image/jpeg"===t.type||"image/png"===t.type,r=t.size/1024/1024<2;return e||this.$message.error("上传头像图片只能是 JPG 格式!"),r||this.$message.error("上传头像图片大小不能超过 2MB!"),e&&r},addModifyArticle:function(t){var e=this;return m(d().mark((function r(){var n,a,i,s,l,u,f,p;return d().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(e.isLoading=!0,n="",a=c(n,2),i=a[0],s=a[1],"add"!==e.type){r.next=12;break}return r.next=6,Object(o["Z"])(e.$apis.apiBackgroundAdminSportsAddPost(t));case 6:l=r.sent,u=c(l,2),i=u[0],s=u[1],r.next=19;break;case 12:return r.next=15,Object(o["Z"])(e.$apis.apiBackgroundAdminSportsModifyPost(t));case 15:f=r.sent,p=c(f,2),i=p[0],s=p[1];case 19:if(e.isLoading=!1,!i){r.next=23;break}return e.$message.error(i.message),r.abrupt("return");case 23:0===s.code?(e.$message.success(s.msg),e.$closeCurrentTab(e.$route.path)):e.$message.error(s.msg);case 24:case"end":return r.stop()}}),r)})))()},submitHandler:function(){var t=this;this.$refs.formIngredients.validate((function(e){if(e){if(t.isLoading)return t.$message.error("请勿重复提交！");t.addModifyArticle(t.formData)}}))},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,r,n){"confirm"===e?t.$closeCurrentTab(t.$route.path):r.confirmButtonLoading||n()}}).then((function(t){})).catch((function(t){}))}}},g=y,v=(r("b465"),r("2877")),b=Object(v["a"])(g,n,a,!1,null,null,null);e["default"]=b.exports},b465:function(t,e,r){"use strict";r("3177")}}]);