(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-member-center-MemberPermission","view-super-health-system-member-center-constants"],{"59a2":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"SuperMemberPermission container-wrapper"},[t("refresh-tool",{on:{refreshPage:e.refreshHandle}}),t("search-form",{ref:"searchRef",attrs:{"form-setting":e.searchFormSetting},on:{search:e.searchHandle}}),t("div",{staticClass:"table-wrapper"},[t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[e._v("数据列表")]),t("div",{staticClass:"align-r"},[t("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(t){return e.gotoAddOrEdit("add")}}},[e._v("新建")])],1)]),t("div",{staticClass:"table-content"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},[t("el-table-column",{attrs:{prop:"name",label:"权限名称",align:"center",width:"180"}}),t("el-table-column",{attrs:{prop:"remark",label:"权限说明",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return[r.row.remark?!r.row.show_all_remark&&r.row.remark.length>20?t("div",[e._v(" "+e._s(e.textFormat(r.row.remark,20))+" "),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){r.row.show_all_remark=!0}}},[e._v("查看更多")])],1):t("div",[e._v(" "+e._s(r.row.remark)+" "),r.row.remark.length>20?t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){r.row.show_all_remark=!1}}},[e._v("收起")]):e._e()],1):t("div",[e._v("--")])]}}])}),t("el-table-column",{attrs:{prop:"update_time",label:"操作时间",align:"center"}}),t("el-table-column",{attrs:{prop:"operator_name",label:"操作人",align:"center"}}),t("el-table-column",{attrs:{fixed:"right",label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.gotoAddOrEdit("edit",r.row)}}},[e._v("编辑")]),t("el-button",{staticClass:"ps-red",attrs:{type:"text",size:"small"},on:{click:function(t){return e.delMemberPermission(r.row.id)}}},[e._v("删除")])]}}])})],1)],1),t("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[t("el-pagination",{staticClass:"ps-text",attrs:{"current-page":e.currentPage,"page-size":e.pageSize,layout:"total, prev, pager, next",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"current-change":e.handleCurrentChange}})],1)])],1)},n=[],l=r("ed08"),o=r("c8c2");function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return t};var e,t={},r=Object.prototype,a=r.hasOwnProperty,n=Object.defineProperty||function(e,t,r){e[t]=r.value},l="function"==typeof Symbol?Symbol:{},o=l.iterator||"@@iterator",c=l.asyncIterator||"@@asyncIterator",u=l.toStringTag||"@@toStringTag";function p(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{p({},"")}catch(e){p=function(e,t,r){return e[t]=r}}function m(e,t,r,a){var l=t&&t.prototype instanceof v?t:v,o=Object.create(l.prototype),i=new D(a||[]);return n(o,"_invoke",{value:T(e,r,i)}),o}function y(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=m;var f="suspendedStart",d="suspendedYield",b="executing",h="completed",_={};function v(){}function g(){}function w(){}var k={};p(k,o,(function(){return this}));var E=Object.getPrototypeOf,A=E&&E(E(C([])));A&&A!==r&&a.call(A,o)&&(k=A);var L=w.prototype=v.prototype=Object.create(k);function N(e){["next","throw","return"].forEach((function(t){p(e,t,(function(e){return this._invoke(t,e)}))}))}function O(e,t){function r(n,l,o,s){var c=y(e[n],e,l);if("throw"!==c.type){var u=c.arg,p=u.value;return p&&"object"==i(p)&&a.call(p,"__await")?t.resolve(p.__await).then((function(e){r("next",e,o,s)}),(function(e){r("throw",e,o,s)})):t.resolve(p).then((function(e){u.value=e,o(u)}),(function(e){return r("throw",e,o,s)}))}s(c.arg)}var l;n(this,"_invoke",{value:function(e,a){function n(){return new t((function(t,n){r(e,a,t,n)}))}return l=l?l.then(n,n):n()}})}function T(t,r,a){var n=f;return function(l,o){if(n===b)throw Error("Generator is already running");if(n===h){if("throw"===l)throw o;return{value:e,done:!0}}for(a.method=l,a.arg=o;;){var i=a.delegate;if(i){var s=S(i,a);if(s){if(s===_)continue;return s}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(n===f)throw n=h,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);n=b;var c=y(t,r,a);if("normal"===c.type){if(n=a.done?h:d,c.arg===_)continue;return{value:c.arg,done:a.done}}"throw"===c.type&&(n=h,a.method="throw",a.arg=c.arg)}}}function S(t,r){var a=r.method,n=t.iterator[a];if(n===e)return r.delegate=null,"throw"===a&&t.iterator.return&&(r.method="return",r.arg=e,S(t,r),"throw"===r.method)||"return"!==a&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+a+"' method")),_;var l=y(n,t.iterator,r.arg);if("throw"===l.type)return r.method="throw",r.arg=l.arg,r.delegate=null,_;var o=l.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,_):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,_)}function P(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function x(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function D(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(P,this),this.reset(!0)}function C(t){if(t||""===t){var r=t[o];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,l=function r(){for(;++n<t.length;)if(a.call(t,n))return r.value=t[n],r.done=!1,r;return r.value=e,r.done=!0,r};return l.next=l}}throw new TypeError(i(t)+" is not iterable")}return g.prototype=w,n(L,"constructor",{value:w,configurable:!0}),n(w,"constructor",{value:g,configurable:!0}),g.displayName=p(w,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===g||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,p(e,u,"GeneratorFunction")),e.prototype=Object.create(L),e},t.awrap=function(e){return{__await:e}},N(O.prototype),p(O.prototype,c,(function(){return this})),t.AsyncIterator=O,t.async=function(e,r,a,n,l){void 0===l&&(l=Promise);var o=new O(m(e,r,a,n),l);return t.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},N(L),p(L,u,"Generator"),p(L,o,(function(){return this})),p(L,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var a in t)r.push(a);return r.reverse(),function e(){for(;r.length;){var a=r.pop();if(a in t)return e.value=a,e.done=!1,e}return e.done=!0,e}},t.values=C,D.prototype={constructor:D,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(x),!t)for(var r in this)"t"===r.charAt(0)&&a.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(a,n){return i.type="throw",i.arg=t,r.next=a,n&&(r.method="next",r.arg=e),!!n}for(var l=this.tryEntries.length-1;l>=0;--l){var o=this.tryEntries[l],i=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var s=a.call(o,"catchLoc"),c=a.call(o,"finallyLoc");if(s&&c){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var l=n;break}}l&&("break"===e||"continue"===e)&&l.tryLoc<=t&&t<=l.finallyLoc&&(l=null);var o=l?l.completion:{};return o.type=e,o.arg=t,l?(this.method="next",this.next=l.finallyLoc,_):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),_},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),x(r),_}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var a=r.completion;if("throw"===a.type){var n=a.arg;x(r)}return n}}throw Error("illegal catch attempt")},delegateYield:function(t,r,a){return this.delegate={iterator:C(t),resultName:r,nextLoc:a},"next"===this.method&&(this.arg=e),_}},t}function c(e,t,r,a,n,l,o){try{var i=e[l](o),s=i.value}catch(e){return void r(e)}i.done?t(s):Promise.resolve(s).then(a,n)}function u(e){return function(){var t=this,r=arguments;return new Promise((function(a,n){var l=e.apply(t,r);function o(e){c(l,a,n,o,i,"next",e)}function i(e){c(l,a,n,o,i,"throw",e)}o(void 0)}))}}var p={name:"SuperMemberPermission",props:{},data:function(){return{isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[],searchFormSetting:{name:{type:"input",label:"权限名称",value:"",placeholder:"请输入权限名称"}},dialogVisible:!1,selectInfo:{}}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getMemberPermission()},searchHandle:Object(l["d"])((function(){this.currentPage=1,this.getMemberPermission()}),300),refreshHandle:function(){this.currentPage=1,this.tableData=[],this.initLoad()},getMemberPermission:function(){var e=this;return u(s().mark((function t(){var r,a;return s().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,r=Object(o["getRequestParams"])(e.searchFormSetting,e.currentPage,e.pageSize),t.next=4,e.$apis.apiBackgroundMemberMemberPermissionListPost(r);case 4:a=t.sent,e.isLoading=!1,0===a.code?(e.tableData=a.data.results.map((function(e){return e.show_all_remark=!1,e})),e.totalCount=a.data.count):e.$message.error(a.msg);case 7:case"end":return t.stop()}}),t)})))()},handleSizeChange:function(e){this.pageSize=e,this.getMemberPermission()},handleCurrentChange:function(e){this.currentPage=e,this.getMemberPermission()},delMemberPermission:function(e){var t=this;return u(s().mark((function r(){return s().wrap((function(r){while(1)switch(r.prev=r.next){case 0:t.$confirm("确定删除会员标签？","提示",{confirmButtonText:t.$t("dialog.confirm_btn"),cancelButtonText:t.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var r=u(s().mark((function r(a,n,l){var o;return s().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if("confirm"!==a){r.next=9;break}return r.next=3,t.$apis.apiBackgroundMemberMemberPermissionDeletePost({ids:[e]});case 3:o=r.sent,0===o.code?(t.$message.success("删除成功"),t.getMemberPermission()):t.$message.error(o.msg),l(),n.confirmButtonLoading=!1,r.next=10;break;case 9:n.confirmButtonLoading||l();case 10:case"end":return r.stop()}}),r)})));function a(e,t,a){return r.apply(this,arguments)}return a}()}).then((function(e){})).catch((function(e){}));case 1:case"end":return r.stop()}}),r)})))()},openDialog:function(e){this.dialogVisible=!0,this.selectInfo=e},gotoAddOrEdit:function(e,t){var r={};"edit"===e&&(r={data:encodeURIComponent(JSON.stringify(t))}),this.$router.push({name:"SuperAddOrEditMemberPermission",params:{type:e},query:r})},textFormat:l["X"]}},m=p,y=(r("dc7d"),r("2877")),f=Object(y["a"])(m,a,n,!1,null,"71bb7919",null);t["default"]=f.exports},ab1f:function(e,t,r){},c8c2:function(e,t,r){"use strict";r.r(t),r.d(t,"getRequestParams",(function(){return u})),r.d(t,"RECENTSEVEN",(function(){return p})),r.d(t,"DIC_OBTAIN_TYPE",(function(){return m})),r.d(t,"DIC_SEND_TYPE",(function(){return y})),r.d(t,"DIC_MEMBER_STATUS",(function(){return f})),r.d(t,"DIC_TRIGGER_TYPE",(function(){return d})),r.d(t,"DIC_MENBER_STATUS",(function(){return b})),r.d(t,"DIC_PERMISSION_TYPE",(function(){return h})),r.d(t,"DIC_MEMBER_CYCLE",(function(){return _})),r.d(t,"SEARCH_FORM_RECORD_DATA",(function(){return v})),r.d(t,"SEARCH_FORM_EXCLUSIVE_DATA",(function(){return g})),r.d(t,"TABLE_HEAD_RECORD_DATA",(function(){return w})),r.d(t,"TABLE_HEAD_EXCLUSIVE_DATA",(function(){return k})),r.d(t,"SEARCH_FORM_SMS_MANAGER_DATA",(function(){return E})),r.d(t,"TABLE_HEAD_SEND_DATA",(function(){return A})),r.d(t,"TABLE_HEAD_CHARGE_INDEX_DATA",(function(){return L})),r.d(t,"TABLE_HEAD_CHARGE_INDEX_CONTENT",(function(){return N})),r.d(t,"SEARCH_FORM_CHARGE_DETAIL_DATA",(function(){return O})),r.d(t,"TABLE_HEAD_CHARGE_DETAIL_DATA",(function(){return T})),r.d(t,"SEARCH_FORM_PERMISSION_MANAGER_DATA",(function(){return S})),r.d(t,"TABLE_HEAD_PERMISSION_MANAGER_DATA",(function(){return P})),r.d(t,"SEARCH_FORM_KEY_MANAGER_DATA",(function(){return x})),r.d(t,"TABLE_HEAD_KEY_MANAGER_DATA",(function(){return D})),r.d(t,"SEARCH_FORM_PROMOTIONAL_PICTURE_DATA",(function(){return C})),r.d(t,"TABLE_HEAD_PROMOTIONAL_PICTURE_DATA",(function(){return M}));var a=r("5a0c");function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach((function(t){i(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function i(e,t,r){return(t=s(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function s(e){var t=c(e,"string");return"symbol"==n(t)?t:t+""}function c(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=n(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var u=function(e,t,r){var a,n={};Object.keys(e).forEach((function(t){("select_time"!==t&&""!==e[t].value&&e[t].value&&0!==e[t].value.length||"boolean"===typeof e[t].value)&&(n[t]=e[t].value)}));var l=o({page:t,page_size:r},n);return 2===(null===(a=e.select_time)||void 0===a||null===(a=a.value)||void 0===a?void 0:a.length)&&(l.start_date=e.select_time.value[0],l.end_date=e.select_time.value[1]),l},p=[a().subtract(7,"day").format("YYYY-MM-DD"),a().format("YYYY-MM-DD")],m=[{name:"线上购买",value:"buy",label:"buy"},{name:"手动发放",value:"manual_release",label:"manual_release"}],y=[{name:"消费通知",value:"consume",label:"consume"},{name:"点餐提醒",value:"order",label:"order"},{name:"充值提醒",value:"recharge",label:"recharge"},{name:"余额提醒",value:"balance",label:"balance"},{name:"提现通知",value:"withdraw",label:"withdraw"},{name:"通行通知",value:"is_pass",label:"is_pass"}],f=[{name:"使用中",value:"using",label:"using"},{name:"已过期",value:"expired",label:"expired"},{name:"非会员",value:"non",label:"non"}],d=[{name:"全部标签",value:"all",label:"all"},{name:"任意标签",value:"any",label:"any"}],b=[{name:"开启",value:!0,label:!0},{name:"关闭",value:!1,label:!1}],h=[{name:"关联链接",value:"association"},{name:"固定模块",value:"fixed"}],_=[{name:"月卡",value:"month"},{name:"季卡",value:"season"},{name:"年卡",value:"year"},{name:"永久会员",value:"permanent"},{name:"畅享会员",value:"week"}],v={select_time:{type:"daterange",label:"购买时间",clearable:!0,format:"yyyy-MM-dd",value:p},member_permission_name:{type:"input",label:"权益",value:"",placeholder:"请输入权益"},nickname:{type:"input",label:"用户姓名",value:"",placeholder:"请输入用户姓名"},phone:{type:"input",label:"手机号",value:"",placeholder:"请输入手机号"},receive_type:{type:"select",value:"",label:"获取方式",dataList:m,multiple:!1,collapseTags:!0,listNameKey:"name",listValueKey:"value",clearable:!0}},g={member_permissions:{type:"select",value:[],label:"权益",dataList:[],multiple:!1,collapseTags:!0,listNameKey:"name",listValueKey:"id",clearable:!0}},w=[{label:"订单号",key:"trade_no",width:"200",fixed:"left"},{label:"购买时间",key:"create_time",width:"200"},{label:"权益",key:"member_permission_name",width:"140"},{label:"金额",key:"origin_fee",width:"140",type:"slot",slotName:"price"},{label:"有效期",key:"days",width:"120",type:"slot",slotName:"days"},{label:"用户姓名",key:"nickname",width:"150"},{label:"手机号",key:"phone",width:"150"},{label:"操作员",key:"operator_name"},{label:"获取方式",key:"receive_type_alias"}],k=[{label:"序号",key:"index",width:"100",fixed:"left"},{label:"权益",key:"member_permissions_name",width:"200",type:"slot",slotName:"permission"},{label:"有效期",key:"days",width:"200",type:"slot",slotName:"days"},{label:"价格",key:"origin_fee",width:"200",type:"slot",slotName:"price"},{label:"说明",key:"remark",width:"200"},{label:"操作员",key:"operator_name"},{label:"修改时间",key:"update_time",width:"200"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"200"}],E={select_time:{type:"daterange",label:"推送时间",clearable:!0,format:"yyyy-MM-dd",value:p},name:{type:"input",label:"用户名",value:"",placeholder:"请输用户名"},phone:{type:"input",label:"手机号",value:"",placeholder:"请输入手机号"},organization_id:{type:"select",multiple:!0,filterable:!0,collapseTags:!0,label:"消费点",value:[],placeholder:"请选择消费点",listNameKey:"name",listValueKey:"id",dataList:[]},company_id:{type:"CompanySelect",value:[],label:"所属项目点",dataList:[],multiple:!0,checkStrictly:!0,clearable:!0,labelWidth:"100px",companyOpts:{label:"name",value:"company"},role:"super"},receive_type:{type:"select",value:[],label:"推送类型",dataList:y,multiple:!1,collapseTags:!0,listNameKey:"name",clearable:!0}},A=[{label:"推送时间",key:"create_time",width:"200"},{label:"用户名",key:"user_name",width:"150"},{label:"手机号",key:"phone",width:"150"},{label:"所属项目点",key:"company_name",width:"150"},{label:"消费点",key:"organization_name",width:"150"},{label:"推送类型",key:"push_type_alias",width:"150"},{label:"推送内容",key:"remark"},{label:"短信服务商",key:"sms_type_alias"}],L=[{label:"会员类型",key:"member_cycle_alias",width:"140"},{label:"说明",key:"remark",width:"250",type:"slot",slotName:"remark",align:"center"},{label:"基础价格（元）",key:"origin_fee",type:"slot",slotName:"price"},{label:"操作时间",key:"update_time"},{label:"操作人",key:"operator_name"},{label:"操作",key:"contact_name",type:"slot",slotName:"operation"}],N=[{member_cycle_alias:"年卡",origin_fee:"155622",remark:"说明说明说明说明"},{member_cycle_alias:"季卡",origin_fee:"26",remark:"11111"},{member_cycle_alias:"月卡",origin_fee:"32",remark:"2222"},{member_cycle_alias:"畅享会员",price:"44",remark:"333"},{member_cycle_alias:"永久会员",origin_fee:"522",remark:"66666666"}],O={name:{type:"input",label:"名称",value:"",placeholder:"请输入名称"},member_labels:{type:"select",value:[],label:"会员标签",dataList:[],multiple:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",clearable:!0},trigger_type:{type:"select",value:[],label:"标签触发类型",dataList:d,collapseTags:!0,listNameKey:"name",listValueKey:"value",clearable:!0,labelWidth:"200"},is_enable:{type:"select",value:[],label:"状态",dataList:b,listNameKey:"name",listValueKey:"value",clearable:!0}},T=[{label:"名称",key:"name",width:"140"},{label:"会员标签",key:"member_labels",width:"250",type:"slot",slotName:"label"},{label:"标签触发类型",key:"trigger_type",type:"slot",slotName:"trigger"},{label:"优惠折扣",key:"discount"},{label:"优惠价",key:"origin_fee",type:"money"},{label:"限购次数",key:"buy_count",type:"slot",slotName:"buyCount"},{label:"说明",key:"remark",showTooltip:!0},{label:"操作时间",key:"update_time"},{label:"操作人",key:"operator_name"},{label:"状态",key:"status",type:"slot",slotName:"status"},{label:"操作",key:"contact_name",type:"slot",slotName:"operation"}],S={name:{type:"input",label:"权限名称",value:"",placeholder:"请输入权限名称"},type:{type:"select",value:[],label:"类型",dataList:h,collapseTags:!0,listNameKey:"name",listValueKey:"value",clearable:!0},associate_url:{type:"input",label:"关联链接",value:"",placeholder:"请输入关联链接"}},P=[{label:"权限名称",key:"name",width:"140"},{label:"图标",key:"icon_url",width:"250",type:"slot",slotName:"image"},{label:"类型",key:"type_alias"},{label:"键值",key:"permission_key"},{label:"关联链接",key:"associate_url",width:"250",type:"slot",slotName:"associateUrl"},{label:"权限说明",key:"remark",showTooltip:!0,width:"200"},{label:"操作时间",key:"update_time"},{label:"操作人",key:"operator_name"},{label:"是否需要会员",key:"status",type:"slot",slotName:"status"},{label:"操作",key:"contact_name",type:"slot",slotName:"operation"}],x={name:{type:"input",label:"名称",value:"",placeholder:"请输入名称"}},D=[{label:"名称",key:"name",width:"140"},{label:"键值",key:"permission_key"},{label:"说明",key:"remark"},{label:"操作时间",key:"update_time"},{label:"操作人",key:"operator_name"},{label:"操作",key:"operation",type:"slot",slotName:"operation"}],C={name:{type:"input",label:"权限名称",value:"",placeholder:"请输入权限名称"},type:{type:"select",value:[],label:"类型",dataList:h,collapseTags:!0,listNameKey:"name",listValueKey:"value",clearable:!0},associate_url:{type:"input",label:"关联链接",value:"",placeholder:"请输入关联链接"}},M=[{label:"名称",key:"name",width:"140"},{label:"图片",key:"url",width:"250",type:"slot",slotName:"image"},{label:"优先级",key:"priority"},{label:"状态",key:"is_release",type:"slot",slotName:"status"},{label:"显示界面",key:"ui_type_alias",type:"slot",slotName:"UIType"},{label:"操作时间",key:"update_time"},{label:"操作人",key:"operator_name"},{label:"操作",key:"contact_name",type:"slot",slotName:"operation"}]},dc7d:function(e,t,r){"use strict";r("ab1f")}}]);