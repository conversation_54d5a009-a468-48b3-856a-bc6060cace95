(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-components-menu-menuNutritionAnalysis","add_meal_month_recipes~add_week_recipes~view-merchant-meal-management-menu-admin-AddMealMonthRecipes~74d45474","view-merchant-meal-management-components-menu-constants"],{2116:function(t,e,a){"use strict";a.r(e),a.d(e,"MEALTIME_SETTING",(function(){return r})),a.d(e,"BAR_OPTION_SETTING",(function(){return n}));var r={tooltip:{trigger:"item",borderColor:"#FCA155",textStyle:{color:"#000",fontWeight:500},backgroundColor:"#fff",extraCssText:"box-shadow: 0 0 10px rgba(0,0,0,0.2);font-weight: 540;",formatter:function(t){var e=t.marker,a=t.percent;return e+t.name+"&nbsp;&nbsp;&nbsp;"+a+"%"}},title:{text:0,subtext:"订单比数",top:"25%",left:"19%",textAlign:"middle",textStyle:{color:"#000",fontSize:40,align:"center"},subtextStyle:{color:"#999",fontSize:16,align:"center"}},legend:{bottom:"5%",right:"right",y:"center",icon:"circle",orient:"vertical",padding:[0,20,0,0]},series:[{center:["20%","50%"],type:"pie",radius:["70%","60%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},hoverAnimation:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!1,fontSize:"20",fontWeight:"bold"}},labelLine:{show:!1},data:[]}],color:["#07DED0","#FE985F","#e98397","#F97C95","#58AFFE","#F8C345"]},n={tooltip:{},dataset:{dimensions:["product","统计摄入量","建议摄入量"],source:[{product:"兆焦耳(mj/kg)","统计摄入量":43.3,"建议摄入量":85.8},{product:"Milk Tea","统计摄入量":43.3,"建议摄入量":85.8},{product:"Cheese Cocoa","统计摄入量":43.3,"建议摄入量":85.8},{product:"Walnut Brownie","统计摄入量":43.3,"建议摄入量":85.8}]},legend:{right:"0"},grid:{left:0,right:0},xAxis:{type:"category"},dataZoom:[{end:30}],yAxis:{axisLine:{show:!0},axisTick:{show:!1},axisLabel:{show:!1}},series:[{type:"bar",barWidth:20},{type:"bar",barWidth:20}],color:["#579def","#5dbf6e"]}},"2b56":function(t,e,a){},"2c01":function(t,e,a){"use strict";a("2b56")},f60f:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"menu-nutrition-analysis"},[t._m(0),e("div",{staticClass:"ps-flex-align-c p-t-10"},[e("div",{staticClass:"p-r-10"},[e("span",{staticClass:"text-color"},[t._v("所属集体：")]),e("span",[t._v(t._s(t.collectiveInfo.collective_name))])]),e("div",{staticClass:"p-r-10"},[e("span",{staticClass:"text-color"},[t._v("总人数：")]),e("span",[t._v(t._s(t.collectiveInfo.number)+"人")])]),e("div",{staticClass:"p-r-10"},[e("span",{staticClass:"text-color"},[t._v("男性人数：")]),e("span",[t._v(t._s(t.collectiveInfo.man_number)+"人")])]),e("div",{staticClass:"p-r-10"},[e("span",{staticClass:"text-color"},[t._v("女性人数：")]),e("span",[t._v(t._s(t.collectiveInfo.women_number)+"人")])])]),e("div",{staticClass:"ps-flex-align-c flex-align-c p-t-10"},[e("span",{staticClass:"text-color"},[t._v("所含人群：")]),t._l(t.collectiveInfo.crowd_info,(function(a,r){return e("el-tag",{key:r,staticClass:"m-r-5 m-t-5",attrs:{size:"medium",effect:"plain",type:"info"}},[t._v(" "+t._s(a)+" ")])}))],2),e("div",{staticClass:"ps-flex-align-c flex-align-c p-t-10"},[e("span",{staticClass:"text-color"},[t._v("标签：")]),t._l(t.collectiveInfo.label_info,(function(a,r){return e("el-tag",{key:r,staticClass:"m-r-5 m-t-5",attrs:{size:"medium",effect:"plain",type:"info"}},[t._v(" "+t._s(a)+" ")])}))],2),t.tableData.length?e("div",{staticClass:"p-t-30 ps-flex-bw"},[e("div",[e("span",{staticClass:"weight-text"},[t._v(t._s(t.collectiveInfo.collective_name)+"·食谱营养分析")]),e("span",{staticClass:"text-color"},[t._v("（"+t._s(t.weekStartEndDate)+"）")])]),t._m(1)]):t._e(),t.tableData.length?e("div",[e("div",{staticClass:"meal_item"},[e("div",{ref:"unreached_chart",attrs:{id:"circular_chart"}}),e("div",{ref:"reached_chart",attrs:{id:"circular_chart"}}),e("div",{ref:"exceed_chart",attrs:{id:"circular_chart"}})])]):t._e(),t.tableData.length?e("div",{},[e("el-table",{ref:"progressTableData",staticClass:"ps-table",staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:"",border:"","header-row-class-name":"ps-table-header-row"}},[e("el-table-column",{attrs:{prop:"weekDate",label:"日期",align:"center"}}),e("el-table-column",{attrs:{prop:"breakfast",label:"早餐",align:"center",width:"300"},scopedSlots:t._u([{key:"default",fn:function(a){return[a.row.breakfast&&a.row.breakfast.length?t._l(a.row.breakfast,(function(a,r){return e("div",{key:r,staticClass:"progress-box"},[e("span",{staticClass:"progress-title"},[t._v(t._s(a.element))]),e("el-progress",{staticClass:"progress-content",attrs:{"show-text":!1,color:t.formatColorName(a.result).color,percentage:t.progressPercentage(a)}}),e("span",[t._v(t._s(a.current_value)+"/"+t._s(a.need_value)+"g")])],1)})):t._e()]}}],null,!1,1560401468)}),e("el-table-column",{attrs:{prop:"lunch",label:"午餐",align:"center",width:"300"},scopedSlots:t._u([{key:"default",fn:function(a){return[a.row.lunch&&a.row.lunch.length?t._l(a.row.lunch,(function(a,r){return e("div",{key:r,staticClass:"progress-box"},[e("span",{staticClass:"progress-title"},[t._v(t._s(a.element))]),e("el-progress",{staticClass:"progress-content",attrs:{"show-text":!1,color:t.formatColorName(a.result).color,percentage:t.progressPercentage(a)}}),e("span",[t._v(t._s(a.current_value)+"/"+t._s(a.need_value)+"g")])],1)})):t._e()]}}],null,!1,3321403423)}),e("el-table-column",{attrs:{prop:"dinner",label:"晚餐",align:"center",width:"300"},scopedSlots:t._u([{key:"default",fn:function(a){return[a.row.dinner&&a.row.dinner.length?t._l(a.row.dinner,(function(a,r){return e("div",{key:r,staticClass:"progress-box"},[e("span",{staticClass:"progress-title"},[t._v(t._s(a.element))]),e("el-progress",{staticClass:"progress-content",attrs:{"show-text":!1,color:t.formatColorName(a.result).color,percentage:t.progressPercentage(a)}}),e("span",[t._v(t._s(a.current_value)+"/"+t._s(a.need_value)+"g")])],1)})):t._e()]}}],null,!1,2234960857)})],1)],1):t._e(),t.radioGroupDate?e("div",{staticClass:"p-t-30"},[e("div",[e("span",{staticClass:"weight-text"},[t._v(t._s(t.collectiveInfo.collective_name)+"·每日营养分析")]),e("span",{staticClass:"text-color"},[t._v("（"+t._s(t.startEndDateRange)+"）")])]),e("div",{staticStyle:{flex:"1"}},[e("el-tabs",{on:{"tab-click":t.changeRadioGroupDate},model:{value:t.radioGroupDate,callback:function(e){t.radioGroupDate=e},expression:"radioGroupDate"}},t._l(t.dateNutritionData,(function(t,a,r){return e("el-tab-pane",{key:r,attrs:{label:a,name:a}})})),1)],1),e("div",{staticClass:"nutrition-analysis-wrapp"},t._l(t.nutritionDataList,(function(a,r){return e("div",{key:r,staticClass:"analysis-wrapp",on:{click:function(e){return t.clickAnalysisDetailedDialog(a)}}},[e("div",{staticClass:"analysis-title ps-flex-bw"},[e("span",{staticClass:"left"},[t._v(t._s(a.name))]),e("span",{staticClass:"status",style:{backgroundColor:"".concat(t.formatColorName(a.result).color)}},[t._v(" "+t._s(t.formatColorName(a.result).name)+" ")])]),e("div",{staticClass:"analysis-content-wrapp ps-flex-bw"},[e("div",{staticClass:"left"},[e("div",{staticClass:"text-color font-size-12"},[t._v("统计摄入量")]),e("div",{staticClass:"total-text"},[t._v(t._s(a.current_value))]),e("div",{staticClass:"font-size-12"},[t._v("建议摄入量："+t._s(a.need_value))])]),e("el-progress",{attrs:{width:90,color:t.formatColorName(a.result).color,"stroke-width":6,type:"circle",percentage:a.scale>=100?100:a.scale}})],1)])})),0),e("div",{staticClass:"bar-box"},[e("div",{ref:"analysisBarChart",staticStyle:{height:"500px"},attrs:{id:"analysisBarChart"}})]),e("div",{},[t._m(2),e("el-table",{ref:"progressTableData",staticClass:"ps-table",staticStyle:{width:"100%"},attrs:{data:t.foodDiversityNutrition,stripe:"",border:"","header-row-class-name":"ps-table-header-row"}},[e("el-table-column",{attrs:{prop:"name",label:"食物类别",align:"center"}}),e("el-table-column",{attrs:{prop:"breakfast",label:"早餐",align:"center",width:"300"}},[e("el-table-column",{attrs:{prop:"breakfast_need_value",label:"推荐摄入量",align:"center"}}),e("el-table-column",{attrs:{prop:"breakfast_current_value",label:"统计摄入量",align:"center"}}),e("el-table-column",{attrs:{prop:"breakfast_scale",label:"百分比",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",{style:{color:"".concat(t.formatColorName(a.row.breakfast_result).color)}},[t._v(" "+t._s(a.row.breakfast_scale)+" "),a.row.breakfast_scale?e("span",[t._v("%")]):t._e()])]}}],null,!1,2743873956)})],1),e("el-table-column",{attrs:{prop:"lunch",label:"午餐",align:"center",width:"300"}},[e("el-table-column",{attrs:{prop:"lunch_need_value",label:"推荐摄入量",align:"center"}}),e("el-table-column",{attrs:{prop:"lunch_current_value",label:"统计摄入量",align:"center"}}),e("el-table-column",{attrs:{prop:"lunch_scale",label:"百分比",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",{style:{color:"".concat(t.formatColorName(a.row.lunch_result).color)}},[t._v(" "+t._s(a.row.lunch_scale)+" "),a.row.lunch_scale?e("span",[t._v("%")]):t._e()])]}}],null,!1,3442770951)})],1),e("el-table-column",{attrs:{prop:"dinner",label:"晚餐",align:"center",width:"300"}},[e("el-table-column",{attrs:{prop:"dinner_need_value",label:"推荐摄入量",align:"center"}}),e("el-table-column",{attrs:{prop:"dinner_current_value",label:"统计摄入量",align:"center"}}),e("el-table-column",{attrs:{prop:"dinner_scale",label:"百分比",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",{style:{color:"".concat(t.formatColorName(a.row.dinner_result).color)}},[t._v(" "+t._s(a.row.dinner_scale)+" "),a.row.dinner_scale?e("span",[t._v("%")]):t._e()])]}}],null,!1,575667425)})],1)],1)],1)]):t._e(),t.analysisDetailedDialogVisible?e("analysis-detailed-dialog",{ref:"analysisDetailedDialog",attrs:{isshow:t.analysisDetailedDialogVisible,title:t.dialoganAlysisDetailedInfo.title,formDataDialog:t.dialoganAlysisDetailedInfo,dateNutritionData:t.dateNutritionData,width:"900px"},on:{"update:isshow":function(e){t.analysisDetailedDialogVisible=e}}}):t._e()],1)},n=[function(){var t=this,e=t._self._c;return e("div",{},[e("span",{staticClass:"weight-text"},[t._v("基本信息")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"marker-wrapper"},[e("div",[e("span",{staticClass:"marker",staticStyle:{background:"#66dcd0"}}),e("span",{staticStyle:{color:"#66dcd0","margin-right":"10px"}},[t._v("早餐")])]),e("div",[e("span",{staticClass:"marker",staticStyle:{background:"#FE985F"}}),e("span",{staticStyle:{color:"#FE985F","margin-right":"10px"}},[t._v("午餐")])]),e("div",[e("span",{staticClass:"marker",staticStyle:{background:"#e98497"}}),e("span",{staticStyle:{color:"#e98497","margin-right":"10px"}},[t._v("晚餐")])])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"marker-wrapper p-b-20"},[e("div",[e("span",{staticClass:"marker",staticStyle:{background:"#ef9a50"}}),e("span",{staticStyle:{"margin-right":"10px"}},[t._v("不足")])]),e("div",[e("span",{staticClass:"marker",staticStyle:{background:"#5dbf6e"}}),e("span",{staticStyle:{"margin-right":"10px"}},[t._v("适宜")])]),e("div",[e("span",{staticClass:"marker",staticStyle:{background:"#ea5b54"}}),e("span",{staticStyle:{"margin-right":"10px"}},[t._v("过量")])])])}],i=a("2116"),o=a("ed08"),l=a("1a30"),s=a("c9d9");function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function u(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */u=function(){return e};var t,e={},a=Object.prototype,r=a.hasOwnProperty,n=Object.defineProperty||function(t,e,a){t[e]=a.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function d(t,e,a){return Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{d({},"")}catch(t){d=function(t,e,a){return t[e]=a}}function f(t,e,a,r){var i=e&&e.prototype instanceof g?e:g,o=Object.create(i.prototype),l=new I(r||[]);return n(o,"_invoke",{value:N(t,a,l)}),o}function h(t,e,a){try{return{type:"normal",arg:t.call(e,a)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",y="suspendedYield",v="executing",m="completed",_={};function g(){}function b(){}function k(){}var w={};d(w,o,(function(){return this}));var C=Object.getPrototypeOf,x=C&&C(C(A([])));x&&x!==a&&r.call(x,o)&&(w=x);var D=k.prototype=g.prototype=Object.create(w);function S(t){["next","throw","return"].forEach((function(e){d(t,e,(function(t){return this._invoke(e,t)}))}))}function E(t,e){function a(n,i,o,l){var s=h(t[n],t,i);if("throw"!==s.type){var u=s.arg,d=u.value;return d&&"object"==c(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){a("next",t,o,l)}),(function(t){a("throw",t,o,l)})):e.resolve(d).then((function(t){u.value=t,o(u)}),(function(t){return a("throw",t,o,l)}))}l(s.arg)}var i;n(this,"_invoke",{value:function(t,r){function n(){return new e((function(e,n){a(t,r,e,n)}))}return i=i?i.then(n,n):n()}})}function N(e,a,r){var n=p;return function(i,o){if(n===v)throw Error("Generator is already running");if(n===m){if("throw"===i)throw o;return{value:t,done:!0}}for(r.method=i,r.arg=o;;){var l=r.delegate;if(l){var s=L(l,r);if(s){if(s===_)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(n===p)throw n=m,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n=v;var c=h(e,a,r);if("normal"===c.type){if(n=r.done?m:y,c.arg===_)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(n=m,r.method="throw",r.arg=c.arg)}}}function L(e,a){var r=a.method,n=e.iterator[r];if(n===t)return a.delegate=null,"throw"===r&&e.iterator.return&&(a.method="return",a.arg=t,L(e,a),"throw"===a.method)||"return"!==r&&(a.method="throw",a.arg=new TypeError("The iterator does not provide a '"+r+"' method")),_;var i=h(n,e.iterator,a.arg);if("throw"===i.type)return a.method="throw",a.arg=i.arg,a.delegate=null,_;var o=i.arg;return o?o.done?(a[e.resultName]=o.value,a.next=e.nextLoc,"return"!==a.method&&(a.method="next",a.arg=t),a.delegate=null,_):o:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,_)}function O(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function T(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(O,this),this.reset(!0)}function A(e){if(e||""===e){var a=e[o];if(a)return a.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,i=function a(){for(;++n<e.length;)if(r.call(e,n))return a.value=e[n],a.done=!1,a;return a.value=t,a.done=!0,a};return i.next=i}}throw new TypeError(c(e)+" is not iterable")}return b.prototype=k,n(D,"constructor",{value:k,configurable:!0}),n(k,"constructor",{value:b,configurable:!0}),b.displayName=d(k,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,k):(t.__proto__=k,d(t,s,"GeneratorFunction")),t.prototype=Object.create(D),t},e.awrap=function(t){return{__await:t}},S(E.prototype),d(E.prototype,l,(function(){return this})),e.AsyncIterator=E,e.async=function(t,a,r,n,i){void 0===i&&(i=Promise);var o=new E(f(t,a,r,n),i);return e.isGeneratorFunction(a)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},S(D),d(D,s,"Generator"),d(D,o,(function(){return this})),d(D,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),a=[];for(var r in e)a.push(r);return a.reverse(),function t(){for(;a.length;){var r=a.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=A,I.prototype={constructor:I,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(T),!e)for(var a in this)"t"===a.charAt(0)&&r.call(this,a)&&!isNaN(+a.slice(1))&&(this[a]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var a=this;function n(r,n){return l.type="throw",l.arg=e,a.next=r,n&&(a.method="next",a.arg=t),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],l=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var s=r.call(o,"catchLoc"),c=r.call(o,"finallyLoc");if(s&&c){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(t,e){for(var a=this.tryEntries.length-1;a>=0;--a){var n=this.tryEntries[a];if(n.tryLoc<=this.prev&&r.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,_):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),_},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.finallyLoc===t)return this.complete(a.completion,a.afterLoc),T(a),_}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.tryLoc===t){var r=a.completion;if("throw"===r.type){var n=r.arg;T(a)}return n}}throw Error("illegal catch attempt")},delegateYield:function(e,a,r){return this.delegate={iterator:A(e),resultName:a,nextLoc:r},"next"===this.method&&(this.arg=t),_}},e}function d(t,e){return v(t)||y(t,e)||h(t,e)||f()}function f(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function h(t,e){if(t){if("string"==typeof t)return p(t,e);var a={}.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?p(t,e):void 0}}function p(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,r=Array(e);a<e;a++)r[a]=t[a];return r}function y(t,e){var a=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=a){var r,n,i,o,l=[],s=!0,c=!1;try{if(i=(a=a.call(t)).next,0===e){if(Object(a)!==a)return;s=!1}else for(;!(s=(r=i.call(a)).done)&&(l.push(r.value),l.length!==e);s=!0);}catch(t){c=!0,n=t}finally{try{if(!s&&null!=a.return&&(o=a.return(),Object(o)!==o))return}finally{if(c)throw n}}return l}}function v(t){if(Array.isArray(t))return t}function m(t,e,a,r,n,i,o){try{var l=t[i](o),s=l.value}catch(t){return void a(t)}l.done?e(s):Promise.resolve(s).then(r,n)}function _(t){return function(){var e=this,a=arguments;return new Promise((function(r,n){var i=t.apply(e,a);function o(t){m(i,r,n,o,l,"next",t)}function l(t){m(i,r,n,o,l,"throw",t)}o(void 0)}))}}var g={props:{menuType:{type:String,default:""},menuId:{type:String,default:""}},data:function(){return{isLoading:!1,collectiveInfo:{},weekStartEndDate:"",pieMealTimeChart:null,barAnalysis:null,circularChartRefList:[{type:"unreached",key:"unreached_chart",title:"摄入不足",data:[{value:0,name:"早餐摄入不足",key:"breakfast"},{value:0,name:"午餐摄入不足",key:"lunch"},{value:0,name:"晚餐摄入不足",key:"dinner"}]},{type:"reached",key:"reached_chart",title:"摄入适宜",data:[{value:0,name:"早餐摄入适宜",key:"breakfast"},{value:0,name:"午餐摄入适宜",key:"lunch"},{value:0,name:"晚餐摄入适宜",key:"dinner"}]},{type:"exceed",key:"exceed_chart",title:"摄入过量",data:[{value:0,name:"早餐摄入过量",key:"breakfast"},{value:0,name:"午餐摄入过量",key:"lunch"},{value:0,name:"晚餐摄入过量",key:"dinner"}]}],dateNutritionData:{},startEndDateRange:"",nutritionDataList:[{key:"energy_kcal",name:"能量(kcal)",title:"能量摄入明细",table_name:"所含能量(kcal)"},{key:"protein",name:"蛋白质(g)",title:"蛋白质摄入明细",table_name:"蛋白质(g)"},{key:"axunge",name:"脂肪(g)",title:"脂肪摄入明细",table_name:"脂肪(g)"},{key:"carbohydrate",name:"碳水化物(g)",title:"碳水化物摄入明细",table_name:"碳水化物(g)"}],pieChart:{unreached_chart:null,reached_chart:null,exceed_chart:null},tableData:[],foodDiversityNutrition:[],foodDiversityTableSetting:[{label:"食物类别",key:"name"},{key:"breakfast",label:"早餐",children:[{key:"breakfast_need_value",label:"推荐摄入量"},{key:"breakfast_current_value",label:"统计摄入量"},{key:"breakfast_scale",label:"百分比",type:"scale"}]},{key:"lunch",label:"午餐",children:[{key:"lunch_need_value",label:"推荐摄入量"},{key:"lunch_current_value",label:"统计摄入量"},{key:"lunch_scale",label:"百分比",type:"scale"}]},{key:"dinner",label:"晚餐",children:[{key:"dinner_need_value",label:"推荐摄入量"},{key:"dinner_current_value",label:"统计摄入量"},{key:"dinner_scale",label:"百分比",type:"scale"}]}],radioGroupDate:"",analysisDetailedDialogVisible:!1,dialoganAlysisDetailedInfo:{}}},watch:{},components:{analysisDetailedDialog:l["default"]},created:function(){this.getMenuNutrition()},beforeDestroy:function(){window.removeEventListener("resize",this.resizeChartHandle)},mounted:function(){window.addEventListener("resize",this.resizeChartHandle)},methods:{getMenuNutrition:function(){var t=this;return _(u().mark((function e(){var a,r,n,i,l,s,c,f,h,p;return u().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(o["Z"])(t.$apis.apiBackgroundFoodMenuMenuNutritionPost({id:Number(t.menuId),menu_type:t.menuType}));case 3:if(a=e.sent,r=d(a,2),n=r[0],i=r[1],t.isLoading=!1,!n){e.next=11;break}return t.$message.error(n.message),e.abrupt("return");case 11:if(0===i.code){for(f in t.collectiveInfo=i.data.collective_info,l=i.data.total_nutrition_data.date_data,t.weekStartEndDate="".concat(Object.keys(l)[0],"至").concat(Object.keys(l)[Object.keys(l).length-1]),s=i.data.total_nutrition_data.meal_type_data,t.circularChartRefList.forEach((function(t){s[t.type]&&t.data.forEach((function(e){e.value=s[t.type][e.key]}))})),c=[],l){for(p in h={date:f,weekDate:"星期".concat(Object(o["M"])(new Date(f),"{a}"),"(").concat(f,")")},l[f])h[p]=l[f][p];c.push(h)}t.tableData=c,t.dateNutritionData=i.data.date_nutrition_data,Object.keys(t.dateNutritionData)&&Object.keys(t.dateNutritionData).length&&(t.startEndDateRange="".concat(Object.keys(t.dateNutritionData)[0],"至").concat(Object.keys(t.dateNutritionData)[Object.keys(t.dateNutritionData).length-1]),t.radioGroupDate=Object.keys(t.dateNutritionData)[0],t.dateNutritionDataInfoChange())}else t.$message.error(i.msg);case 12:case"end":return e.stop()}}),e)})))()},progressPercentage:function(t){var e=parseInt(t.current_value/t.need_value*100),a=0;return e&&(a=e>=100?100:e),a},formatColorName:function(t){var e={color:"",name:""};switch(t){case"unreached":e.color="#e89e42",e.name="不足";break;case"reached":e.color="#5dbf6e",e.name="适宜";break;case"exceed":e.color="#ea5b55",e.name="过量";break}return e},changeRadioGroupDate:function(t){this.dateNutritionDataInfoChange()},dateNutritionDataInfoChange:function(){var t=this,e=[],a=this.dateNutritionData[this.radioGroupDate].food_nutrition,r=Object(o["f"])(this.dateNutritionData[this.radioGroupDate].nutrition_data);this.nutritionDataList.forEach((function(t){t.current_value=r[t.key].current_value,t.need_value=r[t.key].need_value,t.result=r[t.key].result,t.scale=r[t.key].scale})),Object.keys(r)&&(s["c"].forEach((function(t){var a={product:t.name+"("+t.unit+")","统计摄入量":r[t.key].current_value,"建议摄入量":r[t.key].need_value};e.push(a)})),i["BAR_OPTION_SETTING"].dataset.source=e,this.foodDiversityNutrition=[{name:"谷物",key:"cereals"},{name:"鱼禽肉蛋",key:"eggsandmeat"},{name:"水果",key:"fruit"},{name:"蔬菜",key:"vegetable"}],this.foodDiversityNutrition=this.foodDiversityNutrition.map((function(e){for(var r in a[e.key])for(var n in a[e.key][r])t.$set(e,r+"_"+n,a[e.key][r][n]);return e})),this.$nextTick((function(){t.initMealTimeDataPie(),t.initMealTimeDataBar()})))},initMealTimeDataPie:function(){var t=this;this.circularChartRefList.forEach((function(e){var a=e.data,r={};a.forEach((function(t){r[t.name]=t.value})),i["MEALTIME_SETTING"].title.subtext=e.title;var n=i["MEALTIME_SETTING"];n.legend.formatter=function(t){var e=r[t];return t+"    "+(e||0)},n.series[0].data=a,n.title.text=a.reduce((function(t,e){return t+e.value}),0),t.pieChart[e.key]||(t.pieChart[e.key]=t.$echarts.init(t.$refs[e.key])),t.pieChart[e.key].setOption(n)}))},initMealTimeDataBar:function(){var t=i["BAR_OPTION_SETTING"];this.barAnalysis||(this.barAnalysis=this.$echarts.init(this.$refs.analysisBarChart)),this.barAnalysis&&this.barAnalysis.setOption(t)},clickAnalysisDetailedDialog:function(t){this.dialoganAlysisDetailedInfo={id:Number(this.menuId),menu_type:this.menuType,date:this.radioGroupDate,nutrition_type:t.key,title:t.title,table_name:t.table_name},this.analysisDetailedDialogVisible=!0},resizeChartHandle:Object(o["d"])((function(){this.pieChart.unreached_chart&&this.pieChart.unreached_chart.resize(),this.pieChart.reached_chart&&this.pieChart.reached_chart.resize(),this.pieChart.exceed_chart&&this.pieChart.exceed_chart.resize(),this.barAnalysis&&this.barAnalysis.resize()}),300)}},b=g,k=(a("2c01"),a("2877")),w=Object(k["a"])(b,r,n,!1,null,null,null);e["default"]=w.exports}}]);