(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-merchant-admin-BankMerchantManage"],{"14c37":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"container-wrapper"},[e("refresh-tool",{on:{refreshPage:t.refreshHandle}}),e("search-form",{ref:"searchRef",attrs:{"form-setting":t.searchFormSetting,"label-width":"105px"},on:{search:t.searchHandler,reset:t.reset<PERSON><PERSON><PERSON>}}),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("数据列表")]),e("div",{staticClass:"align-r"},[e("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background.sub_merchant_info.add"],expression:"['background.sub_merchant_info.add']"}],attrs:{color:"origin"},on:{click:function(e){return t.showRecordDialog()}}},[t._v("补录")]),e("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background.sub_merchant_info.add"],expression:"['background.sub_merchant_info.add']"}],attrs:{color:"origin"},on:{click:function(e){return t.goToAddOrEdit("add")}}},[t._v("新建")])],1)]),e("div",{staticClass:"table-content"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:t.tableData,height:"460",stripe:"","header-row-class-name":"ps-table-header-row"}},t._l(t.tableSettings,(function(r){return e("table-column",{key:r.key,attrs:{col:r},scopedSlots:t._u([{key:"detail",fn:function(r){var n=r.row;return[e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background.sub_merchant_info.list"],expression:"['background.sub_merchant_info.list']"}],staticClass:"ps-text-blue",attrs:{type:"text",size:"small"},on:{click:function(e){return t.clickShowDialog("history",n.history_sub_mch_ids)}}},[t._v(" 查看 ")])]}},{key:"organizationName",fn:function(e){var r=e.row;return[t._v(" "+t._s(r.get_agreement_info?r.get_agreement_info.organization_name:"")+" ")]}},{key:"isSign",fn:function(e){var r=e.row;return[t._v(" "+t._s(t.getSign(!!r.get_agreement_info&&r.get_agreement_info.is_sign))+" ")]}},{key:"accountName",fn:function(e){var r=e.row;return[t._v(" "+t._s(r.get_agreement_info?r.get_agreement_info.account_name:"")+" ")]}},{key:"operation",fn:function(r){var n=r.row;return[e("el-button",{staticClass:"ps-text-blue",attrs:{type:"text",size:"small"},on:{click:function(e){return t.goToAddOrEdit("detail",n)}}},[t._v("查看")]),e("el-button",{staticClass:"ps-text-blue",attrs:{type:"text",size:"small"},on:{click:function(e){return t.clickShowDialog("upload",n)}}},[t._v("上传资料")]),"1"==n.is_passed?e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background.sub_merchant_info.update_sub_merchant_status"],expression:"['background.sub_merchant_info.update_sub_merchant_status']"}],staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.clickShowDialog("close",n)}}},[t._v("关闭")]):t._e(),"1"==n.is_passed?e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background.sub_merchant_info.update_sub_merchant_status"],expression:"['background.sub_merchant_info.update_sub_merchant_status']"}],staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.clickShowDialog("break",n)}}},[t._v("解约")]):t._e(),"3"==n.is_passed?e("el-button",{staticClass:"ps-text-blue",attrs:{type:"text",size:"small"},on:{click:function(e){return t.clickShowDialog("verify",n)}}},[t._v("验证")]):t._e(),t.showAgreementBtn(n)?e("el-button",{staticClass:"ps-text-blue",attrs:{type:"text",size:"small"},on:{click:function(e){return t.clickShowDialog("protocol",n)}}},[t._v("发送协议")]):t._e()]}}],null,!0)})})),1)],1),e("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[e("pagination",{attrs:{onPaginationChange:t.onPaginationChange,"current-page":t.currentPage,"page-size":t.pageSize,layout:"total, prev, pager, next, jumper",total:t.totalCount},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"update:pageSize":function(e){t.pageSize=e},"update:page-size":function(e){t.pageSize=e}}})],1)]),e("bank-merchant-dialog",{ref:"bankDialog",on:{updateProcotol:t.updateProcotol}}),e("import-dialog-drawer",{attrs:{templateUrl:t.templateUrl,tableSetting:t.tableSetting,show:t.importShowDialog,title:t.importDialogTitle,openExcelType:t.openExcelType},on:{"update:show":function(e){t.importShowDialog=e}}})],1)},o=[],a=r("ed08"),i=r("ddcc"),s=r("941f"),c=r("f63a"),u=r("e762a");function l(t){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},l(t)}function f(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */f=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",c=a.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function h(t,e,r,n){var a=e&&e.prototype instanceof v?e:v,i=Object.create(a.prototype),s=new C(n||[]);return o(i,"_invoke",{value:D(t,r,s)}),i}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var g="suspendedStart",d="suspendedYield",m="executing",y="completed",b={};function v(){}function w(){}function _(){}var x={};u(x,i,(function(){return this}));var S=Object.getPrototypeOf,k=S&&S(S(B([])));k&&k!==r&&n.call(k,i)&&(x=k);var A=_.prototype=v.prototype=Object.create(x);function O(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function L(t,e){function r(o,a,i,s){var c=p(t[o],t,a);if("throw"!==c.type){var u=c.arg,f=u.value;return f&&"object"==l(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,i,s)}),(function(t){r("throw",t,i,s)})):e.resolve(f).then((function(t){u.value=t,i(u)}),(function(t){return r("throw",t,i,s)}))}s(c.arg)}var a;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return a=a?a.then(o,o):o()}})}function D(e,r,n){var o=g;return function(a,i){if(o===m)throw Error("Generator is already running");if(o===y){if("throw"===a)throw i;return{value:t,done:!0}}for(n.method=a,n.arg=i;;){var s=n.delegate;if(s){var c=E(s,n);if(c){if(c===b)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===g)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=m;var u=p(e,r,n);if("normal"===u.type){if(o=n.done?y:d,u.arg===b)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(o=y,n.method="throw",n.arg=u.arg)}}}function E(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,E(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),b;var a=p(o,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,b;var i=a.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,b):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,b)}function j(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function P(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function C(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(j,this),this.reset(!0)}function B(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(l(e)+" is not iterable")}return w.prototype=_,o(A,"constructor",{value:_,configurable:!0}),o(_,"constructor",{value:w,configurable:!0}),w.displayName=u(_,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,u(t,c,"GeneratorFunction")),t.prototype=Object.create(A),t},e.awrap=function(t){return{__await:t}},O(L.prototype),u(L.prototype,s,(function(){return this})),e.AsyncIterator=L,e.async=function(t,r,n,o,a){void 0===a&&(a=Promise);var i=new L(h(t,r,n,o),a);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},O(A),u(A,c,"Generator"),u(A,i,(function(){return this})),u(A,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=B,C.prototype={constructor:C,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(P),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return s.type="throw",s.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],s=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var c=n.call(i,"catchLoc"),u=n.call(i,"finallyLoc");if(c&&u){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=t,i.arg=e,a?(this.method="next",this.next=a.finallyLoc,b):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),b},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),P(r),b}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;P(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:B(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),b}},e}function h(t,e){return y(t)||m(t,e)||g(t,e)||p()}function p(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function g(t,e){if(t){if("string"==typeof t)return d(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?d(t,e):void 0}}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function m(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,s=[],c=!0,u=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(s.push(n.value),s.length!==e);c=!0);}catch(t){u=!0,o=t}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw o}}return s}}function y(t){if(Array.isArray(t))return t}function b(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function v(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?b(Object(r),!0).forEach((function(e){w(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function w(t,e,r){return(e=_(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function _(t){var e=x(t,"string");return"symbol"==l(e)?e:e+""}function x(t,e){if("object"!=l(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=l(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function S(t,e,r,n,o,a,i){try{var s=t[a](i),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,o)}function k(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var a=t.apply(e,r);function i(t){S(a,n,o,i,s,"next",t)}function s(t){S(a,n,o,i,s,"throw",t)}i(void 0)}))}}var A={name:"BankMerchantManage",data:function(){return{isLoading:!1,pageSize:5,totalCount:0,currentPage:1,tableData:[],tableSettings:Object(a["f"])(i["TABLE_HEAD_DATA"]),searchFormSetting:Object(a["f"])(i["SEARCH_FORM_SET_DATA"]),fileListsUpload:[],isShowSearchBtn:!1,isShowResetBtn:!1,uploadTipRightTop:"上传：查询人脸",uploadTipRightBottom:"仅支持jpg、png、bmp格式，大小不超过10M",isShowPopImg:!1,faceImgUrl:"",isUploading:!1,isShowCollapse:!1,importDialogTitle:"补录",importShowDialog:!1,templateUrl:location.origin+"/api/temporary/template_excel/abc/导入补录.xlsx",openExcelType:"AppendSubMerchantInfo",tableSetting:[]}},components:{BankMerchantDialog:s["default"]},mixins:[c["a"]],created:function(){this.initLoad()},methods:{refreshHandle:function(){this.currentPage=1,this.$refs.searchRef.resetForm(),this.tableData=[],this.initLoad()},initLoad:function(){this.searchFormSetting.is_passed.dataList=Object(a["f"])(i["DIC_MERCHANT_STATUS"]),this.getBankMerchantList()},onPaginationChange:function(t){this.currentPage=t.current,this.pageSize=t.pageSize,this.getBankMerchantList()},beforeUpload:function(t){var e=[".jpg",".png",".bmp"];if(!e.includes(this.getSuffix(t.name)))return this.$message.error("上传图片只能是 jpg/png 格式"),!1;var r=t.size/1024/1024<10;if(!r)return this.$message.error("上传图片大小不能超过 10MB!"),!1;this.isUploading=!0},getSuffix:function(t){var e=t.lastIndexOf("."),r="";return-1!==e&&(r=t.substring(e)),r},getFileLists:function(t){this.isUploading=!1,this.fileListsUpload=Object(a["f"])(t)},getBankMerchantList:function(){var t=this;return k(f().mark((function e(){var r,n,o,i,s,c,l;return f().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.isLoading){e.next=2;break}return e.abrupt("return");case 2:return t.isLoading=!0,r=v(v({},t.formatQueryParams(t.searchFormSetting)),{},{page:t.currentPage,page_size:t.pageSize}),e.next=6,Object(a["Z"])(t.$apis.apiBackgroundSubMerchantInfoListPost(r));case 6:if(n=e.sent,o=h(n,2),i=o[0],s=o[1],t.isLoading=!1,!i){e.next=14;break}return t.$message.error(i.message),e.abrupt("return");case 14:if(0===s.code){c=s.data||"";try{c&&(c=u["a"].decode(c),c=JSON.parse(c),l=c?c.results:[],Array.isArray(l)&&l.length>0&&l.map((function(t,e){return t.index=e+1,t})),t.tableData=Object(a["f"])(l),t.totalCount=c.count||-1)}catch(f){c=""}}else t.$message.error(s.msg);case 15:case"end":return e.stop()}}),e)})))()},formatQueryParams:function(t){var e={};for(var r in t)""!==t[r].value&&null!==t[r].value&&0!==t[r].value.length&&(e[r]="is_passed"===r?[t[r].value]:t[r].value);return e},clearFileHandle:function(){this.$refs.faceFileRef.clearHandle(),this.fileListsUpload=[]},resetFormFace:function(){this.$refs.searchRef&&Reflect.has(this.$refs.searchRef,"resetForm")&&this.$refs.searchRef.resetForm(),this.clearFileHandle()},uploadError:function(t){this.isUploading=!1},goToAddOrEdit:function(t,e){var r=e?e.sub_mch_id:"";"detail"===t&&window.sessionStorage.setItem("merchantData",JSON.stringify(e)),this.$router.push({name:"BankMerchantManageDetail",params:{type:"detail"===t?"view":t},query:{status:t,id:r}})},clickShowDialog:function(t,e){this.$refs.bankDialog&&Reflect.has(this.$refs.bankDialog,"setDialogData")&&Reflect.has(this.$refs.bankDialog,"isShowDialog")&&(this.$refs.bankDialog.setDialogData(t,e),this.$refs.bankDialog.isShowDialog(!0))},searchHandler:Object(a["d"])((function(){this.currentPage=1,this.initLoad()}),300),resetHandler:function(){this.refreshHandle()},showRecordDialog:function(){this.tableSetting=Object(a["f"])(i["PRINT_BANK_TABBLE_SETTING"]),this.importShowDialog=!0},confirmImportData:function(t){var e=this;return k(f().mark((function r(){var n,o,i,s,c;return f().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(n=t.allData||[],t.currentPage,!(Array.isArray(n)&&n.length>0)){r.next=16;break}return e.isShowImportDialog=!1,r.next=6,Object(a["Z"])(e.$apis.apiBackgroundSubMerchantInfoAppendSubMerchantInfoImportPost({url:n}));case 6:if(o=r.sent,i=h(o,2),s=i[0],c=i[1],!s){r.next=13;break}return e.$message.error("导入失败 "+s.message),r.abrupt("return");case 13:0===c.code?(e.$message.success("导入成功"),e.getCarBindingList()):e.$message.error("导入失败 "+c.msg),r.next=17;break;case 16:e.$message.error("请先导入数据");case 17:case"end":return r.stop()}}),r)})))()},updateProcotol:function(){this.getBankMerchantList()},getSign:function(t){return t?"是":"否"},showAgreementBtn:function(t){return!(!t||!Reflect.has(t,"get_agreement_info"))&&!t.get_agreement_info.is_sign}}},O=A,L=(r("c75d"),r("2877")),D=Object(L["a"])(O,n,o,!1,null,"b5e66234",null);e["default"]=D.exports},"57cd":function(t,e,r){},c75d:function(t,e,r){"use strict";r("57cd")},e762a:function(t,e,r){"use strict";r.d(e,"a",(function(){return $}));const n="3.7.7",o=n,a="function"===typeof Buffer,i="function"===typeof TextDecoder?new TextDecoder:void 0,s="function"===typeof TextEncoder?new TextEncoder:void 0,c="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",u=Array.prototype.slice.call(c),l=(t=>{let e={};return t.forEach((t,r)=>e[t]=r),e})(u),f=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,h=String.fromCharCode.bind(String),p="function"===typeof Uint8Array.from?Uint8Array.from.bind(Uint8Array):t=>new Uint8Array(Array.prototype.slice.call(t,0)),g=t=>t.replace(/=/g,"").replace(/[+\/]/g,t=>"+"==t?"-":"_"),d=t=>t.replace(/[^A-Za-z0-9\+\/]/g,""),m=t=>{let e,r,n,o,a="";const i=t.length%3;for(let s=0;s<t.length;){if((r=t.charCodeAt(s++))>255||(n=t.charCodeAt(s++))>255||(o=t.charCodeAt(s++))>255)throw new TypeError("invalid character found");e=r<<16|n<<8|o,a+=u[e>>18&63]+u[e>>12&63]+u[e>>6&63]+u[63&e]}return i?a.slice(0,i-3)+"===".substring(i):a},y="function"===typeof btoa?t=>btoa(t):a?t=>Buffer.from(t,"binary").toString("base64"):m,b=a?t=>Buffer.from(t).toString("base64"):t=>{const e=4096;let r=[];for(let n=0,o=t.length;n<o;n+=e)r.push(h.apply(null,t.subarray(n,n+e)));return y(r.join(""))},v=(t,e=!1)=>e?g(b(t)):b(t),w=t=>{if(t.length<2){var e=t.charCodeAt(0);return e<128?t:e<2048?h(192|e>>>6)+h(128|63&e):h(224|e>>>12&15)+h(128|e>>>6&63)+h(128|63&e)}e=65536+1024*(t.charCodeAt(0)-55296)+(t.charCodeAt(1)-56320);return h(240|e>>>18&7)+h(128|e>>>12&63)+h(128|e>>>6&63)+h(128|63&e)},_=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,x=t=>t.replace(_,w),S=a?t=>Buffer.from(t,"utf8").toString("base64"):s?t=>b(s.encode(t)):t=>y(x(t)),k=(t,e=!1)=>e?g(S(t)):S(t),A=t=>k(t,!0),O=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,L=t=>{switch(t.length){case 4:var e=(7&t.charCodeAt(0))<<18|(63&t.charCodeAt(1))<<12|(63&t.charCodeAt(2))<<6|63&t.charCodeAt(3),r=e-65536;return h(55296+(r>>>10))+h(56320+(1023&r));case 3:return h((15&t.charCodeAt(0))<<12|(63&t.charCodeAt(1))<<6|63&t.charCodeAt(2));default:return h((31&t.charCodeAt(0))<<6|63&t.charCodeAt(1))}},D=t=>t.replace(O,L),E=t=>{if(t=t.replace(/\s+/g,""),!f.test(t))throw new TypeError("malformed base64.");t+="==".slice(2-(3&t.length));let e,r,n,o="";for(let a=0;a<t.length;)e=l[t.charAt(a++)]<<18|l[t.charAt(a++)]<<12|(r=l[t.charAt(a++)])<<6|(n=l[t.charAt(a++)]),o+=64===r?h(e>>16&255):64===n?h(e>>16&255,e>>8&255):h(e>>16&255,e>>8&255,255&e);return o},j="function"===typeof atob?t=>atob(d(t)):a?t=>Buffer.from(t,"base64").toString("binary"):E,P=a?t=>p(Buffer.from(t,"base64")):t=>p(j(t).split("").map(t=>t.charCodeAt(0))),C=t=>P(T(t)),B=a?t=>Buffer.from(t,"base64").toString("utf8"):i?t=>i.decode(P(t)):t=>D(j(t)),T=t=>d(t.replace(/[-_]/g,t=>"-"==t?"+":"/")),F=t=>B(T(t)),U=t=>{if("string"!==typeof t)return!1;const e=t.replace(/\s+/g,"").replace(/={0,2}$/,"");return!/[^\s0-9a-zA-Z\+/]/.test(e)||!/[^\s0-9a-zA-Z\-_]/.test(e)},z=t=>({value:t,enumerable:!1,writable:!0,configurable:!0}),R=function(){const t=(t,e)=>Object.defineProperty(String.prototype,t,z(e));t("fromBase64",(function(){return F(this)})),t("toBase64",(function(t){return k(this,t)})),t("toBase64URI",(function(){return k(this,!0)})),t("toBase64URL",(function(){return k(this,!0)})),t("toUint8Array",(function(){return C(this)}))},I=function(){const t=(t,e)=>Object.defineProperty(Uint8Array.prototype,t,z(e));t("toBase64",(function(t){return v(this,t)})),t("toBase64URI",(function(){return v(this,!0)})),t("toBase64URL",(function(){return v(this,!0)}))},N=()=>{R(),I()},$={version:n,VERSION:o,atob:j,atobPolyfill:E,btoa:y,btoaPolyfill:m,fromBase64:F,toBase64:k,encode:k,encodeURI:A,encodeURL:A,utob:x,btou:D,decode:F,isValid:U,fromUint8Array:v,toUint8Array:C,extendString:R,extendUint8Array:I,extendBuiltins:N}}}]);