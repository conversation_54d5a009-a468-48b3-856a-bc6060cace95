(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-consumption-rules-operations-management-survey-components-SurveyDialog"],{"2a3f":function(t,o,i){"use strict";i("cc46")},"88c4":function(t,o,i){"use strict";i.r(o);var e=function(){var t=this,o=t._self._c;return o("dialog-message",{attrs:{show:t.visible,title:t.title,loading:t.isLoading,customClass:"ps-dialog",width:"500px"},on:{"update:show":function(o){t.visible=o},"update:loading":function(o){t.isLoading=o},close:t.handleClose}},[o("el-form",{ref:"dialogForm",staticClass:"dialog-form SurveyDialog",attrs:{model:t.dialogForm,"status-icon":"",rules:t.dialogFormRules,"label-width":"80px"},nativeOn:{submit:function(t){t.preventDefault()}}},[o("el-form-item",{attrs:{label:"题目类型"}},[o("el-radio-group",{staticClass:"ps-radio",on:{change:t.changeType},model:{value:t.dialogForm.type,callback:function(o){t.$set(t.dialogForm,"type",o)},expression:"dialogForm.type"}},[o("el-radio",{attrs:{label:"CHOICE"}},[t._v("选择题")]),o("el-radio",{attrs:{label:"ANSWER"}},[t._v("简答题")]),o("el-radio",{attrs:{label:"SCORE"}},[t._v("评分")])],1)],1),"CHOICE"===t.dialogForm.type?o("el-form-item",[o("el-checkbox",{staticClass:"ps-checkbox",model:{value:t.dialogForm.multiple_choice,callback:function(o){t.$set(t.dialogForm,"multiple_choice",o)},expression:"dialogForm.multiple_choice"}},[t._v("可多选")])],1):t._e(),o("el-form-item",{attrs:{label:"题目内容",prop:"title"}},[o("el-input",{staticClass:"ps-input w-300",attrs:{max:"20"},model:{value:t.dialogForm.title,callback:function(o){t.$set(t.dialogForm,"title",o)},expression:"dialogForm.title"}})],1),"CHOICE"===t.dialogForm.type?o("div",{staticClass:"m-l-80 m-b-8"},[t._l(t.dialogForm.options_data,(function(i,e){return o("div",{key:e,staticClass:"options-wrap"},[o("el-form-item",{staticClass:"form-inline option-form",attrs:{label:i.options,prop:"options_data."+e+".content",rules:t.dialogFormRules.content,"label-width":"35px"}},[o("el-input",{staticClass:"ps-input w-300",attrs:{max:"20"},model:{value:i.content,callback:function(o){t.$set(i,"content",o)},expression:"optionsItem.content"}}),t.dialogForm.options_data.length>2?o("el-button",{staticClass:"ps-red m-l-10",attrs:{type:"text",size:"small"},on:{click:function(o){return t.delOptions(e)}}},[t._v("删除")]):t._e()],1)],1)})),t.dialogForm.options_data.length<7?o("el-button",{attrs:{type:"text",size:"small"},on:{click:t.addoption}},[t._v("添加选项")]):t._e()],2):t._e()],1),o("template",{slot:"tool"},[o("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[o("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:t.isLoading},on:{click:t.clickCancleHandle}},[t._v(" 取消 ")]),o("el-button",{staticClass:"ps-btn",attrs:{disabled:t.isLoading,type:"primary"},on:{click:t.clickConfirmHandle}},[t._v(" 确定 ")])],1)])],2)},a=[],l=i("ed08"),n=["A","B","C","D","E","F","G"],s={name:"SurveyDialog",props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:""},isshow:Boolean,confirm:Function},data:function(){return{isLoading:!1,dialogForm:{type:"CHOICE",title:"",multiple_choice:!1,options_data:[{options:"A",content:""},{options:"B",content:""}]},dialogFormRules:{title:[{required:!0,message:"内容不能为空",trigger:"blur"}],content:[{required:!0,message:"内容不能为空",trigger:"blur"}]}}},computed:{visible:{get:function(){return this.isshow},set:function(t){this.$emit("update:isshow",t)}}},watch:{visible:function(){this.visible&&(this.dialogForm={type:"CHOICE",title:"",multiple_choice:!1,options_data:[{options:"A",content:""},{options:"B",content:""}]})}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){},clickConfirmHandle:function(){var t=this;this.$refs.dialogForm.validate((function(o){if(o){var i={type:t.dialogForm.type,content:t.dialogForm.title,multiple_choice:t.dialogForm.multiple_choice};"CHOICE"===t.dialogForm.type&&(i.options_data=Object(l["f"])(t.dialogForm.options_data)),t.confirm(i)}}))},addoption:function(){var t=this.dialogForm.options_data[this.dialogForm.options_data.length-1].options,o=n[n.indexOf(t)+1];this.dialogForm.options_data.push({options:o,content:""})},delOptions:function(t){this.dialogForm.options_data.splice(t,1),this.dialogForm.options_data.map((function(t,o){t.options=n[o]}))},changeType:function(){this.$refs.dialogForm.resetFields()},clickCancleHandle:function(){this.visible=!1},handleClose:function(t){this.isLoading=!1,this.visible=!1,this.$refs.dialogForm.resetFields()}}},r=s,c=(i("2a3f"),i("2877")),d=Object(c["a"])(r,e,a,!1,null,"15db5582",null);o["default"]=d.exports},cc46:function(t,o,i){}}]);