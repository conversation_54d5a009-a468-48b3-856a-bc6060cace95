(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-consumption-rules-operations-management-survey-SurveyDataTotal"],{"2bc4":function(t,e,r){},"318c":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"SurveyDataTotal container-wrapper"},[e("refresh-tool",{on:{refreshPage:t.refreshHandle}}),e("div",{staticClass:"table-wrapper p-t-20 p-l-20"},t._l(t.dataTotalList,(function(r,n){return e("div",{key:r.survey_question_id,staticClass:"m-b-25"},[e("div",{staticClass:"question-name m-b-10"},[t._v(t._s(n+1)+"、"+t._s(r.content))]),"CHOICE"===r.type?e("div",[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableData",refInFor:!0,staticStyle:{width:"100%"},attrs:{data:r.choiceStatistics,stripe:"","header-row-class-name":"ps-table-header-row"}},[e("el-table-column",{attrs:{prop:"content",label:"选项",align:"center"}}),e("el-table-column",{attrs:{prop:"count",label:"小计",align:"center"}}),e("el-table-column",{attrs:{prop:"percent",label:"比例",align:"center"},scopedSlots:t._u([{key:"default",fn:function(r){return[r.row.percent?e("div",[e("el-progress",{attrs:{percentage:r.row.percent}})],1):t._e()]}}],null,!0)})],1)],1):e("div",[e("el-button",{staticClass:"ps-btn",attrs:{type:"primary"},on:{click:function(e){return t.openDialog(r)}}},[t._v("详细作答情况")])],1)])})),0),e("el-dialog",{attrs:{title:t.dialogTitle,visible:t.dialogVisible,width:"600px",top:"30vh","custom-class":"ps-dialog","close-on-click-modal":!1},on:{"update:visible":function(e){t.dialogVisible=e}}},["ANSWER"===t.dialogType?e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],key:t.tableKey,ref:"tableData",staticStyle:{width:"100%"},attrs:{data:t.dialogTable,stripe:"","header-row-class-name":"ps-table-header-row"}},[e("el-table-column",{attrs:{type:"index",index:t.indexMethod,label:"序号",align:"center"}}),e("el-table-column",{attrs:{prop:"name",label:"姓名",align:"center"}}),e("el-table-column",{attrs:{prop:"create_time",label:"提交答卷时间",align:"center"}}),e("el-table-column",{attrs:{prop:"content",label:"答案",align:"center"}})],1):t._e(),"SCORE"===t.dialogType?e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],key:t.tableKey,ref:"tableData",staticStyle:{width:"100%"},attrs:{data:t.dialogTable,stripe:"","header-row-class-name":"ps-table-header-row"}},[e("el-table-column",{attrs:{prop:"score",label:"评分",align:"center"}}),e("el-table-column",{attrs:{prop:"count",label:"人数",align:"center"}}),e("el-table-column",{attrs:{prop:"percent",label:"占比",align:"center"},scopedSlots:t._u([{key:"default",fn:function(r){return[r.row.percent?e("div",[e("el-progress",{attrs:{percentage:r.row.percent}})],1):t._e()]}}],null,!1,1555576518)})],1):t._e(),e("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[e("el-pagination",{staticClass:"ps-text",attrs:{"current-page":t.currentPage,"page-size":t.pageSize,layout:"total, prev, pager, next",total:t.totalCount,background:"","popper-class":"ps-popper-select"},on:{"current-change":t.handleCurrentChange}})],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"ps-btn",attrs:{type:"primary"},on:{click:function(e){t.dialogVisible=!1}}},[t._v("关 闭")])],1)],1)],1)},o=[];function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}function i(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */i=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},l="function"==typeof Symbol?Symbol:{},c=l.iterator||"@@iterator",s=l.asyncIterator||"@@asyncIterator",u=l.toStringTag||"@@toStringTag";function p(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{p({},"")}catch(t){p=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var a=e&&e.prototype instanceof m?e:m,i=Object.create(a.prototype),l=new D(n||[]);return o(i,"_invoke",{value:P(t,r,l)}),i}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var d="suspendedStart",g="suspendedYield",y="executing",v="completed",b={};function m(){}function w(){}function S(){}var L={};p(L,c,(function(){return this}));var x=Object.getPrototypeOf,O=x&&x(x(N([])));O&&O!==r&&n.call(O,c)&&(L=O);var _=S.prototype=m.prototype=Object.create(L);function T(t){["next","throw","return"].forEach((function(e){p(t,e,(function(t){return this._invoke(e,t)}))}))}function E(t,e){function r(o,i,l,c){var s=h(t[o],t,i);if("throw"!==s.type){var u=s.arg,p=u.value;return p&&"object"==a(p)&&n.call(p,"__await")?e.resolve(p.__await).then((function(t){r("next",t,l,c)}),(function(t){r("throw",t,l,c)})):e.resolve(p).then((function(t){u.value=t,l(u)}),(function(t){return r("throw",t,l,c)}))}c(s.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function P(e,r,n){var o=d;return function(a,i){if(o===y)throw Error("Generator is already running");if(o===v){if("throw"===a)throw i;return{value:t,done:!0}}for(n.method=a,n.arg=i;;){var l=n.delegate;if(l){var c=C(l,n);if(c){if(c===b)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===d)throw o=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=y;var s=h(e,r,n);if("normal"===s.type){if(o=n.done?v:g,s.arg===b)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=v,n.method="throw",n.arg=s.arg)}}}function C(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,C(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),b;var a=h(o,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,b;var i=a.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,b):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,b)}function j(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function k(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function D(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(j,this),this.reset(!0)}function N(e){if(e||""===e){var r=e[c];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(a(e)+" is not iterable")}return w.prototype=S,o(_,"constructor",{value:S,configurable:!0}),o(S,"constructor",{value:w,configurable:!0}),w.displayName=p(S,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,S):(t.__proto__=S,p(t,u,"GeneratorFunction")),t.prototype=Object.create(_),t},e.awrap=function(t){return{__await:t}},T(E.prototype),p(E.prototype,s,(function(){return this})),e.AsyncIterator=E,e.async=function(t,r,n,o,a){void 0===a&&(a=Promise);var i=new E(f(t,r,n,o),a);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},T(_),p(_,u,"Generator"),p(_,c,(function(){return this})),p(_,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=N,D.prototype={constructor:D,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(k),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return l.type="throw",l.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],l=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var c=n.call(i,"catchLoc"),s=n.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=t,i.arg=e,a?(this.method="next",this.next=a.finallyLoc,b):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),b},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),k(r),b}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;k(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:N(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),b}},e}function l(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function c(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?l(Object(r),!0).forEach((function(e){s(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function s(t,e,r){return(e=u(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function u(t){var e=p(t,"string");return"symbol"==a(e)?e:e+""}function p(t,e){if("object"!=a(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=a(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function f(t,e,r,n,o,a,i){try{var l=t[a](i),c=l.value}catch(t){return void r(t)}l.done?e(c):Promise.resolve(c).then(n,o)}function h(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var a=t.apply(e,r);function i(t){f(a,n,o,i,l,"next",t)}function l(t){f(a,n,o,i,l,"throw",t)}i(void 0)}))}}var d={name:"AddSurvey",data:function(){return{isLoading:!1,surveyInfoId:"",surveyTotalCount:0,dataTotalList:[],dialogVisible:!1,dialogTitle:"",dialogType:"",dialogAllTable:[],dialogTable:[],currentPage:1,pageSize:10,totalCount:0,tableKey:0}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.surveyInfoId=this.$route.query.id,this.surveyTotalCount=this.$route.query.num,this.getSurveyDataTotal()},refreshHandle:function(){this.initLoad()},getSurveyDataTotal:function(){var t=this;return h(i().mark((function e(){var r;return i().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.$apis.apiBackgroundMarketingSurveyInfoFeedbackCollectPost({id:t.surveyInfoId});case 3:r=e.sent,t.isLoading=!1,0===r.code?t.dataTotalList=r.data.results.map((function(e){if("CHOICE"===e.type){e.choiceStatistics=[];var r=function(t){var r;e.options.map((function(e){e.options===t&&(r=e.content)})),e.choiceStatistics.push(c(c({},e.choice_statistics[t]),{},{content:r}))};for(var n in e.choice_statistics)r(n);e.choiceStatistics.push({content:"本次有效填写人数",count:t.surveyTotalCount,percent:null})}else if("SCORE"===e.type)for(var o in e.scoreStatistics=[],e.score_statistics)e.scoreStatistics.push(c({},e.score_statistics[o]));return e})):t.$message.error(r.msg);case 6:case"end":return e.stop()}}),e)})))()},openDialog:function(t){this.dialogVisible=!0,this.dialogTitle=t.content,this.dialogType=t.type,"ANSWER"===t.type?this.dialogAllTable=t.answer:"SCORE"===t.type&&(this.dialogAllTable=t.scoreStatistics),this.currentPage=1,this.setSelCurrentPageData(),this.tableKey=Math.random()},setSelCurrentPageData:function(){var t=(this.currentPage-1)*this.pageSize,e=(this.currentPage-1)*this.pageSize+this.pageSize;this.totalCount=this.dialogAllTable.length?this.dialogAllTable.length:0,this.dialogTable=[].concat(this.dialogAllTable.slice(t,e))},handleCurrentChange:function(t){this.currentPage=t,this.setSelCurrentPageData()},indexMethod:function(t){return(this.currentPage-1)*this.pageSize+(t+1)}}},g=d,y=(r("8fac"),r("2877")),v=Object(y["a"])(g,n,o,!1,null,null,null);e["default"]=v.exports},"8fac":function(t,e,r){"use strict";r("2bc4")}}]);