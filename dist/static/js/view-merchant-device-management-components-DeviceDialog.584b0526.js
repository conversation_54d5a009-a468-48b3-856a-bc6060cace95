(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-device-management-components-DeviceDialog"],{"32c8":function(e,t,r){"use strict";r("49dc")},"49dc":function(e,t,r){},efb8:function(e,t,r){"use strict";r.r(t);var i=function(){var e=this,t=e._self._c;return t("dialog-message",{attrs:{show:e.visible,title:e.title,loading:e.isLoading,customClass:"ps-dialog",width:e.width},on:{"update:show":function(t){e.visible=t},"update:loading":function(t){e.isLoading=t},close:e.handleClose}},[e.visible?t("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"deviceForm",staticClass:"dialog-form",attrs:{model:e.deviceForm,"status-icon":"",rules:e.deviceFormRules,"label-width":"120px",inline:""},nativeOn:{submit:function(e){e.preventDefault()}}},["group"===e.type?t("div",[t("el-form-item",{attrs:{label:"","label-width":"0",prop:"groupType"}},[t("el-select",{staticClass:"ps-select",model:{value:e.deviceForm.groupType,callback:function(t){e.$set(e.deviceForm,"groupType",t)},expression:"deviceForm.groupType"}},e._l(e.groupTypeList,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("div",{staticClass:"red m-b-10"},[e._v(e._s("all"===e.deviceForm.groupType?"全天所有餐段仅适用分组可使用该设备":"可按餐段设置设备的适用分组"))]),"meal"===e.deviceForm.groupType?t("div",e._l(e.mealList,(function(r){return t("el-form-item",{key:r.value,staticClass:"text-align-left",attrs:{label:r.label,"label-width":"90px",prop:"allowUserGroup."+r.value}},[t("user-group-select",{staticClass:"search-item-w ps-input w-180",attrs:{multiple:!0,collapseTags:"",clearable:"",placeholder:"请下拉选择","option-data":e.groupList,"show-other":!0},model:{value:e.deviceForm.allowUserGroup[r.value],callback:function(t){e.$set(e.deviceForm.allowUserGroup,r.value,t)},expression:"deviceForm.allowUserGroup[meal.value]"}})],1)})),1):e._e(),"all"===e.deviceForm.groupType?t("div",[t("el-form-item",{staticClass:"text-align-left",attrs:{label:"全天","label-width":"90px",prop:"allowUserGroup.all"}},[t("user-group-select",{staticClass:"search-item-w ps-input w-180",attrs:{multiple:!0,collapseTags:"",clearable:"",placeholder:"请下拉选择","option-data":e.groupList},model:{value:e.deviceForm.allowUserGroup.all,callback:function(t){e.$set(e.deviceForm.allowUserGroup,"all",t)},expression:"deviceForm.allowUserGroup.all"}})],1)],1):e._e()],1):e._e(),"name"===e.type?t("div",[t("el-form-item",{attrs:{label:"设备名：",prop:"deviceName"}},[t("el-input",{staticClass:"ps-input w-250",attrs:{placeholder:"请输入设备名"},model:{value:e.deviceForm.deviceName,callback:function(t){e.$set(e.deviceForm,"deviceName",t)},expression:"deviceForm.deviceName"}})],1)],1):e._e(),"setting"===e.type?t("div",["QCG"!==e.deviceInfo.device_type?t("el-form-item",{attrs:{label:"菜谱设置："}},[t("el-radio-group",{staticClass:"ps-radio",on:{change:e.changeMenuList},model:{value:e.deviceForm.menuListType,callback:function(t){e.$set(e.deviceForm,"menuListType",t)},expression:"deviceForm.menuListType"}},[t("el-radio",{attrs:{label:"week"}},[e._v("周菜谱")]),t("el-radio",{attrs:{label:"month"}},[e._v("月菜谱")])],1)],1):e._e(),t("el-form-item",{attrs:{label:"入口密码：",prop:"password"}},[t("el-input",{staticClass:"ps-input w-250",attrs:{placeholder:"请输入入口密码"},model:{value:e.deviceForm.password,callback:function(t){e.$set(e.deviceForm,"password",t)},expression:"deviceForm.password"}})],1),e.noShowuseOrganizationsType.includes(e.deviceInfo.device_type)||"PS-k1"===e.deviceInfo.device_model?e._e():t("el-form-item",{attrs:{label:"可消费组织：",prop:"payUseOrganizations"}},[t("organization-select",{staticClass:"search-item-w ps-input",attrs:{placeholder:"请选择组织",isLazy:!1,multiple:!0,"check-strictly":!0,role:"merchant","append-to-body":!0,filterable:!1,"disabled-list":[e.deviceInfo.organization],"check-disabled-and-parents":!0,"disabled-same-level":!0,"allow-disabled-same-child":!0},model:{value:e.deviceForm.payUseOrganizations,callback:function(t){e.$set(e.deviceForm,"payUseOrganizations",t)},expression:"deviceForm.payUseOrganizations"}})],1),"QCG"===e.deviceInfo.device_type||"KBCX"===e.deviceInfo.device_type?t("el-form-item",{attrs:{label:"适用组织：",prop:"useOrganizations"}},[t("organization-select",{staticClass:"search-item-w ps-input",attrs:{placeholder:"请选择组织",isLazy:!1,multiple:!0,"check-strictly":!0,role:"merchant","append-to-body":!0,filterable:!1,"org-id":e.deviceInfo.organization,"only-show-org-id-tree":!0},model:{value:e.deviceForm.useOrganizations,callback:function(t){e.$set(e.deviceForm,"useOrganizations",t)},expression:"deviceForm.useOrganizations"}})],1):e._e(),e.noShowuseOrganizationsType.includes(e.deviceInfo.device_type)||"JST"===e.deviceInfo.device_type||"PS-k1"===e.deviceInfo.device_model?e._e():t("el-form-item",{attrs:{label:"可核销组织：",prop:"useOrganizations"}},[t("organization-parent-select",{staticClass:"search-item-w ps-input",attrs:{placeholder:"请选择组织",isLazy:!1,multiple:!0,"check-strictly":!0,role:"merchant","append-to-body":!0,filterable:!1,"disabled-list":[e.deviceInfo.organization],"org-id":e.deviceInfo.organization,"disabled-same-level":!0,"allow-disabled-same-child":!0},model:{value:e.deviceForm.useOrganizations,callback:function(t){e.$set(e.deviceForm,"useOrganizations",t)},expression:"deviceForm.useOrganizations"}})],1),"ZZKWJ"===e.deviceInfo.device_type?t("el-form-item",{attrs:{label:"设备提示语："}},[t("el-input",{staticClass:"ps-input w-250",attrs:{placeholder:"例：如有疑问请联系管理员",maxlength:"25"},model:{value:e.deviceForm.promptMessage,callback:function(t){e.$set(e.deviceForm,"promptMessage",t)},expression:"deviceForm.promptMessage"}})],1):e._e(),"custom"===e.deviceForm.refundType?t("el-form-item",{attrs:{label:" "}},[t("span",[e._v("允许订单在")]),t("el-input-number",{attrs:{min:0,max:24},model:{value:e.deviceForm.refundTime,callback:function(t){e.$set(e.deviceForm,"refundTime",t)},expression:"deviceForm.refundTime"}}),t("span",[e._v("小时内，可进行退款")])],1):e._e(),"meal"===e.deviceForm.refundType?t("el-form-item",{attrs:{label:" "}},[t("span",[e._v("允许餐段")]),t("el-input-number",{attrs:{min:0,max:24},model:{value:e.deviceForm.refundTime,callback:function(t){e.$set(e.deviceForm,"refundTime",t)},expression:"deviceForm.refundTime"}}),t("span",[e._v("小时内，可进行退款")])],1):e._e()],1):e._e(),"muleditname"===e.type?t("div",[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticClass:"ps-table-tree",staticStyle:{width:"100%"},attrs:{data:e.deviceForm.editData,stripe:"","header-row-class-name":"ps-table-header-row"}},[t("el-table-column",{attrs:{prop:"device_name",label:"原设备名",align:"center"}}),t("el-table-column",{attrs:{prop:"",label:"修改设备名",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-input",{staticClass:"ps-input",model:{value:r.row.newName,callback:function(t){e.$set(r.row,"newName",t)},expression:"scope.row.newName"}})]}}],null,!1,169052810)})],1)],1):e._e(),"autoSelling"===e.type?t("div",[t("el-form-item",{attrs:{label:"可消费组织：",prop:"payUseOrganizations"}},[t("organization-select",{staticClass:"search-item-w ps-input",attrs:{placeholder:"请选择组织",isLazy:!1,multiple:!0,"check-strictly":!0,role:"merchant","append-to-body":!0,filterable:!1,"disabled-list":[e.deviceInfo.organization],"check-disabled-and-parents":!0,"disabled-same-level":!0,"allow-disabled-same-child":!0},model:{value:e.deviceForm.payUseOrganizations,callback:function(t){e.$set(e.deviceForm,"payUseOrganizations",t)},expression:"deviceForm.payUseOrganizations"}})],1)],1):e._e()]):e._e(),t("template",{slot:"tool"},[t("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:e.clickCancleHandle}},[e._v(" 取消 ")]),t("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickConfirmHandle}},[e._v(" 确定 ")])],1)])],2)},n=[],o=r("390a"),a=r("cbfb"),s=r("eb51"),c=r("ed08");function l(e){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},l(e)}function u(e,t){return m(e)||f(e,t)||p(e,t)||d()}function d(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function p(e,t){if(e){if("string"==typeof e)return v(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?v(e,t):void 0}}function v(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,i=Array(t);r<t;r++)i[r]=e[r];return i}function f(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var i,n,o,a,s=[],c=!0,l=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(i=o.call(r)).done)&&(s.push(i.value),s.length!==t);c=!0);}catch(e){l=!0,n=e}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw n}}return s}}function m(e){if(Array.isArray(e))return e}function h(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */h=function(){return t};var e,t={},r=Object.prototype,i=r.hasOwnProperty,n=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function d(e,t,r,i){var o=t&&t.prototype instanceof b?t:b,a=Object.create(o.prototype),s=new S(i||[]);return n(a,"_invoke",{value:I(e,r,s)}),a}function p(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var v="suspendedStart",f="suspendedYield",m="executing",g="completed",y={};function b(){}function w(){}function _(){}var F={};u(F,a,(function(){return this}));var L=Object.getPrototypeOf,O=L&&L(L(j([])));O&&O!==r&&i.call(O,a)&&(F=O);var x=_.prototype=b.prototype=Object.create(F);function k(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function z(e,t){function r(n,o,a,s){var c=p(e[n],e,o);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==l(d)&&i.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,a,s)}),(function(e){r("throw",e,a,s)})):t.resolve(d).then((function(e){u.value=e,a(u)}),(function(e){return r("throw",e,a,s)}))}s(c.arg)}var o;n(this,"_invoke",{value:function(e,i){function n(){return new t((function(t,n){r(e,i,t,n)}))}return o=o?o.then(n,n):n()}})}function I(t,r,i){var n=v;return function(o,a){if(n===m)throw Error("Generator is already running");if(n===g){if("throw"===o)throw a;return{value:e,done:!0}}for(i.method=o,i.arg=a;;){var s=i.delegate;if(s){var c=T(s,i);if(c){if(c===y)continue;return c}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if(n===v)throw n=g,i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);n=m;var l=p(t,r,i);if("normal"===l.type){if(n=i.done?g:f,l.arg===y)continue;return{value:l.arg,done:i.done}}"throw"===l.type&&(n=g,i.method="throw",i.arg=l.arg)}}}function T(t,r){var i=r.method,n=t.iterator[i];if(n===e)return r.delegate=null,"throw"===i&&t.iterator.return&&(r.method="return",r.arg=e,T(t,r),"throw"===r.method)||"return"!==i&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+i+"' method")),y;var o=p(n,t.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,y;var a=o.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,y):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function C(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function $(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function S(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(C,this),this.reset(!0)}function j(t){if(t||""===t){var r=t[a];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function r(){for(;++n<t.length;)if(i.call(t,n))return r.value=t[n],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(l(t)+" is not iterable")}return w.prototype=_,n(x,"constructor",{value:_,configurable:!0}),n(_,"constructor",{value:w,configurable:!0}),w.displayName=u(_,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,u(e,c,"GeneratorFunction")),e.prototype=Object.create(x),e},t.awrap=function(e){return{__await:e}},k(z.prototype),u(z.prototype,s,(function(){return this})),t.AsyncIterator=z,t.async=function(e,r,i,n,o){void 0===o&&(o=Promise);var a=new z(d(e,r,i,n),o);return t.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},k(x),u(x,c,"Generator"),u(x,a,(function(){return this})),u(x,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var i in t)r.push(i);return r.reverse(),function e(){for(;r.length;){var i=r.pop();if(i in t)return e.value=i,e.done=!1,e}return e.done=!0,e}},t.values=j,S.prototype={constructor:S,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach($),!t)for(var r in this)"t"===r.charAt(0)&&i.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(i,n){return s.type="throw",s.arg=t,r.next=i,n&&(r.method="next",r.arg=e),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var c=i.call(a,"catchLoc"),l=i.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&i.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,y):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),$(r),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var i=r.completion;if("throw"===i.type){var n=i.arg;$(r)}return n}}throw Error("illegal catch attempt")},delegateYield:function(t,r,i){return this.delegate={iterator:j(t),resultName:r,nextLoc:i},"next"===this.method&&(this.arg=e),y}},t}function g(e,t,r,i,n,o,a){try{var s=e[o](a),c=s.value}catch(e){return void r(e)}s.done?t(c):Promise.resolve(c).then(i,n)}function y(e){return function(){var t=this,r=arguments;return new Promise((function(i,n){var o=e.apply(t,r);function a(e){g(o,i,n,a,s,"next",e)}function s(e){g(o,i,n,a,s,"throw",e)}a(void 0)}))}}function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,i)}return r}function w(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach((function(t){_(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function _(e,t,r){return(t=F(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function F(e){var t=L(e,"string");return"symbol"==l(t)?t:t+""}function L(e,t){if("object"!=l(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=l(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var O={name:"trayDialog",components:{UserGroupSelect:o["a"],OrganizationSelect:a["a"],OrganizationParentSelect:s["a"]},props:{loading:Boolean,isshow:Boolean,type:{type:String,default:""},title:{type:String,default:""},width:{type:String,default:"600px"},deviceInfo:{type:Object,default:function(){return{}}},deviceList:{type:Array,default:function(){return[]}},confirm:Function},data:function(){var e=function(e,t,r){var i=/^[0-9A-Za-z]{8,20}$/;t&&!i.test(t)?r(new Error("密码长度8~20位，英文加数字")):r()},t=function(e,t,r){if(!t)return r(new Error("密码不能为空"));var i=/^[0-9A-Za-z]{8,20}$/;i.test(t)?r():r(new Error("密码长度8~20位，英文加数字"))};return{isLoading:!1,deviceForm:{group:[],groupType:"",allowUserGroup:{all:[],breakfast:[],lunch:[],afternoon:[],dinner:[],supper:[],morning:[]},deviceName:"",menuListType:"week",menuId:"",password:"",isRefund:!1,refundPassword:"",refundType:"any",refundTime:0,editData:[],promptMessage:"",useOrganizations:[],payUseOrganizations:[]},deviceFormRules:{deviceName:[{required:!0,message:"请输入设备名",trigger:"blur"}],menuId:[{required:!0,message:"请选择菜谱",trigger:"change"}],password:[{required:!0,validator:t,trigger:"blur"}],refundPassword:[{validator:e,trigger:"blur"}],groupType:[{required:!0,message:"请选择分组类型",trigger:"change"}]},menuList:[],groupTypeList:[{label:"统一设置适用分组",value:"all"},{label:"按餐段设置适用分组",value:"meal"}],mealList:[{label:"早餐",value:"breakfast"},{label:"午餐",value:"lunch"},{label:"下午茶",value:"afternoon"},{label:"晚餐",value:"dinner"},{label:"夜宵",value:"supper"},{label:"凌晨餐",value:"morning"}],groupList:[],noShowuseOrganizationsType:["ZNC","QCG","TPJ","CPT","K1","RLTPBDJ","ZZKWJ","M2","KBCX"]}},computed:{visible:{get:function(){return this.isshow,this.isshow},set:function(e){this.$emit("update:isshow",e)}}},watch:{visible:function(){var e=this;if(this.visible&&this.userGroupList(this.deviceInfo.organization),"muleditname"===this.type)this.deviceForm.editData=[],this.deviceList.map((function(t){e.deviceForm.editData.push(w(w({},t),{},{newName:""}))}));else if("setting"===this.type){switch(this.deviceForm.password=this.deviceInfo.device_settings_pwd,this.deviceForm.refundPassword=this.deviceInfo.device_refund_pwd,this.deviceForm.menuListType=this.deviceInfo.menu_type,this.deviceForm.menuId=this.deviceInfo.menu_type_id,"QCG"!==this.deviceInfo.device_type&&(this.deviceForm.payUseOrganizations=this.deviceInfo.pay_use_organizations),"QCG"===this.deviceInfo.device_type?this.deviceForm.useOrganizations=this.deviceInfo.cupboard_organization_ids:"ZNC"!==this.deviceInfo.device_type&&(this.deviceForm.useOrganizations=this.deviceInfo.use_organizations),this.deviceForm.useOrganizations.push(this.deviceInfo.organization),this.deviceInfo.can_refund){case 1:this.deviceForm.isRefund=!0,this.deviceForm.refundType="any";break;case 2:this.deviceForm.isRefund=!0,this.deviceForm.refundType="custom",this.deviceForm.refundTime=this.deviceInfo.refund_time;break;case 3:this.deviceForm.isRefund=!0,this.deviceForm.refundType="meal",this.deviceForm.refundTime=this.deviceInfo.refund_time;break}"ZZKWJ"===this.deviceInfo.device_type&&(this.deviceForm.promptMessage=this.deviceInfo.prompt_message),this.getMenuList()}else"name"===this.type?this.deviceForm.deviceName=this.deviceInfo.device_name:"group"===this.type?this.deviceInfo.allow_user_group&&(this.deviceForm.groupType=this.deviceInfo.allow_user_group_setting,"all"===this.deviceInfo.allow_user_group_setting?this.deviceForm.allowUserGroup.all=this.deviceInfo.allow_user_group.all?this.deviceInfo.allow_user_group.all:[]:this.mealList.map((function(t){e.deviceForm.allowUserGroup[t.value]=e.deviceInfo.allow_user_group[t.value]}))):"autoSelling"===this.type&&(this.deviceForm.payUseOrganizations=this.deviceInfo.pay_use_organizations)}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getMenuList()},userGroupList:function(e){var t=this;return y(h().mark((function e(){var r;return h().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.apiCardServiceCardUserGroupListPost({status:"enable",is_show_other:!0,page:1,page_size:99999});case 2:r=e.sent,0===r.code?t.groupList=r.data.results:t.$message.error(r.msg);case 4:case"end":return e.stop()}}),e)})))()},clickConfirmHandle:function(){var e=this;this.$refs.deviceForm.validate((function(t){var r;if(t)switch(e.type){case"group":r={device_no:e.deviceInfo.device_no,user_group_setting:e.deviceForm.groupType},"all"===e.deviceForm.groupType?r.allow_user_group={all:e.deviceForm.allowUserGroup.all}:(r.allow_user_group={},e.mealList.map((function(t){r.allow_user_group[t.value]=e.deviceForm.allowUserGroup[t.value]}))),e.modifyDevice(r);break;case"name":r={device_no:e.deviceInfo.device_no,device_name:e.deviceForm.deviceName},e.modifyDevice(r);break;case"muleditname":var i=[],n=[];e.deviceForm.editData.map((function(e){i.push(e.device_no),n.push({device_name:e.newName,device_no:e.device_no})})),r={choices:0,device_nos:i,data:n},e.modifyMulName(r);break;case"setting":r={device_no:e.deviceInfo.device_no,device_settings_pwd:e.deviceForm.password,menu_type:e.deviceForm.menuListType,menu_type_id:e.deviceForm.menuId},e.deviceForm.isRefund?(r.device_refund_pwd=e.deviceForm.refundPassword,"any"===e.deviceForm.refundType?r.can_refund=1:"custom"===e.deviceForm.refundType?(r.can_refund=2,r.refund_time=e.deviceForm.refundTime):"meal"===e.deviceForm.refundType&&(r.can_refund=3,r.refund_time=e.deviceForm.refundTime)):r.can_refund=0,"ZZKWJ"===e.deviceInfo.device_type&&e.deviceForm.promptMessage&&(r.prompt_message=e.deviceForm.promptMessage);var o=e.deviceForm.useOrganizations.indexOf(e.deviceInfo.organization),a=e.deviceForm.payUseOrganizations.indexOf(e.deviceInfo.organization),s=Object(c["f"])(e.deviceForm.useOrganizations),l=Object(c["f"])(e.deviceForm.payUseOrganizations);o>-1&&s.splice(o,1),a>-1&&l.splice(a,1),"QCG"===e.deviceInfo.device_type?r.cupboard_organization_ids=s:"ZNC"!==e.deviceInfo.device_type&&(r.use_organizations=s),"QCG"!==e.deviceInfo.device_type&&(r.pay_use_organizations=l),e.modifyDeviceConfig(r);break;case"autoSelling":if(!e.deviceForm.payUseOrganizations)return e.$messge.error("请选择可消费组织");var u=Object(c["f"])(e.deviceForm.payUseOrganizations);r={device_no:e.deviceInfo.device_no,pay_use_organizations:u,can_refund:0},e.modifyAutoSelling(r);break}}))},modifyMulName:function(e){var t=this;return y(h().mark((function r(){var i;return h().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return t.isLoading=!0,r.next=3,t.$apis.apiBackgroundDeviceDeviceBatchModifyPost(e);case 3:i=r.sent,t.isLoading=!1,0===i.code?(t.$message.success("修改成功"),t.$emit("confirm","search")):t.$message.error(i.msg);case 6:case"end":return r.stop()}}),r)})))()},modifyDevice:function(e){var t=this;return y(h().mark((function r(){var i;return h().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return t.isLoading=!0,r.next=3,t.$apis.apiBackgroundDeviceDeviceModifyPost(e);case 3:i=r.sent,t.isLoading=!1,0===i.code?(t.$message.success("修改成功"),t.$emit("confirm","search")):t.$message.error(i.msg);case 6:case"end":return r.stop()}}),r)})))()},modifyDeviceConfig:function(e){var t=this;return y(h().mark((function r(){var i;return h().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return t.isLoading=!0,r.next=3,t.$apis.apiBackgroundDeviceDeviceConfigPost(e);case 3:i=r.sent,t.isLoading=!1,0===i.code?(t.$message.success("修改成功"),t.$emit("confirm","search")):t.$message.error(i.msg);case 6:case"end":return r.stop()}}),r)})))()},clickCancleHandle:function(){this.visible=!1},handleClose:function(e){this.isLoading=!1,this.visible=!1,this.$refs.deviceForm&&this.$refs.deviceForm.resetFields()},changeMenuList:function(){this.deviceForm.menuId="",this.getMenuList()},getMenuList:function(){var e=this;return y(h().mark((function t(){var r;return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if("week"!==e.deviceForm.menuListType){t.next=6;break}return t.next=3,e.$apis.apiBackgroundFoodMenuWeeklyListPost();case 3:r=t.sent,t.next=9;break;case 6:return t.next=8,e.$apis.apiBackgroundFoodMenuMonthlyListPost();case 8:r=t.sent;case 9:0===r.code?e.menuList=r.data.results:(e.menuList=[],e.$message.error(r.msg));case 10:case"end":return t.stop()}}),t)})))()},modifyAutoSelling:function(e){var t=this;return y(h().mark((function r(){var i,n,o,a;return h().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return t.isLoading=!0,r.next=3,Object(c["Z"])(t.$apis.apiBackgroundDeviceDeviceConfigPost(e));case 3:if(i=r.sent,n=u(i,2),o=n[0],a=n[1],t.isLoading=!1,!o){r.next=10;break}return r.abrupt("return",t.$message.error(o.$message));case 10:a&&0===a.code?(t.$message.success("修改成功"),t.$emit("confirm","search")):t.$message.error(a.msg);case 11:case"end":return r.stop()}}),r)})))()}}},x=O,k=(r("32c8"),r("2877")),z=Object(k["a"])(x,i,n,!1,null,"92ca4066",null);t["default"]=z.exports}}]);