(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-consumption-rules-constants"],{f6ce:function(e,l,a){"use strict";a.r(l),a.d(l,"WALLET_LIST",(function(){return n})),a.d(l,"MEAL_LIST",(function(){return t})),a.d(l,"DEDUCTION_SERVICE",(function(){return u})),a.d(l,"RECHARGE_SERVICE",(function(){return o}));var n=[{name:"储值钱包",key:"store_wallet_on"},{name:"补贴钱包",key:"subsidy_wallet_on"},{name:"赠送钱包",key:"complimentary_wallet_on"},{name:"电子账户钱包",key:"electronic_wallet_on"},{name:"组合支付",key:"combine_wallet_on"}],t=[{name:"早餐",value:"breakfast",disabled:!1},{name:"午餐",value:"lunch",disabled:!1},{name:"下午茶",value:"afternoon",disabled:!1},{name:"晚餐",value:"dinner",disabled:!1},{name:"夜宵",value:"supper",disabled:!1},{name:"凌晨餐",value:"morning",disabled:!1}],u={name:{type:"input",label:"规则名称",value:"",placeholder:"请输入规则名称"},group_nos:{type:"groupSelect",label:"适用分组",value:[],placeholder:"请选择",multiple:!0},org_nos:{type:"organizationSelect",value:[],label:"消费点",dataList:[],multiple:!0,collapseTags:!0,checkStrictly:!0},status:{type:"select",label:"状态",value:"",placeholder:"请选择活动状态",dataList:[{label:"全部",value:""},{label:"启用",value:"enable"},{label:"停用",value:"stop"}]}},o={name:{type:"input",label:"规则名称",value:"",placeholder:"请输入规则名称"},group_nos:{type:"groupSelect",label:"适用分组",value:[],placeholder:"请选择",multiple:!0},status:{type:"select",label:"状态",value:"",placeholder:"请选择活动状态",dataList:[{label:"全部",value:""},{label:"启用",value:"enable"},{label:"停用",value:"stop"}]}}}}]);