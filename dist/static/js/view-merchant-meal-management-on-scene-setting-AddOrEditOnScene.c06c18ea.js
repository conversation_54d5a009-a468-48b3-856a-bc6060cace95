(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-on-scene-setting-AddOrEditOnScene","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-meal-report-MealPackageRule"],{"0eb7":function(e,a,n){"use strict";n.r(a);var t=function(){var e=this,a=e._self._c;return a("div",{staticClass:"AddOrEditOnScene container-wrapper"},[a("div",{staticClass:"table-wrapper"},[a("div",{staticClass:"table-header"},[a("div",{staticClass:"table-title"},[e._v("当前组织："+e._s(e.orgName))])]),a("div",[a("el-form",{ref:"onSceneFormRef",attrs:{model:e.onSceneForm,"label-width":"120px",rules:e.onSceneRules}},["normal"===e.sceneType?a("div",{staticClass:"tips"},[e._v("注：分组+消费点+餐段的组合只能有一条")]):e._e(),"device"===e.sceneType?a("div",{staticClass:"tips"},[e._v("注：消费点+餐段的组合只能有一条")]):e._e(),"normal"===e.sceneType?a("el-form-item",{attrs:{label:"适用分组",prop:"card_groups"}},[a("user-group-select",{attrs:{multiple:""},model:{value:e.onSceneForm.card_groups,callback:function(a){e.$set(e.onSceneForm,"card_groups",a)},expression:"onSceneForm.card_groups"}})],1):e._e(),"device"===e.sceneType?a("el-form-item",{attrs:{label:"适用设备",prop:"devices"}},[a("el-select",{staticClass:"ps-select",attrs:{placeholder:"请选择设备",multiple:""},model:{value:e.onSceneForm.devices,callback:function(a){e.$set(e.onSceneForm,"devices",a)},expression:"onSceneForm.devices"}},e._l(e.deviceList,(function(e){return a("el-option",{key:e.key,attrs:{label:e.name,value:e.key}})})),1)],1):e._e(),a("el-form-item",{attrs:{label:"适用消费点",prop:"organizations"}},[a("consume-select",{attrs:{multiple:""},model:{value:e.onSceneForm.organizations,callback:function(a){e.$set(e.onSceneForm,"organizations",a)},expression:"onSceneForm.organizations"}})],1),a("el-form-item",{attrs:{label:"可堂食餐段",prop:"scene_meal"}},[a("el-checkbox-group",{model:{value:e.onSceneForm.scene_meal,callback:function(a){e.$set(e.onSceneForm,"scene_meal",a)},expression:"onSceneForm.scene_meal"}},e._l(e.mealTypeList,(function(n){return a("el-checkbox",{key:n.value,staticClass:"ps-checkbox",attrs:{label:n.value}},[e._v(" "+e._s(n.label)+" ")])})),1)],1),a("el-form-item",{attrs:{label:"菜品",prop:"menu_type"}},[a("el-radio-group",{staticClass:"ps-radio",model:{value:e.onSceneForm.menu_type,callback:function(a){e.$set(e.onSceneForm,"menu_type",a)},expression:"onSceneForm.menu_type"}},[a("el-radio",{attrs:{label:"month"}},[e._v("月菜谱")]),a("el-radio",{attrs:{label:"week"}},[e._v("周菜谱")])],1)],1),a("el-form-item",{staticClass:"label-block",attrs:{label:"点餐截止时间(餐段结束前)"}},[a("div",{staticClass:"fake-table-wrapper"},[a("div",{staticClass:"fake-table"},e._l(e.mealTypeList,(function(n){return a("div",{key:n.value,staticClass:"fake-col"},[a("span",{staticClass:"fake-col-title"},[e._v(e._s(n.label))]),a("el-input-number",{staticStyle:{width:"90px",margin:"10px"},attrs:{"controls-position":"right",min:0,max:120,disabled:!e.onSceneForm.scene_meal.includes(n.value),size:"mini"},model:{value:e.onSceneForm[n.field],callback:function(a){e.$set(e.onSceneForm,n.field,a)},expression:"onSceneForm[mt.field]"}})],1)})),0),a("span",{staticStyle:{"margin-left":"16px"}},[e._v("分钟")])])]),a("el-form-item",[a("el-button",{staticClass:"ps-origin-btn w-150",attrs:{type:"primary"},on:{click:e.saveForm}},[e._v("保存")])],1)],1)],1)])])},i=[],l=n("390a"),r=n("7c9c"),o=n("c9d9"),s={name:"AddOrEditOnScene",components:{UserGroupSelect:l["a"],ConsumeSelect:r["a"]},data:function(){return{type:"",sceneType:"",orgName:"",onSceneForm:{card_groups:[],onSceneForm:[],organizations:[],scene_meal:[],menu_type:"month",breakfast_ahead:0,lunch_ahead:0,hit_tea_ahead:0,dinner_ahead:0,midnight_ahead:0,early_ahead:0},onSceneRules:{},mealTypeList:o["a"],deviceList:[]}},created:function(){this.initLoad()},methods:{initLoad:function(){this.orgName=this.$store.getters.userInfo.company_name,this.type=this.$route.params.type,this.sceneType=this.$route.params.sceneType,"edit"===this.type&&(this.editData=JSON.parse(decodeURIComponent(this.$route.query.data)),this.initEditForm())},saveForm:function(){}}},c=s,m=(n("ad8c"),n("2877")),u=Object(m["a"])(c,t,i,!1,null,null,null);a["default"]=u.exports},"32cf":function(e,a,n){},ad8c:function(e,a,n){"use strict";n("32cf")},c9d9:function(e,a,n){"use strict";n.d(a,"a",(function(){return l})),n.d(a,"d",(function(){return r})),n.d(a,"b",(function(){return o})),n.d(a,"c",(function(){return s})),n.d(a,"e",(function(){return c})),n.d(a,"f",(function(){return m})),n.d(a,"g",(function(){return u}));var t=n("5a0c"),i=n("da92"),l=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],r=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],o={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},s=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],c=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],m=(t().subtract(7,"day").format("YYYY-MM-DD"),t().format("YYYY-MM-DD"),function(e){return e?"number"===typeof e?i["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:i["a"].divide(e,100).toFixed(2):"0.00"}),u=function(e){return i["a"].times(e,100)}}}]);