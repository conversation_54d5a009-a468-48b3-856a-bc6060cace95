(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-accessControl-attendance-attendance-AttendanceSetting","attendance_setting"],{"443e":function(t,e,n){},"4d3c":function(t,e,n){"use strict";n("443e")},fb21:function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t._self._c;return e("div",{staticClass:"AttendanceSetting container-wrapper"},[e("refresh-tool",{on:{refreshPage:t.refreshHandle}}),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("数据列表")]),e("div",{staticClass:"align-r"},[e("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_attendance.settings.add"],expression:"['background_attendance.settings.add']"}],attrs:{color:"origin",type:"add"},on:{click:function(e){return t.gotoAddOrEdit("add")}}},[t._v("新建")])],1)]),e("div",{staticClass:"table-content"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},[e("el-table-column",{attrs:{prop:"name",label:"名称",align:"center"}}),e("el-table-column",{attrs:{prop:"attendance_groups",label:"适用考勤组",align:"center"}}),e("el-table-column",{attrs:{fixed:"right",label:"操作",align:"center"},scopedSlots:t._u([{key:"default",fn:function(n){return[e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_attendance.settings.modify"],expression:"['background_attendance.settings.modify']"}],staticClass:"ps-bule",attrs:{type:"text",size:"small"},on:{click:function(e){return t.gotoAddOrEdit("edit",n.row)}}},[t._v("编辑")]),e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_attendance.settings.delete"],expression:"['background_attendance.settings.delete']"}],staticClass:"ps-warn",attrs:{type:"text",size:"small"},on:{click:function(e){return t.delPushSetting(n.row.id)}}},[t._v("删除")])]}}])})],1)],1),e("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[e("el-pagination",{staticClass:"ps-text",attrs:{"current-page":t.currentPage,"page-size":t.pageSize,layout:"total, prev, pager, next",total:t.totalCount,background:"","popper-class":"ps-popper-select"},on:{"current-change":t.handleCurrentChange}})],1)])],1)},a=[],o=n("ed08");function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,a=Object.defineProperty||function(t,e,n){t[e]=n.value},o="function"==typeof Symbol?Symbol:{},s=o.iterator||"@@iterator",u=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function f(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,n){return t[e]=n}}function d(t,e,n,r){var o=e&&e.prototype instanceof b?e:b,i=Object.create(o.prototype),c=new N(r||[]);return a(i,"_invoke",{value:O(t,n,c)}),i}function h(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=d;var p="suspendedStart",g="suspendedYield",v="executing",y="completed",m={};function b(){}function w(){}function x(){}var L={};f(L,s,(function(){return this}));var _=Object.getPrototypeOf,S=_&&_(_(z([])));S&&S!==n&&r.call(S,s)&&(L=S);var k=x.prototype=b.prototype=Object.create(L);function E(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function C(t,e){function n(a,o,c,s){var u=h(t[a],t,o);if("throw"!==u.type){var l=u.arg,f=l.value;return f&&"object"==i(f)&&r.call(f,"__await")?e.resolve(f.__await).then((function(t){n("next",t,c,s)}),(function(t){n("throw",t,c,s)})):e.resolve(f).then((function(t){l.value=t,c(l)}),(function(t){return n("throw",t,c,s)}))}s(u.arg)}var o;a(this,"_invoke",{value:function(t,r){function a(){return new e((function(e,a){n(t,r,e,a)}))}return o=o?o.then(a,a):a()}})}function O(e,n,r){var a=p;return function(o,i){if(a===v)throw Error("Generator is already running");if(a===y){if("throw"===o)throw i;return{value:t,done:!0}}for(r.method=o,r.arg=i;;){var c=r.delegate;if(c){var s=A(c,r);if(s){if(s===m)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===p)throw a=y,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=v;var u=h(e,n,r);if("normal"===u.type){if(a=r.done?y:g,u.arg===m)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(a=y,r.method="throw",r.arg=u.arg)}}}function A(e,n){var r=n.method,a=e.iterator[r];if(a===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,A(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),m;var o=h(a,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,m;var i=o.arg;return i?i.done?(n[e.resultName]=i.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,m):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,m)}function P(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function N(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(P,this),this.reset(!0)}function z(e){if(e||""===e){var n=e[s];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,o=function n(){for(;++a<e.length;)if(r.call(e,a))return n.value=e[a],n.done=!1,n;return n.value=t,n.done=!0,n};return o.next=o}}throw new TypeError(i(e)+" is not iterable")}return w.prototype=x,a(k,"constructor",{value:x,configurable:!0}),a(x,"constructor",{value:w,configurable:!0}),w.displayName=f(x,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,x):(t.__proto__=x,f(t,l,"GeneratorFunction")),t.prototype=Object.create(k),t},e.awrap=function(t){return{__await:t}},E(C.prototype),f(C.prototype,u,(function(){return this})),e.AsyncIterator=C,e.async=function(t,n,r,a,o){void 0===o&&(o=Promise);var i=new C(d(t,n,r,a),o);return e.isGeneratorFunction(n)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},E(k),f(k,l,"Generator"),f(k,s,(function(){return this})),f(k,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=z,N.prototype={constructor:N,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function a(r,a){return c.type="throw",c.arg=e,n.next=r,a&&(n.method="next",n.arg=t),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],c=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var s=r.call(i,"catchLoc"),u=r.call(i,"finallyLoc");if(s&&u){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,m):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),j(n),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var a=r.arg;j(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:z(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),m}},e}function s(t,e,n,r,a,o,i){try{var c=t[o](i),s=c.value}catch(t){return void n(t)}c.done?e(s):Promise.resolve(s).then(r,a)}function u(t){return function(){var e=this,n=arguments;return new Promise((function(r,a){var o=t.apply(e,n);function i(t){s(o,r,a,i,c,"next",t)}function c(t){s(o,r,a,i,c,"throw",t)}i(void 0)}))}}var l={name:"AttendanceSetting",data:function(){return{isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[]}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getAttendanceSettingList()},searchHandle:Object(o["d"])((function(){this.dialogVisible=!1,this.currentPage=1,this.getAttendanceSettingList()}),300),refreshHandle:function(){this.currentPage=1,this.tableData=[],this.initLoad()},getAttendanceSettingList:function(){var t=this;return u(c().mark((function e(){var n;return c().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.$apis.apiBackgroundAttendanceAttendanceSettingsListPost({page:t.currentPage,page_size:t.pageSize});case 3:n=e.sent,t.isLoading=!1,0===n.code?(t.tableData=n.data.results,t.tableData.map((function(t){t.attendance_groups=[],t.attendance_groups_ids=[],t.attendance_groups_list.map((function(e){t.attendance_groups.push(e.name),t.attendance_groups_ids.push(e.id)})),t.attendance_groups=t.attendance_groups.join(",")})),t.totalCount=n.data.count):t.$message.error(n.msg);case 6:case"end":return e.stop()}}),e)})))()},handleSizeChange:function(t){this.pageSize=t,this.getAttendanceSettingList()},handleCurrentChange:function(t){this.currentPage=t,this.getAttendanceSettingList()},delPushSetting:function(t){var e=this;return u(c().mark((function n(){return c().wrap((function(n){while(1)switch(n.prev=n.next){case 0:e.$confirm("确定删除该考勤设置？","提示",{confirmButtonText:e.$t("dialog.confirm_btn"),cancelButtonText:e.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var n=u(c().mark((function n(r,a,o){var i;return c().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if("confirm"!==r){n.next=9;break}return n.next=3,e.$apis.apiBackgroundAttendanceAttendanceSettingsDeletePost({ids:[t]});case 3:i=n.sent,0===i.code?(e.$message.success("删除成功"),e.getAttendanceSettingList()):e.$message.error(i.msg),o(),a.confirmButtonLoading=!1,n.next=10;break;case 9:a.confirmButtonLoading||o();case 10:case"end":return n.stop()}}),n)})));function r(t,e,r){return n.apply(this,arguments)}return r}()}).then((function(t){})).catch((function(t){}));case 1:case"end":return n.stop()}}),n)})))()},gotoAddOrEdit:function(t,e){var n={};"edit"===t&&(n={data:encodeURIComponent(JSON.stringify(e))}),this.$router.push({name:"MerchantAddOrEditAttendanceSetting",params:{type:t},query:n})}}},f=l,d=(n("4d3c"),n("2877")),h=Object(d["a"])(f,r,a,!1,null,"8e316c30",null);e["default"]=h.exports}}]);