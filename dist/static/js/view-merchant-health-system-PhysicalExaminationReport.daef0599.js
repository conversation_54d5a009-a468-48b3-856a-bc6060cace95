(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-health-system-PhysicalExaminationReport","view-merchant-health-system-components-PhysicalExaminationReportDetail"],{"1f65":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e._self._c;return t("div",{attrs:{id:"PhysicalExaminationReport"}},[t("refresh-tool",{on:{refreshPage:e.refreshHandle}}),t("search-form",{ref:"searchRef",attrs:{loading:e.isLoading,"form-setting":e.searchForm},on:{search:e.searchHandle}}),t("div",{staticClass:"table-wrapper"},[t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[e._v("数据列表")]),t("div",{staticClass:"align-r"},[t("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_healthy.healthy_info.batch_add_healthy"],expression:"['background_healthy.healthy_info.batch_add_healthy']"}],attrs:{color:"origin",type:"add"},on:{click:e.showImportDrawer}},[e._v("批量导入")])],1)]),t("div",{staticClass:"table-content"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableView",attrs:{data:e.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},e._l(e.currentTableSetting,(function(a){return t("table-column",{key:a.key,attrs:{col:a},scopedSlots:e._u([{key:"operation",fn:function(a){var i=a.row;return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.showDetail(i)}}},[e._v("报告详情")])]}}],null,!0)})})),1),t("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[t("el-pagination",{staticClass:"ps-text",attrs:{"current-page":e.page,"page-sizes":[10,20,50,100,500],"page-size":e.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)]),t("PhysicalExaminationReportDetail",{attrs:{isShow:e.detailShow,data:e.PhysicalExaminationDetail},on:{"update:isShow":function(t){e.detailShow=t},"update:is-show":function(t){e.detailShow=t}}}),t("import-dialog-drawer",{attrs:{show:e.importDrawerShow,width:"77%",tableSetting:e.drawerTableSetting,title:"导入体检报告",templateUrl:e.downloadUrl,openExcelType:e.openExcelType},on:{cancel:function(t){e.importDrawerShow=!1}}})],1)},n=[],l=a("ed08"),r=a("5d8e"),o=a("5a0c"),s=a.n(o);function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function u(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,i)}return a}function d(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?u(Object(a),!0).forEach((function(t){p(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):u(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function p(e,t,a){return(t=h(t))in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function h(e){var t=m(e,"string");return"symbol"==c(t)?t:t+""}function m(e,t){if("object"!=c(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var i=a.call(e,t||"default");if("object"!=c(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var b={name:"PhysicalExaminationReport",components:{PhysicalExaminationReportDetail:r["default"]},data:function(){return{isLoading:!1,searchForm:{create_time:{type:"daterange",label:"上传时间",value:[s()().subtract(7,"day").format("YYYY-MM-DD"),s()().format("YYYY-MM-DD")],clearable:!1},name:{label:"姓名",type:"input",value:"",placeholder:"请输入",labelWidth:"100px"},person_no:{label:"人员编号",type:"input",value:"",placeholder:"请输入",labelWidth:"100px"},phone:{label:"手机号",type:"input",value:"",placeholder:"请输入",labelWidth:"100px"},data_source:{label:"报告类型",type:"select",value:"",placeholder:"请选择",clearable:!0,dataList:[{value:"",label:"全部"},{value:"custom",label:"后台导入"},{value:"shanghe",label:"上禾体检称"}]}},tableData:[],currentTableSetting:[{label:"姓名",key:"name"},{label:"人员编号",key:"person_no"},{label:"手机号",key:"phone"},{label:"报告类型",key:"data_source_alias"},{label:"体检日期",key:"check_date"},{label:"上传时间",key:"create_time"},{label:"操作人",key:"account_name"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"100"}],drawerTableSetting:[{label:"体检日期",key:"date"},{label:"人员编号",key:"person_no"},{label:"姓名",key:"name"},{label:"性别",key:"gender"},{label:"年龄",key:"age"},{label:"身高（cm）",key:"height"},{label:"体重（kg）",key:"weight"},{label:"左臂高压（mmHg）",key:"sbp"},{label:"左臂低压（mmHg）",key:"dbp"},{label:"左臂心率（次/分）",key:"hr"},{label:"右臂高压（mmHg）",key:"sbpR"},{label:"右臂低压（mmHg）",key:"dbpR"},{label:"右臂心率（次/分）",key:"hrR"},{label:"总胆固醇（mmol/L）",key:"cholesterol"},{label:"血糖（mmol/L）",key:"blood_sugar"},{label:"尿酸（μmol/L）",key:"uric_acid"}],detailShow:!1,importDrawerShow:!1,PhysicalExaminationDetail:{},downloadUrl:"/api/temporary/template_excel/healthy/体检数据导入模板.xlsx",openExcelType:"PhysicalExaminationReportImport",page:1,pageSize:10,totalCount:0}},created:function(){this.initLoad()},methods:{initLoad:function(){this.getDataList()},searchHandle:Object(l["d"])((function(e){e&&"search"===e&&(this.page=1,this.isLoading=!0,this.initLoad())}),300),refreshHandle:function(){this.isLoading=!0,this.page=1,this.initLoad()},getDataList:function(){var e=this,t=d({page:this.page,page_size:this.pageSize},this.formatQueryParams(this.searchForm));this.$apis.apiBackgroundHealthyHealthyInfoCheckDataListPost(t).then((function(t){0===t.code?(e.tableData=Object(l["f"])(t.data.results),e.totalCount=t.data.count,e.isLoading=!1):e.$message.error(t.msg)}))},formatQueryParams:function(e){var t={};for(var a in e)"create_time"===a&&e[a].value.length?(t.start_time=s()(e[a].value[0]).format("YYYY-MM-DD HH:mm:ss"),t.end_time=s()(e[a].value[1]).set("hour",23).set("minute",59).set("second",59).format("YYYY-MM-DD HH:mm:ss")):t[a]=e[a].value||void 0;return t},showDetail:function(e){this.PhysicalExaminationDetail=Object(l["f"])(e),this.detailShow=!0},showImportDrawer:function(){this.importDrawerShow=!0},handleSizeChange:function(e){this.pageSize=e,this.isLoading=!0,this.initLoad()},handleCurrentChange:function(e){this.page=e,this.isLoading=!0,this.initLoad()}}},f=b,y=a("2877"),g=Object(y["a"])(f,i,n,!1,null,"fb03b328",null);t["default"]=g.exports},2619:function(e,t,a){"use strict";a("f3d5")},"5d8e":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"ps-el-drawer"},[t("el-drawer",{attrs:{title:"体检报告详情",visible:e.visible,"show-close":!1,size:"40%"}},[t("div",{staticClass:"p-20"},[t("div",{staticClass:"flex flex-col"},[t("span",{staticClass:"f-w-700 m-b-20"},[e._v("报告类型："+e._s(e.data.data_source_alias))]),e.data.integrity_data&&0!==e.data.integrity_data.length?t("div",e._l(e.data.integrity_data,(function(a,i){return t("div",{key:i,staticClass:"m-b-20"},[t("div",{staticClass:"f-w-700 m-b-10"},[e._v(e._s(a.name))]),t("div",{class:[a.name.includes("血压心率")||a.name.includes("人体成分")?"default-layout":"special-layout","bg-gray"]},e._l(a.children,(function(a,i){return t("div",{key:i,staticClass:"flex-b-c"},[t("div",{staticClass:"m-r-10"},[t("div",[e._v(e._s(a.name)+"："+e._s(a.value||"--")+" "+e._s(a.unit))])]),t("div",{class:["tips","正常"===a.result_text?"normal":"","偏高"===a.result_text?"height":"","偏低"===a.result_text?"low":"","font-size-12"]},[e._v(" "+e._s(a.result_text)+" ")])])})),0)])})),0):t("div",[t("el-empty",{attrs:{description:"暂无数据"}})],1)]),t("div",{staticClass:"ps-el-drawer-footer ps-flex-align-c flex-align-c"},[t("div",{staticClass:"m-r-30"},[t("el-button",{staticClass:"w-100 ps-origin-btn",attrs:{size:"small"},on:{click:function(t){e.visible=!1}}},[e._v("关闭")])],1)])])])],1)},n=[],l={name:"PhysicalExaminationReportDetail",props:{isShow:Boolean,reportName:String,data:{type:Object,default:function(){return{}}}},computed:{visible:{get:function(){return this.isShow},set:function(e){this.$emit("update:isShow",e)}}},data:function(){return{isLoading:!1}},methods:{}},r=l,o=(a("2619"),a("2877")),s=Object(o["a"])(r,i,n,!1,null,"28faa09c",null);t["default"]=s.exports},f3d5:function(e,t,a){}}]);