(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-label-admin-constants"],{f5d9:function(e,a,l){"use strict";l.r(a),l.d(a,"recentSevenDay",(function(){return n})),l.d(a,"PLATFORM_LABEL",(function(){return i})),l.d(a,"INGREDIENTS_LABEL",(function(){return r})),l.d(a,"MERCHANT_LABEL",(function(){return u})),l.d(a,"USER_LABEL",(function(){return c}));var t=l("5a0c"),n=[t().subtract(7,"day").format("YYYY-MM-DD"),t().format("YYYY-MM-DD")],i={name:{type:"input",label:"标签组/标签名称",value:"",placeholder:"请输入标签组/标签名称"},consume_organization_ids:{type:"organizationSelect",value:[],label:"可见范围",listNameKey:"name",listValueKey:"id",dataList:[],multiple:!0,checkStrictly:!0,collapseTags:!0,clearable:!0,role:"super"}},r={name:{type:"input",label:"标签组/标签名称",value:"",placeholder:"请输入标签组/标签名称",maxlength:15},consume_organization_ids:{type:"organizationSelect",value:[],label:"可见范围",listNameKey:"name",listValueKey:"id",dataList:[],multiple:!0,checkStrictly:!0,collapseTags:!0,clearable:!0,role:"super"}},u={name:{type:"input",label:"标签组/标签名称",value:"",placeholder:"请输入标签组/标签名称",maxlength:15},select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"同步时间",value:"",clearable:!0},sync_status:{type:"select",value:"",label:"是否同步",clearable:!1,dataList:[{label:"是",value:!0},{label:"否",value:!1}]}},c={name:{type:"input",label:"标签组/标签名称",value:"",placeholder:"请输入标签组/标签名称"}}}}]);