(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-components-menu-selectModelDialog"],{"0952":function(t,e,r){},"3be8":function(t,e,r){"use strict";r("0952")},"751a":function(t,e,r){},aa12:function(t,e,r){"use strict";r("751a")},b9c1:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",[e("dialog-message",{staticClass:"select-model-wrapper",attrs:{show:t.visible,title:t.title,customClass:"ps-dialog",width:t.width,height:200},on:{"update:show":function(e){t.visible=e},close:t.handleClose}},[e("div",{staticClass:"select-model-dialog"},[e("div",{staticClass:"title"},[e("i",{staticClass:"el-icon-question",staticStyle:{color:"#ff9b45"}}),t._v(" 开启营养指导，需选择菜谱对应的集体人群 ")]),e("div",{staticClass:"model-select-box"},t._l(t.nutritionRuleList,(function(r,n){return e("div",{key:n,class:["model-wrapp",t.modelIndex===n?"model-active":""],on:{click:function(e){return t.clickModel(r,n)}}},[e("div",{staticClass:"model-content"},[e("div",{staticClass:"model-title"},[t._v(t._s(r.name))]),e("div",{staticClass:"model-content-text"},[t._v(" "+t._s(r.remark)+" ")])])])})),0),e("div",{staticClass:"align-r"},[e("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(e){return t.addAndEditCollective("add")}}},[t._v(" 新增集体 ")])],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"progressTableData",staticClass:"ps-table",staticStyle:{width:"100%"},attrs:{data:t.tableData,"row-key":"id",stripe:"","header-row-class-name":"ps-table-header-row"}},[e("el-table-column",{attrs:{label:"",width:"55"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("el-radio",{staticClass:"ps-radio",attrs:{label:r.row.id},on:{change:t.singleElection},model:{value:t.templateSelection,callback:function(e){t.templateSelection=e},expression:"templateSelection"}},[e("span")])]}}])}),e("el-table-column",{attrs:{prop:"name",label:"名称",align:"center"}}),e("el-table-column",{attrs:{prop:"number",label:"总人数",align:"center"}}),e("el-table-column",{attrs:{prop:"man_number",label:"男性人数",align:"center"}}),e("el-table-column",{attrs:{prop:"women_number",label:"女性人数",align:"center"}}),e("el-table-column",{attrs:{prop:"",label:"所含人群及人数",align:"center",width:"220px"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("div",{staticClass:"collapse-wrapper"},[e("div",{staticClass:"collapse-list hide"},t._l(r.row.crowd_list,(function(r,n){return e("el-tag",{key:n,staticClass:"m-r-5 m-t-5 collapse-data",attrs:{size:"medium",effect:"plain",type:"info"}},[t._v(" "+t._s(r.crowd_name)+"（"+t._s(r.number)+"） ")])})),1)])]}}])}),e("el-table-column",{attrs:{prop:"",label:"标签",align:"center",width:"220px"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("div",{staticClass:"collapse-wrapper"},[e("div",{staticClass:"collapse-list hide"},t._l(r.row.label,(function(n,i){return e("el-tag",{key:i,staticClass:"m-r-5 m-t-5 collapse-data",attrs:{size:"medium",effect:"plain",type:"danger",closable:""},on:{close:function(e){return t.closeTag(n,r.row)}}},[t._v(" "+t._s(n.name)+" ")])})),1)])]}}])}),e("el-table-column",{attrs:{prop:"remark",label:"备注",align:"center"}})],1),e("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[e("el-pagination",{staticClass:"ps-text",attrs:{"current-page":t.currentPage,"page-size":t.pageSize,layout:"prev, pager, next, total, jumper",total:t.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1),e("template",{slot:"tool"},[e("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:t.isLoading},on:{click:t.clickCancleHandle}},[t._v(" 取消 ")]),e("el-button",{staticClass:"ps-btn",attrs:{disabled:t.isLoading,type:"primary"},on:{click:t.clickSelect}},[t._v(" 选择 ")])],1)])],2),t.collectiveDialogVisible?e("add-and-modify-collective",{attrs:{isshow:t.collectiveDialogVisible,title:t.collectiveDialogTitle,type:t.collectiveDialogType,confirm:t.getCollectiveList},on:{"update:isshow":function(e){t.collectiveDialogVisible=e}}}):t._e()],1)},i=[],o=r("ed08"),a=r("f72e");function l(t){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},l(t)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function d(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{d({},"")}catch(t){d=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var o=e&&e.prototype instanceof b?e:b,a=Object.create(o.prototype),l=new T(n||[]);return i(a,"_invoke",{value:j(t,r,l)}),a}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var h="suspendedStart",g="suspendedYield",m="executing",v="completed",y={};function b(){}function w(){}function x(){}var L={};d(L,a,(function(){return this}));var _=Object.getPrototypeOf,C=_&&_(_($([])));C&&C!==r&&n.call(C,a)&&(L=C);var k=x.prototype=b.prototype=Object.create(L);function S(t){["next","throw","return"].forEach((function(e){d(t,e,(function(t){return this._invoke(e,t)}))}))}function E(t,e){function r(i,o,a,s){var c=p(t[i],t,o);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==l(d)&&n.call(d,"__await")?e.resolve(d.__await).then((function(t){r("next",t,a,s)}),(function(t){r("throw",t,a,s)})):e.resolve(d).then((function(t){u.value=t,a(u)}),(function(t){return r("throw",t,a,s)}))}s(c.arg)}var o;i(this,"_invoke",{value:function(t,n){function i(){return new e((function(e,i){r(t,n,e,i)}))}return o=o?o.then(i,i):i()}})}function j(e,r,n){var i=h;return function(o,a){if(i===m)throw Error("Generator is already running");if(i===v){if("throw"===o)throw a;return{value:t,done:!0}}for(n.method=o,n.arg=a;;){var l=n.delegate;if(l){var s=D(l,n);if(s){if(s===y)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===h)throw i=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=m;var c=p(e,r,n);if("normal"===c.type){if(i=n.done?v:g,c.arg===y)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(i=v,n.method="throw",n.arg=c.arg)}}}function D(e,r){var n=r.method,i=e.iterator[n];if(i===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,D(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var o=p(i,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,y;var a=o.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function O(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function P(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(O,this),this.reset(!0)}function $(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function r(){for(;++i<e.length;)if(n.call(e,i))return r.value=e[i],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(l(e)+" is not iterable")}return w.prototype=x,i(k,"constructor",{value:x,configurable:!0}),i(x,"constructor",{value:w,configurable:!0}),w.displayName=d(x,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,x):(t.__proto__=x,d(t,u,"GeneratorFunction")),t.prototype=Object.create(k),t},e.awrap=function(t){return{__await:t}},S(E.prototype),d(E.prototype,c,(function(){return this})),e.AsyncIterator=E,e.async=function(t,r,n,i,o){void 0===o&&(o=Promise);var a=new E(f(t,r,n,i),o);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},S(k),d(k,u,"Generator"),d(k,a,(function(){return this})),d(k,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=$,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(P),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function i(n,i){return l.type="throw",l.arg=e,r.next=n,i&&(r.method="next",r.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],l=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var s=n.call(a,"catchLoc"),c=n.call(a,"finallyLoc");if(s&&c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,y):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),P(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var i=n.arg;P(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:$(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),y}},e}function c(t,e){return h(t)||p(t,e)||d(t,e)||u()}function u(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(t,e){if(t){if("string"==typeof t)return f(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?f(t,e):void 0}}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function p(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,l=[],s=!0,c=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=o.call(r)).done)&&(l.push(n.value),l.length!==e);s=!0);}catch(t){c=!0,i=t}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw i}}return l}}function h(t){if(Array.isArray(t))return t}function g(t,e,r,n,i,o,a){try{var l=t[o](a),s=l.value}catch(t){return void r(t)}l.done?e(s):Promise.resolve(s).then(n,i)}function m(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var o=t.apply(e,r);function a(t){g(o,n,i,a,l,"next",t)}function l(t){g(o,n,i,a,l,"throw",t)}a(void 0)}))}}var v={name:"menuPreviewDialog",props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:"选择模型"},width:{type:String,default:"600px"},isshow:Boolean,confirm:Function,formDataDialog:{type:Object,default:function(){return{}}},isModify:{type:Boolean,default:!0}},components:{addAndModifyCollective:a["default"]},computed:{visible:{get:function(){return this.isshow},set:function(t){this.$emit("update:isshow",t)}}},data:function(){return{isLoading:!1,modelIndex:0,tableData:[{address:1},{address:1},{address:1},{address:1},{address:1},{address:1},{address:1},{address:1},{address:1}],nutritionRuleList:[],collectiveDialogVisible:!1,selectListIds:[],templateSelection:"",templateRadio:"",collectiveDialogTitle:"",collectiveDialogType:"",pageSize:5,totalCount:0,totalPageSize:0,currentPage:1}},created:function(){this.getNutritionRuleList(),this.getCollectiveList()},mounted:function(){},methods:{clickConfirmHandle:function(){this.visible=!1},clickCancleHandle:function(){this.visible=!1},handleClose:function(t){this.visible=!1},clickModel:function(t,e){this.modelIndex=e},getNutritionRuleList:function(){var t=this;return m(s().mark((function e(){var r,n,i,a;return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(o["Z"])(t.$apis.apiBackgroundHealthyNutritionRuleListPost({type:"nutrition_guide"}));case 2:if(r=e.sent,n=c(r,2),i=n[0],a=n[1],!i){e.next=9;break}return t.$message.error(i.message),e.abrupt("return");case 9:0===a.code?t.nutritionRuleList=a.data.results:t.$message.error(a.msg);case 10:case"end":return e.stop()}}),e)})))()},getCollectiveList:function(){var t=this;return m(s().mark((function e(){var r,n,i,a;return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(o["Z"])(t.$apis.apiBackgroundFoodCollectiveListPost({page:t.currentPage,page_size:t.pageSize}));case 3:if(r=e.sent,n=c(r,2),i=n[0],a=n[1],t.isLoading=!1,!i){e.next=11;break}return t.$message.error(i.message),e.abrupt("return");case 11:0===a.code?(t.totalCount=a.data.count,t.tableData=a.data.results):t.$message.error(a.msg);case 12:case"end":return e.stop()}}),e)})))()},getModifyNutritionGuidance:function(){var t=this;return m(s().mark((function e(){var r,n,i,a,l;return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r={id:t.formDataDialog.menuId,operate:1,menu_type:t.formDataDialog.menuType,nutrition_rule_id:t.nutritionRuleList[t.modelIndex].id,collective_id:t.templateRadio},t.isLoading=!0,e.next=4,Object(o["Z"])(t.$apis.apiBackgroundFoodMenuModifyNutritionGuidancePost(r));case 4:if(n=e.sent,i=c(n,2),a=i[0],l=i[1],t.isLoading=!1,!a){e.next=12;break}return t.$message.error(a.message),e.abrupt("return");case 12:0===l.code?(t.$message.success("开启成功"),t.$emit("clickSelect",{nutritionGuidance:!0})):t.$message.error(l.msg);case 13:case"end":return e.stop()}}),e)})))()},singleElection:function(t){this.templateRadio=t},handleSizeChange:function(t){this.pageSize=t,this.getCollectiveList()},handleCurrentChange:function(t){this.currentPage=t,this.getCollectiveList()},addAndEditCollective:function(t,e){this.collectiveDialogType=t,this.collectiveDialogVisible=!0,"add"===t&&(this.collectiveDialogTitle="新增集体")},clickSelect:function(){return this.nutritionRuleList.length?this.templateRadio?void(this.isModify?this.getModifyNutritionGuidance():this.$emit("clickSelect",{operate:1,nutrition_rule_id:this.nutritionRuleList[this.modelIndex].id,collective_id:this.templateRadio})):this.$message.error("请选择集体数据"):this.$message.error("该组织没有营养指导模型，请联系管理员")}}},y=v,b=(r("3be8"),r("aa12"),r("2877")),w=Object(b["a"])(y,n,i,!1,null,"81ed2d54",null);e["default"]=w.exports}}]);