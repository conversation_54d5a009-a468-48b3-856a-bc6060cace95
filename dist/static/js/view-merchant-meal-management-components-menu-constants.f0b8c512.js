(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-components-menu-constants"],{2116:function(e,t,o){"use strict";o.r(t),o.d(t,"MEALTIME_SETTING",(function(){return r})),o.d(t,"BAR_OPTION_SETTING",(function(){return i}));var r={tooltip:{trigger:"item",borderColor:"#FCA155",textStyle:{color:"#000",fontWeight:500},backgroundColor:"#fff",extraCssText:"box-shadow: 0 0 10px rgba(0,0,0,0.2);font-weight: 540;",formatter:function(e){var t=e.marker,o=e.percent;return t+e.name+"&nbsp;&nbsp;&nbsp;"+o+"%"}},title:{text:0,subtext:"订单比数",top:"25%",left:"19%",textAlign:"middle",textStyle:{color:"#000",fontSize:40,align:"center"},subtextStyle:{color:"#999",fontSize:16,align:"center"}},legend:{bottom:"5%",right:"right",y:"center",icon:"circle",orient:"vertical",padding:[0,20,0,0]},series:[{center:["20%","50%"],type:"pie",radius:["70%","60%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},hoverAnimation:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!1,fontSize:"20",fontWeight:"bold"}},labelLine:{show:!1},data:[]}],color:["#07DED0","#FE985F","#e98397","#F97C95","#58AFFE","#F8C345"]},i={tooltip:{},dataset:{dimensions:["product","统计摄入量","建议摄入量"],source:[{product:"兆焦耳(mj/kg)","统计摄入量":43.3,"建议摄入量":85.8},{product:"Milk Tea","统计摄入量":43.3,"建议摄入量":85.8},{product:"Cheese Cocoa","统计摄入量":43.3,"建议摄入量":85.8},{product:"Walnut Brownie","统计摄入量":43.3,"建议摄入量":85.8}]},legend:{right:"0"},grid:{left:0,right:0},xAxis:{type:"category"},dataZoom:[{end:30}],yAxis:{axisLine:{show:!0},axisTick:{show:!1},axisLabel:{show:!1}},series:[{type:"bar",barWidth:20},{type:"bar",barWidth:20}],color:["#579def","#5dbf6e"]}}}]);