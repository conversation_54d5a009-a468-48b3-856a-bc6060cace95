(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-consumption-rules-coupon-admin-AddCoupon","view-merchant-consumption-rules-constants"],{"07a7":function(e,t,o){},"08e7":function(e,t,o){e.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}},"68e3":function(e,t,o){"use strict";o.r(t);var r=function(){var e=this,t=e._self._c;return t("dialog-message",{attrs:{show:e.visible,title:e.title,loading:e.isLoading,customClass:"ps-dialog",width:e.width},on:{"update:show":function(t){e.visible=t},"update:loading":function(t){e.isLoading=t},close:e.handleClose}},[t("el-form",{ref:"couponDialogForm",staticClass:"jiaofei-form",attrs:{model:e.couponDialogForm,"status-icon":"","label-width":"80px",inline:""},nativeOn:{submit:function(e){e.preventDefault()}}},[t("el-form-item",{attrs:{label:"部门"}},[t("user-department-select",{staticClass:"w-180 ps-input",attrs:{clearable:!0,multiple:!0,"check-strictly":!0,isLazy:!1,placeholder:"请选择部门","append-to-body":!0},on:{change:e.searchHandle},model:{value:e.couponDialogForm.department,callback:function(t){e.$set(e.couponDialogForm,"department",t)},expression:"couponDialogForm.department"}})],1),t("el-form-item",{attrs:{label:"分组"}},[t("user-group-select",{staticClass:"search-item-w ps-input w-180",attrs:{multiple:!0,"collapse-tags":!0,placeholder:"请下拉选择"},on:{change:e.searchHandle},model:{value:e.couponDialogForm.groupIds,callback:function(t){e.$set(e.couponDialogForm,"groupIds",t)},expression:"couponDialogForm.groupIds"}})],1),t("el-form-item",{attrs:{label:"姓名"}},[t("el-input",{staticClass:"ps-input w-180",attrs:{placeholder:"请输入"},on:{input:e.searchHandle},model:{value:e.couponDialogForm.name,callback:function(t){e.$set(e.couponDialogForm,"name",t)},expression:"couponDialogForm.name"}})],1),t("el-form-item",{attrs:{label:"人员编号"}},[t("el-input",{staticClass:"ps-input w-180",attrs:{placeholder:"请输入"},on:{input:e.searchHandle},model:{value:e.couponDialogForm.personNo,callback:function(t){e.$set(e.couponDialogForm,"personNo",t)},expression:"couponDialogForm.personNo"}})],1),t("el-form-item",{attrs:{label:"性别",prop:"gender"}},[t("el-select",{staticClass:"ps-select w-180",attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select"},on:{change:e.searchHandle},model:{value:e.couponDialogForm.gender,callback:function(t){e.$set(e.couponDialogForm,"gender",t)},expression:"couponDialogForm.gender"}},e._l(e.genderList,(function(e){return t("el-option",{key:e.value,attrs:{label:e.gender,value:e.value}})})),1)],1),t("div",{staticClass:"person-table"},[t("el-table",{ref:"userListRef",attrs:{data:e.userList,"max-height":"350","row-key":e.getRowKey,"header-row-class-name":"ps-table-header-row"},on:{select:e.handleSelection,"select-all":e.handleAllSelection}},[t("el-table-column",{attrs:{type:"selection","reserve-selection":!0,width:"50",align:"center","class-name":"ps-checkbox"}}),t("el-table-column",{attrs:{prop:"card_department_group_alias",label:"部门",align:"center"}}),t("el-table-column",{attrs:{prop:"card_user_group_alias",label:"分组",align:"center"}}),t("el-table-column",{attrs:{prop:"name",label:"姓名",align:"center"}}),t("el-table-column",{attrs:{prop:"gender_alias",label:"性别",align:"center"}}),t("el-table-column",{attrs:{prop:"person_no",label:"人员编号",align:"center"}}),t("el-table-column",{attrs:{prop:"card_no",label:"卡号",align:"center"}})],1)],1),t("div",{staticClass:"block ps-pagination person-table-bottom"},[t("div",{staticStyle:{width:"100px"}},[e._v("已选人数："+e._s(e.selectList.length))]),t("el-pagination",{staticClass:"ps-text",attrs:{"current-page":e.currentPage,"page-sizes":[5,10,20,30,40],"page-size":e.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:e.totalCount,"pager-count":5,background:"","popper-class":"ps-popper-select"},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),t("template",{slot:"tool"},[t("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:e.clickCancleHandle}},[e._v(" 取消 ")]),t("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickConfirmHandle}},[e._v(" 确定 ")])],1)])],2)},n=[],i=o("faa6"),a=o("390a"),s=o("ed08"),c=o("bbd5");function l(e){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},l(e)}function u(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */u=function(){return t};var e,t={},o=Object.prototype,r=o.hasOwnProperty,n=Object.defineProperty||function(e,t,o){e[t]=o.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function p(e,t,o){return Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{p({},"")}catch(e){p=function(e,t,o){return e[t]=o}}function m(e,t,o,r){var i=t&&t.prototype instanceof b?t:b,a=Object.create(i.prototype),s=new P(r||[]);return n(a,"_invoke",{value:k(e,o,s)}),a}function d(e,t,o){try{return{type:"normal",arg:e.call(t,o)}}catch(e){return{type:"throw",arg:e}}}t.wrap=m;var f="suspendedStart",h="suspendedYield",g="executing",v="completed",y={};function b(){}function F(){}function _(){}var w={};p(w,a,(function(){return this}));var x=Object.getPrototypeOf,L=x&&x(x(N([])));L&&L!==o&&r.call(L,a)&&(w=L);var T=_.prototype=b.prototype=Object.create(w);function C(e){["next","throw","return"].forEach((function(t){p(e,t,(function(e){return this._invoke(t,e)}))}))}function O(e,t){function o(n,i,a,s){var c=d(e[n],e,i);if("throw"!==c.type){var u=c.arg,p=u.value;return p&&"object"==l(p)&&r.call(p,"__await")?t.resolve(p.__await).then((function(e){o("next",e,a,s)}),(function(e){o("throw",e,a,s)})):t.resolve(p).then((function(e){u.value=e,a(u)}),(function(e){return o("throw",e,a,s)}))}s(c.arg)}var i;n(this,"_invoke",{value:function(e,r){function n(){return new t((function(t,n){o(e,r,t,n)}))}return i=i?i.then(n,n):n()}})}function k(t,o,r){var n=f;return function(i,a){if(n===g)throw Error("Generator is already running");if(n===v){if("throw"===i)throw a;return{value:e,done:!0}}for(r.method=i,r.arg=a;;){var s=r.delegate;if(s){var c=S(s,r);if(c){if(c===y)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(n===f)throw n=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n=g;var l=d(t,o,r);if("normal"===l.type){if(n=r.done?v:h,l.arg===y)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n=v,r.method="throw",r.arg=l.arg)}}}function S(t,o){var r=o.method,n=t.iterator[r];if(n===e)return o.delegate=null,"throw"===r&&t.iterator.return&&(o.method="return",o.arg=e,S(t,o),"throw"===o.method)||"return"!==r&&(o.method="throw",o.arg=new TypeError("The iterator does not provide a '"+r+"' method")),y;var i=d(n,t.iterator,o.arg);if("throw"===i.type)return o.method="throw",o.arg=i.arg,o.delegate=null,y;var a=i.arg;return a?a.done?(o[t.resultName]=a.value,o.next=t.nextLoc,"return"!==o.method&&(o.method="next",o.arg=e),o.delegate=null,y):a:(o.method="throw",o.arg=new TypeError("iterator result is not an object"),o.delegate=null,y)}function D(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function P(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(D,this),this.reset(!0)}function N(t){if(t||""===t){var o=t[a];if(o)return o.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function o(){for(;++n<t.length;)if(r.call(t,n))return o.value=t[n],o.done=!1,o;return o.value=e,o.done=!0,o};return i.next=i}}throw new TypeError(l(t)+" is not iterable")}return F.prototype=_,n(T,"constructor",{value:_,configurable:!0}),n(_,"constructor",{value:F,configurable:!0}),F.displayName=p(_,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===F||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,p(e,c,"GeneratorFunction")),e.prototype=Object.create(T),e},t.awrap=function(e){return{__await:e}},C(O.prototype),p(O.prototype,s,(function(){return this})),t.AsyncIterator=O,t.async=function(e,o,r,n,i){void 0===i&&(i=Promise);var a=new O(m(e,o,r,n),i);return t.isGeneratorFunction(o)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},C(T),p(T,c,"Generator"),p(T,a,(function(){return this})),p(T,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),o=[];for(var r in t)o.push(r);return o.reverse(),function e(){for(;o.length;){var r=o.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=N,P.prototype={constructor:P,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(E),!t)for(var o in this)"t"===o.charAt(0)&&r.call(this,o)&&!isNaN(+o.slice(1))&&(this[o]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var o=this;function n(r,n){return s.type="throw",s.arg=t,o.next=r,n&&(o.method="next",o.arg=e),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),l=r.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(e,t){for(var o=this.tryEntries.length-1;o>=0;--o){var n=this.tryEntries[o];if(n.tryLoc<=this.prev&&r.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var o=this.tryEntries[t];if(o.finallyLoc===e)return this.complete(o.completion,o.afterLoc),E(o),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var o=this.tryEntries[t];if(o.tryLoc===e){var r=o.completion;if("throw"===r.type){var n=r.arg;E(o)}return n}}throw Error("illegal catch attempt")},delegateYield:function(t,o,r){return this.delegate={iterator:N(t),resultName:o,nextLoc:r},"next"===this.method&&(this.arg=e),y}},t}function p(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,r)}return o}function m(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?p(Object(o),!0).forEach((function(t){d(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):p(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}function d(e,t,o){return(t=f(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}function f(e){var t=h(e,"string");return"symbol"==l(t)?t:t+""}function h(e,t){if("object"!=l(e)||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=l(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function g(e,t,o,r,n,i,a){try{var s=e[i](a),c=s.value}catch(e){return void o(e)}s.done?t(c):Promise.resolve(c).then(r,n)}function v(e){return function(){var t=this,o=arguments;return new Promise((function(r,n){var i=e.apply(t,o);function a(e){g(i,r,n,a,s,"next",e)}function s(e){g(i,r,n,a,s,"throw",e)}a(void 0)}))}}var y={name:"JiaoFeiDialog",components:{UserDepartmentSelect:i["a"],UserGroupSelect:a["a"]},props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:"选择人员"},width:{type:String,default:"900px"},personList:{type:Array,default:function(){return[]}},selectInfo:{type:Object,default:function(){return{}}},isshow:Boolean,confirm:Function},data:function(){return{isLoading:!1,couponDialogForm:{department:[],groupIds:[],name:"",personNo:"",gender:""},genderList:[{gender:"男",value:"MAN"},{gender:"女",value:"WOMEN"},{gender:"其他",value:"OTHER"}],userList:[],pageSize:10,totalCount:0,currentPage:1,selectList:[],organizationId:this.$store.getters.organization,categoryList:[],foodList:[]}},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},watch:{visible:function(){var e=this;this.visible&&(this.currentPage=1,this.totalCount=0,this.isshow&&(this.$nextTick((function(){e.$refs.userListRef.clearSelection()})),this.couponDialogForm.department=[],this.couponDialogForm.groupIds=[],this.couponDialogForm.name="",this.couponDialogForm.personNo="",this.couponDialogForm.gender="",this.selectList=Object(c["a"])(this.personList),this.getUserList()))}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){},searchHandle:Object(s["d"])((function(){this.currentPage=1,this.getUserList()}),300),clickConfirmHandle:function(){var e=this;this.$refs.couponDialogForm.validate((function(t){t&&(e.$emit("confirmPerson",e.selectList),e.visible=!1)}))},clickCancleHandle:function(){this.visible=!1},handleClose:function(e){this.isLoading=!1,this.visible=!1,this.$refs.couponDialogForm.resetFields()},getUserList:function(){var e=this;return v(u().mark((function t(){var o,r,n,i;return u().wrap((function(t){while(1)switch(t.prev=t.next){case 0:for(n in e.isLoading=!0,o={card_department_group_ids:e.couponDialogForm.department,card_user_group_ids:e.couponDialogForm.groupIds,person_name:e.couponDialogForm.name,person_no:e.couponDialogForm.personNo,gender:e.couponDialogForm.gender},r={},o)o[n]&&(r[n]=o[n]);return t.next=6,e.$apis.apiCardServiceCardUserListPost(m(m({},r),{},{org_ids:[e.organizationId],page:e.currentPage,page_size:e.pageSize}));case 6:i=t.sent,e.isLoading=!1,0===i.code?(e.userList=i.data.results,e.totalCount=i.data.count,e.userList.map((function(t){t.card_user_group_alias=t.card_user_group_alias.join("，"),e.personList.map((function(o){t.id===o.id&&e.$refs.userListRef.toggleRowSelection(t,!0)}))}))):e.$message.error(i.msg);case 9:case"end":return t.stop()}}),t)})))()},handleSizeChange:function(e){this.pageSize=e,this.getUserList()},handleCurrentChange:function(e){this.currentPage=e,this.getUserList()},handleSelection:function(e,t){var o=this.selectList.findIndex((function(e){return e.id===t.id}));-1===o?this.selectList.push(t):this.selectList.splice(o,1)},handleAllSelection:function(e){var t=this,o=Object(c["a"])(e),r=!0;this.userList.map((function(e){var t=o.findIndex((function(t){return t.id===e.id}));-1===t&&(r=!1)})),r?this.userList.map((function(e){var o=t.selectList.findIndex((function(t){return t.id===e.id}));-1===o&&t.selectList.push(e)})):this.userList.map((function(e){var o=t.selectList.findIndex((function(t){return t.id===e.id}));-1!==o&&t.selectList.splice(o,1)}))},getRowKey:function(e){return e.id}}},b=y,F=(o("9e41"),o("2877")),_=Object(F["a"])(b,r,n,!1,null,"47bc3d17",null);t["default"]=_.exports},"837a":function(e,t,o){"use strict";o.r(t);var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"MerchantAddCoupon container-wrapper"},[t("div",{staticClass:"table-wrapper"},[t("div",{staticClass:"table-header",staticStyle:{display:"flex","justify-content":"space-between"}},[t("div",{staticClass:"table-title"},[e._v(e._s("add"===e.type?"新建":"编辑")+"优惠券")])]),t("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"couponFormRef",staticStyle:{padding:"0 25px"},attrs:{model:e.couponForm,rules:e.couponFormRule,"label-width":"130px"}},[t("el-form-item",{attrs:{label:"优惠券名称",prop:"couponName"}},[t("el-input",{staticClass:"ps-input w-300",attrs:{disabled:"edit"===e.type,max:"15"},model:{value:e.couponForm.couponName,callback:function(t){e.$set(e.couponForm,"couponName",t)},expression:"couponForm.couponName"}})],1),t("el-form-item",{attrs:{label:"券类型",prop:"couponType"}},[t("el-select",{attrs:{"popper-class":"ps-popper-select w-300",disabled:"edit"===e.type},model:{value:e.couponForm.couponType,callback:function(t){e.$set(e.couponForm,"couponType",t)},expression:"couponForm.couponType"}},[t("el-option",{attrs:{label:"满减券",value:"FULL_DISCOUNT"}}),t("el-option",{attrs:{label:"立减券",value:"INSTANT_DISCOUNT"}}),t("el-option",{attrs:{label:"折扣券",value:"DISCOUNT"}}),t("el-option",{attrs:{label:"兑换券",value:"EXCHANGE"}})],1)],1),t("el-form-item",{attrs:{label:"适用组织",prop:"userOrg"}},[t("organization-select",{staticClass:"search-item-w ps-input w-300",attrs:{placeholder:"请选择所属组织",isLazy:!1,multiple:!0,"check-strictly":!0,"append-to-body":!0,disabled:"edit"===e.type},model:{value:e.couponForm.userOrg,callback:function(t){e.$set(e.couponForm,"userOrg",t)},expression:"couponForm.userOrg"}})],1),"FULL_DISCOUNT"===e.couponForm.couponType?t("el-form-item",{key:"FULL_DISCOUNT",attrs:{label:"使用条件"}},[t("span",[e._v("订单金额满")]),t("el-form-item",{key:"maxPrice",staticClass:"form-content-inline",attrs:{prop:"maxPrice"}},[t("el-input",{staticClass:"margin-input w-180 ps-input",attrs:{disabled:"edit"===e.type},model:{value:e.couponForm.maxPrice,callback:function(t){e.$set(e.couponForm,"maxPrice",t)},expression:"couponForm.maxPrice"}})],1),t("span",[e._v("元减去")]),t("el-form-item",{key:"minPrice",staticClass:"form-content-inline",attrs:{prop:"minPrice"}},[t("el-input",{staticClass:"margin-input w-180 ps-input",attrs:{disabled:"edit"===e.type},model:{value:e.couponForm.minPrice,callback:function(t){e.$set(e.couponForm,"minPrice",t)},expression:"couponForm.minPrice"}})],1),t("span",[e._v("元")])],1):e._e(),"INSTANT_DISCOUNT"===e.couponForm.couponType?t("el-form-item",{key:"INSTANT_DISCOUNT",attrs:{label:"使用条件"}},[t("span",[e._v("优惠金额")]),t("el-form-item",{key:"preferentialPrice",staticClass:"form-content-inline",attrs:{prop:"preferentialPrice"}},[t("el-input",{staticClass:"margin-input w-180 ps-input",attrs:{disabled:"edit"===e.type},model:{value:e.couponForm.preferentialPrice,callback:function(t){e.$set(e.couponForm,"preferentialPrice",t)},expression:"couponForm.preferentialPrice"}})],1),t("span",[e._v("元")])],1):e._e(),"DISCOUNT"===e.couponForm.couponType?t("el-form-item",{key:"DISCOUNT",attrs:{label:"使用条件"}},[t("span",[e._v("优惠券折扣")]),t("el-form-item",{key:"preferentialDiscount",staticClass:"form-content-inline",attrs:{prop:"preferentialDiscount"}},[t("el-input",{staticClass:"margin-input w-180 ps-input",attrs:{disabled:"edit"===e.type},model:{value:e.couponForm.preferentialDiscount,callback:function(t){e.$set(e.couponForm,"preferentialDiscount",t)},expression:"couponForm.preferentialDiscount"}}),t("span",[e._v("折")])],1),t("el-form-item",{staticClass:"m-t-10"},[t("span",[e._v("是否设置每人最大折扣金额")]),t("el-switch",{attrs:{disabled:"edit"===e.type,"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:e.couponForm.isSetMaxDiscount,callback:function(t){e.$set(e.couponForm,"isSetMaxDiscount",t)},expression:"couponForm.isSetMaxDiscount"}}),e.couponForm.isSetMaxDiscount?t("el-form-item",{key:"userMaxDiscount",staticClass:"form-content-inline",attrs:{prop:"userMaxDiscount"}},[t("el-input",{staticClass:"margin-input w-180 ps-input",attrs:{disabled:"edit"===e.type},model:{value:e.couponForm.userMaxDiscount,callback:function(t){e.$set(e.couponForm,"userMaxDiscount",t)},expression:"couponForm.userMaxDiscount"}})],1):e._e(),t("span",[e._v("元")])],1),e.couponForm.isSetMaxDiscount?t("el-form-item",{staticClass:"m-t-10"},[t("span",[e._v("每人最大折扣金额上限：")]),t("el-radio-group",{staticClass:"ps-radio",attrs:{disabled:"edit"===e.type},model:{value:e.couponForm.maxDiscountType,callback:function(t){e.$set(e.couponForm,"maxDiscountType",t)},expression:"couponForm.maxDiscountType"}},[t("el-radio",{attrs:{label:"all"}},[e._v("整单原价扣款")]),t("el-radio",{attrs:{label:"beyond"}},[e._v("超出部分原价扣款")])],1)],1):e._e()],1):e._e(),"EXCHANGE"===e.couponForm.couponType?t("el-form-item",[t("el-form-item",[t("span",[e._v("优惠券是否需要满足订单金额使用")]),t("el-switch",{attrs:{disabled:"edit"===e.type,"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:e.couponForm.isOrderFee,callback:function(t){e.$set(e.couponForm,"isOrderFee",t)},expression:"couponForm.isOrderFee"}}),t("span",{staticClass:"margin-l-20"},[e._v("订单金额")]),t("el-form-item",{key:"orderFee",staticClass:"form-content-inline",attrs:{prop:"orderFee"}},[t("el-input",{staticClass:"margin-input w-180 ps-input",attrs:{disabled:"edit"===e.type},model:{value:e.couponForm.orderFee,callback:function(t){e.$set(e.couponForm,"orderFee",t)},expression:"couponForm.orderFee"}})],1),t("span",[e._v("元")])],1),t("el-form-item",{key:"foodMaxCount",attrs:{prop:"foodMaxCount"}},[t("span",[e._v("兑换券兑换菜品/套餐上限数量")]),t("el-input",{staticClass:"margin-input w-180 ps-input",attrs:{disabled:"edit"===e.type},model:{value:e.couponForm.foodMaxCount,callback:function(t){e.$set(e.couponForm,"foodMaxCount",t)},expression:"couponForm.foodMaxCount"}})],1)],1):e._e(),"EXCHANGE"===e.couponForm.couponType?t("el-form-item",{key:"EXCHANGE"},[t("el-button",{staticClass:"ps-origin-btn",attrs:{type:"primary",size:"mini",disabled:"edit"===e.type},on:{click:function(t){return e.openChooseDialog("food")}}},[e._v("去选择")]),t("el-table",{ref:"tableDataRef",staticStyle:{width:"500px"},attrs:{data:e.couponForm.foodList,"header-row-class-name":"ps-table-header-row"}},[t("el-table-column",{attrs:{prop:"name",label:"菜品名称",align:"center"}}),t("el-table-column",{attrs:{prop:"xx",label:"操作",width:"100px",align:"center"},scopedSlots:e._u([{key:"default",fn:function(o){return[t("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small",disabled:"edit"===e.type},on:{click:function(t){return e.deleteFoodHandle(o.row.index)}}},[e._v("移除")])]}}],null,!1,2781305224)}),t("el-table-column",{attrs:{label:"",prop:"",width:"80px",align:"center"}},[[t("img",{staticClass:"drop-img",attrs:{src:o("cd5c"),alt:""}})]],2)],1)],1):e._e(),t("el-form-item",{attrs:{label:"状态",prop:"couponStatus"}},[t("el-radio-group",{staticClass:"ps-radio",model:{value:e.couponForm.couponStatus,callback:function(t){e.$set(e.couponForm,"couponStatus",t)},expression:"couponForm.couponStatus"}},[t("el-radio",{attrs:{label:!0}},[e._v("启用")]),t("el-radio",{attrs:{label:!1}},[e._v("禁用")])],1)],1),t("el-form-item",{attrs:{label:"适用餐段",prop:"userMeal"}},[t("el-checkbox-group",{attrs:{disabled:"edit"===e.type},model:{value:e.couponForm.userMeal,callback:function(t){e.$set(e.couponForm,"userMeal",t)},expression:"couponForm.userMeal"}},e._l(e.mealList,(function(o){return t("el-checkbox",{key:o.value,staticClass:"ps-checkbox",attrs:{label:o.value}},[e._v(e._s(o.name))])})),1)],1),t("el-form-item",{attrs:{label:"优惠券领取方式",prop:"receiveType"}},[t("el-radio-group",{staticClass:"ps-radio",attrs:{disabled:"edit"===e.type},model:{value:e.couponForm.receiveType,callback:function(t){e.$set(e.couponForm,"receiveType",t)},expression:"couponForm.receiveType"}},[t("el-radio",{attrs:{label:"MANUAL"}},[e._v("手动领取")]),t("el-radio",{attrs:{label:"AUTOMATIC"}},[e._v("自动领取")])],1)],1),"MANUAL"===e.couponForm.receiveType?t("el-form-item",{key:"receiveTime",attrs:{label:"领取时间",prop:"edit"===e.type?"":"receiveTime"}},[t("el-date-picker",{staticClass:"ps-poper-picker",attrs:{type:"daterange","unlink-panels":"","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",disabled:"edit"===e.type},model:{value:e.couponForm.receiveTime,callback:function(t){e.$set(e.couponForm,"receiveTime",t)},expression:"couponForm.receiveTime"}})],1):e._e(),t("el-form-item",{key:"sendCount",staticClass:"form-content-inline",attrs:{label:"MANUAL"===e.couponForm.receiveType?"发放数目":"每人发放数目",prop:"sendCount"}},[t("el-input",{staticClass:"ps-input",attrs:{disabled:"edit"===e.type&&"AUTOMATIC"==e.couponForm.receiveType},model:{value:e.couponForm.sendCount,callback:function(t){e.$set(e.couponForm,"sendCount",t)},expression:"couponForm.sendCount"}})],1),"MANUAL"===e.couponForm.receiveType?t("el-form-item",{key:"receiveCount",staticClass:"form-content-inline",attrs:{label:"每人最多可领取",prop:"receiveCount"}},[t("el-input",{staticClass:"ps-input",attrs:{disabled:"edit"===e.type&&"AUTOMATIC"==e.couponForm.receiveType},model:{value:e.couponForm.receiveCount,callback:function(t){e.$set(e.couponForm,"receiveCount",t)},expression:"couponForm.receiveCount"}})],1):e._e(),t("el-form-item",{attrs:{label:"优惠券是否限制使用时间",prop:"isLimitTime","label-width":"180px"}},[t("el-switch",{attrs:{disabled:"edit"===e.type,"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:e.couponForm.isLimitTime,callback:function(t){e.$set(e.couponForm,"isLimitTime",t)},expression:"couponForm.isLimitTime"}}),t("el-form-item",{staticClass:"form-content-inline",attrs:{label:"优惠券有效期",prop:"edit"===e.type?"":"limitTime"}},[t("el-date-picker",{staticClass:"ps-poper-picker",attrs:{type:"daterange","unlink-panels":"","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:"",disabled:"edit"===e.type},model:{value:e.couponForm.limitTime,callback:function(t){e.$set(e.couponForm,"limitTime",t)},expression:"couponForm.limitTime"}})],1)],1),t("el-form-item",{attrs:{label:"领取条件"}},[t("el-radio-group",{staticClass:"ps-radio",model:{value:e.couponForm.receiveFactor,callback:function(t){e.$set(e.couponForm,"receiveFactor",t)},expression:"couponForm.receiveFactor"}},[t("el-radio",{attrs:{label:"ALL"}},[e._v("所有人")]),t("el-radio",{attrs:{label:"GROUP"}},[e._v("指定分组")]),t("el-radio",{attrs:{label:"PERSON"}},[e._v("指定人员")])],1),"GROUP"===e.couponForm.receiveFactor?t("el-form-item",{attrs:{prop:"receiveGroup"}},[t("user-group-select",{staticClass:"search-item-w ps-input w-250",attrs:{multiple:!0,placeholder:"请下拉选择"},model:{value:e.couponForm.receiveGroup,callback:function(t){e.$set(e.couponForm,"receiveGroup",t)},expression:"couponForm.receiveGroup"}})],1):e._e()],1),"PERSON"===e.couponForm.receiveFactor?t("el-form-item",{attrs:{label:"",prop:"personList"}},[t("el-button",{staticClass:"ps-origin-btn",attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.openChooseDialog("choosePerson")}}},[e._v("去选择")]),t("span",{staticClass:"margin-l-20"},[e._v("已选人数："+e._s(e.personList.length)+"人")]),t("div",[t("el-table",{attrs:{data:e.personList,"max-height":"400","header-row-class-name":"ps-table-header-row"}},[t("el-table-column",{attrs:{prop:"organization_alias",label:"所属组织",align:"center"}}),t("el-table-column",{attrs:{prop:"department_group_name",label:"部门",align:"center"}}),t("el-table-column",{attrs:{prop:"card_user_group_alias",label:"分组",align:"center"}}),t("el-table-column",{attrs:{prop:"person_no",label:"人员编号",align:"center"}}),t("el-table-column",{attrs:{prop:"name",label:"姓名",align:"center"}}),t("el-table-column",{attrs:{width:"180",label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(o){return[t("el-button",{staticClass:"ps-warn",attrs:{type:"text",size:"small"},on:{click:function(t){return e.delOperation(o.$index)}}},[e._v(" 移除 ")])]}}],null,!1,4079810830)})],1)],1)],1):e._e(),t("el-form-item",{attrs:{label:"备注"}},[t("el-input",{attrs:{maxlength:"200",type:"textarea",rows:5,placeholder:"请输入内容",disabled:"edit"===e.type},model:{value:e.couponForm.remark,callback:function(t){e.$set(e.couponForm,"remark",t)},expression:"couponForm.remark"}})],1),t("el-form-item",[t("el-button",{staticClass:"ps-origin-btn w-150",attrs:{size:"small",type:"primary"},on:{click:e.checkForm}},[e._v("保存")])],1)],1)],1),t("person-dialog",{attrs:{isshow:e.dialogPersonVisible,"choose-list":e.chooseList},on:{"update:isshow":function(t){e.dialogPersonVisible=t},confirmPerson:e.confirmPerson}}),t("food-dialog",{attrs:{isshow:e.dialogFoodVisible,"choose-list":e.chooseList},on:{"update:isshow":function(t){e.dialogFoodVisible=t},confirmPerson:e.confirmFood}})],1)},n=[],i=o("cbfb"),a=o("390a"),s=o("68e3"),c=o("4382"),l=o("f6ce"),u=o("ed08"),p=o("aa47");function m(e){return m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},m(e)}function d(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */d=function(){return t};var e,t={},o=Object.prototype,r=o.hasOwnProperty,n=Object.defineProperty||function(e,t,o){e[t]=o.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function l(e,t,o){return Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,o){return e[t]=o}}function u(e,t,o,r){var i=t&&t.prototype instanceof b?t:b,a=Object.create(i.prototype),s=new P(r||[]);return n(a,"_invoke",{value:k(e,o,s)}),a}function p(e,t,o){try{return{type:"normal",arg:e.call(t,o)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var f="suspendedStart",h="suspendedYield",g="executing",v="completed",y={};function b(){}function F(){}function _(){}var w={};l(w,a,(function(){return this}));var x=Object.getPrototypeOf,L=x&&x(x(N([])));L&&L!==o&&r.call(L,a)&&(w=L);var T=_.prototype=b.prototype=Object.create(w);function C(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function O(e,t){function o(n,i,a,s){var c=p(e[n],e,i);if("throw"!==c.type){var l=c.arg,u=l.value;return u&&"object"==m(u)&&r.call(u,"__await")?t.resolve(u.__await).then((function(e){o("next",e,a,s)}),(function(e){o("throw",e,a,s)})):t.resolve(u).then((function(e){l.value=e,a(l)}),(function(e){return o("throw",e,a,s)}))}s(c.arg)}var i;n(this,"_invoke",{value:function(e,r){function n(){return new t((function(t,n){o(e,r,t,n)}))}return i=i?i.then(n,n):n()}})}function k(t,o,r){var n=f;return function(i,a){if(n===g)throw Error("Generator is already running");if(n===v){if("throw"===i)throw a;return{value:e,done:!0}}for(r.method=i,r.arg=a;;){var s=r.delegate;if(s){var c=S(s,r);if(c){if(c===y)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(n===f)throw n=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n=g;var l=p(t,o,r);if("normal"===l.type){if(n=r.done?v:h,l.arg===y)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n=v,r.method="throw",r.arg=l.arg)}}}function S(t,o){var r=o.method,n=t.iterator[r];if(n===e)return o.delegate=null,"throw"===r&&t.iterator.return&&(o.method="return",o.arg=e,S(t,o),"throw"===o.method)||"return"!==r&&(o.method="throw",o.arg=new TypeError("The iterator does not provide a '"+r+"' method")),y;var i=p(n,t.iterator,o.arg);if("throw"===i.type)return o.method="throw",o.arg=i.arg,o.delegate=null,y;var a=i.arg;return a?a.done?(o[t.resultName]=a.value,o.next=t.nextLoc,"return"!==o.method&&(o.method="next",o.arg=e),o.delegate=null,y):a:(o.method="throw",o.arg=new TypeError("iterator result is not an object"),o.delegate=null,y)}function D(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function P(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(D,this),this.reset(!0)}function N(t){if(t||""===t){var o=t[a];if(o)return o.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function o(){for(;++n<t.length;)if(r.call(t,n))return o.value=t[n],o.done=!1,o;return o.value=e,o.done=!0,o};return i.next=i}}throw new TypeError(m(t)+" is not iterable")}return F.prototype=_,n(T,"constructor",{value:_,configurable:!0}),n(_,"constructor",{value:F,configurable:!0}),F.displayName=l(_,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===F||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,l(e,c,"GeneratorFunction")),e.prototype=Object.create(T),e},t.awrap=function(e){return{__await:e}},C(O.prototype),l(O.prototype,s,(function(){return this})),t.AsyncIterator=O,t.async=function(e,o,r,n,i){void 0===i&&(i=Promise);var a=new O(u(e,o,r,n),i);return t.isGeneratorFunction(o)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},C(T),l(T,c,"Generator"),l(T,a,(function(){return this})),l(T,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),o=[];for(var r in t)o.push(r);return o.reverse(),function e(){for(;o.length;){var r=o.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=N,P.prototype={constructor:P,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(E),!t)for(var o in this)"t"===o.charAt(0)&&r.call(this,o)&&!isNaN(+o.slice(1))&&(this[o]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var o=this;function n(r,n){return s.type="throw",s.arg=t,o.next=r,n&&(o.method="next",o.arg=e),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),l=r.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(e,t){for(var o=this.tryEntries.length-1;o>=0;--o){var n=this.tryEntries[o];if(n.tryLoc<=this.prev&&r.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var o=this.tryEntries[t];if(o.finallyLoc===e)return this.complete(o.completion,o.afterLoc),E(o),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var o=this.tryEntries[t];if(o.tryLoc===e){var r=o.completion;if("throw"===r.type){var n=r.arg;E(o)}return n}}throw Error("illegal catch attempt")},delegateYield:function(t,o,r){return this.delegate={iterator:N(t),resultName:o,nextLoc:r},"next"===this.method&&(this.arg=e),y}},t}function f(e,t,o,r,n,i,a){try{var s=e[i](a),c=s.value}catch(e){return void o(e)}s.done?t(c):Promise.resolve(c).then(r,n)}function h(e){return function(){var t=this,o=arguments;return new Promise((function(r,n){var i=e.apply(t,o);function a(e){f(i,r,n,a,s,"next",e)}function s(e){f(i,r,n,a,s,"throw",e)}a(void 0)}))}}var g={name:"MerchantAddCoupon",components:{OrganizationSelect:i["a"],UserGroupSelect:a["a"],PersonDialog:s["default"],FoodDialog:c["default"]},data:function(){var e=this,t=function(e,t,o){if(t){var r=/(^[1-9](\d+)?(\.\d{1,2})?$)|(^\d\.\d{1,2}$)/;r.test(t)?Number(t)>=99999.99?o(new Error("金额单位上限为‘万’")):o():o(new Error("金额格式有误"))}else o(new Error("请输入金额"))},o=function(t,o,r){if(o.length){var n=new Date(Object(u["M"])(new Date,"{y}-{m}-{d}")).getTime(),i=new Date(e.couponForm.receiveTime[0]).getTime(),a=new Date(e.couponForm.receiveTime[1]).getTime();i<n?r(new Error("领取开始时间不能小于当前时间")):e.couponForm.limitTime.length&&i>new Date(e.couponForm.limitTime[0]).getTime()?r(new Error("领取开始时间必须小于使用开始时间")):e.couponForm.limitTime.length&&a>=new Date(e.couponForm.limitTime[1]).getTime()?r(new Error("领取结束时间必须小于使用结束时间")):r()}else r(new Error("请选择领取时间"))},r=function(t,o,r){if(o.length){var n=new Date(Object(u["M"])(new Date,"{y}-{m}-{d}")).getTime(),i=new Date(e.couponForm.limitTime[0]).getTime(),a=new Date(e.couponForm.limitTime[1]).getTime();i<n?r(new Error("开始时间不能小于当前时间")):e.couponForm.receiveTime.length&&i<new Date(e.couponForm.receiveTime[0]).getTime()?r(new Error("开始时间必须小于领取开始时间")):e.couponForm.receiveTime.length&&a<new Date(e.couponForm.receiveTime[1]).getTime()?r(new Error("结束时间必须大于领取结束时间")):r()}else e.couponForm.isLimitTime?r(new Error("请选择发放时间")):r()},n=function(e,t,o){if(t){var r=/^[1-9][0-9]*$/;r.test(t)?t<1?o(new Error("数量必须大于0")):t>1e5?o(new Error("数量必须不能大于99999")):o():o(new Error("请输入正整数"))}else o(new Error("请填写发放张数"))},i=function(e,t,o){if(t){var r=/^[1-9][0-9]*$/;r.test(t)?t<1?o(new Error("数量必须大于0")):t>1e4?o(new Error("数量必须不能大于9999")):o():o(new Error("请输入正整数"))}else o(new Error("请填写数量"))},a=function(e,t,o){if(t){var r=/^\d+(\.\d{1})?$/;t<1||!r.test(t)?o(new Error("数量必须大于0,且输入一位小数")):t>=10||!r.test(t)?o(new Error("数量必须不能大于9,且输入一位小数")):o()}else o(new Error("请填写折扣数"))};return{isLoading:!1,type:"add",progress:"",couponId:"",couponForm:{couponName:"",couponType:"FULL_DISCOUNT",userOrg:[],maxPrice:"",minPrice:"",preferentialPrice:"",preferentialDiscount:"",isSetMaxDiscount:!1,userMaxDiscount:"",maxDiscountType:"all",isOrderFee:!1,orderFee:"",foodMaxCount:"",foodList:[],couponStatus:"",userMeal:[],receiveType:"MANUAL",receiveTime:[],sendCount:"",receiveCount:"",isLimitTime:!1,limitTime:[],receiveFactor:"ALL",receiveGroup:[],receivePersonList:[],remark:""},couponFormRule:{couponName:[{required:!0,message:"请输入优惠券名称",trigger:["change","blur"]}],couponType:[{required:!0,message:"请选择券类型",trigger:["change","blur"]}],userOrg:[{required:!0,message:"请选择适用组织",trigger:["change","blur"]}],maxPrice:[{required:!0,validator:t,trigger:["change","blur"]}],minPrice:[{required:!0,validator:t,trigger:["change","blur"]}],preferentialPrice:[{required:!0,validator:t,trigger:["change","blur"]}],preferentialDiscount:[{required:!0,validator:a,trigger:["change","blur"]}],userMaxDiscount:[{required:!0,validator:t,trigger:["change","blur"]}],foodMaxCount:[{required:!0,validator:i,trigger:"blur"}],couponStatus:[{required:!0,message:"请输入状态",trigger:["change","blur"]}],userMeal:[{required:!0,message:"请选择适用餐段",trigger:["change","blur"]}],receiveType:[{required:!0,message:"请选择优惠券领取方式",trigger:["change","blur"]}],receiveTime:[{required:!0,validator:o,trigger:["change","blur"]}],sendCount:[{required:!0,validator:n,trigger:["change","blur"]}],receiveCount:[{required:!0,validator:i,trigger:["change","blur"]}],limitTime:[{validator:r,trigger:["change","blur"]}],receiveGroup:[{required:!0,message:"请选择分组",trigger:["change","blur"]}]},mealList:l["MEAL_LIST"],chooseList:[],personList:[],sortList:[],SortWrap:null,pickerOptions:{disabledDate:function(e){return e.getTime()<Date.now()-864e5}},dialogPersonVisible:!1,dialogFoodVisible:!1}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){if(this.$route.query.data){var e=JSON.parse(decodeURIComponent(this.$route.query.data));this.couponId=e.id,this.couponForm.couponName=e.name,this.couponForm.couponType=e.coupon_type,this.couponForm.userOrg=e.use_organization,this.couponForm.couponStatus=e.is_enable,this.couponForm.userMeal=e.meal_type_list,this.couponForm.receiveType=e.receive_type,this.couponForm.isLimitTime=e.is_use_time,this.couponForm.limitTime=e.use_start_time&&e.use_end_time?[e.use_start_time,e.use_end_time]:[],this.couponForm.receiveFactor=e.receive_condition,this.couponForm.remark=e.remark,"FULL_DISCOUNT"===this.couponForm.couponType?(this.couponForm.maxPrice=Object(u["i"])(e.use_condition.full_money),this.couponForm.minPrice=Object(u["i"])(e.use_condition.reduce)):"INSTANT_DISCOUNT"===this.couponForm.couponType?this.couponForm.preferentialPrice=Object(u["i"])(e.use_condition.reduce):"DISCOUNT"===this.couponForm.couponType?(this.couponForm.preferentialDiscount=e.use_condition.discount,this.couponForm.isSetMaxDiscount=e.use_condition.is_max,this.couponForm.isSetMaxDiscount&&(this.couponForm.userMaxDiscount=Object(u["i"])(e.use_condition.max_money)),this.couponForm.maxDiscountType=e.use_condition.max_discount):"EXCHANGE"===this.couponForm.couponType&&(this.couponForm.isOrderFee=e.use_condition.is_satisfy,this.couponForm.isOrderFee&&(this.couponForm.orderFee=Object(u["i"])(e.use_condition.satisfy_money)),this.couponForm.foodMaxCount=e.use_condition.max_num,this.couponForm.foodList=e.use_condition.exchange_list.map((function(e,t){return{id:e.food_id,name:e.food_name}}))),"MANUAL"===this.couponForm.receiveType?(this.couponForm.sendCount=e.issued_number,this.couponForm.receiveTime=e.receive_start_time&&e.receive_end_time?[e.receive_start_time,e.receive_end_time]:[],this.couponForm.receiveCount=e.manual_receive_number):"AUTOMATIC"===this.couponForm.receiveType&&(this.couponForm.sendCount=e.automatic_receive_number),"GROUP"===this.couponForm.receiveFactor?this.couponForm.receiveGroup=e.card_user_group:"PERSON"===this.couponForm.receiveFactor&&(this.personList=e.card_info_list.map((function(e,t){return{id:e.id,organization_alias:e.organization_alias,department_group_name:e.department_group_name,card_user_group_alias:e.card_user_group_alias,person_no:e.person_no,name:e.name}})))}this.$route.params.type&&(this.type=this.$route.params.type)},checkForm:function(){var e=this;this.$refs.couponFormRef.validate((function(t){if(!t)return e.$message.error("数据填写有误，请检查"),!1;if(!e.personList.length&&"PERSON"===e.couponForm.receiveFactor)return e.$message.error("请选择人员");var o,r={name:e.couponForm.couponName,coupon_type:e.couponForm.couponType,use_organization:e.couponForm.userOrg,is_enable:e.couponForm.couponStatus,meal_type_list:e.couponForm.userMeal,receive_type:e.couponForm.receiveType,issued_number:e.couponForm.sendCount,is_use_time:e.couponForm.isLimitTime,use_start_time:e.couponForm.limitTime[0],use_end_time:e.couponForm.limitTime[1],receive_condition:e.couponForm.receiveFactor,remark:e.couponForm.remark},n={};"FULL_DISCOUNT"===e.couponForm.couponType?(n.full_money=Object(u["Y"])(e.couponForm.maxPrice),n.reduce=Object(u["Y"])(e.couponForm.minPrice)):"INSTANT_DISCOUNT"===e.couponForm.couponType?n.reduce=Object(u["Y"])(e.couponForm.preferentialPrice):"DISCOUNT"===e.couponForm.couponType?(n.discount=e.couponForm.preferentialDiscount,n.is_max=e.couponForm.isSetMaxDiscount,e.couponForm.isSetMaxDiscount&&(n.max_money=Object(u["Y"])(Number(e.couponForm.userMaxDiscount))),n.max_discount=e.couponForm.maxDiscountType):"EXCHANGE"===e.couponForm.couponType&&(n.is_satisfy=e.couponForm.isOrderFee,n.is_satisfy&&(n.satisfy_money=Object(u["Y"])(Number(e.couponForm.orderFee))),n.max_num=e.couponForm.foodMaxCount,n.exchange_list=e.couponForm.foodList.map((function(e,t){return{food_id:e.id,food_name:e.name,seq:t+1}}))),r.use_condition=n,"MANUAL"===e.couponForm.receiveType?(r.issued_number=e.couponForm.sendCount,r.receive_start_time=e.couponForm.receiveTime[0],r.receive_end_time=e.couponForm.receiveTime[1],r.manual_receive_number=e.couponForm.receiveCount):"AUTOMATIC"===e.couponForm.receiveType&&(r.automatic_receive_number=e.couponForm.sendCount),"GROUP"===e.couponForm.receiveFactor?r.card_user_group=e.couponForm.receiveGroup:"PERSON"===e.couponForm.receiveFactor&&(r.card_info=e.personList.map((function(e,t){return e.id}))),"add"===e.type?o=e.$apis.apiBackgroundCouponCouponManageAddPost:(r.id=e.couponId,o=e.$apis.apiBackgroundCouponCouponManageModifyPost),e.saveSetting(r,o)}))},saveSetting:function(e,t){var o=this;return h(d().mark((function r(){var n;return d().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,t(e);case 2:n=r.sent,0===n.code?(o.$message.success("保存成功"),o.$closeCurrentTab(o.$route.path)):o.$message.error(n.msg);case 4:case"end":return r.stop()}}),r)})))()},openChooseDialog:function(e){"choosePerson"===e?(this.chooseList=this.personList,this.dialogPersonVisible=!0):"food"===e&&(this.chooseList=this.couponForm.foodList,this.dialogFoodVisible=!0)},confirmPerson:function(e){e.map((function(e){e.department_group_name=e.card_department_group_alias,e.gender_name=e.gender_alias})),this.personList=e},confirmFood:function(e){this.couponForm.foodList=e,this.initSortable()},delOperation:function(e){this.personList.splice(e,1)},initSortable:function(){var e=this;this.sortList=this.couponForm.foodList.map((function(e){return e.id}));var t=this.$refs.tableDataRef.$el.querySelector(".el-table__body-wrapper > table > tbody");this.sortWrap=p["a"].create(t,{ghostClass:"sort-active",animation:300,setData:function(e){e.setData("Text","")},onEnd:function(t){var o=e.couponForm.foodList.splice(t.oldIndex,1)[0];e.couponForm.foodList.splice(t.newIndex,0,o);var r=e.sortList.splice(t.oldIndex,1)[0];e.sortList.splice(t.newIndex,0,r)}})},deleteFoodHandle:function(e){this.couponForm.foodList.splice(e,1)}}},v=g,y=(o("f8a7"),o("2877")),b=Object(y["a"])(v,r,n,!1,null,null,null);t["default"]=b.exports},"9e41":function(e,t,o){"use strict";o("07a7")},f6ce:function(e,t,o){"use strict";o.r(t),o.d(t,"WALLET_LIST",(function(){return r})),o.d(t,"MEAL_LIST",(function(){return n})),o.d(t,"DEDUCTION_SERVICE",(function(){return i})),o.d(t,"RECHARGE_SERVICE",(function(){return a}));var r=[{name:"储值钱包",key:"store_wallet_on"},{name:"补贴钱包",key:"subsidy_wallet_on"},{name:"赠送钱包",key:"complimentary_wallet_on"},{name:"电子账户钱包",key:"electronic_wallet_on"},{name:"组合支付",key:"combine_wallet_on"}],n=[{name:"早餐",value:"breakfast",disabled:!1},{name:"午餐",value:"lunch",disabled:!1},{name:"下午茶",value:"afternoon",disabled:!1},{name:"晚餐",value:"dinner",disabled:!1},{name:"夜宵",value:"supper",disabled:!1},{name:"凌晨餐",value:"morning",disabled:!1}],i={name:{type:"input",label:"规则名称",value:"",placeholder:"请输入规则名称"},group_nos:{type:"groupSelect",label:"适用分组",value:[],placeholder:"请选择",multiple:!0},org_nos:{type:"organizationSelect",value:[],label:"消费点",dataList:[],multiple:!0,collapseTags:!0,checkStrictly:!0},status:{type:"select",label:"状态",value:"",placeholder:"请选择活动状态",dataList:[{label:"全部",value:""},{label:"启用",value:"enable"},{label:"停用",value:"stop"}]}},a={name:{type:"input",label:"规则名称",value:"",placeholder:"请输入规则名称"},group_nos:{type:"groupSelect",label:"适用分组",value:[],placeholder:"请选择",multiple:!0},status:{type:"select",label:"状态",value:"",placeholder:"请选择活动状态",dataList:[{label:"全部",value:""},{label:"启用",value:"enable"},{label:"停用",value:"stop"}]}}},f8a7:function(e,t,o){"use strict";o("08e7")}}]);