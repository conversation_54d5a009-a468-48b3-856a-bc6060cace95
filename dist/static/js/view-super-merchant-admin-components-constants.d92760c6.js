(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-merchant-admin-components-constants"],{fa48:function(n,t,e){"use strict";e.r(t),e.d(t,"RECENTYEAR",(function(){return o})),e.d(t,"divide",(function(){return d})),e.d(t,"times",(function(){return s}));var i=e("5a0c"),r=e("da92"),o=[i().format("YYYY-MM-DD HH:mm:ss"),i().add(1,"year").hour(23).minute(59).second(59).format("YYYY-MM-DD HH:mm:ss")],d=function(n){return n?"number"===typeof n?r["a"].divide(n,100).toFixed(2):"string"!==typeof n||isNaN(Number(n))?n:r["a"].divide(n,100).toFixed(2):"0.00"},s=function(n){return r["a"].times(n,100)}}}]);