(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-merchant-admin-components-accountManagement"],{"3c35f":function(t,e){(function(e){t.exports=e}).call(this,{})},8237:function(module,exports,__webpack_require__){(function(process,global){var __WEBPACK_AMD_DEFINE_RESULT__;
/**
 * [js-md5]{@link https://github.com/emn178/js-md5}
 *
 * @namespace md5
 * @version 0.7.3
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON> [<EMAIL>]
 * @copyright Chen, Yi-<PERSON><PERSON> 2014-2017
 * @license MIT
 */(function(){"use strict";var ERROR="input is invalid type",WINDOW="object"===typeof window,root=WINDOW?window:{};root.JS_MD5_NO_WINDOW&&(WINDOW=!1);var WEB_WORKER=!WINDOW&&"object"===typeof self,NODE_JS=!root.JS_MD5_NO_NODE_JS&&"object"===typeof process&&process.versions&&process.versions.node;NODE_JS?root=global:WEB_WORKER&&(root=self);var COMMON_JS=!root.JS_MD5_NO_COMMON_JS&&"object"===typeof module&&module.exports,AMD=__webpack_require__("3c35f"),ARRAY_BUFFER=!root.JS_MD5_NO_ARRAY_BUFFER&&"undefined"!==typeof ArrayBuffer,HEX_CHARS="0123456789abcdef".split(""),EXTRA=[128,32768,8388608,-**********],SHIFT=[0,8,16,24],OUTPUT_TYPES=["hex","array","digest","buffer","arrayBuffer","base64"],BASE64_ENCODE_CHAR="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),blocks=[],buffer8;if(ARRAY_BUFFER){var buffer=new ArrayBuffer(68);buffer8=new Uint8Array(buffer),blocks=new Uint32Array(buffer)}!root.JS_MD5_NO_NODE_JS&&Array.isArray||(Array.isArray=function(t){return"[object Array]"===Object.prototype.toString.call(t)}),!ARRAY_BUFFER||!root.JS_MD5_NO_ARRAY_BUFFER_IS_VIEW&&ArrayBuffer.isView||(ArrayBuffer.isView=function(t){return"object"===typeof t&&t.buffer&&t.buffer.constructor===ArrayBuffer});var createOutputMethod=function(t){return function(e){return new Md5(!0).update(e)[t]()}},createMethod=function(){var t=createOutputMethod("hex");NODE_JS&&(t=nodeWrap(t)),t.create=function(){return new Md5},t.update=function(e){return t.create().update(e)};for(var e=0;e<OUTPUT_TYPES.length;++e){var r=OUTPUT_TYPES[e];t[r]=createOutputMethod(r)}return t},nodeWrap=function(method){var crypto=eval("require('crypto')"),Buffer=eval("require('buffer').Buffer"),nodeMethod=function(t){if("string"===typeof t)return crypto.createHash("md5").update(t,"utf8").digest("hex");if(null===t||void 0===t)throw ERROR;return t.constructor===ArrayBuffer&&(t=new Uint8Array(t)),Array.isArray(t)||ArrayBuffer.isView(t)||t.constructor===Buffer?crypto.createHash("md5").update(new Buffer(t)).digest("hex"):method(t)};return nodeMethod};function Md5(t){if(t)blocks[0]=blocks[16]=blocks[1]=blocks[2]=blocks[3]=blocks[4]=blocks[5]=blocks[6]=blocks[7]=blocks[8]=blocks[9]=blocks[10]=blocks[11]=blocks[12]=blocks[13]=blocks[14]=blocks[15]=0,this.blocks=blocks,this.buffer8=buffer8;else if(ARRAY_BUFFER){var e=new ArrayBuffer(68);this.buffer8=new Uint8Array(e),this.blocks=new Uint32Array(e)}else this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];this.h0=this.h1=this.h2=this.h3=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}Md5.prototype.update=function(t){if(!this.finalized){var e,r=typeof t;if("string"!==r){if("object"!==r)throw ERROR;if(null===t)throw ERROR;if(ARRAY_BUFFER&&t.constructor===ArrayBuffer)t=new Uint8Array(t);else if(!Array.isArray(t)&&(!ARRAY_BUFFER||!ArrayBuffer.isView(t)))throw ERROR;e=!0}var a,s,o=0,i=t.length,n=this.blocks,c=this.buffer8;while(o<i){if(this.hashed&&(this.hashed=!1,n[0]=n[16],n[16]=n[1]=n[2]=n[3]=n[4]=n[5]=n[6]=n[7]=n[8]=n[9]=n[10]=n[11]=n[12]=n[13]=n[14]=n[15]=0),e)if(ARRAY_BUFFER)for(s=this.start;o<i&&s<64;++o)c[s++]=t[o];else for(s=this.start;o<i&&s<64;++o)n[s>>2]|=t[o]<<SHIFT[3&s++];else if(ARRAY_BUFFER)for(s=this.start;o<i&&s<64;++o)a=t.charCodeAt(o),a<128?c[s++]=a:a<2048?(c[s++]=192|a>>6,c[s++]=128|63&a):a<55296||a>=57344?(c[s++]=224|a>>12,c[s++]=128|a>>6&63,c[s++]=128|63&a):(a=65536+((1023&a)<<10|1023&t.charCodeAt(++o)),c[s++]=240|a>>18,c[s++]=128|a>>12&63,c[s++]=128|a>>6&63,c[s++]=128|63&a);else for(s=this.start;o<i&&s<64;++o)a=t.charCodeAt(o),a<128?n[s>>2]|=a<<SHIFT[3&s++]:a<2048?(n[s>>2]|=(192|a>>6)<<SHIFT[3&s++],n[s>>2]|=(128|63&a)<<SHIFT[3&s++]):a<55296||a>=57344?(n[s>>2]|=(224|a>>12)<<SHIFT[3&s++],n[s>>2]|=(128|a>>6&63)<<SHIFT[3&s++],n[s>>2]|=(128|63&a)<<SHIFT[3&s++]):(a=65536+((1023&a)<<10|1023&t.charCodeAt(++o)),n[s>>2]|=(240|a>>18)<<SHIFT[3&s++],n[s>>2]|=(128|a>>12&63)<<SHIFT[3&s++],n[s>>2]|=(128|a>>6&63)<<SHIFT[3&s++],n[s>>2]|=(128|63&a)<<SHIFT[3&s++]);this.lastByteIndex=s,this.bytes+=s-this.start,s>=64?(this.start=s-64,this.hash(),this.hashed=!0):this.start=s}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this}},Md5.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var t=this.blocks,e=this.lastByteIndex;t[e>>2]|=EXTRA[3&e],e>=56&&(this.hashed||this.hash(),t[0]=t[16],t[16]=t[1]=t[2]=t[3]=t[4]=t[5]=t[6]=t[7]=t[8]=t[9]=t[10]=t[11]=t[12]=t[13]=t[14]=t[15]=0),t[14]=this.bytes<<3,t[15]=this.hBytes<<3|this.bytes>>>29,this.hash()}},Md5.prototype.hash=function(){var t,e,r,a,s,o,i=this.blocks;this.first?(t=i[0]-680876937,t=(t<<7|t>>>25)-271733879<<0,a=(-1732584194^2004318071&t)+i[1]-117830708,a=(a<<12|a>>>20)+t<<0,r=(-271733879^a&(-271733879^t))+i[2]-1126478375,r=(r<<17|r>>>15)+a<<0,e=(t^r&(a^t))+i[3]-1316259209,e=(e<<22|e>>>10)+r<<0):(t=this.h0,e=this.h1,r=this.h2,a=this.h3,t+=(a^e&(r^a))+i[0]-680876936,t=(t<<7|t>>>25)+e<<0,a+=(r^t&(e^r))+i[1]-389564586,a=(a<<12|a>>>20)+t<<0,r+=(e^a&(t^e))+i[2]+606105819,r=(r<<17|r>>>15)+a<<0,e+=(t^r&(a^t))+i[3]-1044525330,e=(e<<22|e>>>10)+r<<0),t+=(a^e&(r^a))+i[4]-176418897,t=(t<<7|t>>>25)+e<<0,a+=(r^t&(e^r))+i[5]+1200080426,a=(a<<12|a>>>20)+t<<0,r+=(e^a&(t^e))+i[6]-1473231341,r=(r<<17|r>>>15)+a<<0,e+=(t^r&(a^t))+i[7]-45705983,e=(e<<22|e>>>10)+r<<0,t+=(a^e&(r^a))+i[8]+1770035416,t=(t<<7|t>>>25)+e<<0,a+=(r^t&(e^r))+i[9]-1958414417,a=(a<<12|a>>>20)+t<<0,r+=(e^a&(t^e))+i[10]-42063,r=(r<<17|r>>>15)+a<<0,e+=(t^r&(a^t))+i[11]-1990404162,e=(e<<22|e>>>10)+r<<0,t+=(a^e&(r^a))+i[12]+1804603682,t=(t<<7|t>>>25)+e<<0,a+=(r^t&(e^r))+i[13]-40341101,a=(a<<12|a>>>20)+t<<0,r+=(e^a&(t^e))+i[14]-1502002290,r=(r<<17|r>>>15)+a<<0,e+=(t^r&(a^t))+i[15]+1236535329,e=(e<<22|e>>>10)+r<<0,t+=(r^a&(e^r))+i[1]-165796510,t=(t<<5|t>>>27)+e<<0,a+=(e^r&(t^e))+i[6]-1069501632,a=(a<<9|a>>>23)+t<<0,r+=(t^e&(a^t))+i[11]+643717713,r=(r<<14|r>>>18)+a<<0,e+=(a^t&(r^a))+i[0]-373897302,e=(e<<20|e>>>12)+r<<0,t+=(r^a&(e^r))+i[5]-701558691,t=(t<<5|t>>>27)+e<<0,a+=(e^r&(t^e))+i[10]+38016083,a=(a<<9|a>>>23)+t<<0,r+=(t^e&(a^t))+i[15]-660478335,r=(r<<14|r>>>18)+a<<0,e+=(a^t&(r^a))+i[4]-405537848,e=(e<<20|e>>>12)+r<<0,t+=(r^a&(e^r))+i[9]+568446438,t=(t<<5|t>>>27)+e<<0,a+=(e^r&(t^e))+i[14]-1019803690,a=(a<<9|a>>>23)+t<<0,r+=(t^e&(a^t))+i[3]-187363961,r=(r<<14|r>>>18)+a<<0,e+=(a^t&(r^a))+i[8]+1163531501,e=(e<<20|e>>>12)+r<<0,t+=(r^a&(e^r))+i[13]-1444681467,t=(t<<5|t>>>27)+e<<0,a+=(e^r&(t^e))+i[2]-51403784,a=(a<<9|a>>>23)+t<<0,r+=(t^e&(a^t))+i[7]+1735328473,r=(r<<14|r>>>18)+a<<0,e+=(a^t&(r^a))+i[12]-1926607734,e=(e<<20|e>>>12)+r<<0,s=e^r,t+=(s^a)+i[5]-378558,t=(t<<4|t>>>28)+e<<0,a+=(s^t)+i[8]-2022574463,a=(a<<11|a>>>21)+t<<0,o=a^t,r+=(o^e)+i[11]+1839030562,r=(r<<16|r>>>16)+a<<0,e+=(o^r)+i[14]-35309556,e=(e<<23|e>>>9)+r<<0,s=e^r,t+=(s^a)+i[1]-1530992060,t=(t<<4|t>>>28)+e<<0,a+=(s^t)+i[4]+1272893353,a=(a<<11|a>>>21)+t<<0,o=a^t,r+=(o^e)+i[7]-155497632,r=(r<<16|r>>>16)+a<<0,e+=(o^r)+i[10]-1094730640,e=(e<<23|e>>>9)+r<<0,s=e^r,t+=(s^a)+i[13]+681279174,t=(t<<4|t>>>28)+e<<0,a+=(s^t)+i[0]-358537222,a=(a<<11|a>>>21)+t<<0,o=a^t,r+=(o^e)+i[3]-722521979,r=(r<<16|r>>>16)+a<<0,e+=(o^r)+i[6]+76029189,e=(e<<23|e>>>9)+r<<0,s=e^r,t+=(s^a)+i[9]-640364487,t=(t<<4|t>>>28)+e<<0,a+=(s^t)+i[12]-421815835,a=(a<<11|a>>>21)+t<<0,o=a^t,r+=(o^e)+i[15]+530742520,r=(r<<16|r>>>16)+a<<0,e+=(o^r)+i[2]-995338651,e=(e<<23|e>>>9)+r<<0,t+=(r^(e|~a))+i[0]-198630844,t=(t<<6|t>>>26)+e<<0,a+=(e^(t|~r))+i[7]+1126891415,a=(a<<10|a>>>22)+t<<0,r+=(t^(a|~e))+i[14]-1416354905,r=(r<<15|r>>>17)+a<<0,e+=(a^(r|~t))+i[5]-57434055,e=(e<<21|e>>>11)+r<<0,t+=(r^(e|~a))+i[12]+1700485571,t=(t<<6|t>>>26)+e<<0,a+=(e^(t|~r))+i[3]-1894986606,a=(a<<10|a>>>22)+t<<0,r+=(t^(a|~e))+i[10]-1051523,r=(r<<15|r>>>17)+a<<0,e+=(a^(r|~t))+i[1]-2054922799,e=(e<<21|e>>>11)+r<<0,t+=(r^(e|~a))+i[8]+1873313359,t=(t<<6|t>>>26)+e<<0,a+=(e^(t|~r))+i[15]-30611744,a=(a<<10|a>>>22)+t<<0,r+=(t^(a|~e))+i[6]-1560198380,r=(r<<15|r>>>17)+a<<0,e+=(a^(r|~t))+i[13]+1309151649,e=(e<<21|e>>>11)+r<<0,t+=(r^(e|~a))+i[4]-145523070,t=(t<<6|t>>>26)+e<<0,a+=(e^(t|~r))+i[11]-1120210379,a=(a<<10|a>>>22)+t<<0,r+=(t^(a|~e))+i[2]+718787259,r=(r<<15|r>>>17)+a<<0,e+=(a^(r|~t))+i[9]-343485551,e=(e<<21|e>>>11)+r<<0,this.first?(this.h0=t+1732584193<<0,this.h1=e-271733879<<0,this.h2=r-1732584194<<0,this.h3=a+271733878<<0,this.first=!1):(this.h0=this.h0+t<<0,this.h1=this.h1+e<<0,this.h2=this.h2+r<<0,this.h3=this.h3+a<<0)},Md5.prototype.hex=function(){this.finalize();var t=this.h0,e=this.h1,r=this.h2,a=this.h3;return HEX_CHARS[t>>4&15]+HEX_CHARS[15&t]+HEX_CHARS[t>>12&15]+HEX_CHARS[t>>8&15]+HEX_CHARS[t>>20&15]+HEX_CHARS[t>>16&15]+HEX_CHARS[t>>28&15]+HEX_CHARS[t>>24&15]+HEX_CHARS[e>>4&15]+HEX_CHARS[15&e]+HEX_CHARS[e>>12&15]+HEX_CHARS[e>>8&15]+HEX_CHARS[e>>20&15]+HEX_CHARS[e>>16&15]+HEX_CHARS[e>>28&15]+HEX_CHARS[e>>24&15]+HEX_CHARS[r>>4&15]+HEX_CHARS[15&r]+HEX_CHARS[r>>12&15]+HEX_CHARS[r>>8&15]+HEX_CHARS[r>>20&15]+HEX_CHARS[r>>16&15]+HEX_CHARS[r>>28&15]+HEX_CHARS[r>>24&15]+HEX_CHARS[a>>4&15]+HEX_CHARS[15&a]+HEX_CHARS[a>>12&15]+HEX_CHARS[a>>8&15]+HEX_CHARS[a>>20&15]+HEX_CHARS[a>>16&15]+HEX_CHARS[a>>28&15]+HEX_CHARS[a>>24&15]},Md5.prototype.toString=Md5.prototype.hex,Md5.prototype.digest=function(){this.finalize();var t=this.h0,e=this.h1,r=this.h2,a=this.h3;return[255&t,t>>8&255,t>>16&255,t>>24&255,255&e,e>>8&255,e>>16&255,e>>24&255,255&r,r>>8&255,r>>16&255,r>>24&255,255&a,a>>8&255,a>>16&255,a>>24&255]},Md5.prototype.array=Md5.prototype.digest,Md5.prototype.arrayBuffer=function(){this.finalize();var t=new ArrayBuffer(16),e=new Uint32Array(t);return e[0]=this.h0,e[1]=this.h1,e[2]=this.h2,e[3]=this.h3,t},Md5.prototype.buffer=Md5.prototype.arrayBuffer,Md5.prototype.base64=function(){for(var t,e,r,a="",s=this.array(),o=0;o<15;)t=s[o++],e=s[o++],r=s[o++],a+=BASE64_ENCODE_CHAR[t>>>2]+BASE64_ENCODE_CHAR[63&(t<<4|e>>>4)]+BASE64_ENCODE_CHAR[63&(e<<2|r>>>6)]+BASE64_ENCODE_CHAR[63&r];return t=s[o],a+=BASE64_ENCODE_CHAR[t>>>2]+BASE64_ENCODE_CHAR[t<<4&63]+"==",a};var exports=createMethod();COMMON_JS?module.exports=exports:(root.md5=exports,AMD&&(__WEBPACK_AMD_DEFINE_RESULT__=function(){return exports}.call(exports,__webpack_require__,exports,module),void 0===__WEBPACK_AMD_DEFINE_RESULT__||(module.exports=__WEBPACK_AMD_DEFINE_RESULT__)))})()}).call(this,__webpack_require__("4362"),__webpack_require__("c8ba"))},"87f2":function(t,e,r){},e5129:function(t,e,r){"use strict";r("87f2")},f025:function(t,e,r){"use strict";r.r(e);var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"channel-info-content"},[e("el-form",{attrs:{"label-position":"left","label-width":"100px"}},[e("el-form-item",{attrs:{label:"账号管理："}},[e("el-button",{staticClass:"m-r-20",attrs:{type:"text"},on:{click:function(e){return t.showDrawer("add")}}},[t._v("添加账号")]),e("div",{staticClass:"table-style"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],attrs:{data:t.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},t._l(t.tableSetting,(function(r,a){return e("table-column",{key:a,attrs:{col:r},scopedSlots:t._u([{key:"isDoubleFactor",fn:function(r){var a=r.row;return[e("el-switch",{on:{change:function(e){return t.openOrNot(a)}},model:{value:a.is_double_factor,callback:function(e){t.$set(a,"is_double_factor",e)},expression:"row.is_double_factor"}})]}},{key:"operation",fn:function(r){var a=r.row;return[e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.showDrawer("edit",a)}}},[t._v("编辑")]),e("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.deleteHandle(a)}}},[t._v("移除")])]}}],null,!0)})})),1),e("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[e("el-pagination",{staticClass:"ps-text",attrs:{"current-page":t.page,"page-sizes":[10,20,50,100,500],"page-size":t.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:t.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1)],1)],1),e("div",{staticClass:"ps-el-drawer"},[e("el-drawer",{attrs:{title:"add"===t.drawerType?"添加账号":"编辑账号",visible:t.addAccountDrawerShow,"show-close":!1,size:"40%"}},[e("div",{staticClass:"p-20"},[e("div",{staticClass:"red font-size-14 m-b-20"},[t._v("提示：账号、手机号只能对应一个监管渠道。")]),e("el-form",{ref:"addAccountFormRef",attrs:{rules:t.addAccountFormRule,model:t.addAccountForm,"label-width":"80px","label-position":"right"}},[e("el-form-item",{attrs:{label:"用户姓名",prop:"name"}},[e("el-input",{staticClass:"w-300",attrs:{placeholder:"请输入姓名",maxlength:"10"},model:{value:t.addAccountForm.name,callback:function(e){t.$set(t.addAccountForm,"name",e)},expression:"addAccountForm.name"}})],1),e("el-form-item",{attrs:{label:"手机号码",prop:"phone"}},[e("el-input",{staticClass:"w-300",attrs:{placeholder:"请输入手机号码",maxlength:"11"},model:{value:t.addAccountForm.phone,callback:function(e){t.$set(t.addAccountForm,"phone",e)},expression:"addAccountForm.phone"}})],1),e("el-form-item",{attrs:{label:"登录账号",prop:"account"}},[e("el-input",{staticClass:"w-300",attrs:{placeholder:"请输入登录账号",maxlength:"12",disabled:"edit"===t.drawerType},model:{value:t.addAccountForm.account,callback:function(e){t.$set(t.addAccountForm,"account",e)},expression:"addAccountForm.account"}})],1),e("el-form-item",{attrs:{label:"登录密码",prop:"password",rules:[{required:"add"===t.drawerType,message:"请输入密码",trigger:["change","blur"]},{min:8,message:"登录密码不能小于8位",trigger:["change","blur"]},{pattern:/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,20}$/,message:"请输入8~20位数，需包含英文和数字的密码",trigger:["change","blur"]}]}},[e("el-input",{staticClass:"w-300",attrs:{placeholder:"add"===t.drawerType?"请输入登录密码":"无需修改密码则不填",maxlength:"20"},model:{value:t.addAccountForm.password,callback:function(e){t.$set(t.addAccountForm,"password",e)},expression:"addAccountForm.password"}})],1),e("el-form-item",{attrs:{label:"登录校验",prop:"isCheck"}},[e("div",{staticClass:"ps-flex-align-c flex-align-c"},[e("div",{staticClass:"m-r-10"},[t._v("手机验证码校验")]),e("el-switch",{model:{value:t.addAccountForm.isCheck,callback:function(e){t.$set(t.addAccountForm,"isCheck",e)},expression:"addAccountForm.isCheck"}})],1)])],1),e("div",{staticClass:"ps-el-drawer-footer"},[e("el-button",{staticClass:"w-100",attrs:{size:"small"},on:{click:t.cancelHandle}},[t._v("取消")]),e("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:function(e){return t.saveHandle("save")}}},[t._v("保存")]),e("el-button",{directives:[{name:"show",rawName:"v-show",value:"add"===t.drawerType,expression:"drawerType === 'add'"}],staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:function(e){return t.saveHandle("keepGoing")}}},[t._v("保存并继续")])],1)],1)])],1)],1)},s=[],o=r("ed08"),i=r("8237"),n=r.n(i),c={props:{organizationData:{type:Object,default:function(){return{}}},tabType:{type:String,default:""}},data:function(){return{isLoading:!1,tableData:[],tableSetting:[{label:"姓名",key:"name"},{label:"账号",key:"username"},{label:"手机号",key:"mobile"},{label:"验证码校验",key:"is_double_factor",type:"slot",slotName:"isDoubleFactor"},{label:"修改时间",key:"update_time"},{label:"操作人",key:"operator_name"},{label:"操作",key:"operation",type:"slot",slotName:"operation",width:"200"}],drawerType:"",selectId:"",addAccountDrawerShow:!1,addAccountForm:{name:"",phone:"",account:"",password:"",isCheck:!1},page:1,pageSize:10,totalCount:0,addAccountFormRule:{name:[{required:!0,message:"请输入姓名",trigger:["change","blur"]}],phone:[{required:!0,message:"请输入手机号",trigger:["change","blur"]},{min:8,message:"请输入正确的手机号",trigger:["change","blur"]}],account:[{required:!0,message:"请输入登录账号",trigger:["change","blur"]},{min:5,message:"登录账号不能小于5位",trigger:["change","blur"]}]}}},watch:{tabType:{handler:function(t,e){"accountManagement"===t&&this.getAccountDataList()},immediate:!0}},created:function(){this.getAccountDataList()},methods:{showDrawer:function(t,e){var r=this;this.drawerType=t,"edit"===t?(this.selectId=e.id,this.addAccountForm.name=e.name,this.addAccountForm.phone=e.mobile,this.addAccountForm.account=e.username,this.addAccountForm.password="",this.addAccountForm.isCheck=!1):(this.selectId="",this.addAccountForm.name="",this.addAccountForm.phone="",this.addAccountForm.account="",this.addAccountForm.password="",this.addAccountForm.isCheck=!1),this.addAccountDrawerShow=!0,setTimeout((function(){r.$refs.addAccountFormRef.clearValidate()}),10)},handleSizeChange:function(t){this.pageSize=t,this.getAccountDataList()},handleCurrentChange:function(t){this.page=t,this.getAccountDataList()},cancelHandle:function(){this.$refs.addAccountFormRef.resetFields(),this.addAccountDrawerShow=!1},saveHandle:function(t){var e=this;this.$refs.addAccountFormRef.validate((function(r){if(!r)return e.$message.error("请检查表单填写的内容是否正确");var a={id:"add"===e.drawerType?void 0:e.selectId,supervision_channel_id:e.organizationData.id,username:e.addAccountForm.account,password:e.addAccountForm.password?n()(e.addAccountForm.password):void 0,mobile:e.addAccountForm.phone,name:e.addAccountForm.name,is_double_factor:e.addAccountForm.isCheck};"add"===e.drawerType?e.addAccount(a,t):e.editAccount(a)}))},getAccountDataList:function(){var t=this;this.isLoading=!0,this.$apis.apiBackgroundFundSupervisionAuditAccountListPost({supervision_channel_id:this.organizationData.id}).then((function(e){t.isLoading=!1,0===e.code&&(t.tableData=Object(o["f"])(e.data.results||[]),t.totalCount=e.data.count)}))},addAccount:function(t,e){var r=this;this.$apis.apiBackgroundFundSupervisionAuditAccountAddPost(t).then((function(t){if(0===t.code){if(r.$message.success("新增成功"),r.$refs.addAccountFormRef.resetFields(),"keepGoing"===e)return r.getAccountDataList();r.addAccountDrawerShow=!1,r.getAccountDataList()}else r.$message.error(t.msg)}))},editAccount:function(t){var e=this;this.$apis.apiBackgroundFundSupervisionAuditAccountModifyPost(t).then((function(t){0===t.code?(e.$refs.addAccountFormRef.resetFields(),e.$message.success("修改成功")):e.$message.error(t.msg),e.addAccountDrawerShow=!1,e.getAccountDataList()}))},openOrNot:function(t){var e={id:t.id,username:t.username,is_double_factor:t.is_double_factor};this.editAccount(e)},deleteHandle:function(t){var e=this;this.$confirm("确定要删除 ".concat(t.name," 的人员信息，删除后不可恢复，请谨慎操作。"),"提示",{distinguishCancelAndClose:!0,confirmButtonText:"确定",cancelButtonText:"取消"}).then((function(){e.$apis.apiBackgroundFundSupervisionAuditAccountDeletePost({ids:[t.id]}).then((function(t){0===t.code?e.$message.success("删除成功"):e.$message.error(t.msg),e.getAccountDataList()}))})).catch((function(t){e.$message("已取消删除")}))}}},d=c,u=(r("e5129"),r("2877")),l=Object(u["a"])(d,a,s,!1,null,"5e4db5e3",null);e["default"]=l.exports}}]);