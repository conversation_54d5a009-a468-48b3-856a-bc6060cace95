(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-menu-admin-monthMenuFoodSeting","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-AreaFood","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-components-menu-menuPreviewDialog","view-merchant-meal-management-meal-report-MealPackageRule"],{"22ec":function(e,t,n){},"497c":function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e._self._c;return t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}]},[t("div",{staticClass:"box-header-week"},[t("span",[e._v("本月菜谱（"+e._s(e.currentYear)+"）")]),e._m(0)]),t("div",[t("div",{staticClass:"week-header-wrapper"},e._l(e.weekDays,(function(n){return t("div",{key:n,staticClass:"week-day"},[e._v(e._s(n))])})),0),t("div",{staticClass:"content"},e._l(e.dayList,(function(n,r){return t("div",{key:"".concat(n,"_").concat(r),staticClass:"item",style:{borderRight:r===e.dayList.length-1?e.border:""}},[""!==n?t("div",[e._v(e._s(e.currentMonth)+"月"+e._s(n.dayNumber)+"日")]):e._e(),t("div",{class:{"meal-cell-wrapper-bg":n&&!e.cateringFoodNumber(n.data.foods)},staticStyle:{cursor:"pointer"},on:{click:function(t){return e.openEditDialog(n)}}},[""!==n?t("div",{staticClass:"operate"},[e.cateringFoodNumber(n.data.foods)?t("span",{staticStyle:{"font-size":"12px"}},[e._v(" 已配餐（"+e._s(e.cateringFoodNumber(n.data.foods))+"） ")]):t("span",{staticClass:"copy"},[e._v("未配餐")]),e.cateringFoodNumber(n.data.foods)?t("span",{staticClass:"edit",on:{click:function(t){return t.stopPropagation(),e.openCopyDialog(n)}}},[e._v(" 复制 ")]):e._e()]):e._e(),e.isNutritionGuidance&&""!==n&&n.data.foods.length>0?t("div",{staticClass:"fee-wrapper"},[t("div",{staticClass:"header-wrapper",staticStyle:{padding:"0px"}},[t("div",{staticClass:"marker-wrapper marker-box"},e._l(e.markerList,(function(r){return t("div",{key:r.label},[t("span",{class:r.className,staticStyle:{width:"9px",height:"9px"}}),t("span",{staticStyle:{"margin-right":"10px"}},[e._v(e._s(r.label)+":"+e._s(n[r.key]))])])})),0)])]):e._e()])])})),0)]),t("el-dialog",{attrs:{title:"复制到",visible:e.showCopyDialog,"close-on-press-escape":!1,"close-on-click-modal":!1,"destroy-on-close":!0,width:"600px"},on:{"update:visible":function(t){e.showCopyDialog=t}}},[t("p",[t("span",{staticStyle:{"margin-right":"48px"}},[e._v("已选："+e._s(e.currentDate))])]),t("el-date-picker",{attrs:{type:"dates",placeholder:"选择日期",size:"small","default-value":e.dayValue,"picker-options":e.pickerOptions,"popper-class":"el-picker-box"},model:{value:e.copyDateVal,callback:function(t){e.copyDateVal=t},expression:"copyDateVal"}}),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{size:"small"},on:{click:function(t){e.showCopyDialog=!1}}},[e._v("取 消")]),t("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.handleCopy}},[e._v("确 定")])],1)],1),e.menuPreviewDialogVisible?t("menu-preview-dialog",{ref:"menuPreviewDialog",attrs:{isshow:e.menuPreviewDialogVisible,formDataDialog:e.dialogMenuPeviewInfo,width:"900px"},on:{"update:isshow":function(t){e.menuPreviewDialogVisible=t}}}):e._e(),e.selectModelDialogVisible?t("select-model-dialog",{ref:"selectModelDialog",attrs:{isshow:e.selectModelDialogVisible,formDataDialog:e.selectModelDialogInfo,width:"900px"},on:{"update:isshow":function(t){e.selectModelDialogVisible=t},clickSelect:e.clickSelect}}):e._e()],1)},a=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"meal-type"},[t("div")])}],i=n("5a0c"),o=n("663f"),u=n("b9c1"),c=n("ed08"),s=n("c9d9");function l(e){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},l(e)}function d(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */d=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,a=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,n){return e[t]=n}}function f(e,t,n,r){var i=t&&t.prototype instanceof b?t:b,o=Object.create(i.prototype),u=new O(r||[]);return a(o,"_invoke",{value:E(e,n,u)}),o}function h(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var m="suspendedStart",p="suspendedYield",y="executing",g="completed",v={};function b(){}function w(){}function k(){}var _={};s(_,o,(function(){return this}));var x=Object.getPrototypeOf,D=x&&x(x(P([])));D&&D!==n&&r.call(D,o)&&(_=D);var L=k.prototype=b.prototype=Object.create(_);function M(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function N(e,t){function n(a,i,o,u){var c=h(e[a],e,i);if("throw"!==c.type){var s=c.arg,d=s.value;return d&&"object"==l(d)&&r.call(d,"__await")?t.resolve(d.__await).then((function(e){n("next",e,o,u)}),(function(e){n("throw",e,o,u)})):t.resolve(d).then((function(e){s.value=e,o(s)}),(function(e){return n("throw",e,o,u)}))}u(c.arg)}var i;a(this,"_invoke",{value:function(e,r){function a(){return new t((function(t,a){n(e,r,t,a)}))}return i=i?i.then(a,a):a()}})}function E(t,n,r){var a=m;return function(i,o){if(a===y)throw Error("Generator is already running");if(a===g){if("throw"===i)throw o;return{value:e,done:!0}}for(r.method=i,r.arg=o;;){var u=r.delegate;if(u){var c=V(u,r);if(c){if(c===v)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===m)throw a=g,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=y;var s=h(t,n,r);if("normal"===s.type){if(a=r.done?g:p,s.arg===v)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(a=g,r.method="throw",r.arg=s.arg)}}}function V(t,n){var r=n.method,a=t.iterator[r];if(a===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,V(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var i=h(a,t.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,v;var o=i.arg;return o?o.done?(n[t.resultName]=o.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,v):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function C(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function S(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(C,this),this.reset(!0)}function P(t){if(t||""===t){var n=t[o];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,i=function n(){for(;++a<t.length;)if(r.call(t,a))return n.value=t[a],n.done=!1,n;return n.value=e,n.done=!0,n};return i.next=i}}throw new TypeError(l(t)+" is not iterable")}return w.prototype=k,a(L,"constructor",{value:k,configurable:!0}),a(k,"constructor",{value:w,configurable:!0}),w.displayName=s(k,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,k):(e.__proto__=k,s(e,c,"GeneratorFunction")),e.prototype=Object.create(L),e},t.awrap=function(e){return{__await:e}},M(N.prototype),s(N.prototype,u,(function(){return this})),t.AsyncIterator=N,t.async=function(e,n,r,a,i){void 0===i&&(i=Promise);var o=new N(f(e,n,r,a),i);return t.isGeneratorFunction(n)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},M(L),s(L,c,"Generator"),s(L,o,(function(){return this})),s(L,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=P,O.prototype={constructor:O,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(S),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function a(r,a){return u.type="throw",u.arg=t,n.next=r,a&&(n.method="next",n.arg=e),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],u=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var c=r.call(o,"catchLoc"),s=r.call(o,"finallyLoc");if(c&&s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),S(n),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var a=r.arg;S(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:P(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),v}},t}function f(e,t){return g(e)||y(e,t)||m(e,t)||h()}function h(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function m(e,t){if(e){if("string"==typeof e)return p(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?p(e,t):void 0}}function p(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function y(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i,o,u=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(u.push(r.value),u.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(s)throw a}}return u}}function g(e){if(Array.isArray(e))return e}function v(e,t,n,r,a,i,o){try{var u=e[i](o),c=u.value}catch(e){return void n(e)}u.done?t(c):Promise.resolve(c).then(r,a)}function b(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function o(e){v(i,r,a,o,u,"next",e)}function u(e){v(i,r,a,o,u,"throw",e)}o(void 0)}))}}var w={name:"AddMonthRecipes",components:{menuPreviewDialog:o["default"],selectModelDialog:u["default"]},props:{menuType:{type:String,default:""},menuId:{type:String,default:""}},data:function(){return{dayValue:"",pickerOptions:this.disabledDate(),isLoading:!1,currentDate:"",copyDateVal:[],showCopyDialog:!1,currentMonth:"",currentYear:"",border:"1px solid #ebeef5",dayList:[],weekDays:["星期一","星期二","星期三","星期四","星期五","星期六","星期日"],isNutritionGuidance:!1,markerList:[{key:"insufficientTotal",className:"marker-primary",label:"不足"},{key:"suitableTotal",className:"marker-secondary",label:"适宜"},{key:"overdoseTotal",className:"marker-thridary",label:"过量"}],menuPreviewDialogVisible:!1,dialogMenuPeviewInfo:{},selectModelDialogVisible:!1,selectModelDialogInfo:{}}},mounted:function(){this.initLoad()},methods:{disabledDate:function(){var e=this;return{disabledDate:function(t){var n=new Date(e.dayValue).getMonth(),r=new Date(t).getMonth();return n!==r}}},mealTypeName:function(e){var t="";return s["a"].forEach((function(n){n.value===e&&(t=n.label)})),t},initLoad:function(){this.initDayList()},clickMenuPreview:function(){this.menuPreviewDialogVisible=!0},switchGuidance:function(e){this.selectModelDialogInfo={menuType:this.menuType,menuId:this.menuId},this.isNutritionGuidance=!1,e?this.selectModelDialogVisible=!0:this.getModifyNutritionGuidance()},getModifyNutritionGuidance:function(){var e=this;return b(d().mark((function t(){var n,r,a,i,o;return d().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return n={id:e.menuId,operate:0,menu_type:e.menuType},e.isLoading=!0,t.next=4,Object(c["Z"])(e.$apis.apiBackgroundFoodMenuModifyNutritionGuidancePost(n));case 4:if(r=t.sent,a=f(r,2),i=a[0],o=a[1],e.isLoading=!1,!i){t.next=12;break}return e.$message.error(i.message),t.abrupt("return");case 12:0===o.code?(e.$message.success("关闭营养指导"),e.initLoad()):e.$message.error(o.msg);case 13:case"end":return t.stop()}}),t)})))()},initDayList:function(){var e=this;return b(d().mark((function t(){var n,r,a,o,u,s,l,h,m,p,y,g,v;return d().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(n=Number(e.$route.query.id),n){t.next=4;break}return e.$message.error("id获取失败"),t.abrupt("return");case 4:return e.isLoading=!0,t.next=7,Object(c["Z"])(e.$apis.apiBackgroundFoodMenuMonthlyMonthlyDetailPost({id:n}));case 7:if(r=t.sent,a=f(r,2),o=a[0],u=a[1],e.isLoading=!1,!o){t.next=15;break}return e.$message.error(o.message),t.abrupt("return");case 15:if(0!==u.code){t.next=38;break}for(s=Object.keys(u.data.daily_data),l=i(s[0]).daysInMonth(),e.currentMonth=i(s[0]).format("M"),e.currentYear=i(s[0]).format("YYYY")+"年"+i(s[0]).format("MM")+"月",sessionStorage.setItem("mealDailyData",JSON.stringify(u.data.daily_data)),h=[],m=s[0],p=i(i(m).startOf("month")).day(),0===p&&(p=7),e.isNutritionGuidance=u.data.is_nutrition_guidance,e.$emit("guidanceChange",e.isNutritionGuidance),y=1;y<p;y++)h.push("");g=d().mark((function e(){var t,n,r,a;return d().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t=u.data.daily_data[s[v-1]].foods,n=0,r=0,a=0,t.forEach((function(e){var t=e.total_nutrition;for(var i in t)t[i]>=120?a+=1:t[i]>=80?r+=1:t[i]<80&&(n+=1)})),h.push({dayNumber:v<10?"0".concat(v):v,data:u.data.daily_data[s[v-1]],insufficientTotal:n,suitableTotal:r,overdoseTotal:a,menu_date:s[v-1]});case 6:case"end":return e.stop()}}),e)})),v=1;case 30:if(!(v<=l)){t.next=35;break}return t.delegateYield(g(),"t0",32);case 32:v++,t.next=30;break;case 35:e.dayList=h,t.next=39;break;case 38:e.$message.error(u.msg);case 39:case"end":return t.stop()}}),t)})))()},openCopyDialog:function(e){if(""!==this.currentYear){var t=this.currentYear;t+="".concat(e.dayNumber,"日"),this.currentDate=t,this.copyDateVal=[],this.dayValue=e.menu_date}this.showCopyDialog=!0},openEditDialog:function(e){var t=e.menu_date;t=t.replace("年","-").replace("月","-"),this.$router.push({name:"MerchantAddMealMonthRecipes",query:{guidance:this.guidance,menuType:this.menuType,menuId:this.menuId,monthDate:t,isNutritionGuidance:this.isNutritionGuidance?"guidance":"false"}})},tabClick:function(e){this.foodMenuType=e},handleCopy:function(){var e=this;return b(d().mark((function t(){var n,r;return d().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return n={id:e.$route.query.id,copy_date:e.currentDate.replace("年","-").replace("月","-").replace("日",""),dates:e.copyDateVal.map((function(e){return i(e).format("YYYY-MM-DD")}))},t.next=3,e.$apis.apiBackgroundFoodMenuMonthlyCopyDayFoodPost(n);case 3:r=t.sent,0===r.code?(e.$message.success("操作成功"),e.showCopyDialog=!1,e.initDayList()):e.$message.error(r.msg);case 5:case"end":return t.stop()}}),t)})))()},clickSelect:function(e){this.selectModelDialogVisible=!1,this.initLoad()},cateringFoodNumber:function(e){var t=0,n=0;return e&&e.length&&e.forEach((function(e){t+=e.food_count,n+=e.set_meal_count})),t+n}}},k=w,_=(n("ccd9"),n("2877")),x=Object(_["a"])(k,r,a,!1,null,null,null);t["default"]=x.exports},c9d9:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"d",(function(){return o})),n.d(t,"b",(function(){return u})),n.d(t,"c",(function(){return c})),n.d(t,"e",(function(){return s})),n.d(t,"f",(function(){return l})),n.d(t,"g",(function(){return d}));var r=n("5a0c"),a=n("da92"),i=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],o=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],u={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},c=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],s=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],l=(r().subtract(7,"day").format("YYYY-MM-DD"),r().format("YYYY-MM-DD"),function(e){return e?"number"===typeof e?a["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:a["a"].divide(e,100).toFixed(2):"0.00"}),d=function(e){return a["a"].times(e,100)}},cc06:function(e,t,n){"use strict";function r(e,t){var n={},r=t.useKeyList&&Object.keys(t.useKeyList).length;return r&&Object.keys(t.useKeyList).forEach((function(r,i){t.useKeyList[r].forEach((function(t,i){n[t]={row:[],mergeNum:0,key:r},n=a(n,e,t,r)}))})),t.mergeKeyList&&t.mergeKeyList.forEach((function(t,r){n[t]={row:[],mergeNum:0},n=a(n,e,t)})),n}function a(e,t,n,r){return t.forEach((function(a,o){if(0===o)e[n].row.push(1),e[n].mergeNum=o;else{var u=r?a[r]===t[o-1][r]:!r,c=a[n]===t[o-1][n]&&u;if(c){var s=i(e[n].row);e[n].row[s]+=1,e[n].row.push(0),e[n].mergeNum=o}else e[n].row.push(1),e[n].mergeNum=o}})),e}function i(e){var t=e.length-1;while(t>0){if(e[t])break;t--}return t}function o(e,t,n,r){var a=e[t].row[n],i=a>0?1:0;return[a,i]}n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return o}))},ccd9:function(e,t,n){"use strict";n("22ec")}}]);