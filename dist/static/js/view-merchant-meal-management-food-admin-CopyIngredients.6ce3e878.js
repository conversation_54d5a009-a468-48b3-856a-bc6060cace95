(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-food-admin-CopyIngredients"],{"3fa5":function(t,e,r){"use strict";r.d(e,"a",(function(){return a}));r("9e1f"),r("450d");var n=r("6ed5"),o=r.n(n);function a(t,e){return new Promise((function(r,n){o.a.confirm(t.content?t.content:"",t.title?t.title:"提示",{dangerouslyUseHTMLString:!0|t.useHTML,distinguishCancelAndClose:!0,closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:t.cancel_class?t.cancel_class:"ps-cancel-btn",confirmButtonClass:t.confirm_class?t.confirm_class:"ps-btn",confirmButtonText:t.confirmButtonText,cancelButtonText:t.cancelButtonText,center:""===t.center||t.center}).then((function(t){e?r(e()):r()})).catch((function(t){n(t)}))}))}},f955:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"ingredients-wrapper container-wrapper"},[e("refresh-tool",{on:{refreshPage:t.refreshHandle}}),e("search-form",{ref:"searchRef",attrs:{"form-setting":t.searchFormSetting,"label-width":"110px"},on:{search:t.searchHandle}}),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("数据列表")]),e("div",{staticClass:"align-r"},[e("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(e){return t.copySystemIngredients("multi")}}},[t._v(" 批量复制 ")])],1)]),e("div",{staticClass:"table-content"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:"","header-row-class-name":"ps-table-header-row"},on:{"selection-change":t.handleSelectionChange}},[e("el-table-column",{attrs:{type:"selection",width:"50",align:"center","class-name":"ps-checkbox"}}),e("el-table-column",{attrs:{prop:"name",label:"食材名称",align:"center"}}),e("el-table-column",{attrs:{prop:"all_alias_name",label:"食材别名",align:"center",width:"120","show-overflow-tooltip":""}}),e("el-table-column",{attrs:{prop:"category_name",label:"一级分类",align:"center"}}),e("el-table-column",{attrs:{prop:"sort_name",label:"二级分类",align:"center"}}),e("el-table-column",{attrs:{prop:"is_copy",label:"是否已复制",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.is_copy?"是":"否")+" ")]}}])}),e("el-table-column",{attrs:{fixed:"right",label:"操作",width:"180",align:"center"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.copySystemIngredients("single",r.row.id)}}},[t._v(" 复制 ")])]}}])})],1)],1)]),e("dialog-message",{attrs:{width:"415px",top:"30vh",title:"复制成功",show:t.showDialog,center:""},on:{"update:show":function(e){t.showDialog=e}}},[e("div",{staticClass:"import-food m-b-50"},[t._v(" 前往添加菜品，可快速创建菜品库和食材库，选择系统菜品相关食材会自动创建。 ")]),e("div",{staticClass:"text-center",attrs:{slot:"tool"},slot:"tool"},[e("el-button",{staticClass:"ps-btn w-110",staticStyle:{"margin-right":"20px"},attrs:{type:"primary"},on:{click:t.clickConfirmHandle}},[t._v("添加菜品")]),e("el-button",{staticClass:"ps-cancel-btn w-110",on:{click:t.clickCancleHandle}},[t._v("我知道了")])],1)])],1)},o=[],a=r("ed08"),i=r("3fa5");function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function l(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach((function(e){u(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function u(t,e,r){return(e=f(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function f(t){var e=h(t,"string");return"symbol"==c(e)?e:e+""}function h(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function p(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */p=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",l=a.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var a=e&&e.prototype instanceof b?e:b,i=Object.create(a.prototype),c=new $(n||[]);return o(i,"_invoke",{value:k(t,r,c)}),i}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var d="suspendedStart",m="suspendedYield",g="executing",y="completed",v={};function b(){}function w(){}function _(){}var x={};u(x,i,(function(){return this}));var L=Object.getPrototypeOf,S=L&&L(L(T([])));S&&S!==r&&n.call(S,i)&&(x=S);var O=_.prototype=b.prototype=Object.create(x);function j(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function C(t,e){function r(o,a,i,s){var l=h(t[o],t,a);if("throw"!==l.type){var u=l.arg,f=u.value;return f&&"object"==c(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,i,s)}),(function(t){r("throw",t,i,s)})):e.resolve(f).then((function(t){u.value=t,i(u)}),(function(t){return r("throw",t,i,s)}))}s(l.arg)}var a;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return a=a?a.then(o,o):o()}})}function k(e,r,n){var o=d;return function(a,i){if(o===g)throw Error("Generator is already running");if(o===y){if("throw"===a)throw i;return{value:t,done:!0}}for(n.method=a,n.arg=i;;){var c=n.delegate;if(c){var s=E(c,n);if(s){if(s===v)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===d)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=g;var l=h(e,r,n);if("normal"===l.type){if(o=n.done?y:m,l.arg===v)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(o=y,n.method="throw",n.arg=l.arg)}}}function E(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,E(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var a=h(o,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,v;var i=a.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function I(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function P(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function $(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(I,this),this.reset(!0)}function T(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(c(e)+" is not iterable")}return w.prototype=_,o(O,"constructor",{value:_,configurable:!0}),o(_,"constructor",{value:w,configurable:!0}),w.displayName=u(_,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,u(t,l,"GeneratorFunction")),t.prototype=Object.create(O),t},e.awrap=function(t){return{__await:t}},j(C.prototype),u(C.prototype,s,(function(){return this})),e.AsyncIterator=C,e.async=function(t,r,n,o,a){void 0===a&&(a=Promise);var i=new C(f(t,r,n,o),a);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},j(O),u(O,l,"Generator"),u(O,i,(function(){return this})),u(O,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=T,$.prototype={constructor:$,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(P),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return c.type="throw",c.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],c=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var s=n.call(i,"catchLoc"),l=n.call(i,"finallyLoc");if(s&&l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=t,i.arg=e,a?(this.method="next",this.next=a.finallyLoc,v):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),P(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;P(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:T(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function d(t,e){return b(t)||v(t,e)||g(t,e)||m()}function m(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function g(t,e){if(t){if("string"==typeof t)return y(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?y(t,e):void 0}}function y(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function v(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,c=[],s=!0,l=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(c.push(n.value),c.length!==e);s=!0);}catch(t){l=!0,o=t}finally{try{if(!s&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(l)throw o}}return c}}function b(t){if(Array.isArray(t))return t}function w(t,e,r,n,o,a,i){try{var c=t[a](i),s=c.value}catch(t){return void r(t)}c.done?e(s):Promise.resolve(s).then(n,o)}function _(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var a=t.apply(e,r);function i(t){w(a,n,o,i,c,"next",t)}function c(t){w(a,n,o,i,c,"throw",t)}i(void 0)}))}}var x={name:"CopyIngredients",props:{},data:function(){return{isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[],searchFormSetting:{ingredient_name:{type:"input",label:"食材名称",value:"",placeholder:"请输入食材名称"},sort_id:{type:"treeselect",multiple:!0,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(t){return{id:t.level+"_"+t.id,label:t.name,children:t.sort_list}}},is_copy:{type:"select",label:"是否已复制",value:"all",placeholder:"请选择",collapseTags:!0,dataList:[{label:"全部",value:"all"},{label:"是",value:"finish_copy"},{label:"否",value:"not_copy"}]}},selectListId:[],copyModel:"",showDialog:!1}},created:function(){this.getCategoryCategoryNameList()},mounted:function(){},methods:{initLoad:function(){this.getSystemIngredientList()},searchHandle:Object(a["d"])((function(){this.getSystemIngredientList()}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.getSystemIngredientList()},getCategoryCategoryNameList:function(){var t=this;return _(p().mark((function e(){var r,n,o,i;return p().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(a["Z"])(t.$apis.apiBackgroundFoodIngredientSortCategoryNameListPost());case 2:if(r=e.sent,n=d(r,2),o=n[0],i=n[1],!o){e.next=9;break}return t.$message.error(o.message),e.abrupt("return");case 9:0===i.code?t.searchFormSetting.sort_id.dataList=t.deleteEmptyGroup(i.data):t.$message.error(i.msg);case 10:case"end":return e.stop()}}),e)})))()},deleteEmptyGroup:function(t){var e=this;function r(t){t.map((function(t){t.sort_list&&t.sort_list.length>0?r(t.sort_list):e.$delete(t,"sort_list")}))}return r(t),t},formatQueryParams:function(t){var e={},r=function(){if(t[n].value||t[n].value&&t[n].value.length)if("select_time"!==n)if("sort_id"===n){var r=[],o=[];t[n].value.map((function(t){"1"===t.split("_")[0]?r.push(Number(t.split("_")[1])):"2"===t.split("_")[0]&&o.push(Number(t.split("_")[1]))})),r.length&&(e.category_id=r),o.length&&(e.sort_id=o)}else e[n]=t[n].value;else t[n].value.length>0&&(e.start_time=t[n].value[0],e.end_time=t[n].value[1])};for(var n in t)r();return e},getSystemIngredientList:function(){var t=this;return _(p().mark((function e(){var r,n,o,i;return p().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(a["Z"])(t.$apis.apiBackgroundFoodIngredientSystemIngredientPost(l({},t.formatQueryParams(t.searchFormSetting))));case 3:if(r=e.sent,n=d(r,2),o=n[0],i=n[1],t.isLoading=!1,!o){e.next=12;break}return t.$message.error(o.message),t.tableData=[],e.abrupt("return");case 12:0===i.code?t.tableData=i.data.map((function(t){return null!==t.alias_name?t.all_alias_name=t.alias_name.join(","):t.alias_name=[],t})):(t.$message.error(i.msg),t.tableData=[]);case 13:case"end":return e.stop()}}),e)})))()},handleSelectionChange:function(t){var e=this;this.selectListId=[],t.map((function(t){e.selectListId.push(t.id)}))},copySystemIngredients:function(t,e,r){var n=this;return _(p().mark((function o(){var c,s,l,u,f,h;return p().wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(c=[],c="multi"===t?n.selectListId:[e],s={ids:c},r&&(s.copy_model=r),c.length){o.next=6;break}return o.abrupt("return",n.$message.error("请先选择复制的数据！"));case 6:if(!n.isLoading){o.next=8;break}return o.abrupt("return");case 8:return n.isLoading=!0,o.next=11,Object(a["Z"])(n.$apis.apiBackgroundFoodIngredientSystemIngredientCopyPost(s));case 11:if(l=o.sent,u=d(l,2),f=u[0],h=u[1],n.isLoading=!1,!f){o.next=19;break}return n.$message.error(f.message),o.abrupt("return");case 19:0===h.code?(n.selectListId=[],n.$message.success(h.msg),n.getSystemIngredientList(),n.$closeCurrentTab(n.$route.path)):2===h.code?Object(i["a"])({content:h.msg,confirmButtonText:"覆盖",cancelButtonText:"去重后保存"}).then((function(r){n.copySystemIngredients(t,e,"cover")})).catch((function(r){"cancel"===r&&n.copySystemIngredients(t,e,"deduplication")})):n.$message.error(h.msg);case 20:case"end":return o.stop()}}),o)})))()},clickConfirmHandle:function(t){var e=this;return _(p().mark((function t(){return p().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.clickCancleHandle(),t.next=3,e.$sleep(100);case 3:e.$router.push({name:"MerchantCopyFoods"});case 4:case"end":return t.stop()}}),t)})))()},clickCancleHandle:function(t){this.$closeCurrentTab(this.$route.path),this.showDialog=!1}}},L=x,S=r("2877"),O=Object(S["a"])(L,n,o,!1,null,null,null);e["default"]=O.exports}}]);