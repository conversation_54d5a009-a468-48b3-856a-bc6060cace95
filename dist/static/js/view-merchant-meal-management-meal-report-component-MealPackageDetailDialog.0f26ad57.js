(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-meal-report-component-MealPackageDetailDialog","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-meal-report-MealPackageRule"],{1713:function(e,t,n){},5304:function(e,t,n){"use strict";n("1713")},ba82:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"MealPackageDetailDialog"},[t("CustomDrawer",{attrs:{show:e.showDrawer,size:760,title:e.title,loading:e.isLoading,fixedFooter:"",confirmShow:!1,cancelText:"取消"},on:{"update:show":function(t){e.showDrawer=t},"update:loading":function(t){e.isLoading=t}}},[t("div",{staticClass:"meal-package-box m-r-20"},[t("ul",{staticClass:"tab-box"},e._l(e.talList,(function(n,a){return t("li",{class:["tab-item pointer",e.activeTab===n.value?"active":""],on:{click:function(t){return e.clickTabHandle(n)}}},[e._v(e._s(n.label))])})),0),t("div",{staticClass:"meal-package-content"},[t("el-scrollbar",{staticClass:"package-wrapper"},[e.packageList.length>0?t("ul",{staticClass:"content-box"},e._l(e.packageList,(function(n,a){return t("li",{staticClass:"content-item"},[t("div",{staticClass:"content-item-left"},[t("span",{staticClass:"status inline-block is-take m-r-10"},[e._v(e._s(n.take_meal_status_alias))]),t("span",{staticClass:"time inline-block m-r-10"},[e._v(e._s(n.report_date))]),t("span",{staticClass:"week inline-block m-r-10"},[e._v("星期"+e._s(e.parseTime(n.report_date,"{a}")))]),t("span",{staticClass:"meal inline-block m-r-10"},[e._v(e._s(n.meal_type_alias))])]),t("div",{staticClass:"content-refund m-r-20"},["no_take"===n.take_meal_status?t("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"mini"},on:{click:function(t){return e.clickRefundHandle(n)}}},[e._v("去退款")]):e._e()],1)])})),0):t("div",{},[e._v("暂无更多数据")])])],1),e.packageList.length>0?t("table-statistics",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoadingCollect,expression:"isLoadingCollect"}],staticClass:"m-t-20",attrs:{"element-loading-custom-class":"el-loading-wrapp","element-loading-spinner":"loading","element-loading-text":e.elementLoadingText,statistics:e.collect}}):e._e()],1)])],1)},r=[],i=n("c9d9"),o=n("ed08");function l(e){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},l(e)}function u(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */u=function(){return t};var e,t={},n=Object.prototype,a=n.hasOwnProperty,r=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function f(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{f({},"")}catch(e){f=function(e,t,n){return e[t]=n}}function d(e,t,n,a){var i=t&&t.prototype instanceof b?t:b,o=Object.create(i.prototype),l=new S(a||[]);return r(o,"_invoke",{value:E(e,n,l)}),o}function m(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var h="suspendedStart",y="suspendedYield",p="executing",g="completed",v={};function b(){}function _(){}function k(){}var w={};f(w,o,(function(){return this}));var x=Object.getPrototypeOf,L=x&&x(x(P([])));L&&L!==n&&a.call(L,o)&&(w=L);var D=k.prototype=b.prototype=Object.create(w);function O(e){["next","throw","return"].forEach((function(t){f(e,t,(function(e){return this._invoke(t,e)}))}))}function C(e,t){function n(r,i,o,u){var c=m(e[r],e,i);if("throw"!==c.type){var s=c.arg,f=s.value;return f&&"object"==l(f)&&a.call(f,"__await")?t.resolve(f.__await).then((function(e){n("next",e,o,u)}),(function(e){n("throw",e,o,u)})):t.resolve(f).then((function(e){s.value=e,o(s)}),(function(e){return n("throw",e,o,u)}))}u(c.arg)}var i;r(this,"_invoke",{value:function(e,a){function r(){return new t((function(t,r){n(e,a,t,r)}))}return i=i?i.then(r,r):r()}})}function E(t,n,a){var r=h;return function(i,o){if(r===p)throw Error("Generator is already running");if(r===g){if("throw"===i)throw o;return{value:e,done:!0}}for(a.method=i,a.arg=o;;){var l=a.delegate;if(l){var u=M(l,a);if(u){if(u===v)continue;return u}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(r===h)throw r=g,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);r=p;var c=m(t,n,a);if("normal"===c.type){if(r=a.done?g:y,c.arg===v)continue;return{value:c.arg,done:a.done}}"throw"===c.type&&(r=g,a.method="throw",a.arg=c.arg)}}}function M(t,n){var a=n.method,r=t.iterator[a];if(r===e)return n.delegate=null,"throw"===a&&t.iterator.return&&(n.method="return",n.arg=e,M(t,n),"throw"===n.method)||"return"!==a&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+a+"' method")),v;var i=m(r,t.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,v;var o=i.arg;return o?o.done?(n[t.resultName]=o.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,v):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function j(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function T(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function S(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(j,this),this.reset(!0)}function P(t){if(t||""===t){var n=t[o];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,i=function n(){for(;++r<t.length;)if(a.call(t,r))return n.value=t[r],n.done=!1,n;return n.value=e,n.done=!0,n};return i.next=i}}throw new TypeError(l(t)+" is not iterable")}return _.prototype=k,r(D,"constructor",{value:k,configurable:!0}),r(k,"constructor",{value:_,configurable:!0}),_.displayName=f(k,s,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===_||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,k):(e.__proto__=k,f(e,s,"GeneratorFunction")),e.prototype=Object.create(D),e},t.awrap=function(e){return{__await:e}},O(C.prototype),f(C.prototype,c,(function(){return this})),t.AsyncIterator=C,t.async=function(e,n,a,r,i){void 0===i&&(i=Promise);var o=new C(d(e,n,a,r),i);return t.isGeneratorFunction(n)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},O(D),f(D,s,"Generator"),f(D,o,(function(){return this})),f(D,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var a in t)n.push(a);return n.reverse(),function e(){for(;n.length;){var a=n.pop();if(a in t)return e.value=a,e.done=!1,e}return e.done=!0,e}},t.values=P,S.prototype={constructor:S,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(T),!t)for(var n in this)"t"===n.charAt(0)&&a.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function r(a,r){return l.type="throw",l.arg=t,n.next=a,r&&(n.method="next",n.arg=e),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],l=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var u=a.call(o,"catchLoc"),c=a.call(o,"finallyLoc");if(u&&c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(u){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&a.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),T(n),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var a=n.completion;if("throw"===a.type){var r=a.arg;T(n)}return r}}throw Error("illegal catch attempt")},delegateYield:function(t,n,a){return this.delegate={iterator:P(t),resultName:n,nextLoc:a},"next"===this.method&&(this.arg=e),v}},t}function c(e,t){return d(e)||f(e,t)||g(e,t)||s()}function s(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,r,i,o,l=[],u=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(a=i.call(n)).done)&&(l.push(a.value),l.length!==t);u=!0);}catch(e){c=!0,r=e}finally{try{if(!u&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw r}}return l}}function d(e){if(Array.isArray(e))return e}function m(e,t,n,a,r,i,o){try{var l=e[i](o),u=l.value}catch(e){return void n(e)}l.done?t(u):Promise.resolve(u).then(a,r)}function h(e){return function(){var t=this,n=arguments;return new Promise((function(a,r){var i=e.apply(t,n);function o(e){m(i,a,r,o,l,"next",e)}function l(e){m(i,a,r,o,l,"throw",e)}o(void 0)}))}}function y(e){return b(e)||v(e)||g(e)||p()}function p(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function g(e,t){if(e){if("string"==typeof e)return _(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_(e,t):void 0}}function v(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function b(e){if(Array.isArray(e))return _(e)}function _(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=Array(t);n<t;n++)a[n]=e[n];return a}var k={name:"MealPackageDetailDialog",props:{isshow:Boolean,loading:Boolean,type:{type:String,default:"add"},title:{type:String,default:"餐包使用情况"},infoData:{type:Object,default:function(){return{}}}},data:function(){return{isLoading:!1,activeTab:"all",talList:[{label:"全部",value:"all"}].concat(y(i["a"])),packageList:[],collect:[{key:"total",value:0,label:"已订餐：",dot:!0},{key:"take_out_total",value:0,label:"已取餐：",dot:!0},{key:"no_take_total",value:0,label:"未用餐：",dot:!0},{key:"refund_total",value:0,label:"已退款："}],elementLoadingText:"数据正在加载，请耐心等待...",isLoadingCollect:!1}},computed:{showDrawer:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},watch:{isshow:function(e){e&&(this.packageList=[],this.activeTab="all",this.getMealPackageDetail(),this.getMealPackageCountDetail())}},created:function(){},mounted:function(){},methods:{parseTime:o["M"],clickTabHandle:Object(o["d"])((function(e){this.activeTab=e.value,this.getMealPackageDetail()}),200),getMealPackageDetail:function(){var e=this;return h(u().mark((function t(){var n,a,r,i,l;return u().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,n={page:1,page_size:999999,unified_trade_no:e.infoData.trade_no,date_type:"pay_time",start_date:Object(o["M"])(e.infoData.pay_time,"{y}-{m}-{d}"),end_date:Object(o["M"])(new Date(Object(o["M"])(e.infoData.pay_time,"{y}/{m}/{d}")).getTime()+864e5,"{y}-{m}-{d}")},"all"!==e.activeTab&&(n.meal_type_list=[e.activeTab]),t.next=5,e.$to(e.$apis.apiBackgroundOrderOrderPaymentListPost(n));case 5:if(a=t.sent,r=c(a,2),i=r[0],l=r[1],e.isLoading=!1,!i){t.next=12;break}return t.abrupt("return",e.$message.error(i.message));case 12:0===l.code?e.packageList=l.data.results:e.$message.error(l.msg);case 13:case"end":return t.stop()}}),t)})))()},getMealPackageCountDetail:function(){var e=this;return h(u().mark((function t(){var n,a,r,i;return u().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$to(e.$apis.apiBackgroundOrderOrderReportMealOrderReportMealPackDetailsCountPost({unified_trade_no:e.infoData.trade_no}));case 2:if(n=t.sent,a=c(n,2),r=a[0],i=a[1],!r){t.next=8;break}return t.abrupt("return",e.$message.error(r.message));case 8:0===i.code?e.collect.forEach((function(e){for(var t in i.data)e.key===t&&(e.value=i.data[t])})):e.$message.error(i.msg);case 9:case"end":return t.stop()}}),t)})))()},clickRefundHandle:function(e){var t={tab:1,queryData:{trade_no:e.trade_no,date_type:"pay_time",select_time:[Object(o["M"])(this.infoData.pay_time,"{y}-{m}-{d}"),Object(o["M"])(new Date(Object(o["M"])(this.infoData.pay_time,"{y}/{m}/{d}")).getTime()+864e5,"{y}-{m}-{d}")]}};"all"!==this.activeTab&&(t.queryData.meal_type_list=[this.activeTab]),t.queryData=JSON.stringify(t.queryData),this.$router.push({name:"MerchantConsumption",query:t})}}},w=k,x=(n("5304"),n("2877")),L=Object(x["a"])(w,a,r,!1,null,"3b5d8dc5",null);t["default"]=L.exports},c9d9:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"d",(function(){return o})),n.d(t,"b",(function(){return l})),n.d(t,"c",(function(){return u})),n.d(t,"e",(function(){return c})),n.d(t,"f",(function(){return s})),n.d(t,"g",(function(){return f}));var a=n("5a0c"),r=n("da92"),i=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],o=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],l={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},u=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],c=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],s=(a().subtract(7,"day").format("YYYY-MM-DD"),a().format("YYYY-MM-DD"),function(e){return e?"number"===typeof e?r["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:r["a"].divide(e,100).toFixed(2):"0.00"}),f=function(e){return r["a"].times(e,100)}}}]);