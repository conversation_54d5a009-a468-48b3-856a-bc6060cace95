(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-supplier-admin-AddEditSupplier","view-merchant-consumption-rules-service-admin-AddChargeServiceRule","view-merchant-meal-management-components-mealFoodList-FoodDiscountDialog"],{"0166":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"add_edit_supplier container-wrapper"},[e("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formIngredients",attrs:{rules:t.formRuls,model:t.formData,size:"small","label-width":"130px"}},[e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("基本信息")])]),e("div",{staticStyle:{padding:"0 20px"}},[e("el-form-item",{attrs:{label:"供应商名称",prop:"name"}},[e("el-input",{staticClass:"ps-input p-r-48",staticStyle:{width:"240px"},attrs:{placeholder:"请输入供应商名称",maxlength:"30","show-word-limit":""},model:{value:t.formData.name,callback:function(e){t.$set(t.formData,"name",e)},expression:"formData.name"}})],1),e("el-form-item",{attrs:{label:"工商营业执照（社会统一信用代码)",prop:"credit_code","label-width":"140px"}},[e("el-input",{staticClass:"ps-input p-r-48",staticStyle:{width:"240px"},attrs:{placeholder:"请输入社会统一信用代码",maxlength:"30","show-word-limit":""},model:{value:t.formData.credit_code,callback:function(e){t.$set(t.formData,"credit_code",e)},expression:"formData.credit_code"}})],1),e("el-form-item",{attrs:{label:"税务登记证",prop:"tax_registration_license"}},[e("el-upload",{ref:"uploadRegistration",staticClass:"avatar-uploader",attrs:{data:t.uploadParams,action:t.actionUrl,"show-file-list":!1,"on-success":t.handleAvatarSuccessRegistration,"before-upload":t.beforeAvatarUpload}},[t.formData.tax_registration_license?e("img",{staticClass:"avatar",attrs:{src:t.formData.tax_registration_license}}):e("i",{staticClass:"el-icon-plus avatar-uploader-icon"}),e("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v(" 仅支持上传jpg/png/bmp格式，图片大小且不超过5M ")])])],1),e("el-form-item",{attrs:{label:"食品经营许可证",prop:"business_license"}},[e("el-upload",{ref:"uploadBusiness",staticClass:"avatar-uploader",attrs:{data:t.uploadParams,action:t.actionUrl,"show-file-list":!1,"on-success":t.handleAvatarSuccessBusiness,"before-upload":t.beforeAvatarUpload}},[t.formData.business_license?e("img",{staticClass:"avatar",attrs:{src:t.formData.business_license}}):e("i",{staticClass:"el-icon-plus avatar-uploader-icon"}),e("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v(" 仅支持上传jpg/png/bmp格式，图片大小且不超过5M ")])])],1),e("el-form-item",{attrs:{label:"食品流通许可证",prop:"circulation_license"}},[e("el-upload",{ref:"uploadCirculation",staticClass:"avatar-uploader",attrs:{data:t.uploadParams,action:t.actionUrl,"show-file-list":!1,"on-success":t.handleAvatarSuccessCirculation,"before-upload":t.beforeAvatarUpload}},[t.formData.circulation_license?e("img",{staticClass:"avatar",attrs:{src:t.formData.circulation_license}}):e("i",{staticClass:"el-icon-plus avatar-uploader-icon"}),e("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v(" 仅支持上传jpg/png/bmp格式，图片大小且不超过5M ")])])],1),e("el-form-item",{attrs:{label:"联系人",prop:"person_name"}},[e("el-input",{staticClass:"ps-input p-r-48",staticStyle:{width:"240px"},attrs:{placeholder:"请输入联系人",maxlength:"30","show-word-limit":""},model:{value:t.formData.person_name,callback:function(e){t.$set(t.formData,"person_name",e)},expression:"formData.person_name"}})],1),e("el-form-item",{attrs:{label:"联系电话",prop:"phone"}},[e("el-input",{staticClass:"ps-input p-r-48",staticStyle:{width:"240px"},attrs:{placeholder:"请输入联系电话",maxlength:"11","show-word-limit":""},model:{value:t.formData.phone,callback:function(e){t.$set(t.formData,"phone",e)},expression:"formData.phone"}})],1),e("el-form-item",{attrs:{label:"供应商地址",prop:"address"}},[e("el-input",{staticClass:"ps-input p-r-48",staticStyle:{width:"240px"},attrs:{placeholder:"请输入供应商地址",maxlength:"30","show-word-limit":""},model:{value:t.formData.address,callback:function(e){t.$set(t.formData,"address",e)},expression:"formData.address"}})],1)],1)]),e("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[e("el-button",{staticStyle:{width:"120px"},on:{click:t.closeHandler}},[t._v("取消")]),e("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:t.submitHandler}},[t._v(" "+t._s("add"===t.type?"添加":"保存")+" ")])],1)])],1)},a=[],i=r("ed08"),o=r("d0dd");function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function c(t,e){return p(t)||f(t,e)||u(t,e)||l()}function l(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(t,e){if(t){if("string"==typeof t)return d(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?d(t,e):void 0}}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function f(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,i,o,s=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(s.push(n.value),s.length!==e);c=!0);}catch(t){l=!0,a=t}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw a}}return s}}function p(t){if(Array.isArray(t))return t}function h(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */h=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function d(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,o=Object.create(i.prototype),s=new O(n||[]);return a(o,"_invoke",{value:k(t,r,s)}),o}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=d;var p="suspendedStart",m="suspendedYield",g="executing",v="completed",y={};function b(){}function w(){}function _(){}var x={};u(x,o,(function(){return this}));var S=Object.getPrototypeOf,$=S&&S(S(P([])));$&&$!==r&&n.call($,o)&&(x=$);var E=_.prototype=b.prototype=Object.create(x);function L(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function C(t,e){function r(a,i,o,c){var l=f(t[a],t,i);if("throw"!==l.type){var u=l.arg,d=u.value;return d&&"object"==s(d)&&n.call(d,"__await")?e.resolve(d.__await).then((function(t){r("next",t,o,c)}),(function(t){r("throw",t,o,c)})):e.resolve(d).then((function(t){u.value=t,o(u)}),(function(t){return r("throw",t,o,c)}))}c(l.arg)}var i;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return i=i?i.then(a,a):a()}})}function k(e,r,n){var a=p;return function(i,o){if(a===g)throw Error("Generator is already running");if(a===v){if("throw"===i)throw o;return{value:t,done:!0}}for(n.method=i,n.arg=o;;){var s=n.delegate;if(s){var c=D(s,n);if(c){if(c===y)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===p)throw a=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=g;var l=f(e,r,n);if("normal"===l.type){if(a=n.done?v:m,l.arg===y)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(a=v,n.method="throw",n.arg=l.arg)}}}function D(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,D(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var i=f(a,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,y;var o=i.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function j(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function A(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function O(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(j,this),this.reset(!0)}function P(e){if(e||""===e){var r=e[o];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,i=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(s(e)+" is not iterable")}return w.prototype=_,a(E,"constructor",{value:_,configurable:!0}),a(_,"constructor",{value:w,configurable:!0}),w.displayName=u(_,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,u(t,l,"GeneratorFunction")),t.prototype=Object.create(E),t},e.awrap=function(t){return{__await:t}},L(C.prototype),u(C.prototype,c,(function(){return this})),e.AsyncIterator=C,e.async=function(t,r,n,a,i){void 0===i&&(i=Promise);var o=new C(d(t,r,n,a),i);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},L(E),u(E,l,"Generator"),u(E,o,(function(){return this})),u(E,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=P,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(A),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return s.type="throw",s.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var c=n.call(o,"catchLoc"),l=n.call(o,"finallyLoc");if(c&&l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),A(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;A(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:P(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),y}},e}function m(t,e,r,n,a,i,o){try{var s=t[i](o),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,a)}function g(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var i=t.apply(e,r);function o(t){m(i,n,a,o,s,"next",t)}function s(t){m(i,n,a,o,s,"throw",t)}o(void 0)}))}}var v={name:"SuperAddEditArticle",data:function(){return{isLoading:!1,type:"add",formData:{name:"",credit_code:"",tax_registration_license:"",business_license:"",circulation_license:"",person_name:"",phone:"",address:""},formRuls:{name:[{required:!0,message:"请输入供应商名称",trigger:"blur"}],credit_code:[{required:!0,message:"请输入工商营业执照（社会统一信用代码)",trigger:"blur"}],tax_registration_license:[{required:!0,message:"请上传税务登记证",trigger:"blur"}],business_license:[{required:!0,message:"请上传食品经营许可证",trigger:"blur"}],circulation_license:[{required:!0,message:"请上传食品流通许可证",trigger:"blur"}],person_name:[{required:!0,message:"请输入联系人",trigger:"blur"}],phone:[{required:!0,message:"手机号不能为空",trigger:"blur"},{validator:o["g"],trigger:"change"}],address:[{required:!0,message:"请输入供应商地址",trigger:"blur"}]},tagsList:[],actionUrl:"",uploadParams:{}}},created:function(){this.getUploadToken(),this.type=this.$route.query.type,this.initLoad()},mounted:function(){},methods:{initLoad:function(){if("modify"===this.type){var t=this.$decodeQuery(this.$route.query.data);this.formData={id:t.id,name:t.name,credit_code:t.credit_code,tax_registration_license:t.tax_registration_license,business_license:t.business_license,circulation_license:t.circulation_license,person_name:t.person_name,phone:t.phone,address:t.address}}},searchHandle:Object(i["d"])((function(){this.currentPage=1}),300),getUploadToken:function(){var t=this;return g(h().mark((function e(){var r;return h().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.getUploadToken({prefix:"jpeg/png"});case 2:r=e.sent,0===r.code?(t.actionUrl=r.data.host,t.uploadParams={key:r.data.prefix+(new Date).getTime()+Math.floor(150*Math.random()),prefix:r.data.prefix,policy:r.data.policy,OSSAccessKeyId:r.data.accessid,signature:r.data.signature,callback:r.data.callback,success_action_status:"200"}):t.$message.error(r.msg);case 4:case"end":return e.stop()}}),e)})))()},handleAvatarSuccessRegistration:function(t,e){0===t.code?(this.$refs.uploadRegistration.clearFiles(),this.formData.tax_registration_license=t.data.public_url,this.getUploadToken()):this.$message.error(t.msg)},handleAvatarSuccessBusiness:function(t,e){0===t.code?(this.$refs.uploadBusiness.clearFiles(),this.formData.business_license=t.data.public_url,this.getUploadToken()):this.$message.error(t.msg)},handleAvatarSuccessCirculation:function(t,e){0===t.code?(this.$refs.uploadCirculation.clearFiles(),this.formData.circulation_license=t.data.public_url,this.getUploadToken()):this.$message.error(t.msg)},beforeAvatarUpload:function(t){var e="image/jpeg"===t.type||"image/png"===t.type||"image/bmp"===t.type,r=t.size/1024/1024<5;return e||this.$message.error("上传头像图片只能是 JPG PNG BMP 格式!"),r||this.$message.error("上传头像图片大小不能超过 5MB!"),e&&r},addModifySupplier:function(t){var e=this;return g(h().mark((function r(){var n,a,o,s,l,u,d,f;return h().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(e.isLoading=!0,n="",a=c(n,2),o=a[0],s=a[1],"add"!==e.type){r.next=12;break}return r.next=6,Object(i["Z"])(e.$apis.apiBackgroundFoodIngredientSupplierAddPost(t));case 6:l=r.sent,u=c(l,2),o=u[0],s=u[1],r.next=19;break;case 12:return r.next=15,Object(i["Z"])(e.$apis.apiBackgroundFoodIngredientSupplierModifyPost(t));case 15:d=r.sent,f=c(d,2),o=f[0],s=f[1];case 19:if(e.isLoading=!1,!o){r.next=23;break}return e.$message.error(o.message),r.abrupt("return");case 23:0===s.code?(e.$message.success(s.msg),e.$closeCurrentTab(e.$route.path)):e.$message.error(s.msg);case 24:case"end":return r.stop()}}),r)})))()},submitHandler:function(){var t=this;this.$refs.formIngredients.validate((function(e){if(e){if(t.isLoading)return t.$message.error("请勿重复提交！");t.addModifySupplier(t.formData)}}))},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,r,n){"confirm"===e?t.$closeCurrentTab(t.$route.path):r.confirmButtonLoading||n()}}).then((function(t){})).catch((function(t){}))}}},y=v,b=(r("8e96"),r("2877")),w=Object(b["a"])(y,n,a,!1,null,null,null);e["default"]=w.exports},"6fef":function(t,e,r){},"8e96":function(t,e,r){"use strict";r("6fef")},d0dd:function(t,e,r){"use strict";r.d(e,"a",(function(){return n})),r.d(e,"b",(function(){return a})),r.d(e,"g",(function(){return i})),r.d(e,"c",(function(){return o})),r.d(e,"f",(function(){return s})),r.d(e,"d",(function(){return c})),r.d(e,"e",(function(){return l}));var n=function(t,e,r){if(e){var n=/^-?(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;n.test(e)?r():r(new Error("金额格式有误"))}else r(new Error("请输入金额"))},a=function(t,e,r){if(e){var n=/^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;n.test(e)?r():r(new Error("金额格式有误"))}else r()},i=function(t,e,r){if(!e)return r(new Error("手机号不能为空"));var n=/^1[3456789]\d{9}$/;n.test(e)?r():r(new Error("请输入正确手机号"))},o=function(t,e,r){if(!e)return r(new Error("金额有误"));var n=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;n.test(e)?r():r(new Error("金额格式有误"))},s=function(t,e,r){if(""===e)return r(new Error("不能为空"));var n=/^\d+$/;n.test(e)?r():r(new Error("请输入正确数字"))},c=function(t,e,r){if(""!==e){var n=/^(\+|-)?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;n.test(e)?r():r(new Error("金额格式有误"))}else r(new Error("请输入金额"))},l=function(t,e,r){var n=/^[\u4E00-\u9FA5\w-]+$/;n.test(e)?r():r(new Error("格式不正确，不能包含特殊字符"))}}}]);