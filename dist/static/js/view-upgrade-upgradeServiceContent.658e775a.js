(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-upgrade-upgradeServiceContent","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-meal-report-MealPackageRule"],{a6fa:function(e,t,n){"use strict";n("c9a5")},b735:function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"upgrade-service-content"},[t("div",{staticClass:"upgrade-service-content-header"},[t("div",{staticClass:"header-left"},[t("div",{staticClass:"w-350 ps-flex-align-c flex-align-c"},[t("span",{staticClass:"m-r-100 f-w-700 font-size-24"},[e._v(e._s(e.companyInfo.company_name))]),t("span",{staticClass:"tips"},[e._v(e._s(e.companyInfo.toll_rule_info?e.companyInfo.toll_rule_info.toll_version_name:""))])]),t("span",[e._v("到期时间："+e._s(e.timeFormat(e.companyInfo.service_end_time)))])]),t("div",{staticClass:"header-right"},[t("div",[t("el-statistic",{class:[e.companyInfo.due_day_num<=30?"red":""],attrs:{title:"剩余服务天数","value-style":e.companyInfo.due_day_num<=30?e.fontStyle:{}}},[t("template",{slot:"formatter"},[e._v(" "+e._s(e.companyInfo.due_day_num)+"天 ")])],2)],1),1===e.serviceType?t("div",[t("el-statistic",{attrs:{title:"用户规模使用情况"}},[t("template",{slot:"formatter"},[e._v(" "+e._s(e.companyInfo.use_user_count)+"人/"+e._s(e.companyInfo.user_scale)+"人 ")])],2)],1):e._e()])]),t("div",{staticClass:"upgrade-service-content-content"},[e._m(0),1===e.serviceType&&"expansion"===e.chargeTypeRadioNum?t("div",{staticClass:"userSize"},[t("div",{staticClass:"userSize-header"},[t("h3",[e._v("用户规模")]),"expansion"===e.chargeTypeRadioNum?t("span",{staticClass:"m-l-20 font-size-14"},[e._v("购买后，用户上限为"+e._s(e.maximum||" -- ")+"人")]):e._e()]),t("div",[t("span",{staticClass:"m-r-20 font-size-14"},[e._v("新增用户上限")]),t("el-input-number",{attrs:{step:500,min:1,max:99999},on:{change:e.handleChange},model:{value:e.userSize,callback:function(t){e.userSize=t},expression:"userSize"}})],1)]):e._e(),"renew"===e.chargeTypeRadioNum?t("div",{staticClass:"userSize"},[e._m(1),t("div",[t("span",{staticClass:"m-r-20 font-size-14"},[e._v("现有用户规模: "+e._s(e.companyInfo.user_scale)+" 人")])])]):e._e(),"renew"===e.chargeTypeRadioNum?t("div",{staticClass:"renew"},[t("div",{staticClass:"renew-header"},[t("h3",[e._v("续费")]),t("span",{staticClass:"m-l-20 font-size-14"},[e._v("服务时间："+e._s(e.timeFormat(e.serviceTime.service_start_time))+" 至 "+e._s(e.timeFormat(e.serviceTime.service_end_time)))])]),t("div",{staticClass:"renew-content"},[t("div",{class:["renew-content-item",1===e.chooseRenew?"isClick":""],on:{click:function(t){e.renew(1,1===e.serviceType?e.price(e.companyInfo.toll_rule_info.fee):e.price(e.companyInfo.renew_fee_list[0]))}}},[t("div",[e._m(2),t("div",{staticClass:"m-t-5"},[t("span",{staticClass:"renew-content-item-price"},[e._v(e._s(1===e.serviceType?"￥"+e.price(e.companyInfo.toll_rule_info.fee):"￥"+e.price(e.companyInfo.renew_fee_list[0])))]),1===e.serviceType?t("span",{staticStyle:{"font-size":"14px"}},[e._v("/人/年")]):e._e()])])]),e.companyInfo.toll_rule_info&&e.companyInfo.toll_rule_info.second_discount||2===e.serviceType?t("div",{class:["renew-content-item",2===e.chooseRenew?"isClick":""],on:{click:function(t){e.renew(2,1===e.serviceType?e.price(e.companyInfo.toll_rule_info.fee*e.price(e.companyInfo.toll_rule_info.second_discount)):e.price(e.companyInfo.renew_fee_list[1]))}}},[1===e.serviceType?t("div",{staticClass:"renew-content-item-tip"},[e._v(" "+e._s(e.discount(e.companyInfo.toll_rule_info.second_discount))+"折 ")]):e._e(),t("div",[e._m(3),t("div",{staticClass:"m-t-5"},[t("span",{staticClass:"renew-content-item-price"},[e._v(" "+e._s(1===e.serviceType?"≈￥"+e.price(e.companyInfo.toll_rule_info.fee*e.price(e.companyInfo.toll_rule_info.second_discount)):"￥"+e.price(e.companyInfo.renew_fee_list[1]))+" ")]),1===e.serviceType?t("span",{staticStyle:{"font-size":"14px"}},[e._v("/人/年")]):e._e()])]),1===e.serviceType?t("span",{staticClass:"renew-content-item-bottom"},[e._v(e._s(e.companyInfo.toll_rule_info.alias))]):e._e()]):e._e(),e.companyInfo.toll_rule_info&&e.companyInfo.toll_rule_info.third_discount||2===e.serviceType?t("div",{class:["renew-content-item",3===e.chooseRenew?"isClick":""],on:{click:function(t){e.renew(3,1===e.serviceType?e.price(e.companyInfo.toll_rule_info.fee*e.price(e.companyInfo.toll_rule_info.third_discount)):e.price(e.companyInfo.renew_fee_list[2]))}}},[1===e.serviceType?t("div",{staticClass:"renew-content-item-tip"},[e._v(" "+e._s(e.discount(e.companyInfo.toll_rule_info.third_discount))+"折 ")]):e._e(),t("div",[e._m(4),t("div",{staticClass:"m-t-5"},[t("span",{staticClass:"renew-content-item-price"},[e._v(" "+e._s(1===e.serviceType?"≈￥"+e.price(e.companyInfo.toll_rule_info.fee*e.price(e.companyInfo.toll_rule_info.third_discount)):"￥"+e.price(e.companyInfo.renew_fee_list[2]))+" ")]),1===e.serviceType?t("span",{staticStyle:{"font-size":"14px"}},[e._v("/人/年")]):e._e()])]),1===e.serviceType?t("span",{staticClass:"renew-content-item-bottom"},[e._v(e._s(e.companyInfo.toll_rule_info.alias))]):e._e()]):e._e(),e.companyInfo.toll_rule_info&&e.companyInfo.toll_rule_info.fourth_discount&&2!==e.serviceType?t("div",{class:["renew-content-item",4===e.chooseRenew?"isClick":""],on:{click:function(t){e.renew(4,e.price(e.companyInfo.toll_rule_info.fee*e.price(e.companyInfo.toll_rule_info.fourth_discount)))}}},[1===e.serviceType?t("div",{staticClass:"renew-content-item-tip"},[e._v(" "+e._s(e.discount(e.companyInfo.toll_rule_info.fourth_discount))+"折 ")]):e._e(),t("div",[e._m(5),t("div",{staticClass:"m-t-5"},[t("span",{staticClass:"renew-content-item-price"},[e._v(" "+e._s("≈￥"+e.price(e.companyInfo.toll_rule_info.fee*e.price(e.companyInfo.toll_rule_info.fourth_discount)))+" ")]),1===e.serviceType?t("span",{staticStyle:{"font-size":"14px"}},[e._v("/人/年")]):e._e()])]),1===e.serviceType?t("span",{staticClass:"renew-content-item-bottom"},[e._v(e._s(e.companyInfo.toll_rule_info.alias))]):e._e()]):e._e(),e.companyInfo.toll_rule_info&&e.companyInfo.toll_rule_info.fifth_discount&&2!==e.serviceType?t("div",{class:["renew-content-item",5===e.chooseRenew?"isClick":""],on:{click:function(t){e.renew(5,e.price(e.companyInfo.toll_rule_info.fee*e.price(e.companyInfo.toll_rule_info.fifth_discount)))}}},[1===e.serviceType?t("div",{staticClass:"renew-content-item-tip"},[e._v(" "+e._s(e.discount(e.companyInfo.toll_rule_info.fifth_discount))+"折 ")]):e._e(),t("div",[e._m(6),t("div",{staticClass:"m-t-5"},[t("span",{staticClass:"renew-content-item-price"},[e._v(" "+e._s("≈￥"+e.price(e.companyInfo.toll_rule_info.fee*e.price(e.companyInfo.toll_rule_info.fifth_discount)))+" ")]),1===e.serviceType?t("span",{staticStyle:{"font-size":"14px"}},[e._v("/人/年")]):e._e()])]),1===e.serviceType?t("span",{staticClass:"renew-content-item-bottom"},[e._v(e._s(e.companyInfo.toll_rule_info.alias))]):e._e()]):e._e()])]):e._e(),t("div",{staticClass:"payWay"},[e._m(7),t("div",{staticClass:"payWay-content"},e._l(e.payWayList,(function(n,i){return t("div",{key:i,class:["payWay-content-item","m-r-20",e.clickNum===i?"isClick":""],on:{click:function(t){return e.selectThis(n)}}},[t("img",{attrs:{src:n.imgUrl}}),t("span",{staticClass:"m-l-16"},[e._v(e._s(n.text))])])})),0)])]),t("div",{staticClass:"upgrade-service-content-footer"},[t("div",{staticClass:"footer-top"},[t("div",{staticClass:"footer-top-left"},[t("span",{staticClass:"font-size-16"},[e._v("实付金额：")]),t("span",{staticClass:"m-l-10 font-size-28 priceShow"},[e._v("￥ "+e._s(e.totalPrice))])]),t("div",{staticClass:"footer-top-right"},[t("span",{staticClass:"font-size-14"},[e._v("付款完成后可申请发票")]),t("div",{staticClass:"buyNow m-l-20",on:{click:e.payNow}},[e._v("立即购买")])])]),t("div",{staticClass:"m-t-10"},[t("el-checkbox",{model:{value:e.isRead,callback:function(t){e.isRead=t},expression:"isRead"}}),t("span",{staticClass:"checkbox-label",style:e.isRead?{color:"#FF9B45"}:{}},[e._v(" 我已认真阅读 ")]),e._l(e.agreementList,(function(n,i){return t("span",{key:i,staticStyle:{color:"#2694F1","text-decoration":"underline","font-size":"14px"},on:{click:function(t){return e.gotoTermsOfService(n)}}},[e._v(" 《"+e._s(n.agreement_type_alias)+"》 ")])}))],2)])])},a=[function(){var e=this,t=e._self._c;return t("span",{staticClass:"font-size-14"},[e._v("如购买服务不满足需求，或需要系统升级，请联系客服："),t("span",{staticStyle:{color:"#2694F1","text-decoration":"underline"}},[e._v("4008082098")])])},function(){var e=this,t=e._self._c;return t("div",{staticClass:"userSize-header"},[t("h3",[e._v("用户规模")])])},function(){var e=this,t=e._self._c;return t("span",[t("span",{staticClass:"renew-content-item-title"},[e._v("1")]),e._v("年")])},function(){var e=this,t=e._self._c;return t("span",[t("span",{staticClass:"renew-content-item-title"},[e._v("2")]),e._v("年")])},function(){var e=this,t=e._self._c;return t("span",[t("span",{staticClass:"renew-content-item-title"},[e._v("3")]),e._v("年")])},function(){var e=this,t=e._self._c;return t("span",[t("span",{staticClass:"renew-content-item-title"},[e._v("4")]),e._v("年")])},function(){var e=this,t=e._self._c;return t("span",[t("span",{staticClass:"renew-content-item-title"},[e._v("5")]),e._v("年")])},function(){var e=this,t=e._self._c;return t("div",[t("h3",[e._v("支付方式")])])}],s=n("5a0c"),o=n("c9d9"),c=n("da92"),r={name:"upgradeServiceContent",props:{companyInfo:{type:Object,default:function(){return{}}},serviceType:{type:[Number,String],default:""},chargeTypeRadioNum:{type:String,default:""}},computed:{timeFormat:function(){return function(e){return s(e).format("YYYY-MM-DD")}},discount:function(){return function(e){if(e)return c["a"].divide(e,10).toFixed(1)}},price:function(){return function(e){if(e)return Object(o["f"])(e)}},maximum:function(){return this.companyInfo.user_scale+this.userSize}},watch:{chargeTypeRadioNum:function(e,t){e!==t&&(this.isRead=!1),"renew"===e&&(this.isRead=!0),this.totalPrice=0,this.clickNum=4},companyInfo:{handler:function(e){e&&(this.serviceTime.service_start_time=e.service_start_time,this.serviceTime.service_end_time=e.service_end_time)},immediate:!0}},data:function(){return{userSize:100,payWayList:[{id:0,imgUrl:n("b4df"),value:"wxpay",text:"微信支付"},{id:1,imgUrl:n("5559"),value:"alipay",text:"支付宝"},{id:2,imgUrl:n("6558"),value:"transfer",text:"对公转账"}],clickNum:4,payMethod:"",chooseRenew:0,renewForPersonAYear:0,totalPrice:0,agreementList:[{agreement_type_alias:"服务条款",agreement_type:"TS"}],isRead:!1,fontStyle:{color:"#fd594e"},serviceTime:{service_start_time:"",service_end_time:""}}},methods:{handleChange:function(){if(!this.userSize)return this.$message.error("用户上限不能为空");if(0===this.companyInfo.due_day_num&&2===this.serviceType)return this.$message.error("目前服务天数为0，请先续费");var e=this.setParams();this.calculatePrice(e)},selectThis:function(e){if(0===this.companyInfo.due_day_num&&2===this.serviceType)return this.$message.error("目前服务天数为0，请先续费");this.clickNum=e.id,this.payMethod=e.value,4!==this.clickNum&&this.handleChange()},renew:function(e,t){this.chooseRenew=e,this.renewForPersonAYear=t,this.serviceTime.service_end_time=s(this.companyInfo.service_end_time).add(e,"year").format("YYYY-MM-DD"),this.handleChange()},payNow:function(){var e=this;if(!this.isRead)return this.$message.error("请阅读服务条款并勾选已读后重试");if(4===this.clickNum)return this.$message.error("请选择支付方式");var t=this.setParams();Object.assign(t,{pay_method:this.payMethod}),t.price=Object(o["g"])(this.totalPrice),this.$apis.apiBackgroundTollBackgroundTollOrderCreatePost(t).then((function(t){0===t.code?(e.$emit("showQRCode",t.data,e.totalPrice),e.$emit("refresh")):e.$message.error(t.msg)}))},setParams:function(){var e={transaction_type:this.chargeTypeRadioNum,price:1===this.serviceType?this.companyInfo.toll_rule_info.fee:0,user_scale:"expansion"===this.chargeTypeRadioNum?this.userSize:this.companyInfo.user_scale};if("renew"===this.chargeTypeRadioNum)switch(Object.assign(e,{renew_year:this.chooseRenew}),this.serviceType){case 1:e.price=Object(o["g"])(this.renewForPersonAYear);break;case 2:if(!this.chooseRenew)return this.$message.error("请选择续费年限");e.price=this.companyInfo.renew_fee_list[this.chooseRenew-1];break}return e},calculatePrice:function(e){var t=this;this.$apis.apiBackgroundTollBackgroundTollOrderGetCalcFeePost(e).then((function(e){0===e.code?t.totalPrice=e.data.calc_fee?Object(o["f"])(e.data.calc_fee):0:t.$message.error(e.msg)}))},gotoTermsOfService:function(e){var t=window.location.origin+"/#/agreement?type="+e.agreement_type+"&key=AGREEMENTLIST";window.open(t,"_blank")}}},l=r,_=(n("a6fa"),n("2877")),u=Object(_["a"])(l,i,a,!1,null,"1b387a12",null);t["default"]=u.exports},c9a5:function(e,t,n){},c9d9:function(e,t,n){"use strict";n.d(t,"a",(function(){return s})),n.d(t,"d",(function(){return o})),n.d(t,"b",(function(){return c})),n.d(t,"c",(function(){return r})),n.d(t,"e",(function(){return l})),n.d(t,"f",(function(){return _})),n.d(t,"g",(function(){return u}));var i=n("5a0c"),a=n("da92"),s=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],o=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],c={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},r=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],l=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],_=(i().subtract(7,"day").format("YYYY-MM-DD"),i().format("YYYY-MM-DD"),function(e){return e?"number"===typeof e?a["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:a["a"].divide(e,100).toFixed(2):"0.00"}),u=function(e){return a["a"].times(e,100)}}}]);