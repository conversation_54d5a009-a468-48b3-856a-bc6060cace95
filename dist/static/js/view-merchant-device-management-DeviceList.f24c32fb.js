(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-device-management-DeviceList","view-merchant-device-management-constants"],{"3d7d":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"DeviceList container-wrapper"},[t("refresh-tool",{on:{refreshPage:e.refreshHandle}}),t("search-form",{ref:"searchRef",attrs:{"form-setting":e.searchFormSetting,autoSearch:!1},on:{search:e.searchHandle}}),t("div",{staticClass:"table-wrapper"},[t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[e._v("设备列表")]),t("div",{staticClass:"align-r"},[t("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_device.device.batch_update_face"],expression:"['background_device.device.batch_update_face']"}],attrs:{name:"人脸同步",color:"plain"},on:{click:e.synchronousFace}},[e._v("人脸同步")]),t("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_device.abnormal_video.list"],expression:"['background_device.abnormal_video.list']"}],attrs:{name:"异常视频",color:"plain"},on:{click:e.clickAbnormalVideo}},[e._v("异常视频")]),t("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_device.device.batch_modify"],expression:"['background_device.device.batch_modify']"}],attrs:{name:"批量编辑",color:"origin",type:"mul"},on:{click:function(t){return e.openDeviceDialog("muleditname")}}},[e._v("批量编辑")]),t("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_device.device.mpdify"],expression:"['background_device.device.mpdify']"}],attrs:{name:"批量删除",color:"plain",type:"mul"},on:{click:function(t){return e.mulOperation("mulDel")}}},[e._v("批量删除")]),t("button-icon",{attrs:{name:"切换视图",color:"plain",type:"change"},on:{click:e.changeView}},[e._v("切换视图")])],1)]),t("div",{staticClass:"table-content"},["tableData"===e.dataView?t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","header-row-class-name":"ps-table-header-row"},on:{"selection-change":e.handleSelectionChange}},[t("el-table-column",{attrs:{type:"selection",width:"50",align:"center","class-name":"ps-checkbox"}}),t("el-table-column",{attrs:{prop:"",label:"设备图片",align:"center",width:"150"},scopedSlots:e._u([{key:"default",fn:function(i){return["AZDCJ"===i.row.device_type&&"PS-D2"===i.row.device_model?t("el-image",{staticClass:"w-100-p",staticStyle:{"max-height":"100px"},attrs:{src:e.loadDeviceImg("PS-D2D")}},[t("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[t("i",{staticClass:"el-icon-picture-outline"})])]):"CJY"===i.row.device_type?t("el-image",{staticClass:"w-100-p",staticStyle:{"max-height":"100px"},attrs:{src:e.loadDeviceImg("CJY")}},[t("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[t("i",{staticClass:"el-icon-picture-outline"})])]):"LYG"===i.row.device_type?t("el-image",{staticClass:"w-100-p",staticStyle:{"max-height":"100px"},attrs:{src:e.loadDeviceImg("LYG")}},[t("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[t("i",{staticClass:"el-icon-picture-outline"})])]):i.row.device_model?t("el-image",{staticClass:"w-100-p",staticStyle:{height:"100px"},attrs:{src:e.loadDeviceImg(i.row.device_model)}},[t("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[t("i",{staticClass:"el-icon-picture-outline"})])]):e._e()]}}],null,!1,1688234111)}),t("el-table-column",{attrs:{prop:"consumer_name",label:"所属组织",align:"center"}}),t("el-table-column",{attrs:{prop:"group_name_alias",label:"适用分组",align:"center"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("span",{staticClass:"ps-i pointer",on:{click:function(t){return e.openDeviceDialog("group",i.row)}}},[e._v("查看")])]}}],null,!1,479916741)}),t("el-table-column",{attrs:{prop:"device_type_alias",label:"设备类型",align:"center"}}),t("el-table-column",{attrs:{prop:"device_model_alias",label:"设备型号",align:"center","show-overflow-tooltip":"",width:"140"}}),t("el-table-column",{attrs:{prop:"device_name",label:"设备名",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("span",[e._v(e._s(i.row.device_name))]),t("i",{directives:[{name:"permission",rawName:"v-permission",value:["background_device.device.modify"],expression:"['background_device.device.modify']"}],staticClass:"el-icon-edit ps-i",on:{click:function(t){return e.openDeviceDialog("name",i.row)}}})]}}],null,!1,2665666426)}),t("el-table-column",{attrs:{prop:"device_no",label:"设备号",align:"center"}}),t("el-table-column",{attrs:{prop:"version_number",label:"版本号",align:"center"}}),t("el-table-column",{attrs:{prop:"serial_no",label:"SN码",align:"center"}}),t("el-table-column",{attrs:{prop:"device_mac",label:"设备地址",align:"center"}}),t("el-table-column",{attrs:{prop:"activation_status",label:"激活状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("span",[e._v(e._s(i.row.activation_status?"已激活":"未激活"))])]}}],null,!1,1566368420)}),t("el-table-column",{attrs:{prop:"online",label:"设备状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("span",[e._v(e._s(i.row.online?"在线":"离线"))])]}}],null,!1,4026617887)}),t("el-table-column",{attrs:{prop:"activate_time",label:"激活码有效期",align:"center",width:"170"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("div",[e._v("生效时间："+e._s(i.row.effective))]),t("div",[e._v("失效时间："+e._s(i.row.expiration))])]}}],null,!1,2626898780)}),t("el-table-column",{attrs:{label:"操作",width:"150",align:"center",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_device.device.config"],expression:"['background_device.device.config']"}],staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.openDeviceDialog("ZDSHG"===i.row.device_type?"autoSelling":"setting",i.row)}}},[e._v("设置")]),"QCG"===i.row.device_type?t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_device.device.device_info"],expression:"['background_device.device.device_info']"}],staticClass:"ps-blue",attrs:{type:"text",size:"small"},on:{click:function(t){return e.gotoCupboardEdit(i.row)}}},[e._v("编辑信息")]):e._e(),"ZNC"===i.row.device_type?t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_device.device.device_food_bind.list"],expression:"['background_device.device.device_food_bind.list']"}],staticClass:"ps-blue",attrs:{type:"text",size:"small"},on:{click:function(t){return e.gotoSetFood(i.row)}}},[e._v("绑定菜品")]):e._e(),t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_device.device.mpdify"],expression:"['background_device.device.mpdify']"}],staticClass:"ps-red",attrs:{type:"text",size:"small"},on:{click:function(t){return e.mulOperation("del",i.row.device_no)}}},[e._v("删除")])]}}],null,!1,3906072057)})],1):e._e(),"viewData"===e.dataView?t("div",{staticClass:"view-data"},e._l(e.tableData,(function(i,n){return t("div",{key:n,staticClass:"view-data-item"},[t("div",{staticClass:"item-top"},[t("div",[t("el-checkbox",{model:{value:i.isChoice,callback:function(t){e.$set(i,"isChoice",t)},expression:"item.isChoice"}},[e._v(e._s(i.device_name))])],1),t("div",{staticClass:"item-status"},[t("div",{class:["item-point",e.statusBgColor(i.status_alias)]}),t("div",[e._v(e._s(i.status_alias))])])]),t("div",{staticClass:"item-img"},["AZDCJ"===i.device_type&&"PS-D2"===i.device_model?t("img",{staticClass:"w-100-p",attrs:{src:e.loadDeviceImg("PS-D2D"),alt:"暂无图片"}}):t("img",{staticClass:"w-100-p",attrs:{src:e.loadDeviceImg(i.device_model),alt:"暂无图片"}})]),t("div",{class:["item-bottom",e.statusBgColor(i.status_alias)]}),t("div",{staticClass:"item-mask"},[t("div",{staticClass:"text"},[e._v("所属组织："+e._s(i.consumer_name))]),t("div",{staticClass:"text"},[e._v(" 适用分组："+e._s(i.group_name_alias)+" "),t("i",{staticClass:"el-icon-edit ps-i",on:{click:function(t){return e.openDeviceDialog("group",i)}}})]),t("div",{staticClass:"btn"},[t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_device.device.config"],expression:"['background_device.device.config']"}],staticClass:"ps-origin-btn",staticStyle:{"min-width":"56px"},attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.openDeviceDialog("setting",i)}}},[e._v("设置")]),"QCG"===i.device_type?t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_device.device.device_info"],expression:"['background_device.device.device_info']"}],staticClass:"ps-plain-btn",staticStyle:{"min-width":"56px"},attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.gotoCupboardEdit(i)}}},[e._v("编辑信息")]):e._e(),"ZNC"===i.device_type?t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_device.device.device_food_bind.list"],expression:"['background_device.device.device_food_bind.list']"}],staticClass:"ps-plain-btn",staticStyle:{"min-width":"56px"},attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.gotoSetFood(i)}}},[e._v("绑定菜品")]):e._e(),t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_device.device.mpdify"],expression:"['background_device.device.mpdify']"}],staticClass:"ps-red-btn",staticStyle:{"min-width":"56px"},attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.mulOperation("del",i.device_no)}}},[e._v("删除")])],1)])])})),0):e._e()],1),t("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[t("el-pagination",{staticClass:"ps-text",attrs:{"current-page":e.currentPage,"page-sizes":[10,20,50,100,500],"page-size":e.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)]),t("device-dialog",{attrs:{isshow:e.deviceDialogVisible,title:e.deviceDialogTitle,type:e.deviceDialogType,width:e.deviceDialogWidth,"device-info":e.deviceInfo,"device-list":e.selectList,organizationList:e.searchFormSetting.organization_id.dataList,deviceTypeList:e.searchFormSetting.device_type.dataList},on:{"update:isshow":function(t){e.deviceDialogVisible=t},confirm:e.searchHandle}})],1)},a=[],r=i("ed08"),o=i("efb8"),c=i("4dff");function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function l(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */l=function(){return t};var e,t={},i=Object.prototype,n=i.hasOwnProperty,a=Object.defineProperty||function(e,t,i){e[t]=i.value},r="function"==typeof Symbol?Symbol:{},o=r.iterator||"@@iterator",c=r.asyncIterator||"@@asyncIterator",u=r.toStringTag||"@@toStringTag";function p(e,t,i){return Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{p({},"")}catch(e){p=function(e,t,i){return e[t]=i}}function d(e,t,i,n){var r=t&&t.prototype instanceof y?t:y,o=Object.create(r.prototype),c=new j(n||[]);return a(o,"_invoke",{value:L(e,i,c)}),o}function v(e,t,i){try{return{type:"normal",arg:e.call(t,i)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var g="suspendedStart",f="suspendedYield",m="executing",h="completed",b={};function y(){}function _(){}function S(){}var w={};p(w,o,(function(){return this}));var P=Object.getPrototypeOf,k=P&&P(P(E([])));k&&k!==i&&n.call(k,o)&&(w=k);var D=S.prototype=y.prototype=Object.create(w);function C(e){["next","throw","return"].forEach((function(t){p(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,t){function i(a,r,o,c){var l=v(e[a],e,r);if("throw"!==l.type){var u=l.arg,p=u.value;return p&&"object"==s(p)&&n.call(p,"__await")?t.resolve(p.__await).then((function(e){i("next",e,o,c)}),(function(e){i("throw",e,o,c)})):t.resolve(p).then((function(e){u.value=e,o(u)}),(function(e){return i("throw",e,o,c)}))}c(l.arg)}var r;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){i(e,n,t,a)}))}return r=r?r.then(a,a):a()}})}function L(t,i,n){var a=g;return function(r,o){if(a===m)throw Error("Generator is already running");if(a===h){if("throw"===r)throw o;return{value:e,done:!0}}for(n.method=r,n.arg=o;;){var c=n.delegate;if(c){var s=O(c,n);if(s){if(s===b)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===g)throw a=h,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=m;var l=v(t,i,n);if("normal"===l.type){if(a=n.done?h:f,l.arg===b)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(a=h,n.method="throw",n.arg=l.arg)}}}function O(t,i){var n=i.method,a=t.iterator[n];if(a===e)return i.delegate=null,"throw"===n&&t.iterator.return&&(i.method="return",i.arg=e,O(t,i),"throw"===i.method)||"return"!==n&&(i.method="throw",i.arg=new TypeError("The iterator does not provide a '"+n+"' method")),b;var r=v(a,t.iterator,i.arg);if("throw"===r.type)return i.method="throw",i.arg=r.arg,i.delegate=null,b;var o=r.arg;return o?o.done?(i[t.resultName]=o.value,i.next=t.nextLoc,"return"!==i.method&&(i.method="next",i.arg=e),i.delegate=null,b):o:(i.method="throw",i.arg=new TypeError("iterator result is not an object"),i.delegate=null,b)}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function X(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function j(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function E(t){if(t||""===t){var i=t[o];if(i)return i.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,r=function i(){for(;++a<t.length;)if(n.call(t,a))return i.value=t[a],i.done=!1,i;return i.value=e,i.done=!0,i};return r.next=r}}throw new TypeError(s(t)+" is not iterable")}return _.prototype=S,a(D,"constructor",{value:S,configurable:!0}),a(S,"constructor",{value:_,configurable:!0}),_.displayName=p(S,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===_||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,S):(e.__proto__=S,p(e,u,"GeneratorFunction")),e.prototype=Object.create(D),e},t.awrap=function(e){return{__await:e}},C(x.prototype),p(x.prototype,c,(function(){return this})),t.AsyncIterator=x,t.async=function(e,i,n,a,r){void 0===r&&(r=Promise);var o=new x(d(e,i,n,a),r);return t.isGeneratorFunction(i)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},C(D),p(D,u,"Generator"),p(D,o,(function(){return this})),p(D,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),i=[];for(var n in t)i.push(n);return i.reverse(),function e(){for(;i.length;){var n=i.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=E,j.prototype={constructor:j,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(X),!t)for(var i in this)"t"===i.charAt(0)&&n.call(this,i)&&!isNaN(+i.slice(1))&&(this[i]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var i=this;function a(n,a){return c.type="throw",c.arg=t,i.next=n,a&&(i.method="next",i.arg=e),!!a}for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r],c=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var s=n.call(o,"catchLoc"),l=n.call(o,"finallyLoc");if(s&&l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,t){for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var r=a;break}}r&&("break"===e||"continue"===e)&&r.tryLoc<=t&&t<=r.finallyLoc&&(r=null);var o=r?r.completion:{};return o.type=e,o.arg=t,r?(this.method="next",this.next=r.finallyLoc,b):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),b},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.finallyLoc===e)return this.complete(i.completion,i.afterLoc),X(i),b}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.tryLoc===e){var n=i.completion;if("throw"===n.type){var a=n.arg;X(i)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,i,n){return this.delegate={iterator:E(t),resultName:i,nextLoc:n},"next"===this.method&&(this.arg=e),b}},t}function u(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,n)}return i}function p(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?u(Object(i),!0).forEach((function(t){d(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):u(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function d(e,t,i){return(t=v(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function v(e){var t=g(e,"string");return"symbol"==s(t)?t:t+""}function g(e,t){if("object"!=s(e)||!e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var n=i.call(e,t||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function f(e,t,i,n,a,r,o){try{var c=e[r](o),s=c.value}catch(e){return void i(e)}c.done?t(s):Promise.resolve(s).then(n,a)}function m(e){return function(){var t=this,i=arguments;return new Promise((function(n,a){var r=e.apply(t,i);function o(e){f(r,n,a,o,c,"next",e)}function c(e){f(r,n,a,o,c,"throw",e)}o(void 0)}))}}var h={name:"DeviceList",components:{deviceDialog:o["default"]},props:{},data:function(){return{isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[{name:"1"}],viewData:[{name:"取餐柜001",isChoice:!1,status:"在线",img:"https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/9df1ad0960343dc5cbeb75b321c6c4661640336843252.png",point:"XXX食堂/XXX档口",group:"XXX分组"},{name:"取餐柜001",isChoice:!1,status:"离线",img:"https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/9df1ad0960343dc5cbeb75b321c6c4661640336843372.png",point:"XXX食堂/XXX档口/XXX食堂/XXX档口",group:"XXX分组"},{name:"取餐柜001",isChoice:!1,status:"未激活",img:"https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/9df1ad0960343dc5cbeb75b321c6c4661640336843431.png",point:"XXX食堂/XXX档口",group:"XXX分组"}],searchFormSetting:{organization_id:{type:"consumeSelect",label:"消费点",value:"",placeholder:"请选择消费点"},online:{type:"select",label:"设备状态",value:"",placeholder:"请选择设备状态",dataList:[{label:"全部",value:""},{label:"离线",value:!1},{label:"在线",value:!0}]},device_type:{type:"select",label:"设备类型",value:"",placeholder:"请选择设备类型",listNameKey:"name",listValueKey:"key",dataList:[]},device_name:{type:"input",label:"设备名",value:"",placeholder:"请输入设备名"},device_no:{type:"input",label:"设备号",value:"",placeholder:"请输入设备号"},activation_status:{type:"select",label:"激活状态",value:"",placeholder:"请选择激活状态",dataList:[{label:"全部",value:""},{label:"未激活",value:!1},{label:"已激活",value:!0}]}},dataView:"tableData",deviceDialogVisible:!1,deviceDialogTitle:"",deviceDialogType:"",deviceDialogWidth:"",deviceInfo:{},selectList:[],deviceUi:[]}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getDeviceList(),this.getDeviceType()},searchHandle:Object(r["d"])((function(e){e&&"search"===e&&(this.currentPage=1,this.deviceDialogVisible=!1,this.getDeviceList())}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.currentPage=1,this.tableData=[],this.initLoad()},formatQueryParams:function(e){var t={};for(var i in e)""!==e[i].value&&null!==e[i].value&&0!==e[i].value.length&&("select_date"!==i?t[i]=e[i].value:e[i].value.length>0&&(t.start_date=e[i].value[0],t.end_date=e[i].value[1]));return t},getDeviceList:function(){var e=this;return m(l().mark((function t(){var i;return l().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,e.$apis.apiBackgroundDeviceDeviceListPost(p(p({},e.formatQueryParams(e.searchFormSetting)),{},{page:e.currentPage,page_size:e.pageSize}));case 3:i=t.sent,e.isLoading=!1,0===i.code?(e.tableData=i.data.results,e.tableData.map((function(e){e.effective=e.activate_time.split(" ")[0],e.expiration=e.activate_time.split(" ")[3],e.group_name_alias="",e.group_name.map((function(t){e.group_name_alias+=t+"， "})),e.group_name_alias=e.group_name_alias.slice(0,-2),e.status_alias=e.activation_status?e.online?"在线":"离线":"未激活"})),e.totalCount=i.data.count):e.$message.error(i.msg);case 6:case"end":return t.stop()}}),t)})))()},handleSizeChange:function(e){this.pageSize=e,this.getDeviceList()},handleCurrentChange:function(e){this.currentPage=e,this.getDeviceList()},handleSelectionChange:function(e){this.selectList=[],this.selectList=Object.freeze(e)},mulOperation:function(e,t){var i=this;if(!t&&!this.selectList.length)return this.$message.error("请先选择数据！");var n="提示",a="";switch(e){case"mulDel":a="确定批量删除所选设备吗？";break;case"del":a="确定删除该设备吗？";break}this.$confirm("".concat(a),"".concat(n),{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var n=m(l().mark((function n(a,r,o){var c,s;return l().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if("confirm"!==a){n.next=19;break}r.confirmButtonLoading=!0,c={},s=[],n.t0=e,n.next="mulDel"===n.t0?7:"del"===n.t0?11:14;break;case 7:return i.selectList.map((function(e){s.push(e.device_no)})),c.device_nos=s,c.choices=3,n.abrupt("break",14);case 11:return c.device_nos=[t],c.choices=3,n.abrupt("break",14);case 14:i.delDevice(c),o(),r.confirmButtonLoading=!1,n.next=20;break;case 19:r.confirmButtonLoading||o();case 20:case"end":return n.stop()}}),n)})));function a(e,t,i){return n.apply(this,arguments)}return a}()}).then((function(e){})).catch((function(e){}))},delDevice:function(e){var t=this;return m(l().mark((function i(){var n;return l().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,t.$apis.apiBackgroundDeviceDeviceBatchModifyPost(e);case 2:n=i.sent,0===n.code?t.$message.success("删除设备成功"):t.$message.error(n.msg);case 4:case"end":return i.stop()}}),i)})))()},openDeviceDialog:function(e,t){switch(this.deviceDialogType=e,this.deviceInfo=t,e){case"group":this.deviceDialogTitle="修改分组",this.deviceDialogWidth="400px";break;case"name":this.deviceDialogTitle="修改设备名",this.deviceDialogWidth="500px";break;case"setting":this.deviceDialogTitle="设置",this.deviceDialogWidth="500px";break;case"muleditname":if(this.deviceDialogTitle="批量编辑",this.deviceDialogWidth="600px",!this.selectList.length)return this.deviceDialogVisible=!1,this.$message.error("请先选择数据！");break;case"autoSelling":this.deviceDialogTitle="设置可消费组织",this.deviceDialogWidth="500px";break;default:break}this.deviceDialogVisible=!0},changeView:function(){"tableData"===this.dataView?this.dataView="viewData":"viewData"===this.dataView&&(this.dataView="tableData")},statusBgColor:function(e){return"未激活"===e?"notActive":"在线"===e?"online":"离线"===e?"offline":void 0},getDeviceType:function(){var e=this;return m(l().mark((function t(){var i;return l().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundDeviceDeviceDeviceTypePost();case 2:i=t.sent,0===i.code?e.searchFormSetting.device_type.dataList=i.data:e.$message.error(i.msg);case 4:case"end":return t.stop()}}),t)})))()},gotoSetFood:function(e){this.$router.push({name:"MerchantWeightFood",query:{deviceId:e.device_no,deviceModel:e.device_model,deviceName:e.device_name}})},gotoCupboardEdit:function(e){var t=JSON.parse(e.cupboard_json).ceil_list;this.$router.push({name:"MerchantCupboardEdit",query:{device_no:e.device_no,device_name:e.device_name,ceil_list:JSON.stringify(t)}})},loadDeviceImg:function(e){return c["DEVICE_IMG"][e]?i("57fa")("./"+c["DEVICE_IMG"][e]):""},synchronousFace:function(){if(!this.selectList.length)return this.$message.error("请先选择数据！");var e=[];for(var t in this.selectList)e.push(this.selectList[t].device_no);this.getSynchronouFaceInfo(e)},getSynchronouFaceInfo:function(e){var t=this;return m(l().mark((function i(){var n;return l().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return t.isLoading=!0,i.next=3,t.$apis.apiBackgroundDeviceDeviceBatchUpdateFacePost({device_ids:e});case 3:n=i.sent,t.isLoading=!1,0===n.code?(t.$message.success("人脸同步成功"),t.getDeviceList()):(t.$refs.tableData.clearSelection(),t.$message.error(n.msg));case 6:case"end":return i.stop()}}),i)})))()},clickAbnormalVideo:function(){this.$router.push({name:"MerchantAbnormalVideoList",query:{}})}}},b=h,y=(i("b25e"),i("2877")),_=Object(y["a"])(b,n,a,!1,null,null,null);t["default"]=_.exports},"4dff":function(e,t,i){"use strict";i.r(t),i.d(t,"recentSevenDay",(function(){return p})),i.d(t,"TableSetting",(function(){return d})),i.d(t,"TabTypeList",(function(){return v})),i.d(t,"TableButtonList",(function(){return g})),i.d(t,"DishPatternTableButton",(function(){return f})),i.d(t,"WeekList",(function(){return m})),i.d(t,"SwitchDate",(function(){return h})),i.d(t,"DEVICE_IMG",(function(){return b})),i.d(t,"PRINT_ADMIN_SEARCH",(function(){return y})),i.d(t,"PRINT_LIST_SEARCH",(function(){return _})),i.d(t,"PRINT_ADMIN_TABLE",(function(){return S})),i.d(t,"PRINT_LIST_TABLE",(function(){return w})),i.d(t,"getRequestParams",(function(){return P}));var n=i("ed08"),a=i("5a0c");function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function o(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,n)}return i}function c(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?o(Object(i),!0).forEach((function(t){s(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):o(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function s(e,t,i){return(t=l(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function l(e){var t=u(e,"string");return"symbol"==r(t)?t:t+""}function u(e,t){if("object"!=r(e)||!e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var n=i.call(e,t||"default");if("object"!=r(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var p=[a().subtract(7,"day").format("YYYY-MM-DD"),a().format("YYYY-MM-DD")],d=[{label:"设备名称",key:"device_name"},{label:"图片1",key:"1",type:"slot",slotName:"default"},{label:"图片2",key:"2",type:"slot",slotName:"default"},{label:"图片3",key:"3",type:"slot",slotName:"default"},{label:"操作",key:"operation",type:"slot",slotName:"operation"}],v=[{name:"显示设置",key:"showSetting"},{name:"图片设置",key:"imgSetting"}],g=[{name:"一键清空",key:"cleanImg"},{name:"导入图片",key:"importImg"}],f=[{name:"一键清空",key:"cleanDish"},{name:"复制",key:"copy"}],m=[{label:"全部",key:0},{label:"周一",key:1},{label:"周二",key:2},{label:"周三",key:3},{label:"周四",key:4},{label:"周五",key:5},{label:"周六",key:6},{label:"周日",key:7}],h=function(e){var t="";switch(e){case 0:t="周一";break;case 1:t="周二";break;case 2:t="周三";break;case 3:t="周四";break;case 4:t="周五";break;case 5:t="周六";break;case 6:t="周日";break}return t},b={"PS-502":"PS-502.png","PS-503":"PS-503.png","PS-1050":"PS-1050.png","PS-1314":"PS-1314.png","PS-1500":"PS-1500.png","PS-1516":"PS-1516.png","PS-BOX32":"PS-BOX32.png","PS-BOX-BX10":"PS-BOX-BX10.png","PS-BOX-BX20":"PS-BOX-BX20.png","PS-C1050":"PS-C1050.png","PS-C1050-1":"PS-C1050-1.png","PS-C1050-2":"PS-C1050-2.png","PS-C1051":"PS-C1051.png","PS-D2":"PS-D2.png","PS-HY11S":"PS-HY11S.png","PS-HY11W":"PS-HY11W.png","PS-K1 02":"PS-K1 02.png","PS-k1":"PS-k1.png","PS-KW001":"PS-KW001.png","PS-M2":"PS-M2.png","PS-P2":"PS-P2.png","PS-TP":"PS-TP.png","PS-ZJ001":"PS-ZJ001.png","PS-ZY001":"PS-ZY001.png","PS-ZY002右":"PS-ZY002右.png","PS-ZY002左":"PS-ZY002左.png","PS-ZZKWJ":"PS-ZZKWJ.png","PS-H8":"PS-H8.png","PS-LY01":"PS-LYY.png","PS-D2D":"PS-D2D.png","PS-K1-TP":"PS-K1-TP.png","PS-TPJ":"PS-TPJ.png",CXP:"CXP.png","PS-KB01":"PS-KB01.png","PS-KC001":"PS-KC001.png","PS-D2mini":"PS-D2.png",CJY:"CJY.png",LYG:"LYG.png"},y={org_ids:{type:"organizationSelect",value:[],label:"适用组织",checkStrictly:!0,isLazy:!1,collapseTags:!0,multiple:!0},type:{type:"select",label:"类型",value:"",placeholder:"请选择类型",dataList:[{label:"全部",value:""},{label:"热敏纸打印机",value:"thermal_paper"},{label:"标签打印机",value:"label"}]},sn:{type:"input",label:"序列号",value:"",placeholder:"请输入序列号"},name:{type:"input",label:"名称",value:"",placeholder:"请输入名称"}},_={create_time:{timeRange:!0,type:"daterange",label:"创建时间",clearable:!0,value:Object(n["t"])(0,{format:"{y}-{m}-{d}"})},print_time:{timeRange:!0,type:"daterange",label:"打印时间",clearable:!0,value:[]},print_no:{type:"input",label:"打印单号",value:"",placeholder:"请输入打印单号"},task_status:{type:"select",label:"打印状态",value:"",multiple:!0,clearable:!0,collapseTags:!0,placeholder:"请选择打印状态",dataList:[{label:"未开始",value:"not_started"},{label:"打印中",value:"printing"},{label:"打印成功",value:"success"},{label:"打印异常",value:"terminated"},{label:"打印终止",value:"abnormal"}]}},S=[{label:"打印机品牌",key:"brand_alias"},{label:"序列号/编号",key:"sn",width:160},{label:"名称",key:"name"},{label:"类型",key:"type_alias"},{label:"设备状态",key:"printer_status_alias"},{label:"适用组织",key:"org"},{label:"运行状态",key:"running_status_alias"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"220"}],w=[{label:"打印单号",key:"print_no"},{label:"创建时间",key:"create_time"},{label:"打印时间",key:"print_time"},{label:"打印进度",key:"progress",type:"slot",slotName:"progress"},{label:"打印份数",key:"print_num"},{label:"打印状态",key:"task_status_alias"},{label:"打印设备",key:"printer_name"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"80"}],P=function(e,t,i){var n={};Object.keys(e).forEach((function(t){var i,a;e[t].timeRange&&(null===(i=e[t].value)||void 0===i?void 0:i.length)>0?(n["start_"+t]=e[t].value[0],n["end_"+t]=e[t].value[1]):""!==e[t].value&&e[t].value&&(null===(a=e[t].value)||void 0===a?void 0:a.length)>0&&(n[t]=e[t].value)}));var a=c({page:t,page_size:i},n);return a}},"57fa":function(e,t,i){var n={"./CJY.png":"e0e6","./LYG.png":"d2f6","./PS-1050.png":"30d9","./PS-1314.png":"80f9","./PS-1500.png":"63bd","./PS-1516.png":"0bd2","./PS-502.png":"4fcc","./PS-503.png":"0196","./PS-BOX-BX10.png":"99e0","./PS-BOX-BX20.png":"e5de","./PS-BOX32.png":"8cba","./PS-C1050-1.png":"3339","./PS-C1050-2.png":"9728","./PS-C1050.png":"1e2d","./PS-C1051.png":"81b4","./PS-D2.png":"6e34","./PS-D2D.png":"8d85","./PS-H8.png":"3e53","./PS-HY11S.png":"1716","./PS-HY11W.png":"9fb1","./PS-K1 02.png":"3824","./PS-K1-TP.png":"e438","./PS-KB01.png":"06f5","./PS-KC001.png":"1aae","./PS-KW001.png":"a656","./PS-LYY.png":"48ab","./PS-M2.png":"aa11","./PS-P2.png":"284d","./PS-TP.png":"1daf","./PS-ZJ001.png":"4c8e","./PS-ZY001.png":"9ed2","./PS-ZY002右.png":"2886","./PS-ZY002左.png":"b705","./PS-ZZKWJ.png":"7335","./PS-k1.png":"0bee"};function a(e){var t=r(e);return i(t)}function r(e){if(!i.o(n,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return n[e]}a.keys=function(){return Object.keys(n)},a.resolve=r,e.exports=a,a.id="57fa"},8812:function(e,t,i){e.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}},b25e:function(e,t,i){"use strict";i("8812")}}]);