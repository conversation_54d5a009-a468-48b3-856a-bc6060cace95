(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-meal-report-component-AddMealReport","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-meal-report-MealPackageRule"],{"3fa5":function(e,t,r){"use strict";r.d(t,"a",(function(){return n}));r("9e1f"),r("450d");var a=r("6ed5"),i=r.n(a);function n(e,t){return new Promise((function(r,a){i.a.confirm(e.content?e.content:"",e.title?e.title:"提示",{dangerouslyUseHTMLString:!0|e.useHTML,distinguishCancelAndClose:!0,closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:e.cancel_class?e.cancel_class:"ps-cancel-btn",confirmButtonClass:e.confirm_class?e.confirm_class:"ps-btn",confirmButtonText:e.confirmButtonText,cancelButtonText:e.cancelButtonText,center:""===e.center||e.center}).then((function(e){t?r(t()):r()})).catch((function(e){a(e)}))}))}},"7dde":function(e,t,r){"use strict";r("92ad")},"92ad":function(e,t,r){},b176:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e._self._c;return t("el-drawer",e._g(e._b({staticClass:"meal-drawer-wrapper",attrs:{title:"add"===e.type?"新增报餐规则":"编辑报餐规则",visible:e.showDrawer,direction:e.direction,wrapperClosable:e.wrapperClosable,size:"820px"},on:{"update:visible":function(t){e.showDrawer=t}}},"el-drawer",e.$attrs,!1),e.$listeners),[t("div",{staticClass:"meal-declaration container-wrapper"},[t("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"mealFormRef",attrs:{model:e.mealForm,rules:e.mealFormRules,"label-width":"125px"}},[t("div",{staticClass:"basic"},[t("div",{staticClass:"l-title clearfix"},[t("span",{staticClass:"float-l"},[e._v("基本设置")])]),t("div",{staticClass:"form-line margin-button"}),t("div",[t("el-form-item",{attrs:{size:"small",label:"当前组织：",prop:"company"}},[[t("span",[e._v(e._s(e.mealForm.company))])]],2),t("el-form-item",{attrs:{label:"规则名称：",prop:"name",size:"small"}},[t("el-input",{staticClass:"w-220",attrs:{maxlength:"20"},model:{value:e.mealForm.name,callback:function(t){e.$set(e.mealForm,"name",t)},expression:"mealForm.name"}})],1),t("el-form-item",{attrs:{size:"small",label:"适用分组：",prop:"report_card_groups"}},[t("user-group-select",{attrs:{multiple:""},model:{value:e.mealForm.report_card_groups,callback:function(t){e.$set(e.mealForm,"report_card_groups",t)},expression:"mealForm.report_card_groups"}})],1),t("el-form-item",{attrs:{size:"small",label:"适用消费点：",prop:"report_organizations"}},[t("consume-select",{attrs:{multiple:""},model:{value:e.mealForm.report_organizations,callback:function(t){e.$set(e.mealForm,"report_organizations",t)},expression:"mealForm.report_organizations"}})],1),t("el-form-item",{attrs:{label:"餐段配置：",prop:"meal_types"}},[t("div",{staticClass:"meal-types"},[t("el-checkbox-group",{model:{value:e.mealForm.meal_types,callback:function(t){e.$set(e.mealForm,"meal_types",t)},expression:"mealForm.meal_types"}},e._l(e.mealType,(function(r){return t("el-checkbox",{key:r.value,staticClass:"ps-checkbox",attrs:{label:r.value,name:"meal_types"}},[t("span",{staticStyle:{width:"50px",display:"inline-block"}},[e._v(e._s(r.label))]),t("el-input",{staticStyle:{width:"80px","margin-right":"10px"},attrs:{disabled:!e.mealForm.meal_types.includes(r.value),size:"mini"},model:{value:e.mealForm[r.field2],callback:function(t){e.$set(e.mealForm,r.field2,t)},expression:"mealForm[mt.field2]"}}),e._v("元 ")],1)})),1)],1)]),t("el-form-item",{attrs:{label:"下单方式：",prop:"order_type"}},[t("el-checkbox-group",{model:{value:e.mealForm.order_type,callback:function(t){e.$set(e.mealForm,"order_type",t)},expression:"mealForm.order_type"}},[t("el-checkbox",{staticClass:"ps-checkbox",attrs:{label:"report_meal",name:"order_type"}},[e._v(" 报餐模式 ")]),t("el-checkbox",{staticClass:"ps-checkbox",attrs:{label:"meal_pack",name:"order_type",disabled:"offline"===e.mealForm.consume_type}},[e._v(" 餐包模式 ")])],1)],1),t("el-form-item",{attrs:{label:"取餐方式：",prop:"take_out_type"}},[t("el-checkbox-group",{model:{value:e.mealForm.take_out_type,callback:function(t){e.$set(e.mealForm,"take_out_type",t)},expression:"mealForm.take_out_type"}},[t("el-checkbox",{staticClass:"ps-checkbox",attrs:{label:"on_scene",name:"take_out_type"}},[e._v(" 堂食 ")]),t("el-checkbox",{staticClass:"ps-checkbox",attrs:{label:"bale",name:"take_out_type"}},[e._v(" 食堂自提 ")])],1)],1),t("el-form-item",{attrs:{label:"扣费方式：",prop:"consume_type"}},[t("el-radio-group",{model:{value:e.mealForm.consume_type,callback:function(t){e.$set(e.mealForm,"consume_type",t)},expression:"mealForm.consume_type"}},[t("el-radio",{staticClass:"ps-radio",attrs:{label:"online"}},[e._v("线上支付")]),t("el-radio",{staticClass:"ps-radio",attrs:{label:"offline",disabled:-1!==e.mealForm.order_type.indexOf("meal_pack")}},[e._v(" 线下核销 ")])],1)],1),t("el-form-item",{staticStyle:{height:"42px"},attrs:{size:"small",label:"限制份数：",prop:"is_limit"}},[t("el-radio-group",{staticClass:"is_limit",model:{value:e.mealForm.is_limit,callback:function(t){e.$set(e.mealForm,"is_limit",t)},expression:"mealForm.is_limit"}},[t("el-radio",{staticClass:"ps-radio",attrs:{label:0}},[e._v("不限制")]),t("el-radio",{staticClass:"ps-radio",attrs:{label:1}},[e._v("单人单餐限制")])],1),1===e.mealForm.is_limit?t("div",{staticClass:"inline-block"},[t("el-input-number",{staticStyle:{margin:"0 12px"},attrs:{min:1,"controls-position":"right"},model:{value:e.mealForm.limit_count,callback:function(t){e.$set(e.mealForm,"limit_count",t)},expression:"mealForm.limit_count"}}),e._v(" 份 ")],1):e._e()],1),t("el-form-item",{attrs:{label:"服务费：",prop:"fuwu_fee"}},[t("el-input",{staticStyle:{width:"100px"},model:{value:e.mealForm.fuwu_fee,callback:function(t){e.$set(e.mealForm,"fuwu_fee",t)},expression:"mealForm.fuwu_fee"}}),t("span",{staticClass:"tail-unit"},[e._v("元")])],1),t("el-form-item",{attrs:{size:"small",label:"取消订单："}},[t("div",{staticClass:"box-flex m-t-5"},[t("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:e.mealForm.can_refund,callback:function(t){e.$set(e.mealForm,"can_refund",t)},expression:"mealForm.can_refund"}}),t("span",{staticClass:"tips"},[e._v("注：订单完成后不允许取消")])],1),e.mealForm.can_refund?t("div",{staticClass:"can-refund-wrapper"},[t("el-radio",{staticClass:"ps-radio",attrs:{label:"pay_after"},model:{value:e.mealForm.refund_remit_type,callback:function(t){e.$set(e.mealForm,"refund_remit_type",t)},expression:"mealForm.refund_remit_type"}},[[t("span",[e._v("下单后")]),"pay_after"===e.mealForm.refund_remit_type?t("div",{staticClass:"inline-block"},[t("el-input-number",{staticStyle:{margin:"0 12px"},attrs:{min:1,"controls-position":"right"},model:{value:e.mealForm.refund_remit_time,callback:function(t){e.$set(e.mealForm,"refund_remit_time",t)},expression:"mealForm.refund_remit_time"}}),t("span",[e._v("小时内")])],1):e._e()]],2),t("div",[t("el-radio",{staticClass:"ps-radio",attrs:{label:"meal_before"},model:{value:e.mealForm.refund_remit_type,callback:function(t){e.$set(e.mealForm,"refund_remit_type",t)},expression:"mealForm.refund_remit_type"}},[[t("span",[e._v("餐段结束前")]),"meal_before"===e.mealForm.refund_remit_type?t("div",{staticClass:"inline-block"},[t("el-input-number",{staticStyle:{margin:"0 12px"},attrs:{min:1,"controls-position":"right"},model:{value:e.mealForm.refund_remit_time,callback:function(t){e.$set(e.mealForm,"refund_remit_time",t)},expression:"mealForm.refund_remit_time"}}),t("span",[e._v("小时内")])],1):e._e()]],2)],1),t("el-radio",{staticClass:"ps-radio",attrs:{label:"day_hour_before"},model:{value:e.mealForm.refund_remit_type,callback:function(t){e.$set(e.mealForm,"refund_remit_type",t)},expression:"mealForm.refund_remit_type"}},[[t("span",[e._v("就餐当日前")]),"day_hour_before"===e.mealForm.refund_remit_type?t("div",{staticClass:"inline-block"},[t("el-input-number",{staticStyle:{margin:"0 5px"},attrs:{min:0,"controls-position":"right"},on:{change:e.changeRefundRemitDay},model:{value:e.mealForm.refund_remit_day,callback:function(t){e.$set(e.mealForm,"refund_remit_day",t)},expression:"mealForm.refund_remit_day"}}),t("span",[e._v("天")]),t("el-input-number",{staticStyle:{margin:"0 5px"},attrs:{"controls-position":"right",min:e.minHour,max:23},on:{change:e.changeRefundRemitBeforeHour},model:{value:e.mealForm.refund_remit_before_hour,callback:function(t){e.$set(e.mealForm,"refund_remit_before_hour",t)},expression:"mealForm.refund_remit_before_hour"}}),t("span",[e._v("小时")])],1):e._e()]],2)],1):e._e()]),e.mealForm.can_refund?t("el-form-item",{attrs:{label:"是否需要审核："}},[t("div",{staticClass:"box-flex"},[t("el-switch",{staticClass:"m-t-10 m-r-20",attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:e.mealForm.cancel_review,callback:function(t){e.$set(e.mealForm,"cancel_review",t)},expression:"mealForm.cancel_review"}}),e.mealForm.cancel_review?t("el-form-item",{attrs:{label:""}},[t("el-radio-group",{staticClass:"ps-radio",model:{value:e.mealForm.review_type,callback:function(t){e.$set(e.mealForm,"review_type",t)},expression:"mealForm.review_type"}},[t("el-radio",{attrs:{label:"auto_reject"}},[e._v("开餐后，自动拒绝")]),t("el-radio",{attrs:{label:"auto_success"}},[e._v("开餐后，自动同意")])],1)],1):e._e()],1)]):e._e()],1),t("div",{staticClass:"l-title clearfix"},[t("span",{staticClass:"float-l"},[e._v("点餐设置")])]),t("div",{staticClass:"form-line margin-button"}),t("div",[t("el-form-item",{attrs:{size:"small",label:"可报餐天数：",prop:"can_reservation_days"}},[t("el-input-number",{attrs:{min:0,"controls-position":"right"},model:{value:e.mealForm.can_reservation_days,callback:function(t){e.$set(e.mealForm,"can_reservation_days",t)},expression:"mealForm.can_reservation_days"}}),t("span",{staticClass:"tail-unit"},[e._v("天（含当天）")])],1),t("el-form-item",{attrs:{label:"报餐截止："}},[t("div",{staticClass:"box-flex row-between"},[t("div",[e._v("餐段结束前")]),t("div",[e._v("单位：小时")])]),t("div",{staticClass:"fake-table-wrapper"},[t("div",{staticClass:"fake-table"},e._l(e.mealType,(function(r){return t("div",{key:r.value,staticClass:"fake-col"},[t("span",{staticClass:"fake-col-title"},[e._v(e._s(r.label))]),t("el-input-number",{staticStyle:{width:"86px",margin:"10px"},attrs:{"controls-position":"right",min:0,disabled:!e.mealForm.meal_types.includes(r.value),size:"mini"},model:{value:e.mealForm[r.field],callback:function(t){e.$set(e.mealForm,r.field,t)},expression:"mealForm[mt.field]"}})],1)})),0)])]),t("el-form-item",{staticClass:"skip_wrapper",attrs:{label:"以下日期跳过："}},[t("div",{staticClass:"box-flex"},[t("div",{staticClass:"m-r-20"},[t("el-checkbox",{staticClass:"ps-checkbox",model:{value:e.mealForm.skip_weekends,callback:function(t){e.$set(e.mealForm,"skip_weekends",t)},expression:"mealForm.skip_weekends"}},[e._v("周末")]),t("el-checkbox",{staticClass:"ps-checkbox",model:{value:e.mealForm.skip_holiday,callback:function(t){e.$set(e.mealForm,"skip_holiday",t)},expression:"mealForm.skip_holiday"}},[e._v("节假日")]),t("el-checkbox",{staticClass:"ps-checkbox",model:{value:e.mealForm.skip_work_days,callback:function(t){e.$set(e.mealForm,"skip_work_days",t)},expression:"mealForm.skip_work_days"}},[e._v("工作日")])],1),t("div",{staticClass:"ship-box"},[t("div",[t("el-button",{staticClass:"hidden-picker",attrs:{type:"text"}},[e._v(" 添加 "),t("el-date-picker",{attrs:{type:"dates",clearable:!1,placeholder:"选择一个或多个日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd","popper-class":"hidden-picker-year","picker-options":e.pickerOptions},model:{value:e.mealForm.skip_days,callback:function(t){e.$set(e.mealForm,"skip_days",t)},expression:"mealForm.skip_days"}})],1),t("el-button",{attrs:{type:"text"},on:{click:e.clearShipDay}},[e._v("清空")])],1)])]),t("transition-group",{staticClass:"no-ship",attrs:{name:"el-zoom-in-center",tag:"div"}},e._l(e.mealForm.skip_days,(function(r,a){return t("div",{key:r,staticClass:"no-ship-time"},[e._v(" "+e._s(e.formatNoSkipTime(r))+" "),t("div",{staticClass:"del-time",on:{click:function(t){return e.delSkipTime(a)}}},[t("i",{staticClass:"el-icon-error"})])])})),0)],1),t("div",{staticClass:"skip_wrapper"},[t("el-form-item",{staticClass:"skip_wrapper",attrs:{label:"以下日期不跳过："}},[t("div",[t("el-button",{staticClass:"hidden-picker",attrs:{type:"text"}},[e._v(" 添加 "),t("el-date-picker",{attrs:{type:"dates",clearable:!1,placeholder:"选择一个或多个日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd","popper-class":"hidden-picker-year","picker-options":e.pickerOptions},model:{value:e.mealForm.no_skip_days,callback:function(t){e.$set(e.mealForm,"no_skip_days",t)},expression:"mealForm.no_skip_days"}})],1),t("el-button",{attrs:{type:"text"},on:{click:e.clearNoShipDay}},[e._v("清空")])],1),t("transition-group",{staticClass:"no-ship",attrs:{name:"el-zoom-in-center",tag:"div"}},e._l(e.mealForm.no_skip_days,(function(r,a){return t("div",{key:r,staticClass:"no-ship-time"},[e._v(" "+e._s(e.formatNoSkipTime(r))+" "),t("div",{staticClass:"del-time",on:{click:function(t){return e.delNoSkipTime(a)}}},[t("i",{staticClass:"el-icon-error"})])])})),0)],1)],1)],1)]),t("div",{staticClass:"ps-el-drawer-footer"},[t("el-button",{staticClass:"w-100",attrs:{size:"small"},on:{click:function(t){e.showDrawer=!1}}},[e._v("取消")]),t("el-button",{staticClass:"w-100",attrs:{size:"small",type:"primary"},on:{click:e.saveSetting}},[e._v("保存")])],1)])],1)])},i=[],n=r("2f62"),o=r("390a"),s=r("7c9c"),l=r("c9d9"),c=r("ed08"),m=r("3fa5");function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}function d(e,t){return _(e)||f(e,t)||v(e,t)||p()}function p(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var a,i,n,o,s=[],l=!0,c=!1;try{if(n=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(a=n.call(r)).done)&&(s.push(a.value),s.length!==t);l=!0);}catch(e){c=!0,i=e}finally{try{if(!l&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return s}}function _(e){if(Array.isArray(e))return e}function y(e){return g(e)||b(e)||v(e)||h()}function h(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function v(e,t){if(e){if("string"==typeof e)return k(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?k(e,t):void 0}}function b(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function g(e){if(Array.isArray(e))return k(e)}function k(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,a=Array(t);r<t;r++)a[r]=e[r];return a}function w(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */w=function(){return t};var e,t={},r=Object.prototype,a=r.hasOwnProperty,i=Object.defineProperty||function(e,t,r){e[t]=r.value},n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",s=n.asyncIterator||"@@asyncIterator",l=n.toStringTag||"@@toStringTag";function c(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,r){return e[t]=r}}function m(e,t,r,a){var n=t&&t.prototype instanceof v?t:v,o=Object.create(n.prototype),s=new M(a||[]);return i(o,"_invoke",{value:D(e,r,s)}),o}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=m;var p="suspendedStart",f="suspendedYield",_="executing",y="completed",h={};function v(){}function b(){}function g(){}var k={};c(k,o,(function(){return this}));var F=Object.getPrototypeOf,x=F&&F(F(E([])));x&&x!==r&&a.call(x,o)&&(k=x);var C=g.prototype=v.prototype=Object.create(k);function S(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function O(e,t){function r(i,n,o,s){var l=d(e[i],e,n);if("throw"!==l.type){var c=l.arg,m=c.value;return m&&"object"==u(m)&&a.call(m,"__await")?t.resolve(m.__await).then((function(e){r("next",e,o,s)}),(function(e){r("throw",e,o,s)})):t.resolve(m).then((function(e){c.value=e,o(c)}),(function(e){return r("throw",e,o,s)}))}s(l.arg)}var n;i(this,"_invoke",{value:function(e,a){function i(){return new t((function(t,i){r(e,a,t,i)}))}return n=n?n.then(i,i):i()}})}function D(t,r,a){var i=p;return function(n,o){if(i===_)throw Error("Generator is already running");if(i===y){if("throw"===n)throw o;return{value:e,done:!0}}for(a.method=n,a.arg=o;;){var s=a.delegate;if(s){var l=$(s,a);if(l){if(l===h)continue;return l}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(i===p)throw i=y,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);i=_;var c=d(t,r,a);if("normal"===c.type){if(i=a.done?y:f,c.arg===h)continue;return{value:c.arg,done:a.done}}"throw"===c.type&&(i=y,a.method="throw",a.arg=c.arg)}}}function $(t,r){var a=r.method,i=t.iterator[a];if(i===e)return r.delegate=null,"throw"===a&&t.iterator.return&&(r.method="return",r.arg=e,$(t,r),"throw"===r.method)||"return"!==a&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+a+"' method")),h;var n=d(i,t.iterator,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,h;var o=n.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,h):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,h)}function j(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function L(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function M(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(j,this),this.reset(!0)}function E(t){if(t||""===t){var r=t[o];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,n=function r(){for(;++i<t.length;)if(a.call(t,i))return r.value=t[i],r.done=!1,r;return r.value=e,r.done=!0,r};return n.next=n}}throw new TypeError(u(t)+" is not iterable")}return b.prototype=g,i(C,"constructor",{value:g,configurable:!0}),i(g,"constructor",{value:b,configurable:!0}),b.displayName=c(g,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,g):(e.__proto__=g,c(e,l,"GeneratorFunction")),e.prototype=Object.create(C),e},t.awrap=function(e){return{__await:e}},S(O.prototype),c(O.prototype,s,(function(){return this})),t.AsyncIterator=O,t.async=function(e,r,a,i,n){void 0===n&&(n=Promise);var o=new O(m(e,r,a,i),n);return t.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},S(C),c(C,l,"Generator"),c(C,o,(function(){return this})),c(C,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var a in t)r.push(a);return r.reverse(),function e(){for(;r.length;){var a=r.pop();if(a in t)return e.value=a,e.done=!1,e}return e.done=!0,e}},t.values=E,M.prototype={constructor:M,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(L),!t)for(var r in this)"t"===r.charAt(0)&&a.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function i(a,i){return s.type="throw",s.arg=t,r.next=a,i&&(r.method="next",r.arg=e),!!i}for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n],s=o.completion;if("root"===o.tryLoc)return i("end");if(o.tryLoc<=this.prev){var l=a.call(o,"catchLoc"),c=a.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return i(o.catchLoc,!0);if(this.prev<o.finallyLoc)return i(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return i(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return i(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&a.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var n=i;break}}n&&("break"===e||"continue"===e)&&n.tryLoc<=t&&t<=n.finallyLoc&&(n=null);var o=n?n.completion:{};return o.type=e,o.arg=t,n?(this.method="next",this.next=n.finallyLoc,h):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),h},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),L(r),h}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var a=r.completion;if("throw"===a.type){var i=a.arg;L(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,r,a){return this.delegate={iterator:E(t),resultName:r,nextLoc:a},"next"===this.method&&(this.arg=e),h}},t}function F(e,t,r,a,i,n,o){try{var s=e[n](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(a,i)}function x(e){return function(){var t=this,r=arguments;return new Promise((function(a,i){var n=e.apply(t,r);function o(e){F(n,a,i,o,s,"next",e)}function s(e){F(n,a,i,o,s,"throw",e)}o(void 0)}))}}function C(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function S(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?C(Object(r),!0).forEach((function(t){O(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):C(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function O(e,t,r){return(t=D(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function D(e){var t=$(e,"string");return"symbol"==u(t)?t:t+""}function $(e,t){if("object"!=u(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=u(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var j={name:"AddMealDeclaration",components:{UserGroupSelect:o["a"],ConsumeSelect:s["a"]},props:{show:{required:!0,type:Boolean},wrapperClosable:{type:Boolean,default:!0},direction:{type:String,default:"rtl"},infoData:{type:Object,default:function(){return{}}},type:{type:String,default:"add"},confirm:Function},data:function(){var e=function(e,t,r){if(t){var a=/^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;a.test(t)?r():r(new Error("金额格式有误"))}else r()};return{isLoading:!1,editData:[],mealType:l["a"],mealForm:{company:"xx公司",name:"",report_card_groups:[],report_organizations:[],meal_types:[],is_limit:0,limit_count:"",can_reservation_days:"",breakfast_ahead:0,lunch_ahead:0,hit_tea_ahead:0,dinner_ahead:0,midnight_ahead:0,early_ahead:0,order_type:["report_meal"],take_out_type:["on_scene"],consume_type:"online",breakfast_fixed:0,lunch_fixed:0,hit_tea_fixed:0,dinner_fixed:0,midnight_fixed:0,early_fixed:0,can_refund:!0,cancel_review:!0,review_type:"auto_reject",refund_remit_type:"pay_after",refund_remit_time:"",refund_remit_day:"",refund_remit_before_hour:"",fuwu_fee:"",skip_holiday:!1,skip_weekends:!1,skip_work_days:!1,skip_days:[],no_skip_days:[]},mealFormRules:{name:[{required:!0,message:"请输入规则名称",trigger:"change, blur"}],report_card_groups:[{required:!0,message:"至少选择一个分组",trigger:"change, blur"}],report_organizations:[{required:!0,message:"至少选择一个消费点",trigger:"change, blur"}],meal_types:[{required:!0,message:"至少选择一个餐段",trigger:"change, blur"}],can_reservation_days:[{type:"number",required:!0,message:"时间必须大于或等于0"}],order_type:[{required:!0,message:"至少选择一个下单方式",trigger:"change, blur"}],take_out_type:[{required:!0,message:"至少选择一个取餐方式",trigger:"change, blur"}],consume_type:[{required:!0,message:"至少选择一个取餐方式",trigger:"change, blur"}],is_limit:[{required:!0,message:"至少选择一个取餐方式",trigger:"change, blur"}],fuwu_fee:[{validator:e,trigger:"blur"}]},pickerOptions:{disabledDate:function(e){return e.getTime()<Date.now()-864e5}},minHour:0}},created:function(){},computed:S(S({},Object(n["c"])(["userInfo"])),{},{showDrawer:{get:function(){return this.show&&this.initLoad(),this.show},set:function(e){this.$emit("update:show",e)}}}),methods:{initLoad:function(){var e=this;return x(w().mark((function t(){return w().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.resetForm(),e.mealForm.company=e.userInfo.company_name,"edit"===e.type&&(e.editData=e.infoData,e.initEditForm());case 3:case"end":return t.stop()}}),t)})))()},initEditForm:function(){var e=this;this.mealForm.meal_types=this.editData.meal_type_detail.meal_type;var t=this.mealType.map((function(e){return e.field})),r=[].concat(y(t),["name","report_card_groups","report_organizations","can_reservation_days","order_type","take_out_type","consume_type","can_refund","refund_remit_type","refund_remit_time","cancel_review","review_type","refund_remit_before_hour","refund_remit_day","skip_holiday","skip_weekends","skip_work_days"]);r.forEach((function(t){e.mealForm[t]=e.editData[t]})),this.editData.skip_days&&(this.mealForm.skip_days="string"===typeof this.editData.skip_days?JSON.parse(this.editData.skip_days):this.editData.skip_days),this.editData.no_skip_days&&(this.mealForm.no_skip_days="string"===typeof this.editData.no_skip_days?JSON.parse(this.editData.no_skip_days):this.editData.no_skip_days);var a=this.mealType.map((function(e){return e.field2}));a.push("fuwu_fee"),a.forEach((function(t){e.mealForm[t]=Object(c["i"])(e.editData[t])})),this.mealForm.is_limit=0===this.editData.limit_count?0:1,this.editData.limit_count>0&&(this.mealForm.limit_count=this.editData.limit_count)},changeRefundRemitDay:function(e){0==this.mealForm.refund_remit_day&&0==e?(this.$set(this.mealForm,"refund_remit_before_hour",1),this.minHour=1):this.minHour=0},changeRefundRemitBeforeHour:function(e){0==this.mealForm.refund_remit_day&&0==e?this.minHour=1:this.minHour=0},saveSetting:function(){var e=this;this.$refs.mealFormRef.validate((function(t){if(t){if(!e.checkForm())return;if(e.isLoading)return;"add"===e.type?e.addMealDeclaration(e.formatData()):e.checkPackageRule()}}))},addMealDeclaration:function(e){var t=this;return x(w().mark((function r(){var a,i,n,o;return w().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return t.isLoading=!0,r.next=3,Object(c["Z"])(t.$apis.apiBackgroundReportMealReportMealSettingsAddPost(e));case 3:if(a=r.sent,i=d(a,2),n=i[0],o=i[1],t.isLoading=!1,!n){r.next=11;break}return t.$message.error(n.message),r.abrupt("return");case 11:0===o.code?(t.$message.success(o.msg),t.$emit("confirm",o.msg)):t.$message.error(o.msg);case 12:case"end":return r.stop()}}),r)})))()},checkPackageRule:function(){var e=this;return x(w().mark((function t(){var r;return w().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundReportMealReportMealSettingsCheckReportMealPackSettingsPost({id:e.editData.id});case 2:r=t.sent,0===r.code?r.data.flag?Object(m["a"])({content:"该规则已被餐包规则关联，修改后将同步下架对应餐包"}).then((function(t){e.modifyMealDeclaration(e.formatData())})).catch((function(e){})):e.modifyMealDeclaration(e.formatData()):e.$message.error(r.msg);case 4:case"end":return t.stop()}}),t)})))()},modifyMealDeclaration:function(e){var t=this;return x(w().mark((function r(){var a,i,n,o;return w().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return t.isLoading=!0,r.next=3,Object(c["Z"])(t.$apis.apiBackgroundReportMealReportMealSettingsModifyPost(S(S({},e),{},{id:t.editData.id})));case 3:if(a=r.sent,i=d(a,2),n=i[0],o=i[1],t.isLoading=!1,!n){r.next=11;break}return t.$message.error(n.message),r.abrupt("return");case 11:0===o.code?(t.$message.success(o.msg),t.$emit("confirm",o.msg)):t.$message.error(o.msg);case 12:case"end":return r.stop()}}),r)})))()},formatNoSkipTime:function(e){var t=e.replace(/-/g,(function(e,t){var r="";switch(t){case 4:r="年";break;case 7:r="月";break}return r}));return t+"日"},delNoSkipTime:function(e){this.mealForm.no_skip_days.splice(e,1)},clearNoShipDay:function(){this.mealForm.no_skip_days=[]},delSkipTime:function(e){this.mealForm.skip_days.splice(e,1)},clearShipDay:function(){this.mealForm.skip_days=[]},checkForm:function(){var e=!0;if(this.mealForm.can_refund){if(!this.mealForm.refund_remit_type)return this.$message.error("请选择取消订单类型！"),e=!1,!1;if("day_hour_before"!==this.mealForm.refund_remit_type&&void 0===this.mealForm.refund_remit_time)return this.$message.error("取消时间不能为空！"),e=!1,!1;if(void 0===this.mealForm.refund_remit_day||void 0===this.mealForm.refund_remit_before_hour)return this.$message.error("取消时间不能为空！"),e=!1,!1}return e},formatData:function(){var e=this,t={organization:sessionStorage.getItem("organization"),name:this.mealForm.name,report_card_groups:this.mealForm.report_card_groups,report_organizations:this.mealForm.report_organizations,meal_types:[],limit_count:1===this.mealForm.is_limit?this.mealForm.limit_count:0,can_reservation_days:this.mealForm.can_reservation_days,order_type:this.mealForm.order_type,take_out_type:this.mealForm.take_out_type,consume_type:this.mealForm.consume_type,can_refund:this.mealForm.can_refund,cancel_review:this.mealForm.cancel_review,review_type:this.mealForm.review_type,refund_remit_day:this.mealForm.refund_remit_day,refund_remit_before_hour:this.mealForm.refund_remit_before_hour,fuwu_fee:Object(c["Y"])(this.mealForm.fuwu_fee),skip_holiday:this.mealForm.skip_holiday,skip_weekends:this.mealForm.skip_weekends,skip_work_days:this.mealForm.skip_work_days,no_skip_days:JSON.stringify(this.mealForm.no_skip_days),skip_days:JSON.stringify(this.mealForm.skip_days)};return this.mealType.map((function(r){e.mealForm.meal_types.map((function(a){r.value===a&&(t[r.field]=e.mealForm[r.field],t[r.field2]=Object(c["Y"])(e.mealForm[r.field2]),t.meal_types.push(a))}))})),this.mealForm.can_refund?(t.refund_remit_type=this.mealForm.refund_remit_type,t.refund_remit_time=this.mealForm.refund_remit_time):t.cancel_review=!1,Object(c["h"])(t)},resetForm:function(){this.mealForm={company:"xx公司",name:"",report_card_groups:[],report_organizations:[],meal_types:[],is_limit:0,limit_count:"",can_reservation_days:"",breakfast_ahead:0,lunch_ahead:0,hit_tea_ahead:0,dinner_ahead:0,midnight_ahead:0,early_ahead:0,order_type:["report_meal"],take_out_type:["on_scene"],consume_type:"online",breakfast_fixed:0,lunch_fixed:0,hit_tea_fixed:0,dinner_fixed:0,midnight_fixed:0,early_fixed:0,can_refund:!0,cancel_review:!0,review_type:"auto_reject",refund_remit_type:"pay_after",refund_remit_time:"",refund_remit_day:"",refund_remit_before_hour:"",fuwu_fee:"",skip_holiday:!1,skip_weekends:!1,skip_work_days:!1,skip_days:[],no_skip_days:[]}}}},L=j,M=(r("7dde"),r("2877")),E=Object(M["a"])(L,a,i,!1,null,"34e0b167",null);t["default"]=E.exports},c9d9:function(e,t,r){"use strict";r.d(t,"a",(function(){return n})),r.d(t,"d",(function(){return o})),r.d(t,"b",(function(){return s})),r.d(t,"c",(function(){return l})),r.d(t,"e",(function(){return c})),r.d(t,"f",(function(){return m})),r.d(t,"g",(function(){return u}));var a=r("5a0c"),i=r("da92"),n=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],o=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],s={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},l=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],c=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],m=(a().subtract(7,"day").format("YYYY-MM-DD"),a().format("YYYY-MM-DD"),function(e){return e?"number"===typeof e?i["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:i["a"].divide(e,100).toFixed(2):"0.00"}),u=function(e){return i["a"].times(e,100)}}}]);