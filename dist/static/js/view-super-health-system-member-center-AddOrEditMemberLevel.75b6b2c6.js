(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-member-center-AddOrEditMemberLevel"],{"2bbd":function(e,t,r){},"47da":function(e,t,r){"use strict";r.r(t);var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"AddOrEditMemberLevel container-wrapper"},[t("div",{staticClass:"table-wrapper"},[t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[e._v(e._s("add"===e.type?"新建":"编辑")+"会员等级")])]),t("div",[t("el-form",{ref:"memberFormRef",attrs:{model:e.memberForm,rules:e.memberFormRules,"label-width":"180px"}},[t("el-form-item",{attrs:{label:"会员等级："}},[e._v(e._s(e.memberForm.grade))]),t("el-form-item",{attrs:{label:"等级名称：",prop:"name"}},[t("el-input",{staticClass:"ps-input w-180",attrs:{maxlength:"5"},model:{value:e.memberForm.name,callback:function(t){e.$set(e.memberForm,"name",t)},expression:"memberForm.name"}})],1),t("el-form-item",{attrs:{label:"会员等级成长分数区间：",prop:"growthScore"}},[e._v(" "+e._s(e.memberForm.startScore)+" ~ "),t("el-input",{staticClass:"ps-input w-180",attrs:{maxlength:"7"},model:{value:e.memberForm.growthScore,callback:function(t){e.$set(e.memberForm,"growthScore",t)},expression:"memberForm.growthScore"}}),e._v(" 分 ")],1),"edit"===e.type?t("div",{staticClass:"tips"},[e._v("说明：成长分上限必须小于下一等级的成长分区间上限")]):e._e(),t("el-form-item",{attrs:{label:"购买/续费成长分数："}},[t("el-form-item",{staticClass:"m-b-10",attrs:{prop:"monthScore"}},[e._v(" 月卡（30天）"),t("el-input",{staticClass:"ps-input w-180",attrs:{maxlength:"7"},model:{value:e.memberForm.monthScore,callback:function(t){e.$set(e.memberForm,"monthScore",t)},expression:"memberForm.monthScore"}}),e._v(" 分 ")],1),t("el-form-item",{staticClass:"m-b-10",attrs:{prop:"seasonScore"}},[e._v(" 季卡（90天）"),t("el-input",{staticClass:"ps-input w-180",attrs:{maxlength:"7"},model:{value:e.memberForm.seasonScore,callback:function(t){e.$set(e.memberForm,"seasonScore",t)},expression:"memberForm.seasonScore"}}),e._v(" 分 ")],1),t("el-form-item",{staticClass:"m-b-10",attrs:{prop:"yearScore"}},[e._v(" 年卡（365天）"),t("el-input",{staticClass:"ps-input w-180",attrs:{maxlength:"7"},model:{value:e.memberForm.yearScore,callback:function(t){e.$set(e.memberForm,"yearScore",t)},expression:"memberForm.yearScore"}}),e._v(" 分 ")],1)],1),t("el-form-item",{attrs:{label:"会员权限：",prop:"memberPermission"}},[t("el-select",{staticClass:"ps-select w-250 m-l-5",attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",multiple:"","collapse-tags":""},on:{change:function(t){return e.changeMemberPermission()}},model:{value:e.memberForm.memberPermission,callback:function(t){e.$set(e.memberForm,"memberPermission",t)},expression:"memberForm.memberPermission"}},e._l(e.memberPermissionList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),t("el-form-item",[t("div",{staticClass:"label-list"},e._l(e.permissionNameList,(function(r,o){return t("div",{key:o,staticClass:"label-list-item"},[t("span",{staticClass:"m-r-5"},[e._v(e._s(r))]),t("i",{staticClass:"el-icon-close del-icon",on:{click:function(t){return e.delPermission(o)}}})])})),0)]),t("el-form-item",[t("el-button",{staticClass:"ps-origin-btn w-150",attrs:{size:"small",type:"primary"},on:{click:e.saveSetting}},[e._v("保存")])],1)],1)],1)])])},n=[];function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function a(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */a=function(){return t};var e,t={},r=Object.prototype,o=r.hasOwnProperty,n=Object.defineProperty||function(e,t,r){e[t]=r.value},s="function"==typeof Symbol?Symbol:{},m=s.iterator||"@@iterator",c=s.asyncIterator||"@@asyncIterator",u=s.toStringTag||"@@toStringTag";function l(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,r){return e[t]=r}}function h(e,t,r,o){var i=t&&t.prototype instanceof y?t:y,a=Object.create(i.prototype),s=new N(o||[]);return n(a,"_invoke",{value:k(e,r,s)}),a}function p(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=h;var f="suspendedStart",d="suspendedYield",b="executing",g="completed",v={};function y(){}function w(){}function _(){}var S={};l(S,m,(function(){return this}));var F=Object.getPrototypeOf,L=F&&F(F(j([])));L&&L!==r&&o.call(L,m)&&(S=L);var x=_.prototype=y.prototype=Object.create(S);function P(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function E(e,t){function r(n,a,s,m){var c=p(e[n],e,a);if("throw"!==c.type){var u=c.arg,l=u.value;return l&&"object"==i(l)&&o.call(l,"__await")?t.resolve(l.__await).then((function(e){r("next",e,s,m)}),(function(e){r("throw",e,s,m)})):t.resolve(l).then((function(e){u.value=e,s(u)}),(function(e){return r("throw",e,s,m)}))}m(c.arg)}var a;n(this,"_invoke",{value:function(e,o){function n(){return new t((function(t,n){r(e,o,t,n)}))}return a=a?a.then(n,n):n()}})}function k(t,r,o){var n=f;return function(i,a){if(n===b)throw Error("Generator is already running");if(n===g){if("throw"===i)throw a;return{value:e,done:!0}}for(o.method=i,o.arg=a;;){var s=o.delegate;if(s){var m=$(s,o);if(m){if(m===v)continue;return m}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(n===f)throw n=g,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);n=b;var c=p(t,r,o);if("normal"===c.type){if(n=o.done?g:d,c.arg===v)continue;return{value:c.arg,done:o.done}}"throw"===c.type&&(n=g,o.method="throw",o.arg=c.arg)}}}function $(t,r){var o=r.method,n=t.iterator[o];if(n===e)return r.delegate=null,"throw"===o&&t.iterator.return&&(r.method="return",r.arg=e,$(t,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+o+"' method")),v;var i=p(n,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var a=i.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function O(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function N(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(O,this),this.reset(!0)}function j(t){if(t||""===t){var r=t[m];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,a=function r(){for(;++n<t.length;)if(o.call(t,n))return r.value=t[n],r.done=!1,r;return r.value=e,r.done=!0,r};return a.next=a}}throw new TypeError(i(t)+" is not iterable")}return w.prototype=_,n(x,"constructor",{value:_,configurable:!0}),n(_,"constructor",{value:w,configurable:!0}),w.displayName=l(_,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,l(e,u,"GeneratorFunction")),e.prototype=Object.create(x),e},t.awrap=function(e){return{__await:e}},P(E.prototype),l(E.prototype,c,(function(){return this})),t.AsyncIterator=E,t.async=function(e,r,o,n,i){void 0===i&&(i=Promise);var a=new E(h(e,r,o,n),i);return t.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},P(x),l(x,u,"Generator"),l(x,m,(function(){return this})),l(x,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var o in t)r.push(o);return r.reverse(),function e(){for(;r.length;){var o=r.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},t.values=j,N.prototype={constructor:N,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(C),!t)for(var r in this)"t"===r.charAt(0)&&o.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(o,n){return s.type="throw",s.arg=t,r.next=o,n&&(r.method="next",r.arg=e),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var m=o.call(a,"catchLoc"),c=o.call(a,"finallyLoc");if(m&&c){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(m){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&o.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),C(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var o=r.completion;if("throw"===o.type){var n=o.arg;C(r)}return n}}throw Error("illegal catch attempt")},delegateYield:function(t,r,o){return this.delegate={iterator:j(t),resultName:r,nextLoc:o},"next"===this.method&&(this.arg=e),v}},t}function s(e,t,r,o,n,i,a){try{var s=e[i](a),m=s.value}catch(e){return void r(e)}s.done?t(m):Promise.resolve(m).then(o,n)}function m(e){return function(){var t=this,r=arguments;return new Promise((function(o,n){var i=e.apply(t,r);function a(e){s(i,o,n,a,m,"next",e)}function m(e){s(i,o,n,a,m,"throw",e)}a(void 0)}))}}var c={name:"AddOrEditMemberLevel",props:{},data:function(){var e=this,t=function(t,r,o){var n=/^\d+$/;if(""===r)return o(new Error("不能为空"));n.test(r)?r<e.memberForm.startScore?o(new Error("必须大于等于起始分")):e.maxScore&&r>=e.maxScore?o(new Error("必须小于下一等级成长分区间上限")):o():o(new Error("请输入正整数"))},r=function(e,t,r){var o=/^\d+$/;t&&!o.test(t)?r(new Error("请输入正整数")):r()};return{isLoading:!1,type:"",settingData:{},memberForm:{grade:1,name:"",startScore:0,growthScore:"",monthScore:"",seasonScore:"",yearScore:"",memberPermission:[]},memberFormRules:{name:[{required:!0,message:"请输入等级名称",trigger:"blur"}],growthScore:[{required:!0,validator:t,trigger:"blur"}],monthScore:[{validator:r,trigger:"blur"}],seasonScore:[{validator:r,trigger:"blur"}],yearScore:[{validator:r,trigger:"blur"}],memberPermission:[{required:!0,message:"请选择权限",trigger:"blur"}]},maxScore:null,memberPermissionList:[],permissionNameList:[]}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.$route.params.type&&(this.type=this.$route.params.type),"add"===this.type?(this.memberForm.grade=this.$route.query.grade,this.memberForm.startScore=this.$route.query.startScore):(this.settingData=JSON.parse(decodeURIComponent(this.$route.query.data)),this.memberForm.grade=this.settingData.grade,this.memberForm.name=this.settingData.name,this.memberForm.startScore=this.settingData.start_growth_value,this.memberForm.growthScore=this.settingData.end_growth_value,this.memberForm.monthScore=this.settingData.month_growth_points,this.memberForm.seasonScore=this.settingData.season_growth_points,this.memberForm.yearScore=this.settingData.year_growth_points,this.memberForm.memberPermission=this.settingData.member_permissions_list.map((function(e){return e.id})),this.permissionNameList=this.settingData.member_permissions_list.map((function(e){return e.name})),this.maxScore=this.$route.query.maxScore),this.getMemberPermission()},saveSetting:function(){var e=this;this.$refs.memberFormRef.validate((function(t){if(t){var r,o={grade:e.memberForm.grade,name:e.memberForm.name,start_growth_value:e.memberForm.startScore,end_growth_value:e.memberForm.growthScore,member_permissions:e.memberForm.memberPermission};switch(e.memberForm.monthScore&&(o.month_growth_points=e.memberForm.monthScore),e.memberForm.seasonScore&&(o.season_growth_points=e.memberForm.seasonScore),e.memberForm.yearScore&&(o.year_growth_points=e.memberForm.yearScore),e.type){case"add":r=e.$apis.apiBackgroundMemberMemberGradeAddPost(o);break;case"edit":o.id=Number(e.settingData.id),r=e.$apis.apiBackgroundMemberMemberGradeModifyPost(o);break}e.confirmOperation(r)}}))},confirmOperation:function(e){var t=this;return m(a().mark((function r(){var o;return a().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!t.isLoading){r.next=2;break}return r.abrupt("return");case 2:return t.isLoading=!0,r.next=5,e;case 5:o=r.sent,t.isLoading=!1,0===o.code?(t.$message.success("成功"),t.$closeCurrentTab(t.$route.path)):t.$message.error(o.msg);case 8:case"end":return r.stop()}}),r)})))()},getMemberPermission:function(){var e=this;return m(a().mark((function t(){var r;return a().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,e.$apis.apiBackgroundMemberMemberPermissionListPost({page:1,page_size:99999});case 3:r=t.sent,e.isLoading=!1,0===r.code?e.memberPermissionList=r.data.results:e.$message.error(r.msg);case 6:case"end":return t.stop()}}),t)})))()},delPermission:function(e){this.memberForm.memberPermission.splice(e,1),this.permissionNameList.splice(e,1)},changeMemberPermission:function(){var e=this;this.permissionNameList=[],this.memberPermissionList.map((function(t){-1!==e.memberForm.memberPermission.indexOf(t.id)&&e.permissionNameList.push(t.name)}))}}},u=c,l=(r("db09"),r("2877")),h=Object(l["a"])(u,o,n,!1,null,"73dd6e1a",null);t["default"]=h.exports},db09:function(e,t,r){"use strict";r("2bbd")}}]);