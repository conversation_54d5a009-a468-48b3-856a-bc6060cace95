(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-user-center-components-AddCustomerDialog"],{"9c9b":function(t,e,r){"use strict";r("9dcf")},"9dcf":function(t,e,r){},f888:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("el-drawer",{attrs:{title:t.title,visible:t.visible,width:"450px","custom-class":"ps-dialog-message","close-on-press-escape":!1,"show-close":"",wrapperClosable:!1},on:{"update:visible":function(e){t.visible=e},close:t.close}},[e("el-form",{ref:"dialogForm",staticClass:"m-t-20",attrs:{"label-width":"100px",rules:t.dialogFormRules,model:t.dialogEditData}},[e("el-form-item",{attrs:{label:"人员编号",prop:"person_no"}},[e("el-input",{staticClass:"w-250",attrs:{placeholder:"请输入人员编号",type:"text",disabled:"edit"===t.type},on:{input:t.dialogInputChange},model:{value:t.dialogEditData.person_no,callback:function(e){t.$set(t.dialogEditData,"person_no",e)},expression:"dialogEditData.person_no"}})],1),e("el-form-item",{attrs:{label:"姓名",prop:"name"}},[e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoadingPerson,expression:"isLoadingPerson"}],staticClass:"dialog-person-info"},[t._v(" "+t._s(t.dialogEditData.name)+" ")])]),e("el-form-item",{attrs:{label:"客户号",prop:"client_number"}},[e("el-input",{staticClass:"w-250",attrs:{placeholder:"请输入客户号",type:"text"},model:{value:t.dialogEditData.client_number,callback:function(e){t.$set(t.dialogEditData,"client_number",e)},expression:"dialogEditData.client_number"}})],1)],1),e("span",{staticClass:"dialog-footer-drawer"},[e("el-button",{staticClass:"ps-cancel-btn",on:{click:t.clickCancleHandle}},[t._v("取 消")]),e("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.isBindLoading,expression:"isBindLoading"}],staticClass:"ps-btn",attrs:{type:"primary"},on:{click:t.submitEditDialog}},[t._v("确 定")])],1)],1)},o=[],i=r("ed08");function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},c=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function d(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{d({},"")}catch(t){d=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,a=Object.create(i.prototype),s=new A(n||[]);return o(a,"_invoke",{value:j(t,r,s)}),a}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",m="suspendedYield",g="executing",v="completed",y={};function b(){}function w(){}function _(){}var E={};d(E,c,(function(){return this}));var x=Object.getPrototypeOf,L=x&&x(x(I([])));L&&L!==r&&n.call(L,c)&&(E=L);var D=_.prototype=b.prototype=Object.create(E);function C(t){["next","throw","return"].forEach((function(e){d(t,e,(function(t){return this._invoke(e,t)}))}))}function P(t,e){function r(o,i,s,c){var l=h(t[o],t,i);if("throw"!==l.type){var u=l.arg,d=u.value;return d&&"object"==a(d)&&n.call(d,"__await")?e.resolve(d.__await).then((function(t){r("next",t,s,c)}),(function(t){r("throw",t,s,c)})):e.resolve(d).then((function(t){u.value=t,s(u)}),(function(t){return r("throw",t,s,c)}))}c(l.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function j(e,r,n){var o=p;return function(i,a){if(o===g)throw Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var s=n.delegate;if(s){var c=k(s,n);if(c){if(c===y)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=g;var l=h(e,r,n);if("normal"===l.type){if(o=n.done?v:m,l.arg===y)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(o=v,n.method="throw",n.arg=l.arg)}}}function k(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,k(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var i=h(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,y;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function O(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function S(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function A(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(O,this),this.reset(!0)}function I(e){if(e||""===e){var r=e[c];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(a(e)+" is not iterable")}return w.prototype=_,o(D,"constructor",{value:_,configurable:!0}),o(_,"constructor",{value:w,configurable:!0}),w.displayName=d(_,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,d(t,u,"GeneratorFunction")),t.prototype=Object.create(D),t},e.awrap=function(t){return{__await:t}},C(P.prototype),d(P.prototype,l,(function(){return this})),e.AsyncIterator=P,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new P(f(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},C(D),d(D,u,"Generator"),d(D,c,(function(){return this})),d(D,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=I,A.prototype={constructor:A,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(S),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return s.type="throw",s.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),l=n.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),S(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;S(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:I(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),y}},e}function c(t,e){return h(t)||f(t,e)||u(t,e)||l()}function l(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(t,e){if(t){if("string"==typeof t)return d(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?d(t,e):void 0}}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function f(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,s=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(s.push(n.value),s.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return s}}function h(t){if(Array.isArray(t))return t}function p(t,e,r,n,o,i,a){try{var s=t[i](a),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,o)}function m(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){p(i,n,o,a,s,"next",t)}function s(t){p(i,n,o,a,s,"throw",t)}a(void 0)}))}}var g={name:"AddCustomerDialog",props:{loading:Boolean,type:{type:String,default:"add"},title:{type:String,default:"新建"},show:Boolean},data:function(){var t=function(t,e,r){""===e?r(new Error("请输入客户号")):e&&!/^[a-zA-Z0-9_]+$/i.test(e)?r(new Error("请输入正确的客户号")):r()};return{isLoading:!1,dialogEditData:{person_no:"",name:"",phone:"",client_number:""},isLoadingPerson:!1,dialogFormRules:{person_no:[{required:!0,message:"请输入人员",trigger:"blur"}],name:[{required:!1,message:"请填入人员编号查询",trigger:"blur"}],client_number:[{required:!0,validator:t,trigger:"blur"}]},isHasPersonInfo:!1,isBindLoading:!1}},computed:{visible:{get:function(){return this.show},set:function(t){this.$emit("update:show",t)}}},watch:{visible:function(t){t&&this.initLoad()}},created:function(){},mounted:function(){},methods:{initLoad:function(){},clickCancleHandle:function(){this.visible=!1},dialogInputChange:function(t){var e=this;t.length>0&&(this.timer&&clearTimeout(this.timer),this.timer=setTimeout((function(){e.getNameAndMobile(t)}),1500))},getNameAndMobile:function(t){var e=this;return m(s().mark((function r(){var n,o,a,l,u;return s().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return e.isLoadingPerson=!0,r.next=3,Object(i["Z"])(e.$apis.apiCardServiceThirdCardUserGetCardInfoPost({person_no:t}));case 3:if(n=r.sent,o=c(n,2),a=o[0],l=o[1],e.isLoadingPerson=!1,!a){r.next=12;break}return e.isHasPersonInfo=!1,e.setNameAndMobile(),r.abrupt("return");case 12:0===l.code?(u=l.data||{},e.isHasPersonInfo=!0,e.setNameAndMobile(u.name,u.id)):(e.isHasPersonInfo=!1,e.setNameAndMobile());case 13:case"end":return r.stop()}}),r)})))()},setNameAndMobile:function(t,e){this.dialogEditData.name=t||"",this.dialogEditData.card_info_id=e||""},submitEditDialog:function(){var t=this;this.$refs.dialogForm.validate((function(e){if(e){if(!t.isHasPersonInfo&&"add"===t.type)return void t.$message.error("没有查询到该用户，请重新修改人员编号查询！");t.bindCardInfo()}}))},bindCardInfo:function(){var t=this;return m(s().mark((function e(){var r,n,o,a,l,u;return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r="add"===t.type?"新增":"修改",n={client_number:t.dialogEditData.client_number,person_no:t.dialogEditData.person_no},"edit"===t.type?(n.id=t.dialogEditData.id,n.is_modify=!0):n.card_info_id=t.dialogEditData.card_info_id,t.isBindLoading=!0,e.next=6,Object(i["Z"])(t.$apis.apiCardServiceThirdCardUserAbcClientModifyPost(n));case 6:if(o=e.sent,a=c(o,2),l=a[0],u=a[1],t.isBindLoading=!1,!l){e.next=14;break}return t.$message.error(r+"失败 "+l.message),e.abrupt("return");case 14:u&&0===u.code?(t.$message.success(r+"成功"),t.$emit("confirmDialog",t.dialogEditData)):t.$message.error(r+"失败 "+u.msg);case 15:case"end":return e.stop()}}),e)})))()},setInfoData:function(t){t&&(this.dialogEditData.person_no=t.person_no||"",this.dialogEditData.name=t.name||"",this.dialogEditData.client_number=t.client_number||"",this.dialogEditData.id=t.id||"")},close:function(){this.$refs.dialogForm&&this.$refs.dialogForm.resetFields(),this.dialogEditData={person_no:"",name:"",phone:"",client_number:"",id:""},this.$emit("close")}}},v=g,y=(r("9c9b"),r("2877")),b=Object(y["a"])(v,n,o,!1,null,"13590d6e",null);e["default"]=b.exports}}]);