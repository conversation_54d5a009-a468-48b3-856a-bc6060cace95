(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-health-system-components-PhysicalExaminationReportDetail"],{2619:function(t,s,i){"use strict";i("f3d5")},"5d8e":function(t,s,i){"use strict";i.r(s);var a=function(){var t=this,s=t._self._c;return s("div",{staticClass:"ps-el-drawer"},[s("el-drawer",{attrs:{title:"体检报告详情",visible:t.visible,"show-close":!1,size:"40%"}},[s("div",{staticClass:"p-20"},[s("div",{staticClass:"flex flex-col"},[s("span",{staticClass:"f-w-700 m-b-20"},[t._v("报告类型："+t._s(t.data.data_source_alias))]),t.data.integrity_data&&0!==t.data.integrity_data.length?s("div",t._l(t.data.integrity_data,(function(i,a){return s("div",{key:a,staticClass:"m-b-20"},[s("div",{staticClass:"f-w-700 m-b-10"},[t._v(t._s(i.name))]),s("div",{class:[i.name.includes("血压心率")||i.name.includes("人体成分")?"default-layout":"special-layout","bg-gray"]},t._l(i.children,(function(i,a){return s("div",{key:a,staticClass:"flex-b-c"},[s("div",{staticClass:"m-r-10"},[s("div",[t._v(t._s(i.name)+"："+t._s(i.value||"--")+" "+t._s(i.unit))])]),s("div",{class:["tips","正常"===i.result_text?"normal":"","偏高"===i.result_text?"height":"","偏低"===i.result_text?"low":"","font-size-12"]},[t._v(" "+t._s(i.result_text)+" ")])])})),0)])})),0):s("div",[s("el-empty",{attrs:{description:"暂无数据"}})],1)]),s("div",{staticClass:"ps-el-drawer-footer ps-flex-align-c flex-align-c"},[s("div",{staticClass:"m-r-30"},[s("el-button",{staticClass:"w-100 ps-origin-btn",attrs:{size:"small"},on:{click:function(s){t.visible=!1}}},[t._v("关闭")])],1)])])])],1)},e=[],n={name:"PhysicalExaminationReportDetail",props:{isShow:Boolean,reportName:String,data:{type:Object,default:function(){return{}}}},computed:{visible:{get:function(){return this.isShow},set:function(t){this.$emit("update:isShow",t)}}},data:function(){return{isLoading:!1}},methods:{}},l=n,c=(i("2619"),i("2877")),r=Object(c["a"])(l,a,e,!1,null,"28faa09c",null);s["default"]=r.exports},f3d5:function(t,s,i){}}]);