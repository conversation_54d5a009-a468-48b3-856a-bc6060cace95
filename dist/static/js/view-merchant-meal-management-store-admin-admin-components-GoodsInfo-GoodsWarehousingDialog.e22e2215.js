(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-store-admin-admin-components-GoodsInfo-GoodsWarehousingDialog"],{"27b6":function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t._self._c;return e("div",{staticClass:"GoodsWarehousingDialog"},[e("el-dialog",{attrs:{title:"商品入库",visible:t.visible,top:"20vh","custom-class":"ps-dialog","show-close":"","close-on-click-modal":!1},on:{"update:visible":function(e){t.visible=e}}},[e("div",[e("el-table",{staticClass:"ps-table",staticStyle:{width:"100%"},attrs:{data:t.tableData.slice((t.currentPage-1)*t.pageSize,t.currentPage*t.pageSize),border:"","header-row-class-name":"ps-table-header-row"}},[e("el-table-column",{attrs:{prop:"index",label:"序号",width:"70",align:"center"}}),e("el-table-column",{attrs:{prop:"stock_name",label:"商品名称",align:"center"},scopedSlots:t._u([{key:"default",fn:function(n){return[e("el-autocomplete",{ref:"el_auto",staticClass:"inline-input",attrs:{"fetch-suggestions":t.querySearch,"trigger-on-focus":!1,clearable:!0,"popper-class":t.noData?"platform-auto-complete":"","value-key":"stock_name",size:"mini",placeholder:"请输入内容"},on:{input:function(e){return t.changeHandleGoods(e,n.row.index)},select:function(e){return t.handleSubmit(e,n.row.index)},clear:function(e){return t.clearSelect(n.row.index)}},scopedSlots:t._u([{key:"default",fn:function(n){var r=n.item;return t.noData?[e("div",{staticClass:"default"},[t._v(t._s(r.default))])]:void 0}}],null,!0),model:{value:n.row.stock_name,callback:function(e){t.$set(n.row,"stock_name",e)},expression:"scope.row.stock_name"}})]}}])}),e("el-table-column",{attrs:{prop:"spec",label:"规格",align:"center"}}),e("el-table-column",{attrs:{prop:"barcode",label:"条码",align:"center",width:"180"},scopedSlots:t._u([{key:"default",fn:function(n){return[e("div",{staticClass:"ps-flex flex-align-c flex-justify-c"},[e("el-input",{staticClass:"ps-input",attrs:{size:"mini"},on:{input:function(e){return t.changeBarcode(n.row,n.row.index)}},model:{value:n.row.barcode,callback:function(e){t.$set(n.row,"barcode",e)},expression:"scope.row.barcode"}}),e("el-button",{staticClass:"ps-text-blue m-l-10",attrs:{type:"text",size:"small"},on:{click:function(e){return t.clickStockSearch(n.row,n.row.index)}}},[t._v(" 搜索 ")])],1)]}}])}),e("el-table-column",{attrs:{prop:"stock_num",label:"当前库存",align:"center"}}),e("el-table-column",{attrs:{prop:"current_stock_num",label:"当前入库量",align:"center"},scopedSlots:t._u([{key:"default",fn:function(n){return[e("el-input",{staticClass:"ps-input",attrs:{size:"mini",onkeyup:"value=value.replace(/[^\\d]/g,'')",maxlength:"4"},model:{value:n.row.current_stock_num,callback:function(e){t.$set(n.row,"current_stock_num",e)},expression:"scope.row.current_stock_num"}})]}}])}),e("el-table-column",{attrs:{label:"操作",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(n){return[e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:t.clickAddStock}},[t._v(" 新增 ")]),t.tableData.length>1?e("el-button",{staticClass:"ps-warn",attrs:{type:"text",size:"small"},on:{click:function(e){return t.clickDelectStock(n.row)}}},[t._v(" 删除 ")]):t._e()]}}])})],1),e("div",{staticClass:"pageSizeItem ps-pagination"},[e("el-pagination",{staticClass:"ps-text",attrs:{"current-page":t.currentPage,"page-size":t.pageSize,layout:"prev, pager, next, total, jumper",total:t.tableData.length,background:"","popper-class":"ps-popper-select"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange,"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e}}})],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"ps-cancel-btn",on:{click:t.canceDialogHandle}},[t._v("取 消")]),e("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"ps-btn",attrs:{type:"primary"},on:{click:t.clickDetermineWarehousing}},[t._v(" 确定入库 ")])],1)])],1)},o=[],a=n("ed08");function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function c(t,e){return p(t)||f(t,e)||u(t,e)||s()}function s(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(t,e){if(t){if("string"==typeof t)return l(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?l(t,e):void 0}}function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function f(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,a,i,c=[],s=!0,u=!1;try{if(a=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=a.call(n)).done)&&(c.push(r.value),c.length!==e);s=!0);}catch(t){u=!0,o=t}finally{try{if(!s&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(u)throw o}}return c}}function p(t){if(Array.isArray(t))return t}function h(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */h=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},a="function"==typeof Symbol?Symbol:{},c=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function l(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function(t,e,n){return t[e]=n}}function f(t,e,n,r){var a=e&&e.prototype instanceof y?e:y,i=Object.create(a.prototype),c=new G(r||[]);return o(i,"_invoke",{value:j(t,n,c)}),i}function p(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var d="suspendedStart",m="suspendedYield",g="executing",b="completed",v={};function y(){}function k(){}function w(){}var _={};l(_,c,(function(){return this}));var x=Object.getPrototypeOf,S=x&&x(x(z([])));S&&S!==n&&r.call(S,c)&&(_=S);var L=w.prototype=y.prototype=Object.create(_);function D(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function O(t,e){function n(o,a,c,s){var u=p(t[o],t,a);if("throw"!==u.type){var l=u.arg,f=l.value;return f&&"object"==i(f)&&r.call(f,"__await")?e.resolve(f.__await).then((function(t){n("next",t,c,s)}),(function(t){n("throw",t,c,s)})):e.resolve(f).then((function(t){l.value=t,c(l)}),(function(t){return n("throw",t,c,s)}))}s(u.arg)}var a;o(this,"_invoke",{value:function(t,r){function o(){return new e((function(e,o){n(t,r,e,o)}))}return a=a?a.then(o,o):o()}})}function j(e,n,r){var o=d;return function(a,i){if(o===g)throw Error("Generator is already running");if(o===b){if("throw"===a)throw i;return{value:t,done:!0}}for(r.method=a,r.arg=i;;){var c=r.delegate;if(c){var s=P(c,r);if(s){if(s===v)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===d)throw o=b,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=g;var u=p(e,n,r);if("normal"===u.type){if(o=r.done?b:m,u.arg===v)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=b,r.method="throw",r.arg=u.arg)}}}function P(e,n){var r=n.method,o=e.iterator[r];if(o===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,P(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var a=p(o,e.iterator,n.arg);if("throw"===a.type)return n.method="throw",n.arg=a.arg,n.delegate=null,v;var i=a.arg;return i?i.done?(n[e.resultName]=i.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,v):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function C(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function G(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(C,this),this.reset(!0)}function z(e){if(e||""===e){var n=e[c];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function n(){for(;++o<e.length;)if(r.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return a.next=a}}throw new TypeError(i(e)+" is not iterable")}return k.prototype=w,o(L,"constructor",{value:w,configurable:!0}),o(w,"constructor",{value:k,configurable:!0}),k.displayName=l(w,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===k||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,l(t,u,"GeneratorFunction")),t.prototype=Object.create(L),t},e.awrap=function(t){return{__await:t}},D(O.prototype),l(O.prototype,s,(function(){return this})),e.AsyncIterator=O,e.async=function(t,n,r,o,a){void 0===a&&(a=Promise);var i=new O(f(t,n,r,o),a);return e.isGeneratorFunction(n)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},D(L),l(L,u,"Generator"),l(L,c,(function(){return this})),l(L,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=z,G.prototype={constructor:G,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(E),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return c.type="throw",c.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],c=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var s=r.call(i,"catchLoc"),u=r.call(i,"finallyLoc");if(s&&u){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=t,i.arg=e,a?(this.method="next",this.next=a.finallyLoc,v):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),E(n),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;E(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:z(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),v}},e}function d(t,e,n,r,o,a,i){try{var c=t[a](i),s=c.value}catch(t){return void n(t)}c.done?e(s):Promise.resolve(s).then(r,o)}function m(t){return function(){var e=this,n=arguments;return new Promise((function(r,o){var a=t.apply(e,n);function i(t){d(a,r,o,i,c,"next",t)}function c(t){d(a,r,o,i,c,"throw",t)}i(void 0)}))}}function g(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function b(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?g(Object(n),!0).forEach((function(e){v(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):g(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function v(t,e,n){return(e=y(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function y(t){var e=k(t,"string");return"symbol"==i(e)?e:e+""}function k(t,e){if("object"!=i(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=i(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var w={props:{isshow:Boolean,type:{type:String,default:""}},data:function(){return{isLoading:!1,dialogVisible:!1,allGoodsInfos:[],tableData:[{index:1,id:"",stock_name:"",spec:"",barcode:"",stock_num:"",current_stock_num:""}],currentPage:1,pageSize:10,noData:!1}},computed:{visible:{get:function(){return this.isshow},set:function(t){this.$emit("update:isshow",t)}}},mounted:function(){this.initGoodsList()},methods:{getGoodsList:function(t){var e=this;return new Promise((function(n){e.isLoading=!0,e.$apis.apiBackgroundStoreGoodsListPost(b(b({},t),{},{page:1,page_size:99999})).then((function(t){if(e.isLoading=!1,Reflect.has(t,"code")&&0===t.code){var r=t.data||{};n(r.results)}n([])})).catch((function(t){e.isLoading=!1,n([])}))}))},initGoodsList:function(){var t=this;return m(h().mark((function e(){var n;return h().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.getGoodsList();case 2:if(n=e.sent,n.length){e.next=5;break}return e.abrupt("return");case 5:t.allGoodsInfos=n.map((function(t){return t.stock_name=t.name,t}));case 6:case"end":return e.stop()}}),e)})))()},changeHandleGoods:function(t,e){var n={stock_name:t};this.initStockData(e,n)},handleSubmit:function(t,e){if(!t.default){for(var n=0;n<this.tableData.length;n++)if(this.tableData[n].id===t.id)return this.initStockData(e),this.$message.error("当前选择的商品已存在");var r={id:t.id,stock_name:t.stock_name,spec:t.spec,barcode:t.barcode,stock_num:t.stock_num};this.initStockData(e,r)}},querySearch:function(t,e){var n=t?this.allGoodsInfos.filter(this.createFilter(t)):this.allGoodsInfos;this.noData=!1,0===n.length&&(n=[{default:"无匹配数据"}],this.noData=!0),n.length>100&&n.splice(100,n.length-1),e(n)},createFilter:function(t){return function(e){return e.stock_name.toUpperCase().match(t.toUpperCase())}},clearSelect:function(t){this.$refs.el_auto.activated=!0,this.initStockData(t)},changeBarcode:function(t,e){var n={barcode:t.barcode};this.initStockData(e,n)},clickStockSearch:function(t,e){var n=this;return m(h().mark((function r(){var o,a,i,c;return h().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(t.barcode){r.next=2;break}return r.abrupt("return");case 2:return o={full_barcode:t.barcode},r.next=5,n.getGoodsList(o);case 5:if(a=r.sent,a.length){r.next=9;break}return n.$confirm("未找到该条码对应商品，请重新输入","提示",{cancelButtonText:"关闭",closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",showConfirmButton:!1,beforeClose:function(){var t=m(h().mark((function t(e,n,r){return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:"confirm"===e?(n.confirmButtonLoading=!0,r()):n.confirmButtonLoading||r();case 1:case"end":return t.stop()}}),t)})));function e(e,n,r){return t.apply(this,arguments)}return e}()}).then((function(t){})).catch((function(t){})),r.abrupt("return");case 9:i=0;case 10:if(!(i<n.tableData.length)){r.next=17;break}if(n.tableData[i].id!==a[0].id||e===i){r.next=14;break}return n.initStockData(e),r.abrupt("return",n.$message.error("当前选择的商品已存在"));case 14:i++,r.next=10;break;case 17:c={id:a[0].id,stock_name:a[0].name,spec:a[0].spec,barcode:a[0].barcode,stock_num:a[0].stock_num},n.initStockData(e,c);case 19:case"end":return r.stop()}}),r)})))()},initStockData:function(t,e){var n=e||{},r={index:t,id:n.id?n.id:"",stock_name:n.stock_name?n.stock_name:"",spec:n.spec?n.spec:"",barcode:n.barcode?n.barcode:"",stock_num:n.stock_num?n.stock_num:"",current_stock_num:n.current_stock_num?n.current_stock_num:""};this.$set(this.tableData,t-1,r)},setGoodsAddStock:function(t){var e=this;return m(h().mark((function n(){var r,o,i,s;return h().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return e.isLoading=!0,n.next=3,Object(a["Z"])(e.$apis.apiBackgroundStoreGoodsAddStockPost({stock_list:t}));case 3:if(r=n.sent,o=c(r,2),i=o[0],s=o[1],e.isLoading=!1,!i){n.next=11;break}return e.$message.error(i.message),n.abrupt("return");case 11:0===s.code?(e.$emit("determineStock",s.data),e.visible=!1):e.$message.error(s.msg);case 12:case"end":return n.stop()}}),n)})))()},clickDetermineWarehousing:function(){for(var t=0;t<this.tableData.length;t++){var e=this.tableData[t];if(!e.stock_name||!e.barcode||!e.current_stock_num)return this.$message.error("第".concat(e.index,"条，请完善信息"))}var n=this.tableData.map((function(t){return{id:t.id,stock_num:Number(t.current_stock_num)}}));this.setGoodsAddStock(n)},clickAddStock:function(){this.tableData.push({index:this.tableData.length+1,id:"",stock_name:"",spec:"",barcode:"",stock_num:"",current_stock_num:""})},clickDelectStock:function(t){var e=t.index-1;this.tableData.splice(e,1),this.tableData.forEach((function(t,e){t.index=e+1}))},canceDialogHandle:function(){this.visible=!1},handleSizeChange:function(t){this.pageSize=t},handleCurrentChange:function(t){this.currentPage=t}}},_=w,x=(n("a23c"),n("2877")),S=Object(x["a"])(_,r,o,!1,null,"1610f5ee",null);e["default"]=S.exports},"89e1":function(t,e,n){},a23c:function(t,e,n){"use strict";n("89e1")}}]);