(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-merchant-admin-supplier-management-SupplierManagementDetail"],{"0335":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("custom-drawer",t._g(t._b({staticClass:"drawer-wrapper",attrs:{title:"详情",show:t.showDrawer,direction:t.direction,wrapperClosable:t.wrapperClosable,"confirm-text":"确定"},on:{"update:show":function(e){t.showDrawer=e},cancel:t.handlerClose}},"custom-drawer",t.$attrs,!1),t.$listeners),[e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"p-l-20 p-r-20 p-b-20"},[e("div",{staticClass:"tab-box m-b-20"},[e("el-radio-group",{attrs:{size:"small"},on:{input:t.changeTabType},model:{value:t.tabType,callback:function(e){t.tabType=e},expression:"tabType"}},t._l(t.tabList,(function(a){return e("el-radio-button",{key:a.value,attrs:{label:a.value}},[t._v(t._s(a.name))])})),1)],1),0===t.tabType?e("ul",{staticClass:"list-box"},[e("li",{staticClass:"list-item ps-flex-align-c flex-align-c m-b-16"},[e("div",{staticClass:"supper-label"},[t._v("供应商名称：")]),e("div",{staticClass:"supper-value"},[t._v(t._s(t.detailData.name))])]),e("li",{staticClass:"list-item ps-flex-align-c flex-align-c m-b-16"},[e("div",{staticClass:"supper-label"},[t._v("社会统一信用代码：")]),e("div",{staticClass:"supper-value"},[t._v(t._s(t.detailData.credit_code))])]),e("li",{staticClass:"list-item ps-flex-align-c flex-align-c m-b-16"},[e("div",{staticClass:"supper-label"},[t._v("创建时间：")]),e("div",{staticClass:"supper-value"},[t._v(t._s(t.detailData.create_time))])]),e("li",{staticClass:"list-item ps-flex-align-c flex-align-c m-b-16"},[e("div",{staticClass:"supper-label"},[t._v("供应类别：")]),e("div",{staticClass:"supper-value"},[t._v(t._s(t.detailData.supply_category))])]),e("li",{staticClass:"list-item m-b-16"},[e("div",{staticClass:"supper-label"},[t._v("资质信息")]),e("div",{staticClass:"supper-value-img p-20"},t._l(t.detailData.imgList,(function(a,i){return e("div",{key:i,staticClass:"img-box inline-block m-r-20"},[e("el-image",{staticClass:"img-item",attrs:{src:a.url,"preview-src-list":t.computedImgList(t.detailData.imgList),"initial-index":i,fit:"cover"}}),e("div",{staticClass:"img-text text-center"},[t._v(t._s(t.computedType(a.name)))]),e("div",{staticClass:"img-time text-center"},[t._v(t._s(a.time))])],1)})),0)])]):e("div",{},[e("el-table",{ref:"tableData",staticStyle:{width:"100%"},attrs:{data:t.tableData,size:"small",stripe:"","header-row-class-name":"ps-table-header-row"}},t._l(t.tableSettings,(function(a){return e("table-column",{key:a.key,attrs:{col:a},scopedSlots:t._u([{key:"operation",fn:function(a){var i=a.row;return[e("el-button",{staticClass:"ps-text",attrs:{disabled:!i.img_list,type:"text",size:"small"},on:{click:function(e){return t.clickHandler(i)}}},[t._v("查看")])]}}],null,!0)})})),1)],1),e("image-viewer",{attrs:{"initial-index":0,"z-index":3e3,"on-close":t.closeViewer,"preview-src-list":t.previewSrcList},model:{value:t.showViewer,callback:function(e){t.showViewer=e},expression:"showViewer"}})],1)])},n=[],s=a("ed08"),r=a("5a0c"),l=a.n(r);function o(t){return d(t)||p(t)||u(t)||c()}function c(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(t,e){if(t){if("string"==typeof t)return m(t,e);var a={}.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?m(t,e):void 0}}function p(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function d(t){if(Array.isArray(t))return m(t)}function m(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,i=Array(e);a<e;a++)i[a]=t[a];return i}var f={name:"SupplierManagementDetail",components:{},props:{isShow:{required:!0,type:Boolean},wrapperClosable:{type:Boolean,default:!0},direction:{type:String,default:"rtl"},infoData:{type:Object,default:function(){return{}}}},data:function(){return{isLoading:!1,tabType:0,tabList:[{name:"基本信息",value:0},{name:"车辆信息",value:2},{name:"司机信息",value:3},{name:"应用组织",value:4}],tableSettingList:{1:[{label:"组织名",key:"organization_name"},{label:"合同期限",key:"contact_name"},{label:"合同有效期",key:"contact_phone"},{label:"附件",key:"operation",type:"slot",slotName:"operation"}],2:[{label:"车牌号",key:"plate_number"},{label:"车辆类型",key:"car_type_alias"},{label:"图片",key:"img_list",type:"slot",slotName:"operation"}],3:[{label:"司机姓名",key:"name"},{label:"联系方式",key:"number"},{label:"证件信息",key:"img_list",type:"slot",slotName:"operation"}],4:[{label:"所属项目点",key:"company_name"},{label:"所属组织",key:"organization_name"},{label:"联系人姓名",key:"contact_name"},{label:"联系电话",key:"contact_phone"},{label:"供应商地址",key:"address"}]},detailData:{},tableData:[],previewSrcList:[],showViewer:!1}},computed:{showDrawer:{get:function(){return this.isShow},set:function(t){this.$emit("update:isShow",t)}},imageIndex:function(){var t=0,e=this.initialIndex;if(e>=0)return t=e,t;var a=this.previewSrcList.indexOf(this.src);return a>=0?(t=a,t):t},preview:function(){var t=this.previewSrcList;return Array.isArray(t)&&t.length>0},tableSettings:function(){return this.tableSettingList["".concat(this.tabType)]},computedImgList:function(){return function(t){return t.map((function(t){return t.url}))}},computedType:function(){return function(t){switch(t){case"1":return"营业执照";case"2":return"食品经营许可证";case"3":return"食品生产许可证"}}}},watch:{isShow:function(t){t&&this.init()}},created:function(){},mounted:function(){},methods:{init:function(){this.detailData=Object(s["f"])(this.infoData),this.tableData=this.detailData.same_supper_info?this.detailData.same_supper_info:[],this.detailData.imgList=this.infoData.certification_info&&this.infoData.certification_info.length?this.infoData.certification_info.map((function(t){var e={name:t.aptitude,time:t.expiry_date?"".concat(l()(t.expiry_date[0]).format("YYYY-MM-DD")," 至 ").concat(l()(t.expiry_date[1]).format("YYYY-MM-DD")):"--",url:t.imgUrl[0]};return e})):[]},clickCancelHandle:function(){this.showDrawer=!1},clickConfirmHandle:function(){var t=this;this.$refs.formRef.validate((function(e){e&&(t.$emit("confirm",t.formData),t.resetHandle(),t.showDrawer=!1)}))},resetHandle:function(){},clickHandler:function(t){this.previewSrcList=t.img_list||[],this.showViewer=!0},closeViewer:function(){this.showViewer=!1},changeTabType:function(t){switch(this.tableData=[],t){case 0:this.init();break;case 1:this.getContractInformation();break;case 2:this.getCarInfo();break;case 3:this.getDriverInfo();break;case 4:this.getAppOrgInfo();break}},getContractInformation:function(){var t=this;this.$apis.apiBackgroundAdminSupplierManageVendorContractListPost({id:this.infoData.id}).then((function(e){0===e.code||t.$message.error(e.msg)}))},getCarInfo:function(){var t=this;this.$apis.apiBackgroundAdminSupplierManageVendorVehicleInformationListPost({id:this.infoData.id}).then((function(e){0===e.code?t.tableData=e.data.results.map((function(t){return Object.assign(t,{img_list:t.car_img}),t})):t.$message.error(e.msg)}))},getDriverInfo:function(){var t=this;this.$apis.apiBackgroundAdminSupplierManageVendorDriverInformationListPost({id:this.infoData.id}).then((function(e){0===e.code?t.tableData=e.data.results.map((function(t){return Object.assign(t,{img_list:[].concat(o(t.driving_licence),o(t.health_certificate))}),t})):t.$message.error(e.msg)}))},getAppOrgInfo:function(){var t=this;this.$apis.apiBackgroundAdminSupplierManageVendorOrganizationInformationListPost({id:this.infoData.id}).then((function(e){0===e.code?t.tableData=Object(s["f"])(e.data):t.$message.error(e.msg)}))},handlerClose:function(){this.tabType=0,this.showDrawer=!1}}},b=f,h=(a("bbe6"),a("2877")),g=Object(h["a"])(b,i,n,!1,null,null,null);e["default"]=g.exports},"9a49":function(t,e,a){},bbe6:function(t,e,a){"use strict";a("9a49")}}]);