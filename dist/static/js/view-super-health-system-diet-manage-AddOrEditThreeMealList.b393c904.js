(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-diet-manage-AddOrEditThreeMealList","view-super-health-system-threemeallist"],{1436:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"AddOrEditDietPlanList container-wrapper"},[e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v(t._s("add"===t.type?"新建":"编辑")+"三餐推荐")])]),e("div",[e("el-form",{ref:"formRef",attrs:{model:t.formData,rules:t.formRuls,"label-width":"180px"}},[e("el-form-item",{staticClass:"endItem",attrs:{label:"食谱名称：",prop:"name"}},[e("el-input",{staticClass:"ps-input",staticStyle:{"max-width":"400px"},attrs:{type:"textarea",maxlength:"20",placeholder:"不超过20个字","show-word-limit":"",autosize:{minRows:4,maxRows:8}},model:{value:t.formData.name,callback:function(e){t.$set(t.formData,"name",e)},expression:"formData.name"}})],1),e("el-form-item",{staticClass:"endItem",attrs:{label:"食谱简介：",prop:"detail"}},[e("el-input",{staticClass:"ps-input",staticStyle:{"max-width":"400px"},attrs:{type:"textarea",maxlength:"200",placeholder:"不超过200个字","show-word-limit":"",autosize:{minRows:4,maxRows:8}},model:{value:t.formData.detail,callback:function(e){t.$set(t.formData,"detail",e)},expression:"formData.detail"}})],1),e("el-form-item",{staticClass:"endItem",attrs:{label:"餐段：",prop:"meal_type"}},[e("el-radio-group",{staticClass:"ps-radio",model:{value:t.formData.meal_type,callback:function(e){t.$set(t.formData,"meal_type",e)},expression:"formData.meal_type"}},[e("el-radio",{attrs:{label:"breakfast"}},[t._v("早餐")]),e("el-radio",{attrs:{label:"lunch"}},[t._v("午餐")]),e("el-radio",{attrs:{label:"dinner"}},[t._v("晚餐")])],1)],1),e("el-form-item",{attrs:{label:"正餐菜品："}},[e("el-button",{staticClass:"ps-btn float-r",staticStyle:{margin:"0 15px 15px 0"},attrs:{type:"primary",size:"small",icon:"el-icon-plus"},on:{click:t.clickAddFood}},[t._v(" 添加 ")]),e("el-table",{ref:"mainFoods",staticStyle:{width:"100%"},attrs:{data:t.mainFoods,stripe:"","header-row-class-name":"ps-table-header-row"}},t._l(t.tableSettings,(function(r){return e("table-column",{key:r.key,attrs:{col:r},scopedSlots:t._u([{key:"food",fn:function(r){var n=r.row,o=r.index;return[e("virtual-select",{staticClass:"ps-select margin-right",attrs:{width:150,"popover-width":200,placeholder:"请下拉选择",filterable:"","data-list":t.allFoodList,option:{label:"name",value:"id"}},on:{change:function(e){return t.changeFood(e,o)}},model:{value:n.food_id,callback:function(e){t.$set(n,"food_id",e)},expression:"row.food_id"}})]}},{key:"weight",fn:function(r){var n=r.row,o=r.index;return[e("el-input",{staticClass:"ps-input",attrs:{size:"small",placeholder:"请输入重量",disabled:!n.food_id},on:{input:function(e){return t.changeWeight(n,o)}},model:{value:n.weight,callback:function(e){t.$set(n,"weight",e)},expression:"row.weight"}})]}},{key:"operation",fn:function(r){r.row;var n=r.index;return[e("el-button",{staticClass:"ps-red",attrs:{type:"text",size:"small"},on:{click:function(e){return t.clickDelfood(n)}}},[t._v(" 删除 ")])]}}],null,!0)})})),1)],1),e("el-form-item",{attrs:{label:"营养合计："}},[e("span",[t._v("热量："+t._s(t.totalNutrition.energyKcal)+"kcal，")]),e("span",[t._v("碳水化合物："+t._s(t.totalNutrition.carbohydrate)+"g，")]),e("span",[t._v("蛋白质："+t._s(t.totalNutrition.protein)+"g，")]),e("span",[t._v("脂肪："+t._s(t.totalNutrition.axunge)+"g")])]),e("el-form-item",[e("el-button",{staticClass:"ps-origin-btn w-150",attrs:{size:"small",type:"primary"},on:{click:t.submitHandler}},[t._v(" 保存 ")])],1)],1)],1)])])},o=[],a=r("da92"),i=r("ed08"),s=r("dfd5"),l=r("8003");function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function u(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function f(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u(Object(r),!0).forEach((function(e){d(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function d(t,e,r){return(e=h(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function h(t){var e=p(t,"string");return"symbol"==c(e)?e:e+""}function p(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function m(t,e){return w(t)||b(t,e)||g(t,e)||y()}function y(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function g(t,e){if(t){if("string"==typeof t)return v(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?v(t,e):void 0}}function v(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function b(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,a,i,s=[],l=!0,c=!1;try{if(a=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=a.call(r)).done)&&(s.push(n.value),s.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(c)throw o}}return s}}function w(t){if(Array.isArray(t))return t}function _(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */_=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",l=a.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var a=e&&e.prototype instanceof v?e:v,i=Object.create(a.prototype),s=new $(n||[]);return o(i,"_invoke",{value:S(t,r,s)}),i}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var h="suspendedStart",p="suspendedYield",m="executing",y="completed",g={};function v(){}function b(){}function w(){}var x={};u(x,i,(function(){return this}));var k=Object.getPrototypeOf,O=k&&k(k(P([])));O&&O!==r&&n.call(O,i)&&(x=O);var L=w.prototype=v.prototype=Object.create(x);function j(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function F(t,e){function r(o,a,i,s){var l=d(t[o],t,a);if("throw"!==l.type){var u=l.arg,f=u.value;return f&&"object"==c(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,i,s)}),(function(t){r("throw",t,i,s)})):e.resolve(f).then((function(t){u.value=t,i(u)}),(function(t){return r("throw",t,i,s)}))}s(l.arg)}var a;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return a=a?a.then(o,o):o()}})}function S(e,r,n){var o=h;return function(a,i){if(o===m)throw Error("Generator is already running");if(o===y){if("throw"===a)throw i;return{value:t,done:!0}}for(n.method=a,n.arg=i;;){var s=n.delegate;if(s){var l=D(s,n);if(l){if(l===g)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===h)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=m;var c=d(e,r,n);if("normal"===c.type){if(o=n.done?y:p,c.arg===g)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o=y,n.method="throw",n.arg=c.arg)}}}function D(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,D(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var a=d(o,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,g;var i=a.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,g):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function N(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function $(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(N,this),this.reset(!0)}function P(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(c(e)+" is not iterable")}return b.prototype=w,o(L,"constructor",{value:w,configurable:!0}),o(w,"constructor",{value:b,configurable:!0}),b.displayName=u(w,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,u(t,l,"GeneratorFunction")),t.prototype=Object.create(L),t},e.awrap=function(t){return{__await:t}},j(F.prototype),u(F.prototype,s,(function(){return this})),e.AsyncIterator=F,e.async=function(t,r,n,o,a){void 0===a&&(a=Promise);var i=new F(f(t,r,n,o),a);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},j(L),u(L,l,"Generator"),u(L,i,(function(){return this})),u(L,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=P,$.prototype={constructor:$,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(E),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return s.type="throw",s.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],s=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=t,i.arg=e,a?(this.method="next",this.next=a.finallyLoc,g):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),E(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;E(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:P(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),g}},e}function x(t,e,r,n,o,a,i){try{var s=t[a](i),l=s.value}catch(t){return void r(t)}s.done?e(l):Promise.resolve(l).then(n,o)}function k(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var a=t.apply(e,r);function i(t){x(a,n,o,i,s,"next",t)}function s(t){x(a,n,o,i,s,"throw",t)}i(void 0)}))}}var O={name:"AddOrEditThreeMealList",props:{},components:{VirtualSelect:l["a"]},data:function(){return{isLoading:!1,type:"",formData:{name:"",detail:"",meal_type:""},formRuls:{name:[{required:!0,message:"请输入食谱名称",trigger:"blur"}],meal_type:[{required:!0,message:"请选择餐段",trigger:"change"}]},numReg:/^\+?[1-9][0-9]*$/,mainFoods:[],allFoodList:[],tableSettings:[{label:"菜品名称",key:"food",type:"slot",slotName:"food",width:"160"},{label:"菜品每100克热量（kcal）",key:"food_energy_kcal",width:"190"},{label:"推荐重量（g）",key:"weight",type:"slot",slotName:"weight"},{label:"热量（kcal）",key:"energy_kcal"},{label:"碳水化合物（g）",key:"carbohydrate"},{label:"蛋白质（g）",key:"protein"},{label:"脂肪（g）",key:"axunge"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"100"}],nutritionData:{},timer:null}},computed:{totalNutrition:function(){var t={energyKcal:0,carbohydrate:0,protein:0,axunge:0};return this.mainFoods.forEach((function(e){e.energy_kcal&&(t.energyKcal=a["a"].plus(e.energy_kcal,t.energyKcal)),e.carbohydrate&&(t.carbohydrate=a["a"].plus(e.carbohydrate,t.carbohydrate)),e.protein&&(t.protein=a["a"].plus(e.protein,t.protein)),e.axunge&&(t.axunge=a["a"].plus(e.axunge,t.axunge))})),t}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){var t=this;return k(_().mark((function e(){var r,n;return _().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=Object(i["x"])("allFoodList"),JSON.parse(r)&&JSON.parse(r).length){e.next=8;break}return e.next=4,t.getFoodlist();case 4:n=e.sent,t.allFoodList=n.data.results,e.next=9;break;case 8:t.allFoodList=JSON.parse(r);case 9:t.$route.params.type&&(t.type=t.$route.params.type),"modify"===t.type&&t.getThreeMealDetails();case 11:case"end":return e.stop()}}),e)})))()},getThreeMealDetails:function(){var t=this;return k(_().mark((function e(){var r,n,o,a,s;return _().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(i["Z"])(t.$apis.apiBackgroundHealthyThreeMealDetailsPost({id:t.$route.query.id}));case 3:if(r=e.sent,n=m(r,2),o=n[0],a=n[1],t.isLoading=!1,!o){e.next=11;break}return t.$message.error(o.message),e.abrupt("return");case 11:0===a.code?(t.nutritionData={},s=a.data,t.formData.name=s.name,t.formData.detail=s.detail,t.formData.meal_type=s.meal_type,t.mainFoods=s.foods.map((function(e){var r=t.allFoodList.filter((function(t){return t.id===e.food_id}));return t.nutritionData[r[0].id]=r[0],{food_id:e.food_id,weight:e.weight,food_energy_kcal:r[0].nutrition.energy_kcal,energy_kcal:t.countNutrition(r[0].nutrition.energy_kcal,e.weight),carbohydrate:t.countNutrition(r[0].nutrition.carbohydrate,e.weight),protein:t.countNutrition(r[0].nutrition.protein,e.weight),axunge:t.countNutrition(r[0].nutrition.axunge,e.weight)}}))):t.$message.error(a.msg);case 12:case"end":return e.stop()}}),e)})))()},clickAddFood:function(){this.mainFoods.push({food_id:"",weight:"",food_energy_kcal:"",energy_kcal:"",carbohydrate:"",protein:"",axunge:""})},changeFood:function(t,e){this.nutritionData[t.id]=t,this.changeWeight(t,e,"selectFood")},changeWeight:function(t,e,r){var n=Object(i["f"])(this.mainFoods[e]);if("selectFood"===r&&(n.food_energy_kcal=t.nutrition?t.nutrition.energy_kcal:0),n.food_id&&n.weight&&this.numReg.test(Number(n.weight))){var o=this.nutritionData[n.food_id];n.energy_kcal=this.countNutrition(o.nutrition.energy_kcal,n.weight),n.carbohydrate=this.countNutrition(o.nutrition.carbohydrate,n.weight),n.protein=this.countNutrition(o.nutrition.protein,n.weight),n.axunge=this.countNutrition(o.nutrition.axunge,n.weight)}else n.energy_kcal="",n.carbohydrate="",n.protein="",n.axunge="";this.$set(this.mainFoods,e,n)},clickDelfood:function(t){this.mainFoods.splice(t,1)},submitHandler:function(){var t=this;this.$refs.formRef.validate((function(e){if(e){if(t.isLoading)return t.$message.error("请勿重复提交！");var r={name:t.formData.name,detail:t.formData.detail,meal_type:t.formData.meal_type,foods:[]};if(!t.mainFoods.length)return t.$message.error("请选择正餐菜品");for(var n=0;n<t.mainFoods.length;n++){var o=t.mainFoods[n];if(!o.food_id)return t.$message.error("正餐菜品：第".concat(n+1,"条数据,请选择菜品"));if(!o.weight||!t.numReg.test(Number(o.weight)))return t.$message.error("正餐菜品：第".concat(n+1,"条数据,请输入重量"));o.food_id&&o.weight&&r.foods.push({food_id:o.food_id,weight:o.weight})}t.addModifyThreeMeal(r)}}))},addModifyThreeMeal:function(t){var e=this;return k(_().mark((function r(){var n,o,a,s,l,c,u,d;return _().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(e.isLoading=!0,n="",o=m(n,2),a=o[0],s=o[1],"add"!==e.type){r.next=12;break}return r.next=6,Object(i["Z"])(e.$apis.apiBackgroundHealthyThreeMealAddPost(t));case 6:l=r.sent,c=m(l,2),a=c[0],s=c[1],r.next=19;break;case 12:return r.next=15,Object(i["Z"])(e.$apis.apiBackgroundHealthyThreeMealModifyPost(f({id:e.$route.query.id},t)));case 15:u=r.sent,d=m(u,2),a=d[0],s=d[1];case 19:if(e.isLoading=!1,!a){r.next=23;break}return e.$message.error(a.message),r.abrupt("return");case 23:0===s.code?(e.$message.success(s.msg),e.$closeCurrentTab(e.$route.path)):e.$message.error(s.msg);case 24:case"end":return r.stop()}}),r)})))()},countNutrition:function(t,e){var r=a["a"].divide(t,100);return a["a"].times(r,e).toFixed(2)},getFoodlist:s["getFoodlist"]}},L=O,j=(r("44a0"),r("2877")),F=Object(j["a"])(L,n,o,!1,null,"68db17b8",null);e["default"]=F.exports},"44a0":function(t,e,r){"use strict";r("93e9")},"93e9":function(t,e,r){},dfd5:function(t,e,r){"use strict";function n(){var t=this;return new Promise((function(e,r){t.$apis.apiBackgroundAdminFoodListPost({page:1,page_size:999999}).then((function(t){sessionStorage.setItem("allFoodList",t.data.results?JSON.stringify(t.data.results):"[]"),e(t)})).catch((function(t){r(t)}))}))}r.r(e),r.d(e,"getFoodlist",(function(){return n}))}}]);