(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-parameter-config-health-rule-components-FoodDiversityConfig"],{"818c":function(t,e,s){},"9b74":function(t,e,s){"use strict";s("818c")},e989:function(t,e,s){"use strict";s.r(e);var o=function(){var t=this,e=t._self._c;return e("div",{staticClass:"food-diversity"},[e("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formRef",attrs:{rules:t.formRuls,model:t.formData,size:"small"}},[e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("基本信息")])]),e("div",{staticStyle:{"max-width":"50%",padding:"0 20px"}},[e("el-form-item",{staticClass:"block-label",attrs:{label:"维度分值",prop:"score"}},[e("el-input",{staticClass:"ps-input",attrs:{disabled:t.disabled,placeholder:"请输入维度分值"},model:{value:t.formData.score,callback:function(e){t.$set(t.formData,"score",e)},expression:"formData.score"}},[e("template",{slot:"append"},[t._v("分")])],2)],1),e("el-form-item",{staticClass:"block-label",attrs:{label:"提示语",prop:"tips"}},[e("el-input",{staticClass:"ps-input",attrs:{type:"textarea",autosize:{minRows:4,maxRows:6},placeholder:"请输入提示语",disabled:t.disabled},model:{value:t.formData.tips,callback:function(e){t.$set(t.formData,"tips",e)},expression:"formData.tips"}})],1),t._l(t.formData.config,(function(s,o,a){return e("div",{key:a,staticClass:"form-content-box m-b-20"},[e("div",[t._v("变量值")]),t._l(s.listText,(function(s,o){return e("div",{key:o,staticClass:"p-t-10"},[t._v(" "+t._s(s.text)+" "),e("span",{staticStyle:{color:"red"}},[t._v(t._s(s.tips))])])})),e("div",{staticClass:"p-t-10 p-b-10 flex-between"},[e("div",[t._v("规则配置")]),t.disabled?t._e():e("div",{staticClass:"align-r"},[e("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(e){return t.addRule(s,o)}}},[t._v(" 新增规则 ")])],1)]),e("div",{staticClass:"ps-flex-align-c"},[e("span",{staticClass:"p-r-10 p-t-5"},[t._v("如果")]),e("div",t._l(s.content,(function(a,i){return e("div",{key:i,staticClass:"ps-flex-align-c flex-wrap"},[e("span",{staticClass:"p-r-10 p-t-5"},[t._v(t._s(s.unitText))]),e("el-form-item",{attrs:{label:"",prop:"config."+o+".content."+i+".comparison",rules:{required:!0,message:"请选择",trigger:"blur"}}},[e("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:a.comparison,callback:function(e){t.$set(a,"comparison",e)},expression:"contentItem.comparison"}},t._l(t.comparisonList,(function(t,s){return e("el-option",{key:s,attrs:{label:t.label,value:t.value}})})),1)],1),e("div",{staticClass:"p-l-10"},[e("el-form-item",{attrs:{label:"",prop:"config."+o+".content."+i+".comparison_score",rules:{required:!0,message:"请输入",trigger:"blur"}}},[e("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"100px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:a.comparison_score,callback:function(e){t.$set(a,"comparison_score",e)},expression:"contentItem.comparison_score"}})],1)],1),e("div",{staticClass:"p-t-5 p-r-10"},[t._v("执行")]),e("el-form-item",{attrs:{label:"",prop:"config."+o+".content."+i+".operation",rules:{required:!0,message:"请选择",trigger:"blur"}}},[e("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:a.operation,callback:function(e){t.$set(a,"operation",e)},expression:"contentItem.operation"}},t._l(t.operationList,(function(t,s){return e("el-option",{key:s,attrs:{label:t.label,value:t.value}})})),1)],1),e("div",{staticClass:"p-l-10"},[e("el-form-item",{attrs:{label:"",prop:"config."+o+".content."+i+".operation_score",rules:{required:!0,message:"请输入",trigger:"blur"}}},[e("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"100px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:a.operation_score,callback:function(e){t.$set(a,"operation_score",e)},expression:"contentItem.operation_score"}})],1)],1),e("div",{staticClass:"p-t-5 p-r-10"},[t._v("分")]),e("div",{staticClass:"m-b-30"},[s.content.length>1&&!t.disabled?e("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.removeRule(a,o,i)}}},[t._v(" 删除 ")]):t._e()],1)],1)})),0)])],2)}))],2)]),e("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[e("el-button",{staticStyle:{width:"120px"},on:{click:t.closeHandler}},[t._v("取消")]),e("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:t.submitHandler}},[t._v(" "+t._s("see"===t.type?"返回":"编辑")+" ")])],1)])],1)},a=[],i={props:{type:String,data:Object,disabled:Boolean},data:function(){return{isLoading:!1,formData:{score:"",tips:"",config:{diversity:{listText:[{text:"x种",tips:"（用户每餐食物多样摄入种类）"}],unitText:"x",content:[{comparison:"",comparison_score:"",operation:"",operation_score:""}]},cereals:{listText:[{text:"x1克/餐",tips:"（用户每餐谷物摄入量）"},{text:"y1克/餐",tips:"（查表法计算得出每餐谷物推荐摄入量）"}],unitText:"x1正负",content:[{comparison:"",comparison_score:"",operation:"",operation_score:""}]},eggsandmeat:{listText:[{text:"x2克/餐",tips:"（用户每餐鱼禽蛋肉摄入量）"},{text:"y2克/餐",tips:"（查表法计算得出用户每餐鱼禽蛋肉摄入量）"}],unitText:"x2正负",content:[{comparison:"",comparison_score:"",operation:"",operation_score:""}]},fruit:{listText:[{text:"x3克/餐",tips:"（用户每餐水果摄入量）"},{text:"y3克/餐",tips:"（查表计算得出用户每餐水果摄入量）"}],unitText:"x3正负",content:[{comparison:"",comparison_score:"",operation:"",operation_score:""},{comparison:"",comparison_score:"",operation:"",operation_score:""}]},vegetable:{listText:[{text:"x4克/餐",tips:"（用户每餐蔬菜摄入量）"},{text:"y4克/餐",tips:"（查表计算得出用户每餐蔬菜摄入量）"}],unitText:"x4正负",content:[{comparison:"",comparison_score:"",operation:"",operation_score:""},{comparison:"",comparison_score:"",operation:"",operation_score:""}]}}},formRuls:{},comparisonList:[{label:"等于",value:"=="},{label:"大于",value:">"},{label:"大于等于",value:">="},{label:"小于",value:"<"},{label:"小于等于",value:"<="}],operationList:[{label:"加",value:"+"},{label:"减",value:"-"}]}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){for(var t in this.formData.score=this.data.score,this.formData.tips=this.data.tips,this.formData.type=this.data.type,this.data.config)this.formData.config[t].content=this.data.config[t]},addRule:function(t,e){this.formData.config[e].content.push({comparison:"",comparison_score:"",operation:"",operation_score:""})},removeRule:function(t,e,s){this.formData.config[e].content.splice(s,1)},submitHandler:function(){var t=this;this.$refs.formRef.validate((function(e){if(e){if(t.isLoading)return t.$message.error("请勿重复提交！");var s={key:t.formData.type},o={};for(var a in t.formData.config)o[a]=t.formData.config[a].content;s[t.formData.type]={score:t.formData.score,tips:t.formData.tips,config:o},t.$emit("submitHandler",s)}}))},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,s,o){"confirm"===e?t.$closeCurrentTab(t.$route.path):s.confirmButtonLoading||o()}}).then((function(t){})).catch((function(t){}))}}},n=i,r=(s("9b74"),s("2877")),l=Object(r["a"])(n,o,a,!1,null,"cfeeba88",null);e["default"]=l.exports}}]);