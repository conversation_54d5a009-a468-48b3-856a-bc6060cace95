(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-article-push-article-admin-articleAdmin","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-meal-report-MealPackageRule"],{"5866d":function(e,t,r){e.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}},"97dd":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"ArticleAdmin container-wrapper"},[t("refresh-tool",{on:{refreshPage:e.refreshHandle}}),t("search-form",{ref:"searchRef",attrs:{"form-setting":e.searchFormSetting},on:{search:e.searchHandle}}),t("div",{staticClass:"table-wrapper"},[t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[e._v("数据列表")]),t("div",{staticClass:"align-r"},[t("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(t){return e.gotoArticle("add")}}},[e._v("添加文章")])],1)]),t("div",{staticClass:"table-content"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticClass:"ps-table-tree",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","header-row-class-name":"ps-table-header-row","tree-props":{children:"children_list",hasChildren:"has_children"}}},[t("el-table-column",{attrs:{prop:"title",label:"文章标题",align:"center"}}),t("el-table-column",{attrs:{prop:"image",label:"封面",align:"center"},scopedSlots:e._u([{key:"default",fn:function(e){return[t("el-image",{staticStyle:{width:"100px",height:"100px"},attrs:{src:e.row.image?e.row.image:"https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/5067c24dac9548be9ebe8f29d88d3dad1688112556266.jpg","preview-src-list":[e.row.image]}})]}}])}),t("el-table-column",{attrs:{prop:"source_alias",label:"来源",align:"center"}}),t("el-table-column",{attrs:{prop:"is_recommend",label:"是否推荐",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("div",[e._v(" "+e._s(r.row.is_recommend?"是":"否")+" ")])]}}])}),t("el-table-column",{attrs:{prop:"categorys_all",label:"分类",align:"center","show-overflow-tooltip":!0}}),t("el-table-column",{attrs:{prop:"author",label:"作者",align:"center"}}),t("el-table-column",{attrs:{prop:"read_number",label:"阅读量",align:"center"}}),t("el-table-column",{attrs:{prop:"create_time",label:"创建时间",align:"center"}}),t("el-table-column",{attrs:{prop:"release_time",label:"发布时间",align:"center"}}),t("el-table-column",{attrs:{prop:"operator_name",label:"操作人",align:"center"}}),t("el-table-column",{attrs:{fixed:"right",label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.gotoArticle("modify",r.row)}}},[e._v(" 编辑 ")]),r.row.is_release?e._e():t("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.clickIsRelease(!0,r.row)}}},[e._v(" 发布 ")]),r.row.is_release?t("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.clickIsRelease(!1,r.row)}}},[e._v(" 取消发布 ")]):e._e(),t("span",{staticStyle:{margin:"0 10px",color:"#e2e8f0"}},[e._v("|")]),t("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.deleteHandler("single",r.row.id)}}},[e._v(" 删除 ")])]}}])})],1)],1),t("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[t("el-pagination",{staticClass:"ps-text",attrs:{"page-size":e.pageSize,layout:"total, prev, pager, next",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"current-change":e.handleCurrentChange}})],1)])],1)},a=[],i=r("ed08");r("c9d9");function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach((function(t){s(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function s(e,t,r){return(t=u(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function u(e){var t=f(e,"string");return"symbol"==o(t)?t:t+""}function f(e,t){if("object"!=o(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function d(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */d=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},l=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function f(e,t,r,n){var i=t&&t.prototype instanceof b?t:b,o=Object.create(i.prototype),l=new B(n||[]);return a(o,"_invoke",{value:j(e,r,l)}),o}function p(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var m="suspendedStart",h="suspendedYield",g="executing",y="completed",v={};function b(){}function w(){}function _(){}var k={};u(k,l,(function(){return this}));var x=Object.getPrototypeOf,L=x&&x(x($([])));L&&L!==r&&n.call(L,l)&&(k=L);var A=_.prototype=b.prototype=Object.create(k);function C(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function O(e,t){function r(a,i,l,c){var s=p(e[a],e,i);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==o(f)&&n.call(f,"__await")?t.resolve(f.__await).then((function(e){r("next",e,l,c)}),(function(e){r("throw",e,l,c)})):t.resolve(f).then((function(e){u.value=e,l(u)}),(function(e){return r("throw",e,l,c)}))}c(s.arg)}var i;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){r(e,n,t,a)}))}return i=i?i.then(a,a):a()}})}function j(t,r,n){var a=m;return function(i,o){if(a===g)throw Error("Generator is already running");if(a===y){if("throw"===i)throw o;return{value:e,done:!0}}for(n.method=i,n.arg=o;;){var l=n.delegate;if(l){var c=S(l,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===m)throw a=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=g;var s=p(t,r,n);if("normal"===s.type){if(a=n.done?y:h,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(a=y,n.method="throw",n.arg=s.arg)}}}function S(t,r){var n=r.method,a=t.iterator[n];if(a===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,S(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=p(a,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var o=i.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,v):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function P(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function B(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(P,this),this.reset(!0)}function $(t){if(t||""===t){var r=t[l];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,i=function r(){for(;++a<t.length;)if(n.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(o(t)+" is not iterable")}return w.prototype=_,a(A,"constructor",{value:_,configurable:!0}),a(_,"constructor",{value:w,configurable:!0}),w.displayName=u(_,s,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,u(e,s,"GeneratorFunction")),e.prototype=Object.create(A),e},t.awrap=function(e){return{__await:e}},C(O.prototype),u(O.prototype,c,(function(){return this})),t.AsyncIterator=O,t.async=function(e,r,n,a,i){void 0===i&&(i=Promise);var o=new O(f(e,r,n,a),i);return t.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},C(A),u(A,s,"Generator"),u(A,l,(function(){return this})),u(A,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=$,B.prototype={constructor:B,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(E),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(n,a){return l.type="throw",l.arg=t,r.next=n,a&&(r.method="next",r.arg=e),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],l=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var c=n.call(o,"catchLoc"),s=n.call(o,"finallyLoc");if(c&&s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),E(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;E(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:$(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),v}},t}function p(e,t){return v(e)||y(e,t)||h(e,t)||m()}function m(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function h(e,t){if(e){if("string"==typeof e)return g(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?g(e,t):void 0}}function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function y(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,i,o,l=[],c=!0,s=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw a}}return l}}function v(e){if(Array.isArray(e))return e}function b(e,t,r,n,a,i,o){try{var l=e[i](o),c=l.value}catch(e){return void r(e)}l.done?t(c):Promise.resolve(c).then(n,a)}function w(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){b(i,n,a,o,l,"next",e)}function l(e){b(i,n,a,o,l,"throw",e)}o(void 0)}))}}var _={name:"ArticleAdmin",components:{},props:{},data:function(){return{isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[],searchFormSetting:{date_type:{type:"select",value:"create_time",maxWidth:"130px",dataList:[{label:"创建时间",value:"create_time"},{label:"发布时间",value:"release_time"}]},select_time:{type:"daterange",label:"",clearable:!1,value:[]},title:{type:"input",label:"标题",value:"",placeholder:"请输入标题"},source:{type:"select",value:"",label:"来源",placeholder:"请选择",listNameKey:"name",listValueKey:"id",multiple:!1,collapseTags:!0,dataList:[{name:"原创",id:"1"},{name:"转载",id:"2"}]},is_recommend:{type:"select",value:"",label:"是否推荐",placeholder:"请选择",listNameKey:"name",listValueKey:"id",multiple:!1,collapseTags:!0,dataList:[{name:"是",id:!0},{name:"否",id:!1}]},categorys:{type:"select",value:[],label:"分类",placeholder:"请选择",listNameKey:"name",listValueKey:"id",multiple:!0,collapseTags:!0,dataList:[]},operator:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"}}}},created:function(){this.getArticleChildTagList(),this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getArticleList()},searchHandle:Object(i["d"])((function(){this.currentPage=1,this.initLoad()}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.initLoad()},getArticleChildTagList:function(){var e=this;return w(d().mark((function t(){var r,n,a,o;return d().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(i["Z"])(e.$apis.apiBackgroundAdminArticleCategoryListPost({page:1,page_size:9999}));case 2:if(r=t.sent,n=p(r,2),a=n[0],o=n[1],!a){t.next=9;break}return e.$message.error(a.message),t.abrupt("return");case 9:0===o.code?e.searchFormSetting.categorys.dataList=o.data.results:e.$message.error(o.msg);case 10:case"end":return t.stop()}}),t)})))()},getArticleList:function(){var e=this;return w(d().mark((function t(){var r,n,a,o;return d().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(i["Z"])(e.$apis.apiBackgroundAdminArticleListPost(c(c({},e.formatQueryParams(e.searchFormSetting)),{},{page:e.currentPage,page_size:e.pageSize})));case 3:if(r=t.sent,n=p(r,2),a=n[0],o=n[1],e.isLoading=!1,!a){t.next=11;break}return e.$message.error(a.message),t.abrupt("return");case 11:0===o.code?(e.totalCount=o.data.count,e.tableData=o.data.results.map((function(e){var t=[];return e.categorys.map((function(e){t.push(e.name)})),e.categorys_all=t.join("、"),e}))):e.$message.error(o.msg);case 12:case"end":return t.stop()}}),t)})))()},getArticleChangeRelease:function(e){var t=this;return w(d().mark((function r(){var n,a,o,l;return d().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return t.isLoading=!0,r.next=3,Object(i["Z"])(t.$apis.apiBackgroundAdminArticleChangeReleasePost(e));case 3:if(n=r.sent,a=p(n,2),o=a[0],l=a[1],t.isLoading=!1,!o){r.next=11;break}return t.$message.error(o.message),r.abrupt("return");case 11:0===l.code?t.getArticleList():t.$message.error(l.msg);case 12:case"end":return r.stop()}}),r)})))()},clickIsRelease:function(e,t){var r={is_release:e,id:t.id};this.getArticleChangeRelease(r)},gotoArticle:function(e,t){this.$router.push({name:"SuperAddEditArticle",query:{type:e,data:this.$encodeQuery(t)}})},gotoArticleCategory:function(){this.$router.push({name:"SuperArticleCategory"})},deleteHandler:function(e,t){var r=this;this.$confirm("确定删除？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-warn",center:!0,beforeClose:function(){var e=w(d().mark((function e(n,a,o){var l,c,s,u;return d().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("confirm"!==n){e.next=19;break}if(!r.dialogLoading){e.next=3;break}return e.abrupt("return",r.$message.error("请勿重复提交！"));case 3:return r.dialogLoading=!0,a.confirmButtonLoading=!0,e.next=7,Object(i["Z"])(r.$apis.apiBackgroundAdminArticleDeletePost({ids:[t]}));case 7:if(l=e.sent,c=p(l,2),s=c[0],u=c[1],r.dialogLoading=!1,!s){e.next=15;break}return r.$message.error(s.message),e.abrupt("return");case 15:0===u.code?(o(),r.$message.success(u.msg),r.getArticleList()):r.$message.error(u.msg),a.confirmButtonLoading=!1,e.next=20;break;case 19:a.confirmButtonLoading||o();case 20:case"end":return e.stop()}}),e)})));function n(t,r,n){return e.apply(this,arguments)}return n}()}).then((function(e){})).catch((function(e){}))},handleSizeChange:function(e){this.pageSize=e,this.getAccountList()},handleCurrentChange:function(e){this.currentPage=e,this.getArticleList()},formatQueryParams:function(e){var t={};for(var r in e)""!==e[r].value&&("select_date"!==r?t[r]=e[r].value:e[r].value.length>0&&(t.start_date=e[r].value[0],t.end_date=e[r].value[1]));return t}}},k=_,x=(r("ad48"),r("2877")),L=Object(x["a"])(k,n,a,!1,null,null,null);t["default"]=L.exports},ad48:function(e,t,r){"use strict";r("5866d")},c9d9:function(e,t,r){"use strict";r.d(t,"a",(function(){return i})),r.d(t,"d",(function(){return o})),r.d(t,"b",(function(){return l})),r.d(t,"c",(function(){return c})),r.d(t,"e",(function(){return s})),r.d(t,"f",(function(){return u})),r.d(t,"g",(function(){return f}));var n=r("5a0c"),a=r("da92"),i=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],o=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],l={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},c=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],s=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],u=(n().subtract(7,"day").format("YYYY-MM-DD"),n().format("YYYY-MM-DD"),function(e){return e?"number"===typeof e?a["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:a["a"].divide(e,100).toFixed(2):"0.00"}),f=function(e){return a["a"].times(e,100)}}}]);