(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-member-center-MemberPermissionManager","view-super-health-system-member-center-constants"],{"6c55":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"permisson-manager container-wrapper"},[t("refresh-tool",{on:{refreshPage:e.refreshPage}}),t("search-form",{ref:"searchRef",attrs:{"form-setting":e.searchFormSetting},on:{search:e.search<PERSON>andler,reset:e.reset<PERSON><PERSON><PERSON>}}),t("div",{staticClass:"table-wrapper"},[t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[e._v("数据列表")]),t("div",{staticClass:"align-r"},[t("button-icon",{attrs:{color:"origin"},on:{click:function(t){return e.manageProjectSite()}}},[e._v("项目点管理")]),t("button-icon",{attrs:{color:"origin"},on:{click:function(t){return e.handlerDetailKeyManager()}}},[e._v("键值管理")]),t("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(t){return e.handlerAddorEditRecord("add")}}},[e._v("新增")])],1)]),t("div",{staticClass:"table-content m-t-20"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData,height:"460",stripe:"","header-row-class-name":"ps-table-header-row"}},e._l(e.tableSettings,(function(r){return t("table-column",{key:r.key,attrs:{col:r},scopedSlots:e._u([{key:"image",fn:function(r){var a=r.row;return[t("el-image",{staticClass:"img-item",attrs:{src:a.icon_url,fit:"fit","preview-src-list":e.getPreViewList(a)}},[t("div",{staticClass:"image-slot m-t-20",attrs:{slot:"error"},slot:"error"},[t("i",{staticClass:"el-icon-picture-outline"})])])]}},{key:"status",fn:function(r){var a=r.row;return[t("el-switch",{directives:[{name:"loading",rawName:"v-loading",value:a.isSwitchLoading,expression:"row.isSwitchLoading"}],attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},on:{change:function(t){return e.memberStatusChange(t,a,a.index)}},model:{value:a.is_enable,callback:function(t){e.$set(a,"is_enable",t)},expression:"row.is_enable"}})]}},{key:"associateUrl",fn:function(t){var r=t.row;return[e._v(" "+e._s(e.getUrlByType(r))+" ")]}},{key:"operation",fn:function(r){var a=r.row;return[t("el-button",{staticClass:"ps-text-origin",attrs:{type:"text",size:"small"},on:{click:function(t){return e.handlerAddorEditRecord("edit",a)}}},[e._v("编辑")]),t("el-button",{staticClass:"ps-text-warn",attrs:{type:"text",size:"small"},on:{click:function(t){return e.handlerDeleteRecord(a)}}},[e._v("删除")])]}}],null,!0)})})),1)],1),t("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[t("pagination",{attrs:{"current-page":e.currentPage,"page-size":e.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:e.totalCount},on:{"size-change":e.handlerSizeChange,"current-change":e.handlerPageChange,"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t},"update:pageSize":function(t){e.pageSize=t},"update:page-size":function(t){e.pageSize=t}}})],1)]),t("member-key-or-permiss-dialog",{ref:"permissDialog",attrs:{isshow:e.dialogVisible,title:e.dialogTitle,type:e.type,dialogType:e.dialogType,selectInfo:e.selectInfo,confirm:e.handlerConfirm},on:{"update:isshow":function(t){e.dialogVisible=t}}}),t("dialog-message",{attrs:{width:"470px",title:"项目点管理",show:e.manageProjectSiteDialogShow,customClass:"expire-dialog",showFooter:!1},on:{"update:show":function(t){e.manageProjectSiteDialogShow=t},close:e.handleClose},scopedSlots:e._u([{key:"tool",fn:function(){return[t("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"}},[t("el-button",{staticClass:"ps-cancel-btn renew-btn",on:{click:e.handleClose}},[e._v("取消")]),t("el-button",{staticClass:"ps-btn",on:{click:e.confirm}},[e._v("确定")])],1)]},proxy:!0}])},[[t("el-form",{ref:"manageProjectSiteForm",attrs:{model:e.manageProjectSiteForm,"label-width":"100px",inline:""}},[t("el-form-item",{attrs:{label:"项目点名称"}},[t("company-select",{ref:"companySelect",staticClass:"search-item-w ps-select",attrs:{options:e.companyOpts,"collapse-tags":!0,clearable:!0,multiple:!0,isSelectAll:e.isSelectAll,placeholder:"请选择"},on:{selectAll:e.getSelectAll},model:{value:e.manageProjectSiteForm.projectList,callback:function(t){e.$set(e.manageProjectSiteForm,"projectList",t)},expression:"manageProjectSiteForm.projectList"}})],1),t("el-form-item",[t("el-checkbox",{on:{change:e.selectAll},model:{value:e.isSelectAll,callback:function(t){e.isSelectAll=t},expression:"isSelectAll"}},[e._v("全选")])],1)],1)]],2)],1)},n=[],i=r("ed08"),o=r("c8c2"),l=r("6e71"),s=r("f3be");function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach((function(t){p(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function p(e,t,r){return(t=f(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function f(e){var t=y(e,"string");return"symbol"==c(t)?t:t+""}function y(e,t){if("object"!=c(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=c(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function d(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */d=function(){return t};var e,t={},r=Object.prototype,a=r.hasOwnProperty,n=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function m(e,t,r,a){var i=t&&t.prototype instanceof v?t:v,o=Object.create(i.prototype),l=new T(a||[]);return n(o,"_invoke",{value:j(e,r,l)}),o}function p(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=m;var f="suspendedStart",y="suspendedYield",h="executing",b="completed",g={};function v(){}function _(){}function w(){}var k={};u(k,o,(function(){return this}));var S=Object.getPrototypeOf,A=S&&S(S(C([])));A&&A!==r&&a.call(A,o)&&(k=A);var E=w.prototype=v.prototype=Object.create(k);function P(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function O(e,t){function r(n,i,o,l){var s=p(e[n],e,i);if("throw"!==s.type){var u=s.arg,m=u.value;return m&&"object"==c(m)&&a.call(m,"__await")?t.resolve(m.__await).then((function(e){r("next",e,o,l)}),(function(e){r("throw",e,o,l)})):t.resolve(m).then((function(e){u.value=e,o(u)}),(function(e){return r("throw",e,o,l)}))}l(s.arg)}var i;n(this,"_invoke",{value:function(e,a){function n(){return new t((function(t,n){r(e,a,t,n)}))}return i=i?i.then(n,n):n()}})}function j(t,r,a){var n=f;return function(i,o){if(n===h)throw Error("Generator is already running");if(n===b){if("throw"===i)throw o;return{value:e,done:!0}}for(a.method=i,a.arg=o;;){var l=a.delegate;if(l){var s=L(l,a);if(s){if(s===g)continue;return s}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(n===f)throw n=b,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);n=h;var c=p(t,r,a);if("normal"===c.type){if(n=a.done?b:y,c.arg===g)continue;return{value:c.arg,done:a.done}}"throw"===c.type&&(n=b,a.method="throw",a.arg=c.arg)}}}function L(t,r){var a=r.method,n=t.iterator[a];if(n===e)return r.delegate=null,"throw"===a&&t.iterator.return&&(r.method="return",r.arg=e,L(t,r),"throw"===r.method)||"return"!==a&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+a+"' method")),g;var i=p(n,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,g;var o=i.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,g):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function D(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function x(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function T(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(D,this),this.reset(!0)}function C(t){if(t||""===t){var r=t[o];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function r(){for(;++n<t.length;)if(a.call(t,n))return r.value=t[n],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(c(t)+" is not iterable")}return _.prototype=w,n(E,"constructor",{value:w,configurable:!0}),n(w,"constructor",{value:_,configurable:!0}),_.displayName=u(w,s,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===_||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,u(e,s,"GeneratorFunction")),e.prototype=Object.create(E),e},t.awrap=function(e){return{__await:e}},P(O.prototype),u(O.prototype,l,(function(){return this})),t.AsyncIterator=O,t.async=function(e,r,a,n,i){void 0===i&&(i=Promise);var o=new O(m(e,r,a,n),i);return t.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},P(E),u(E,s,"Generator"),u(E,o,(function(){return this})),u(E,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var a in t)r.push(a);return r.reverse(),function e(){for(;r.length;){var a=r.pop();if(a in t)return e.value=a,e.done=!1,e}return e.done=!0,e}},t.values=C,T.prototype={constructor:T,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(x),!t)for(var r in this)"t"===r.charAt(0)&&a.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(a,n){return l.type="throw",l.arg=t,r.next=a,n&&(r.method="next",r.arg=e),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],l=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var s=a.call(o,"catchLoc"),c=a.call(o,"finallyLoc");if(s&&c){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,g):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),x(r),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var a=r.completion;if("throw"===a.type){var n=a.arg;x(r)}return n}}throw Error("illegal catch attempt")},delegateYield:function(t,r,a){return this.delegate={iterator:C(t),resultName:r,nextLoc:a},"next"===this.method&&(this.arg=e),g}},t}function h(e,t){return w(e)||_(e,t)||g(e,t)||b()}function b(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function g(e,t){if(e){if("string"==typeof e)return v(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?v(e,t):void 0}}function v(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,a=Array(t);r<t;r++)a[r]=e[r];return a}function _(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var a,n,i,o,l=[],s=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;s=!1}else for(;!(s=(a=i.call(r)).done)&&(l.push(a.value),l.length!==t);s=!0);}catch(e){c=!0,n=e}finally{try{if(!s&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw n}}return l}}function w(e){if(Array.isArray(e))return e}function k(e,t,r,a,n,i,o){try{var l=e[i](o),s=l.value}catch(e){return void r(e)}l.done?t(s):Promise.resolve(s).then(a,n)}function S(e){return function(){var t=this,r=arguments;return new Promise((function(a,n){var i=e.apply(t,r);function o(e){k(i,a,n,o,l,"next",e)}function l(e){k(i,a,n,o,l,"throw",e)}o(void 0)}))}}var A={name:"MemberPermissionManager",data:function(){return{searchFormSetting:Object(i["f"])(o["SEARCH_FORM_PERMISSION_MANAGER_DATA"]),tableSettings:Object(i["f"])(o["TABLE_HEAD_PERMISSION_MANAGER_DATA"]),tableData:[],isLoading:!1,currentPage:1,pageSize:10,totalCount:0,dialogVisible:!1,dialogTitle:"",dialogType:"permission",type:"add",selectInfo:{},srcList:[],manageProjectSiteDialogShow:!1,manageProjectSiteForm:{projectList:[]},companyOpts:{label:"name",value:"company"},isSelectAll:!1,defaultProjectList:[]}},components:{MemberKeyOrPermissDialog:s["default"],CompanySelect:l["a"]},created:function(){this.initData()},methods:{initData:function(){this.getDataList(),this.getMemberOnDetail()},searchHandler:Object(i["d"])((function(){this.dialogVisible=!1,this.currentPage=1,this.getDataList()}),300),refreshPage:function(){this.dialogVisible=!1,this.currentPage=1,this.$refs.searchRef&&this.$refs.searchRef.resetForm(),this.getDataList()},resetHandler:function(){this.currentPage=1,this.getDataList()},handlerTableItemEdit:function(e,t,r){this.$set(this.tableData[t],r,!0)},handlerTableItemSave:function(e,t,r){var a=this;return S(d().mark((function n(){var o,l,s,c,u;return d().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return o={id:e.id,remark:e.remark,price:e.price},n.next=3,Object(i["Z"])(a.$api.save(o));case 3:if(l=n.sent,s=h(l,2),c=s[0],u=s[1],!c){n.next=9;break}return n.abrupt("return",a.$message.error(c.message||"保存失败"));case 9:u&&0===u.code?(a.$message.success("保存成功"),a.$set(a.tableData[t],r,!1)):a.$message.error(u.msg||"保存失败");case 10:case"end":return n.stop()}}),n)})))()},handlerEditRecord:function(e){},handlerDetailKeyManager:function(){this.$router.push({name:"MemberKeyManager"})},handlerPageChange:function(e){this.currentPage=e,this.getDataList()},handlerSizeChange:function(e){this.pageSize=e,this.getDataList()},handlerDeleteRecord:function(e){var t=this,r=e.id||"";if(e&&"fixed"===e.type)return this.$message.error("亲，类型是固定模块无法进行删除！");this.$confirm("是否删除这条权限记录？","提示",{confirmButtonText:"确认",cancelButtonText:"取消",closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-warn",center:!0,beforeClose:function(){var e=S(d().mark((function e(a,n,o){var l,s,c,u;return d().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("confirm"!==a){e.next=15;break}return n.confirmButtonLoading=!0,e.next=4,Object(i["Z"])(t.$apis.apiBackgroundMemberMemberPermissionDeletePost({ids:[r]}));case 4:if(l=e.sent,s=h(l,2),c=s[0],u=s[1],n.confirmButtonLoading=!1,!c){e.next=12;break}return t.$message.error(c.message),e.abrupt("return");case 12:0===u.code?(o(),t.$message.success(u.msg),t.searchHandler()):t.$message.error(u.msg),e.next=16;break;case 15:n.confirmButtonLoading||o();case 16:case"end":return e.stop()}}),e)})));function a(t,r,a){return e.apply(this,arguments)}return a}()}).then((function(e){})).catch((function(e){}))},getDataList:function(){var e=this;return S(d().mark((function t(){var r,a,n,o,l,s;return d().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.isLoading){t.next=2;break}return t.abrupt("return");case 2:return e.isLoading=!0,r=m(m({},e.formatQueryParams(e.searchFormSetting)),{},{page:e.currentPage,page_size:e.pageSize}),t.next=6,Object(i["Z"])(e.$apis.apiBackgroundMemberMemberPermissionListPost(r));case 6:if(a=t.sent,n=h(a,2),o=n[0],l=n[1],e.isLoading=!1,!o){t.next=14;break}return e.$message.error(o.message),t.abrupt("return");case 14:0===l.code?(s=l.data.results||[],Array.isArray(s)&&s.length>0&&s.map((function(e,t){return e.index=t+1,e})),e.tableData=Object(i["f"])(s),e.totalCount=l.data.count||-1):e.$message.error(l.msg);case 15:case"end":return t.stop()}}),t)})))()},formatQueryParams:function(e){var t={};for(var r in e)""!==e[r].value&&null!==e[r].value&&0!==e[r].value.length&&(t[r]="is_passed"===r?[e[r].value]:e[r].value);return t},handlerAddorEditRecord:function(e,t){this.type=e,t&&(this.selectInfo=Object(i["f"])(t)),this.dialogTitle="add"===e?"新建":"编辑",this.dialogVisible=!0},getPreViewList:function(e){var t=e.icon_url;return t?[t]:[]},handlerConfirm:function(){this.dialogVisible=!1,this.getDataList()},memberStatusChange:function(e,t,r){var a=this;return S(d().mark((function e(){var n,o,l,s,c,u;return d().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=r-1>=0?r-1:0,o=Object(i["f"])(t),"association"===o.type?(delete o.permission_dict,delete o.permission_key):delete o.associate_url,a.$set(a.tableData[n],"isSwitchLoading",!0),e.next=6,Object(i["Z"])(a.$apis.apiBackgroundMemberMemberPermissionModifyPost(o));case 6:if(l=e.sent,s=h(l,2),c=s[0],u=s[1],a.$set(a.tableData[n],"isSwitchLoading",!1),!c){e.next=14;break}return a.$set(a.tableData[n],"is_enable",!t.is_enable),e.abrupt("return",a.$message.error(c.message||"修改失败"));case 14:u&&0===u.code?a.$message.success("修改成功"):(a.$set(a.tableData[n],"is_enable",!t.is_enable),a.$message.error(u.msg||"修改失败"));case 15:case"end":return e.stop()}}),e)})))()},getUrlByType:function(e){return e&&e.associate_url&&"fixed"!==e.type?e.associate_url:""},manageProjectSite:function(){this.manageProjectSiteForm.projectList=this.defaultProjectList,this.manageProjectSiteDialogShow=!0},handleClose:function(){this.isSelectAll=!1,this.manageProjectSiteForm.projectList=this.defaultProjectList,this.manageProjectSiteDialogShow=!1},getMemberOnDetail:function(){var e=this;return S(d().mark((function t(){var r,a,n,o;return d().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(i["Z"])(e.$apis.apiBackgroundMemberMemberPermissionGetMemberOnPost());case 2:if(r=t.sent,a=h(r,2),n=a[0],o=a[1],!n){t.next=8;break}return t.abrupt("return",e.$message.error(n.message||"获取失败"));case 8:o&&0===o.code?(e.defaultProjectList=o.data.company?o.data.company:[],e.manageProjectSiteForm.projectList=e.defaultProjectList):e.$message.error(o.msg);case 9:case"end":return t.stop()}}),t)})))()},confirmMemberOnDetail:function(e){var t=this;return S(d().mark((function r(){var a,n,o,l,s;return d().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return a={company:e},r.next=3,Object(i["Z"])(t.$apis.apiBackgroundMemberMemberPermissionAddMemberOnPost(a));case 3:n=r.sent,o=h(n,2),l=o[0],s=o[1],l&&t.$message.error(l.message||"设置失败"),s&&0===s.code?t.$message.success("设置成功"):t.$message.error(s.msg),t.manageProjectSiteDialogShow=!1;case 10:case"end":return r.stop()}}),r)})))()},confirm:function(){this.defaultProjectList=this.manageProjectSiteForm.projectList,this.confirmMemberOnDetail(this.manageProjectSiteForm.projectList)},selectAll:function(e){this.$refs.companySelect.getAllSelectData()},getSelectAll:function(e){this.manageProjectSiteForm.projectList=e}}},E=A,P=(r("cd26f"),r("2877")),O=Object(P["a"])(E,a,n,!1,null,"291173b2",null);t["default"]=O.exports},"8cc0":function(e,t,r){},c8c2:function(e,t,r){"use strict";r.r(t),r.d(t,"getRequestParams",(function(){return u})),r.d(t,"RECENTSEVEN",(function(){return m})),r.d(t,"DIC_OBTAIN_TYPE",(function(){return p})),r.d(t,"DIC_SEND_TYPE",(function(){return f})),r.d(t,"DIC_MEMBER_STATUS",(function(){return y})),r.d(t,"DIC_TRIGGER_TYPE",(function(){return d})),r.d(t,"DIC_MENBER_STATUS",(function(){return h})),r.d(t,"DIC_PERMISSION_TYPE",(function(){return b})),r.d(t,"DIC_MEMBER_CYCLE",(function(){return g})),r.d(t,"SEARCH_FORM_RECORD_DATA",(function(){return v})),r.d(t,"SEARCH_FORM_EXCLUSIVE_DATA",(function(){return _})),r.d(t,"TABLE_HEAD_RECORD_DATA",(function(){return w})),r.d(t,"TABLE_HEAD_EXCLUSIVE_DATA",(function(){return k})),r.d(t,"SEARCH_FORM_SMS_MANAGER_DATA",(function(){return S})),r.d(t,"TABLE_HEAD_SEND_DATA",(function(){return A})),r.d(t,"TABLE_HEAD_CHARGE_INDEX_DATA",(function(){return E})),r.d(t,"TABLE_HEAD_CHARGE_INDEX_CONTENT",(function(){return P})),r.d(t,"SEARCH_FORM_CHARGE_DETAIL_DATA",(function(){return O})),r.d(t,"TABLE_HEAD_CHARGE_DETAIL_DATA",(function(){return j})),r.d(t,"SEARCH_FORM_PERMISSION_MANAGER_DATA",(function(){return L})),r.d(t,"TABLE_HEAD_PERMISSION_MANAGER_DATA",(function(){return D})),r.d(t,"SEARCH_FORM_KEY_MANAGER_DATA",(function(){return x})),r.d(t,"TABLE_HEAD_KEY_MANAGER_DATA",(function(){return T})),r.d(t,"SEARCH_FORM_PROMOTIONAL_PICTURE_DATA",(function(){return C})),r.d(t,"TABLE_HEAD_PROMOTIONAL_PICTURE_DATA",(function(){return N}));var a=r("5a0c");function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach((function(t){l(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function l(e,t,r){return(t=s(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function s(e){var t=c(e,"string");return"symbol"==n(t)?t:t+""}function c(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=n(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var u=function(e,t,r){var a,n={};Object.keys(e).forEach((function(t){("select_time"!==t&&""!==e[t].value&&e[t].value&&0!==e[t].value.length||"boolean"===typeof e[t].value)&&(n[t]=e[t].value)}));var i=o({page:t,page_size:r},n);return 2===(null===(a=e.select_time)||void 0===a||null===(a=a.value)||void 0===a?void 0:a.length)&&(i.start_date=e.select_time.value[0],i.end_date=e.select_time.value[1]),i},m=[a().subtract(7,"day").format("YYYY-MM-DD"),a().format("YYYY-MM-DD")],p=[{name:"线上购买",value:"buy",label:"buy"},{name:"手动发放",value:"manual_release",label:"manual_release"}],f=[{name:"消费通知",value:"consume",label:"consume"},{name:"点餐提醒",value:"order",label:"order"},{name:"充值提醒",value:"recharge",label:"recharge"},{name:"余额提醒",value:"balance",label:"balance"},{name:"提现通知",value:"withdraw",label:"withdraw"},{name:"通行通知",value:"is_pass",label:"is_pass"}],y=[{name:"使用中",value:"using",label:"using"},{name:"已过期",value:"expired",label:"expired"},{name:"非会员",value:"non",label:"non"}],d=[{name:"全部标签",value:"all",label:"all"},{name:"任意标签",value:"any",label:"any"}],h=[{name:"开启",value:!0,label:!0},{name:"关闭",value:!1,label:!1}],b=[{name:"关联链接",value:"association"},{name:"固定模块",value:"fixed"}],g=[{name:"月卡",value:"month"},{name:"季卡",value:"season"},{name:"年卡",value:"year"},{name:"永久会员",value:"permanent"},{name:"畅享会员",value:"week"}],v={select_time:{type:"daterange",label:"购买时间",clearable:!0,format:"yyyy-MM-dd",value:m},member_permission_name:{type:"input",label:"权益",value:"",placeholder:"请输入权益"},nickname:{type:"input",label:"用户姓名",value:"",placeholder:"请输入用户姓名"},phone:{type:"input",label:"手机号",value:"",placeholder:"请输入手机号"},receive_type:{type:"select",value:"",label:"获取方式",dataList:p,multiple:!1,collapseTags:!0,listNameKey:"name",listValueKey:"value",clearable:!0}},_={member_permissions:{type:"select",value:[],label:"权益",dataList:[],multiple:!1,collapseTags:!0,listNameKey:"name",listValueKey:"id",clearable:!0}},w=[{label:"订单号",key:"trade_no",width:"200",fixed:"left"},{label:"购买时间",key:"create_time",width:"200"},{label:"权益",key:"member_permission_name",width:"140"},{label:"金额",key:"origin_fee",width:"140",type:"slot",slotName:"price"},{label:"有效期",key:"days",width:"120",type:"slot",slotName:"days"},{label:"用户姓名",key:"nickname",width:"150"},{label:"手机号",key:"phone",width:"150"},{label:"操作员",key:"operator_name"},{label:"获取方式",key:"receive_type_alias"}],k=[{label:"序号",key:"index",width:"100",fixed:"left"},{label:"权益",key:"member_permissions_name",width:"200",type:"slot",slotName:"permission"},{label:"有效期",key:"days",width:"200",type:"slot",slotName:"days"},{label:"价格",key:"origin_fee",width:"200",type:"slot",slotName:"price"},{label:"说明",key:"remark",width:"200"},{label:"操作员",key:"operator_name"},{label:"修改时间",key:"update_time",width:"200"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"200"}],S={select_time:{type:"daterange",label:"推送时间",clearable:!0,format:"yyyy-MM-dd",value:m},name:{type:"input",label:"用户名",value:"",placeholder:"请输用户名"},phone:{type:"input",label:"手机号",value:"",placeholder:"请输入手机号"},organization_id:{type:"select",multiple:!0,filterable:!0,collapseTags:!0,label:"消费点",value:[],placeholder:"请选择消费点",listNameKey:"name",listValueKey:"id",dataList:[]},company_id:{type:"CompanySelect",value:[],label:"所属项目点",dataList:[],multiple:!0,checkStrictly:!0,clearable:!0,labelWidth:"100px",companyOpts:{label:"name",value:"company"},role:"super"},receive_type:{type:"select",value:[],label:"推送类型",dataList:f,multiple:!1,collapseTags:!0,listNameKey:"name",clearable:!0}},A=[{label:"推送时间",key:"create_time",width:"200"},{label:"用户名",key:"user_name",width:"150"},{label:"手机号",key:"phone",width:"150"},{label:"所属项目点",key:"company_name",width:"150"},{label:"消费点",key:"organization_name",width:"150"},{label:"推送类型",key:"push_type_alias",width:"150"},{label:"推送内容",key:"remark"},{label:"短信服务商",key:"sms_type_alias"}],E=[{label:"会员类型",key:"member_cycle_alias",width:"140"},{label:"说明",key:"remark",width:"250",type:"slot",slotName:"remark",align:"center"},{label:"基础价格（元）",key:"origin_fee",type:"slot",slotName:"price"},{label:"操作时间",key:"update_time"},{label:"操作人",key:"operator_name"},{label:"操作",key:"contact_name",type:"slot",slotName:"operation"}],P=[{member_cycle_alias:"年卡",origin_fee:"155622",remark:"说明说明说明说明"},{member_cycle_alias:"季卡",origin_fee:"26",remark:"11111"},{member_cycle_alias:"月卡",origin_fee:"32",remark:"2222"},{member_cycle_alias:"畅享会员",price:"44",remark:"333"},{member_cycle_alias:"永久会员",origin_fee:"522",remark:"66666666"}],O={name:{type:"input",label:"名称",value:"",placeholder:"请输入名称"},member_labels:{type:"select",value:[],label:"会员标签",dataList:[],multiple:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",clearable:!0},trigger_type:{type:"select",value:[],label:"标签触发类型",dataList:d,collapseTags:!0,listNameKey:"name",listValueKey:"value",clearable:!0,labelWidth:"200"},is_enable:{type:"select",value:[],label:"状态",dataList:h,listNameKey:"name",listValueKey:"value",clearable:!0}},j=[{label:"名称",key:"name",width:"140"},{label:"会员标签",key:"member_labels",width:"250",type:"slot",slotName:"label"},{label:"标签触发类型",key:"trigger_type",type:"slot",slotName:"trigger"},{label:"优惠折扣",key:"discount"},{label:"优惠价",key:"origin_fee",type:"money"},{label:"限购次数",key:"buy_count",type:"slot",slotName:"buyCount"},{label:"说明",key:"remark",showTooltip:!0},{label:"操作时间",key:"update_time"},{label:"操作人",key:"operator_name"},{label:"状态",key:"status",type:"slot",slotName:"status"},{label:"操作",key:"contact_name",type:"slot",slotName:"operation"}],L={name:{type:"input",label:"权限名称",value:"",placeholder:"请输入权限名称"},type:{type:"select",value:[],label:"类型",dataList:b,collapseTags:!0,listNameKey:"name",listValueKey:"value",clearable:!0},associate_url:{type:"input",label:"关联链接",value:"",placeholder:"请输入关联链接"}},D=[{label:"权限名称",key:"name",width:"140"},{label:"图标",key:"icon_url",width:"250",type:"slot",slotName:"image"},{label:"类型",key:"type_alias"},{label:"键值",key:"permission_key"},{label:"关联链接",key:"associate_url",width:"250",type:"slot",slotName:"associateUrl"},{label:"权限说明",key:"remark",showTooltip:!0,width:"200"},{label:"操作时间",key:"update_time"},{label:"操作人",key:"operator_name"},{label:"是否需要会员",key:"status",type:"slot",slotName:"status"},{label:"操作",key:"contact_name",type:"slot",slotName:"operation"}],x={name:{type:"input",label:"名称",value:"",placeholder:"请输入名称"}},T=[{label:"名称",key:"name",width:"140"},{label:"键值",key:"permission_key"},{label:"说明",key:"remark"},{label:"操作时间",key:"update_time"},{label:"操作人",key:"operator_name"},{label:"操作",key:"operation",type:"slot",slotName:"operation"}],C={name:{type:"input",label:"权限名称",value:"",placeholder:"请输入权限名称"},type:{type:"select",value:[],label:"类型",dataList:b,collapseTags:!0,listNameKey:"name",listValueKey:"value",clearable:!0},associate_url:{type:"input",label:"关联链接",value:"",placeholder:"请输入关联链接"}},N=[{label:"名称",key:"name",width:"140"},{label:"图片",key:"url",width:"250",type:"slot",slotName:"image"},{label:"优先级",key:"priority"},{label:"状态",key:"is_release",type:"slot",slotName:"status"},{label:"显示界面",key:"ui_type_alias",type:"slot",slotName:"UIType"},{label:"操作时间",key:"update_time"},{label:"操作人",key:"operator_name"},{label:"操作",key:"contact_name",type:"slot",slotName:"operation"}]},cd26f:function(e,t,r){"use strict";r("8cc0")}}]);