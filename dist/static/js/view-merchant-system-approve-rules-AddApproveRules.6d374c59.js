(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-system-approve-rules-AddApproveRules","approve_rules"],{"535d":function(e,t,r){"use strict";r("87b2")},"5a9e":function(e,t,r){"use strict";r.r(t);var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"AddApproveRules container-wrapper"},[t("div",{staticClass:"table-wrapper"},[t("div",{staticClass:"table-header",staticStyle:{display:"flex","justify-content":"space-between"}},[t("div",{staticClass:"table-title"},[e._v(e._s("add"===e.type?"新增":"编辑")+"规则")])]),t("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"deviceForm",staticStyle:{padding:"0 25px"},attrs:{model:e.approveRuleInfo,rules:e.approveRuleFormRules,"label-width":"auto"}},[t("div",{staticClass:"setting-wrap"},[t("div",{staticClass:"title"},[e._v("基础设置")]),t("el-form-item",{attrs:{label:"规则名称",prop:"approveRuleName"}},[t("el-input",{staticClass:"ps-input w-300",attrs:{max:"20"},model:{value:e.approveRuleInfo.approveRuleName,callback:function(t){e.$set(e.approveRuleInfo,"approveRuleName",t)},expression:"approveRuleInfo.approveRuleName"}})],1),t("el-form-item",{attrs:{label:"审批类型",prop:"approveRuleType"}},[t("el-select",{staticClass:"ps-select w-300",model:{value:e.approveRuleInfo.approveRuleType,callback:function(t){e.$set(e.approveRuleInfo,"approveRuleType",t)},expression:"approveRuleInfo.approveRuleType"}},e._l(e.approveTypeList,(function(e){return t("el-option",{key:e.key,attrs:{label:e.name,value:e.key}})})),1)],1)],1),t("div",{staticClass:"setting-wrap"},[t("div",{staticClass:"title"},[e._v("审批设置")]),t("el-form-item",{attrs:{label:"审批方式",prop:"approveMethod"}},[t("el-radio-group",{model:{value:e.approveRuleInfo.approveMethod,callback:function(t){e.$set(e.approveRuleInfo,"approveMethod",t)},expression:"approveRuleInfo.approveMethod"}},[t("el-radio",{staticClass:"ps-radio",attrs:{label:"one_by_one_approve"}},[e._v("依次审批（本环节内审批账号依次审批）")]),t("el-radio",{staticClass:"ps-radio",attrs:{label:"and_approve"}},[e._v("会签（需所有审批账号同意）")]),t("el-radio",{staticClass:"ps-radio",attrs:{label:"or_approve"}},[e._v("或签（一名审批账号同意或拒绝即可）")])],1)],1),t("el-form-item",{attrs:{label:"审批人",required:""}},e._l(e.approveRuleInfo.approveAccounts,(function(o,n){return t("el-form-item",{key:"approveAccounts"+n,staticClass:"approve-accounts",attrs:{label:"审批账号"+(n+1),"label-width":"90px",prop:"approveAccounts[".concat(n,"]"),rules:e.approveRuleFormRules.approveAccounts}},[t("el-select",{staticClass:"ps-input w-300",attrs:{multiple:"","collapse-tags":!1,filterable:"",placeholder:"请选择审批人"},model:{value:e.approveRuleInfo.approveAccounts[n],callback:function(t){e.$set(e.approveRuleInfo.approveAccounts,n,t)},expression:"approveRuleInfo.approveAccounts[index]"}},e._l(e.accountList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id,disabled:e.disabled}})})),1),t("img",{attrs:{src:r("a851"),alt:""},on:{click:e.addApproveAccounts}}),n>0?t("img",{attrs:{src:r("1597"),alt:""},on:{click:function(t){return e.delApproveAccounts(n)}}}):e._e()],1)})),1),"finance"===e.approveRuleInfo.approveRuleType?t("el-form-item",{attrs:{label:"审批通过备注",rules:"finance"===e.approveRuleInfo.approveRuleType?[{required:!0,message:"此项为必填项",trigger:["change","blur"]}]:[{required:!1}]}},[t("el-radio-group",{model:{value:e.approveRuleInfo.isNeedRemark,callback:function(t){e.$set(e.approveRuleInfo,"isNeedRemark",t)},expression:"approveRuleInfo.isNeedRemark"}},[t("el-radio",{attrs:{label:!0}},[e._v("必填")]),t("el-radio",{attrs:{label:!1}},[e._v("非必填")])],1)],1):e._e()],1),t("el-button",{staticClass:"ps-origin-btn w-150 form-btn",attrs:{size:"small",type:"primary"},on:{click:e.checkForm}},[e._v("保存")])],1)],1)])},n=[];function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function i(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */i=function(){return t};var e,t={},r=Object.prototype,o=r.hasOwnProperty,n=Object.defineProperty||function(e,t,r){e[t]=r.value},p="function"==typeof Symbol?Symbol:{},u=p.iterator||"@@iterator",s=p.asyncIterator||"@@asyncIterator",l=p.toStringTag||"@@toStringTag";function c(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,r){return e[t]=r}}function v(e,t,r,o){var a=t&&t.prototype instanceof b?t:b,i=Object.create(a.prototype),p=new j(o||[]);return n(i,"_invoke",{value:N(e,r,p)}),i}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=v;var d="suspendedStart",h="suspendedYield",m="executing",y="completed",g={};function b(){}function R(){}function w(){}var _={};c(_,u,(function(){return this}));var x=Object.getPrototypeOf,k=x&&x(x(C([])));k&&k!==r&&o.call(k,u)&&(_=k);var L=w.prototype=b.prototype=Object.create(_);function A(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function I(e,t){function r(n,i,p,u){var s=f(e[n],e,i);if("throw"!==s.type){var l=s.arg,c=l.value;return c&&"object"==a(c)&&o.call(c,"__await")?t.resolve(c.__await).then((function(e){r("next",e,p,u)}),(function(e){r("throw",e,p,u)})):t.resolve(c).then((function(e){l.value=e,p(l)}),(function(e){return r("throw",e,p,u)}))}u(s.arg)}var i;n(this,"_invoke",{value:function(e,o){function n(){return new t((function(t,n){r(e,o,t,n)}))}return i=i?i.then(n,n):n()}})}function N(t,r,o){var n=d;return function(a,i){if(n===m)throw Error("Generator is already running");if(n===y){if("throw"===a)throw i;return{value:e,done:!0}}for(o.method=a,o.arg=i;;){var p=o.delegate;if(p){var u=E(p,o);if(u){if(u===g)continue;return u}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(n===d)throw n=y,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);n=m;var s=f(t,r,o);if("normal"===s.type){if(n=o.done?y:h,s.arg===g)continue;return{value:s.arg,done:o.done}}"throw"===s.type&&(n=y,o.method="throw",o.arg=s.arg)}}}function E(t,r){var o=r.method,n=t.iterator[o];if(n===e)return r.delegate=null,"throw"===o&&t.iterator.return&&(r.method="return",r.arg=e,E(t,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+o+"' method")),g;var a=f(n,t.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,g;var i=a.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,g):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function $(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function j(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function C(t){if(t||""===t){var r=t[u];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function r(){for(;++n<t.length;)if(o.call(t,n))return r.value=t[n],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(a(t)+" is not iterable")}return R.prototype=w,n(L,"constructor",{value:w,configurable:!0}),n(w,"constructor",{value:R,configurable:!0}),R.displayName=c(w,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===R||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,c(e,l,"GeneratorFunction")),e.prototype=Object.create(L),e},t.awrap=function(e){return{__await:e}},A(I.prototype),c(I.prototype,s,(function(){return this})),t.AsyncIterator=I,t.async=function(e,r,o,n,a){void 0===a&&(a=Promise);var i=new I(v(e,r,o,n),a);return t.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},A(L),c(L,l,"Generator"),c(L,u,(function(){return this})),c(L,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var o in t)r.push(o);return r.reverse(),function e(){for(;r.length;){var o=r.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},t.values=C,j.prototype={constructor:j,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach($),!t)for(var r in this)"t"===r.charAt(0)&&o.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(o,n){return p.type="throw",p.arg=t,r.next=o,n&&(r.method="next",r.arg=e),!!n}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],p=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var u=o.call(i,"catchLoc"),s=o.call(i,"finallyLoc");if(u&&s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&o.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var a=n;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,g):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),$(r),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var o=r.completion;if("throw"===o.type){var n=o.arg;$(r)}return n}}throw Error("illegal catch attempt")},delegateYield:function(t,r,o){return this.delegate={iterator:C(t),resultName:r,nextLoc:o},"next"===this.method&&(this.arg=e),g}},t}function p(e,t,r,o,n,a,i){try{var p=e[a](i),u=p.value}catch(e){return void r(e)}p.done?t(u):Promise.resolve(u).then(o,n)}function u(e){return function(){var t=this,r=arguments;return new Promise((function(o,n){var a=e.apply(t,r);function i(e){p(a,o,n,i,u,"next",e)}function u(e){p(a,o,n,i,u,"throw",e)}i(void 0)}))}}var s={name:"AddApproveRules",data:function(){return{isLoading:!1,type:"add",progress:"",approveRuleData:{},approveRuleInfo:{approveRuleName:"",approveRuleType:"",approveMethod:"one_by_one_approve",approveAccounts:[""],isNeedRemark:!1},approveRuleFormRules:{approveRuleName:[{required:!0,message:"请输入规则名称",trigger:"blur"}],approveRuleType:[{required:!0,message:"请选择审批类型",trigger:"blur"}],approveMethod:[{required:!0,message:"请选择审批方式",trigger:"blur"}],approveAccounts:[{required:!0,message:"请选择审批账号",trigger:"blur"}]},approveTypeList:[{key:"approve_order_visitor",name:"访客就餐"},{key:"register",name:"自注册"},{key:"fund",name:"资金上传"},{key:"finance",name:"财务审批"}],accountList:[]}},created:function(){this.$route.query.data&&(this.approveRuleData=JSON.parse(this.$route.query.data)),this.$route.params.type&&(this.type=this.$route.params.type),this.initLoad()},mounted:function(){},methods:{initLoad:function(){var e=this;return u(i().mark((function t(){return i().wrap((function(t){while(1)switch(t.prev=t.next){case 0:"edit"===e.type&&(e.approveRuleInfo.approveRuleName=e.approveRuleData.name,e.approveRuleInfo.approveRuleType=e.approveRuleData.approve_type,e.approveRuleInfo.approveMethod=e.approveRuleData.approve_method,e.approveRuleInfo.approveAccounts=e.approveRuleData.approve_json.account_ids,e.approveRuleInfo.isNeedRemark=e.approveRuleData.is_remark),e.getAccountList();case 2:case"end":return t.stop()}}),t)})))()},checkForm:function(){var e=this;this.$refs.deviceForm.validate((function(t){if(!t)return e.$message.error("数据填写有误，请检查"),!1;var r,o={name:e.approveRuleInfo.approveRuleName,approve_type:e.approveRuleInfo.approveRuleType,approve_method:e.approveRuleInfo.approveMethod,approve_json:{account_ids:e.approveRuleInfo.approveAccounts,approve_method:e.approveRuleInfo.approveMethod},is_remark:"finance"===e.approveRuleInfo.approveRuleType?e.approveRuleInfo.isNeedRemark:void 0};"add"===e.type?r=e.$apis.apiBackgroundApproveApproveRuleAddPost:"edit"===e.type&&(o.id=e.approveRuleData.id,r=e.$apis.apiBackgroundApproveApproveRuleModifyPost),e.saveSetting(o,r)}))},saveSetting:function(e,t){var r=this;return u(i().mark((function o(){var n;return i().wrap((function(o){while(1)switch(o.prev=o.next){case 0:return o.next=2,t(e);case 2:n=o.sent,0===n.code?(r.$message.success("保存成功"),r.$closeCurrentTab(r.$route.path)):r.$message.error(n.msg);case 4:case"end":return o.stop()}}),o)})))()},addApproveAccounts:function(){this.approveRuleInfo.approveAccounts.push("")},delApproveAccounts:function(e){this.approveRuleInfo.approveAccounts.splice(e,1)},getAccountList:function(){var e=this;return u(i().mark((function t(){var r;return i().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,e.$apis.apiBackgroundOrganizationAccountListPost({page:1,page_size:9999,status:1});case 3:r=t.sent,e.isLoading=!1,0===r.code?e.accountList=r.data.results.map((function(e){return e.name=e.username+"-"+e.member_name,e})):e.$message.error(r.msg);case 6:case"end":return t.stop()}}),t)})))()}}},l=s,c=(r("535d"),r("2877")),v=Object(c["a"])(l,o,n,!1,null,null,null);t["default"]=v.exports},"87b2":function(e,t,r){e.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}}}]);