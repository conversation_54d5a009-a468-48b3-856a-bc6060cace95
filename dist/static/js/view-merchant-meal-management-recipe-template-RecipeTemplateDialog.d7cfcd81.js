(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-recipe-template-RecipeTemplateDialog","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-meal-report-MealPackageRule"],{"72c6":function(t,e,r){},c9d9:function(t,e,r){"use strict";r.d(e,"a",(function(){return i})),r.d(e,"d",(function(){return o})),r.d(e,"b",(function(){return l})),r.d(e,"c",(function(){return u})),r.d(e,"e",(function(){return s})),r.d(e,"f",(function(){return c})),r.d(e,"g",(function(){return f}));var n=r("5a0c"),a=r("da92"),i=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],o=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],l={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},u=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],s=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],c=(n().subtract(7,"day").format("YYYY-MM-DD"),n().format("YYYY-MM-DD"),function(t){return t?"number"===typeof t?a["a"].divide(t,100).toFixed(2):"string"!==typeof t||isNaN(Number(t))?t:a["a"].divide(t,100).toFixed(2):"0.00"}),f=function(t){return a["a"].times(t,100)}},ce4b:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("dialog-message",{attrs:{show:t.visible,loading:t.dialogLoading,title:t.dialogTitle,width:t.dialogWidth},on:{"update:show":function(e){t.visible=e},close:t.closeDialog}},[e("el-form",{ref:"dialogFormRef",staticClass:"RecipeTemplateDialog",attrs:{model:t.dialogForm,rules:t.dialogrules,"label-width":t.formLabelWidth,size:"medium"}},[e("div",{staticClass:"form-block"},[e("el-form-item",{staticClass:"inline-block",attrs:{label:"模板名称",prop:"name"}},[e("el-input",{staticClass:"w-200",attrs:{maxlength:20},model:{value:t.dialogForm.name,callback:function(e){t.$set(t.dialogForm,"name",e)},expression:"dialogForm.name"}})],1),e("el-form-item",{staticClass:"inline-block",attrs:{label:"应季食材优先生成","label-width":"140px",prop:"seasonalPriority"}},[e("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:t.dialogForm.seasonalPriority,callback:function(e){t.$set(t.dialogForm,"seasonalPriority",e)},expression:"dialogForm.seasonalPriority"}})],1)],1),e("div",{staticClass:"meal-attr"},t._l(t.formSetting,(function(r,n){return e("el-form-item",{key:n,attrs:{label:"",prop:"","label-width":"30px"}},[e("h3",[t._v(t._s(r.label))]),t._l(r.children,(function(n){return e("el-form-item",{key:n.key,staticClass:"inline-block m-b-20",attrs:{label:n.label,prop:"attrData."+r.key+"."+n.key,rules:t.dialogrules.integerInput}},[e("el-input",{staticClass:"w-100",model:{value:t.dialogForm.attrData[r.key][n.key],callback:function(e){t.$set(t.dialogForm.attrData[r.key],n.key,e)},expression:"dialogForm.attrData[item.key][child.key]"}}),e("span",{staticClass:"m-l-10"},[t._v("道")])],1)}))],2)})),1),e("el-form-item",{attrs:{label:"备注",prop:"seasonalPriority"}},[e("el-input",{staticClass:"ps-input",staticStyle:{width:"87%"},attrs:{type:"textarea",rows:4,placeholder:"请输入备注",maxlength:50,"show-word-limit":""},model:{value:t.dialogForm.remark,callback:function(e){t.$set(t.dialogForm,"remark",e)},expression:"dialogForm.remark"}})],1)],1),e("div",{staticClass:"text-right m-t-40",attrs:{slot:"tool"},slot:"tool"},[e("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:t.dialogLoading},on:{click:t.cancleDialog}},[t._v("取消")]),e("el-button",{staticClass:"ps-btn",attrs:{disabled:t.dialogLoading,type:"primary"},on:{click:t.submitHandler}},[t._v("确定")])],1)],1)},a=[],i=r("e925"),o=r("ed08"),l=r("c9d9");function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function d(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,o=Object.create(i.prototype),l=new P(n||[]);return a(o,"_invoke",{value:S(t,r,l)}),o}function m(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=d;var h="suspendedStart",g="suspendedYield",p="executing",y="completed",v={};function b(){}function k(){}function w(){}var _={};f(_,o,(function(){return this}));var x=Object.getPrototypeOf,L=x&&x(x(O([])));L&&L!==r&&n.call(L,o)&&(_=L);var F=w.prototype=b.prototype=Object.create(_);function D(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function $(t,e){function r(a,i,o,l){var s=m(t[a],t,i);if("throw"!==s.type){var c=s.arg,f=c.value;return f&&"object"==u(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,o,l)}),(function(t){r("throw",t,o,l)})):e.resolve(f).then((function(t){c.value=t,o(c)}),(function(t){return r("throw",t,o,l)}))}l(s.arg)}var i;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return i=i?i.then(a,a):a()}})}function S(e,r,n){var a=h;return function(i,o){if(a===p)throw Error("Generator is already running");if(a===y){if("throw"===i)throw o;return{value:t,done:!0}}for(n.method=i,n.arg=o;;){var l=n.delegate;if(l){var u=E(l,n);if(u){if(u===v)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===h)throw a=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=p;var s=m(e,r,n);if("normal"===s.type){if(a=n.done?y:g,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(a=y,n.method="throw",n.arg=s.arg)}}}function E(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,E(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=m(a,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var o=i.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function A(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function P(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(A,this),this.reset(!0)}function O(e){if(e||""===e){var r=e[o];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,i=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(u(e)+" is not iterable")}return k.prototype=w,a(F,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:k,configurable:!0}),k.displayName=f(w,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===k||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,f(t,c,"GeneratorFunction")),t.prototype=Object.create(F),t},e.awrap=function(t){return{__await:t}},D($.prototype),f($.prototype,l,(function(){return this})),e.AsyncIterator=$,e.async=function(t,r,n,a,i){void 0===i&&(i=Promise);var o=new $(d(t,r,n,a),i);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},D(F),f(F,c,"Generator"),f(F,o,(function(){return this})),f(F,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=O,P.prototype={constructor:P,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return l.type="throw",l.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],l=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var u=n.call(o,"catchLoc"),s=n.call(o,"finallyLoc");if(u&&s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(u){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),j(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;j(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:O(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function c(t,e){return g(t)||h(t,e)||d(t,e)||f()}function f(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(t,e){if(t){if("string"==typeof t)return m(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?m(t,e):void 0}}function m(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function h(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,i,o,l=[],u=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(l.push(n.value),l.length!==e);u=!0);}catch(t){s=!0,a=t}finally{try{if(!u&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw a}}return l}}function g(t){if(Array.isArray(t))return t}function p(t,e,r,n,a,i,o){try{var l=t[i](o),u=l.value}catch(t){return void r(t)}l.done?e(u):Promise.resolve(u).then(n,a)}function y(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var i=t.apply(e,r);function o(t){p(i,n,a,o,l,"next",t)}function l(t){p(i,n,a,o,l,"throw",t)}o(void 0)}))}}var v={name:"RecipeTemplateDialog",model:{prop:"showDialog",event:"changeShow"},props:{showDialog:{required:!0},dialogTitle:{type:String,default:"新建模板"},type:{type:String,default:"add"},infoData:{type:Object,default:function(){return{}}},dialogWidth:{type:String,default:"800px"},formLabelWidth:{type:[String,Number],default:"100px"},formSize:{type:String,default:"medium"},closehandle:Function,confirmhandle:Function},data:function(){var t=function(t,e,r){e?Object(i["b"])(e)?r():r(new Error("仅支持输入整数")):r()};return{dialogLoading:!1,dialogForm:{name:"",seasonalPriority:!1,attrData:{},remark:""},dialogrules:{name:[{required:!0,message:"请输入名称",trigger:"blur"}],integerInput:[{validator:t,trigger:"change"}]},formSetting:[],mealList:l["a"],templateAttributeList:[]}},computed:{visible:{get:function(){return this.showDialog},set:function(t){this.$emit("changeShow",t)}}},watch:{showDialog:function(t){t&&this.init()}},created:function(){this.getFoodAttributeList()},mounted:function(){},methods:{init:function(){var t=this;"modify"===this.type&&(this.dialogForm.name=this.infoData.name,this.dialogForm.seasonalPriority=this.infoData.seasonal_priority,this.dialogForm.remark=this.infoData.remark,this.infoData.category_data&&this.infoData.category_data.length>0&&this.infoData.category_data.forEach((function(e){var r=e.category_json_data;for(var n in t.dialogForm.attrData[e.meal_type])""!==r[n]&&t.$set(t.dialogForm.attrData[e.meal_type],n,r[n])})))},getFoodAttributeList:function(){var t=this;return y(s().mark((function e(){var r,n,a,i;return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.$to(t.$apis.apiBackgroundFoodFoodCategoryListPost({page:1,page_size:99999}));case 3:if(r=e.sent,n=c(r,2),a=n[0],i=n[1],t.isLoading=!1,!a){e.next=11;break}return t.$message.error(a.message),e.abrupt("return");case 11:0===i.code?t.setDefaultAttrData(i.data.results):t.$message.error(i.msg);case 12:case"end":return e.stop()}}),e)})))()},setDefaultAttrData:function(t){var e=this,r={};t?(this.templateAttributeList=t.map((function(t){var e={label:t.name,key:t.id};return r[t.id]="",e})),this.formSetting=this.mealList.map((function(t){return e.$set(e.dialogForm.attrData,t.value,Object(o["f"])(r)),{label:t.label,key:t.value,children:Object(o["f"])(e.templateAttributeList)}}))):(this.templateAttributeList.map((function(t){r[t.key]=""})),this.mealList.map((function(t){e.$set(e.dialogForm.attrData,t.value,Object(o["f"])(r))})))},closeDialog:function(){this.resetForm(),this.closehandle&&this.closehandle()},cancleDialog:function(){this.closehandle&&this.closehandle(),this.visible=!1},confirmDialog:function(){this.confirmhandle&&this.confirmhandle()},submitHandler:function(){var t=this;if(this.isLoading)return this.$message.error("请不要重复点击提交！");this.$refs.dialogFormRef.validate(function(){var e=y(s().mark((function e(r){var n;return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:r&&(n=t.formatTemplateParams(),"modify"===t.type?t.modifyTemplate(n):t.addTemplate(n));case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},formatTemplateParams:function(){var t={name:this.dialogForm.name,seasonal_priority:this.dialogForm.seasonalPriority};this.dialogForm.remark&&(t.remark=this.dialogForm.remark);var e=[];for(var r in this.dialogForm.attrData){var n=this.dialogForm.attrData[r],a={};for(var i in n)""!==n[i]?a[i]=Number(n[i]):a[i]=0;e.push({meal_type:r,category_json_data:a})}return t.category_data=e,"modify"===this.type&&(t.id=this.infoData.id),t},addTemplate:function(t){var e=this;return y(s().mark((function r(){var n,a,i,o;return s().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return e.dialogLoading=!0,r.next=3,e.$to(e.$apis.apiBackgroundFoodMenuTemplateAddPost(t));case 3:if(n=r.sent,a=c(n,2),i=a[0],o=a[1],e.dialogLoading=!1,!i){r.next=11;break}return e.$message.error(i.message),r.abrupt("return");case 11:0===o.code?(e.$message.success(o.msg),e.confirmDialog(),e.visible=!1):e.$message.error(o.msg);case 12:case"end":return r.stop()}}),r)})))()},modifyTemplate:function(t){var e=this;return y(s().mark((function r(){var n,a,i,o;return s().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return e.dialogLoading=!0,r.next=3,e.$to(e.$apis.apiBackgroundFoodMenuTemplateModifyPost(t));case 3:if(n=r.sent,a=c(n,2),i=a[0],o=a[1],e.dialogLoading=!1,!i){r.next=11;break}return e.$message.error(i.message),r.abrupt("return");case 11:0===o.code?(e.$message.success(o.msg),e.confirmDialog(),e.visible=!1):e.$message.error(o.msg);case 12:case"end":return r.stop()}}),r)})))()},resetForm:function(){var t=this.$refs.dialogFormRef;t&&t.clearValidate(),this.dialogForm={name:"",seasonalPriority:!1,attrData:{},remark:""},this.setDefaultAttrData()}}},b=v,k=(r("df57"),r("2877")),w=Object(k["a"])(b,n,a,!1,null,"981dae18",null);e["default"]=w.exports},df57:function(t,e,r){"use strict";r("72c6")},e925:function(t,e,r){"use strict";r.d(e,"c",(function(){return n})),r.d(e,"g",(function(){return a})),r.d(e,"i",(function(){return i})),r.d(e,"e",(function(){return o})),r.d(e,"h",(function(){return l})),r.d(e,"f",(function(){return u})),r.d(e,"d",(function(){return s})),r.d(e,"m",(function(){return c})),r.d(e,"l",(function(){return f})),r.d(e,"n",(function(){return d})),r.d(e,"j",(function(){return m})),r.d(e,"b",(function(){return h})),r.d(e,"k",(function(){return g})),r.d(e,"a",(function(){return p}));var n=function(t){return/^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(t)},a=function(t){return/^([0-9]{3,4}-)?[0-9]{7,8}$/.test(t.toString())},i=function(t){return/^\w{5,20}$/.test(t)},o=function(t){return/^(?=.*[0-9])(?=.*[a-zA-Z])(.{8,20})$/.test(t)},l=function(t){return/(^([0-9]{3,4}-)?[0-9]{7,8}$)|(^1[3,4,5,6,7,8,9][0-9]{9}$)/.test(t.toString())},u=function(t){return/^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(t.toString())},s=function(t){return/\d/.test(t)},c=function(t){return/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(t)},f=function(t){return/^(([1-9]?[0-9])|([1-9]?[0-9]\.[1-9])|([1-9]?[1-9]\.[0]))$/.test(t)},d=function(t){return/^(([0-9])|([0-9]\.\d{1,3})|([1-9]\.[0]))$/.test(t)},m=function(t){return/^-?(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(t)},h=function(t){return/^[0-9]+$/.test(t)},g=function(t){return/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1}|[1-9][0-9]*\.\d{1})))$/.test(t)},p=function(t){return/^[a-zA-Z0-9]+$/.test(t)}}}]);