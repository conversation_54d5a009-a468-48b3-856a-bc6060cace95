(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-accessControl-attendance-access-control-AddOrEditControlSetting","view-merchant-accessControl-attendance-constantsConfig"],{"1e55":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"AddOrEditControlSetting container-wrapper"},[e("div",{staticClass:"table-wrapper"},[e("el-form",{ref:"settingFormRef",attrs:{model:t.settingForm,rules:t.settingFormRules,"label-width":"100px"}},[e("div",{staticClass:"title"},[t._v("基础设置")]),e("el-form-item",{attrs:{label:"名称",prop:"name"}},[e("el-input",{staticClass:"ps-input w-250",model:{value:t.settingForm.name,callback:function(e){t.$set(t.settingForm,"name",e)},expression:"settingForm.name"}})],1),e("el-form-item",{attrs:{label:"适用分组",prop:"userGroups"}},[e("user-group-select",{staticClass:"ps-input w-250",attrs:{multiple:!0,"collapse-tags":!0,"show-other":!0,placeholder:"请下拉选择"},model:{value:t.settingForm.userGroups,callback:function(e){t.$set(t.settingForm,"userGroups",e)},expression:"settingForm.userGroups"}})],1),e("el-form-item",{attrs:{label:"进出设置",prop:"limitPass"}},[e("el-radio-group",{model:{value:t.settingForm.limitPass,callback:function(e){t.$set(t.settingForm,"limitPass",e)},expression:"settingForm.limitPass"}},[e("el-radio",{staticClass:"ps-radio",attrs:{label:-1}},[t._v("无限制")]),e("el-radio",{staticClass:"ps-radio",attrs:{label:"count"}},[t._v("按次")])],1),-1!==t.settingForm.limitPass?e("el-form-item",{attrs:{prop:"limitCount"}},[e("el-input",{staticClass:"ps-input w-250",model:{value:t.settingForm.limitCount,callback:function(e){t.$set(t.settingForm,"limitCount",e)},expression:"settingForm.limitCount"}}),t._v("次 ")],1):t._e()],1),e("div",{staticClass:"title"},[t._v("时间设置")]),e("button-icon",{staticStyle:{"margin-left":"20px"},attrs:{color:"origin",type:"add"},on:{click:function(e){return t.addTimeSetting()}}},[t._v("增加时间段")]),t._l(t.settingForm.timeSetting,(function(n,i){return e("div",{key:i,staticClass:"time-setting"},[e("el-form-item",{attrs:{label:"晚归时间"}},[e("div",{staticClass:"time-range"},[e("el-form-item",{attrs:{prop:"timeSetting."+i+".sign_in_time",rules:t.settingFormRules.validateSignTime}},[e("el-time-picker",{staticClass:"ps-picker w-150",attrs:{"value-format":"HH:mm",format:"HH:mm"},model:{value:n.sign_in_time,callback:function(e){t.$set(n,"sign_in_time",e)},expression:"item.sign_in_time"}})],1),t._v(" 至 "),e("el-form-item",{attrs:{prop:"timeSetting."+i+".sign_out_time",rules:t.settingFormRules.validateSignTime}},[e("el-time-picker",{staticClass:"ps-picker w-150",attrs:{"value-format":"HH:mm",format:"HH:mm"},model:{value:n.sign_out_time,callback:function(e){t.$set(n,"sign_out_time",e)},expression:"item.sign_out_time"}})],1)],1)]),e("el-form-item",{attrs:{label:"节假日跳过"}},[e("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:n.is_pass_holiday,callback:function(e){t.$set(n,"is_pass_holiday",e)},expression:"item.is_pass_holiday"}})],1),e("el-form-item",{staticClass:"special-date",attrs:{label:"特殊日期"}},[t._l(n.except_date_range,(function(r,i){return e("div",{key:i,staticClass:"no-skip-days"},[t._v(" "+t._s(t.formatDateText(r))+" "),e("div",{staticClass:"del-day",on:{click:function(e){return t.delSkipDay(n,i)}}},[e("i",{staticClass:"el-icon-error"})])])})),e("div",{staticClass:"hidden-date"},[e("img",{attrs:{src:r("a851"),alt:""}}),e("el-date-picker",{attrs:{type:"dates",clearable:!1,placeholder:"选择一个或多个日期",format:"MM-dd","value-format":"MM-dd","popper-class":"hidden-picker-year"},on:{change:function(e){return t.changeSkipDays(e,n)}},model:{value:n.skipDays,callback:function(e){t.$set(n,"skipDays",e)},expression:"item.skipDays"}})],1)],2),e("el-form-item",{attrs:{label:"适用日期",prop:"timeSetting."+i+".week_day_range",rules:t.settingFormRules.week_day_range}},[e("el-select",{staticClass:"ps-select w-250",attrs:{multiple:!0,placeholder:"请下拉选择","popper-class":"ps-popper-select"},on:{"visible-change":function(e){return t.visibleSelectWeek(e,i)},change:function(e){return t.changeWeek(e,i)}},model:{value:n.week_day_range,callback:function(e){t.$set(n,"week_day_range",e)},expression:"item.week_day_range"}},t._l(t.weekList,(function(t){return e("el-option",{key:t.key,attrs:{label:t.name,value:t.key,disabled:t.disabled}})})),1)],1),t.settingForm.timeSetting.length>1?e("el-button",{staticClass:"delete-box",attrs:{type:"danger",icon:"el-icon-delete",circle:""},on:{click:function(e){return t.delTimeSetting(i)}}}):t._e()],1)})),e("el-form-item",[e("el-button",{staticClass:"ps-origin-btn w-150",attrs:{size:"small",type:"primary"},on:{click:t.saveSetting}},[t._v("保存")])],1)],2)],1)])},i=[],a=r("390a"),o=r("9210");function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},o=a.iterator||"@@iterator",l=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function m(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{m({},"")}catch(t){m=function(t,e,r){return t[e]=r}}function p(t,e,r,n){var a=e&&e.prototype instanceof _?e:_,o=Object.create(a.prototype),s=new $(n||[]);return i(o,"_invoke",{value:C(t,r,s)}),o}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=p;var g="suspendedStart",d="suspendedYield",h="executing",y="completed",v={};function _(){}function b(){}function w(){}var k={};m(k,o,(function(){return this}));var S=Object.getPrototypeOf,x=S&&S(S(j([])));x&&x!==r&&n.call(x,o)&&(k=x);var F=w.prototype=_.prototype=Object.create(k);function L(t){["next","throw","return"].forEach((function(e){m(t,e,(function(t){return this._invoke(e,t)}))}))}function O(t,e){function r(i,a,o,c){var l=f(t[i],t,a);if("throw"!==l.type){var u=l.arg,m=u.value;return m&&"object"==s(m)&&n.call(m,"__await")?e.resolve(m.__await).then((function(t){r("next",t,o,c)}),(function(t){r("throw",t,o,c)})):e.resolve(m).then((function(t){u.value=t,o(u)}),(function(t){return r("throw",t,o,c)}))}c(l.arg)}var a;i(this,"_invoke",{value:function(t,n){function i(){return new e((function(e,i){r(t,n,e,i)}))}return a=a?a.then(i,i):i()}})}function C(e,r,n){var i=g;return function(a,o){if(i===h)throw Error("Generator is already running");if(i===y){if("throw"===a)throw o;return{value:t,done:!0}}for(n.method=a,n.arg=o;;){var s=n.delegate;if(s){var c=D(s,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===g)throw i=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=h;var l=f(e,r,n);if("normal"===l.type){if(i=n.done?y:d,l.arg===v)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(i=y,n.method="throw",n.arg=l.arg)}}}function D(e,r){var n=r.method,i=e.iterator[n];if(i===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,D(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var a=f(i,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,v;var o=a.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function P(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function $(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function j(e){if(e||""===e){var r=e[o];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,a=function r(){for(;++i<e.length;)if(n.call(e,i))return r.value=e[i],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(s(e)+" is not iterable")}return b.prototype=w,i(F,"constructor",{value:w,configurable:!0}),i(w,"constructor",{value:b,configurable:!0}),b.displayName=m(w,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,m(t,u,"GeneratorFunction")),t.prototype=Object.create(F),t},e.awrap=function(t){return{__await:t}},L(O.prototype),m(O.prototype,l,(function(){return this})),e.AsyncIterator=O,e.async=function(t,r,n,i,a){void 0===a&&(a=Promise);var o=new O(p(t,r,n,i),a);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},L(F),m(F,u,"Generator"),m(F,o,(function(){return this})),m(F,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=j,$.prototype={constructor:$,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(P),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function i(n,i){return s.type="throw",s.arg=e,r.next=n,i&&(r.method="next",r.arg=t),!!i}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],s=o.completion;if("root"===o.tryLoc)return i("end");if(o.tryLoc<=this.prev){var c=n.call(o,"catchLoc"),l=n.call(o,"finallyLoc");if(c&&l){if(this.prev<o.catchLoc)return i(o.catchLoc,!0);if(this.prev<o.finallyLoc)return i(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return i(o.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return i(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=t,o.arg=e,a?(this.method="next",this.next=a.finallyLoc,v):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),P(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var i=n.arg;P(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:j(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function l(t,e,r,n,i,a,o){try{var s=t[a](o),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,i)}function u(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var a=t.apply(e,r);function o(t){l(a,n,i,o,s,"next",t)}function s(t){l(a,n,i,o,s,"throw",t)}o(void 0)}))}}var m={name:"AddOrEditControlSetting",components:{UserGroupSelect:a["a"]},props:{},data:function(){var t=this,e=function(t,e,r){if(!e)return r(new Error("请输入次数"));var n=/^[0-9]*[1-9][0-9]*$/;n.test(e)?r():r(new Error("限制次数必须为大于零的正整数"))},r=function(e,r,n){var i=e.field.split(".")[1],a=t.settingForm.timeSetting[i];a.sign_out_time&&a.sign_in_time&&a.sign_out_time===a.sign_in_time?n(new Error("晚归开始时间与结束时间不能相同")):n()};return{isLoading:!1,type:"",settingData:{},settingForm:{name:"",limitPass:-1,limitCount:"",userGroups:[],timeSetting:[{sign_in_time:"",sign_out_time:"",is_pass_holiday:!1,week_day_range:[],except_date_range:[],skipDays:[]}]},settingFormRules:{name:[{required:!0,message:"请输入名称",trigger:"blur"}],userGroups:[{required:!0,message:"请选择适用分组",trigger:"change"}],limitPass:[{required:!0,message:"请选择进出设置限制",trigger:"change"}],limitCount:[{required:!0,validator:e,trigger:"blur"}],validateSignTime:[{required:!0,validator:r,trigger:"change"}],week_day_range:[{required:!0,message:"请选择适用日期",trigger:"change"}]},weekList:o["weekList"]}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){var t=this;this.$route.query.data&&(this.settingData=JSON.parse(decodeURIComponent(this.$route.query.data)),this.settingForm.name=this.settingData.name,this.settingForm.limitPass=-1===this.settingData.limit_pass?-1:"count",this.settingForm.limitCount=-1===this.settingData.limit_pass?"":this.settingData.limit_pass,this.settingForm.userGroups=this.settingData.user_groups_ids,this.settingForm.timeSetting=[],this.settingData.access_control_time_settings.map((function(e,r){t.settingForm.timeSetting.push({}),t.$set(t.settingForm.timeSetting[r],"sign_in_time",e.sign_in_time),t.$set(t.settingForm.timeSetting[r],"sign_out_time",e.sign_out_time),t.$set(t.settingForm.timeSetting[r],"is_pass_holiday",e.is_pass_holiday),t.$set(t.settingForm.timeSetting[r],"week_day_range",e.week_day_range),t.$set(t.settingForm.timeSetting[r],"except_date_range",e.except_date_range),t.$set(t.settingForm.timeSetting[r],"skipDays",e.except_date_range)}))),this.$route.params.type&&(this.type=this.$route.params.type),this.weekList.map((function(e){t.$set(e,"disabled",!1)}))},saveSetting:function(){var t=this;this.$refs.settingFormRef.validate((function(e){if(e){t.settingForm.timeSetting.map((function(e){t.$delete(e,"skipDays"),e.sign_in_time||t.$delete(e,"sign_in_time"),e.sign_out_time||t.$delete(e,"sign_out_time")}));var r,n={name:t.settingForm.name,limit_pass:-1===t.settingForm.limitPass?t.settingForm.limitPass:t.settingForm.limitCount,card_user_groups:t.settingForm.userGroups,access_control_time_settings:t.settingForm.timeSetting};switch(t.type){case"add":r=t.$apis.apiBackgroundAttendanceAccessControlSettingsAddPost(n);break;case"edit":n.id=Number(t.settingData.id),r=t.$apis.apiBackgroundAttendanceAccessControlSettingsModifyPost(n);break}t.confirmOperation(r)}}))},confirmOperation:function(t){var e=this;return u(c().mark((function r(){var n;return c().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!e.isLoading){r.next=2;break}return r.abrupt("return");case 2:return e.isLoading=!0,r.next=5,t;case 5:n=r.sent,e.isLoading=!1,0===n.code?(e.$message.success("成功"),e.$closeCurrentTab(e.$route.path)):e.$message.error(n.msg);case 8:case"end":return r.stop()}}),r)})))()},addTimeSetting:function(){this.settingForm.timeSetting.push({sign_in_time:"",sign_out_time:"",is_pass_holiday:!1,week_day_range:[],except_date_range:[],skipDays:[]})},delTimeSetting:function(t){this.settingForm.timeSetting.splice(t,1)},changeSkipDays:function(t,e){e.except_date_range=e.skipDays},clearSkipDays:function(t){t.except_date_range=[],t.skipDays=""},delSkipDay:function(t,e){t.except_date_range.splice(e,1),t.skipDays=t.except_date_range},formatDateText:function(t){var e=t.split("-")[0]+"月"+t.split("-")[1]+"日";return e},visibleSelectWeek:function(t,e){var r=this;if(t){var n=!1;this.weekList.map((function(t){t.disabled=!1,r.settingForm.timeSetting.map((function(r,i){i!==e&&r.week_day_range.map((function(e){e===t.key&&(t.disabled=!0,n=!0)}))}))})),n&&(this.weekList[0].disabled=!0)}},changeWeek:function(t,e){-1!==t.indexOf("all")&&(this.settingForm.timeSetting[e].week_day_range=["1","2","3","4","5","6","7"])}}},p=m,f=(r("6889"),r("2877")),g=Object(f["a"])(p,n,i,!1,null,"7cbeada4",null);e["default"]=g.exports},6889:function(t,e,r){"use strict";r("d546")},9210:function(t,e,r){"use strict";r.r(e),r.d(e,"weekList",(function(){return u})),r.d(e,"recentSevenDay",(function(){return m})),r.d(e,"punchStatuaList",(function(){return p})),r.d(e,"getRequestParams",(function(){return f}));var n=r("5a0c");function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function a(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function o(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?a(Object(r),!0).forEach((function(e){s(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function s(t,e,r){return(e=c(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function c(t){var e=l(t,"string");return"symbol"==i(e)?e:e+""}function l(t,e){if("object"!=i(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var u=[{key:"all",name:"全部"},{key:"1",name:"周一"},{key:"2",name:"周二"},{key:"3",name:"周三"},{key:"4",name:"周四"},{key:"5",name:"周五"},{key:"6",name:"周六"},{key:"7",name:"周日"}],m=[n().subtract(7,"day").format("YYYY-MM-DD"),n().format("YYYY-MM-DD")],p=[{key:"sign_in",name:"签到"},{key:"sign_out",name:"签退"},{key:"be_late",name:"迟到"},{key:"leave_early",name:"早退"},{key:"for_leave",name:"请假"},{key:"absence_work",name:"缺卡"}],f=function(t,e,r){var n,i={};Object.keys(t).forEach((function(e){("select_time"!==e&&""!==t[e].value&&t[e].value||"boolean"===typeof t[e].value)&&(i[e]=t[e].value)}));var a=o({page:e,page_size:r},i);return 2===(null===(n=t.select_time)||void 0===n||null===(n=n.value)||void 0===n?void 0:n.length)&&(a.start_date=t.select_time.value[0]+" 00:00:00",a.end_date=t.select_time.value[1]+" 23:59:59"),a}},d546:function(t,e,r){}}]);