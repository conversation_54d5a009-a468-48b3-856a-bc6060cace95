(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-supervision-and-management-food-safety-management-compontents-CopyWorkforceDialog"],{5875:function(t,e,r){"use strict";r("8010")},8010:function(t,e,r){},ad12:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("el-drawer",{staticClass:"ps-el-drawer",attrs:{visible:t.visible,title:t.title,loading:t.isLoading,size:"678px",wrapperClosable:!1},on:{"update:visible":function(e){t.visible=e},"update:loading":function(e){t.isLoading=e},close:t.handleClose}},[e("div",{staticClass:"p-10"},[e("div",[t._v("已选择天数："+t._s(t.days)+" 天")]),e("el-date-picker",{staticClass:"ps-picker m-t-10",attrs:{type:"daterange",clearable:!0,format:"yyyy-MM-dd","value-format":"yyyy-MM-dd",align:"left","unlink-panels":"","range-separator":"⇀","start-placeholder":"请选择起始日期","end-placeholder":"请选择结束日期","picker-options":t.pickerOptions,"popper-class":"ps-poper-picker"},on:{change:t.dateChange},model:{value:t.chooseDate,callback:function(e){t.chooseDate=e},expression:"chooseDate"}})],1),e("div",{staticClass:"ps-el-drawer-footer m-l-20"},[e("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:t.isBtnLoading},on:{click:t.clickCancleHandle}},[t._v(" 取消 ")]),e("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.isBtnLoading,expression:"isBtnLoading"}],staticClass:"ps-btn",attrs:{type:"primary",disabled:t.isBtnLoading||!t.chooseDate||t.chooseDate.length<=0},on:{click:t.clickConfirmHandle}},[t._v(" 确定 ")])],1)])},o=[],i=r("ed08");function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},s=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function h(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,a=Object.create(i.prototype),c=new P(n||[]);return o(a,"_invoke",{value:O(t,r,c)}),a}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var d="suspendedStart",y="suspendedYield",v="executing",g="completed",m={};function b(){}function w(){}function L(){}var x={};f(x,s,(function(){return this}));var k=Object.getPrototypeOf,_=k&&k(k(M([])));_&&_!==r&&n.call(_,s)&&(x=_);var D=L.prototype=b.prototype=Object.create(x);function E(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function r(o,i,c,s){var u=p(t[o],t,i);if("throw"!==u.type){var l=u.arg,f=l.value;return f&&"object"==a(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,c,s)}),(function(t){r("throw",t,c,s)})):e.resolve(f).then((function(t){l.value=t,c(l)}),(function(t){return r("throw",t,c,s)}))}s(u.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function O(e,r,n){var o=d;return function(i,a){if(o===v)throw Error("Generator is already running");if(o===g){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var c=n.delegate;if(c){var s=j(c,n);if(s){if(s===m)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===d)throw o=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var u=p(e,r,n);if("normal"===u.type){if(o=n.done?g:y,u.arg===m)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(o=g,n.method="throw",n.arg=u.arg)}}}function j(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,j(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var i=p(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,m;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function C(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function N(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function P(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(C,this),this.reset(!0)}function M(e){if(e||""===e){var r=e[s];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(a(e)+" is not iterable")}return w.prototype=L,o(D,"constructor",{value:L,configurable:!0}),o(L,"constructor",{value:w,configurable:!0}),w.displayName=f(L,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,L):(t.__proto__=L,f(t,l,"GeneratorFunction")),t.prototype=Object.create(D),t},e.awrap=function(t){return{__await:t}},E(S.prototype),f(S.prototype,u,(function(){return this})),e.AsyncIterator=S,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new S(h(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},E(D),f(D,l,"Generator"),f(D,s,(function(){return this})),f(D,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=M,P.prototype={constructor:P,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(N),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return c.type="throw",c.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var s=n.call(a,"catchLoc"),u=n.call(a,"finallyLoc");if(s&&u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),N(r),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;N(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:M(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),m}},e}function s(t,e){return p(t)||h(t,e)||l(t,e)||u()}function u(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function l(t,e){if(t){if("string"==typeof t)return f(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?f(t,e):void 0}}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function h(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],s=!0,u=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);s=!0);}catch(t){u=!0,o=t}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw o}}return c}}function p(t){if(Array.isArray(t))return t}function d(t,e,r,n,o,i,a){try{var c=t[i](a),s=c.value}catch(t){return void r(t)}c.done?e(s):Promise.resolve(s).then(n,o)}function y(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){d(i,n,o,a,c,"next",t)}function c(t){d(i,n,o,a,c,"throw",t)}a(void 0)}))}}var v={name:"CopyWorkforceDialog",props:{loading:Boolean,isshow:Boolean,title:{type:String,default:"请选择复制到的日期"},limitId:{type:String,default:""},dialogType:{type:String,default:"default"},useDate:{type:String,default:""},confirm:Function},data:function(){return{isLoading:!1,isBtnLoading:!1,chooseDate:[],personList:[],days:0,pickerOptions:{disabledDate:function(t){var e=new Date;return e.setHours(0,0,0,0),t.getTime()<e.getTime()}}}},computed:{visible:{get:function(){return this.isshow},set:function(t){this.$emit("update:isshow",t)}}},watch:{visible:function(){this.visible}},mounted:function(){},methods:{clickCancleHandle:function(){this.handleClose()},clickConfirmHandle:function(){var t=this;return y(c().mark((function e(){var r,n,o,a,u,l;return c().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.chooseDate&&!(t.chooseDate.length<=0)){e.next=2;break}return e.abrupt("return",t.$message.error("请选择日期"));case 2:return r=t.personList.map((function(t){return t.id})),t.isBtnLoading=!0,n={job_person_ids:r,start_date:t.chooseDate[0]||"",end_date:t.chooseDate[1]||"",copy_date:t.useDate,operate_type:"copy"},e.next=7,Object(i["Z"])(t.$apis.apiBackgroundFundSupervisionCanteenSafetyManagementModifyPersonSchedulePost(n));case 7:if(o=e.sent,a=s(o,2),u=a[0],l=a[1],t.isBtnLoading=!1,!u){e.next=14;break}return e.abrupt("return",t.$message.error("保存失败"));case 14:l&&0===l.code?(t.$message.success("保存成功"),t.$emit("confirm",t.personList,t.chooseDate)):t.$message.error(l.msg);case 15:case"end":return e.stop()}}),e)})))()},handleClose:function(t){this.isLoading=!1,this.chooseDate=[],this.visible=!1,this.type="default",this.personList=[],this.days=0,this.$emit("close",!1)},setPersonList:function(t){t&&(this.personList=Object(i["f"])(t))},dateChange:function(t){this.days=this.calculateDays(t[0],t[1])},calculateDays:function(t,e){var r=new Date(t+" 00:00:00"),n=new Date(e+" 23:59:59");if(!isNaN(r)&&!isNaN(n)){var o=n-r,i=o/864e5;return Math.abs(Math.round(i))}alert("请输入有效的日期格式 (YYYY-MM-DD)")}}},g=v,m=(r("5875"),r("2877")),b=Object(m["a"])(g,n,o,!1,null,"6a3f7e7f",null);e["default"]=b.exports}}]);