(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-food-admin-IngredientsLibrary","view-merchant-meal-management-components-TreeSelect","view-merchant-meal-management-components-mealFoodList-NutritionTable","view-merchant-meal-management-food-admin-constants"],{"0449":function(t,e,n){"use strict";n.r(e),n.d(e,"DEFAULT_NUTRITION",(function(){return r})),n.d(e,"ELEMENT_NUTRITION",(function(){return a})),n.d(e,"VITAMIN_NUTRITION",(function(){return i})),n.d(e,"NUTRITION_LIST",(function(){return o})),n.d(e,"LIBRARY_SEARCH_SETTING_MERCHANT",(function(){return l})),n.d(e,"LIBRARY_SEARCH_SETTING_CATEORY",(function(){return u})),n.d(e,"COMMODITY_SEARCH_SETTING_MERCHANT",(function(){return s})),n.d(e,"SPEC_LIST",(function(){return c}));var r=[{name:"千卡",key:"energy_kcal",unit:"Kcal",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{name:"胆固醇",key:"cholesterol",unit:"mg",type:"default"},{name:"膳食纤维",key:"dietary_fiber",unit:"g",type:"default"}],a=[{key:"Ca",name:"钙",unit:"mg",type:"element"},{key:"P",name:"磷",unit:"mg",type:"element"},{key:"K",name:"钾",unit:"mg",type:"element"},{key:"Na",name:"钠",unit:"mg",type:"element"},{name:"镁",key:"Mg",unit:"mg",type:"element"},{key:"Fe",name:"铁",unit:"mg",type:"element"},{key:"I",name:"碘",unit:"μg",type:"element"},{key:"Se",name:"硒",unit:"μg",type:"element"},{key:"Zn",name:"锌",unit:"mg",type:"element"},{key:"Cu",name:"铜",unit:"mg",type:"element"},{key:"F",name:"氟",unit:"mg",type:"element"},{key:"Cr",name:"铬",unit:"μg",type:"element"},{key:"Mo",name:"钼",unit:"μg",type:"element"},{key:"Mn",name:"锰",unit:"mg",type:"element"}],i=[{key:"VA",name:"维生素A",unit:"μg",type:"vitamin"},{key:"VD",name:"维生素D",unit:"μg",type:"vitamin"},{key:"VE",name:"维生素E",unit:"mg",type:"vitamin"},{key:"VK",name:"维生素K",unit:"μg",type:"vitamin"},{key:"VB1",name:"维生素B1",unit:"mg",type:"vitamin"},{key:"VB2",name:"维生素B2",unit:"mg",type:"vitamin"},{key:"VB6",name:"维生素B6",unit:"mg",type:"vitamin"},{key:"VB12",name:"维生素B12",unit:"μg",type:"vitamin"},{key:"VC",name:"维生素C",unit:"mg",type:"vitamin"},{key:"VB5",name:"泛酸",unit:"mg",type:"vitamin"},{key:"VM",name:"叶酸",unit:"μg",type:"vitamin"},{key:"VB3",name:"烟酸",unit:"mg",type:"vitamin"},{key:"Choline",name:" 胆碱",unit:"mg",type:"vitamin"},{key:"Nicotinamide",name:"烟酰胺",unit:"mg",type:"vitamin"},{key:"VH",name:"生物素",unit:"mg",type:"vitamin"}],o=[].concat(r,a,i),l={select_time:{type:"datetimerange",label:"创建时间",format:"yyyy-MM-dd HH:mm:ss",value:[]},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(t){return{id:t.level+"_"+t.id,label:t.name,children:t.sort_list}}},create_source:{type:"organizationSelect",label:"创建来源",multiple:!1,checkStrictly:!0,isLazy:!1,value:"",placeholder:"请选择创建来源",dataList:[{name:"全部",id:""},{company:0,create_time:"2022-01-25 09:49:26",has_children:!1,id:1,level:-1,level_name:"",level_tag:-1,name:"系统",parent:null,status:"enable",status_alias:"正常",tree_id:-1}]},is_enable_nutrition:{type:"select",label:"营养录入",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"是",value:"1"},{label:"否",value:"0"}]},ingredient_name:{type:"input",label:"食材名称",value:"",placeholder:"请输入食材名称"},label_filter:{type:"select",label:"",value:"",placeholder:"",dataList:[{label:"请选择",value:"select",disabled:!0},{label:"包含",value:"Include"},{label:"不包含",value:"Exclude"}]},label_list:{type:"treeselect",label:"",value:[],placeholder:"选择标签",multiple:!0,limit:1,level:1,valueConsistsOf:"LEAF_PRIORITY",normalizer:function(t){return{id:t.id,label:t.name,children:t.label_list}},dataList:[]},is_entering:{type:"select",label:"录入图片",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"已录入",value:"1"},{label:"未录入",value:"0"}]},is_entering22:{type:"select",label:"是否应季",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"是",value:"1"},{label:"否",value:"0"}]}},u={select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"修改时间",value:[]},category:{type:"select",label:"分类",value:[],placeholder:"请选择分类",multiple:!0,collapseTags:!0,dataList:[{label:"挂失",value:"LOSS"},{label:"退卡",value:"QUIT"},{label:"使用中",value:"ENABLE"},{label:"未发卡",value:"UNUSED"}]},person_no:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"}},s={select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"创建时间",value:[]},nutrition:{type:"select",label:"营养录入",value:[],placeholder:"请选择",multiple:!0,collapseTags:!0,dataList:[{label:"全部",value:""},{label:"是",value:"1"},{label:"否",value:"0"}]},perso_no:{type:"input",label:"菜品/商品名称",value:"",placeholder:"请输入菜品/商品名称"},hasx:{type:"select",label:"已有菜品/商品",value:"",placeholder:"请选择已有菜品/商品",dataList:[{label:"是",value:!0},{label:"否",value:!1}]},attributes:{type:"select",label:"属性",value:"",placeholder:"请选择属性",dataList:[{label:"是",value:!0},{label:"否",value:!1}]}},c=[{name:"份"},{name:"碗"},{name:"瓶"},{name:"碟"},{name:"盅"},{name:"盆"},{name:"件"},{name:"串"},{name:"例"},{name:"只"},{name:"边"}]},"0e41":function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t._self._c;return e("div",[e("el-form",{ref:"formIngredients",attrs:{model:t.formData,rules:t.formRuls,size:"small"}},[t._l(t.currentNutritionList,(function(n){return[e("div",{key:n.key,staticClass:"nutrition-item"},[e("el-form-item",{attrs:{prop:n.key,label:n.name+"：",rules:t.formRuls.nutrition}},[e("el-input",{staticClass:"ps-input",staticStyle:{width:"120px"},attrs:{readonly:t.readonly,disabled:"",placeholder:"请输入"},on:{change:t.changeNutrition},model:{value:t.formData[n.key],callback:function(e){t.$set(t.formData,n.key,e)},expression:"formData[nutrition.key]"}}),e("span",{staticStyle:{"margin-left":"10px"}},[t._v(t._s(n.unit))])],1)],1)]})),e("div",{staticClass:"text-center pointer"},[e("span",{staticStyle:{color:"#027DB4"},on:{click:function(e){t.showAll=!t.showAll}}},[t._v(t._s(t.showAll?"收起":"查看更多营养信息"))])])],2)],1)},a=[],i=n("0449"),o={props:{tableDataNutrition:Object,readonly:{type:Boolean,default:!1}},data:function(){var t=function(t,e,n){if(e){var r=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;r.test(e)?n():n(new Error("营养数据有误，仅支持两位小数"))}else n()};return{formData:{},nutritionList:i["NUTRITION_LIST"],formRuls:{nutrition:[{validator:t,trigger:"change"}]},showAll:!1}},watch:{tableDataNutrition:{handler:function(t){this.initData()},deep:!0}},computed:{currentNutritionList:function(){var t=[];return t=this.showAll?this.nutritionList:this.nutritionList.slice(0,4),t}},mounted:function(){this.initData()},methods:{initData:function(t){var e=this;this.nutritionList.forEach((function(t){var n=e.tableDataNutrition[t.key]?e.tableDataNutrition[t.key]:0;e.$set(e.formData,t.key,n)}))},changeNutrition:function(t){this.$emit("update:tableDataNutrition",this.formData)}}},l=o,u=(n("f50f"),n("2877")),s=Object(u["a"])(l,r,a,!1,null,null,null);e["default"]=s.exports},"200a":function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t._self._c;return e("div",{staticClass:"ingredients-wrapper container-wrapper"},[e("refresh-tool",{on:{refreshPage:t.refreshHandle}}),e("search-form",{ref:"searchRef",attrs:{"form-setting":t.searchFormSetting,"label-width":"105px"},on:{search:t.searchHandle}}),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("数据列表")]),e("div",{staticClass:"align-r"},[e("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(e){return t.clickOrganization("batchorgAnization")}}},[t._v(" 批量应用组织 ")]),e("button-icon",{attrs:{color:"plain",type:"mul"},on:{click:t.clickcategoryAdmin}},[t._v("分类管理")]),e("button-icon",{attrs:{color:"plain",type:"export"},on:{click:function(e){return t.gotoExport()}}},[t._v("导出EXCEL")])],1)]),e("div",{staticClass:"table-content"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:"","header-row-class-name":"ps-table-header-row"},on:{"selection-change":t.handleSelectionChange}},[e("el-table-column",{attrs:{type:"selection",width:"50",align:"center","class-name":"ps-checkbox"}}),e("el-table-column",{attrs:{prop:"id",label:"序号",align:"center"}}),e("el-table-column",{attrs:{prop:"name",label:"食材名称",align:"center"}}),e("el-table-column",{attrs:{prop:"sort_name",label:"分类",align:"center"}}),e("el-table-column",{attrs:{prop:"name1",label:"营养信息",align:"center"},scopedSlots:t._u([{key:"default",fn:function(n){return[e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.clickShowDialogNutrition(n.row)}}},[t._v(" 查看 ")])]}}])}),e("el-table-column",{attrs:{prop:"organization_name",label:"创建来源",align:"center"}}),e("el-table-column",{attrs:{prop:"name1",label:"应用组织",align:"center"},scopedSlots:t._u([{key:"default",fn:function(n){return[e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.clickOrganization("see",n.row)}}},[t._v(" 查看 ")])]}}])}),e("el-table-column",{attrs:{prop:"create_time",label:"创建时间",align:"center"}}),e("el-table-column",{attrs:{fixed:"right",label:"操作",width:"180",align:"center"},scopedSlots:t._u([{key:"default",fn:function(n){return[e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.clickOrganization("editOperation",n.row)}}},[t._v(" 应用组织 ")])]}}])})],1)],1),e("el-dialog",{attrs:{title:"应用组织",visible:t.showDialogStructure,width:"30%","custom-class":"ps-dialog"},on:{"update:visible":function(e){t.showDialogStructure=e}}},[e("tree-select-ingredients",{attrs:{structureTree:t.structureTree,organizationDisabled:t.organizationDisabled,structureData:t.structureData},on:{inputTree:t.inputTree}}),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"ps-cancel-btn",on:{click:function(e){t.showDialogStructure=!1}}},[t._v("取 消")]),e("el-button",{staticClass:"ps-btn",attrs:{type:"primary"},on:{click:t.determineOrganization}},[t._v("确 定")])],1)],1),e("el-dialog",{attrs:{title:"营养信息",visible:t.showDialogNutrition,width:"50%","custom-class":"ps-dialog"},on:{"update:visible":function(e){t.showDialogNutrition=e}}},[e("nutrition-data",{attrs:{tableDataNutrition:t.tableDataNutrition}})],1),e("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[e("el-pagination",{staticClass:"ps-text",attrs:{"current-page":t.currentPage,"page-sizes":[10,20,50,100,500],"page-size":t.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:t.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1)],1)},a=[],i=n("ed08"),o=n("32b8"),l=n("0e41");function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function s(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function c(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?s(Object(n),!0).forEach((function(e){p(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function p(t,e,n){return(e=f(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function f(t){var e=d(t,"string");return"symbol"==u(e)?e:e+""}function d(t,e){if("object"!=u(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=u(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function m(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */m=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,a=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function c(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,n){return t[e]=n}}function p(t,e,n,r){var i=e&&e.prototype instanceof b?e:b,o=Object.create(i.prototype),l=new V(r||[]);return a(o,"_invoke",{value:N(t,n,l)}),o}function f(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=p;var d="suspendedStart",h="suspendedYield",g="executing",y="completed",v={};function b(){}function w(){}function _(){}var k={};c(k,o,(function(){return this}));var L=Object.getPrototypeOf,S=L&&L(L(I([])));S&&S!==n&&r.call(S,o)&&(k=S);var D=_.prototype=b.prototype=Object.create(k);function x(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function O(t,e){function n(a,i,o,l){var s=f(t[a],t,i);if("throw"!==s.type){var c=s.arg,p=c.value;return p&&"object"==u(p)&&r.call(p,"__await")?e.resolve(p.__await).then((function(t){n("next",t,o,l)}),(function(t){n("throw",t,o,l)})):e.resolve(p).then((function(t){c.value=t,o(c)}),(function(t){return n("throw",t,o,l)}))}l(s.arg)}var i;a(this,"_invoke",{value:function(t,r){function a(){return new e((function(e,a){n(t,r,e,a)}))}return i=i?i.then(a,a):a()}})}function N(e,n,r){var a=d;return function(i,o){if(a===g)throw Error("Generator is already running");if(a===y){if("throw"===i)throw o;return{value:t,done:!0}}for(r.method=i,r.arg=o;;){var l=r.delegate;if(l){var u=T(l,r);if(u){if(u===v)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===d)throw a=y,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=g;var s=f(e,n,r);if("normal"===s.type){if(a=r.done?y:h,s.arg===v)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(a=y,r.method="throw",r.arg=s.arg)}}}function T(e,n){var r=n.method,a=e.iterator[r];if(a===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,T(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var i=f(a,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,v;var o=i.arg;return o?o.done?(n[e.resultName]=o.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,v):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function C(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function V(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(C,this),this.reset(!0)}function I(e){if(e||""===e){var n=e[o];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,i=function n(){for(;++a<e.length;)if(r.call(e,a))return n.value=e[a],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw new TypeError(u(e)+" is not iterable")}return w.prototype=_,a(D,"constructor",{value:_,configurable:!0}),a(_,"constructor",{value:w,configurable:!0}),w.displayName=c(_,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,c(t,s,"GeneratorFunction")),t.prototype=Object.create(D),t},e.awrap=function(t){return{__await:t}},x(O.prototype),c(O.prototype,l,(function(){return this})),e.AsyncIterator=O,e.async=function(t,n,r,a,i){void 0===i&&(i=Promise);var o=new O(p(t,n,r,a),i);return e.isGeneratorFunction(n)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},x(D),c(D,s,"Generator"),c(D,o,(function(){return this})),c(D,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=I,V.prototype={constructor:V,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(E),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function a(r,a){return l.type="throw",l.arg=e,n.next=r,a&&(n.method="next",n.arg=t),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],l=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var u=r.call(o,"catchLoc"),s=r.call(o,"finallyLoc");if(u&&s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(u){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),E(n),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var a=r.arg;E(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:I(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),v}},e}function h(t,e){return w(t)||b(t,e)||y(t,e)||g()}function g(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function y(t,e){if(t){if("string"==typeof t)return v(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?v(t,e):void 0}}function v(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function b(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,a,i,o,l=[],u=!0,s=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=i.call(n)).done)&&(l.push(r.value),l.length!==e);u=!0);}catch(t){s=!0,a=t}finally{try{if(!u&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(s)throw a}}return l}}function w(t){if(Array.isArray(t))return t}function _(t,e,n,r,a,i,o){try{var l=t[i](o),u=l.value}catch(t){return void n(t)}l.done?e(u):Promise.resolve(u).then(r,a)}function k(t){return function(){var e=this,n=arguments;return new Promise((function(r,a){var i=t.apply(e,n);function o(t){_(i,r,a,o,l,"next",t)}function l(t){_(i,r,a,o,l,"throw",t)}o(void 0)}))}}var L={name:"ingredientsLibrary",components:{"tree-select-ingredients":o["default"],nutritionData:l["default"]},props:{},data:function(){return{isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[{name1:1}],structureTree:[],structureData:[],showDialogStructure:!1,searchFormSetting:{sort:{type:"select",label:"分类",value:"",listNameKey:"name",listValueKey:"id",placeholder:"请选择分类",multiple:!1,collapseTags:!0,filterable:!0,dataList:[]},name:{type:"input",label:"食材名称",value:"",placeholder:"请输入食材名称"}},showDialogNutrition:!1,tableDataNutrition:[],organizationDisabled:!1,structureType:"",dialogDataRow:{},selectListId:[]}},created:function(){this.initLoad(),this.getOrganizationTreeList(),this.foodIngredientSortList()},mounted:function(){},methods:{initLoad:function(){this.redientWarehouse()},searchHandle:Object(i["d"])((function(){this.currentPage=1,this.initLoad()}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.currentPage=1,this.initLoad()},foodIngredientSortList:function(){var t=this;return k(m().mark((function e(){var n,r,a,o;return m().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(i["Z"])(t.$apis.apiBackgroundFoodIngredientSortListPost({page:1,page_size:9999}));case 2:if(n=e.sent,r=h(n,2),a=r[0],o=r[1],!a){e.next=9;break}return t.$message.error(a.message),e.abrupt("return");case 9:0===o.code?t.searchFormSetting.sort.dataList=o.data.results:t.$message.error(o.msg);case 10:case"end":return e.stop()}}),e)})))()},clickOrganization:function(t,e){var n=this;if("batchorgAnization"===t&&!this.selectListId.length)return this.$message.error("请先选择数据！");this.structureData=[],this.showDialogStructure=!0,this.structureType=t,"see"===t?(this.organizationDisabled=!0,this.structureData=e.use_organizations):"editOperation"===t?(this.dialogDataRow=e,e.use_organizations.map((function(t){n.structureData.push(t.id)})),this.organizationDisabled=!1):this.organizationDisabled=!1},formatQueryParams:function(t){var e={};for(var n in t)""!==t[n].value&&("select_date"!==n?e[n]=t[n].value:t[n].value&&t[n].value.length>0&&(e.start_date=t[n].value[0],e.end_date=t[n].value[1]));return e},redientWarehouse:function(){var t=this;return k(m().mark((function e(){var n,r,a,o;return m().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(i["Z"])(t.$apis.apiBackgroundFoodIngredientWarehousePost(c(c({},t.formatQueryParams(t.searchFormSetting)),{},{page:t.currentPage,page_size:t.pageSize})));case 3:if(n=e.sent,r=h(n,2),a=r[0],o=r[1],t.isLoading=!1,!a){e.next=11;break}return t.$message.error(a.message),e.abrupt("return");case 11:0===o.code?(t.totalCount=o.data.count,t.tableData=o.data.results):t.$message.error(o.msg);case 12:case"end":return e.stop()}}),e)})))()},foodIngredientSync:function(){var t=this;return k(m().mark((function e(){var n,r,a,o;return m().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(i["Z"])(t.$apis.apiBackgroundFoodIngredientSyncPost({ids:"editOperation"===t.structureType?[t.dialogDataRow.id]:t.selectListId,organizations:t.structureData}));case 3:if(n=e.sent,r=h(n,2),a=r[0],o=r[1],t.isLoading=!1,!a){e.next=11;break}return t.$message.error(a.message),e.abrupt("return");case 11:0===o.code?(t.showDialogStructure=!1,t.redientWarehouse()):t.$message.error(o.msg);case 12:case"end":return e.stop()}}),e)})))()},confirmGrant:function(){},handleSizeChange:function(t){this.pageSize=t,this.redientWarehouse()},handleCurrentChange:function(t){this.currentPage=t,this.redientWarehouse()},handleSelectionChange:function(t){var e=this;this.selectListId=[];var n=Object.freeze(t);n.map((function(t){e.selectListId.push(t.id)}))},determineOrganization:function(){"see"!==this.structureType?this.foodIngredientSync():this.showDialogStructure=!1},inputTree:function(t){this.structureData=t},clickShowDialogNutrition:function(t){this.showDialogNutrition=!0,this.tableDataNutrition=[];var e=JSON.parse(t.nutrition.element),n=JSON.parse(t.nutrition.vitamin),r={energy_mj:t.nutrition.energy_mj?t.nutrition.energy_mj:0,energy_kcal:t.nutrition.energy_kcal?t.nutrition.energy_kcal:0,protein:t.nutrition.protein?t.nutrition.protein:0,axunge:t.nutrition.axunge?t.nutrition.axunge:0,Ca:e.Ca?e.Ca:0,P:e.P?e.P:0,K:e.K?e.K:0,Na:e.Na?e.Na:0,Mg:e.Mg?e.Mg:0,Fe:e.Fe?e.Fe:0,I:e.I?e.I:0,Zn:e.Zn?e.Zn:0,Se:e.Se?e.Se:0,Cu:e.Cu?e.Cu:0,F:e.F?e.F:0,Cr:e.Cr?e.Cr:0,Mn:e.Mn?e.Mn:0,Mo:e.Mo?e.Mo:0,VA:n.VA?n.VA:0,VD:n.VD?n.VD:0,VE:n.VE?n.VE:0,VK:n.VK?n.VK:0,VB1:n.VB1?n.VB1:0,VB2:n.VB2?n.VB2:0,VB6:n.VB6?n.VB6:0,VB12:n.VB12?n.VB12:0,VC:n.VC?n.VC:0,VB5:n.VB5?n.VB5:0,VM:n.VM?n.VM:0,VB3:n.VB3?n.VB3:0,Choline:n.Choline?n.Choline:0,Nicotinamide:n.Nicotinamide?n.Nicotinamide:0,VH:n.VH?n.VH:0};this.tableDataNutrition=[r]},getOrganizationTreeList:function(){var t=this;return k(m().mark((function e(){var n,r,a,o;return m().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(i["Z"])(t.$apis.apiBackgroundOrganizationOrganizationTreeListPost());case 2:if(n=e.sent,r=h(n,2),a=r[0],o=r[1],!a){e.next=9;break}return t.$message.error(a.message),e.abrupt("return");case 9:0===o.code?t.structureTree=t.deleteEmptyGroup(o.data):t.$message.error(o.msg);case 10:case"end":return e.stop()}}),e)})))()},deleteEmptyGroup:function(t){var e=this;function n(t){t.map((function(t){t.children_list&&t.children_list.length>0?n(t.children_list):e.$delete(t,"children_list")}))}return n(t),t},gotoExport:function(){this.$message.error("暂无导出")},clickcategoryAdmin:function(){this.$router.push({name:"MerchantIngredientsCategory"})}}},S=L,D=(n("bc9d"),n("2877")),x=Object(D["a"])(S,r,a,!1,null,null,null);e["default"]=x.exports},"32b8":function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t._self._c;return e("div",[e("tree-select",{attrs:{multiple:!0,options:t.structureTree,"default-expand-level":1,normalizer:t.structureNormalizer,limit:3,limitText:function(t){return"+"+t},placeholder:"请选择","value-consists-of":"ALL",flat:!0,disabled:t.organizationDisabled},on:{input:t.inputTree},model:{value:t.structure,callback:function(e){t.structure=e},expression:"structure"}})],1)},a=[],i={name:"TreeSelectIngredients",props:{structureTree:{type:Array,default:function(){return[]}},structureData:{type:Array,default:function(){return[]}},organizationDisabled:Boolean},data:function(){return{}},computed:{structure:{get:function(){return this.structureData},set:function(t){this.$emit("update:structureData",t)}}},mounted:function(){},methods:{structureNormalizer:function(t){return{id:t.id,label:t.name,children:t.children_list}},inputTree:function(t){this.$emit("inputTree",t)}}},o=i,l=n("2877"),u=Object(l["a"])(o,r,a,!1,null,null,null);e["default"]=u.exports},"95d4":function(t,e,n){},bc9d:function(t,e,n){"use strict";n("fdbb")},f50f:function(t,e,n){"use strict";n("95d4")},fdbb:function(t,e,n){}}]);