(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-channel-manage-ChannelStatistics","homechannel"],{"0208":function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t._self._c;return e("div",{staticClass:"ChannelManagement container-wrapper"},[e("refresh-tool",{on:{refreshPage:t.refreshHandle}}),e("search-form",{ref:"searchRef",attrs:{"form-setting":t.searchSetting},on:{search:t.searchHandle,reset:t.reset<PERSON>and<PERSON>}},[e("template",{slot:"append"},[e("el-form-item",{staticClass:"block-label",attrs:{label:"渠道"}},[e("el-cascader",{ref:"channelMul",staticClass:"ps-select",staticStyle:{width:"80%"},attrs:{placeholder:"请选择",clearable:"",options:t.channelTreeList,"show-all-levels":!1,props:t.cascaderProps,"collapse-tags":""},on:{change:t.chooseChange},model:{value:t.searchSetting.channel_ids,callback:function(e){t.$set(t.searchSetting,"channel_ids",e)},expression:"searchSetting.channel_ids"}})],1),e("el-form-item",{staticClass:"block-label",attrs:{label:"行业类型"}},[e("el-select",{staticClass:"ps-select",staticStyle:{width:"100%"},attrs:{placeholder:"请选择",multiple:"","collapse-tags":"",size:"small"},on:{change:t.chooseChange},model:{value:t.searchSetting.industry,callback:function(e){t.$set(t.searchSetting,"industry",e)},expression:"searchSetting.industry"}},t._l(t.industryTypeList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)],1)],2),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("数据列表")]),e("div",{staticClass:"align-r"},[e("el-button",{staticClass:"ps-btn-theme",attrs:{size:"mini"},on:{click:function(e){return t.gotoExport()}}},[t._v(" "+t._s(t.$t("button.export"))+" ")])],1)]),e("div",{staticClass:"table-content"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:t.tableData,"row-key":"id",stripe:"","max-height":t.maxHeight,"header-row-class-name":"ps-table-header-row"}},t._l(t.tableSetting,(function(n,r){return e("table-column",{key:r,attrs:{col:n},scopedSlots:t._u([{key:"industryType",fn:function(e){var n=e.row;return[t._v(" "+t._s(t.formatIndustry(n.industry))+" ")]}}],null,!0)})})),1)],1),e("table-statistics",{staticClass:"m-b-20",attrs:{statistics:t.collect}}),t._e()],1)],1)},a=[],i=n("ed08"),o=n("f63a"),c=n("2232"),s=n("c938"),l=n("2f62");function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function h(t,e){return g(t)||m(t,e)||d(t,e)||f()}function f(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(t,e){if(t){if("string"==typeof t)return p(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?p(t,e):void 0}}function p(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function m(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,a,i,o,c=[],s=!0,l=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=i.call(n)).done)&&(c.push(r.value),c.length!==e);s=!0);}catch(t){l=!0,a=t}finally{try{if(!s&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(l)throw a}}return c}}function g(t){if(Array.isArray(t))return t}function y(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */y=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,a=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function l(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function(t,e,n){return t[e]=n}}function h(t,e,n,r){var i=e&&e.prototype instanceof b?e:b,o=Object.create(i.prototype),c=new D(r||[]);return a(o,"_invoke",{value:T(t,n,c)}),o}function f(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var d="suspendedStart",p="suspendedYield",m="executing",g="completed",v={};function b(){}function w(){}function S(){}var L={};l(L,o,(function(){return this}));var _=Object.getPrototypeOf,O=_&&_(_(H([])));O&&O!==n&&r.call(O,o)&&(L=O);var x=S.prototype=b.prototype=Object.create(L);function C(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function j(t,e){function n(a,i,o,c){var s=f(t[a],t,i);if("throw"!==s.type){var l=s.arg,h=l.value;return h&&"object"==u(h)&&r.call(h,"__await")?e.resolve(h.__await).then((function(t){n("next",t,o,c)}),(function(t){n("throw",t,o,c)})):e.resolve(h).then((function(t){l.value=t,o(l)}),(function(t){return n("throw",t,o,c)}))}c(s.arg)}var i;a(this,"_invoke",{value:function(t,r){function a(){return new e((function(e,a){n(t,r,e,a)}))}return i=i?i.then(a,a):a()}})}function T(e,n,r){var a=d;return function(i,o){if(a===m)throw Error("Generator is already running");if(a===g){if("throw"===i)throw o;return{value:t,done:!0}}for(r.method=i,r.arg=o;;){var c=r.delegate;if(c){var s=E(c,r);if(s){if(s===v)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===d)throw a=g,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=m;var l=f(e,n,r);if("normal"===l.type){if(a=r.done?g:p,l.arg===v)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(a=g,r.method="throw",r.arg=l.arg)}}}function E(e,n){var r=n.method,a=e.iterator[r];if(a===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,E(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var i=f(a,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,v;var o=i.arg;return o?o.done?(n[e.resultName]=o.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,v):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function P(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function k(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function D(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(P,this),this.reset(!0)}function H(e){if(e||""===e){var n=e[o];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,i=function n(){for(;++a<e.length;)if(r.call(e,a))return n.value=e[a],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw new TypeError(u(e)+" is not iterable")}return w.prototype=S,a(x,"constructor",{value:S,configurable:!0}),a(S,"constructor",{value:w,configurable:!0}),w.displayName=l(S,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,S):(t.__proto__=S,l(t,s,"GeneratorFunction")),t.prototype=Object.create(x),t},e.awrap=function(t){return{__await:t}},C(j.prototype),l(j.prototype,c,(function(){return this})),e.AsyncIterator=j,e.async=function(t,n,r,a,i){void 0===i&&(i=Promise);var o=new j(h(t,n,r,a),i);return e.isGeneratorFunction(n)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},C(x),l(x,s,"Generator"),l(x,o,(function(){return this})),l(x,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=H,D.prototype={constructor:D,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(k),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function a(r,a){return c.type="throw",c.arg=e,n.next=r,a&&(n.method="next",n.arg=t),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],c=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var s=r.call(o,"catchLoc"),l=r.call(o,"finallyLoc");if(s&&l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),k(n),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var a=r.arg;k(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:H(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),v}},e}function v(t,e,n,r,a,i,o){try{var c=t[i](o),s=c.value}catch(t){return void n(t)}c.done?e(s):Promise.resolve(s).then(r,a)}function b(t){return function(){var e=this,n=arguments;return new Promise((function(r,a){var i=t.apply(e,n);function o(t){v(i,r,a,o,c,"next",t)}function c(t){v(i,r,a,o,c,"throw",t)}o(void 0)}))}}function w(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function S(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?w(Object(n),!0).forEach((function(e){L(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):w(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function L(t,e,n){return(e=_(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function _(t){var e=O(t,"string");return"symbol"==u(e)?e:e+""}function O(t,e){if("object"!=u(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=u(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var x={name:"ChannelStatistics",data:function(){return{isLoading:!1,searchSetting:Object(i["f"])(c["CHANNLE_STATISTICS_SEARCH_SETTING"]),tableSetting:Object(i["f"])(c["CHANNLE_STATISTICS_TABLE_SETTING"]),tableData:[],pageSize:1e3,totalCount:0,currentPage:1,cascaderProps:{label:"name",value:"id",children:"children_list",checkStrictly:!0,multiple:!0},startDate:"",endDate:"",channelTreeList:[],industryTypeList:s,collect:Object(i["f"])(c["CHANNLE_STATISTICS_TOTAL_SETTING"]),maxHeight:500,screenHeight:1080}},mixins:[o["a"]],created:function(){this.initLoad()},computed:S({},Object(l["c"])(["userInfo"])),watch:{screenHeight:function(t){this.maxHeight=t-500}},methods:{initLoad:function(){var t=this,e=Object(i["y"])(7);e&&(this.searchSetting.select_time.value=[e[0],e[1]],this.startDate=e[0],this.endDate=e[1]),this.getDataList(),this.initDic(),this.screenHeight=window.innerHeight,this.maxHeight=this.screenHeight-500,window.addEventListener("resize",(function(){t.screenHeight=window.innerHeight}))},initDic:function(){var t=this;return b(y().mark((function e(){var n;return y().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.getChannelTreeList();case 2:n=e.sent,Array.isArray(n)&&n.length>0&&(t.channelTreeList=Object(i["f"])(n));case 4:case"end":return e.stop()}}),e)})))()},searchHandle:Object(i["d"])((function(){this.currentPage=1,this.getDataList()}),300),refreshHandle:function(){this.currentPage=1,this.searchSetting=Object(i["f"])(c["CHANNLE_STATISTICS_SEARCH_SETTING"]),this.initLoad()},resetHandler:function(){this.searchSetting.channel_ids=[],this.searchSetting.industry=[],this.getDataList()},formatQueryParams:function(t){var e={};for(var n in t)if(Reflect.has(t[n],"value")&&""!==t[n].value&&null!==t[n].value)"org_name"===n?e[n]=t[n].value:"select_time"===n&&(this.startDate=t[n].value[0],this.endDate=t[n].value[1]);else if("channel_ids"===n&&t[n].length>0){var r=this.getCheckChannel(t[n]);e[n]=r}else"industry"===n&&t[n].length>0&&(e[n]=t[n]);return e},getDataList:function(){var t=this;return b(y().mark((function e(){var n,r,a,i,o,c,s,l,f;return y().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,n=S(S({},t.formatQueryParams(t.searchSetting)),{},{page:t.currentPage,page_size:t.pageSize,start_date:t.startDate,end_date:t.endDate}),e.next=4,t.$to(t.$apis.apiBackgroundChannelDataStatisticsGetStatisticsDataPost(n));case 4:if(r=e.sent,a=h(r,2),i=a[0],o=a[1],t.isLoading=!1,!i){e.next=13;break}return t.tableData=[],t.$message.error(i.message),e.abrupt("return");case 13:if(0===o.code){if(c=o.data||{},t.tableData=c.results||[],t.totalCount=c.count||0,s=c.summary_data||{},"object"===u(s)&&Object.keys(s).length>0)for(l in t.collect)f=t.collect[l].key,t.collect[l].value=s[f]}else t.tableData=[],t.$message.error(o.msg);case 14:case"end":return e.stop()}}),e)})))()},handleCurrentChange:function(t){this.currentPage=t,this.getDataList()},handleSizeChange:function(t){this.pageSize=t,this.getDataList()},gotoExport:function(){var t={url:"apiBackgroundChannelDataStatisticsGetStatisticsDataExportPost",params:Object(i["w"])(this.searchSetting,this.currentPage,this.pageSize)};this.exportHandle(t)},getChannelTreeList:function(){var t=this;return new Promise((function(e){var n=t.userInfo.channel_id||"";t.$apis.apiBackgroundChannelChannelTreeListPost({channel_id:n}).then((function(t){if(Reflect.has(t,"code")&&0===t.code){var n=t.data||{};Object(i["T"])(n.results,"children_list"),e(n.results)}e([])})).catch((function(t){e([])}))}))},formatIndustry:function(t){if(!t)return"";var e=this.industryTypeList.find((function(e){return e.id===t.toString()}));return e?e.name:""},chooseChange:function(t){this.searchHandle()},getCheckChannel:function(t){if(!t||0===t.length)return null;var e=this.$refs.channelMul?this.$refs.channelMul.getCheckedNodes():[],n=[];return e&&e.length>0&&e.forEach((function(t){n.push(t.value)})),n}}},C=x,j=n("2877"),T=Object(j["a"])(C,r,a,!1,null,"5b838372",null);e["default"]=T.exports},c938:function(t){t.exports=JSON.parse('[{"id":"1","name":"互联网/电子商务"},{"id":"2","name":"IT软件与服务"},{"id":"3","name":"IT硬件与设备"},{"id":"4","name":"电子技术"},{"id":"5","name":"通信与运营商"},{"id":"6","name":"网络游戏"},{"id":"7","name":"银行"},{"id":"8","name":"基金|理财|信托"},{"id":"9","name":"保险"},{"id":"10","name":"餐饮"},{"id":"11","name":"酒店"},{"id":"12","name":"旅游"},{"id":"13","name":"快递"},{"id":"14","name":"物流"},{"id":"15","name":"仓储"},{"id":"16","name":"培训"},{"id":"17","name":"院校"},{"id":"18","name":"学术科研"},{"id":"19","name":"交警"},{"id":"20","name":"博物馆"},{"id":"21","name":"公共事业|非盈利机构"},{"id":"22","name":"医药医疗"},{"id":"23","name":"护理美容"},{"id":"24","name":"保健与卫生"},{"id":"25","name":"汽车相关"},{"id":"26","name":"摩托车相关"},{"id":"27","name":"火车相关"},{"id":"28","name":"飞机相关"},{"id":"29","name":"建筑"},{"id":"30","name":"物业"},{"id":"31","name":"消费品"},{"id":"32","name":"法律"},{"id":"33","name":"会展"},{"id":"34","name":"中介服务"},{"id":"35","name":"认证"},{"id":"36","name":"审计"},{"id":"37","name":"传媒"},{"id":"38","name":"体育"},{"id":"39","name":"娱乐休闲"},{"id":"40","name":"印刷"},{"id":"41","name":"其它"},{"id":"42","name":"小学"},{"id":"43","name":"中学"},{"id":"44","name":"大学"},{"id":"45","name":"医院"}]')}}]);