(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-user-health-records-detail-BodyTesting"],{5698:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"body-testing records-wrapp-bg"},[e("div",{staticClass:"ps-flex-bw flex-align-c"},[e("div",{staticClass:"ps-flex flex-wrap"},[e("span",{staticClass:"p-r-10",staticStyle:{"font-weight":"bold"}},[t._v("身体检测")]),e("span",{staticClass:"testing-time"},[t._v("更新时间："+t._s(t.dartime))])]),e("div",{staticClass:"ps-flex flex-align-c flex-wrap"},[e("button-icon",{attrs:{color:"plain",type:"Import"}},[t._v(" 导入数据 ")]),e("button-icon",{attrs:{color:"plain",type:"export",size:"small"}},[t._v(" 导出数据 ")]),e("div",{staticClass:"m-l-5"},[e("el-button",{staticClass:"ps-origin-btn m-l-30",attrs:{type:"primary",size:"mini"},on:{click:t.gotoBodyDetail}},[e("div",{staticClass:"ps-flex flex-align-c"},[e("i",{staticClass:"iconfont icon-gengduo el-icon--left",staticStyle:{"font-size":"13px"}}),t._v(" 更多数据 ")])])],1)],1)]),e("div",{staticStyle:{"font-weight":"bold"}},[t._v("科室检查")]),e("div",{staticClass:"inspect-wrapp"},[Object.keys(t.formData)&&Object.keys(t.formData).length?e("div",t._l(t.formData,(function(r,n,i){return e("div",{key:i},[e("div",{staticClass:"l-title clearfix"},[e("span",[t._v(" "+t._s(r.name))])]),e("div",{staticClass:"inspect-content ps-flex flex-wrap"},t._l(r.children,(function(r,n,i){return e("div",{key:i,staticClass:"content-wrapp ps-flex-bw p-r-20 p-b-15"},[e("span",{staticClass:"text"},[t._v(t._s(r.name)+"："),e("span",{staticClass:"shuzi"},[t._v(t._s(r.value))])]),e("span",[t._v("-- "+t._s(r.unit))])])})),0)])})),0):e("el-empty",{attrs:{description:"暂无数据"}})],1)])},i=[],a=r("5a0c"),s=r.n(a);function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function c(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function l(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?c(Object(r),!0).forEach((function(e){f(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function f(t,e,r){return(e=p(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function p(t){var e=u(t,"string");return"symbol"==o(e)?e:e+""}function u(t,e){if("object"!=o(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var m={props:{formInfoData:{type:Object,default:function(){return{}}},paramsInfo:{type:Object,default:function(){return{}}}},data:function(){return{formData:{},dartime:"",aa:{"基本信息":{"姓名":"100(cm)","脉率":"100(cm)"},"人体成分":{BMI:"300(cm)","基础代谢":"100(cm)"}}}},watch:{formInfoData:function(t){this.formData=t}},created:function(){this.gartime()},mounted:function(){},methods:{gartime:function(){this.dartime=s()().format("YYYY-MM-DD hh-mm-ss")},gotoBodyDetail:function(){this.$router.push({name:"SuperBodyDetail",query:l({},this.paramsInfo)})}}},y=m,b=(r("85af"),r("2877")),d=Object(b["a"])(y,n,i,!1,null,"03929ff6",null);e["default"]=d.exports},"85af":function(t,e,r){"use strict";r("ea7a")},ea7a:function(t,e,r){}}]);