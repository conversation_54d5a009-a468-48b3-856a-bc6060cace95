(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-member-center-AddOrEditMemberPermission"],{"42a4":function(e,t,r){},"7f85":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"AddOrEditMemberPermission container-wrapper"},[t("div",{staticClass:"table-wrapper"},[t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[e._v(e._s("add"===e.type?"新建":"编辑")+"会员权限")])]),t("div",[t("el-form",{ref:"memberFormRef",attrs:{model:e.memberForm,rules:e.memberFormRules,"label-width":"180px"}},[t("el-form-item",{attrs:{label:"权限名称：",prop:"name"}},[t("el-input",{staticClass:"ps-input w-250",attrs:{maxlength:"15"},model:{value:e.memberForm.name,callback:function(t){e.$set(e.memberForm,"name",t)},expression:"memberForm.name"}})],1),t("el-form-item",{attrs:{label:"权限说明：",prop:"remark"}},[t("el-input",{staticClass:"ps-input w-250",attrs:{type:"textarea",rows:3,maxlength:"140","show-word-limit":""},model:{value:e.memberForm.remark,callback:function(t){e.$set(e.memberForm,"remark",t)},expression:"memberForm.remark"}})],1),t("el-form-item",{attrs:{label:"权限：",prop:"permission"}},[t("select-tree",e._g(e._b({staticClass:"search-item-w w-250",attrs:{treeData:e.permissionList,treeProps:e.treeProps,loadTree:e.getMemberPermission},model:{value:e.memberForm.permission,callback:function(t){e.$set(e.memberForm,"permission",t)},expression:"memberForm.permission"}},"select-tree",e.$attrs,!1),e.$listeners))],1),t("el-form-item",[t("el-button",{staticClass:"ps-origin-btn w-150",attrs:{size:"small",type:"primary"},on:{click:e.saveSetting}},[e._v("保存")])],1)],1)],1)])])},o=[],i=r("fb36");function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},c=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",m=i.toStringTag||"@@toStringTag";function l(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,r){return e[t]=r}}function f(e,t,r,n){var i=t&&t.prototype instanceof g?t:g,a=Object.create(i.prototype),s=new M(n||[]);return o(a,"_invoke",{value:O(e,r,s)}),a}function h(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var p="suspendedStart",d="suspendedYield",y="executing",v="completed",b={};function g(){}function w(){}function L(){}var x={};l(x,c,(function(){return this}));var k=Object.getPrototypeOf,F=k&&k(k(C([])));F&&F!==r&&n.call(F,c)&&(x=F);var E=L.prototype=g.prototype=Object.create(x);function P(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function _(e,t){function r(o,i,s,c){var u=h(e[o],e,i);if("throw"!==u.type){var m=u.arg,l=m.value;return l&&"object"==a(l)&&n.call(l,"__await")?t.resolve(l.__await).then((function(e){r("next",e,s,c)}),(function(e){r("throw",e,s,c)})):t.resolve(l).then((function(e){m.value=e,s(m)}),(function(e){return r("throw",e,s,c)}))}c(u.arg)}var i;o(this,"_invoke",{value:function(e,n){function o(){return new t((function(t,o){r(e,n,t,o)}))}return i=i?i.then(o,o):o()}})}function O(t,r,n){var o=p;return function(i,a){if(o===y)throw Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:e,done:!0}}for(n.method=i,n.arg=a;;){var s=n.delegate;if(s){var c=$(s,n);if(c){if(c===b)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=y;var u=h(t,r,n);if("normal"===u.type){if(o=n.done?v:d,u.arg===b)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(o=v,n.method="throw",n.arg=u.arg)}}}function $(t,r){var n=r.method,o=t.iterator[n];if(o===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,$(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),b;var i=h(o,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,b;var a=i.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,b):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,b)}function S(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function j(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function M(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(S,this),this.reset(!0)}function C(t){if(t||""===t){var r=t[c];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function r(){for(;++o<t.length;)if(n.call(t,o))return r.value=t[o],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(a(t)+" is not iterable")}return w.prototype=L,o(E,"constructor",{value:L,configurable:!0}),o(L,"constructor",{value:w,configurable:!0}),w.displayName=l(L,m,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,L):(e.__proto__=L,l(e,m,"GeneratorFunction")),e.prototype=Object.create(E),e},t.awrap=function(e){return{__await:e}},P(_.prototype),l(_.prototype,u,(function(){return this})),t.AsyncIterator=_,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new _(f(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},P(E),l(E,m,"Generator"),l(E,c,(function(){return this})),l(E,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=C,M.prototype={constructor:M,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(j),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function o(n,o){return s.type="throw",s.arg=t,r.next=n,o&&(r.method="next",r.arg=e),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),u=n.call(a,"finallyLoc");if(c&&u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,b):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),b},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),j(r),b}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;j(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:C(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),b}},t}function c(e,t,r,n,o,i,a){try{var s=e[i](a),c=s.value}catch(e){return void r(e)}s.done?t(c):Promise.resolve(c).then(n,o)}function u(e){return function(){var t=this,r=arguments;return new Promise((function(n,o){var i=e.apply(t,r);function a(e){c(i,n,o,a,s,"next",e)}function s(e){c(i,n,o,a,s,"throw",e)}a(void 0)}))}}var m={name:"AddOrEditMemberPermission",components:{SelectTree:i["a"]},props:{},data:function(){return{isLoading:!1,type:"",settingData:{},memberForm:{name:"",remark:"",permission:[]},memberFormRules:{name:[{required:!0,message:"请输入权限名称",trigger:"blur"}],permission:[{required:!0,message:"请选择权限",trigger:"blur"}]},permissionList:[],treeProps:{value:"key",label:"verbose_name",isLeaf:"is_leaf",children:"children"}}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.$route.query.data&&(this.settingData=JSON.parse(decodeURIComponent(this.$route.query.data)),this.memberForm.name=this.settingData.name,this.memberForm.permission=this.settingData.permission,this.memberForm.remark=this.settingData.remark),this.$route.params.type&&(this.type=this.$route.params.type),this.getMemberPermission()},saveSetting:function(){var e=this;this.$refs.memberFormRef.validate((function(t){if(t){var r,n={name:e.memberForm.name,permission:e.memberForm.permission};switch(e.memberForm.remark&&(n.remark=e.memberForm.remark),e.type){case"add":r=e.$apis.apiBackgroundMemberMemberPermissionAddPost(n);break;case"edit":n.id=Number(e.settingData.id),r=e.$apis.apiBackgroundMemberMemberPermissionModifyPost(n);break}e.confirmOperation(r)}}))},confirmOperation:function(e){var t=this;return u(s().mark((function r(){var n;return s().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!t.isLoading){r.next=2;break}return r.abrupt("return");case 2:return t.isLoading=!0,r.next=5,e;case 5:n=r.sent,t.isLoading=!1,0===n.code?(t.$message.success("成功"),t.$closeCurrentTab(t.$route.path)):t.$message.error(n.msg);case 8:case"end":return r.stop()}}),r)})))()},getMemberPermission:function(){var e=this;return u(s().mark((function t(){var r;return s().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,e.$apis.apiBackgroundMemberMemberPermissionGetMerchantPermissionsPost({page:1,page_size:99999});case 3:r=t.sent,e.isLoading=!1,0===r.code?e.permissionList=r.data:e.$message.error(r.msg);case 6:case"end":return t.stop()}}),t)})))()},addLabel:function(){this.memberForm.permissions.push("")},delLabel:function(e){this.memberForm.permissions.splice(e,1)}}},l=m,f=(r("bc0e"),r("2877")),h=Object(f["a"])(l,n,o,!1,null,"31e66d84",null);t["default"]=h.exports},bc0e:function(e,t,r){"use strict";r("42a4")}}]);