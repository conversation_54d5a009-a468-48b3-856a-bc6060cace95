(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-address-admin-AddressAreaAdmin","view-merchant-meal-management-address-admin-AddressAdmin","view-merchant-meal-management-address-admin-components-AddressAreaDialog","view-merchant-meal-management-address-admin-deliver-HengReport","view-merchant-meal-management-meal-report-MealPackageRule"],{"1c85":function(e,t,r){e.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}},"2cde":function(e,t,r){"use strict";r("eafb")},"6e59":function(e,t,r){"use strict";r("1c85")},"76d5":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"AddressAreaAdmin container-wrapper"},[t("refresh-tool",{on:{refreshPage:e.refreshHandle}}),t("search-form",{ref:"searchRef",attrs:{"form-setting":e.searchFormSetting,"label-width":"105px",autoSearch:!1},on:{search:e.searchHandle}}),t("div",{staticClass:"table-wrapper"},[t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[e._v("数据列表")]),t("div",{staticClass:"align-r"},[t("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["address.adders_area.add"],expression:"['address.adders_area.add']"}],attrs:{color:"origin",type:"add"},on:{click:function(t){return e.openDialog("add")}}},[e._v("新增")]),t("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["address.adders_area.modify"],expression:"['address.adders_area.modify']"}],attrs:{color:"plain"},on:{click:function(t){return e.openDialog("modify")}}},[e._v("区域编辑")]),t("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["address.adders_area.delete"],expression:"['address.adders_area.delete']"}],attrs:{color:"plain",type:"del"},on:{click:function(t){return e.mulOperation("mulDel")}}},[e._v("批量删除")]),t("button-icon",{attrs:{color:"plain"},on:{click:function(t){return e.openDialog("PrintTicket")}}},[e._v("小票打印")]),t("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["address.adders_area.clone"],expression:"['address.adders_area.clone']"}],attrs:{color:"plain",type:"mul"},on:{click:function(t){return e.openDialog("copy")}}},[e._v("批量复制")])],1)]),t("div",{staticClass:"table-content"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","row-class-name":e.tableRowClassName,"span-method":e.objectSpanMethod},on:{"selection-change":e.handleSelectionChange}},[t("el-table-column",{attrs:{type:"selection",width:"50",align:"center","class-name":"ps-checkbox"}}),t("el-table-column",{attrs:{prop:"name",label:"配送区域",align:"center"}}),t("el-table-column",{attrs:{prop:"organization_name",label:"组织",align:"center"}}),t("el-table-column",{attrs:{prop:"l0",label:"一级",align:"center"}}),t("el-table-column",{attrs:{prop:"l1",label:"二级",align:"center"}}),t("el-table-column",{attrs:{prop:"l2",label:"三级",align:"center"}}),t("el-table-column",{attrs:{prop:"l3",label:"四级",align:"center"}}),t("el-table-column",{attrs:{prop:"l4",label:"五级",align:"center"}}),t("el-table-column",{attrs:{width:"80",label:"操作",align:"center",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["address.adders_area.delete"],expression:"['address.adders_area.delete']"}],staticClass:"ps-warn",attrs:{type:"text",size:"small"},on:{click:function(t){return e.mulOperation("del",r.row)}}},[e._v(" 删除 ")])]}}])})],1)],1),t("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[t("el-pagination",{staticClass:"ps-text",attrs:{"current-page":e.currentPage,"page-sizes":[10,20,50,100,500],"page-size":e.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)]),t("address-area-dialog",{attrs:{isshow:e.dialogVisible,type:e.dialogType,title:e.dialogTitle,width:e.dialogwidth,"address-info":e.addressInfo,"select-list-id":e.selectListId},on:{"update:isshow":function(t){e.dialogVisible=t},confirm:e.searchHandle}}),t("print-ticket",{attrs:{isshow:e.dialogPrintVisible,type:"addressArea",title:"小票打印","select-list-id":e.selectListId},on:{"update:isshow":function(t){e.dialogPrintVisible=t},confirm:e.searchHandle}})],1)},a=[],i=r("ed08"),o=r("f262"),s=r("f1bf");function l(e){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},l(e)}function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function d(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(e){d=function(e,t,r){return e[t]=r}}function f(e,t,r,n){var i=t&&t.prototype instanceof b?t:b,o=Object.create(i.prototype),s=new F(n||[]);return a(o,"_invoke",{value:j(e,r,s)}),o}function h(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var p="suspendedStart",m="suspendedYield",g="executing",y="completed",v={};function b(){}function w(){}function x(){}var _={};d(_,o,(function(){return this}));var k=Object.getPrototypeOf,L=k&&k(k(I([])));L&&L!==r&&n.call(L,o)&&(_=L);var A=x.prototype=b.prototype=Object.create(_);function O(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function P(e,t){function r(a,i,o,s){var c=h(e[a],e,i);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==l(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,o,s)}),(function(e){r("throw",e,o,s)})):t.resolve(d).then((function(e){u.value=e,o(u)}),(function(e){return r("throw",e,o,s)}))}s(c.arg)}var i;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){r(e,n,t,a)}))}return i=i?i.then(a,a):a()}})}function j(t,r,n){var a=p;return function(i,o){if(a===g)throw Error("Generator is already running");if(a===y){if("throw"===i)throw o;return{value:e,done:!0}}for(n.method=i,n.arg=o;;){var s=n.delegate;if(s){var l=S(s,n);if(l){if(l===v)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===p)throw a=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=g;var c=h(t,r,n);if("normal"===c.type){if(a=n.done?y:m,c.arg===v)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(a=y,n.method="throw",n.arg=c.arg)}}}function S(t,r){var n=r.method,a=t.iterator[n];if(a===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,S(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=h(a,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var o=i.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,v):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function C(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function F(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(C,this),this.reset(!0)}function I(t){if(t||""===t){var r=t[o];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,i=function r(){for(;++a<t.length;)if(n.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(l(t)+" is not iterable")}return w.prototype=x,a(A,"constructor",{value:x,configurable:!0}),a(x,"constructor",{value:w,configurable:!0}),w.displayName=d(x,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,x):(e.__proto__=x,d(e,u,"GeneratorFunction")),e.prototype=Object.create(A),e},t.awrap=function(e){return{__await:e}},O(P.prototype),d(P.prototype,s,(function(){return this})),t.AsyncIterator=P,t.async=function(e,r,n,a,i){void 0===i&&(i=Promise);var o=new P(f(e,r,n,a),i);return t.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},O(A),d(A,u,"Generator"),d(A,o,(function(){return this})),d(A,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=I,F.prototype={constructor:F,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(E),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(n,a){return s.type="throw",s.arg=t,r.next=n,a&&(r.method="next",r.arg=e),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var l=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),E(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;E(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:I(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),v}},t}function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach((function(t){f(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function f(e,t,r){return(t=h(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function h(e){var t=p(e,"string");return"symbol"==l(t)?t:t+""}function p(e,t){if("object"!=l(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=l(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function m(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function g(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){m(i,n,a,o,s,"next",e)}function s(e){m(i,n,a,o,s,"throw",e)}o(void 0)}))}}var y={name:"AddressAreaAdmin",components:{AddressAreaDialog:o["default"],PrintTicket:s["a"]},data:function(){return{tableData:[],currentPage:1,pageSize:10,totalCount:0,isLoading:!1,searchFormSetting:{organization:{type:"organizationSelect",multiple:!1,isLazy:!1,clearable:!0,checkStrictly:!0,label:"组织",value:this.$store.getters.organization,placeholder:"请选择组织"},name:{type:"input",label:"配送区域",value:"",placeholder:"请输入配送区域",dataList:[]}},dialogVisible:!1,dialogType:"",dialogTitle:"",dialogwidth:"",addressInfo:{},selectListId:[],selectList:[],dialogPrintVisible:!1}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getAddressAreaList()},refreshHandle:function(){this.currentPage=1,this.tableData=[],this.initLoad()},searchHandle:Object(i["d"])((function(e){e&&"search"===e&&(this.dialogVisible=!1,this.dialogPrintVisible=!1,this.currentPage=1,this.getAddressAreaList())}),300),formatQueryParams:function(e){var t={};for(var r in e)""!==e[r].value&&("select_date"!==r?t[r]=e[r].value:e[r].value.length>0&&(t.start_date=e[r].value[0],t.end_date=e[r].value[1]));return t},getAddressAreaList:function(){var e=this;return g(c().mark((function t(){var r;return c().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiAddressAddersAreaListPost(d(d({},e.formatQueryParams(e.searchFormSetting)),{},{page:e.currentPage,page_size:e.pageSize}));case 2:if(r=t.sent,0!==r.code){t.next=11;break}if(e.tableData=[],e.totalCount=r.data.count,r.data.results){t.next=8;break}return t.abrupt("return");case 8:r.data.results.forEach((function(t,r){var n;n=!!(r%2);for(var a=0;a<t.adders_centers.length;a++){var i=0;1===t.adders_centers.length&&(i=-1),0===a&&1!==t.adders_centers.length&&(i=t.adders_centers.length),e.tableData.push({id:t.id,name:t.name,organization_id:t.organization_id,organization_name:t.organization_name,address_id:t.adders_centers[a].id,l0:t.adders_centers[a].l0,l1:t.adders_centers[a].l1,l2:t.adders_centers[a].l2,l3:t.adders_centers[a].l3,l4:t.adders_centers[a].l4,rowspan:i,rowColor:n})}})),t.next=12;break;case 11:e.$message.error(r.msg);case 12:case"end":return t.stop()}}),t)})))()},objectSpanMethod:function(e){var t=e.row,r=(e.column,e.rowIndex,e.columnIndex);if(0===r||1===r)return 1!==t.rowspan?{rowspan:t.rowspan,colspan:1}:{rowspan:0,colspan:0}},tableRowClassName:function(e){e.row;var t=e.rowIndex,r="";return(t+1)%2===0&&(r+="table-header-row"),r},handleSizeChange:function(e){this.pageSize=e,this.getAddressAreaList()},handleCurrentChange:function(e){this.currentPage=e,this.getAddressAreaList()},handleSelectionChange:function(e){var t=this;this.selectListId=[],this.selectList=Object.freeze(e),this.selectList.map((function(e){t.selectListId.push(e.id)}))},mulOperation:function(e,t){var r=this;if(!t&&!this.selectListId.length)return this.$message.error("请先选择数据！");var n="提示",a="";switch(e){case"mulDel":a="确定批量删除所选配送区域吗？";break;case"del":a="确定移除该配送点吗？";break}this.$confirm("".concat(a),"".concat(n),{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var n=g(c().mark((function n(a,i,o){var s;return c().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if("confirm"!==a){n.next=16;break}i.confirmButtonLoading=!0,s={},n.t0=e,n.next="mulDel"===n.t0?6:"del"===n.t0?8:11;break;case 6:return s.ids=r.selectListId,n.abrupt("break",11);case 8:return s.ids=[t.id],s.addr_center_id=t.address_id,n.abrupt("break",11);case 11:r.delAddressArea(s),o(),i.confirmButtonLoading=!1,n.next=17;break;case 16:i.confirmButtonLoading||o();case 17:case"end":return n.stop()}}),n)})));function a(e,t,r){return n.apply(this,arguments)}return a}()}).then((function(e){})).catch((function(e){}))},delAddressArea:function(e){var t=this;return g(c().mark((function r(){var n;return c().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,t.$apis.apiAddressAddersAreaDeletePost(e);case 2:n=r.sent,0===n.code?(t.$message.success("删除成功"),t.getAddressAreaList()):t.$message.error(n.msg);case 4:case"end":return r.stop()}}),r)})))()},openDialog:function(e,t){var r=this;switch(this.addressInfo=t,this.dialogType=e,e){case"add":this.dialogTitle="新增配送区域",this.dialogwidth="500px",this.dialogVisible=!0;break;case"modify":if(0===this.selectListId.length)return this.$message.error("请选择需要编辑的配送区域");if(this.selectListId.length>1)return this.$message.error("仅支持编辑单个配送区域");var n=[];this.tableData.forEach((function(e){r.selectListId[0]===e.id&&n.push(e.address_id)})),this.addressInfo=this.selectList[0],this.addressInfo.address_ids=n,this.dialogTitle="编辑配送区域",this.dialogwidth="500px",this.dialogVisible=!0;break;case"PrintTicket":if(!this.selectListId.length)return this.$message.error("请先选择数据！");this.dialogPrintVisible=!0;break;case"copy":if(this.dialogTitle="批量复制",!this.selectListId.length)return this.$message.error("请先选择数据！");this.dialogVisible=!0;break}}}},v=y,b=(r("2cde"),r("2877")),w=Object(b["a"])(v,n,a,!1,null,null,null);t["default"]=w.exports},c9d9:function(e,t,r){"use strict";r.d(t,"a",(function(){return i})),r.d(t,"d",(function(){return o})),r.d(t,"b",(function(){return s})),r.d(t,"c",(function(){return l})),r.d(t,"e",(function(){return c})),r.d(t,"f",(function(){return u})),r.d(t,"g",(function(){return d}));var n=r("5a0c"),a=r("da92"),i=[{label:"早餐",value:"breakfast",field:"breakfast_ahead",field2:"breakfast_fixed"},{label:"午餐",value:"lunch",field:"lunch_ahead",field2:"lunch_fixed"},{label:"下午茶",value:"afternoon",field:"hit_tea_ahead",field2:"hit_tea_fixed"},{label:"晚餐",value:"dinner",field:"dinner_ahead",field2:"dinner_fixed"},{label:"夜宵",value:"supper",field:"midnight_ahead",field2:"midnight_fixed"},{label:"凌晨餐",value:"morning",field:"early_ahead",field2:"early_fixed"}],o=[{label:"堂食",value:"on_scene",field:"on_scene_ahead",field2:"on_scene_fixed"},{label:"外卖",value:"waimai",field:"waimai_ahead",field2:"waimai_fixed"},{label:"食堂自提",value:"bale",field:"bale_ahead",field2:"bale_fixed"},{label:"取餐柜",value:"cupboard",field:"cupboard_ahead",field2:"cupboard_fixed"}],s={"早餐":"breakfast","午餐":"lunch","下午茶":"afternoon","晚餐":"dinner","夜宵":"supper","凌晨餐":"morning"},l=[{name:"千卡",key:"energy_kcal",unit:"kcal/kg",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"},{key:"Ca",name:"钙",unit:"mg"},{key:"P",name:"磷",unit:"mg"},{key:"K",name:"钾",unit:"mg"},{key:"Na",name:"钠",unit:"mg"},{name:"镁",key:"Mg",unit:"mg"},{key:"Fe",name:"铁",unit:"mg"},{key:"I",name:"碘",unit:"μg"},{key:"Se",name:"硒",unit:"μg"},{key:"Zn",name:"锌",unit:"mg"},{key:"Cu",name:"铜",unit:"mg"},{key:"F",name:"氟",unit:"mg"},{key:"Cr",name:"铬",unit:"μg"},{key:"Mo",name:"钼",unit:"μg"},{key:"Mn",name:"锰",unit:"mg"},{key:"VA",name:"维生素A",unit:"μg"},{key:"VD",name:"维生素D",unit:"μg"},{key:"VE",name:"维生素E",unit:"mg"},{key:"VK",name:"维生素K",unit:"μg"},{key:"VB1",name:"维生素B1",unit:"mg"},{key:"VB2",name:"维生素B2",unit:"mg"},{key:"VB6",name:"维生素B6",unit:"mg"},{key:"VB12",name:"维生素B12",unit:"μg"},{key:"VC",name:"维生素C",unit:"mg"},{key:"VB5",name:"泛酸",unit:"mg"},{key:"VM",name:"叶酸",unit:"μg"},{key:"VB3",name:"烟酸",unit:"mg"},{key:"Choline",name:" 胆碱",unit:"mg"},{key:"VH",name:"生物素",unit:"mg"}],c=[{label:"设备堂食",value:"instore"},{label:"预约-堂食",value:"on_scene"},{label:"预约-食堂自取",value:"bale"},{label:"预约-取餐柜取餐",value:"cupboard"},{label:"预约-外卖",value:"waimai"},{label:"报餐",value:"report"}],u=(n().subtract(7,"day").format("YYYY-MM-DD"),n().format("YYYY-MM-DD"),function(e){return e?"number"===typeof e?a["a"].divide(e,100).toFixed(2):"string"!==typeof e||isNaN(Number(e))?e:a["a"].divide(e,100).toFixed(2):"0.00"}),d=function(e){return a["a"].times(e,100)}},eafb:function(e,t,r){e.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}},f262:function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("dialog-message",{attrs:{show:e.visible,title:e.title,loading:e.isLoading,customClass:"ps-dialog",width:e.width,"destroy-on-close":!1},on:{"update:show":function(t){e.visible=t},"update:loading":function(t){e.isLoading=t},close:e.handleClose}},[e.visible?t("el-form",{ref:"dialogForm",attrs:{model:e.dialogForm,"status-icon":"",rules:e.dialogFormRules,"label-width":"120px"},nativeOn:{submit:function(e){e.preventDefault()}}},["add"===e.type||"modify"===e.type?t("div",[t("el-form-item",{attrs:{label:"名称：",prop:"name"}},[t("el-input",{staticClass:"ps-input w-250",attrs:{placeholder:"请输入配送点名称"},model:{value:e.dialogForm.name,callback:function(t){e.$set(e.dialogForm,"name",t)},expression:"dialogForm.name"}})],1),t("el-form-item",{attrs:{label:"组织：",prop:"org"}},[t("organization-select",{staticClass:"search-item-w ps-input w-250",attrs:{placeholder:"请选择所属组织",isLazy:!1,multiple:!1,"check-strictly":!0,"append-to-body":!0,disabled:"modify"===e.type},model:{value:e.dialogForm.org,callback:function(t){e.$set(e.dialogForm,"org",t)},expression:"dialogForm.org"}})],1),t("el-form-item",{attrs:{label:"配送点：",prop:"address"}},[t("address-select",{staticClass:"search-item-w ps-input w-250",attrs:{placeholder:"请选择配送点",multiple:!0,"check-strictly":!0,"is-select-child":!0,"default-expand-all":!0,orgId:e.dialogForm.org,"append-to-body":!0},model:{value:e.dialogForm.address,callback:function(t){e.$set(e.dialogForm,"address",t)},expression:"dialogForm.address"}})],1)],1):e._e(),"copy"===e.type?t("div",{staticStyle:{"margin-left":"90px"}},[t("div",{staticStyle:{"margin-bottom":"20px","font-weight":"bold"}},[e._v("请选择需要复制的组织:")]),t("el-form-item",{attrs:{label:"",prop:"orgs","label-width":"0px"}},[t("organization-select",{staticClass:"search-item-w ps-input w-250",attrs:{placeholder:"请选择需要复制的组织",isLazy:!1,multiple:!0,"check-strictly":!0,"append-to-body":!0},model:{value:e.dialogForm.orgs,callback:function(t){e.$set(e.dialogForm,"orgs",t)},expression:"dialogForm.orgs"}})],1),t("div",{staticStyle:{color:"#ff9b45"}},[e._v("非对应组织使用地址会在复制成功后相应剔除")])],1):e._e()]):e._e(),t("template",{slot:"tool"},[t("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:e.clickCancleHandle}},[e._v("取消")]),t("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickConfirmHandle}},[e._v("确定")])],1)])],2)},a=[],i=r("cbfb"),o=r("94c6");function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach((function(t){u(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function u(e,t,r){return(t=d(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function d(e){var t=f(e,"string");return"symbol"==s(t)?t:t+""}function f(e,t){if("object"!=s(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function h(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */h=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function d(e,t,r,n){var i=t&&t.prototype instanceof b?t:b,o=Object.create(i.prototype),s=new F(n||[]);return a(o,"_invoke",{value:j(e,r,s)}),o}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var p="suspendedStart",m="suspendedYield",g="executing",y="completed",v={};function b(){}function w(){}function x(){}var _={};u(_,o,(function(){return this}));var k=Object.getPrototypeOf,L=k&&k(k(I([])));L&&L!==r&&n.call(L,o)&&(_=L);var A=x.prototype=b.prototype=Object.create(_);function O(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function P(e,t){function r(a,i,o,l){var c=f(e[a],e,i);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==s(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,o,l)}),(function(e){r("throw",e,o,l)})):t.resolve(d).then((function(e){u.value=e,o(u)}),(function(e){return r("throw",e,o,l)}))}l(c.arg)}var i;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){r(e,n,t,a)}))}return i=i?i.then(a,a):a()}})}function j(t,r,n){var a=p;return function(i,o){if(a===g)throw Error("Generator is already running");if(a===y){if("throw"===i)throw o;return{value:e,done:!0}}for(n.method=i,n.arg=o;;){var s=n.delegate;if(s){var l=S(s,n);if(l){if(l===v)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===p)throw a=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=g;var c=f(t,r,n);if("normal"===c.type){if(a=n.done?y:m,c.arg===v)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(a=y,n.method="throw",n.arg=c.arg)}}}function S(t,r){var n=r.method,a=t.iterator[n];if(a===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,S(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=f(a,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var o=i.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,v):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function C(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function F(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(C,this),this.reset(!0)}function I(t){if(t||""===t){var r=t[o];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,i=function r(){for(;++a<t.length;)if(n.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(s(t)+" is not iterable")}return w.prototype=x,a(A,"constructor",{value:x,configurable:!0}),a(x,"constructor",{value:w,configurable:!0}),w.displayName=u(x,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,x):(e.__proto__=x,u(e,c,"GeneratorFunction")),e.prototype=Object.create(A),e},t.awrap=function(e){return{__await:e}},O(P.prototype),u(P.prototype,l,(function(){return this})),t.AsyncIterator=P,t.async=function(e,r,n,a,i){void 0===i&&(i=Promise);var o=new P(d(e,r,n,a),i);return t.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},O(A),u(A,c,"Generator"),u(A,o,(function(){return this})),u(A,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=I,F.prototype={constructor:F,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(E),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(n,a){return s.type="throw",s.arg=t,r.next=n,a&&(r.method="next",r.arg=e),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var l=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),E(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;E(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:I(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),v}},t}function p(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function m(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){p(i,n,a,o,s,"next",e)}function s(e){p(i,n,a,o,s,"throw",e)}o(void 0)}))}}var g={name:"AddressAreaDialog",components:{AddressSelect:o["a"],OrganizationSelect:i["a"]},props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:""},width:{type:String,default:"500px"},isshow:Boolean,confirm:Function,addressInfo:{type:Object,default:function(){}},selectListId:{type:Array,default:function(){return[]}}},data:function(){return{isLoading:!1,pageSize:10,totalCount:0,currentPage:1,time:(new Date).getTime(),dialogForm:{name:"",org:null,address:[],orgs:[]},allChildId:[],dialogFormRules:{name:[{required:!0,message:"请输入组织",trigger:"change"}],org:[{required:!0,message:"请选择所属组织",trigger:"blur"}],address:[{required:!0,message:"请选择配送点",trigger:"blur"}],orgs:[{required:!0,message:"请选择组织",trigger:"blur"}]}}},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},watch:{visible:function(){this.initLoad()}},created:function(){},mounted:function(){},methods:{initLoad:function(){"modify"===this.type&&(this.dialogForm.name=this.addressInfo.name,this.dialogForm.org=this.addressInfo.organization_id,this.dialogForm.address=this.addressInfo.address_ids)},clickConfirmHandle:function(e){var t=this;this.$refs.dialogForm.validate((function(e){e&&("add"===t.type||"modify"===t.type?t.getAllId():"copy"===t.type&&t.cloneAddressArea())}))},getAllId:function(){var e=this;return m(h().mark((function t(){var r;return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:r={},r.organization=e.dialogForm.org,r.name=e.dialogForm.name,r.use_points=e.dialogForm.address,"add"===e.type?e.confirmAdd(r):e.modifyAddress(r);case 5:case"end":return t.stop()}}),t)})))()},getAllChildren:function(e){var t=this;return m(h().mark((function r(){var n;return h().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,t.$apis.apiAddressAddersCenterGetAllChildrenPost({id:e});case 2:n=r.sent,0===n.code?(t.allChildId=t.allChildId.concat(n.data),t.allChildId.push(e)):t.$message.error(n.msg);case 4:case"end":return r.stop()}}),r)})))()},confirmAdd:function(e){var t=this;return m(h().mark((function r(){var n;return h().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!t.isLoading){r.next=2;break}return r.abrupt("return");case 2:return t.isLoading=!0,r.next=5,t.$apis.apiAddressAddersAreaAddPost(e);case 5:n=r.sent,t.isLoading=!1,0===n.code?(t.$emit("confirm","search"),t.$message.success(n.msg)):t.$message.error(n.msg);case 8:case"end":return r.stop()}}),r)})))()},modifyAddress:function(e){var t=this;return m(h().mark((function r(){var n;return h().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!t.isLoading){r.next=2;break}return r.abrupt("return");case 2:return t.isLoading=!0,r.next=5,t.$apis.apiAddressAddersAreaModifyPost(c({id:t.addressInfo.id},e));case 5:n=r.sent,t.isLoading=!1,0===n.code?(t.$emit("confirm","search"),t.$message.success(n.msg)):t.$message.error(n.msg);case 8:case"end":return r.stop()}}),r)})))()},cloneAddressArea:function(e){var t=this;return m(h().mark((function e(){var r;return h().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.isLoading){e.next=2;break}return e.abrupt("return");case 2:return t.isLoading=!0,e.next=5,t.$apis.apiAddressAddersAreaClonePost({ids:t.selectListId,used_orgs:t.dialogForm.orgs});case 5:r=e.sent,t.isLoading=!1,0===r.code?(t.$emit("confirm","search"),t.$message.success(r.msg)):t.$message.error(r.msg);case 8:case"end":return e.stop()}}),e)})))()},clickCancleHandle:function(){this.visible=!1},handleClose:function(e){this.dialogForm={name:"",org:null,address:[]},this.isLoading=!1,this.visible=!1},getOrganization:function(e){this.dialogForm.org=e}}},y=g,v=(r("6e59"),r("2877")),b=Object(v["a"])(y,n,a,!1,null,null,null);t["default"]=b.exports}}]);