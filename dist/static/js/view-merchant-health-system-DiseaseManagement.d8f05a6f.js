(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-health-system-DiseaseManagement"],{"8cda":function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"disease container-wrapper has-organization"},[t("refresh-tool",{on:{refreshPage:e.refreshHandle}}),t("div",{attrs:{id:"classification-container"}},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.diseaseLoading,expression:"diseaseLoading"}],staticClass:"disease-tree p-t-20 p-b-20"},[t("div",[t("div",{staticClass:"p-l-20 p-b-20 p-r-20"},[t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_healthy.disease.add"],expression:"['background_healthy.disease.add']"}],staticClass:"ps-origin-btn m-b-20",on:{click:function(t){return e.clickAddDiseaseName("add")}}},[e._v(" 添加疾病 ")]),t("el-input",{staticClass:"tree-search ps-input",attrs:{placeholder:"输入关键词搜索"},on:{input:e.searchHandle},model:{value:e.diseaseName,callback:function(t){e.diseaseName=t},expression:"diseaseName"}})],1),t("el-scrollbar",{attrs:{"wrap-class":"group-scrollbar-wrapper"}},[t("ul",{staticClass:"infinite-list"},e._l(e.diseaseList,(function(i,a){return t("li",{key:a},[t("div",{staticClass:"primary-disease",class:{active:e.activeIndex===a},on:{click:function(t){return e.clickDiseaseName(i,a)}}},[t("div",{staticClass:"disease-name ellipsis"},[e._v(e._s(i.name))]),i.is_admin?e._e():t("el-popover",{ref:"popoverRef",refInFor:!0,attrs:{placement:"bottom-start",trigger:"hover","popper-class":"popper-wrapp"}},[t("div",{staticClass:"popper-button"},[t("div",[t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_healthy.disease.modify"],expression:"['background_healthy.disease.modify']"}],staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.clickAddDiseaseName("modify",i)}}},[e._v(" 重命名 ")])],1),t("div",[t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_healthy.disease.delete"],expression:"['background_healthy.disease.delete']"}],staticClass:"ps-red",attrs:{type:"text",size:"small"},on:{click:function(t){return e.deleteDiseaseHandler(i)}}},[e._v(" 删除 ")])],1)]),t("div",{attrs:{slot:"reference"},slot:"reference"},[t("i",{staticClass:"el-icon-more tree-icon",attrs:{slot:"reference"},slot:"reference"})])])],1)])})),0)])],1)]),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.diseaseDetailsLoading,expression:"diseaseDetailsLoading"}],staticClass:"disease-data p-20"},[t("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.diseaseDetailsLoading,expression:"diseaseDetailsLoading"}],ref:"diseaseForm",attrs:{model:e.diseaseDetails,"status-icon":"",rules:e.dialogDiseaseRules,"label-width":"80px"},nativeOn:{submit:function(e){e.preventDefault()}}},[t("div",{staticClass:"m-b-20"},[e._v("选择标签")]),e._l(e.labelList,(function(i){return t("el-form-item",{key:i.key,attrs:{label:i.name}},[t("el-button",{staticClass:"ps-origin-btn m-r-10",attrs:{size:"small",disabled:e.diseaseDetails.is_admin||!e.allPermissions.includes("background_healthy.disease.modify")},on:{click:function(t){return e.showRightDialogHandle(i.type,e.diseaseDetails[i.key])}}},[e._v("添加")]),e._l(e.diseaseDetails[i.key],(function(a,r){return t("el-tag",{key:r,staticClass:"m-r-5 collapse-data",attrs:{size:"medium",effect:"plain",type:"info",color:"#fff",closable:!e.diseaseDetails.is_admin},on:{close:function(t){return e.deleteLableHandler(i.type,a,e.diseaseDetails[i.key],r)}}},[e._v(" "+e._s(a.label_name)+" ")])}))],2)})),t("div",{staticClass:"m-t-60 m-b-20"},[t("span",[e._v("营养素推荐")]),t("el-button",{staticClass:"ps-origin-btn float-r",attrs:{size:"small",disabled:e.diseaseDetails.is_admin||!e.allPermissions.includes("background_healthy.disease.modify")},on:{click:function(t){return e.showRightDialogHandle("addNutrition")}}},[e._v("添加")])],1),t("el-table",{ref:"tableData",staticClass:"ps-table-tree",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},e._l(e.tableSettings,(function(i){return t("table-column",{key:i.key,attrs:{col:i},scopedSlots:e._u([{key:"operation",fn:function(i){var a=i.row;return[t("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small",disabled:e.diseaseDetails.is_admin},on:{click:function(t){return e.showRightDialogHandle("modifyNutrition",a)}}},[e._v(" 编辑 ")]),t("span",{staticStyle:{margin:"0 10px",color:"#e2e8f0"}},[e._v("|")]),t("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small",disabled:e.diseaseDetails.is_admin},on:{click:function(t){return e.deleteNutritionHandler("single",a)}}},[e._v(" 删除 ")])]}}],null,!0)})})),1)],2)],1)]),t("div",{staticClass:"ps-el-drawer"},[e.showDialogDisease?t("el-drawer",{attrs:{title:e.dialogDiseaseTitle,visible:e.showDialogDisease,"show-close":!1,size:"40%"}},[t("div",{staticClass:"p-20"},[t("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.dialogDiseaseFormLoading,expression:"dialogDiseaseFormLoading"}],ref:"dialogDiseaseForm",attrs:{model:e.dialogDiseaseForm,"status-icon":"",rules:e.dialogDiseaseRules,"label-width":"80px"},nativeOn:{submit:function(e){e.preventDefault()}}},[t("el-form-item",{attrs:{label:"疾病名称",prop:"name"}},[t("el-input",{staticClass:"ps-input",staticStyle:{width:"190px"},attrs:{maxlength:10},model:{value:e.dialogDiseaseForm.name,callback:function(t){e.$set(e.dialogDiseaseForm,"name",t)},expression:"dialogDiseaseForm.name"}})],1)],1),t("div",{staticClass:"ps-el-drawer-footer"},[t("el-button",{on:{click:function(t){e.showDialogDisease=!1}}},[e._v("取 消")]),t("el-button",{staticClass:"ps-btn",attrs:{type:"primary",loading:e.dialogDiseaseFormLoading},on:{click:e.determineDiseaseDialog}},[e._v(" 确 定 ")])],1)],1)]):e._e()],1),t("disease-dialog",{attrs:{type:e.rightDialogType,"info-data":e.rightDialogInfo,"nutrition-list":e.nutritionData},on:{confirmdialog:e.confirmdialogHandle},model:{value:e.showRightDialog,callback:function(t){e.showRightDialog=t},expression:"showRightDialog"}})],1)},r=[],n=i("ed08"),s=i("c520"),o=i("5a50"),l=i("2f62");function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function u(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */u=function(){return t};var e,t={},i=Object.prototype,a=i.hasOwnProperty,r=Object.defineProperty||function(e,t,i){e[t]=i.value},n="function"==typeof Symbol?Symbol:{},s=n.iterator||"@@iterator",o=n.asyncIterator||"@@asyncIterator",l=n.toStringTag||"@@toStringTag";function d(e,t,i){return Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(e){d=function(e,t,i){return e[t]=i}}function f(e,t,i,a){var n=t&&t.prototype instanceof b?t:b,s=Object.create(n.prototype),o=new B(a||[]);return r(s,"_invoke",{value:$(e,i,o)}),s}function m(e,t,i){try{return{type:"normal",arg:e.call(t,i)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var g="suspendedStart",h="suspendedYield",p="executing",v="completed",y={};function b(){}function D(){}function w(){}var x={};d(x,s,(function(){return this}));var k=Object.getPrototypeOf,L=k&&k(k(P([])));L&&L!==i&&a.call(L,s)&&(x=L);var _=w.prototype=b.prototype=Object.create(x);function O(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function j(e,t){function i(r,n,s,o){var l=m(e[r],e,n);if("throw"!==l.type){var u=l.arg,d=u.value;return d&&"object"==c(d)&&a.call(d,"__await")?t.resolve(d.__await).then((function(e){i("next",e,s,o)}),(function(e){i("throw",e,s,o)})):t.resolve(d).then((function(e){u.value=e,s(u)}),(function(e){return i("throw",e,s,o)}))}o(l.arg)}var n;r(this,"_invoke",{value:function(e,a){function r(){return new t((function(t,r){i(e,a,t,r)}))}return n=n?n.then(r,r):r()}})}function $(t,i,a){var r=g;return function(n,s){if(r===p)throw Error("Generator is already running");if(r===v){if("throw"===n)throw s;return{value:e,done:!0}}for(a.method=n,a.arg=s;;){var o=a.delegate;if(o){var l=C(o,a);if(l){if(l===y)continue;return l}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(r===g)throw r=v,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);r=p;var c=m(t,i,a);if("normal"===c.type){if(r=a.done?v:h,c.arg===y)continue;return{value:c.arg,done:a.done}}"throw"===c.type&&(r=v,a.method="throw",a.arg=c.arg)}}}function C(t,i){var a=i.method,r=t.iterator[a];if(r===e)return i.delegate=null,"throw"===a&&t.iterator.return&&(i.method="return",i.arg=e,C(t,i),"throw"===i.method)||"return"!==a&&(i.method="throw",i.arg=new TypeError("The iterator does not provide a '"+a+"' method")),y;var n=m(r,t.iterator,i.arg);if("throw"===n.type)return i.method="throw",i.arg=n.arg,i.delegate=null,y;var s=n.arg;return s?s.done?(i[t.resultName]=s.value,i.next=t.nextLoc,"return"!==i.method&&(i.method="next",i.arg=e),i.delegate=null,y):s:(i.method="throw",i.arg=new TypeError("iterator result is not an object"),i.delegate=null,y)}function N(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function F(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function B(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(N,this),this.reset(!0)}function P(t){if(t||""===t){var i=t[s];if(i)return i.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,n=function i(){for(;++r<t.length;)if(a.call(t,r))return i.value=t[r],i.done=!1,i;return i.value=e,i.done=!0,i};return n.next=n}}throw new TypeError(c(t)+" is not iterable")}return D.prototype=w,r(_,"constructor",{value:w,configurable:!0}),r(w,"constructor",{value:D,configurable:!0}),D.displayName=d(w,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===D||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,d(e,l,"GeneratorFunction")),e.prototype=Object.create(_),e},t.awrap=function(e){return{__await:e}},O(j.prototype),d(j.prototype,o,(function(){return this})),t.AsyncIterator=j,t.async=function(e,i,a,r,n){void 0===n&&(n=Promise);var s=new j(f(e,i,a,r),n);return t.isGeneratorFunction(i)?s:s.next().then((function(e){return e.done?e.value:s.next()}))},O(_),d(_,l,"Generator"),d(_,s,(function(){return this})),d(_,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),i=[];for(var a in t)i.push(a);return i.reverse(),function e(){for(;i.length;){var a=i.pop();if(a in t)return e.value=a,e.done=!1,e}return e.done=!0,e}},t.values=P,B.prototype={constructor:B,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(F),!t)for(var i in this)"t"===i.charAt(0)&&a.call(this,i)&&!isNaN(+i.slice(1))&&(this[i]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var i=this;function r(a,r){return o.type="throw",o.arg=t,i.next=a,r&&(i.method="next",i.arg=e),!!r}for(var n=this.tryEntries.length-1;n>=0;--n){var s=this.tryEntries[n],o=s.completion;if("root"===s.tryLoc)return r("end");if(s.tryLoc<=this.prev){var l=a.call(s,"catchLoc"),c=a.call(s,"finallyLoc");if(l&&c){if(this.prev<s.catchLoc)return r(s.catchLoc,!0);if(this.prev<s.finallyLoc)return r(s.finallyLoc)}else if(l){if(this.prev<s.catchLoc)return r(s.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<s.finallyLoc)return r(s.finallyLoc)}}}},abrupt:function(e,t){for(var i=this.tryEntries.length-1;i>=0;--i){var r=this.tryEntries[i];if(r.tryLoc<=this.prev&&a.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var n=r;break}}n&&("break"===e||"continue"===e)&&n.tryLoc<=t&&t<=n.finallyLoc&&(n=null);var s=n?n.completion:{};return s.type=e,s.arg=t,n?(this.method="next",this.next=n.finallyLoc,y):this.complete(s)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.finallyLoc===e)return this.complete(i.completion,i.afterLoc),F(i),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.tryLoc===e){var a=i.completion;if("throw"===a.type){var r=a.arg;F(i)}return r}}throw Error("illegal catch attempt")},delegateYield:function(t,i,a){return this.delegate={iterator:P(t),resultName:i,nextLoc:a},"next"===this.method&&(this.arg=e),y}},t}function d(e,t){return p(e)||h(e,t)||m(e,t)||f()}function f(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function m(e,t){if(e){if("string"==typeof e)return g(e,t);var i={}.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?g(e,t):void 0}}function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,a=Array(t);i<t;i++)a[i]=e[i];return a}function h(e,t){var i=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=i){var a,r,n,s,o=[],l=!0,c=!1;try{if(n=(i=i.call(e)).next,0===t){if(Object(i)!==i)return;l=!1}else for(;!(l=(a=n.call(i)).done)&&(o.push(a.value),o.length!==t);l=!0);}catch(e){c=!0,r=e}finally{try{if(!l&&null!=i.return&&(s=i.return(),Object(s)!==s))return}finally{if(c)throw r}}return o}}function p(e){if(Array.isArray(e))return e}function v(e,t,i,a,r,n,s){try{var o=e[n](s),l=o.value}catch(e){return void i(e)}o.done?t(l):Promise.resolve(l).then(a,r)}function y(e){return function(){var t=this,i=arguments;return new Promise((function(a,r){var n=e.apply(t,i);function s(e){v(n,a,r,s,o,"next",e)}function o(e){v(n,a,r,s,o,"throw",e)}s(void 0)}))}}function b(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,a)}return i}function D(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?b(Object(i),!0).forEach((function(t){w(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):b(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function w(e,t,i){return(t=x(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function x(e){var t=k(e,"string");return"symbol"==c(t)?t:t+""}function k(e,t){if("object"!=c(e)||!e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var a=i.call(e,t||"default");if("object"!=c(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var L={name:"DiseaseManagement",props:{},components:{DiseaseDialog:s["default"]},data:function(){var e=function(e,t,i){var a=/^\d{1,4}$/;t&&!a.test(t)?i(new Error("请输入正确的疾病编号,8位数字")):i()};return{activeIndex:0,diseaseInfo:{},diseaseName:"",diseaseList:[],dialogDiseaseTitle:"",dialogDiseaseType:"",showDialogDisease:!1,dialogDiseaseFormLoading:!1,dialogDiseaseForm:{name:""},dialogDiseaseRules:{name:[{required:!0,message:"请输入疾病名称",trigger:["blur","change"]}],disease_number:[{required:!1,validator:e,trigger:["blur","change"]}]},dialogDiseaseInfo:{},labelList:[{name:"少食",key:"suitable_label",type:"suitable"},{name:"禁食",key:"not_recommend_label",type:"not_recommend"},{name:"推荐",key:"recommend_label",type:"recommend"}],diseaseDetails:{name:"",nutrition_control:{},suitable_label:[],not_recommend_label:[],recommend_label:[]},diseaseDetailsLoading:!1,diseaseLoading:!1,tableSettings:[{label:"营养素名称",key:"name"},{label:"推荐范围",key:"ranges"},{label:"操作",key:"operation",type:"slot",slotName:"operation"}],tableData:[],showRightDialog:!1,rightDialogType:"",rightDialogInfo:{},nutritionKeyList:{},allNutrition:[],nutritionData:[]}},created:function(){var e=this;Object(n["f"])(o["a"]).forEach((function(t){e.nutritionKeyList[t.key]=t})),this.initLoad()},computed:D({},Object(l["c"])(["allPermissions"])),mounted:function(){},methods:{initLoad:function(){this.getDiseaseList()},searchHandle:Object(n["d"])((function(){this.initLoad()}),300),refreshHandle:function(){this.initLoad()},getDiseaseList:function(){var e=this;return y(u().mark((function t(){var i,a,r,s,o;return u().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.diseaseLoading=!0,i={page:1,page_size:9999},e.diseaseName&&(i.name=e.diseaseName),t.next=5,Object(n["Z"])(e.$apis.apiBackgroundHealthyDiseaseListPost(i));case 5:if(a=t.sent,r=d(a,2),s=r[0],o=r[1],e.diseaseLoading=!1,!s){t.next=13;break}return e.$message.error(s.message),t.abrupt("return");case 13:0===o.code?(e.diseaseList=o.data.results,o.data.results.length?(e.activeIndex=0,e.diseaseInfo=o.data.results[0],e.getDiseaseDetails()):e.initFormatData({})):e.$message.error(o.msg);case 14:case"end":return t.stop()}}),t)})))()},clickAddDiseaseName:function(e,t){this.dialogDiseaseType=e,this.dialogDiseaseForm={name:"modify"===e?t.name:""},"modify"===e?(this.dialogDiseaseTitle="修改",this.dialogDiseaseInfo=t):(this.dialogDiseaseTitle="新增",this.dialogDiseaseInfo={}),this.showDialogDisease=!0},deleteDiseaseHandler:function(e){var t=this;this.$confirm("删除后数据不可恢复，确定要删除？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-warn",center:!0,beforeClose:function(){var i=y(u().mark((function i(a,r,s){var o,l,c,f;return u().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if("confirm"!==a){i.next=19;break}if(!t.dialogDiseaseFormLoading){i.next=3;break}return i.abrupt("return",t.$message.error("请勿重复提交！"));case 3:return t.dialogDiseaseFormLoading=!0,r.confirmButtonLoading=!0,i.next=7,Object(n["Z"])(t.$apis.apiBackgroundHealthyDiseaseDeletePost({id:e.id}));case 7:if(o=i.sent,l=d(o,2),c=l[0],f=l[1],t.dialogDiseaseFormLoading=!1,!c){i.next=15;break}return t.$message.error(c.message),i.abrupt("return");case 15:0===f.code?(s(),t.$message.success(f.msg),t.getDiseaseList()):t.$message.error(f.msg),r.confirmButtonLoading=!1,i.next=20;break;case 19:r.confirmButtonLoading||s();case 20:case"end":return i.stop()}}),i)})));function a(e,t,a){return i.apply(this,arguments)}return a}()}).then((function(e){})).catch((function(e){}))},determineDiseaseDialog:function(){var e=this;this.$refs.dialogDiseaseForm.validate((function(t){if(t){var i={name:e.dialogDiseaseForm.name};"add"===e.dialogDiseaseType?e.getDiseaseAdd(i):e.getDiseaseModify(D(D({},i),{},{id:e.dialogDiseaseInfo.id}))}}))},getDiseaseAdd:function(e){var t=this;return y(u().mark((function i(){var a,r,s,o;return u().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return t.dialogDiseaseFormLoading=!0,i.next=3,Object(n["Z"])(t.$apis.apiBackgroundHealthyDiseaseAddPost(e));case 3:if(a=i.sent,r=d(a,2),s=r[0],o=r[1],t.dialogDiseaseFormLoading=!1,!s){i.next=11;break}return t.$message.error(s.message),i.abrupt("return");case 11:0===o.code?(t.showDialogDisease=!1,t.getDiseaseList()):t.$message.error(o.msg);case 12:case"end":return i.stop()}}),i)})))()},getDiseaseModify:function(e){var t=this;return y(u().mark((function i(){var a,r,s,o;return u().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return t.dialogDiseaseFormLoading=!0,i.next=3,Object(n["Z"])(t.$apis.apiBackgroundHealthyDiseaseModifyPost(e));case 3:if(a=i.sent,r=d(a,2),s=r[0],o=r[1],t.dialogDiseaseFormLoading=!1,!s){i.next=11;break}return t.$message.error(s.message),i.abrupt("return");case 11:0===o.code?(t.showDialogDisease=!1,t.getDiseaseList()):t.$message.error(o.msg);case 12:case"end":return i.stop()}}),i)})))()},getDiseaseDetails:function(){var e=this;return y(u().mark((function t(){var i,a,r,s;return u().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.diseaseDetailsLoading=!0,t.next=3,Object(n["Z"])(e.$apis.apiBackgroundHealthyDiseaseDetailsPost({id:e.diseaseInfo.id}));case 3:if(i=t.sent,a=d(i,2),r=a[0],s=a[1],e.diseaseDetailsLoading=!1,!r){t.next=11;break}return e.$message.error(r.message),t.abrupt("return");case 11:0===s.code?(e.diseaseDetails=D({disease_id:e.diseaseInfo.id,is_admin:e.diseaseInfo.is_admin},s.data),e.initFormatData(s.data)):e.$message.error(s.msg);case 12:case"end":return t.stop()}}),t)})))()},initFormatData:function(e){var t=this;this.allNutrition=[],this.tableData=e.nutrition_control.map((function(e){return t.allNutrition.push(e.key),e.name=t.nutritionKeyList[e.key].name,e.ranges="".concat(e.range_one,"~").concat(e.range_two).concat(t.nutritionKeyList[e.key].unit),e}))},clickDiseaseName:function(e,t){this.activeIndex=t,this.diseaseInfo=e,this.getDiseaseDetails()},getDiseaseSaveSetting:function(){var e=this;return y(u().mark((function t(){var i,a,r,s;return u().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(n["Z"])(e.$apis.apiBackgroundHealthyDiseaseSaveSettingPost(e.formatData()));case 2:if(i=t.sent,a=d(i,2),r=a[0],s=a[1],!r){t.next=9;break}return e.$message.error(r.message),t.abrupt("return");case 9:0===s.code?(e.$message.success("保存成功"),e.getDiseaseDetails()):e.$message.error(s.msg);case 10:case"end":return t.stop()}}),t)})))()},deleteLableHandler:function(e,t){var i=this;this.$confirm("确定删除该标签吗？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-warn",center:!0,beforeClose:function(){var a=y(u().mark((function a(r,s,o){var l,c,f,m;return u().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if("confirm"!==r){a.next=19;break}if(!i.dialogDiseaseFormLoading){a.next=3;break}return a.abrupt("return",i.$message.error("请勿重复提交！"));case 3:return i.dialogDiseaseFormLoading=!0,s.confirmButtonLoading=!0,a.next=7,Object(n["Z"])(i.$apis.apiBackgroundHealthyDiseaseDeleteLabelPost({id:i.diseaseInfo.id,label_ids:[t.id],label_type:e}));case 7:if(l=a.sent,c=d(l,2),f=c[0],m=c[1],i.dialogDiseaseFormLoading=!1,!f){a.next=15;break}return i.$message.error(f.message),a.abrupt("return");case 15:0===m.code?(o(),i.$message.success(m.msg),i.getDiseaseDetails()):i.$message.error(m.msg),s.confirmButtonLoading=!1,a.next=20;break;case 19:s.confirmButtonLoading||o();case 20:case"end":return a.stop()}}),a)})));function r(e,t,i){return a.apply(this,arguments)}return r}()}).then((function(e){})).catch((function(e){}))},deleteNutritionHandler:function(e,t){var i=this;this.$confirm("删除后数据不可恢复，确定要删除？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-warn",center:!0,beforeClose:function(){var e=y(u().mark((function e(a,r,s){var o,l,c,f;return u().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("confirm"!==a){e.next=19;break}if(!i.dialogDiseaseFormLoading){e.next=3;break}return e.abrupt("return",i.$message.error("请勿重复提交！"));case 3:return i.dialogDiseaseFormLoading=!0,r.confirmButtonLoading=!0,e.next=7,Object(n["Z"])(i.$apis.apiBackgroundHealthyDiseaseDeleteNutritionControlPost({id:i.diseaseInfo.id,nutrition_key:t.key}));case 7:if(o=e.sent,l=d(o,2),c=l[0],f=l[1],i.dialogDiseaseFormLoading=!1,!c){e.next=15;break}return i.$message.error(c.message),e.abrupt("return");case 15:0===f.code?(s(),i.$message.success(f.msg),i.getDiseaseDetails()):i.$message.error(f.msg),r.confirmButtonLoading=!1,e.next=20;break;case 19:r.confirmButtonLoading||s();case 20:case"end":return e.stop()}}),e)})));function a(t,i,a){return e.apply(this,arguments)}return a}()}).then((function(e){})).catch((function(e){}))},showRightDialogHandle:function(e,t){if(this.rightDialogType=e,"addNutrition"===e||"modifyNutrition"===e){var i=[];i=t?Object(n["f"])(this.allNutrition).splice(this.allNutrition.indexOf(t.key),1):Object(n["f"])(this.allNutrition),this.nutritionData=Object(n["f"])(o["a"]).map((function(e){return e.disabled=i.includes(e.key),e}))}this.rightDialogInfo=t?"modifyNutrition"===e?D(D({},Object(n["f"])(t)),{},{disease_id:this.diseaseInfo.id}):Object(n["f"])(this.diseaseDetails):{disease_id:this.diseaseInfo.id},this.showRightDialog=!0},confirmdialogHandle:function(){this.showRightDialog=!1,this.rightDialogType="",this.getDiseaseDetails()}}},_=L,O=(i("e1ee"),i("2877")),j=Object(O["a"])(_,a,r,!1,null,null,null);t["default"]=j.exports},dc85:function(e,t,i){e.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}},e1ee:function(e,t,i){"use strict";i("dc85")}}]);