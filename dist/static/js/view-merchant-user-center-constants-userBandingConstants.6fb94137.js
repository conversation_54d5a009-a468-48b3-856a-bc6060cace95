(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-user-center-constants-userBandingConstants"],{"4f90":function(e,a,l){"use strict";l.r(a),l.d(a,"TABLE_HEAD_DATA_USER_BINDING",(function(){return r})),l.d(a,"SEARCH_FORM_SET_DATA_USER_BINDING",(function(){return t})),l.d(a,"URL_TEMPLATE_MODEL",(function(){return n})),l.d(a,"TABLE_HEAD_DATA_IMPORT_USER_BINGING",(function(){return o}));var r=[{label:"水控账号",key:"username"},{label:"人员编号",key:"person_no"},{label:"姓名",key:"name"},{label:"性别",key:"gender_alias"},{label:"部门",key:"card_department_group_alias",type:"slot",slotName:"cardDepartmentGroupAlias"},{label:"分组",key:"card_user_group_alias",type:"slot",slotName:"cardUserGroupAlias"},{label:"操作",key:"operation",type:"slot",slotName:"operation",width:"100"}],t={person_no:{type:"input",value:"",label:"人员编号",placeholder:"请输入",clearable:!0},username:{type:"input",value:"",label:"水控账号",placeholder:"请输入",clearable:!0},name:{type:"input",value:"",label:"姓名",placeholder:"请输入",clearable:!0},card_department_group_ids:{type:"organizationDepartmentSelect",multiple:!0,isLazy:!1,checkStrictly:!0,label:"部门",value:[],placeholder:"请选择部门",clearable:!0},card_user_group_ids:{type:"groupSelect",label:"分组",value:[],placeholder:"请选择分组",multiple:!0,collapseTags:!0,clearable:!0}},n="/api/temporary/template_excel/水控账号导入.xlsx",o=[{label:"水控账号",key:"username"},{label:"人员编号",key:"person_no"}]}}]);