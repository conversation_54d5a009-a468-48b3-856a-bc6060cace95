(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-report-financial-statements-PersonMealReport","view-merchant-meal-management-booking-setting-CommonPagination","view-merchant-order-RechargeOrder","view-merchant-order-RefundOrder"],{"85e9":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[e("el-pagination",{staticClass:"ps-text",attrs:{"current-page":t.currentPage,"page-sizes":[10,20,50,100,500],"page-size":t.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:t.total,background:"","popper-class":"ps-popper-select"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange,"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e}}})],1)},o=[],i={data:function(){return{pageSize:10,currentPage:1}},props:{total:{type:Number},onPaginationChange:{type:Function}},methods:{handleSizeChange:function(t,e){this.pageSize=t,e||this.onPaginationChange({current:this.currentPage,pageSize:this.pageSize})},handleCurrentChange:function(t,e){this.currentPage=t,e||this.onPaginationChange({current:this.currentPage,pageSize:this.pageSize})}}},a=i,c=r("2877"),u=Object(c["a"])(a,n,o,!1,null,null,null);e["default"]=u.exports},"87ac":function(t,e,r){"use strict";var n=r("ed08"),o=r("2f62");function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function a(t,e){return f(t)||l(t,e)||u(t,e)||c()}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(t,e){if(t){if("string"==typeof t)return s(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?s(t,e):void 0}}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function l(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}function f(t){if(Array.isArray(t))return t}function h(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */h=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},c=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,a=Object.create(i.prototype),c=new D(n||[]);return o(a,"_invoke",{value:k(t,r,c)}),a}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var g="suspendedStart",y="suspendedYield",d="executing",v="completed",m={};function b(){}function w(){}function P(){}var S={};l(S,c,(function(){return this}));var x=Object.getPrototypeOf,O=x&&x(x(N([])));O&&O!==r&&n.call(O,c)&&(S=O);var _=P.prototype=b.prototype=Object.create(S);function j(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function L(t,e){function r(o,a,c,u){var s=p(t[o],t,a);if("throw"!==s.type){var l=s.arg,f=l.value;return f&&"object"==i(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,c,u)}),(function(t){r("throw",t,c,u)})):e.resolve(f).then((function(t){l.value=t,c(l)}),(function(t){return r("throw",t,c,u)}))}u(s.arg)}var a;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return a=a?a.then(o,o):o()}})}function k(e,r,n){var o=g;return function(i,a){if(o===d)throw Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var c=n.delegate;if(c){var u=E(c,n);if(u){if(u===m)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===g)throw o=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=d;var s=p(e,r,n);if("normal"===s.type){if(o=n.done?v:y,s.arg===m)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=v,n.method="throw",n.arg=s.arg)}}}function E(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,E(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var i=p(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,m;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function C(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function T(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function D(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(C,this),this.reset(!0)}function N(e){if(e||""===e){var r=e[c];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(i(e)+" is not iterable")}return w.prototype=P,o(_,"constructor",{value:P,configurable:!0}),o(P,"constructor",{value:w,configurable:!0}),w.displayName=l(P,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,P):(t.__proto__=P,l(t,s,"GeneratorFunction")),t.prototype=Object.create(_),t},e.awrap=function(t){return{__await:t}},j(L.prototype),l(L.prototype,u,(function(){return this})),e.AsyncIterator=L,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new L(f(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},j(_),l(_,s,"Generator"),l(_,c,(function(){return this})),l(_,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=N,D.prototype={constructor:D,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(T),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return c.type="throw",c.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(u&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),T(r),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;T(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:N(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),m}},e}function p(t,e,r,n,o,i,a){try{var c=t[i](a),u=c.value}catch(t){return void r(t)}c.done?e(u):Promise.resolve(u).then(n,o)}function g(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){p(i,n,o,a,c,"next",t)}function c(t){p(i,n,o,a,c,"throw",t)}a(void 0)}))}}function y(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function d(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?y(Object(r),!0).forEach((function(e){v(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function v(t,e,r){return(e=m(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function m(t){var e=b(t,"string");return"symbol"==i(e)?e:e+""}function b(t,e){if("object"!=i(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}e["a"]={data:function(){return{currentTableSetting:[],dialogPrintVisible:!1}},computed:d({},Object(o["c"])(["userInfo"])),mounted:function(){},methods:{initPrintSetting:function(){var t=this;return g(h().mark((function e(){var r,o;return h().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=[],e.next=3,t.getPrintSettingInfo();case 3:r=e.sent;try{r=r?Object(n["E"])(r):Object(n["E"])(t.tableSetting)}catch(i){r=Object(n["E"])(t.tableSetting)}r.length<12?(o=Object(n["m"])(t.tableSetting,r),o=t.deleteWidthKey(o),t.currentTableSetting=o):t.currentTableSetting=Object(n["m"])(t.tableSetting,r);case 6:case"end":return e.stop()}}),e)})))()},getPrintSettingInfo:function(){var t=this;return g(h().mark((function e(){var r,o,i,c,u;return h().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=null,e.next=3,Object(n["Z"])(t.$apis.apiBackgroundAdminAccountGetAccountPrintInfoPost({id:t.userInfo.account_id,print_key:t.printType}));case 3:if(o=e.sent,i=a(o,2),c=i[0],u=i[1],!c){e.next=10;break}return t.$message.error(c.message),e.abrupt("return",r);case 10:return 0===u.code?r=u.data:t.$message.error(u.msg),e.abrupt("return",r);case 12:case"end":return e.stop()}}),e)})))()},setPrintSettingInfo:function(t,e){var r=this;return g(h().mark((function o(){var i,c,u,s;return h().wrap((function(o){while(1)switch(o.prev=o.next){case 0:return o.next=2,Object(n["Z"])(r.$apis.apiBackgroundAdminAccountSetAccountPrintInfoPost(d({id:r.userInfo.account_id,print_key:r.printType,print_list:t},e)));case 2:if(i=o.sent,c=a(i,2),u=c[0],s=c[1],!u){o.next=9;break}return r.$message.error(u.message),o.abrupt("return");case 9:0===s.code?r.$message.success("设置成功"):r.$message.error(s.msg);case 10:case"end":return o.stop()}}),o)})))()},openPrintSetting:function(){this.dialogPrintVisible=!0},confirmPrintDialog:function(t,e){var r=this;return g(h().mark((function o(){var i;return h().wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(!t){o.next=6;break}return i=Object(n["f"])(t),i.length<12&&(i=r.deleteWidthKey(i)),o.next=5,r.setPrintSettingInfo(i,e);case 5:r.currentTableSetting=i;case 6:case"end":return o.stop()}}),o)})))()},deleteWidthKey:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"children";function r(t){t.map((function(t){t.width&&delete t.width,t.minWidth&&delete t.minWidth,t[e]&&t[e].length>0&&r(t[e])}))}return r(t),t},indexMethod:function(t){return(this.page-1)*this.pageSize+(t+1)},setCollectData:function(t){var e=this;this.collect.forEach((function(r){t.data.collect&&void 0!==t.data.collect[r.key]&&e.$set(r,"value",t.data.collect[r.key])}))},setSummaryData:function(t){var e=t.data.collect;e[this.tableSetting[0].key]="合计",this.tableData.length&&this.tableData.push(e)}},beforeDestroy:function(){}}},"999d":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",[e("div",{staticClass:"booking-meal-wrapper container-wrapper"},[e("refresh-tool",{on:{refreshPage:t.refreshHandle}}),e("search-form",{ref:"searchRef",attrs:{"form-setting":t.searchFormSetting,autoSearch:!1},on:{search:t.searchHandle}}),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("数据列表")]),e("div",{staticClass:"align-r"},[e("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_order.finance_report.person_meal_list_export"],expression:"['background_order.finance_report.person_meal_list_export']"}],attrs:{color:"plain",type:"export"},on:{click:t.gotoExport}},[t._v("导出Excel")]),e("button-icon",{attrs:{color:"plain"},on:{click:t.gotoPrint}},[t._v("打印")]),e("button-icon",{attrs:{color:"plain"},on:{click:t.openPrintSetting}},[t._v("报表设置")])],1)]),e("div",{staticClass:"table-content"},[e("custom-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{border:"","table-data":t.tableData,"table-setting":t.currentTableSetting,stripe:"",index:t.indexMethod,isFirst:t.isFirstSearch,"header-row-class-name":"ps-table-header-row"}})],1),e("table-statistics",{attrs:{statistics:t.collect}}),e("common-pagination",{ref:"pagination",attrs:{total:t.total,onPaginationChange:t.onPaginationChange}})],1)],1),t.dialogPrintVisible?e("print-setting",{attrs:{extraParams:{printType:t.printType},tableSetting:t.tableSetting,defaultCheckedSetting:t.currentTableSetting,show:t.dialogPrintVisible},on:{"update:show":function(e){t.dialogPrintVisible=e},confirm:t.confirmPrintDialog}}):t._e()],1)},o=[],i=r("85e9"),a=r("3eb4"),c=r("f63a"),u=r("87ac"),s=r("ed08");function l(t){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},l(t)}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function h(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach((function(e){p(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function p(t,e,r){return(e=g(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function g(t){var e=y(t,"string");return"symbol"==l(e)?e:e+""}function y(t,e){if("object"!=l(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=l(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function d(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */d=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,a=Object.create(i.prototype),c=new D(n||[]);return o(a,"_invoke",{value:k(t,r,c)}),a}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",g="suspendedYield",y="executing",v="completed",m={};function b(){}function w(){}function P(){}var S={};s(S,a,(function(){return this}));var x=Object.getPrototypeOf,O=x&&x(x(N([])));O&&O!==r&&n.call(O,a)&&(S=O);var _=P.prototype=b.prototype=Object.create(S);function j(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function L(t,e){function r(o,i,a,c){var u=h(t[o],t,i);if("throw"!==u.type){var s=u.arg,f=s.value;return f&&"object"==l(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,a,c)}),(function(t){r("throw",t,a,c)})):e.resolve(f).then((function(t){s.value=t,a(s)}),(function(t){return r("throw",t,a,c)}))}c(u.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function k(e,r,n){var o=p;return function(i,a){if(o===y)throw Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var c=n.delegate;if(c){var u=E(c,n);if(u){if(u===m)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=y;var s=h(e,r,n);if("normal"===s.type){if(o=n.done?v:g,s.arg===m)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=v,n.method="throw",n.arg=s.arg)}}}function E(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,E(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var i=h(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,m;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function C(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function T(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function D(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(C,this),this.reset(!0)}function N(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(l(e)+" is not iterable")}return w.prototype=P,o(_,"constructor",{value:P,configurable:!0}),o(P,"constructor",{value:w,configurable:!0}),w.displayName=s(P,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,P):(t.__proto__=P,s(t,u,"GeneratorFunction")),t.prototype=Object.create(_),t},e.awrap=function(t){return{__await:t}},j(L.prototype),s(L.prototype,c,(function(){return this})),e.AsyncIterator=L,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new L(f(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},j(_),s(_,u,"Generator"),s(_,a,(function(){return this})),s(_,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=N,D.prototype={constructor:D,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(T),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return c.type="throw",c.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(u&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),T(r),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;T(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:N(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),m}},e}function v(t,e,r,n,o,i,a){try{var c=t[i](a),u=c.value}catch(t){return void r(t)}c.done?e(u):Promise.resolve(u).then(n,o)}function m(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){v(i,n,o,a,c,"next",t)}function c(t){v(i,n,o,a,c,"throw",t)}a(void 0)}))}}var b={name:"DetailTotalList",components:{CommonPagination:i["default"]},mixins:[c["a"],u["a"]],data:function(){return{isLoading:!1,tableSetting:[{label:"姓名",key:"name"},{label:"人员编号",key:"person_no"},{label:"所属分组",key:"payer_group"},{label:"餐段",key:"meal_type_alias"},{label:"一级组织",key:"primary"},{label:"二级组织",key:"secondary"},{label:"三级组织",key:"company"},{label:"食堂",key:"canteen"},{label:"食堂区域",key:"area"},{label:"消费点",key:"consume_org_name"},{label:"点餐情况",key:"meal_status_alias"}],tableData:[],page:1,pageSize:10,total:0,searchFormSetting:Object(s["f"])(a["PersonMealReportSearchForm"]),collect:[{key:"user_count",value:0,label:"总人数:"},{key:"stall_count",value:0,label:"本档口点餐人数:"},{key:"other_stall_count",value:0,label:"其他档口点餐人数:"},{key:"no_meal_count",value:0,label:"未点餐人数:"}],currentTableSetting:[],dialogPrintVisible:!1,printType:"PersonMealReport",isFirstSearch:!0}},created:function(){this.initLoad(!0)},mounted:function(){this.initPrintSetting()},methods:{initLoad:function(t){t||(this.getPersonMealReportList(),this.requestDeviceType())},refreshHandle:function(){this.$refs.searchRef.resetForm(),this.$refs.pagination.handleCurrentChange(1,!0),this.$refs.pagination.handleSizeChange(10,!0),this.tableData=[],this.isFirstSearch=!0},searchHandle:function(t){var e=this;return m(d().mark((function r(){return d().wrap((function(r){while(1)switch(r.prev=r.next){case 0:t&&"search"===t&&(e.$refs.pagination.handleCurrentChange(1,!0),e.$refs.pagination.handleSizeChange(10,!0),e.onPaginationChange({current:1,pageSize:10}),e.isFirstSearch=!1);case 1:case"end":return r.stop()}}),r)})))()},formatQueryParams:function(t,e,r){var n={};for(var o in t)""!==t[o].value&&null!==t[o].value&&0!==t[o].value.length&&(n[o]=t[o].value);return n.page=e,n.page_size=r,n},getPersonMealReportList:function(){var t=this;return m(d().mark((function e(){var r,n;return d().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=t.formatQueryParams(t.searchFormSetting,t.page,t.pageSize),t.isLoading=!0,e.next=4,t.$apis.apiBackgroundReportCenterDataReportPersonMealListPost(r);case 4:n=e.sent,t.isLoading=!1,0===n.code?(t.total=n.data.count,t.tableData=n.data.result,t.setCollectData(n)):t.$message.error(n.msg);case 7:case"end":return e.stop()}}),e)})))()},onPaginationChange:function(t){this.page=t.current,this.pageSize=t.pageSize,this.getPersonMealReportList()},gotoExport:function(){var t={type:"ExportPersonMealReport",url:"apiBackgroundReportCenterDataReportPersonMealListExportPost",params:this.formatQueryParams(this.searchFormSetting,this.page,this.pageSize)};this.exportHandle(t)},gotoPrint:function(){var t=this.formatQueryParams(this.searchFormSetting),e=this.$router.resolve({name:"Print",query:{print_type:this.printType,print_title:"人员就餐统计报表",result_key:"result",api:"apiBackgroundReportCenterDataReportPersonMealListPost",show_print_header_and_footer:!0,table_setting:JSON.stringify(this.tableSetting),current_table_setting:JSON.stringify(this.currentTableSetting),collect:JSON.stringify(this.collect),push_summary:!1,params:JSON.stringify(h(h({},t),{},{page:1,page_size:this.total?this.total:10}))}}),r=e.href;window.open(r,"_blank")}}},w=b,P=r("2877"),S=Object(P["a"])(w,n,o,!1,null,"775c7a74",null);e["default"]=S.exports}}]);