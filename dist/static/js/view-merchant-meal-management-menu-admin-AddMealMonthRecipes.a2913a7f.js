(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-menu-admin-AddMealMonthRecipes"],{"1b11":function(t,e,n){},"8df8":function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t._self._c;return e("div",{staticClass:"add_meal_month_recipes container-wrapper"},[e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"table-wrapper"},[e("div",{staticClass:"tab m-b-20"},[e("div",{class:["tab-item","foodSeting"===t.foodMenuType?"active":""],on:{click:function(e){return t.tabClick("foodSeting")}}},[t._v(" 菜品配置 ")]),t.isNutritionGuidance?e("div",{class:["tab-item","nutritionAnalysis"===t.foodMenuType?"active":""],on:{click:function(e){return t.tabClick("nutritionAnalysis")}}},[t._v(" 营养分析 ")]):t._e()]),"foodSeting"===t.foodMenuType?e("div",t._l(t.mealList,(function(n,r){return e("div",{key:r,staticClass:"meal-wrapper"},[e("div",{staticClass:"meal-content p-b-10"},[e("div",{staticClass:"meal-text"},[t._v(" "+t._s(n.label)+"（"+t._s(n.dataList?n.dataList.food_data.length||n.dataList.set_meal_data.length:0)+"） ")]),e("div",{staticClass:"operate"},[n.dataList&&(n.dataList.food_data.length||n.dataList.set_meal_data.length)?e("span",{staticClass:"catering-text",on:{click:function(e){return t.clickMenuCatering(n)}}},[t._v(" 配餐 ")]):e("span",{staticClass:"catering-no-text",on:{click:function(e){return t.clickMenuCatering(n)}}},[t._v("未配餐")]),n.dataList&&(n.dataList.food_data.length||n.dataList.set_meal_data.length)?e("span",{staticClass:"cope-text",on:{click:function(e){return t.copyMeal(n)}}},[t._v(" 复制 ")]):t._e()])]),!t.isNutritionGuidance||"breakfast"!==n.value&&"lunch"!==n.value&&"dinner"!==n.value?t._e():e("div",{staticClass:"ps-flex-align-c flex-align-c p-b-10"},[e("div",{staticClass:"progress-box"},[e("span",{staticClass:"progress-title"},[t._v("能量摄入")]),e("el-progress",{staticClass:"progress-content",attrs:{"show-text":!1,color:t.percentageColor(n.dataList?n.dataList.total_nutrition.energy_kcal:""),percentage:t.percentageTotal(n.dataList?n.dataList.total_nutrition.energy_kcal:0)}})],1),e("div",{staticClass:"progress-box"},[e("span",{staticClass:"progress-title"},[t._v("三大营养元素")]),e("el-progress",{staticClass:"progress-content",attrs:{"show-text":!1,color:t.percentageColor(n.dataList?n.dataList.total_nutrition.total_axunge_protein_carbohydrate:""),percentage:t.percentageTotal(n.dataList?n.dataList.total_nutrition.total_axunge_protein_carbohydrate:0)}})],1),e("div",{staticClass:"progress-box"},[e("span",{staticClass:"progress-title"},[t._v("食物多样性")]),e("el-progress",{staticClass:"progress-content",attrs:{"show-text":!1,color:t.percentageColor(n.dataList?n.dataList.total_nutrition.total_cereals_eggsandmeat_fruit_vegetable:""),percentage:t.percentageTotal(n.dataList?n.dataList.total_nutrition.total_cereals_eggsandmeat_fruit_vegetable:0)}})],1)]),e("el-table",{ref:"progressTableData",refInFor:!0,staticClass:"ps-table",staticStyle:{width:"100%"},attrs:{data:n.dataList?n.dataList.food_data:[],stripe:"","header-row-class-name":"ps-table-header-row"}},[e("el-table-column",{attrs:{prop:"food_name",label:"菜品名称",align:"center"}}),e("el-table-column",{key:"ingredient_group",attrs:{prop:"ingredient_group",label:"食材组成",align:"center",width:"200"},scopedSlots:t._u([{key:"default",fn:function(n){return[n.row.ingredient_group&&n.row.ingredient_group.length?e("div",{staticClass:"ps-flex-align-c flex-wrap"},t._l(n.row.ingredient_group,(function(r,a){return e("div",{key:a},[e("span",{staticClass:"p-t-10"},[t._v(" "+t._s(a===n.row.ingredient_group.length-1&&r.name+":"+r.number||r.name+":"+r.number+"、")+" ")])])})),0):t._e()]}}],null,!0)}),e("el-table-column",{attrs:{prop:"",label:"营养元素",align:"center",width:"200"},scopedSlots:t._u([{key:"default",fn:function(n){return[e("div",{staticClass:"ps-flex-align-c flex-wrap"},[t._v(" "+t._s(n.row.ingredient_category_count+"类")+"/ "),t._l(t.mainNutritionList,(function(r,a){return e("div",{key:a},[t._v(" "+t._s(a===t.mainNutritionList.length-1&&r.name+":"+n.row.main_nutrition[r.key]+r.unit||r.name+":"+n.row.main_nutrition[r.key]+r.unit+"/")+" ")])}))],2)]}}],null,!0)}),e("el-table-column",{attrs:{prop:"setting_stocks",label:"库存",align:"center"}}),e("el-table-column",{attrs:{prop:"food_buy_limit",label:"限制可点数量",align:"center"}}),e("el-table-column",{attrs:{label:"操作",align:"center",width:"100",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.clickMenuFoodDelete(n,r.row)}}},[t._v(" 删除 ")])]}}],null,!0)})],1)],1)})),0):t._e(),"nutritionAnalysis"===t.foodMenuType?e("menu-nutrition-analysis",{attrs:{menuType:t.menu_type,menuId:t.menu_id}}):t._e()],1),e("el-dialog",{attrs:{title:"复制到",visible:t.showMealCopyDialog,"close-on-press-escape":!1,"close-on-click-modal":!1,"destroy-on-close":!0,width:"600px"},on:{"update:visible":function(e){t.showMealCopyDialog=e}}},[e("div",[t._v(" 日期： "),e("el-date-picker",{attrs:{placeholder:"选择日期",size:"small","default-value":t.monthDate,"picker-options":t.pickerOptions,"popper-class":"el-picker-box"},model:{value:t.copyMealDate,callback:function(e){t.copyMealDate=e},expression:"copyMealDate"}})],1),e("div",{staticStyle:{"margin-top":"12px"}},[t._v(" 餐段： "),e("el-select",{attrs:{size:"small"},model:{value:t.copyMealType,callback:function(e){t.copyMealType=e},expression:"copyMealType"}},t._l(t.mealTypeList,(function(t){return e("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{attrs:{size:"small"},on:{click:function(e){t.showMealCopyDialog=!1}}},[t._v("取 消")]),e("el-button",{attrs:{size:"small",type:"primary"},on:{click:t.handleCopyMeal}},[t._v("确 定")])],1)])],1)},a=[],o=n("c9d9"),i=n("5a0c"),s=n("ed08"),l=n("f60f");function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function c(t,e){return f(t)||d(t,e)||_(t,e)||p()}function p(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,a,o,i,s=[],l=!0,u=!1;try{if(o=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=o.call(n)).done)&&(s.push(r.value),s.length!==e);l=!0);}catch(t){u=!0,a=t}finally{try{if(!l&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(u)throw a}}return s}}function f(t){if(Array.isArray(t))return t}function h(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */h=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,a=Object.defineProperty||function(t,e,n){t[e]=n.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function c(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,n){return t[e]=n}}function p(t,e,n,r){var o=e&&e.prototype instanceof _?e:_,i=Object.create(o.prototype),s=new O(r||[]);return a(i,"_invoke",{value:T(t,n,s)}),i}function d(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=p;var f="suspendedStart",y="suspendedYield",g="executing",m="completed",v={};function _(){}function b(){}function w(){}var L={};c(L,i,(function(){return this}));var k=Object.getPrototypeOf,x=k&&k(k($([])));x&&x!==n&&r.call(x,i)&&(L=x);var M=w.prototype=_.prototype=Object.create(L);function C(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function D(t,e){function n(a,o,i,s){var l=d(t[a],t,o);if("throw"!==l.type){var c=l.arg,p=c.value;return p&&"object"==u(p)&&r.call(p,"__await")?e.resolve(p.__await).then((function(t){n("next",t,i,s)}),(function(t){n("throw",t,i,s)})):e.resolve(p).then((function(t){c.value=t,i(c)}),(function(t){return n("throw",t,i,s)}))}s(l.arg)}var o;a(this,"_invoke",{value:function(t,r){function a(){return new e((function(e,a){n(t,r,e,a)}))}return o=o?o.then(a,a):a()}})}function T(e,n,r){var a=f;return function(o,i){if(a===g)throw Error("Generator is already running");if(a===m){if("throw"===o)throw i;return{value:t,done:!0}}for(r.method=o,r.arg=i;;){var s=r.delegate;if(s){var l=S(s,r);if(l){if(l===v)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===f)throw a=m,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=g;var u=d(e,n,r);if("normal"===u.type){if(a=r.done?m:y,u.arg===v)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(a=m,r.method="throw",r.arg=u.arg)}}}function S(e,n){var r=n.method,a=e.iterator[r];if(a===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,S(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var o=d(a,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,v;var i=o.arg;return i?i.done?(n[e.resultName]=i.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,v):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function N(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function O(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function $(e){if(e||""===e){var n=e[i];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,o=function n(){for(;++a<e.length;)if(r.call(e,a))return n.value=e[a],n.done=!1,n;return n.value=t,n.done=!0,n};return o.next=o}}throw new TypeError(u(e)+" is not iterable")}return b.prototype=w,a(M,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:b,configurable:!0}),b.displayName=c(w,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,c(t,l,"GeneratorFunction")),t.prototype=Object.create(M),t},e.awrap=function(t){return{__await:t}},C(D.prototype),c(D.prototype,s,(function(){return this})),e.AsyncIterator=D,e.async=function(t,n,r,a,o){void 0===o&&(o=Promise);var i=new D(p(t,n,r,a),o);return e.isGeneratorFunction(n)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},C(M),c(M,l,"Generator"),c(M,i,(function(){return this})),c(M,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=$,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(N),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function a(r,a){return s.type="throw",s.arg=e,n.next=r,a&&(n.method="next",n.arg=t),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],s=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var l=r.call(i,"catchLoc"),u=r.call(i,"finallyLoc");if(l&&u){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),N(n),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var a=r.arg;N(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:$(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),v}},e}function y(t,e,n,r,a,o,i){try{var s=t[o](i),l=s.value}catch(t){return void n(t)}s.done?e(l):Promise.resolve(l).then(r,a)}function g(t){return function(){var e=this,n=arguments;return new Promise((function(r,a){var o=t.apply(e,n);function i(t){y(o,r,a,i,s,"next",t)}function s(t){y(o,r,a,i,s,"throw",t)}i(void 0)}))}}function m(t){return w(t)||b(t)||_(t)||v()}function v(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _(t,e){if(t){if("string"==typeof t)return L(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?L(t,e):void 0}}function b(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function w(t){if(Array.isArray(t))return L(t)}function L(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var k={components:{menuNutritionAnalysis:l["default"]},data:function(){return{pickerOptions:this.disabledDate(),isLoading:!1,menu_type:"",menu_id:"",monthDate:"",foodMenuType:"foodSeting",mealList:[],showMealCopyDialog:!1,copyMealDate:"",copyMealType:"",mealTypeList:[{label:"全部",value:"all"}].concat(m(o["a"])),dataInfoDialog:{},isNutritionGuidance:!1,mainNutritionList:[{name:"能量",key:"energy_kcal",value:0,unit:"kcal"},{name:"脂肪",key:"axunge",value:0,unit:"g"},{name:"碳水化物",key:"carbohydrate",value:0,unit:"g"},{name:"蛋白质",key:"protein",value:0,unit:"g"}]}},created:function(){this.initLoad(),"guidance"===this.$route.query.isNutritionGuidance&&(this.isNutritionGuidance=!0),this.mealList=o["a"]},mounted:function(){},methods:{disabledDate:function(){var t=this;return{disabledDate:function(e){var n=new Date(t.monthDate).getMonth(),r=new Date(e).getMonth();return n!==r}}},initLoad:function(){this.menu_type=this.$route.query.menuType,this.menu_id=String(this.$route.query.menuId),this.monthDate=this.$route.query.monthDate,this.getMenuDateNutrition()},tabClick:function(t){this.foodMenuType=t},copyMeal:function(t){this.dataInfoDialog=t.dataList,this.showMealCopyDialog=!0},handleCopyMeal:function(t,e){var n=this;return g(h().mark((function t(){var e,r;return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(n.copyMealDate){t.next=2;break}return t.abrupt("return",n.$message.error("请选择日期"));case 2:if(n.copyMealType){t.next=4;break}return t.abrupt("return",n.$message.error("请选择餐段"));case 4:return n.showMealCopyDialog=!1,e={id:n.dataInfoDialog.id,target_data:[]},e.target_data.push({date:i(n.copyMealDate).format("YYYY-MM-DD"),meal_type:n.copyMealType}),t.next=9,n.$apis.apiBackgroundFoodMenuMonthlyCopyMealTypeFoodPost(e);case 9:r=t.sent,0===r.code?(n.$message.success("操作成功！"),n.showMealCopyDialog=!1,n.getMenuDateNutrition()):n.$message.error(r.msg);case 11:case"end":return t.stop()}}),t)})))()},getMenuDateNutrition:function(){var t=this;return g(h().mark((function e(){var n,r,a,i,l;return h().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n={id:t.menu_id,menu_type:t.menu_type,date:t.monthDate},t.isLoading=!0,e.next=4,Object(s["Z"])(t.$apis.apiBackgroundFoodMenuMenuDateNutritionPost(n));case 4:if(r=e.sent,a=c(r,2),i=a[0],l=a[1],t.isLoading=!1,!i){e.next=12;break}return t.$message.error(i.message),e.abrupt("return");case 12:0===l.code?t.mealList=o["a"].map((function(t){var e=l.data.result[t.value];return e?(t.dataList=e,t.dataList.food_data.forEach((function(t){t.setting_stocks=-1===e.setting_stocks[t.id]?"无数量限制":e.setting_stocks[t.id],t.food_buy_limit=e.food_buy_limit[t.id]}))):t.dataList=null,t})):t.$message.error(l.msg);case 13:case"end":return e.stop()}}),e)})))()},clickMenuFoodDelete:function(t,e){this.getMenuMenuFoodDelete(e.id,t.value)},getMenuMenuFoodDelete:function(t,e){var n=this;return g(h().mark((function r(){var a,o,i,l,u;return h().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return a={id:n.menu_id,menu_type:n.menu_type,date:n.monthDate,meal_type:e,food_id:t},n.isLoading=!0,r.next=4,Object(s["Z"])(n.$apis.apiBackgroundFoodMenuMenuFoodDeletePost(a));case 4:if(o=r.sent,i=c(o,2),l=i[0],u=i[1],n.isLoading=!1,!l){r.next=12;break}return n.$message.error(l.message),r.abrupt("return");case 12:0===u.code?n.getMenuDateNutrition():n.$message.error(u.msg);case 13:case"end":return r.stop()}}),r)})))()},percentageTotal:function(t){var e=0;return t&&(e=parseInt(t)>=100?100:parseInt(t/120*100)),e},percentageColor:function(t){var e="";return parseInt(t)>=120?e="#ea5b55":parseInt(t)>=80?e="#5dbf6e":parseInt(t)<80&&(e="#e89e42"),e},clickMenuCatering:function(t){"afternoon"!==t.value&&"supper"!==t.value&&"morning"!==t.value||(this.isNutritionGuidance=!1),this.$router.push({name:"MerchantMenuCatering",query:{isNutritionGuidance:this.isNutritionGuidance?"true":"false",menuId:this.$route.query.menuId,menuType:this.$route.query.menuType,currentEditMealType:t.value,currentEditDate:this.$route.query.monthDate,data:t.dataList?this.$encodeQuery(t.dataList):this.$encodeQuery([])}})}}},x=k,M=(n("e01c"),n("2877")),C=Object(M["a"])(x,r,a,!1,null,null,null);e["default"]=C.exports},e01c:function(t,e,n){"use strict";n("1b11")}}]);