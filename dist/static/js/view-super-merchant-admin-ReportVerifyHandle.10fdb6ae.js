(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-merchant-admin-ReportVerifyHandle"],{a20a:function(t,e,r){},db5b:function(t,e,r){"use strict";r("a20a")},f759:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"ReportVerifyHandle container-wrapper"},[e("refresh-tool",{on:{refreshPage:t.refreshHandle}}),e("search-form",{ref:"searchRef",attrs:{loading:t.isLoading,"form-setting":t.searchFormSetting},on:{search:t.searchHandle}}),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("数据列表")]),e("div",{staticClass:"align-r"},[e("button-icon",{attrs:{color:"origin"},on:{click:function(e){return t.openDialog("all")}}},[t._v("全部生成")])],1)]),e("div",{staticClass:"table-content"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},t._l(t.tableSetting,(function(r){return e("table-column",{key:r.key,attrs:{col:r},scopedSlots:t._u([{key:"operation",fn:function(r){var n=r.row;return[e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.openDialog("part",n)}}},[t._v("重新生成")])]}}],null,!0)})})),1)],1)]),e("el-dialog",{attrs:{title:"重新生成",visible:t.dialogVisible,width:"600px",customClass:"ps-dialog"},on:{"update:visible":function(e){t.dialogVisible=e}}},[e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}]},[e("el-form",{ref:"dialogFormRef",staticClass:"jiaofei-form",attrs:{model:t.dialogForm,rules:t.dialogFormRules,"label-width":"100px"}},[e("el-form-item",{attrs:{label:"数据时间：",prop:"selectTime"}},[e("el-date-picker",{staticClass:"ps-poper-picker",attrs:{type:"daterange",align:"left","unlink-panels":"","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:""},model:{value:t.dialogForm.selectTime,callback:function(e){t.$set(t.dialogForm,"selectTime",e)},expression:"dialogForm.selectTime"}})],1),e("el-form-item",{attrs:{label:"选择报表：",prop:"reportType"}},[e("el-radio-group",{staticClass:"ps-radio report-radio",model:{value:t.dialogForm.reportType,callback:function(e){t.$set(t.dialogForm,"reportType",e)},expression:"dialogForm.reportType"}},t._l(t.reportTypeList,(function(r,n){return e("el-radio",{key:n,attrs:{label:n}},[t._v(t._s(r.name))])})),1)],1)],1),"all"===t.dialogType?e("div",{staticClass:"tips"},[t._v("注：全部生成时不显示脚本的运行状态")]):t._e()],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:t.isLoading},on:{click:function(e){t.dialogVisible=!1}}},[t._v("取 消")]),e("el-button",{staticClass:"ps-btn",attrs:{type:"primary",disabled:t.isLoading},on:{click:t.confirmDialog}},[t._v("立即执行")])],1)])],1)},a=[],o=r("ed08");function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},l=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function p(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{p({},"")}catch(t){p=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var o=e&&e.prototype instanceof b?e:b,i=Object.create(o.prototype),s=new O(n||[]);return a(i,"_invoke",{value:R(t,r,s)}),i}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var h="suspendedStart",m="suspendedYield",y="executing",g="completed",v={};function b(){}function w(){}function x(){}var L={};p(L,l,(function(){return this}));var _=Object.getPrototypeOf,k=_&&_(_(j([])));k&&k!==r&&n.call(k,l)&&(L=k);var T=x.prototype=b.prototype=Object.create(L);function F(t){["next","throw","return"].forEach((function(e){p(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function r(a,o,s,l){var c=d(t[a],t,o);if("throw"!==c.type){var u=c.arg,p=u.value;return p&&"object"==i(p)&&n.call(p,"__await")?e.resolve(p.__await).then((function(t){r("next",t,s,l)}),(function(t){r("throw",t,s,l)})):e.resolve(p).then((function(t){u.value=t,s(u)}),(function(t){return r("throw",t,s,l)}))}l(c.arg)}var o;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return o=o?o.then(a,a):a()}})}function R(e,r,n){var a=h;return function(o,i){if(a===y)throw Error("Generator is already running");if(a===g){if("throw"===o)throw i;return{value:t,done:!0}}for(n.method=o,n.arg=i;;){var s=n.delegate;if(s){var l=P(s,n);if(l){if(l===v)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===h)throw a=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=y;var c=d(e,r,n);if("normal"===c.type){if(a=n.done?g:m,c.arg===v)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(a=g,n.method="throw",n.arg=c.arg)}}}function P(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,P(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var o=d(a,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,v;var i=o.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function C(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function O(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function j(e){if(e||""===e){var r=e[l];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,o=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(i(e)+" is not iterable")}return w.prototype=x,a(T,"constructor",{value:x,configurable:!0}),a(x,"constructor",{value:w,configurable:!0}),w.displayName=p(x,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,x):(t.__proto__=x,p(t,u,"GeneratorFunction")),t.prototype=Object.create(T),t},e.awrap=function(t){return{__await:t}},F(S.prototype),p(S.prototype,c,(function(){return this})),e.AsyncIterator=S,e.async=function(t,r,n,a,o){void 0===o&&(o=Promise);var i=new S(f(t,r,n,a),o);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},F(T),p(T,u,"Generator"),p(T,l,(function(){return this})),p(T,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=j,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(C),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return s.type="throw",s.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],s=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),C(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;C(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:j(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function l(t,e,r,n,a,o,i){try{var s=t[o](i),l=s.value}catch(t){return void r(t)}s.done?e(l):Promise.resolve(l).then(n,a)}function c(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var o=t.apply(e,r);function i(t){l(o,n,a,i,s,"next",t)}function s(t){l(o,n,a,i,s,"throw",t)}i(void 0)}))}}var u={name:"ReportVerifyHandle",data:function(){return{isLoading:!1,tableData:[],tableSetting:[{label:"序号",key:"index",type:"index",width:"80"},{key:"company_name",label:"项目名称"},{key:"yingyee",label:"营业额日报表"},{key:"gerenxiaofei",label:"个人消费汇总"},{key:"bumenxiaofei",label:"部门消费汇总表"},{key:"qianbaoribaobiao",label:"个人钱包日报表、账户钱包日报表"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"100"}],searchFormSetting:{name:{type:"input",value:"",label:"项目名称",placeholder:"请输入项目名称"}},dialogForm:{selectTime:[],reportType:""},reportTypeList:{yingyee:{name:"营业额日报表",api:"apiBackgroundAdminScriptReportFixOrderBusinessReportPost"},gerenxiaofei:{name:"个人消费汇总",api:"apiBackgroundAdminScriptReportFixPersonDepartmentPaymentReportPost"},bumenxiaofei:{name:"部门消费汇总表",api:"apiBackgroundAdminScriptReportFixPersonDepartmentPaymentReportPost"},qianbaoribaobiao:{name:"个人钱包日报表、账户钱包日报表",api:"apiBackgroundAdminScriptReportFixCardWalletPersonDailyReportPost"}},dialogFormRules:{selectTime:[{required:!0,message:"请选择数据时间",trigger:"blur"}],reportType:[{required:!0,message:"请选择报表",trigger:"blur"}]},dialogType:"",dialogVisible:!1,selectInfo:{},timer:null,reportStatus:0}},mounted:function(){this.initLoad()},methods:{initLoad:function(){var t=this;return c(s().mark((function e(){return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.getAllCompany();case 1:case"end":return e.stop()}}),e)})))()},refreshHandle:function(){this.tableData=[],this.initLoad()},searchHandle:Object(o["d"])((function(){this.currentPage=1,this.getAllCompany()}),300),formatQueryParams:function(t){var e={};for(var r in t)""!==t[r].value&&null!==t[r].value&&0!==t[r].value.length&&("select_time"!==r?e[r]=t[r].value:t[r].value.length>0&&(e.start_time=t[r].value[0],e.end_time=t[r].value[1]));return e},getAllCompany:function(){var t=this;return c(s().mark((function e(){var r;return s().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.$apis.apiBackgroundAdminScriptReportGetAllCompanyPost(t.formatQueryParams(t.searchFormSetting));case 3:r=e.sent,t.isLoading=!1,0===r.code?(t.tableData=[],r.data.map((function(e){t.tableData.push({company_id:e[0],company_name:e[1],yingyee:"",gerenxiaofei:"",bumenxiaofei:"",qianbaoribaobiao:""})}))):t.$message.error(r.msg);case 6:case"end":return e.stop()}}),e)})))()},openDialog:function(t,e){this.reportStatus&&this.$message.error("报表任务执行中"),this.dialogType=t,this.selectInfo={},this.dialogForm.selectTime=[],this.dialogForm.reportType="",e&&(this.selectInfo=e),this.dialogVisible=!0},confirmDialog:function(){var t=this;this.$refs.dialogFormRef.validate((function(e){if(e){var r={start_date:t.dialogForm.selectTime[0],end_date:t.dialogForm.selectTime[1]};"part"===t.dialogType&&(r.company_id=t.selectInfo.company_id),t.scriptReport(r)}}))},scriptReport:function(t){var e=this;return c(s().mark((function r(){var n;return s().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!e.isLoading){r.next=2;break}return r.abrupt("return");case 2:return e.isLoading=!0,r.next=5,e.$apis[e.reportTypeList[e.dialogForm.reportType].api](t);case 5:n=r.sent,e.isLoading=!1,e.dialogVisible=!1,0===n.code?(e.reportStatus=1,"part"===e.dialogType&&e.tableData.map((function(t){return t.company_id===e.selectInfo.company_id&&(t[e.dialogForm.reportType]="正在生成"),t})),e.getRepotResult(n.data.query_id),e.timer=setInterval((function(){e.getRepotResult(n.data.query_id)}),1e4)):(e.$message.error(n.msg),e.isLoading=!1);case 9:case"end":return r.stop()}}),r)})))()},getRepotResult:function(t){var e=this;return c(s().mark((function r(){var n,a;return s().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,e.$sleep(2e3);case 2:return r.next=4,e.$apis.apiBackgroundAdminScriptReportScriptResultQueryPost({query_id:t});case 4:n=r.sent,0===n.code?("success"===n.data.status?(e.$message.success(n.msg),a="成功",clearInterval(e.timer),e.reportStatus=0):"failure"===n.data.status&&(e.$message.error(n.msg),a="失败",clearInterval(e.timer),e.reportStatus=0),Object.keys(e.selectInfo).length&&"processing"!==n.data.status&&e.tableData.map((function(t){return t.company_id===e.selectInfo.company_id&&(t[e.dialogForm.reportType]=a),t}))):(e.$message.error(n.msg),clearInterval(e.timer),e.reportStatus=0);case 6:case"end":return r.stop()}}),r)})))()}}},p=u,f=(r("db5b"),r("2877")),d=Object(f["a"])(p,n,a,!1,null,null,null);e["default"]=d.exports}}]);