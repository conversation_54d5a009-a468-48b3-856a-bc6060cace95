(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-merchant-meal-management-meal-report-MealReportAdmin"],{"13c6":function(t,e,r){"use strict";r("eb21")},"3b33":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"meal-declaration container-wrapper"},[e("refresh-tool",{on:{refreshPage:t.refreshHandle}}),e("search-form",{ref:"searchRef",attrs:{"form-setting":t.searchFormSetting,"label-width":"105px",autoSearch:!1},on:{search:t.searchHandle}}),e("div",{staticClass:"table-wrapper"},[e("div",{staticClass:"table-header"},[e("div",{staticClass:"table-title"},[t._v("数据列表")]),e("div",{staticClass:"align-r"},[e("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_report_meal.report_meal_settings.add"],expression:"['background_report_meal.report_meal_settings.add']"}],attrs:{color:"origin",type:"add"},on:{click:function(e){return t.addOrEditMeal("add")}}},[t._v("新增报餐")]),e("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background_report_meal.report_meal_settings.delete"],expression:"['background_report_meal.report_meal_settings.delete']"}],attrs:{color:"plain",type:"del"},on:{click:function(e){return t.mulOperation("mulDel")}}},[t._v("批量删除")])],1)]),e("div",{staticClass:"table-content"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:"","header-row-class-name":"ps-table-header-row","cell-style":{textAlign:"center"},"header-cell-style":{textAlign:"center"}},on:{"selection-change":t.handleSelectionChange}},[e("el-table-column",{attrs:{type:"selection",width:"50",align:"center","class-name":"ps-checkbox"}}),e("el-table-column",{attrs:{prop:"name",label:"规则名称",width:"160"}}),e("el-table-column",{attrs:{prop:"report_card_groups_alias",label:"适用分组"},scopedSlots:t._u([{key:"default",fn:function(r){return t._l(r.row.report_card_groups_alias,(function(r){return e("el-tag",{key:r,staticStyle:{margin:"0 8px 8px 0"}},[t._v(" "+t._s(r)+" ")])}))}}])}),e("el-table-column",{attrs:{prop:"report_organization_alias",label:"适用消费点"},scopedSlots:t._u([{key:"default",fn:function(r){return t._l(r.row.report_organization_alias,(function(r){return e("el-tag",{key:r,staticStyle:{margin:"0 8px 8px 0"}},[t._v(" "+t._s(r)+" ")])}))}}])}),e("el-table-column",{attrs:{prop:"meal_type_detail.meal_type_verbose",label:"适用餐段"},scopedSlots:t._u([{key:"default",fn:function(r){return t._l(r.row.meal_type_detail.meal_type_verbose,(function(r){return e("el-tag",{key:r,staticStyle:{margin:"0 8px 8px 0"}},[t._v(" "+t._s(r)+" ")])}))}}])}),e("el-table-column",{attrs:{prop:"create_time",label:"创建时间",width:"160"}}),e("el-table-column",{attrs:{prop:"is_open",label:"状态"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2",disabled:!t.allPermissions.includes("background_report_meal.report_meal_settings.modify_open_status")},on:{change:function(e){return t.isOpenSetting(r.row)}},model:{value:r.row.is_open,callback:function(e){t.$set(r.row,"is_open",e)},expression:"scope.row.is_open"}})]}}])}),e("el-table-column",{attrs:{label:"操作",width:"180"},scopedSlots:t._u([{key:"default",fn:function(r){return[e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_report_meal.report_meal_settings.modify"],expression:"['background_report_meal.report_meal_settings.modify']"}],staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.addOrEditMeal("edit",r.row)}}},[t._v("编辑")]),e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background_report_meal.report_meal_settings.delete"],expression:"['background_report_meal.report_meal_settings.delete']"}],attrs:{type:"text",size:"small"},on:{click:function(e){return t.mulOperation("del",r.row)}}},[t._v("删除")])]}}])})],1)],1),e("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[e("el-pagination",{staticClass:"ps-text",attrs:{"current-page":t.currentPage,"page-sizes":[10,20,50,100,500],"page-size":t.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:t.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)]),e("add-meal-report",{attrs:{show:t.showDrawer,infoData:t.drawerInfoData,type:t.drawerType,size:560},on:{"update:show":function(e){t.showDrawer=e},confirm:t.confirmHandle}})],1)},a=[],o=r("ed08"),i=r("2f62"),s=r("b176");function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function l(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */l=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function p(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{p({},"")}catch(t){p=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var o=e&&e.prototype instanceof b?e:b,i=Object.create(o.prototype),s=new M(n||[]);return a(i,"_invoke",{value:j(t,r,s)}),i}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var d="suspendedStart",g="suspendedYield",m="executing",v="completed",y={};function b(){}function w(){}function _(){}var x={};p(x,i,(function(){return this}));var L=Object.getPrototypeOf,k=L&&L(L(z([])));k&&k!==r&&n.call(k,i)&&(x=k);var S=_.prototype=b.prototype=Object.create(x);function O(t){["next","throw","return"].forEach((function(e){p(t,e,(function(t){return this._invoke(e,t)}))}))}function P(t,e){function r(a,o,i,s){var l=h(t[a],t,o);if("throw"!==l.type){var u=l.arg,p=u.value;return p&&"object"==c(p)&&n.call(p,"__await")?e.resolve(p.__await).then((function(t){r("next",t,i,s)}),(function(t){r("throw",t,i,s)})):e.resolve(p).then((function(t){u.value=t,i(u)}),(function(t){return r("throw",t,i,s)}))}s(l.arg)}var o;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return o=o?o.then(a,a):a()}})}function j(e,r,n){var a=d;return function(o,i){if(a===m)throw Error("Generator is already running");if(a===v){if("throw"===o)throw i;return{value:t,done:!0}}for(n.method=o,n.arg=i;;){var s=n.delegate;if(s){var c=E(s,n);if(c){if(c===y)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===d)throw a=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=m;var l=h(e,r,n);if("normal"===l.type){if(a=n.done?v:g,l.arg===y)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(a=v,n.method="throw",n.arg=l.arg)}}}function E(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,E(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var o=h(a,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,y;var i=o.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function C(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function D(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function M(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(C,this),this.reset(!0)}function z(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,o=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(c(e)+" is not iterable")}return w.prototype=_,a(S,"constructor",{value:_,configurable:!0}),a(_,"constructor",{value:w,configurable:!0}),w.displayName=p(_,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,p(t,u,"GeneratorFunction")),t.prototype=Object.create(S),t},e.awrap=function(t){return{__await:t}},O(P.prototype),p(P.prototype,s,(function(){return this})),e.AsyncIterator=P,e.async=function(t,r,n,a,o){void 0===o&&(o=Promise);var i=new P(f(t,r,n,a),o);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},O(S),p(S,u,"Generator"),p(S,i,(function(){return this})),p(S,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=z,M.prototype={constructor:M,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(D),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return s.type="throw",s.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],s=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var c=n.call(i,"catchLoc"),l=n.call(i,"finallyLoc");if(c&&l){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,y):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),D(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;D(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:z(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),y}},e}function u(t,e,r,n,a,o,i){try{var s=t[o](i),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,a)}function p(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var o=t.apply(e,r);function i(t){u(o,n,a,i,s,"next",t)}function s(t){u(o,n,a,i,s,"throw",t)}i(void 0)}))}}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function h(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach((function(e){d(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function d(t,e,r){return(e=g(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function g(t){var e=m(t,"string");return"symbol"==c(e)?e:e+""}function m(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var v={components:{AddMealReport:s["default"]},name:"MealDeclaration",data:function(){return{isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[],searchFormSetting:{report_organizations:{type:"organizationSelect",label:"组织",value:[],placeholder:"请选择",multiple:!0,collapseTags:!0,checkStrictly:!0,dataList:[]},report_card_groups:{type:"groupSelect",label:"适用分组",value:[],placeholder:"请选择",multiple:!0}},selectListId:[],showDrawer:!1,drawerInfoData:{},drawerType:""}},created:function(){this.initLoad()},computed:h({},Object(i["c"])(["allPermissions"])),mounted:function(){},methods:{initLoad:function(){this.getMealReportList()},searchHandle:Object(o["d"])((function(t){t&&"search"===t&&(this.currentPage=1,this.getMealReportList())}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.currentPage=1,this.initLoad()},formatQueryParams:function(t){var e={};for(var r in t)""!==t[r].value&&null!==t[r].value&&0!==t[r].value.length&&("select_date"!==r?e[r]=t[r].value:t[r].value.length>0&&(e.start_date=t[r].value[0],e.end_date=t[r].value[1]));return e},getMealReportList:function(){var t=this;return p(l().mark((function e(){var r;return l().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,t.$apis.apiBackgroundReportMealReportMealSettingsListPost(h(h({},t.formatQueryParams(t.searchFormSetting)),{},{page:t.currentPage,page_size:t.pageSize}));case 3:r=e.sent,t.isLoading=!1,0===r.code?(t.tableData=r.data.results,t.totalCount=r.data.count):t.$message.error(r.msg);case 6:case"end":return e.stop()}}),e)})))()},handleSizeChange:function(t){this.pageSize=t,this.getMealReportList()},handleCurrentChange:function(t){this.currentPage=t,this.getMealReportList()},handleSelectionChange:function(t){var e=this;this.selectListId=[];var r=Object.freeze(t);r.map((function(t){e.selectListId.push(t.id)}))},switchFn:function(t){},mulOperation:function(t,e){var r=this;if(!e&&!this.selectListId.length)return this.$message.error("请先选择数据！");var n="提示",a="";switch(t){case"del":a="确定删除该报餐设置？";break;case"mulDel":a="确定删除所选报餐设置？";break}this.$confirm("".concat(a),"".concat(n),{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var n=p(l().mark((function n(a,o,i){return l().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if("confirm"!==a){n.next=13;break}o.confirmButtonLoading=!0,n.t0=t,n.next="del"===n.t0?5:"mulDel"===n.t0?7:9;break;case 5:return r.deleteSetting([e.id]),n.abrupt("break",9);case 7:return r.deleteSetting(r.selectListId),n.abrupt("break",9);case 9:i(),o.confirmButtonLoading=!1,n.next=14;break;case 13:o.confirmButtonLoading||i();case 14:case"end":return n.stop()}}),n)})));function a(t,e,r){return n.apply(this,arguments)}return a}()}).then((function(t){})).catch((function(t){}))},deleteSetting:function(t){var e=this;return p(l().mark((function r(){var n;return l().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,e.$apis.apiBackgroundReportMealReportMealSettingsDeletePost({ids:t});case 2:n=r.sent,0===n.code?(e.$message.success(n.msg),e.getMealReportList()):e.$message.error(n.msg);case 4:case"end":return r.stop()}}),r)})))()},isOpenSetting:function(t){var e=this;return p(l().mark((function r(){var n,a;return l().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return n={id:t.id,is_open:t.is_open},r.next=3,e.$apis.apiBackgroundReportMealReportMealSettingsModifyOpenStatusPost(n);case 3:a=r.sent,0===a.code?(e.$message.success(a.msg),e.getMealReportList()):e.$message.error(a.msg);case 5:case"end":return r.stop()}}),r)})))()},addOrEditMeal:function(t,e){this.showDrawer=!0,this.drawerInfoData=e,this.drawerType=t},confirmHandle:function(){this.showDrawer=!1,this.currentPage=1,this.getMealReportList()}}},y=v,b=(r("13c6"),r("2877")),w=Object(b["a"])(y,n,a,!1,null,"321e40bc",null);e["default"]=w.exports},eb21:function(t,e,r){}}]);