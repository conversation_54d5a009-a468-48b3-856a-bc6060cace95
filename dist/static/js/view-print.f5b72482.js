(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-print","view-print-ElTable3"],{5769:function(t,e,r){"use strict";r("8d3d")},"62e17":function(t,e,r){"use strict";r.r(e);r("38a0"),r("450d");var n,a,i=r("ad41"),o=r.n(i),s={extends:o.a,mounted:function(){this.$nextTick((function(){var t=this.$el.querySelector(".el-table__header-wrapper thead"),e=t.cloneNode(!0);this.$el.querySelector(".el-table__body-wrapper table").appendChild(e)}))}},u=s,c=(r("bcca"),r("2877")),l=Object(c["a"])(u,n,a,!1,null,"147963be",null);e["default"]=l.exports},"8d3d":function(t,e,r){},b014:function(t,e,r){"use strict";function n(t){if(null==t)throw new TypeError("Cannot destructure "+t)}r.r(e);var a=function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],attrs:{id:"print"}},[t.showPrintHeaderAndFooter?e("div",{staticClass:"page-header-footer"},[e("table",{staticClass:"wrapper-table"},[e("thead",[e("tr",[e("td",[e("div",{class:{"page-header-space":!0}})])])]),e("tbody",[e("tr",[e("td",[e("div",{staticClass:"print-table-wrapper"},["RechargeDailySummaryList"===t.type||"RechargeSettlementSummaryList"===t.type&&t.tableDataList.username?e("span",[t._v(" 收银员："+t._s(t.tableDataList.username)+" ")]):t._e(),t.printDateState?[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"myTableRef",staticClass:"print-table",staticStyle:{width:"100%",border:"1px solid black","border-color":"black"},attrs:{data:t.tableData,"show-summary":t.showSummary,"summary-method":t.getSummaries,"span-method":t.arraySpanMethod,"row-style":{height:"0"},"cell-style":t.tableCellStyle,"header-cell-style":t.tableHeaderCellStyle}},[e("el-table-column",{key:t.tableColumnKey,attrs:{prop:"date",label:"日期"},scopedSlots:t._u([{key:"header",fn:function(r){return[e("div",{staticClass:"clearfix",staticStyle:{width:"100%","text-align":"center"}},[e("span",[t._v("起止时间："+t._s(t.params.start_date+"至"+t.params.end_date))])])]}}],null,!1,4199160955)},t._l(t.currentTableSetting,(function(r){return e("table-column",{key:r.key,attrs:{col:r},scopedSlots:t._u([{key:"dateStartFee",fn:function(r){var n=r.row;return[e("span",{class:{"danger bold":n.error_date_start_fee}},[t._v(" "+t._s(t._f("formatMoney")(n.date_start_fee))+" ")])]}},{key:"dateEndFee",fn:function(r){var n=r.row;return[e("span",{class:{"danger bold":n.error_date_end_fee}},[t._v(" "+t._s(t._f("formatMoney")(n.date_end_fee))+" ")])]}},{key:"checkStatus",fn:function(e){var r=e.row;return[t._v(" "+t._s(r.check_status?"已晨检":"未晨检")+" ")]}},{key:"checkResult",fn:function(r){var n=r.row;return[e("div",{class:"不合格"===n.check_result_alias?"ps-red":""},[t._v(" "+t._s(n.check_result_alias)+" ")])]}},{key:"temperature",fn:function(e){var r=e.row;return[t._v(" "+t._s(r.temperature?r.temperature+"°C":"")+" ")]}},{key:"foodWeight",fn:function(e){var r=e.row;return[t._v(" "+t._s(r.food_weight?r.food_weight+"g":"")+" ")]}},{key:"images",fn:function(r){return n(r),[e("div",[t._v("查看")])]}},"SupervisionCanteenSampleRecord"===t.type?{key:"entryCupboard",fn:function(e){var r=e.row;return[t._v(" "+t._s(r.entry_cupboard?"是":"否")+" ")]}}:null,"SupervisionCanteenSampleRecord"===t.type?{key:"sampleEntryUser",fn:function(e){var r=e.row;return[t._v(" "+t._s(t.getNameByList(r.sample_entry_user))+" ")]}}:null,"SupervisionCanteenSampleRecord"===t.type?{key:"sampleExitUser",fn:function(r){var n=r.row;return[e("div",{staticClass:"person-tag"},[t._v(" "+t._s(t.getNameByList(n.sample_exit_user))+" ")])]}}:null,"SupervisionCanteenSampleRecord"===t.type?{key:"reservedUserName",fn:function(r){var n=r.row;return[e("div",{staticClass:"person-tag"},[t._v(" "+t._s(n.reserved_user_name?n.reserved_user_name:"--")+" ")])]}}:null,"SupervisionCanteenSampleRecord"===t.type?{key:"notReservedReason",fn:function(r){var n=r.row;return[e("div",{staticClass:"person-tag"},[t._v(" "+t._s(n.not_reserved_reason?n.not_reserved_reason:"--")+" ")])]}}:null,"SupervisionCanteenSampleRecord"===t.type?{key:"notEntryReason",fn:function(r){var n=r.row;return[n.entry_cupboard?t._e():e("div",{staticClass:"person-tag"},[t._v(" "+t._s(n.not_entry_reason?n.not_entry_reason:"--")+" ")])]}}:null],null,!0)})})),1)],1)]:e("el-table",{ref:"myTableRef",staticClass:"print-table",staticStyle:{width:"100%"},attrs:{data:t.tableData,"show-summary":t.showSummary,"header-row-class-name":"ps-table-header-row",border:""}},t._l(t.currentTableSetting,(function(r){return e("table-column",{key:r.key,attrs:{col:r},scopedSlots:t._u([{key:"dateStartFee",fn:function(r){var n=r.row;return[e("span",{class:{"danger bold":n.error_date_start_fee}},[t._v(" "+t._s(t._f("formatMoney")(n.date_start_fee))+" ")])]}},{key:"dateEndFee",fn:function(r){var n=r.row;return[e("span",{class:{"danger bold":n.error_date_end_fee}},[t._v(" "+t._s(t._f("formatMoney")(n.date_end_fee))+" ")])]}},{key:"checkStatus",fn:function(e){var r=e.row;return[t._v(" "+t._s(r.check_status?"已晨检":"未晨检")+" ")]}},{key:"checkResult",fn:function(r){var n=r.row;return[e("div",{class:"不合格"===n.check_result_alias?"ps-red":""},[t._v(" "+t._s(n.check_result_alias)+" ")])]}},{key:"temperature",fn:function(e){var r=e.row;return[t._v(" "+t._s(r.temperature?r.temperature+"°C":"")+" ")]}},{key:"foodWeight",fn:function(e){var r=e.row;return[t._v(" "+t._s(r.food_weight?r.food_weight+"g":"")+" ")]}},{key:"images",fn:function(r){return n(r),[e("div",[t._v("查看")])]}},"SupervisionCanteenSampleRecord"===t.type?{key:"entryCupboard",fn:function(e){var r=e.row;return[t._v(" "+t._s(r.entry_cupboard?"是":"否")+" ")]}}:null,"SupervisionCanteenSampleRecord"===t.type?{key:"sampleEntryUser",fn:function(e){var r=e.row;return[t._v(" "+t._s(t.getNameByList(r.sample_entry_user))+" ")]}}:null,"SupervisionCanteenSampleRecord"===t.type?{key:"sampleExitUser",fn:function(r){var n=r.row;return[e("div",{staticClass:"person-tag"},[t._v(" "+t._s(t.getNameByList(n.sample_exit_user))+" ")])]}}:null,"SupervisionCanteenSampleRecord"===t.type?{key:"reservedUserName",fn:function(r){var n=r.row;return[e("div",{staticClass:"person-tag"},[t._v(" "+t._s(n.reserved_user_name?n.reserved_user_name:"--")+" ")])]}}:null,"SupervisionCanteenSampleRecord"===t.type?{key:"notReservedReason",fn:function(r){var n=r.row;return[e("div",{staticClass:"person-tag"},[t._v(" "+t._s(n.not_reserved_reason?n.not_reserved_reason:"--")+" ")])]}}:null,"SupervisionCanteenSampleRecord"===t.type?{key:"notEntryReason",fn:function(r){var n=r.row;return[n.entry_cupboard?t._e():e("div",{staticClass:"person-tag"},[t._v(" "+t._s(n.not_entry_reason?n.not_entry_reason:"--")+" ")])]}}:null],null,!0)})})),1)],2),e("table-statistics",{attrs:{statistics:t.collect}}),"SoldierTurnoverList"===t.type?e("div",{staticClass:"m-l-20"},[e("div",[t._v(" 打印分组："+t._s(t.tableDataList.group_name_list.join("、"))+" ")]),t._m(0)]):t._e(),"SurgeryReportList"===t.type?e("div",{staticClass:"m-l-20"},[e("div",[t._v("打印分组："+t._s(t.tableDataList.card_groups))]),e("div",[t._v("打印消费点："+t._s(t.tableDataList.orgs))])]):t._e()],1)])]),t._m(1)]),e("div",{staticClass:"page-header"},[t._m(2),e("h3",{staticStyle:{"text-align":"center",margin:"10px 0","font-weight":"600"}},[t._v(" "+t._s("".concat(t.organizationName,"-").concat(t.title))+" ")])]),"CardInfoSubsidyInfoList"!==t.type&&"ConsumeReconciliation"!==t.type?e("div",{staticClass:"page-footer"},[e("div",{staticClass:"autograph-wrapper"},[e("span",{staticStyle:{padding:"0 30px 0 0px"}},[t._v("使用单位："+t._s(t.organizationName))]),e("span",{staticStyle:{padding:"0 30px 0 0px"}},[t._v("打印时间："+t._s(t._f("formatDate")(new Date)))]),t._l(t.signatureList,(function(r,n){return e("span",{key:n,staticStyle:{padding:"0 60px 0 0px"}},[t._v(t._s(r.value))])}))],2)]):t._e()]):e("div",{staticClass:"print-table-wrapper"},[e("el-table",{ref:"myTableRef",staticClass:"print-table",staticStyle:{width:"100%"},attrs:{data:t.tableData,"show-summary":t.showSummary,"header-row-class-name":"ps-table-header-row",border:""}},t._l(t.currentTableSetting,(function(r){return e("table-column",{key:r.key,attrs:{col:r},scopedSlots:t._u([{key:"dateStartFee",fn:function(r){var n=r.row;return[e("span",{class:{"danger bold":n.error_date_start_fee}},[t._v(" "+t._s(t._f("formatMoney")(n.date_start_fee))+" ")])]}},{key:"dateEndFee",fn:function(r){var n=r.row;return[e("span",{class:{"danger bold":n.error_date_end_fee}},[t._v(" "+t._s(t._f("formatMoney")(n.date_end_fee))+" ")])]}}],null,!0)})})),1)],1),e("div",{staticClass:"print-tools noprint"},[e("el-button",{directives:[{name:"print",rawName:"v-print",value:t.printObj,expression:"printObj"}],staticClass:"print-btn",attrs:{size:"medium",circle:"",icon:"el-icon-printer"},on:{click:t.toPrint}})],1)])},i=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"m-t-30"},[e("span",{staticClass:"m-b-10"},[t._v("备注：")]),e("div",[e("div",[t._v(" 1.收入包括以下几种卡类型：（1）普通补贴卡A、高职补贴卡A；（2）高职补贴卡B、高职充值卡；（3）特殊卡、海军规培学员卡；（4）机关补贴卡；（5）普通补贴卡B、普通充值卡等 ")]),e("div",[t._v(" 2.军人卡机包含：（1）普通补贴卡A、高职补贴卡A；（2）高职补贴卡B、高职充值卡；（3）特殊卡、海军规培学员卡；（4）机关补贴卡（无折扣）； ")]),e("div",[t._v(" 3.普通卡机包含：（1）普通补贴卡A、高职补贴卡A；（2）高职补贴卡B、高职充值卡；（3）特殊卡、海军规培学员卡；（4）机关补贴卡；（5）普通补贴卡B、普通充值卡等 ")]),e("div",[t._v(" 4.普通补贴卡A、高职补贴卡A每月享受工作日天数补贴；特殊卡、海军规培学员卡每月享受足月天数补贴。 ")])])])},function(){var t=this,e=t._self._c;return e("tfoot",[e("tr",[e("td",[e("div",{staticClass:"page-footer-space"})])])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"logo-wrapper"},[e("img",{attrs:{src:r("58b6"),alt:"",srcset:""}})])}],o=r("ed08"),s=r("5a0c"),u=r("cc06"),c=r("62e17"),l=r("7b1d"),f=r.n(l),p=r("4362"),d=r("2f62");function h(t){return h="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},h(t)}function y(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */y=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,r){return t[e]=r}}function l(t,e,r,n){var i=e&&e.prototype instanceof g?e:g,o=Object.create(i.prototype),s=new $(n||[]);return a(o,"_invoke",{value:E(t,r,s)}),o}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var p="suspendedStart",d="suspendedYield",m="executing",v="completed",_={};function g(){}function b(){}function w(){}var S={};c(S,o,(function(){return this}));var k=Object.getPrototypeOf,L=k&&k(k(N([])));L&&L!==r&&n.call(L,o)&&(S=L);var O=w.prototype=g.prototype=Object.create(S);function x(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function C(t,e){function r(a,i,o,s){var u=f(t[a],t,i);if("throw"!==u.type){var c=u.arg,l=c.value;return l&&"object"==h(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,o,s)}),(function(t){r("throw",t,o,s)})):e.resolve(l).then((function(t){c.value=t,o(c)}),(function(t){return r("throw",t,o,s)}))}s(u.arg)}var i;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return i=i?i.then(a,a):a()}})}function E(e,r,n){var a=p;return function(i,o){if(a===m)throw Error("Generator is already running");if(a===v){if("throw"===i)throw o;return{value:t,done:!0}}for(n.method=i,n.arg=o;;){var s=n.delegate;if(s){var u=j(s,n);if(u){if(u===_)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===p)throw a=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=m;var c=f(e,r,n);if("normal"===c.type){if(a=n.done?v:d,c.arg===_)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(a=v,n.method="throw",n.arg=c.arg)}}}function j(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,j(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),_;var i=f(a,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,_;var o=i.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,_):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,_)}function A(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function D(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function $(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(A,this),this.reset(!0)}function N(e){if(e||""===e){var r=e[o];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,i=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(h(e)+" is not iterable")}return b.prototype=w,a(O,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:b,configurable:!0}),b.displayName=c(w,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,c(t,u,"GeneratorFunction")),t.prototype=Object.create(O),t},e.awrap=function(t){return{__await:t}},x(C.prototype),c(C.prototype,s,(function(){return this})),e.AsyncIterator=C,e.async=function(t,r,n,a,i){void 0===i&&(i=Promise);var o=new C(l(t,r,n,a),i);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},x(O),c(O,u,"Generator"),c(O,o,(function(){return this})),c(O,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=N,$.prototype={constructor:$,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(D),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return s.type="throw",s.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var u=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(u&&c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(u){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,_):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),_},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),D(r),_}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;D(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:N(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),_}},e}function m(t,e){return w(t)||b(t,e)||_(t,e)||v()}function v(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _(t,e){if(t){if("string"==typeof t)return g(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?g(t,e):void 0}}function g(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function b(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,i,o,s=[],u=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(s.push(n.value),s.length!==e);u=!0);}catch(t){c=!0,a=t}finally{try{if(!u&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw a}}return s}}function w(t){if(Array.isArray(t))return t}function S(t,e,r,n,a,i,o){try{var s=t[i](o),u=s.value}catch(t){return void r(t)}s.done?e(u):Promise.resolve(u).then(n,a)}function k(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var i=t.apply(e,r);function o(t){S(i,n,a,o,s,"next",t)}function s(t){S(i,n,a,o,s,"throw",t)}o(void 0)}))}}function L(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function O(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?L(Object(r),!0).forEach((function(e){x(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):L(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function x(t,e,r){return(e=C(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function C(t){var e=E(t,"string");return"symbol"==h(e)?e:e+""}function E(t,e){if("object"!=h(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=h(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var j={name:"print",components:{ElTable:c["default"]},directives:{print:f.a},mounted:function(){},data:function(){return{tableDataList:null,isLoading:!1,requestApi:"",type:"",title:"",organizationName:"",printDateState:!0,tableSetting:[],currentTableSetting:[],tableData:[],params:{},dialogVisible:!1,showPrintHeaderAndFooter:!1,resultKey:"data",showSummary:!1,pushSummary:!1,collect:[],tableColumnKey:1,rowMergeArrs:[],mergeOpts:{useKeyList:{},mergeKeyList:["name"]},printObj:{id:"print",popTitle:"&nbsp",extraCss:"",extraHead:""},need_to_handle:!1,quitStatus:{ENABLE:"使用中",DELETE:"删除",PERSON_QUIT:"退户",FREEZE:"冻结中"},isMerge:!0,signatureList:[{value:"主管签字："},{value:"制表人签字："},{value:"审核人签字："}]}},created:function(){var t=this;try{if(this.type=this.$route.query.print_type,this.title=this.$route.query.print_title,this.requestApi=this.$route.query.api,this.showSummary="true"===this.$route.query.show_summary,this.showPrintHeaderAndFooter="true"===this.$route.query.show_print_header_and_footer,this.tableSetting=JSON.parse(this.$route.query.table_setting),this.currentTableSetting=JSON.parse(this.$route.query.current_table_setting),this.need_to_handle=this.$route.query.need_to_handle,this.$route.query.mergeOpts&&(this.mergeOpts=JSON.parse(this.$route.query.mergeOpts)),this.printDateState=!!this.$route.query.print_date_state&&this.$route.query.print_date_state,this.params=JSON.parse(this.$route.query.params),this.printDateState&&Reflect.has(this.params,"start_time")&&(this.params.start_date=s(this.params.start_time).format("YYYY-MM-DD"),this.params.end_date=s(this.params.end_time).format("YYYY-MM-DD")),this.$route.query.collect&&(this.collect=JSON.parse(this.$route.query.collect)),this.pushSummary="true"===this.$route.query.push_summary,this.$route.query.result_key&&(this.resultKey=this.$route.query.result_key),this.$store.getters.userInfo&&!this.$store.getters.userInfo.organizationList)return;this.$store.getters.userInfo.organizationList.forEach((function(e){t.$store.getters.organization===e.id&&(t.organizationName=e.name)})),this.$route.query.isMerge&&(this.isMerge="0"!==this.$route.query.isMerge)}catch(e){}this.initLoad()},computed:O({},Object(d["c"])(["userInfo"])),methods:{tableCellStyle:function(){return"border-color:black;"},tableHeaderCellStyle:function(){return"background: #eef1f6;border-color: black;color:black"},parseTime:o["M"],initLoad:function(){this.getTableData(),this.printListener(),this.getAccountPrintSignInfo()},formattingProcess:function(t){var e=this;this.need_to_handle&&t.map((function(t){return t.card_user_group_alias=t.card_user_group_alias.join("，"),t.organization_alias=t.organization_alias.join("，"),t.person_status_alias=e.formatQuitStatusText(t.card_orgs),t=Object(o["p"])(t),t}))},formatQuitStatusText:function(t){var e=this,r="";return t.forEach((function(t){t.organization===e.$store.getters.organization&&(r=e.quitStatus[t.person_status])})),r},getTableData:function(){var t=this;return k(y().mark((function e(){var r,n,a,i,s;return y().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.requestApi){e.next=2;break}return e.abrupt("return",t.$message.error("请求失败，请检查参数！"));case 2:return t.isLoading=!0,e.next=5,Object(o["Z"])(t.$apis[t.requestApi](t.params));case 5:if(r=e.sent,n=m(r,2),a=n[0],i=n[1],t.isLoading=!1,t.tableData=[],!a){e.next=14;break}return t.$message.error(a.message),e.abrupt("return");case 14:0===i.code?(t.tableDataList=i.data,s=i.data,"AccountWalletDaily"===t.type&&t.initAccountWalletDaily(s),t.resultKey?(t.formattingProcess(s[t.resultKey]),t.tableData=s[t.resultKey]):(t.formattingProcess(s),t.tableData=s),t.collect&&t.collect.length&&t.setCollectData(i),t.pushSummary&&t.setSummaryData(i),t.isMerge&&(t.rowMergeArrs=Object(u["a"])(t.tableData,t.mergeOpts)),Object(p["nextTick"])((function(){})),t.resetTableStyle()):t.$message.error(i.msg);case 15:case"end":return e.stop()}}),e)})))()},initAccountWalletDaily:function(t){var e={},r=t.result.length,n=t.result;n=n.map((function(t,a){0!==a&&a!==r-1||(e[t.date]={date_start_fee:t.date_start_fee,date_end_fee:t.date_end_fee});var i=s(t.date).add(1,"day").format("YYYY-MM-DD");if(a>0){var o=n[a-1];o&&o.date===i&&o.date_start_fee!==t.date_end_fee&&(t.error_date_end_fee=!0,o.error_date_start_fee=!0)}else{var u=e[i];u&&u.date_start_fee!==t.date_end_fee&&(t.error_date_end_fee=!0)}return t}))},openSetting:function(){this.dialogVisible=!0},confirmDialog:function(t,e){this.currentTableSetting=t,this.tableColumnKey++,this.setPrintSettingInfo(t,e)},setCollectData:function(t){var e=this;this.collect.forEach((function(r){t.data.collect&&void 0!==t.data.collect[r.key]&&e.$set(r,"value",t.data.collect[r.key])}))},setSummaryData:function(t){var e=t.data.collect;e[this.tableSetting[0].key]="合计",this.tableData.push(e)},getSummaries:function(t){},resetTableStyle:function(){"CardInfoSubsidyInfoList"===this.type?Object(p["nextTick"])((function(){var t=document.querySelectorAll("colgroup");setTimeout((function(){for(var e=0;e<t.length;e++)t[e].childNodes.forEach((function(t,e){3===e?t.setAttribute("width","230px"):t.setAttribute("width","100%")}))}),100)})):this.$nextTick((function(){var t=document.querySelectorAll("colgroup");setTimeout((function(){for(var e=0;e<t.length;e++)t[e].childNodes.forEach((function(t,e){t.setAttribute("width","100%")}))}),50)}))},arraySpanMethod:function(t){t.row;var e=t.column,r=t.rowIndex,n=t.columnIndex;if(this.isMerge){var a=Object.keys(this.mergeOpts.useKeyList),i=this.mergeOpts.useKeyList&&a.length;if(i)for(var o in this.mergeOpts.useKeyList)if(this.mergeOpts.useKeyList[o].includes(e.property))return Object(u["b"])(this.rowMergeArrs,e.property,r,n);if(this.mergeOpts.mergeKeyList&&this.mergeOpts.mergeKeyList.length&&this.mergeOpts.mergeKeyList.includes(e.property))return Object(u["b"])(this.rowMergeArrs,e.property,r,n)}},getAccountPrintSignInfo:function(){var t=this;return k(y().mark((function e(){var r,n,a,i;return y().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$to(t.$apis.apiBackgroundOrganizationAccountGetAccountPrintSignInfoPost({id:t.userInfo.account_id,print_sign_key:t.type+"Sign"}));case 2:if(r=e.sent,n=m(r,2),a=n[0],i=n[1],!a){e.next=9;break}return t.$message.error(a.message),e.abrupt("return");case 9:0===i.code?Array.isArray(i.data)&&(t.signatureList=i.data):t.$message.error(i.msg);case 10:case"end":return e.stop()}}),e)})))()},setPrintSettingInfo:function(t,e){var r=this;return k(y().mark((function n(){var a,i,s,u;return y().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,Object(o["Z"])(r.$apis.apiBackgroundAdminAccountSetAccountPrintInfoPost(O({id:r.userInfo.account_id,print_key:r.type,print_list:t},e)));case 2:if(a=n.sent,i=m(a,2),s=i[0],u=i[1],!s){n.next=9;break}return r.$message.error(s.message),n.abrupt("return");case 9:0===u.code?(r.getAccountPrintSignInfo(),r.resetTableStyle()):r.$message.error(u.msg);case 10:case"end":return n.stop()}}),n)})))()},printListener:function(){if(window.matchMedia){var t=window.matchMedia("print");t.addListener((function(t){t.matches||window.close()}))}},toPrint:function(){},getNameByList:function(t){if(!t||0===t.length)return"";if("object"===h(t)){var e=[];for(var r in t)e.push(t[r]);return e.join("、")}if(Array.isArray(t)&&t.length>0){var n=t.map((function(t){return t.name}));return n.join("、")}}}},A=j,D=(r("5769"),r("2877")),$=Object(D["a"])(A,a,i,!1,null,null,null);e["default"]=$.exports},b69f:function(t,e,r){},bcca:function(t,e,r){"use strict";r("b69f")},cc06:function(t,e,r){"use strict";function n(t,e){var r={},n=e.useKeyList&&Object.keys(e.useKeyList).length;return n&&Object.keys(e.useKeyList).forEach((function(n,i){e.useKeyList[n].forEach((function(e,i){r[e]={row:[],mergeNum:0,key:n},r=a(r,t,e,n)}))})),e.mergeKeyList&&e.mergeKeyList.forEach((function(e,n){r[e]={row:[],mergeNum:0},r=a(r,t,e)})),r}function a(t,e,r,n){return e.forEach((function(a,o){if(0===o)t[r].row.push(1),t[r].mergeNum=o;else{var s=n?a[n]===e[o-1][n]:!n,u=a[r]===e[o-1][r]&&s;if(u){var c=i(t[r].row);t[r].row[c]+=1,t[r].row.push(0),t[r].mergeNum=o}else t[r].row.push(1),t[r].mergeNum=o}})),t}function i(t){var e=t.length-1;while(e>0){if(t[e])break;e--}return e}function o(t,e,r,n){var a=t[e].row[r],i=a>0?1:0;return[a,i]}r.d(e,"a",(function(){return n})),r.d(e,"b",(function(){return o}))}}]);