<template>
  <div class="booking-meal-wrapper container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      @search="searchHandle"
      :form-setting="searchFormSetting"
      :autoSearch="false"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <!-- <el-button size="mini">打印</el-button>
          <el-button size="mini" @click="handleExport">导出</el-button>
          <el-button size="mini">报表设置</el-button> -->
          <el-button size="mini" @click="handleExport" v-permission="['background_order.reservation_order.list_export']">导出</el-button>
        </div>
      </div>

      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          :empty-text="isFirstSearch ? '暂无数据，请查询' : ''"
          header-row-class-name="ps-table-header-row"
          :span-method="objectSpanMethod"
        >
          <el-table-column
            v-for="col in columns"
            :prop="col.column"
            :label="col.label"
            align="center"
            :key="col.column"
            :width="col.width"
          >
            <template slot-scope="scope">
              <span v-if="col.column.includes('fee')">
                ￥{{ scope.row[col.column] | formatMoney }}
              </span>
              <span v-else-if="scope.row[col.column] === '' || scope.row[col.column] === null">
                <span>--</span>
              </span>
              <span v-else-if="col.column === 'consume_type'">
                <span>{{ scope.row[col.column] | ConsumeType }}</span>
              </span>
              <span v-else-if="col.column === 'dining_time'">
                <span>{{ scope.row[col.column] | formatDate }}</span>
              </span>
              <el-button
                v-else-if="col.column === 'create_date6'"
                type="text"
                size="small"
                @click="handler(scope.row, 'food')"
              >
                查看
              </el-button>
              <el-button
                v-else-if="col.column === 'total_set_meal_list'"
                type="text"
                size="small"
                @click="handler(scope.row, 'set_meal_list')"
              >
                查看
              </el-button>
              <span v-else>
                <span>{{ scope.row[col.column] }}</span>
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <common-pagination
        ref="pagination"
        :total="total"
        :onPaginationChange="onPaginationChange"
      ></common-pagination>
      <el-dialog title="菜品详情" :visible.sync="dialogVisible" width="800px">
        <div class="p-b-20" v-if="setMealtableData.length">
          <div class="p-b-10">套餐信息：</div>
          <el-table
            :data="setMealtableData"
            border
            stripe
            header-row-class-name="ps-table-header-row"
            :span-method="setMealSpanMethod"
          >
            <el-table-column prop="name" label="套餐名称"></el-table-column>
            <el-table-column prop="count" label="套餐份数"></el-table-column>
            <el-table-column prop="real_fee" label="套餐单价">
              <template slot-scope="scope">￥{{ scope.row.real_fee | formatMoney }}</template>
            </el-table-column>
            <el-table-column prop="packing_fee" label="套餐打包费">
              <template slot-scope="scope">￥{{ scope.row.packing_fee | formatMoney }}</template>
            </el-table-column>
            <el-table-column prop="food_name" label="菜品名称"></el-table-column>
            <el-table-column prop="food_spec_name" label="菜品规格"></el-table-column>
            <el-table-column prop="food_count" label="菜品数量"></el-table-column>
            <el-table-column prop="food_real_fee" label="菜品单价">
              <template slot-scope="scope">￥{{ scope.row.food_real_fee | formatMoney }}</template>
            </el-table-column>
          </el-table>
        </div>

        <div v-if="Menu_tableData.length">
          <div class="p-b-10">单品信息：</div>
          <el-table
            :data="Menu_tableData"
            border
            stripe
            header-row-class-name="ps-table-header-row"
          >
            <el-table-column prop="name" label="菜品名称"></el-table-column>
            <el-table-column prop="count" label="数量"></el-table-column>
            <el-table-column prop="raw_fee" label="单价">
              <template slot-scope="scope">
                ￥{{ (scope.row.raw_fee / scope.row.count) | formatMoney }}
              </template>
            </el-table-column>
            <el-table-column prop="packing_fee" label="打包费">
              <template slot-scope="scope">￥{{ scope.row.packing_fee | formatMoney }}</template>
            </el-table-column>
          </el-table>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="menuNo">取 消</el-button>
          <el-button type="primary" @click="menuYes">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import exportExcel from '@/mixins/exportExcel' // 导出混入
import CommonPagination from './CommonPagination'
import { searchFormSetting, columns } from './constantsAndConfig'
import { getRequestParams, deepClone } from '@/utils'
import { mergeHandle, mergeRowAction } from '@/utils/table'
export default {
  name: 'OrderList',
  mixins: [exportExcel],
  components: {
    CommonPagination
  },
  mounted() {
    this.initLoad()
  },
  filters: {
    ConsumeType(value) {
      if (value === 'online') return '线上'
      if (value === 'offline') return '线下'
    }
  },
  data() {
    return {
      currentPage: 1,
      pageSize: 10,
      total: 0,
      isLoading: false,
      columns,
      searchFormSetting: deepClone(searchFormSetting),
      tableData: [],
      dialogVisible: false,
      Menu_tableData: [],
      mergeOpts: {
        useKeyList: {}, // 根据id去合并set_meal_id:[name]
        mergeKeyList: ['name', 'count', 'real_fee', 'packing_fee'] // 通用的合并字段，根據值合并
      },
      rowMergeArrs: [], // 处理完的数据给表格合并
      setMealtableData: [],
      isFirstSearch: false
    }
  },
  methods: {
    initLoad() {
      this.requestReservationOrderInfoList()
      this.userGroupList()
      // this.userConsumeList()
      // this.getFoodRecordList()
    },
    // 获取分组信息
    async userGroupList() {
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardUserGroupListPost({
        status: 'enable',
        page: 1,
        page_size: 9999999
      })
      this.isLoading = false
      if (res.code === 0) {
        this.searchFormSetting.user_group_id.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // async getFoodRecordList() {
    //   this.isLoading = true
    //   const res = await this.$apis.apiBackgroundOrderOrderReservationPaymentFoodRecordList()
    //   this.isLoading = false
    //   if (res.code === 0) {
    //     const result = []
    //     for (let key in res.data.result) {
    //       result.push({ label: res.data.result[key], value: Number(key) })
    //     }
    //     this.searchFormSetting.foods.dataList = result
    //   } else {
    //     this.$message.error(res.msg)
    //   }
    // },

    // // 获取组织信息
    // async organizationList() {
    //   this.isLoading = true
    //   const res = await this.$apis.apiBackgroundOrganizationOrganizationListPost({
    //     status: 'enable',
    //     page: 1,
    //     page_size: 9999
    //   })
    //   this.isLoading = false
    //   if (res.code === 0) {
    //     this.searchFormSetting.organization_id.dataList = res.data.results
    //   } else {
    //     this.$message.error(res.msg)
    //   }
    // },

    async requestReservationOrderInfoList() {
      const res = await this.$apis.apiBackgroundOrderReservationOrderInfoListPost(getRequestParams(this.searchFormSetting, this.currentPage, this.pageSize))
      if (res.code === 0) {
        this.tableData = res.data.results
        this.total = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.$refs.pagination.handleCurrentChange(1, true)
      this.$refs.pagination.handleSizeChange(10, true)
      this.isFirstSearch = false
      this.tableData = []
      this.onPaginationChange({ current: 1, pageSize: 10 })
    },
    searchHandle(e) {
      if (e && e === 'search') {
        this.$refs.pagination.handleCurrentChange(1, true)
        this.$refs.pagination.handleSizeChange(10, true)
        this.isFirstSearch = false
        this.onPaginationChange({ current: 1, pageSize: 10 })
      }
    },

    onPaginationChange(data) {
      this.pageSize = data.pageSize
      this.currentPage = data.current
      if (!this.isFirstSearch) {
        this.requestReservationOrderInfoList()
      }
      // this.initLoad()
    },
    handleExport() {
      const option = {
        type: 'ExportOrderList',
        params: {
          page: 1,
          page_size: 9999999,
          ...getRequestParams(this.searchFormSetting)
        }
      }
      this.exportHandle(option)
    },
    // 表格总单号垂直合并 // row当前行 rowIndex当前行号 column当前列 columnIndex当前列号
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        const dataProvider = this.tableData
        const cellValue = row[column.property]
        if (cellValue) {
          const prevRow = dataProvider[rowIndex - 1]
          let nextRow = dataProvider[rowIndex + 1]
          if (prevRow && prevRow[column.property] === cellValue) {
            return { rowspan: 0, colspan: 0 }
          } else {
            let rowspan = 1
            while (nextRow && nextRow[column.property] === cellValue) {
              rowspan++
              nextRow = dataProvider[rowspan + rowIndex]
            }
            if (rowspan > 1) {
              return { rowspan, colspan: 1 }
            }
          }
        }
      }
    },
    // 套餐合并
    setMealSpanMethod({ row, column, rowIndex, columnIndex }) {
      let keys = Object.keys(this.mergeOpts.useKeyList)
      let useKey = this.mergeOpts.useKeyList && keys.length
      if (useKey) {
        for (const key in this.mergeOpts.useKeyList) {
          if (this.mergeOpts.useKeyList[key].includes(column.property)) {
            return mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
          }
        }
      }
      if (
        this.mergeOpts.mergeKeyList &&
        this.mergeOpts.mergeKeyList.length &&
        this.mergeOpts.mergeKeyList.includes(column.property)
      ) {
        return mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
      }
    },
    // 菜品
    handler(row, type) {
      r
      this.dialogVisible = true
      if (type === 'food') {
        this.Menu_tableData = []
        this.Menu_tableData = row.foo_list
      } else if (type === 'set_meal_list') {
        if (row.total_set_meal_list.length) {
          let setMealtableData = []
          row.total_set_meal_list.map(v => {
            // 先添加套餐基本信息行
            let setMealObj = {
              name: v.name,
              count: v.count,
              real_fee: v.real_fee,
              raw_fee: v.raw_fee,
              packing_fee: v.packing_fee,
              food_name: '',
              food_spec_name: '',
              food_count: '',
              food_real_fee: '',
              food_raw_fee: ''
            }
            setMealtableData.push(setMealObj)

            // 如果有food_list，再添加食品明细行
            if (v.food_list && v.food_list.length > 0) {
              v.food_list.map(food => {
                let foodObj = {
                  name: v.name,
                  count: v.count,
                  real_fee: v.real_fee,
                  raw_fee: v.raw_fee,
                  packing_fee: v.packing_fee,
                  food_name: food.food_name,
                  food_spec_name: food.spec_name,
                  food_count: food.count,
                  food_real_fee: food.real_fee,
                  food_raw_fee: food.raw_fee
                }
                setMealtableData.push(foodObj)
              })
            }
          })
          this.setMealtableData = setMealtableData
          // 定义数据 套餐合并表格
          this.rowMergeArrs = mergeHandle(this.setMealtableData, this.mergeOpts)
        }
      }
    },
    menuYes() {
      this.dialogVisible = false
    },
    menuNo() {
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped lang="scss">
.search-wrapper {
  padding: 0 20px;

  .top-part {
    display: flex;
    align-items: center;

    .search-item {
      display: flex;
      align-items: center;
      margin-right: 12px;

      .item-label {
        font-size: 12px;
      }
    }
  }
}

.search-group {
  margin-top: 20px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  .grid-content {
    display: flex;
    align-items: center;
    margin-right: 20px;
    margin-bottom: 12px;

    .label {
      margin-right: 8px;
      font-size: 12px;
    }
  }
}
</style>
